// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply from: "config.gradle"
//apply plugin: 'module-manager-plugin'
//apply from: "module-settings.gradle"

buildscript {
//    ext.kotlin_version = '1.3.72'
//    ext.kotlin_version = "1.4.32"// OLD
//    ext.kotlin_version = "1.5.31"//窗口相机升级1
    ext.kotlin_version = "1.9.10"//
//    ext.kotlin_version = "1.8.22"//窗口相机升级1，更新到该版本后，打包会出错。

    repositories {
        maven {
            url 'https://maven.aliyun.com/repository/public'
        }
        maven {
            credentials {
                username '5f62d76b18c17e7748944fe3'
                password 'wFc(Rbgh][Vp'
            }
            url 'https://packages.aliyun.com/maven/repository/2036093-release-Iz2UYC/'
        }
        maven {
            credentials {
                username '5f62d76b18c17e7748944fe3'
                password 'wFc(Rbgh][Vp'
            }
            url 'https://packages.aliyun.com/maven/repository/2036093-snapshot-UmgYsR/'
        }
        google()
//        jcenter()//服务即将关闭
        // 配置HMS Core SDK的Maven仓地址。
        maven {
            url 'https://developer.huawei.com/repo/'
//            allowInsecureProtocol = true
        }//华为推送
        //buf修改5
        mavenCentral()

    }
    dependencies {
//        classpath 'com.android.tools.build:gradle:4.2.2'// update
        classpath 'com.android.tools.build:gradle:8.3.0'// update
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
//        classpath "org.jetbrains.kotlin:kotlin-android-extensions:$kotlin_version"
        classpath 'com.marktoo.gradle:module-manager-plugin:2.0.4'
//        classpath 'com.tencent.mm:AndResGuard-gradle-plugin:2.0.18'
        classpath 'org.greenrobot:greendao-gradle-plugin:3.3.1'// update
        classpath 'com.tencent.bugly:symtabfileuploader:2.2.1'
//        classpath 'com.huawei.agconnect:agcp:1.2.0.300'
//        classpath 'com.huawei.agconnect:agcp:1.4.1.300'

        // 增加AGC插件配置，请您参见AGC插件依赖关系选择合适的AGC插件版本。和gradle插件版本相匹配
        classpath 'com.huawei.agconnect:agcp:1.9.1.301'//华为推送

        //buf修改6
        classpath 'com.google.protobuf:protobuf-gradle-plugin:0.9.1'
        classpath 'com.kezong:fat-aar:1.2.16'
    }
}

allprojects {
    repositories {
        maven { url "https://jitpack.io" }
        // 配置HMS Core SDK的Maven仓地址。
        maven {
            url 'https://developer.huawei.com/repo/'
//            allowInsecureProtocol = true
        }//华为推送
        maven {
            url 'https://maven.aliyun.com/repository/public'
        }
        maven {
            credentials {
                username '5f62d76b18c17e7748944fe3'
                password 'wFc(Rbgh][Vp'
            }
            url 'https://packages.aliyun.com/maven/repository/2036093-release-Iz2UYC/'
        }
        maven {
            credentials {
                username '5f62d76b18c17e7748944fe3'
                password 'wFc(Rbgh][Vp'
            }
            url 'https://packages.aliyun.com/maven/repository/2036093-snapshot-UmgYsR/'
        }
        mavenCentral()
        google()
//        jcenter()//服务即将关闭
        maven { url "https://plugins.gradle.org/m2/" }
        flatDir {
            dirs 'libs'
        }
    }


}

//task clean(type: Delete, dependsOn: deleteDescriptors) {//clean时，执行清理aar缓存
task clean(type: Delete) {//
    delete rootProject.buildDir
}

tasks.configureEach {
    if (it.name == "mergeDebugJniLibFolders") {
        it.dependsOn("greendao")
    } else if (it.name == "generateDebugResources") {
        it.dependsOn("greendao")
    } else if (it.name == "extractDebugLinksDebug") {
        it.dependsOn("greendao")
    } else if (it.name == "processDebugManifest") {
        it.dependsOn("greendao")
    } else if (it.name == "mergeDebugShaders") {
        it.dependsOn("greendao")
    } else if (it.name == "processDebugJavaRes") {
        it.dependsOn("greendao")
    } else if (it.name == "extractDeepLinksDebug") {
        it.dependsOn("greendao")
    }
}


configurations.all {
    resolutionStrategy{
        force 'androidx.appcompat:appcompat:1.3.0'
        force 'org.jetbrains.kotlin:kotlin-stdlib-common:1.8.0'
        force 'androidx.core:core:1.8.0'
    }

}

//configurations.all {
//    resolutionStrategy.eachDependency { details ->
//        if (details.requested.group == "org.jetbrains.kotlin") {
//            details.useVersion "1.7.1" // 项目当前的 Kotlin 版本
//        }
//    }
//}


subprojects {
    project.configurations.all {
        //遍历所有依赖库，通过判断 requested.group 和 requested.name 来指定使用的版本
        resolutionStrategy.eachDependency { DependencyResolveDetails details ->
            def requested = details.requested
            if (requested.group == 'androidx.appcompat') {
                if (requested.module.name == 'appcompat') {
                    details.useVersion '1.3.0'
                }
            }

//            androidx.core:core-ktx:1.3.2
            if (requested.group == 'androidx.core') {
                if (requested.module.name == 'core') {
                    details.useVersion '1.8.0'
                }
                if (requested.module.name == 'core-ktx') {
                    details.useVersion '1.8.0'
                }
            }

            // androidx.activity:activity-ktx:1.1.0
            if (requested.group == 'androidx.activity') {
                if (requested.module.name == 'activity-ktx') {
                    details.useVersion '1.3.0'
                }
                if (requested.module.name == 'activity') {
                    details.useVersion '1.3.0'
                }
            }

            if (requested.group == 'androidx.fragment') {
                if (requested.module.name == 'fragment') {
                    details.useVersion '1.3.4'
                }
            }

            if (requested.group == 'com.squareup.okhttp3') {
                if (requested.module.name == 'okhttp') {
                    details.useVersion '3.14.7'
                }
            }


//            if (requested.group == 'com.qcloud.cos') {
//                if (requested.module.name == 'qcloud-foundation') {
//                    details.useVersion '1.5.55'
//                }
//            }
        }
    }
}


subprojects {
    afterEvaluate { project ->
        if (project.plugins.hasPlugin('com.android.application') ||
                project.plugins.hasPlugin('com.android.library')) {
            def androidExtension = project.extensions.findByName("android")
            if (androidExtension != null && !androidExtension.hasProperty("namespace")) {
                androidExtension.namespace = project.group.toString()
            }
        }
    }
}
