<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"

    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/locationDetailLv">

<!--        <com.baidu.mapapi.map.MapView-->
<!--            android:id="@+id/bdMapView"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="match_parent"-->
<!--            android:clickable="true"-->
<!--            android:focusable="true" />-->

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white">

            <RelativeLayout
                android:id="@+id/search_location"
                android:layout_width="match_parent"
                android:layout_height="27dp"
                android:layout_marginStart="8dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="8dp"
                android:layout_marginBottom="@dimen/dp_8"
                android:background="@drawable/shape_location_edit_ebf1f5"
                android:visibility="visible"
                app:layout_constraintBottom_toTopOf="@id/attendanceHintLayout"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:drawableStart="@drawable/iconsmallsearch"
                    android:drawablePadding="5dp"
                    android:text="搜索"
                    android:textColor="#ffaaaaaa"
                    android:textSize="@dimen/sp_12" />
            </RelativeLayout>

            <!--            <View-->
            <!--                android:id="@+id/empty"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="8dp"-->
            <!--                app:layout_constraintTop_toBottomOf="@id/search_location" />-->

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/attendanceHintLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#99000000"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/search_location"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/attendanceHintIv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:src="@drawable/icon_location_top_hint"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/listHintText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:text="选择地点后，可拖动地图修改考勤中心点以保证地点的准确性"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_12"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toEndOf="@id/attendanceHintIv"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/mapCenter"
            android:layout_width="30dp"
            android:layout_height="48dp"
            android:layout_centerInParent="true"
            android:src="@drawable/icon_location_center" />

        <ImageView
            android:id="@+id/reset_loc"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="20dp"
            android:src="@drawable/icon_location_loc" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/locationDetailLv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true">

        <include
            android:id="@+id/location_detail_layou"
            layout="@layout/include_location_detail_layout" />
    </LinearLayout>

    <include
        android:id="@+id/search_result_location"
        layout="@layout/include_search_result_location" />
</RelativeLayout>