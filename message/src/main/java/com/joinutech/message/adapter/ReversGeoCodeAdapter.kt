package com.joinutech.message.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
//import com.baidu.mapapi.search.core.PoiInfo
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.message.R

class ReversGeoCodeAdapter(val context: Context, private val adapterType: String)
    : RecyclerView.Adapter<ReversGeoCodeAdapter.MyHolder>() {

    private val list: ArrayList<Object> = arrayListOf()

    private var mCurrentChoicePoiInfo = -1

    private var onItemClickListener: OnItemClickListener? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyHolder {
        return MyHolder(LayoutInflater.from(context).inflate(R.layout.select_location_item_layout, parent, false))
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: MyHolder, position: Int) {
        val poiInfo = list[position]
        holder.itemView.tag = position
//        holder.itemView.findViewById<TextView>(R.id.tv_location_name).text = poiInfo.name
//        holder.itemView.findViewById<TextView>(R.id.tv_location_address).text = poiInfo.address
        if (StringUtils.isEmpty(adapterType)) {
            holder.itemView.findViewById<ImageView>(R.id.iv_select_tag).visibility = View.INVISIBLE
        } else {
            if (mCurrentChoicePoiInfo == position) {
                holder.itemView.findViewById<ImageView>(R.id.iv_select_tag).visibility = View.VISIBLE
            } else {
                holder.itemView.findViewById<ImageView>(R.id.iv_select_tag).visibility = View.INVISIBLE
            }
        }
        holder.itemView.setOnClickListener {
            onSelectPoi(it)
        }
    }

    private fun onSelectPoi(view: View) {
        val targetPosition = view.tag as Int
        if (targetPosition in list.indices) {
//            onItemClickListener?.onItemClick(view, list[targetPosition], adapterType)
        }
        notifyDataSetChanged()
    }

//    inner class OnChoiceAddress(var position: Int) : View.OnClickListener {
//
//        override fun onClick(v: View?) {
//            mCurrentChoicePoiInfo = position
//            mCurrentChoiceAddress = list!![mCurrentChoicePoiInfo]
//            notifyDataSetChanged()
//        }
//    }

//    fun setDataList(poiInfoList: List<PoiInfo>?) {
//        list.clear()
//        if (poiInfoList != null && poiInfoList.isNotEmpty()) {
//            list.addAll(poiInfoList)
//            mCurrentChoicePoiInfo = 0
//        }
//        notifyDataSetChanged()
//    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        this.onItemClickListener = onItemClickListener
    }

//    fun getChoicePoiInfo(): PoiInfo? {
//        if (mCurrentChoicePoiInfo in list.indices) {
//            return list[mCurrentChoicePoiInfo]
//        }
//        return null
//    }

    fun clearData() {
        list.clear()
        notifyDataSetChanged()
    }

    interface OnItemClickListener {
//        fun onItemClick(view: View, poiInfo: PoiInfo, adapterType: String)
    }

    class MyHolder(view: View) : RecyclerView.ViewHolder(view) {}
}