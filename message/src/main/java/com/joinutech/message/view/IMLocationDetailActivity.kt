package com.joinutech.message.view

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
//import com.baidu.mapapi.map.BaiduMap
//import com.baidu.mapapi.map.MapStatus
//import com.baidu.mapapi.map.MapStatusUpdateFactory
//import com.baidu.mapapi.model.LatLng
//import com.baidu.mapapi.search.core.PoiInfo
//import com.baidu.mapapi.search.core.SearchResult
//import com.baidu.mapapi.search.geocode.*
//import com.baidu.mapapi.search.poi.*
//import com.baidu.mapapi.search.sug.SuggestionSearch
//import com.baidu.mapapi.search.sug.SuggestionSearchOption
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.service.LocationCallback
import com.joinutech.ddbeslibrary.service.LocationResult
import com.joinutech.ddbeslibrary.service.LocationService
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.widget.MyOrientationListener
import com.joinutech.message.R
import com.joinutech.message.adapter.ReversGeoCodeAdapter
import com.joinutech.message.databinding.ActivityImLocationBinding

/**
 * Description 地图定位页
 * Author HJR36
 * Date 2018/7/19 17:33
 */
@Suppress("DEPRECATION")
@Route(path = RouteIm.locationActivity)
class IMLocationDetailActivity : MyUseBindingActivity<ActivityImLocationBinding>(),
//        BaiduMap.OnMapStatusChangeListener, OnGetGeoCoderResultListener,
//        OnGetPoiSearchResultListener,
    ReversGeoCodeAdapter.OnItemClickListener {

    override val contentViewResId: Int = R.layout.activity_im_location
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityImLocationBinding {
        return ActivityImLocationBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = true

//    private lateinit var mBaiduMap: BaiduMap
//
//    /**坐标反查api*/
//    private lateinit var mSearch: GeoCoder
//
//    /**关键词搜索*/
//    private var mSuggestionSearch: SuggestionSearch? = null
//
//    /**关键词搜索结果选中后，检索周围信息当前城市搜索*/
//    private var mPoiSearch: PoiSearch? = null

    private var isFirstLoc = true
    private var city: String = "北京"
    private var locationService: LocationService? = null
    private var mListener: MyLocationListener = MyLocationListener()

    var type: String = ""

    private lateinit var mAdapter: ReversGeoCodeAdapter
    private lateinit var mSearchAdapter: ReversGeoCodeAdapter

    /**当前中心点坐标*/
    private var currentCenterLatLng: LocationResult = LocationResult()

    private var mCurrentDirection = 0f

    private var mCurrentAccracy: Float = 0.toFloat()

    private var isStartLoc = false
    private var myOrientationListener: MyOrientationListener? = null
    private var getLocationPermission = false

    override fun initImmersion() {
        setLeftTitle("取消", View.OnClickListener { finish() })
        if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("type"))) {
            type = intent.getStringExtra("type") ?: ""
        }
        when (type) {
            "selectMapLocation" -> {
                setPageTitle("请选择地点")
                setRightTitle("发送", this)
            }
            "selectLocation" -> {
                if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("title"))) {
                    val title = intent.getStringExtra("title") ?: ""
                    setPageTitle(title)
                }
                if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("rightTitle"))) {
                    val rightTitle = intent.getStringExtra("rightTitle") ?: ""
                    setRightTitle(rightTitle, this)
                }
            }
            "addAttendanceLocation" -> {
                setPageTitle("添加考勤地点")
                setRightTitle("完成", this)
            }
            else -> {
                setPageTitle("位置信息")
            }
        }
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        locationService = (application as BaseApplication).locationService
        useLocationOrientationListener()//调用方向传感器
//        mBaiduMap = binding.bdMapView.map
//        binding.bdMapView.showScaleControl(false)
//        binding.bdMapView.showZoomControls(false)
        binding.resetLoc.setOnClickListener(this)
        binding.locationDetailLayou.mapGuide.setOnClickListener(this)
        binding.searchResultLocation.searchList.layoutManager = LinearLayoutManager(this)
        binding.locationDetailLayou.locationList.layoutManager = LinearLayoutManager(this)
        binding.searchLocation.setOnClickListener(this)
        binding.searchResultLocation.cancle.setOnClickListener(this)
        binding.locationDetailLayou.retryRv.setOnClickListener(this)
        binding.searchResultLocation.confirmSearch.setOnClickListener(this)
    }

    @SuppressLint("CheckResult")
    private fun checkLocationPermission() {
        PermissionUtils.requestPermissionActivity(this,
                arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),
                hint = "",
                onSuccess = {
                    getLocationPermission = true
                    locationEvent()
                },
                onError = {
                    ToastUtil.show(mContext!!, "位置发送能需要使用位置权限")
                    binding.locationDetailLayou.locationList.visibility = View.GONE
                    binding.locationDetailLayou.NoLocation.visibility = View.VISIBLE
                } , preTips = "您需要申请位置权限才能使用定位功能")

//        val rxPermissions = RxPermissions(this)
//        rxPermissions.request(Manifest.permission.ACCESS_FINE_LOCATION).subscribe {
//            if (it) {
//                getLocationPermission = true
//                locationEvent()
//            } else {
//                ToastUtil.show(mContext!!, "位置发送能需要使用位置权限")
//                binding.locationDetailLayou.locationList.visibility = View.GONE
//                binding.locationDetailLayou.NoLocation.visibility = View.VISIBLE
//            }
//        }
    }

    private fun locationEvent() {
        if (StringUtils.isEmpty(type) && isStartLoc) {
            return
        }
        if (StringUtils.isEmpty(type)) {
            isStartLoc = true
        }
        if (isStartLoc) {
            //开启定位图层
//            mBaiduMap.isMyLocationEnabled = true
//            locationService?.apply {
//                initLocation()
//                registerListener(mListener)
//                setLocationOption(getOption())
//                start(false)
//            }
        }
//        mSearch = GeoCoder.newInstance()
//        mSearch.setOnGetGeoCodeResultListener(this)
////        mBaiduMap.setOnMapLoadedCallback {
////            showLog("地图加载完成 获取中心点信息")
////            getCenter()
////        }
//        mBaiduMap.setOnMapStatusChangeListener(this)
//        initSuggestionSearch()
//        // 初始化搜索模块，注册搜索事件监听
//        mPoiSearch = PoiSearch.newInstance()
//        mPoiSearch?.setOnGetPoiSearchResultListener(this)
    }

    private fun initSuggestionSearch() {
        // 初始化建议搜索模块，注册建议搜索事件监听
//        mSuggestionSearch = SuggestionSearch.newInstance()
//        mSuggestionSearch?.setOnGetSuggestionResultListener { suggestionResult ->
//            if (suggestionResult?.allSuggestions != null) {
//                val suggest: MutableList<HashMap<String, String?>> = ArrayList()
//                for (info in suggestionResult.allSuggestions) {
//                    if (info.getKey() != null && info.getDistrict() != null && info.getCity() != null) {
//                        val map = HashMap<String, String?>()
//                        map["key"] = info.getKey()
//                        map["city"] = info.getCity()
//                        map["dis"] = info.getDistrict()
//                        suggest.add(map)
//                    }
//                }
//                val temp = arrayListOf<PoiInfo>()
//                for (info in suggestionResult.allSuggestions) {
//                    if (info.key != null) {
//                        val poiInfo = PoiInfo()
//                        poiInfo.address = info.district
//                        poiInfo.name = info.key
//                        poiInfo.location = info.pt
//                        temp.add(poiInfo)
//                    }
//                }
//                showLog("--》》检索到周边POI列表 ${temp.size}")
//                mSearchAdapter.setDataList(temp)
//            }

    }

    private fun doSuggestionSearch(keyword: String, city: String = "北京") {
        // 使用建议搜索服务获取建议列表，结果在onSuggestionResult()中更新
//        mSuggestionSearch!!.requestSuggestion(SuggestionSearchOption()
//                .keyword(keyword) // 关键字
//                .city(city)) // 城市
    }

    private fun searchKeywordOnPosition() {
//        val keyword = binding.searchResultLocation.searchLocationEdit.text?.toString() ?: ""
//        if (StringUtils.isEmpty(keyword)) {
//            binding.searchResultLocation.emptyBac.visibility = View.VISIBLE
//            binding.searchResultLocation.searchList.visibility = View.INVISIBLE
//        } else {
//            binding.searchResultLocation.emptyBac.visibility = View.INVISIBLE
//            binding.searchResultLocation.searchList.visibility = View.VISIBLE
//            /**
//             * 使用建议搜索服务获取建议列表，结果在onSuggestionResult()中更新
//             */
//            showLog("---->>>> 开始检索关键词 $city :: $keyword")
//            mSuggestionSearch?.requestSuggestion(
//                    SuggestionSearchOption().city(city).keyword(keyword)
//            )
//        }
    }

    /**
     * <AUTHOR>
     * 定位结合方向传感器，从而可以实时监测到X轴坐标的变化，从而就可以检测到
     * 定位图标方向变化，只需要将这个动态变化的X轴的坐标更新myCurrentX值，
     * 最后在MyLocationData data.driection(myCurrentX);
     */
    private fun useLocationOrientationListener() {
//        myOrientationListener = MyOrientationListener(mContext)
//        myOrientationListener!!.setMyOrientationListener { x ->
//            //监听方向的改变，方向改变时，需要得到地图上方向图标的位置
//            mCurrentDirection = x
//            // 构造定位数据
//            locationService?.apply {
//                // 设置定位数据
//                setLocation(mBaiduMap,
//                        currentCenterLatLng.lat,
//                        currentCenterLatLng.lng,
//                        mCurrentAccracy,
//                        mCurrentDirection)
//
//                // 设置自定义图标
//                addMarker(mBaiduMap, R.drawable.icon_location_start)
//            }
//        }
    }

//    override fun initLogic() {
//        super.initLogic()
//        // POI结果列表
//        mAdapter = ReversGeoCodeAdapter(mContext!!, "selectMapLocation")
//        binding.locationDetailLayou.locationList.adapter = mAdapter
//        // POI搜索返回结果列表
//        mSearchAdapter = ReversGeoCodeAdapter(mContext!!, "")
//        binding.searchResultLocation.searchList.adapter = mSearchAdapter
//        if (type == "selectMapLocation" || type == "addAttendanceLocation" || type == "selectLocation") {
//            if (type == "addAttendanceLocation") {
//                binding.attendanceHintLayout.visibility = View.VISIBLE
//            }
//            binding.locationDetailLayou.locationList.visibility = View.VISIBLE
//            binding.locationDetailLayou.addressRv.visibility = View.GONE
//            binding.searchResultLocation.searchLocationEdit.addTextChangedListener(object : TextWatcher {
//                override fun afterTextChanged(s: Editable?) {
////                    val text = s.toString()
////                    if (StringUtils.isEmpty(text)) {
////                        binding.searchResultLocation.emptyBac.visibility = View.VISIBLE
////                        binding.searchResultLocation.searchList.visibility = View.INVISIBLE
////                    } else {
////                        binding.searchResultLocation.emptyBac.visibility = View.INVISIBLE
////                        binding.searchResultLocation.searchList.visibility = View.VISIBLE
////                        /**
////                         * 使用建议搜索服务获取建议列表，结果在onSuggestionResult()中更新
////                         */
////                        if (city == null) {
////                            city = "北京"
////                        }
////                        mSuggestionSearch?.requestSuggestion(SuggestionSearchOption()
////                                .keyword(text).city(city))
////                    }
//                }
//
//                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
//
//                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
//                    val keyword = s?.toString() ?: ""
////                    searchKeywordOnPosition()
//                    if (StringUtils.isEmpty(keyword)) {
//                        binding.searchResultLocation.emptyBac.visibility = View.VISIBLE
//                        binding.searchResultLocation.searchList.visibility = View.INVISIBLE
//                    } else {
//                        binding.searchResultLocation.emptyBac.visibility = View.INVISIBLE
//                        binding.searchResultLocation.searchList.visibility = View.VISIBLE
//                        doSuggestionSearch(keyword, city)
//                    }
//                }
//            })
//            binding.searchResultLocation.searchLocationEdit.setOnKeyListener { _, keyCode, event ->
//                if (keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN) {
//                    val keyword = binding.searchResultLocation.searchLocationEdit.text?.toString() ?: ""
////                    searchKeywordOnPosition()
//                    if (StringUtils.isEmpty(keyword)) {
//                        binding.searchResultLocation.emptyBac.visibility = View.VISIBLE
//                        binding.searchResultLocation.searchList.visibility = View.INVISIBLE
//                    } else {
//                        binding.searchResultLocation.emptyBac.visibility = View.INVISIBLE
//                        binding.searchResultLocation.searchList.visibility = View.VISIBLE
//                        doSuggestionSearch(keyword, city)
//                    }
//                }
//                false
//            }
//            mAdapter.setOnItemClickListener(this)
//            mSearchAdapter.setOnItemClickListener(this)
//            isStartLoc = true
//            checkLocationPermission()
//        } else {
//            val lat = intent.getDoubleExtra("lat", 0.0)
//            val lng = intent.getDoubleExtra("lng", 0.0)
//            val poiName = if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("poiName"))) {
//                intent.getStringExtra("poiName") ?: ""
//            } else ""
//            val poiAddress = if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("poiAddress"))) {
//                intent.getStringExtra("poiAddress") ?: ""
//            } else ""
//
//            //地图导航
//            fun startMap() {
//                val view = View.inflate(mContext, com.joinutech.ddbeslibrary.R.layout.dialog_transmit_bottom_layout, null)
//                val dialog = BottomDialogUtil.showBottomDialog(mContext!!, view, Gravity.BOTTOM)
//                val startBaiduMap = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.takePicture)
//                val startAMap = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.selectPicture)
//                val tencentMapLine = view.findViewById<View>(com.joinutech.ddbeslibrary.R.id.tencentMapLine)
//                val tencentMap = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.tencentMap)
//                startBaiduMap.text = "百度地图"
//                startAMap.text = "高德地图"
//                tencentMapLine.visibility = View.VISIBLE
//                tencentMap.setOnClickListener {
//                    dialog.dismiss()
//                    ThridMapUtil.goToTencentMap(mContext!!, lat, lng, poiName)
//                }
//                startBaiduMap.setOnClickListener {
//                    dialog.dismiss()
//                    ThridMapUtil.startBaiduMap(mContext!!, lat, lng, poiName)
//                }
//                startAMap.setOnClickListener {
//                    dialog.dismiss()
//                    ThridMapUtil.startAmap(mContext!!, lat, lng, poiName)
//                }
//                dialog.show()
//            }
//
//            binding.locationDetailLayou.locationList.visibility = View.GONE
//            binding.locationDetailLayou.addressRv.visibility = View.VISIBLE
//            binding.locationDetailLayou.mapGuide.setOnClickListener(object : OnNoDoubleClickCallback() {
//                override fun onNoDoubleClick(v: View) {
//                    startMap()
//                }
//            })
//            binding.searchLocation.visibility = View.GONE
//            binding.mapCenter.visibility = View.GONE
//            binding.locationDetailLayou.locationName.text = poiName
//            binding.locationDetailLayou.locationAddress.text = poiAddress
//            fun showLocationAtMap() {
//                //百度地图一打开就自动将中心点移动到定位位置
//                locationService?.apply {
//                    //设定中心点坐标
//                    setCenter(mBaiduMap, lat, lng, R.drawable.icon_location_center)
//                    //改变地图状态
//                    zoomMap(mBaiduMap, lat, lng, 16f)
//                }
//            }
//            showLocationAtMap()
//        }
//    }
//
//    /**----------------------- setOnMapStatusChangeListener -----------------------*/
//    /**
//     * 手势操作地图，设置地图状态等操作导致地图状态开始改变。
//     *
//     * @param status 地图状态改变开始时的地图状态
//     */
//    override fun onMapStatusChangeStart(status: MapStatus?) {
//
//    }
//
//    /**
//     * 手势操作地图，设置地图状态等操作导致地图状态开始改变。
//     *
//     * @param status 地图状态改变开始时的地图状态
//     *
//     * @param reason 地图状态改变的原因
//     *               用户手势触发导致的地图状态改变,比如双击、拖拽、滑动底图
//     *               int REASON_GESTURE = 1;
//     *               SDK导致的地图状态改变, 比如点击缩放控件、指南针图标
//     *               int REASON_API_ANIMATION = 2;
//     *               开发者调用,导致的地图状态改变
//     *               int REASON_DEVELOPER_ANIMATION = 3;
//     */
//    override fun onMapStatusChangeStart(status: MapStatus?, reason: Int) {
//
//    }
//
//    /**
//     * 地图状态变化中
//     *
//     * @param status 当前地图状态
//     */
//    override fun onMapStatusChange(status: MapStatus?) {
//
//    }
//
//    /**
//     * 地图状态改变结束
//     *
//     * @param status 地图状态改变结束后的地图状态
//     */
//    override fun onMapStatusChangeFinish(status: MapStatus?) {
//        showLog("地图中心位置变化后 获取中心点信息 ${status?.target?.latitude} -- ${status?.target?.longitude} ")
//        getCenter(status)
//    }

//    private fun getCenter(status: MapStatus?) {
//        val mapStatus = status ?: mBaiduMap.mapStatus
//        val lat = StringUtils.getDigitsNumber(mapStatus.target.latitude, "0.######").toDouble()
//        val lng = StringUtils.getDigitsNumber(mapStatus.target.longitude, "0.######").toDouble()
//        currentCenterLatLng = currentCenterLatLng.copy(lat = lat, lng = lng)
//        showLog("latlng__当前中心点位置为： ${GsonUtil.toJson(currentCenterLatLng)}")
//        //反Geo搜索
//        mSearch.reverseGeoCode(
//                ReverseGeoCodeOption().location(
//                        LatLng(currentCenterLatLng.lat, currentCenterLatLng.lng)
//                )
//        )
//    }

    /**-----------------------setOnMapStatusChangeListener-----------------------*/

    /**-----------------------OnGetGeoCoderResultListener-----------------------*/
//    override fun onGetGeoCodeResult(p0: GeoCodeResult?) {
//
//    }
//
//    override fun onGetReverseGeoCodeResult(reverseGeoCodeResult: ReverseGeoCodeResult?) {
//        if (reverseGeoCodeResult!!.poiList != null && reverseGeoCodeResult.poiList.size > 0) {
//            showLog("--》》获取到POI列表 ${reverseGeoCodeResult.poiList.size}")
//            binding.locationDetailLayou.locationList.visibility = View.VISIBLE
//            binding.locationDetailLayou.NoLocation.visibility = View.GONE
//            mAdapter.setDataList(reverseGeoCodeResult.poiList)
//            binding.locationDetailLayou.locationList.scrollToPosition(0)
//        }
//    }
//    /**-----------------------OnGetGeoCoderResultListener-----------------------*/
//
//    /**-----------------------setOnGetPoiSearchResultListener-----------------------*/
//    override fun onGetPoiResult(result: PoiResult?) {
//        if (result == null || result.error == SearchResult.ERRORNO.RESULT_NOT_FOUND) {
//            toastShort("抱歉，未搜索到对应结果2")
//            return
//        }
//        if (result.error == SearchResult.ERRORNO.NO_ERROR) {
//            mBaiduMap.clear()
//            showLog("--》》检索到周边POI列表2 ${result.allPoi.size}")
//            mAdapter.setDataList(result.allPoi)
//            mAdapter.notifyDataSetChanged()
//            val ll = result.allPoi[0].location
//            val u = MapStatusUpdateFactory.newLatLng(ll)
//            mBaiduMap.animateMapStatus(u)
//            return
//        }
//    }
//
//    /**deprecated*/
//    override fun onGetPoiDetailResult(p0: PoiDetailResult?) {
//
//    }
//
//    override fun onGetPoiDetailResult(result: PoiDetailSearchResult?) {
//        if (result!!.error != SearchResult.ERRORNO.NO_ERROR) run {
//            toastShort("抱歉，未搜索到对应结果3")
//        }
//    }
//
//    override fun onGetPoiIndoorResult(p0: PoiIndoorResult?) {
//
//    }
//
//    /**-----------------------setOnGetPoiSearchResultListener-----------------------*/
//
//    override fun onNoDoubleClick(v: View) {
//        when (v) {
////            binding.locationDetailLayou.mapGuide -> {
////                startMap()
////            }
//            binding.resetLoc -> {
//                isFirstLoc = true
//                checkLocationPermission()
//            }
//            tv_rightTitle -> sendLocation()
//            binding.searchLocation -> {
//                binding.searchLocation.visibility = View.INVISIBLE
//                binding.searchResultLocation.searchRv.visibility = View.VISIBLE
//            }
//            binding.searchResultLocation.cancle -> {
//                binding.searchLocation.visibility = View.VISIBLE
//                binding.searchResultLocation.searchRv.visibility = View.INVISIBLE
//                binding.searchResultLocation.searchLocationEdit.text.clear()
//                mSearchAdapter.clearData()
//            }
//            binding.locationDetailLayou.retryRv -> {
//                isFirstLoc = true
//                checkLocationPermission()
//            }
//            binding.searchResultLocation.confirmSearch -> {
//                searchKeywordOnPosition()
//            }
//        }
//    }

    private fun sendLocation() {
//        val choicePoiInfo = mAdapter.getChoicePoiInfo()
//        choicePoiInfo?.let {
//            currentCenterLatLng = currentCenterLatLng.copy(
//                    lat = it.location.latitude, lng = it.location.longitude,
//                    name = it.name, address = it.address)
//        }
//        currentCenterLatLng.let { latLng ->
//            // 当前对象为参数，返回最后一行内容
//            showLog("center lat = ${latLng.lat} ,lng = ${latLng.lng}")
//
//            val lat = StringUtils.getDigitsNumber(latLng.lat, "0.######").toDouble()
//            val lng = StringUtils.getDigitsNumber(latLng.lng, "0.######").toDouble()
//
//            val intent = Intent()
//            intent.putExtra("latitude", lat)
//            intent.putExtra("longitude", lng)
//            intent.putExtra("address", latLng.address)
//            intent.putExtra("city", latLng.city)
//            intent.putExtra("title", latLng.name)
//
//            val uri = "http://api.map.baidu.com" +
//                    "/staticimage/v2?width=450&height=300&" +
//                    "center=${latLng.lng},${latLng.lat}" +
//                    "&zoom=17&markers=${latLng.lng},${latLng.lat}" +
//                    "&markerStyles=m,A&ak=oNTFxeyWSyizNLjLwtE3A0kVlAwMuVWV"
//
//            when (type) {
//                "selectMapLocation" -> {
//                    //发送地理位置
////                    val uri = "http://api.map.baidu.com" +
////                            "/staticimage?width=450&height=300&" +
////                            "center=${latLng.lng},${latLng.lat}" +
////                            "&zoom=17&markers=${latLng.lng},${latLng.lat}" +
////                            "&markerStyles=m,A"
//                    intent.putExtra("locuri", uri)
//                    setResult(RESULT_OK, intent)
//                }
//                "addAttendanceLocation", //添加考勤位置
//                    //选择位置
//                "selectLocation" -> {
//
//                    intent.putExtra("uri", uri)
//                    setResult(Activity.RESULT_OK, intent)
//                }
//            }
//        }
//        finish()
    }

    inner class MyLocationListener : LocationCallback {
        override fun getTag(): String = "location"
        override fun onLocationResult(result: LocationResult) {
            showLog("获取到定位信息 ${GsonUtil.toJson(result)}")
            // map view 销毁后不在处理新接收的位置
//            if (!result.locationSuccess || binding.bdMapView == null) return
            if (isFirstLoc) {
                city = result.city
            }
            currentCenterLatLng = result
//            mCurrentLat = result.lat
//            mCurrentLon = result.lng
//            mCurrentAccracy = result.radius

//            locationService?.apply {
//                setLocation(mBaiduMap,
//                        currentCenterLatLng.lat,
//                        currentCenterLatLng.lng,
//                        mCurrentAccracy,
//                        mCurrentDirection)
//
//                // 设置自定义图标
//                addMarker(mBaiduMap, R.drawable.icon_location_start)
//                if (isFirstLoc && !result.city.isNullOrBlank()) {
//                    isFirstLoc = false
//                    zoomMap(mBaiduMap, currentCenterLatLng.lat, currentCenterLatLng.lng,
//                            16f, true)
//                }
//            }
        }

        override fun onPoiLocationResult(result: LocationResult) {
            // 不需要解析POI，当前页面会自己完成POI选择
        }
    }

    override fun onStart() {
        super.onStart()
        //开启方向传感器
        myOrientationListener!!.start()
    }

    override fun onResume() {
//        binding.bdMapView.onResume()
        super.onResume()
    }

    override fun onPause() {
//        binding.bdMapView.onPause()
        super.onPause()
    }

    override fun onStop() {
        super.onStop()
        myOrientationListener!!.stop()
    }

    override fun onDestroy() {
//        if (type == "selectMapLocation" || type == "addAttendanceLocation") {
//            mPoiSearch?.destroy()
//            mSuggestionSearch?.destroy()
//        }
//        if (isStartLoc && getLocationPermission) {
//            //退出时销毁定位
//            locationService?.apply {
//                unregisterListener(mListener)
//                stop()
//            }
//            //关闭定位图层
//            mBaiduMap.isMyLocationEnabled = false
//        }
//        binding.bdMapView.onDestroy()
        super.onDestroy()
    }

//    override fun onItemClick(view: View, result: PoiInfo, adapterType: String) {
//        if ("selectMapLocation" == adapterType) {
//            val ll = result.location
//            val u = MapStatusUpdateFactory.newLatLng(ll)
//            mBaiduMap.animateMapStatus(u)
//        } else {
//            if (currentFocus != null && currentFocus?.windowToken != null) {
//                (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
//                        .hideSoftInputFromWindow(currentFocus!!.windowToken,
//                                InputMethodManager.HIDE_NOT_ALWAYS)
//            }
//            binding.searchLocation.visibility = View.VISIBLE
//            binding.searchResultLocation.searchRv.visibility = View.INVISIBLE
//            binding.searchResultLocation.searchLocationEdit.text.clear()
//            mSearchAdapter.clearData()
//            mPoiSearch?.searchInCity(PoiCitySearchOption()
//                    .city(city)
//                    .cityLimit(false)
//                    .keyword(result.name).pageNum(0))
//        }
//    }
}