package com.joinutech.message.view.tcpimpages.imadapter

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.library.im.imtcp.ConstantImMsgType
import com.ddbes.library.im.imtcp.Logger
import com.ddbes.library.im.netutil.ImNetApprovalUtil
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.joinutech.common.util.CompanyHolder
import com.joinutech.ddbeslibrary.bean.PageInfoBean
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.joinutech.ddbeslibrary.bean.UploadFileBean
import com.joinutech.ddbeslibrary.imbean.DataBeanButton
import com.joinutech.ddbeslibrary.imbean.DataBeanDetail
import com.joinutech.ddbeslibrary.imbean.DetailReportContent
import com.joinutech.ddbeslibrary.imbean.FlutterPushData
import com.joinutech.ddbeslibrary.imbean.NoticeMsgBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.utils.COMMON_WEB_ACTIVITY
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.ConsKeys
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.OnNoDoubleClickListener
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.XUtil
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.utils.toast
import com.joinutech.ddbeslibrary.utils.toastShort
import com.joinutech.ddbeslibrary.widget.CircleImageView
import com.joinutech.ddbeslibrary.widget.roundimage.RoundedImageView
import com.joinutech.flutter.EventApplyDetailPage
import com.joinutech.flutter.EventApplyInfoData
import com.joinutech.flutter.EventDoneWith
import com.joinutech.flutter.EventRefreshApprove
import com.joinutech.flutter.EventToApproveDetail
import com.joinutech.flutter.EventToApproveList
import com.joinutech.message.R
import com.joinutech.message.bean.ReportInviteMsgBean
import com.joinutech.message.util.TimeUtils
import com.joinutech.message.view.tcpimpages.TcpNoticeListActivity
import com.joinutech.message.view.tcpimpages.imadapter.approve_content_adapter.ApproveContentItemAdapter
import com.joinutech.message.view.tcpimpages.utils.MsgActionHolder
import com.trello.rxlifecycle3.LifecycleTransformer
import okhttp3.MediaType
import okhttp3.RequestBody
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

class NoticeListAdapter(
    var life: LifecycleTransformer<Result<Any>>, var userId: String?, var accessToken: String,
    var mActivity: Activity, val dataList: ArrayList<NoticeMsgBean>
) : RecyclerView.Adapter<NoticeListAdapter.MsgViewHolder>() {
    val tcpNoticeListActivity = mActivity as TcpNoticeListActivity
    var secondImageWidth = 0
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MsgViewHolder {
        val itemView = LayoutInflater.from(mActivity)
            .inflate(R.layout.item_total_notice, parent, false)
        return MsgViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return if (dataList.isNullOrEmpty()) 0 else dataList.size
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(msgHolder: MsgViewHolder, position: Int) {

        if (dataList.isNotEmpty()) {
            if (position == dataList.lastIndex) {
                msgHolder.spaceHolder.visibility = View.VISIBLE
            } else {
                msgHolder.spaceHolder.visibility = View.GONE
            }
            val bean = dataList[position]
            msgHolder.showTargetLayout(bean.tempType)
//            val itemTime = TimeUtils.getTime2(bean.timestamp)//格式化时间
            val itemTime = TimeUtils.getTime(bean.timestamp)//格式化时间
            //填充数据
            Logger.i("验证审批bean", "===bean.json==" + GsonUtil.toJson(bean))
            msgHolder.forthNoticeWatch.visibility = View.VISIBLE
            when (bean.tempType) {
                1 -> {//系统通知------------模板1------------------------------------------------------------------
                    msgHolder.firstItemTime.text = itemTime
                    //显示通知头像
                    if (bean.iconUrl.isNullOrEmpty()){
                        msgHolder.firstNoticeIcon.setImageResource(R.drawable.system_notification_icon)
                    }else{
                        ImageLoaderUtils.loadImage(mActivity, msgHolder.firstNoticeIcon, bean.iconUrl)
                    }

                    //显示用户头像
                    try {
                        val dataBeanDetail =
                            GsonUtil.fromJson(bean.detail, DataBeanDetail::class.java)//json转对象
                        if (dataBeanDetail?.header?.isNotBlank() ?: false) {
                            msgHolder.firstUserIcon.visibility = View.VISIBLE
                            ImageLoaderUtils.loadImage(
                                mActivity, msgHolder.firstUserIcon, dataBeanDetail?.header
                                    ?: ""
                            )
                        } else {
                            msgHolder.firstUserIcon.visibility = View.GONE
                        }

                    }catch (e: Exception){
                        msgHolder.firstUserIcon.visibility = View.GONE
                        e.printStackTrace()
                    }

                    //显示通知内容
                    msgHolder.firstNoticeTitle.text = bean.context
                    val dealTime = CommonUtils.stampToDate(bean.timestamp)//格式化时间
                    msgHolder.firstNoticeTime.text = dealTime
                    //点击详情
                    msgHolder.firstWatch.setOnClickListener(object : OnNoDoubleClickListener {
                        override fun onNoDoubleClick(v: View) {
                            checkMyCompany(bean, true) {
                                firstWatchClick(bean)
                            }
                        }
                    })
                    msgHolder.firstItemLayout.setOnClickListener(object : OnNoDoubleClickListener {
                        override fun onNoDoubleClick(v: View) {
                            checkMyCompany(bean) {
                                firstWatchClick(bean)
                            }
                        }
                    })
                }
                2 -> {//考勤打卡的大图样式-----------------------模板2-------------------------------------------------------
                    msgHolder.secondItemTime.text = itemTime
                    //显示通知头像
                    ImageLoaderUtils.loadImage(mActivity, msgHolder.secondNoticeIcon, bean.iconUrl)

                    //显示通知内容
                    if (!bean.noticeName.isNullOrBlank()) {
                        XUtil.showView(msgHolder.secondNoticeName)
                        msgHolder.secondNoticeName.text = bean.noticeName
                    } else {
                        XUtil.hideView(msgHolder.secondNoticeName)
                    }
                    if (!bean.noticeTitle.isNullOrBlank()) {
                        XUtil.showView(msgHolder.secondNoticeTitle)
                        msgHolder.secondNoticeTitle.text = bean.noticeTitle
                    } else {
                        XUtil.hideView(msgHolder.secondNoticeTitle)
                    }
                    //显示模板2的大图图片//修改控件高度tcp
                    /* Logger.i("----执行----验证模板2大图样式--开始显示--","----secondImageWith----"+secondImageWidth)
                     val imageLayoutParams = msgHolder.secondImageLayout.layoutParams as ConstraintLayout.LayoutParams
                     imageLayoutParams.height=secondImageWidth/2
                     msgHolder.secondImageLayout.requestLayout()
                     Logger.i("----执行----验证模板2大图样式----","----secondImageHeight----"+msgHolder.secondImageLayout.height)*/

                    val dataBeanDetail = GsonUtil.fromJson(bean.detail, DataBeanDetail::class.java)
                    Logger.i(
                        "----执行----验证模板2大图样式----",
                        "----backgroundUrl----" + dataBeanDetail?.backgroundUrl
                    )
                    Logger.i(
                        "----执行----验证模板2大图样式----",
                        "----promptTitle----" + dataBeanDetail?.promptTitle
                    )
                    ImageLoaderUtils.loadImage(
                        mActivity, msgHolder.secondImageBg, dataBeanDetail?.backgroundUrl
                            ?: ""
                    )
                    msgHolder.secondImageText.text = dataBeanDetail?.promptTitle ?: ""
                    //点击模板2
                    msgHolder.secondImageLayout.setOnClickListener(object :
                        OnNoDoubleClickListener {
                        override fun onNoDoubleClick(v: View) {
//                            secondNoticeClick(bean)
                            checkMyCompany(bean) {
                                thirdWatchClick(bean)
                            }
                        }
                    })
                }
                3 -> {//工作通知通用样式-----------------------------模板3-------------------------------------------------
                    msgHolder.thirdItmeTime.text = itemTime
                    //显示通知头像
                    ImageLoaderUtils.loadImage(mActivity, msgHolder.thirdNoticeIcon, bean.iconUrl)

                    //显示通知内容
                    if (!bean.noticeName.isNullOrBlank()) {
                        XUtil.showView(msgHolder.thirdNoticeName)
                        msgHolder.thirdNoticeName.text = bean.noticeName
                    } else {
                        XUtil.hideView(msgHolder.thirdNoticeName)
                    }
                    if (!bean.noticeTitle.isNullOrBlank()) {
                        XUtil.showView(msgHolder.thirdNoticeTitle)
                        msgHolder.thirdNoticeTitle.text = bean.noticeTitle
                    } else {
                        XUtil.hideView(msgHolder.thirdNoticeTitle)
                    }
                    //填充content，如果msgType是20300（有人给我提交了汇报）做特殊处理
                    when (bean.msgType) {
                        ConstantImMsgType.SSChatMessageTypeWorkSubmitReport -> {//20300，有人提交了汇报
                            val dataBeanDetail =
                                GsonUtil.fromJson(bean.detail, DataBeanDetail::class.java)
                            val contentList1 = arrayListOf<DetailReportContent>()
                            if (dataBeanDetail != null) {
                                val tempList = GsonUtil.fromJson2<ArrayList<DetailReportContent>>(
                                    dataBeanDetail.reportContent,
                                    object : TypeToken<ArrayList<DetailReportContent>>() {}.type
                                )
                                Logger.i("----执行----", "---tempList.size--" + tempList?.size)
                                if (tempList != null) {
                                    contentList1.addAll(tempList)
                                }
                            }
                            when (contentList1.size) {
                                0 -> {
                                    XUtil.hideView(
                                        msgHolder.thirdNoticeContent1,
                                        msgHolder.thirdNoticeContent2,
                                        msgHolder.thirdNoticeContent3
                                    )
                                }
                                1 -> {
                                    XUtil.hideView(
                                        msgHolder.thirdNoticeContent2,
                                        msgHolder.thirdNoticeContent3
                                    )
                                    XUtil.showView(msgHolder.thirdNoticeContent1)
                                    msgHolder.thirdNoticeContent1.text =
                                        "${contentList1[0].title}：${contentList1[0].prompt}"
                                }
                                2 -> {
                                    XUtil.hideView(msgHolder.thirdNoticeContent3)
                                    XUtil.showView(
                                        msgHolder.thirdNoticeContent1,
                                        msgHolder.thirdNoticeContent2
                                    )
                                    msgHolder.thirdNoticeContent1.text =
                                        "${contentList1[0].title}：${contentList1[0].prompt}"
                                    msgHolder.thirdNoticeContent2.text =
                                        "${contentList1[1].title}：${contentList1[1].prompt}"
                                }
                                3 -> {
                                    XUtil.showView(
                                        msgHolder.thirdNoticeContent1,
                                        msgHolder.thirdNoticeContent2,
                                        msgHolder.thirdNoticeContent3
                                    )
                                    msgHolder.thirdNoticeContent1.text =
                                        "${contentList1[0].title}：${contentList1[0].prompt}"
                                    msgHolder.thirdNoticeContent2.text =
                                        "${contentList1[1].title}：${contentList1[1].prompt}"
                                    msgHolder.thirdNoticeContent3.text =
                                        "${contentList1[2].title}：${contentList1[2].prompt}"
                                }
                            }
                        }
                        else -> {//其他情况
                            var contentList= arrayListOf<String>()
                            if (bean.content.contains("[")) {
                                contentList = Gson().fromJson<ArrayList<String>>(
                                    bean.content,
                                    object : TypeToken<ArrayList<String>>() {}.type
                                )
                            }else{
                                contentList.add(bean.content)
                            }

                            when (contentList.size) {
                                0 -> {
                                    XUtil.hideView(
                                        msgHolder.thirdNoticeContent1,
                                        msgHolder.thirdNoticeContent2,
                                        msgHolder.thirdNoticeContent3
                                    )
                                }
                                1 -> {
                                    XUtil.hideView(
                                        msgHolder.thirdNoticeContent2,
                                        msgHolder.thirdNoticeContent3
                                    )
                                    XUtil.showView(msgHolder.thirdNoticeContent1)
                                    msgHolder.thirdNoticeContent1.text = contentList[0]
                                }
                                2 -> {
                                    XUtil.hideView(msgHolder.thirdNoticeContent3)
                                    XUtil.showView(
                                        msgHolder.thirdNoticeContent1,
                                        msgHolder.thirdNoticeContent2
                                    )
                                    msgHolder.thirdNoticeContent1.text = contentList[0]
                                    msgHolder.thirdNoticeContent2.text = contentList[1]
                                }
                                3 -> {
                                    XUtil.showView(
                                        msgHolder.thirdNoticeContent1,
                                        msgHolder.thirdNoticeContent2,
                                        msgHolder.thirdNoticeContent3
                                    )
                                    msgHolder.thirdNoticeContent1.text = contentList[0]
                                    msgHolder.thirdNoticeContent2.text = contentList[1]
                                    msgHolder.thirdNoticeContent3.text = contentList[2]
                                }
                            }
                        }
                    }
                    //点击详情
                    msgHolder.thirdWatch.setOnClickListener(object : OnNoDoubleClickListener {
                        override fun onNoDoubleClick(v: View) {
                            checkMyCompany(bean, true) {
                                thirdWatchClick(bean)
                            }
                        }
                    })
                    msgHolder.thirdItemLayout.setOnClickListener(object : OnNoDoubleClickListener {
                        override fun onNoDoubleClick(v: View) {
                            checkMyCompany(bean) {
                                thirdWatchClick(bean)
                            }
                        }
                    })
                }
                4 -> {  //审批通知的样式--------------------------模板4----------------------------------------------------

                    NoticeParser.parseApproveNoticeType(4 ,mActivity , msgHolder , bean , itemTime , forthNoticeWatchItemClick = {

                        checkMyCompany(bean, true , result = {
                            forthWatchClick(bean)
                        })

                    } , forthItemLayoutItemClick = {
                        checkMyCompany(bean) {
                            forthWatchClick(bean)
                        }

                    } , forthNoticeButton1ItemClick = { button ->
                        checkMyCompany(bean) {
                            if (button.needToSignature) {
                                //需要签名
                                buttonClickListener?.onButtonClick(bean, button)
                            }else{
                                //不需要签名
                                forthButtonClick(bean, button)
                            }

                        }
                    } , forthNoticeButton2ItemClick = { button ->
                        checkMyCompany(bean) {
                            if (button.needToSignature) {
                                buttonClickListener?.onButtonClick(bean, button)
                            } else {
                                forthButtonClick(bean, button)
                            }
                        }
                    } )

                }
                5 -> {//纯提示无详情------------------------------------------------------------------------------
                    msgHolder.fifthItemTime.text = itemTime
                    //显示通知头像
                    ImageLoaderUtils.loadImage(mActivity, msgHolder.fifthNoticeIcon, bean.iconUrl)

                    //显示通知name和title
                    if (!bean.noticeName.isNullOrBlank()) {
                        XUtil.showView(msgHolder.fifthNoticeName)
                        msgHolder.fifthNoticeName.text = bean.noticeName
                    } else {
                        XUtil.hideView(msgHolder.fifthNoticeName)
                    }
                    if (!bean.noticeTitle.isNullOrBlank()) {
                        XUtil.showView(msgHolder.fifthNoticeTitle)
                        msgHolder.fifthNoticeTitle.text = bean.noticeTitle
                    } else {
                        XUtil.hideView(msgHolder.fifthNoticeTitle)
                    }
                    //填充content
                    val contentList = Gson().fromJson<ArrayList<String>>(
                        bean.content,
                        object : TypeToken<ArrayList<String>>() {}.type
                    )
                    when (contentList.size) {
                        0 -> {
                            XUtil.hideView(
                                msgHolder.fifthNoticeContent1,
                                msgHolder.fifthNoticeContent2,
                                msgHolder.fifthNoticeContent3
                            )
                        }
                        1 -> {
                            XUtil.hideView(
                                msgHolder.fifthNoticeContent2,
                                msgHolder.fifthNoticeContent3
                            )
                            XUtil.showView(msgHolder.fifthNoticeContent1)
                            msgHolder.fifthNoticeContent1.text = contentList[0]
                        }
                        2 -> {
                            XUtil.hideView(msgHolder.fifthNoticeContent3)
                            XUtil.showView(
                                msgHolder.fifthNoticeContent1,
                                msgHolder.fifthNoticeContent2
                            )
                            msgHolder.fifthNoticeContent1.text = contentList[0]
                            msgHolder.fifthNoticeContent2.text = contentList[1]
                        }
                        3 -> {
                            XUtil.showView(
                                msgHolder.fifthNoticeContent1,
                                msgHolder.fifthNoticeContent2,
                                msgHolder.fifthNoticeContent3
                            )
                            msgHolder.fifthNoticeContent1.text = contentList[0]
                            msgHolder.fifthNoticeContent2.text = contentList[1]
                            msgHolder.fifthNoticeContent3.text = contentList[2]
                        }
                    }
                }
                6 -> {//20301(别人邀请我提交汇报的特殊处理)-------------模板6-------------------------------------------------
                    msgHolder.sixthItmeTime.text = itemTime
                    //显示通知头像
                    ImageLoaderUtils.loadImage(mActivity, msgHolder.sixthNoticeIcon, bean.iconUrl)

                    //显示通知内容
                    if (!bean.noticeName.isNullOrBlank()) {
                        XUtil.showView(msgHolder.sixthNoticeName)
                        msgHolder.sixthNoticeName.text = bean.noticeName
                    } else {
                        XUtil.hideView(msgHolder.sixthNoticeName)
                    }
                    if (!bean.noticeTitle.isNullOrBlank()) {
                        XUtil.showView(msgHolder.sixthNoticeTitle)
                        msgHolder.sixthNoticeTitle.text = bean.noticeTitle
                    } else {
                        XUtil.hideView(msgHolder.sixthNoticeTitle)
                    }
                    //填充content
                    //tcp之json转集合
                    /*//json转集合的方式1
                    val contentList = Gson().fromJson<ArrayList<String>>(bean.content,
                            object : TypeToken<ArrayList<String>>() {}.type)
                    //json转集合的方式2
                    GsonUtil.fromJson<DetailReportContent>(dataBeanDetail.reportContent,Array<DetailReportContent>::class.java)
                    //json转集合的方式3
                    val fromJson2 = GsonUtil.fromJson2<ArrayList<DetailReportContent>>(dataBeanDetail.reportContent,
                            object : TypeToken<ArrayList<DetailReportContent>>() {}.type)*/

                    val dataBeanDetail = GsonUtil.fromJson(bean.detail, DataBeanDetail::class.java)
                    XUtil.hideView(msgHolder.sixthNoticeContent2, msgHolder.sixthNoticeContent3)
                    XUtil.showView(msgHolder.sixthNoticeContent1)
                    if (dataBeanDetail != null) {//此时的类型是别人邀请我提交汇报
                        val reportInviteMsgBean = ReportInviteMsgBean(
                            dataBeanDetail.avatar,
                            dataBeanDetail.end,
                            dataBeanDetail.endDay,
                            dataBeanDetail.holiday,
                            dataBeanDetail.modelName,
                            dataBeanDetail.period,
                            dataBeanDetail.pre,
                            dataBeanDetail.start,
                            dataBeanDetail.startDay,
                            dataBeanDetail.userId,
                            dataBeanDetail.userName,
                            dataBeanDetail.week
                        )
                        msgHolder.sixthNoticeContent1.text =
                            showMsgSubheadContent(reportInviteMsgBean)
                    }

                    //点击详情
                    msgHolder.sixthWatch.setOnClickListener(object : OnNoDoubleClickListener {
                        override fun onNoDoubleClick(v: View) {
                            checkMyCompany(bean, true) {
                                sixthWatchClick(bean)
                            }
                        }
                    })
                    msgHolder.sixthItemLayout.setOnClickListener(object : OnNoDoubleClickListener {
                        override fun onNoDoubleClick(v: View) {
                            checkMyCompany(bean) {
                                sixthWatchClick(bean)
                            }
                        }
                    })
                }

                7 -> {
                    // 机器人消息 在群组消息中，不在此处解析
                }

                // 审批结构样式修改为左右结构的：
                8 -> {
                    NoticeParser.parseApproveNoticeType(8 , mActivity , msgHolder , bean , itemTime , forthNoticeWatchItemClick = {

                        checkMyCompany(bean, true , result = {
                            forthWatchClick(bean)
                        })

                    } , forthItemLayoutItemClick = {
                        checkMyCompany(bean) {
                            forthWatchClick(bean)
                        }

                    } , forthNoticeButton1ItemClick = { button ->
                        checkMyCompany(bean) {
                            if (button.needToSignature) {
                                //需要签名
                                buttonClickListener?.onButtonClick(bean, button)
                            }else{
                                //不需要签名
                                forthButtonClick(bean, button)
                            }

                        }
                    } , forthNoticeButton2ItemClick = { button ->
                        checkMyCompany(bean) {
                            if (button.needToSignature) {
                                buttonClickListener?.onButtonClick(bean, button)
                            } else {
                                forthButtonClick(bean, button)
                            }
                        }
                    } )
                }

                else -> {
                    // 未知类型

                }
            }
        }
    }

    //按钮或者详情的点击事件在这里
    fun forthButtonClick(bean: NoticeMsgBean?, mbutton: DataBeanButton?,uploadFileBean:UploadFileBean?=null , successResult: (() -> Unit)? = null) {
        if (mbutton == null) {
            return
        }
        tcpNoticeListActivity.showLoading()
        Logger.i("-------执行----", "---点击按钮了---")
        when (mbutton.methodType) {
            1 -> {//get请求
                ImNetApprovalUtil.executeGetMethod(life, mbutton.apiUrl, accessToken, onSuccess = {
                    Logger.i("-------执行----", "---点击按钮了成功了-it--" + it)
                    //因为点击的一定是审批类的消息，所以在审批的表中查找
                    ToastUtil.show(mActivity, "操作成功！")
                    tcpNoticeListActivity.hideLoading()
                }, onFailer = {
                    Logger.i("-------执行----", "---点击按钮了失败了-it--" + it)
                    ToastUtil.show(mActivity, " ${it}")
                    tcpNoticeListActivity.hideLoading()
                })
            }
            2 -> {//post请求
                if (uploadFileBean != null) {
                    val jsonObject=JSONObject(mbutton.body)
                    val jsonObject2=JSONObject()
                    jsonObject2.put("fileId",uploadFileBean.fileId)
                    jsonObject2.put("hash",uploadFileBean.hash)
                    jsonObject2.put("fileName",uploadFileBean.fileName)
                    jsonObject.put("signatureImage",jsonObject2)
                    mbutton.body=jsonObject.toString()
                }

                val parse = MediaType.parse("application/json; charset=utf-8")
                val requestBody = RequestBody.create(parse, mbutton.body)
                ImNetApprovalUtil.executePostMethod(
                    life,
                    mbutton.apiUrl,
                    accessToken,
                    requestBody,
                    onSuccess = {
                        Logger.i("-------执行----", "---点击按钮了成功了-it--" + it)
                        ToastUtil.show(mActivity, "操作成功！")
                        tcpNoticeListActivity.hideLoading()

                        EventBusUtils.sendEvent(EventBusEvent("UpdateApproveStatus",NoticeMsgBean()))
                    },
                    onFailer = {
                        Logger.i("-------执行----", "---点击按钮了失败了-it--" + it)
                        ToastUtil.show(mActivity, " ${it}")
                        tcpNoticeListActivity.hideLoading()
                    })
            }
            3 -> {//put请求
                if (mbutton.body.isNullOrBlank()) {//没有body
                    ImNetApprovalUtil.executePutMethodWithoutBody(
                        life,
                        mbutton.apiUrl,
                        accessToken,
                        onSuccess = {
                            ToastUtil.show(mActivity, "操作成功！")
                            tcpNoticeListActivity.hideLoading()
                        },
                        onFailer = {
                            ToastUtil.show(mActivity, " ${it}")
                            tcpNoticeListActivity.hideLoading()
                        })
                } else {//有body
                    if (uploadFileBean != null) {
                        val jsonObject=JSONObject(mbutton.body)
                        val jsonObject2=JSONObject()
                        jsonObject2.put("fileId",uploadFileBean.fileId)
                        jsonObject2.put("hash",uploadFileBean.hash)
                        jsonObject2.put("fileName",uploadFileBean.fileName)
                        jsonObject.put("signatureImage",jsonObject2)
                        mbutton.body=jsonObject.toString()
                    }
                    val parse = MediaType.parse("application/json; charset=utf-8")
                    val requestBody = RequestBody.create(parse, mbutton.body)
                    ImNetApprovalUtil.executePutMethodWithBody(
                        life,
                        mbutton.apiUrl,
                        accessToken,
                        requestBody,
                        onSuccess = {
                            ToastUtil.show(mActivity, "操作成功！")
                            tcpNoticeListActivity.hideLoading()
                        },
                        onFailer = {
                            ToastUtil.show(mActivity, " ${it}")
                            tcpNoticeListActivity.hideLoading()
                        })
                }
            }
            4 -> {//delete请求
                if (mbutton.body.isNullOrBlank()) {//没有body
                    ImNetApprovalUtil.executeDeleteMethodWithoutBody(
                        life,
                        mbutton.apiUrl,
                        accessToken,
                        onSuccess = {
                            ToastUtil.show(mActivity, "操作成功！")
                            tcpNoticeListActivity.hideLoading()
                        },
                        onFailer = {
                            ToastUtil.show(mActivity, " ${it}")
                            tcpNoticeListActivity.hideLoading()
                        })
                } else {//有body
                    val parse = MediaType.parse("application/json; charset=utf-8")
                    val requestBody = RequestBody.create(parse, mbutton.body)
                    ImNetApprovalUtil.executeDeleteMethodWithBody(
                        life,
                        mbutton.apiUrl,
                        accessToken,
                        requestBody,
                        onSuccess = {
                            ToastUtil.show(mActivity, "操作成功！")
                            tcpNoticeListActivity.hideLoading()
                        },
                        onFailer = {
                            ToastUtil.show(mActivity, " ${it}")
                            tcpNoticeListActivity.hideLoading()
                        })
                }
            }
            6 -> {
                if (StringUtils.isNotBlankAndEmpty(mbutton.apiUrl)) {
                    Logger.i("直接跳转","==apiUrl=${mbutton.apiUrl}")
                    MsgActionHolder().onAction1000(mbutton.apiUrl)
                }
            }

            // todo methodType  7 按钮跳转类型 跳转flutter   apiUrl代表routeName body代表argumengs
            7 -> {
                try {
                    Logger.i("moon" , "methodType  7----${mbutton.apiUrl}")
                    tcpNoticeListActivity.hideLoading()
                    val jsonObject= JSONObject(mbutton.body)
                    val orgId = jsonObject.opt("orgId") as? String
                    val approveId = jsonObject.opt("approveId") as? String
                    EventBus.getDefault().post(EventDoneWith(mbutton.apiUrl,orgId ?:"",approveId ?:""))
                }catch (e: java.lang.Exception){}
            }
        }
    }

    private fun refreshFlutterRedDot(orgId : String) {
        EventBus.getDefault().post(EventRefreshApprove())
    }

    //设置点击“同意”和“拒绝”的监听
    interface ButtonClickListener{
        fun onButtonClick(bean:NoticeMsgBean,button:DataBeanButton)
    }

    var buttonClickListener:ButtonClickListener?=null
    fun setFourthButtonsClickListener(listener: ButtonClickListener) {
        buttonClickListener=listener
    }

    private fun firstWatchClick(bean: NoticeMsgBean) {
        Logger.i("-------执行----", "---点击系统详情了-functionType--" + bean.functionType)

        when (bean.functionType) {
            0 -> {
            }
            1 -> {//好友申请
//                MsgActionHolder().onActionOne(bean.routeId)
                EventBus.getDefault().post(EventApplyDetailPage( recordId = bean.routeId , type = 0))
            }
            2 -> {//团队申请
//                MsgActionHolder().onActionTwo(bean.routeId)
                EventBus.getDefault().post(EventApplyDetailPage( recordId = bean.routeId , companyId = bean.companyId, type = 2))
            }
            3 -> {//外部协作人
//                MsgActionHolder().onActionThree(bean.routeId)
                EventBus.getDefault().post(EventApplyDetailPage( recordId = bean.routeId , type = 1))
            }
            4 -> {//外部协作文件夹web
//                MsgActionHolder().onActionFour()
            }
            5 -> {//本地im消息数据详情
                var icon = ""
                var name = ""
                try {
                    val dataBeanDetail = GsonUtil.fromJson(bean.detail, DataBeanDetail::class.java)
                    icon = dataBeanDetail?.handlerAvatar ?: ""
                    name = dataBeanDetail?.handlerName ?: ""
                }catch (e: Exception){
                    e.printStackTrace()
                }
                val contentList = Gson().fromJson<ArrayList<String>>(
                    bean.content,
                    object : TypeToken<ArrayList<String>>() {}.type
                )
                MsgActionHolder().onActionFive(
                    mActivity,
                    bean.companyLogo,
                    icon,
                    name,
                    contentList.firstOrNull() ?: "",
                    bean.timestamp,
                    isFrom = ConstantImMsgType.SystemMsgSessionTyp,
                    companyName = bean.companyName,
                    msgType = bean.msgType
                )
            }
        }
    }

    /**考勤缺卡时间，如果存在，则加载指定日期的考勤数据*/
    private fun secondNoticeClick(bean: NoticeMsgBean) {
        Logger.i("-------执行----", "---点击打卡按钮了-functionType--" + bean.functionType)
        when (bean.functionType) {
            200 -> {
                MsgActionHolder().onAction200(bean.companyId, 0L)
            }
        }

    }

    private fun thirdWatchClick(bean: NoticeMsgBean) {
        Logger.i("-------执行----", "---点击工作通知详情了--functionType-" + bean.functionType)
        var spaceTimeLong = 0L
        val dataBeanDetail = GsonUtil.fromJson(bean.detail, DataBeanDetail::class.java)
        //缺卡提醒  //打卡提醒
        if (bean.msgType == ConstantImMsgType.SSChatMessageTypeWorkAttendanceLackRemind) {
            spaceTimeLong = dataBeanDetail?.needClock ?: 0L
            Logger.i("-------执行----", "---点击工作通知详情了-缺卡提醒时间-spaceTimeLong-" + spaceTimeLong)
        }
        if (bean.msgType == ConstantImMsgType.SSChatMessageTypeWorkAttendanceClockInRemind) {
            spaceTimeLong = dataBeanDetail?.needClock ?: 0L
            Logger.i("-------执行----", "---点击工作通知详情了-提醒打卡提醒时间-spaceTimeLong-" + spaceTimeLong)
        }
        when (bean.functionType) {
            100 -> {//审批详情
                if (dataBeanDetail != null) {
//                    MsgActionHolder().onAction100(
//                        bean.companyId,
//                        dataBeanDetail.routeId,
//                        dataBeanDetail.typeId
//                    )
                    EventBus.getDefault().post(EventToApproveDetail(dataBeanDetail.routeId, bean.companyId))
                }
            }

            101 -> {
                EventBus.getDefault().post(EventToApproveList(2 , bean.companyId))
            }

            200 -> {//考勤打卡
                MsgActionHolder().onAction200(bean.companyId, spaceTimeLong)
            }
            201 -> {//考勤规则
                if (dataBeanDetail != null) {
                    MsgActionHolder().onAction201(bean.companyId, dataBeanDetail.routeId)
                }
            }
            202 -> {//特殊工作日调整
                if (dataBeanDetail != null) {
                    MsgActionHolder().onAction202(bean.companyId, dataBeanDetail.routeId)
                }
            }
            500 -> {//有人提交了汇报，去详情
                Logger.i("---执行---汇报log---", "----点击去汇报详情----")
                if (dataBeanDetail != null) {
                    MsgActionHolder().onAction500(
                        bean.companyId,
                        dataBeanDetail.routeId,
                        dataBeanDetail.modelId,
                        dataBeanDetail.modelName,
                        dataBeanDetail.reportName
                    )
                }
            }
            501 -> {//有人提交了汇报，新建汇报
                Logger.i("---执行---汇报log---", "----点击去新建汇报----")
                if (dataBeanDetail != null) {
                    val creator = SearchMemberBean(
                        headimg = dataBeanDetail.avatar,
                        userId = dataBeanDetail.userId,
                        name = dataBeanDetail.userName,
                        select = true
                    )
                    MsgActionHolder().onAction501(
                        bean.companyId,
                        dataBeanDetail.routeId,
                        dataBeanDetail.modelName,
                        creator
                    )
                }
            }
            502 -> {//去汇报统计
                Logger.i(
                    "---执行---汇报log---", "----点击去汇报统计----" +
                            " ${GsonUtil.toJson(bean)}-->${GsonUtil.toJson(dataBeanDetail)}"
                )
                if (dataBeanDetail != null) {
                    MsgActionHolder().onAction502(
                        bean.companyId,
                        dataBeanDetail.routeId,
                        dataBeanDetail.modelName,
                        dataBeanDetail.period,
                        dataBeanDetail.startTime,
                        dataBeanDetail.endTime
                    )
                }
            }
            503 -> {//去汇报评论（暂时和详情一样，只是不需要reportName）
                Logger.i("---执行---汇报log---", "----点击去汇报评论--暂时和详情一样--")
                if (dataBeanDetail != null) {
                    MsgActionHolder().onAction503(
                        bean.companyId,
                        dataBeanDetail.routeId,
                        dataBeanDetail.modelId,
                        dataBeanDetail.modelName
                    )
                }
            }
            600 -> {//被授予权限-去看详情
                Logger.i("---执行---汇报log---", "----点击权限变更--暂时和详情一样--")
                if (dataBeanDetail != null) {
                    MsgActionHolder().onAction600(
                        dataBeanDetail.permissions,
                        managerName = bean.noticeTitle
                    )
                }
            }

            204 -> {
                // 公告详情 跳转
                val companyId = bean.companyId
                openNoticePad(mActivity , bean.routeId , companyId)
            }
        }
    }

    private fun openNoticePad(context: Context , noticeId : String , companyId : String){
        val pageInfo = PageInfoBean()
        pageInfo.title = context.resources.getString(com.joinutech.ddbeslibrary.R.string.notice_detail_title)
        pageInfo.noticeId = noticeId
        pageInfo.pageType = 1
        pageInfo.companyId = companyId

        if (pageInfo.noticeId.isNullOrBlank() || pageInfo.companyId.isNullOrBlank()) {
            toast(context , "暂时无法查看详情")
            return
        }

        ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
            .withString("pageInfo", GsonUtil.toJson(pageInfo))
            .withBoolean("isNoticeWeb", true)
            .navigation()
    }

    //点击审批详情
    private fun forthWatchClick(bean: NoticeMsgBean) {
        Logger.i("-------执行----", "---点击审批通知详情了-functionType--" + bean.functionType)
        var dataBeanDetail: DataBeanDetail? = null
        try {
            dataBeanDetail = GsonUtil.fromJson(bean.detail, DataBeanDetail::class.java)
        }catch (e: Exception){}

        if (bean.msgType == ConstantImMsgType.TicketMsgWithDraw) {//工单审批撤回
            ToastUtil.show(mActivity,"该工单已经撤回")
            return
        }

        when (bean.functionType) {
            100 -> {
                if (dataBeanDetail != null) {
                    EventBus.getDefault().post(EventToApproveDetail(dataBeanDetail.routeId, bean.companyId))

                }
            }//审批详情
            101 -> {
                Logger.i("-------执行----", "---点击审批通知详情了-bean.companyId--" + bean.companyId)
//                MsgActionHolder().onAction101(bean.companyId)
                EventBus.getDefault().post(EventToApproveList(2 , bean.companyId))
            }

            203 -> {
                // 跳转考勤-审批tab
                val companyId = bean.companyId
                val date = System.currentTimeMillis()

                ARouter.getInstance()
                    .build(attendanceHomeActivity)
                    .withString(ConsKeys.COMPANY_ID, companyId)
                    .withInt(ConsKeys.ATTEND_TAB_CHECK , 2)
                    .withLong(ConsKeys.KEY_INTENT_DATA, date)
                    .navigation()

            }

            //审批列表
            /*  3100 -> {
                  MsgActionHolder().onAction3100(bean.companyId)
              }//金蝶审批列表*/
            3100,
            3101 -> {
                if (!bean.ext.isNullOrBlank()) {
                    try {
                        val json = JSONObject(bean.ext)
                        val targetUrl = json.optString("kingdeeUrl")
                        if (!targetUrl.isNullOrBlank()) {
                            MsgActionHolder().onAction3101(bean.companyId, targetUrl)
                        }
                    } catch (e: Exception) {
                    }
                } else {
                    mActivity.toastShort("暂无详情")
                }
            }//金蝶审批详情，会通过路由的拦截器访问接口给地址拼接上金蝶的token
            1001->{
                if (!dataBeanDetail?.link.isNullOrBlank()) {
                    try {
                        val targetUrl = dataBeanDetail?.link
                        if (!targetUrl.isNullOrBlank()) {
                            MsgActionHolder().onAction1001(bean.companyId, targetUrl)
                        }
                    } catch (e: Exception) {
                    }
                } else {
                    mActivity.toastShort("暂无详情。")
                }
            }//金蝶审批详情2，不会通过路由的拦截器添加token，直接使用取出的原始地址；
            2100 -> {
                if (dataBeanDetail != null) {
//                    MsgActionHolder().onAction2100(bean.companyId, dataBeanDetail.routeId, dataBeanDetail.typeId)
                }
            }//访客待审批列表
            2101 -> {
                if (dataBeanDetail != null) {
                    val targetActivity = mActivity as TcpNoticeListActivity
                    targetActivity.getHasChangedMsgIdSet(onResult = { hasChangedMsgIdSet, life ->
                        MsgActionHolder().onAction2101(
                            mActivity,
                            bean = bean,
                            hasChangedMsgIdSet = hasChangedMsgIdSet,
                            life = life,
                            msgId = bean.msgId,
                            visitorId = dataBeanDetail.routeId
                        )
                    })

                }
            }

            //  【工单】跳转web页面
            1000 -> {
                val addressURL = dataBeanDetail?.link
                Logger.i("直接跳转","==link=${addressURL}")
                if (StringUtils.isNotBlankAndEmpty(addressURL)) {
                    MsgActionHolder().onAction1000(addressURL!!)
                }
            }//跳转到指定的web地址

            1100 -> {
                // TODO Flutter 专用 点击跳转 进入flutter 审批 ，
                //  进入flutter 审批 、 会议详情
                try {
                    val dataBeanDetail = GsonUtil.fromJson(bean.detail, FlutterPushData::class.java)
                    if (dataBeanDetail == null) return
                    val routeName = dataBeanDetail.routeName ?:""
                    val argument = dataBeanDetail.argument
                    EventBus.getDefault().post(EventApplyInfoData(routeName , argument))
                }catch (e: java.lang.Exception){
                    e.printStackTrace()
                }
            }

            1002 -> {
                // 金蝶
                if (dataBeanDetail == null) return
                val addressURL = dataBeanDetail?.link
                if (addressURL.isNullOrBlank()) return
                val paramType = dataBeanDetail.paramType ?:0
                if (paramType == 0){
                    ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                        .withString(
                            ConsKeys.PARAMS_URL, addressURL
                        ).withString("plusToken" ,"yes")
                        .navigation()
                }else{
                    ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                        .withString(ConsKeys.PARAMS_URL, addressURL
                        ).navigation()
                }
            }
        }
    }

    private val attendanceHomeActivity: String = "/work/AttendanceHomeActivity"


    private fun sixthWatchClick(bean: NoticeMsgBean) {
        Logger.i("-------执行----", "---点击--别人邀请我提交汇报--通知详情了-functionType--" + bean.functionType)
        val dataBeanDetail = GsonUtil.fromJson(bean.detail, DataBeanDetail::class.java)
        when (bean.functionType) {
            501 -> {//别人邀请我提交汇报，去新建
                Logger.i("---执行---汇报log---", "----点击去汇报新建----")
                if (dataBeanDetail != null) {
                    val creator = SearchMemberBean(
                        headimg = dataBeanDetail.avatar,
                        userId = dataBeanDetail.userId,
                        name = dataBeanDetail.userName,
                        select = true
                    )
                    MsgActionHolder().onAction501(
                        bean.companyId,
                        dataBeanDetail.routeId,
                        dataBeanDetail.modelName,
                        creator
                    )
                }
            }
        }
    }

    private fun checkMyCompany(
        bean: NoticeMsgBean,
        isWatchDetail: Boolean = false,
        result: () -> Unit
    ) {
        tcpNoticeListActivity.showLog("检查当前点击事件 ${GsonUtil.toJson(bean)}")
        fun checkWork() {
            if ((!bean.companyId.isNullOrBlank() && CompanyHolder.getTotalCompanyIdMap().keys.contains(
                    bean.companyId
                ))
            ) {
                result.invoke()
                refreshFlutterRedDot(bean.companyId)
            } else {
                if (isWatchDetail) {
                    tcpNoticeListActivity.toastShort("您已不是当前公司的员工，无法查看详情!")
                } else {
                    tcpNoticeListActivity.toastShort("当前用户不是此公司的员工或者公司解散了!")
                }
            }
        }

        when (bean.sessionType) {
            ConstantImMsgType.SystemMsgSessionTyp -> {
                tcpNoticeListActivity.showLog("当前消息为系统消息，直接放行---<<<")
                result.invoke()
            }
            ConstantImMsgType.WorkMsgSessionType,
            ConstantImMsgType.ApprovalMsgSessionType,
            ConstantImMsgType.KingdeeMsgSessionType,
            ConstantImMsgType.TicketMsgSessionType,
            ConstantImMsgType.TrainMsgSessionType,
            ConstantImMsgType.MatterMsgSessionType,
            ConstantImMsgType.TeamMsgSessionType -> {
                tcpNoticeListActivity.showLog("当前消息为工作、审批、协作消息，需要过滤判断--->>>")
                checkWork()
            }
        }

    }

    class MsgViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var firstItemLayout: LinearLayout = itemView.findViewById(R.id.item_notice_first_layout)
        var secondItemLayout: ConstraintLayout =
            itemView.findViewById(R.id.item_notice_second_layout)
        var thirdItemLayout: ConstraintLayout = itemView.findViewById(R.id.item_notice_third_layout)
        var forthItemLayout: ConstraintLayout = itemView.findViewById(R.id.item_notice_forth_layout)
        var fifthItemLayout: ConstraintLayout = itemView.findViewById(R.id.item_notice_fifth_layout)
        var sixthItemLayout: ConstraintLayout = itemView.findViewById(R.id.item_notice_sixth_layout)

        var firstItemTime: TextView = itemView.findViewById(R.id.first_item_time)
        var firstNoticeIcon: CircleImageView = itemView.findViewById(R.id.first_notice_icon)
        var firstUserIcon: CircleImageView = itemView.findViewById(R.id.first_user_icon)
        var firstNoticeTitle: TextView = itemView.findViewById(R.id.first_notice_title)
        var firstNoticeContent: TextView = itemView.findViewById(R.id.first_notice_content)
        var firstNoticeTime: TextView = itemView.findViewById(R.id.first_notice_time)
        var firstWatch: TextView = itemView.findViewById(R.id.first_watch)

        var secondItemTime: TextView = itemView.findViewById(R.id.second_item_time)
        var secondNoticeIcon: ImageView = itemView.findViewById(R.id.second_notice_icon)
        var secondNoticeName: TextView = itemView.findViewById(R.id.second_notice_name)
        var secondNoticeTitle: TextView = itemView.findViewById(R.id.second_notice_title)
        var secondImageLayout: FrameLayout = itemView.findViewById(R.id.second_image_layout)
        var secondImageBg: ImageView = itemView.findViewById(R.id.second_image_bg)
        var secondImageText: TextView = itemView.findViewById(R.id.second_image_text)

        var thirdItmeTime: TextView = itemView.findViewById(R.id.third_item_time)
        var thirdNoticeIcon: ImageView = itemView.findViewById(R.id.third_notice_icon)
        var thirdNoticeName: TextView = itemView.findViewById(R.id.third_notice_name)
        var thirdUserIcon: CircleImageView = itemView.findViewById(R.id.third_user_icon)
        var thirdNoticeTitle: TextView = itemView.findViewById(R.id.third_notice_title)
        var thirdNoticeContent1: TextView = itemView.findViewById(R.id.third_notice_content1)
        var thirdNoticeContent2: TextView = itemView.findViewById(R.id.third_notice_content2)
        var thirdNoticeContent3: TextView = itemView.findViewById(R.id.third_notice_content3)
        var thirdTaskLayout: LinearLayout = itemView.findViewById(R.id.third_task_layout)
        var thirdTaskIcon: RoundedImageView = itemView.findViewById(R.id.third_task_icon)
        var thirdTaskDesc: TextView = itemView.findViewById(R.id.third_task_desc)
        var thirdWatch: TextView = itemView.findViewById(R.id.third_watch)

        var forthItemTime: TextView = itemView.findViewById(R.id.forth_item_time)
        var forthNoticeIcon: CircleImageView = itemView.findViewById(R.id.forth_notice_icon)
        var forthNoticeName: TextView = itemView.findViewById(R.id.forth_notice_name)
        var forthNoticeWatch: TextView = itemView.findViewById(R.id.forth_notice_watch)
        var forthUserIcon: CircleImageView = itemView.findViewById(R.id.forth_user_icon)
        var forthNoticeTitle: TextView = itemView.findViewById(R.id.forth_notice_title)
        var forthNoticeContent1: TextView = itemView.findViewById(R.id.forth_notice_content1)
        var forthNoticeContent2: TextView = itemView.findViewById(R.id.forth_notice_content2)
        var forthNoticeContent3: TextView = itemView.findViewById(R.id.forth_notice_content3)
        var forthNoticeResultState: TextView = itemView.findViewById(R.id.forth_notice_result_state)
        var forthNoticeButton2: TextView = itemView.findViewById(R.id.forth_notice_two)  // 拒绝
        var forthNoticeButton1: TextView = itemView.findViewById(R.id.forth_notice_one)  // 同意

        // 审批的content 内容
        var approveContentRcy = itemView.findViewById<RecyclerView>(R.id.forth_approve_content_rcy)
        val approveContentAdapter = ApproveContentItemAdapter(approveContentRcy.context)

        val unKnowTypeLayout = itemView.findViewById<LinearLayout>(R.id.approve_unknonw_type_layout)

        init {
            approveContentRcy.layoutManager = LinearLayoutManager(approveContentRcy.context)
            approveContentRcy.adapter = approveContentAdapter
        }

        var fifthItemTime: TextView = itemView.findViewById(R.id.fifth_item_time)
        var fifthNoticeIcon: ImageView = itemView.findViewById(R.id.fifth_notice_icon)
        var fifthNoticeName: TextView = itemView.findViewById(R.id.fifth_notice_name)
        var fifthUserIcon: CircleImageView = itemView.findViewById(R.id.fifth_user_icon)
        var fifthNoticeTitle: TextView = itemView.findViewById(R.id.fifth_notice_title)
        var fifthNoticeContent1: TextView = itemView.findViewById(R.id.fifth_notice_content1)
        var fifthNoticeContent2: TextView = itemView.findViewById(R.id.fifth_notice_content2)
        var fifthNoticeContent3: TextView = itemView.findViewById(R.id.fifth_notice_content3)
        var fifthTaskLayout: LinearLayout = itemView.findViewById(R.id.fifth_task_layout)
        var fifthTaskIcon: RoundedImageView = itemView.findViewById(R.id.fifth_task_icon)
        var fifthTaskDesc: TextView = itemView.findViewById(R.id.fifth_task_desc)

        var sixthItmeTime: TextView = itemView.findViewById(R.id.sixth_item_time)
        var sixthNoticeIcon: ImageView = itemView.findViewById(R.id.sixth_notice_icon)
        var sixthNoticeName: TextView = itemView.findViewById(R.id.sixth_notice_name)
        var sixthUserIcon: CircleImageView = itemView.findViewById(R.id.sixth_user_icon)
        var sixthNoticeTitle: TextView = itemView.findViewById(R.id.sixth_notice_title)
        var sixthNoticeContent1: TextView = itemView.findViewById(R.id.sixth_notice_content1)
        var sixthNoticeContent2: TextView = itemView.findViewById(R.id.sixth_notice_content2)
        var sixthNoticeContent3: TextView = itemView.findViewById(R.id.sixth_notice_content3)
        var sixthWatch: TextView = itemView.findViewById(R.id.sixth_watch)

        var spaceHolder: View = itemView.findViewById(R.id.mspace_holder)

        fun showTargetLayout(tempType: Int) {
            when (tempType) {
                1 -> {
                    XUtil.showView(firstItemLayout)
                    XUtil.hideView(
                        secondItemLayout,
                        thirdItemLayout,
                        forthItemLayout,
                        fifthItemLayout,
                        sixthItemLayout,
                        unKnowTypeLayout
                    )
                }
                2 -> {
                    XUtil.showView(secondItemLayout)
                    XUtil.hideView(
                        firstItemLayout,
                        thirdItemLayout,
                        forthItemLayout,
                        fifthItemLayout,
                        sixthItemLayout,
                        unKnowTypeLayout
                    )
                }
                3 -> {
                    XUtil.showView(thirdItemLayout)
                    XUtil.hideView(
                        firstItemLayout,
                        secondItemLayout,
                        forthItemLayout,
                        fifthItemLayout,
                        sixthItemLayout,
                        unKnowTypeLayout
                    )
                }
                4 ,8 -> {
                    XUtil.showView(forthItemLayout)
                    XUtil.hideView(
                        firstItemLayout,
                        secondItemLayout,
                        thirdItemLayout,
                        fifthItemLayout,
                        sixthItemLayout,
                        unKnowTypeLayout
                    )
                }
                5 -> {
                    XUtil.showView(fifthItemLayout)
                    XUtil.hideView(
                        firstItemLayout,
                        secondItemLayout,
                        thirdItemLayout,
                        forthItemLayout,
                        sixthItemLayout,
                        unKnowTypeLayout
                    )
                }
                6 -> {
                    XUtil.showView(sixthItemLayout)
                    XUtil.hideView(
                        firstItemLayout,
                        secondItemLayout,
                        thirdItemLayout,
                        forthItemLayout,
                        fifthItemLayout,
                        unKnowTypeLayout
                    )
                }

                else -> {
                    XUtil.hideView(
                        firstItemLayout,
                        secondItemLayout,
                        thirdItemLayout,
                        forthItemLayout,
                        fifthItemLayout,
                        unKnowTypeLayout
                    )
                    XUtil.showView(unKnowTypeLayout)
                }
            }
        }

    }

    private fun showMsgSubheadContent(bean: ReportInviteMsgBean): String {
        val sb = java.lang.StringBuilder()
        when (bean.period) {
            0 -> {
//                "日"
                if (!bean.week.isNullOrEmpty()) {
                    sb.append("提交日期：")
                    var weekValue = "每天"
                    if (bean.week.size < 7) {
                        val sb1 = java.lang.StringBuilder()
                        bean.week.forEach {
                            sb1.append("${weekTimeNumValueToString(it)},")
                        }
                        if (sb1.isNotBlank() && sb1.endsWith(",")) {
                            weekValue = sb1.substring(0, sb1.lastIndex).toString()
                        }
                    }
                    sb.append(weekValue)
                    if (bean.holiday == 0) {
                        sb.append("（法定节假日不提交）\n")
                    } else sb.append("\n")
                }
                sb.append("提交开始时间：")
                if (bean.pre == 1) {
                    sb.append(
                        "${bean.start.toInt()}:00\n提交截止时间: " +
                                "次日${bean.end.toInt()}：00\n" +
                                "次日${bean.end.toInt()}：00后提交为迟交"
                    )

                } else {
                    sb.append(
                        "${bean.start.toInt()}:00\n提交截止时间: " +
                                "${bean.end.toInt()}：00\n" +
                                "${bean.end.toInt()}：00后提交为迟交"
                    )
                }
            }
            1 -> {
//                "周"
//                sb.append(weekTimeNumValueToString(bean.startDay))
//                if (bean.pre == 1) {
//                    sb.append("至次周")
//                } else {
//                    sb.append("至")
//                }
//                sb.append(weekTimeNumValueToString(bean.endDay))
                if (bean.holiday == 0) {
                    sb.append("提交规则：")
                    sb.append("（法定节假日不提交）\n")
                }
//                else sb.append("\n")
                sb.append("提交开始时间：")
                if (bean.pre == 1) {
                    sb.append(
                        weekTimeNumValueToString(bean.startDay) +
                                "${bean.start.toInt()}:00\n提交截止时间: " +
                                "次周${weekTimeNumValueToString(bean.endDay)}" +
                                "${bean.end.toInt()}：00\n" +
                                "次周${weekTimeNumValueToString(bean.endDay)}" +
                                "${bean.end.toInt()}：00后提交为迟交"
                    )

                } else {
                    sb.append(
                        weekTimeNumValueToString(bean.startDay) +
                                "${bean.start.toInt()}:00\n提交截止时间: " +
                                weekTimeNumValueToString(bean.endDay) +
                                "${bean.end.toInt()}：00\n" +
                                weekTimeNumValueToString(bean.endDay) +
                                "${bean.end.toInt()}：00后提交为迟交"
                    )
                }
            }
            2 -> {
//                "月"
//                sb.append("提交日期：")
//                sb.append("${bean.startDay.toInt()}号")
//                if (bean.pre == 1) {
//                    sb.append("至次月")
//                } else {
//                    sb.append("至")
//                }
//                sb.append("${bean.endDay.toInt()}号")
                if (bean.holiday == 0) {
                    sb.append("提交规则：")
                    sb.append("（法定节假日不提交）\n")
                }
//                else sb.append("\n")
                sb.append("提交开始时间：")
                if (bean.pre == 1) {
                    sb.append(
                        "${bean.startDay.toInt()}号" +
                                "${bean.start.toInt()}:00\n提交截止时间: " +
                                "次月${bean.endDay.toInt()}号" +
                                "${bean.end.toInt()}：00\n" +
                                "次月${bean.endDay.toInt()}" +
                                "${bean.end.toInt()}：00后提交为迟交"
                    )

                } else {
                    sb.append(
                        "${bean.startDay.toInt()}号" +
                                "${bean.start.toInt()}:00\n提交截止时间: " +
                                "${bean.endDay.toInt()}号" +
                                "${bean.end.toInt()}：00\n" +
                                "${bean.endDay.toInt()}" +
                                "${bean.end.toInt()}：00后提交为迟交"
                    )
                }
            }
        }
        return sb.toString()
    }

    private fun weekTimeNumValueToString(weekValue: String): String {
        return when (weekValue) {
            "01" -> {
                "周一"
            }
            "02" -> {
                "周二"
            }
            "03" -> {
                "周三"
            }
            "04" -> {
                "周四"
            }
            "05" -> {
                "周五"
            }
            "06" -> {
                "周六"
            }
            "07" -> {
                "周日"
            }
            else -> {
                "周一"
            }
        }
    }
}