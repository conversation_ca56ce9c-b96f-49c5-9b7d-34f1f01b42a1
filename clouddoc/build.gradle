apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply plugin: 'kotlin-kapt'

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    namespace = "com.joinutech.ddbes.clouddoc" // 使用模块的实际包名
    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode 1
        versionName "1.0"

//        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'

        kapt {
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }
    }

    repositories {
        flatDir {
            dirs '../ddbeslibrary/libs', "./libs"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    dexOptions {
        incremental true
        javaMaxHeapSize "4g"
    }

    viewBinding{
        enable true
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation supportLibs.stdlib_jdk7//代替
//    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"

    implementation project(':lib_im')
//    implementation project(':lib_picture')
    kapt supportLibs['arouter_compiler']
    api supportLibs['arouter']

    implementation supportLibs['appcompat']
//    implementation 'androidx.core:core-ktx:1.3.0'
//    testImplementation 'junit:junit:4.12'
//    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
//    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
}
