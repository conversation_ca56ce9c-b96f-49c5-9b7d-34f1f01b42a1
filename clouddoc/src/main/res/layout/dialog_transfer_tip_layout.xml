<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_35"
        android:layout_marginEnd="@dimen/dp_35"
        android:background="@drawable/shape_white_rounded_dialog"
        android:orientation="vertical"
        android:padding="@dimen/dp_20">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_180"
            android:src="@drawable/ic_no_wifi_tip" />

        <TextView
            android:id="@+id/tv_trans_continue"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_20"
            android:background="@drawable/bg_transfer_action_24"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="继续使用流量传输"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:id="@+id/tv_trans_pause"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_12"
            android:paddingStart="@dimen/dp_20"
            android:paddingTop="@dimen/dp_6"
            android:paddingEnd="@dimen/dp_20"
            android:paddingBottom="@dimen/dp_6"
            android:text="暂停传输"
            android:textColor="#386DFF"
            android:textSize="@dimen/sp_14" />

    </LinearLayout>
</LinearLayout>