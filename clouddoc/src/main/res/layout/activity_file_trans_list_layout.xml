<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_30"
        android:background="@color/white"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_download"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="下载记录"
            android:textColor="@color/select_tab_color"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_upload"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_upload"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dp_50"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="上传记录"
            android:textColor="@color/select_tab_color"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_download"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/indicator_download"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_1_5"
            android:background="@drawable/bg_indicator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/tv_download"
            app:layout_constraintStart_toStartOf="@id/tv_download" />

        <View
            android:id="@+id/indicator_upload"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_1_5"
            android:background="@drawable/bg_indicator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/tv_upload"
            app:layout_constraintStart_toStartOf="@id/tv_upload" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/toolbar_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_grey"
        android:visibility="visible" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_file_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        android:layout_weight="1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/white">

        <ImageView
            android:id="@+id/iv_empty_icon"
            android:layout_width="@dimen/dp_245"
            android:layout_height="@dimen/dp_180"
            android:src="@drawable/ic_download_empty"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_empty_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/dp_23"
            android:text="暂无下载记录"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_15"
            app:layout_constraintBottom_toBottomOf="@id/iv_empty_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <LinearLayout
        android:id="@+id/ll_bottom_manage"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_80"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_25"
        android:paddingEnd="@dimen/dp_25">

        <ImageView
            android:id="@+id/iv_select_all"
            android:layout_width="@dimen/dp_14"
            android:layout_height="@dimen/dp_14"
            android:src="@drawable/selector_circle_select_green" />

        <TextView
            android:id="@+id/tv_select_all"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:paddingStart="@dimen/dp_10"
            android:text="全选"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_15" />

        <LinearLayout
            android:id="@+id/ll_delete"
            android:layout_width="@dimen/dp_93"
            android:layout_height="@dimen/dp_35"
            android:layout_marginStart="@dimen/dp_20"
            android:background="@drawable/bg_transfer_delete"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:src="@drawable/ic_transfer_delete" />

            <TextView
                android:id="@+id/tv_delete_file"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_8"
                android:includeFontPadding="false"
                android:text="删除"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14" />
        </LinearLayout>

    </LinearLayout>
</LinearLayout>