<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_62"
    android:background="@color/white"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_10">

    <ImageView
        android:id="@+id/iv_select"
        android:layout_width="@dimen/dp_33"
        android:layout_height="@dimen/dp_33"
        android:padding="@dimen/dp_10"
        android:src="@drawable/selector_circle_select_green"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/iv_file_icon"
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_22"
        android:layout_marginStart="@dimen/dp_10"
        android:src="@drawable/default_heading"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_select"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginStart="@dimen/dp_10" />

    <TextView
        android:id="@+id/tv_file_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:paddingStart="@dimen/dp_10"
        android:paddingEnd="@dimen/dp_10"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toTopOf="@id/tv_file_progress_info"
        app:layout_constraintEnd_toStartOf="@id/right_menu"
        app:layout_constraintStart_toEndOf="@id/iv_file_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="测试文件123.png" />


    <TextView
        android:id="@+id/tv_file_progress_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:paddingStart="@dimen/dp_10"
        android:paddingEnd="@dimen/dp_10"
        android:textColor="@color/transfer_state_color"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/tv_file_name"
        app:layout_constraintStart_toStartOf="@id/tv_file_name"
        app:layout_constraintTop_toBottomOf="@id/tv_file_name"
        tools:text="2019-11-11 21:00，60kb" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/right_menu"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_10"
        android:paddingEnd="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_transfer"
            android:layout_width="@dimen/dp_15"
            android:layout_height="@dimen/dp_15"
            android:src="@drawable/ic_transfer_download_2"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.joinutech.common.widget.CircleProgressView
            android:id="@+id/cpv_transfer_progress"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:visibility="visible"
            app:cpv_bg_color="#D8D8D8"
            app:cpv_line_color="#386DFF"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/bottom_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:layout_marginStart="@dimen/dp_10"
        android:background="@color/line_grey"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>