<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_35"
        android:layout_marginEnd="@dimen/dp_35"
        android:background="@drawable/shape_white_rounded_dialog"
        android:paddingTop="@dimen/dp_30">

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="确定要删除正在上传的文件吗？" />

        <View
            android:id="@+id/line_h"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_2"
            android:layout_marginTop="@dimen/dp_30"
            android:background="@color/line_grey"
            app:layout_constraintTop_toBottomOf="@id/tv_content" />

        <View
            android:id="@+id/line_v"
            android:layout_width="@dimen/dp_0_2"
            android:layout_height="@dimen/dp_40"
            android:background="@color/line_grey"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line_h" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="@dimen/dp_10"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/line_v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line_h"
            tools:text="下次再说" />

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/dp_10"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/line_v"
            app:layout_constraintTop_toBottomOf="@id/line_h"
            tools:text="删除" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>