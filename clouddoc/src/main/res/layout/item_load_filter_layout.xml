<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_30"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tv_file_count"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:includeFontPadding="false"
        android:paddingStart="@dimen/dp_20"
        android:text="正在下载...(6)"
        android:textColor="@color/color666666"
        android:textSize="@dimen/sp_12" />

    <ImageView
        android:id="@+id/iv_state"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:src="@drawable/ic_transfer_pause"
        android:tint="#386DFF"
        android:tintMode="src_in"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_state"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_5"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingEnd="@dimen/dp_20"
        android:text="全部暂停"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        tools:visibility="visible" />

</LinearLayout>