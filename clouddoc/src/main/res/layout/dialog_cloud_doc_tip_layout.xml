<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/iv_cd_top"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_197"
            android:layout_marginStart="@dimen/dp_5"
            android:src="@drawable/ic_cd_tip_top"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/iv_cd_bottom"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_197"
            android:layout_marginStart="@dimen/dp_14"
            android:layout_marginBottom="@dimen/dp_5"
            android:src="@drawable/ic_cd_tip_bottom"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>