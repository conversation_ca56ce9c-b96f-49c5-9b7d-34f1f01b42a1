<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.joinutech.ddbeslibrary.widget.LollipopFixedWebView
        android:id="@+id/web_cloud_doc"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--默认空白页面-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/empty_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible"
        app:ev_content="你还没有加入工作团队"
        app:ev_desc="马上尝试创建或加入一个团队吧\n加入团队后，你即可开始使用考勤、公告等功能"
        app:ev_icon="@drawable/ic_empty_company"
        app:ev_negative="加入团队"
        app:ev_positive="创建团队"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</FrameLayout>