package com.joinutech.ddbes.clouddoc

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.graphics.BitmapFactory
import android.net.http.SslError
import android.os.Build
import android.os.Bundle
import android.view.View
import android.webkit.*
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import com.alibaba.android.arouter.launcher.ARouter
import com.google.gson.reflect.TypeToken
import com.joinutech.common.base.LinkBuilder
import com.joinutech.common.base.isWebDebug
import com.joinutech.common.helper.OnFragmentResumeListener
import com.joinutech.common.helper.WechatHelper
import com.joinutech.common.provider.FilePreviewProvider
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.common.storage.*
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.UserHolder
import com.joinutech.common.widget.OnEmptyClickListener
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.base.BaseFragment
import com.joinutech.ddbeslibrary.org.GlobalCompanyHolder
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.service.LoginService
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.widget.activity.TaskImagePreviewActivity
import com.luck.picture.lib.config.PictureMimeType
import com.marktoo.lib.cachedweb.CommonWebConfig
import com.marktoo.lib.cachedweb.WebListener
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import io.reactivex.FlowableEmitter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject

/**
 * @PackageName: com.joinutech.ddbes.clouddoc
 * @ClassName:
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/7/27 11:03
 * @Desc: 云文档首页；云文档分享文件后，预览详情；云文档协作文件查看详情
 */
class CloudDocFragment : BaseFragment(), OnDocInvokeListener, OnEmptyClickListener {

    override val layoutRes: Int = R.layout.fragment_cloud_doc_layout

    var listener: OnFragmentResumeListener? = null

    private var commWeb: CommonWebConfig? = null

    private lateinit var webDoc: WebView

    /**无参见团队时显示默认页面*/
    private lateinit var emptyPage: PageEmptyView

    /**作为fragment存在时标识*/
    private var subPage: Boolean = true
    private var targetUrl: String = ""

//    private lateinit var selector: PicSelector2

    /**当前目录id*/
    private var currentDir = ""

    override fun initView(rootView: View) {
        webDoc = rootView.findViewById(R.id.web_cloud_doc)
        emptyPage = rootView.findViewById(R.id.empty_page)
        emptyPage.clickListener = this

        initWebView(webDoc)
//        selector = PicSelector2(this, 9)

        GlobalCompanyHolder.companyUpdateResult.observe(this, Observer {
            showLog("云文档 -- 接收团队信息更新")
            when (it.type) {
                1 -> {
//                    showLog("公司信息更新后 传输 公司信息")
//                    webSaveCurrentCompany()
                }
                2 -> {

                }
                else -> {
                    emptyPage.visibility = View.VISIBLE
                    webDoc.visibility = View.GONE
                }
            }
        })

        GlobalCompanyHolder.currentCompany.observe(this, Observer {
            showLog("云文档 -- 当前团队更新")
            webSaveCurrentCompany()
        })
    }

    override fun onResume() {
        super.onResume()
        showLog("++++++ fragment document onResume()")
        if (!receiveState) {
            listener?.onResumed(3)
        }
//        updateStatusBar(true)
        showCloudDoc()
    }

    private fun showCloudDoc() {
        // 设置距离顶部高度，避免延伸到状态栏
        subPage = arguments?.getBoolean("subPage", true) ?: true
        targetUrl = arguments?.getString("targetUrl", "") ?: ""

        if (CompanyHolder.getCurrentOrg() != null) {
            if (webDoc.visibility != View.VISIBLE) {
                webDoc.visibility = View.VISIBLE
                emptyPage.visibility = View.GONE
                if (!pageInited) {
                    if (subPage) {
                        val lastUrl=LinkBuilder.generate(mActivity,
                            statusBarHeight = ConsValue.getStatusBarHeight(requireContext())
                        ).buildCloudDocUrl()
                        Loggerr.i("云盘地址", "===${lastUrl}===")
                        loadUrl(lastUrl)
                    } else {
                        if (targetUrl.isNotBlank()) {
                            // isTop web提供导航栏 platform 1 标识安卓 height 标识状态栏高度
//                            if (targetUrl.contains("https://mobile.ddbes.com")) {
//                                targetUrl = targetUrl.replace("https://mobile.ddbes.com", "http://192.168.0.62:8085")
//                            }
                            loadUrl(targetUrl.plus("&platform=1&height=${ConsValue.getStatusBarHeight(requireContext())}&isTop=1"))
                        } else {
                            mActivity.finish()
                        }
                    }
                }
            }
        } else {
            emptyPage.visibility = View.VISIBLE
            webDoc.visibility = View.GONE
        }
    }

    private var canGoBack = 0
    private var webInited = 0

    private fun initWebView(webView: WebView) {
        showLog("初始化线程 ：${Thread.currentThread().id}")
        commWeb = CommonWebConfig(mActivity, webView)
        commWeb?.let {
            it.debug = isWebDebug
            it.cacheable = false
            it.autoWide = true
            it.zoomable = true
            it.multiWindow = false
            it.defaultEncoding = "utf-8"
//            it.userAgent = "ddbes"
            it.jsBridge = true
            it.applyWebSettings()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                webView.settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            }
            it.addInterceptor()
            it.addDefaultClient()
            it.webListener = object : WebListener() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    showLog("load page finished,$url")
                    if (canGoBack == 0) {
                        canGoBack = 1
                    }
                }

                override fun onProgressChanged(view: WebView?, newProgress: Int) {
                    super.onProgressChanged(view, newProgress)
                }

                override fun onHttpError(view: WebView?,
                                         request: WebResourceRequest?,
                                         errorResponse: WebResourceResponse?) {
                    showLog("Http Error: ${errorResponse?.statusCode}")
                    canGoBack = -1
                    super.onHttpError(view, request, errorResponse)
                }

                override fun onReceivedError(view: WebView?, errorCode: Int?,desc:String?, url: String?) {
                    showLog("receive Http Error: $errorCode : $url")
                    canGoBack = -1
                }

                override fun onReceivedTitle(view: WebView?, title: String?, hasError: Boolean) {
                    showLog("onReceivedTitle: $title")
                    title?.let {
                        if (it.contains("404") || it.contains("500")
                                || it.contains("error", true)
                                || it.contains("网页无法打开")
                                || it.contains("failed", true)) {
                            canGoBack = -1
                        }
                    }
                }

                override fun onSslError(view: WebView?, handler: SslErrorHandler?, error: SslError?) {
                    showLog("onSslError: ${error.toString()}")
//                super.onSslError(view, handler, error)
                    handler?.proceed()
                }
            }
            it.useCached(false)
            webView.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
            it.addMutualInterface(DocMutualInterface(mActivity, this), ConsKeys.JS_TRANS_PORT)
        }
    }

    private fun loadUrl(realUrl: String) {
        Loggerr.i("云盘检测", "===云盘地址=${realUrl}===")
//        val ceshiUrl="http://************:8086/pan/index.html#/?platform=1&version=3.3.1&appVersion=30301&height=37"
        val ceshiUrl="http://************:8086/pan/"
        commWeb?.loadUrl(realUrl, ConsValue.getWebHeaders())
    }

    private var receiveState = false

    private fun invokeJs(method: String, data: String? = null) {
        showLog("调用js：[$method]::[$data]")
        if (data.isNullOrBlank()) {
            commWeb?.invokeJS("javascript:window.ddbes_pan.$method()")
        } else {
            commWeb?.invokeJS("javascript:window.ddbes_pan.$method(${data})")
        }
    }

    fun invokeJsGetData(method: String, params: Any?, result: (String) -> Unit) {
        val code = if (params != null) {
            "$method($params)"
        } else {
            "$method()"
        }
        commWeb?.invokeJS(code, resultCallback = ValueCallback { value ->
            result.invoke(value)
        }, inJavaBridge = true)
    }

    class DocMutualInterface(private val activity: AppCompatActivity,
                             private val listener: OnDocInvokeListener?) {

        @JavascriptInterface
        fun client_goBack(data: String) {
            FileStorage.showLog("云文档页面---  web点击返回键")
            activity.runOnUiThread {
                listener?.onWebGoBack()
            }
        }

        @JavascriptInterface
        fun pan_isAlready() {
            FileStorage.showLog("云文档页面---  加载完毕回调")
            activity.runOnUiThread {
                listener?.onWebInitFinish()
            }
        }

        @JavascriptInterface
        fun pan_changeCompany() {
            FileStorage.showLog("云文档页面---  接收公司切换后信息")
            activity.runOnUiThread {
                listener?.onChangeCompany()
            }
        }

        @JavascriptInterface
        fun pan_getToken() {
            FileStorage.showLog("云文档页面---  token过期后主动请求token")
            activity.runOnUiThread {
                listener?.webGetToken()
            }
        }

        @JavascriptInterface
        fun pan_CopyLink(data: String) {
            FileStorage.showLog("云文档页面---  复制内容到剪贴板 $data")
            activity.runOnUiThread {
                listener?.onCopy(data)
            }
        }

        @JavascriptInterface
        fun pan_SetStorage(data: String) {
            FileStorage.showLog("云文档页面---  存储内容到移动端 $data")
            activity.runOnUiThread {
                listener?.onSaveData(data)
            }
        }

        @JavascriptInterface
        fun pan_GetStorage() {
            FileStorage.showLog("云文档页面---  获取存储到移动端的内容")
            activity.runOnUiThread {
                listener?.onGetData()
            }
        }

        @JavascriptInterface
        fun pan_goTransferRecordVC() {
            FileStorage.showLog("云文档页面---  跳转文件传输页面")
            activity.runOnUiThread {
                listener?.showTransList()
            }
        }

        @JavascriptInterface
        fun client_take_photo(parentId: String) {
            FileStorage.showLog("云文档页面---  拍照 $parentId")
            activity.runOnUiThread {
                listener?.takePhoto(parentId)
            }
        }

        @JavascriptInterface
        fun client_take_video(parentId: String) {
            FileStorage.showLog("云文档页面---  视频选择 $parentId")
            activity.runOnUiThread {
                listener?.selectPic(parentId, PictureMimeType.ofVideo())
            }
        }

        @JavascriptInterface
        fun client_take_picture(parentId: String) {
            FileStorage.showLog("云文档页面---  选择图片 $parentId")
            activity.runOnUiThread {
                listener?.selectPic(parentId, PictureMimeType.ofImage())
            }
        }

        /**
         * 分享文件给好友
         * fileType:0带后缀1文件夹2多图
         *
         * {
         * "fileName":"PictureSelector_20200804_182917.JPEG.JPEG",
         * "fileType":"0",
         * "textarea":"Julia云资产的赵阳阳，分享给你一个文件【PictureSelector_20200804_182917.JPEG.JPEG】",
         * "link":"https://pan.ddbes.com/pan.html#/share/2Ylt0JZi33n",
         * "password":"6511",
         * "ids":["2488484430818051069"]
         * }
         * */
        @JavascriptInterface
        fun pan_shareFileToFriend(json: String) {
            FileStorage.showLog("云文档页面---  分享文件给好友--$json")
            activity.runOnUiThread {
                listener?.shareToFriend(json)
            }
        }

        /**
         * 分享文件到微信
         * {
         * "fileName":"1596000353179113.jpg",
         * "fileType":0,
         * "textarea":"云服务团队的金刚，分享给你一个文件【1596000353179113.jpg】",
         * "link":"https://pan.ddbes.com/pan.html#/share/2YlVwoQG8Xz",
         * "password":"8878",
         * "ids":[]
         * }*/
        @JavascriptInterface
        fun pan_shareFileToWX(json: String) {
            FileStorage.showLog("云文档页面---  分享文件到微信--$json")
            activity.runOnUiThread {
                listener?.shareToWx(json)
            }
        }

        /**分享文件给同事*/
        @JavascriptInterface
        fun pan_shareFileToOrgMember(json: String) {
            FileStorage.showLog("云文档页面---  分享文件给同事--$json")
            activity.runOnUiThread {
                listener?.shareToOrgMember(json)
            }
        }

        /**保存图片到相册
         * url 下载地址
         * name 文件名
         * {"url":"https://ddbes-pan-test-1257239906.cos.ap-beijing.myqcloud.com/2495532203715331069?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLRJwTlAa8QUIqCi8j4C4g5HDuLWVnjy1%26q-sign-time%3D1596771180%3B1596772080%26q-key-time%3D1596771180%3B1596772080%26q-header-list%3D%26q-url-param-list%3Dresponse-cache-control%3Bresponse-content-disposition%3Bresponse-content-language%3Bresponse-expires%26q-signature%3Da12a6eddecc7841fc2d8c2a31c1c674920391242&response-cache-control=no-cache&response-content-disposition=attachment%3B%20filename%3D%221596000352931419.jpg%22&response-content-language=zh-CN&response-expires=Sat%2C%2008%20Aug%202020%2003%3A33%3A00%20GMT",
         * "name":"1596000352931419.jpg","
         * id":"2495532204789072893"
         * }
         * {"url":"https://ddbes-pan-test-1257239906.cos.ap-beijing.myqcloud.com/2495502770304451581?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLRJwTlAa8QUIqCi8j4C4g5HDuLWVnjy1%26q-sign-time%3D1597221422%3B1597222322%26q-key-time%3D1597221422%3B1597222322%26q-header-list%3D%26q-url-param-list%3Dresponse-cache-control%3Bresponse-content-disposition%3Bresponse-content-language%3Bresponse-expires%26q-signature%3D6600d3c9c0a0f9bbf56b73660f04581aaa526f7c&response-cache-control=no-cache&response-content-disposition=attachment%3B%20filename%3D%22EVA%25E5%25A4%25A7%25E5%25B1%2595%25E4%25B8%25AD%25E5%259B%25BD%25E9%25A3%258E.jpg%22&response-content-language=zh-CN&response-expires=Thu%2C%2013%20Aug%202020%2008%3A37%3A02%20GMT","name":"EVA大展中国风.jpg","id":"2495503539103597565"}
         * */
        @JavascriptInterface
        fun client_saveToGallery(json: String) {
            FileStorage.showLog("云文档页面---  保存图片到相册 $json")
            activity.runOnUiThread {
                listener?.saveToGallery(json)
            }
        }

        /**
         * 预览文件 type 1 图片 2 文件
         *          data url
         *          name 文件名
         */
        @JavascriptInterface
        fun client_previewFile(json: String) {
            FileStorage.showLog("云文档页面---  预览文件 $json")
            activity.runOnUiThread {
                listener?.previewFile(json)
            }
        }

        @JavascriptInterface
        fun client_previewFileOnLine(json: String) {
            FileStorage.showLog("云文档分享文件 页面---  预览文件 $json")
            activity.runOnUiThread {
                listener?.previewFileOnline(json)
            }
        }

        /**
         */
        @JavascriptInterface
        fun client_showBottomBar(show: Boolean) {
            FileStorage.showLog("云文档页面---  进入二级页面判断 $show")
            activity.runOnUiThread {
                listener?.showBottomBar(show)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: EventBusEvent<Any>) {
//        showLog("CloudDoc onEvent() ${event.code}")
        receiveState = true
        when (event.code) {
            "on_drawer_close" -> {
//                updateStatusBar(true)
            }
            IMEvent.CONNECT_ERROR -> {
                showLog("网络断开，暂停所有文件传输")
                FileUpTransferManager.onPauseAll()
                FileDownTransferManager.onPauseAll()
            }
            IMEvent.CONNECT_SUCCESS -> {
                showLog("网络连通，检查文件传输")
                if (canGoBack == -1) {
                    refreshPage()
                }
            }
//            EventBusAction.CURRENT_COMPANY_UPDATE -> {
//                if (event.data != null && event.data is Int) {
//                    when (event.data as Int) {
//                        1 -> {
//                            showLog("公司信息更新或切换后 传输 公司信息")
//                            webSaveCurrentCompany()
//                        }
//                        2 -> {
//
//                        }
//                        else -> {
////                        showEmptyView()
//                        }
//                    }
//                }
//            }
            "file_transfer_success" -> {
                showLog("有文件上传完成，触发刷新web页面")
                onFileUploaded()
            }
            "cloud_doc_back_press" -> {
                onBackPress()
            }
        }
    }

    private fun refreshPage() {
        showCloudDoc()
    }

    private var pageInited = false

    override fun onWebGoBack() {
        if (!subPage) {
            mActivity.finish()
        } else {
            mActivity.onBackPressed()
        }
    }

    override fun onWebInitFinish() {
        showLog("web初始化完成，需要返回数据给web，用户数据和当前公司数据")
        showLog("web初始化完成后 传输 用户信息")
        webSaveUserInfo()
        showLog("web初始化完成后 传输 公司信息")
        webSaveCurrentCompany()
        webInited = 1
    }

    override fun webGetToken() {
        // 刷新token后返回token给web
        Loggerr.i("云盘检测", "===获取新的token给web===")
        webSaveToken()
    }

    override fun webSaveToken() {
        // 保存token到h5
        if (UserHolder.isLogin()) {
            Flowable.create({ emitter: FlowableEmitter<String> ->
                val call = LoginService.refreshToken(UserHolder.getRefreshToken())
                val tokenBean = call.execute().body()
                if (tokenBean != null) {
                    UserHolder.saveToken(tokenBean)
                    Loggerr.i("云盘检测", "===获取新的token给web=成功==")
                    emitter.onNext(UserHolder.getAccessToken())
                } else {
                    Loggerr.i("云盘检测", "===获取新的token给web==失败=")
                    emitter.onNext("-2")
                }
            }, BackpressureStrategy.BUFFER)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(object : BaseSubscriber<String>() {
                        override fun onError(ex: ApiException) {
                            EventBusUtils.sendEvent(EventBusEvent(IMEvent.MULTIUSER_LOGIN, "请重新登录"))
                        }

                        override fun onComplete() {
                        }

                        override fun onNext(token: String?) {
                            if (!token.isNullOrBlank() && token != "-2") {
                                showLog("回调给web用户token $token")
                                val json = JSONObject()
                                json.put("token", token)
                                showLog("回调给web用户token $json")
                                invokeJs("pan_h5SaveToken", json.toString())
                            } else {
                                EventBusUtils.sendEvent(EventBusEvent(IMEvent.MULTIUSER_LOGIN, "请重新登录"))
                            }
                        }
                    })
        }
    }

    override fun webSaveUserInfo() {
        if (UserHolder.isLogin()) {
            Loggerr.i("云盘检测", "===web加载完毕后回调用户信息给web=${GsonUtil.toJson(UserHolder.getCurrentUser())}===")
            invokeJs("pan_getUserBaseInfo", GsonUtil.toJson(UserHolder.getCurrentUser()))
        } else {
            invokeJs("pan_getUserBaseInfo", JSONObject().toString())
        }
    }

    override fun webSaveCurrentCompany() {
        if (UserHolder.isLogin()) {
            val org = CompanyHolder.getCurrentOrg()
            if (org != null) {
                Loggerr.i("云盘检测", "===web加载完毕反馈公司信息=${GsonUtil.toJson(org)}===")
                invokeJs("pan_getCurrentCompanyInfo", GsonUtil.toJson(org))
            } else {
                invokeJs("pan_getCurrentCompanyInfo", JSONObject().toString())
            }
        }
    }

    override fun onChangeCompany() {
        showLog("展示侧滑菜单，显示公司选项")
        EventBusUtils.sendEvent(EventBusEvent(EventBusAction.Event_CLICK_HOME_WORK_LEFT, 4))
    }

    override fun onCopy(data: String) {
        XUtil.toClipboard(mActivity, data) {
            showToast("复制成功")
        }
    }

    override fun onSaveData(data: String) {
        if (UserHolder.isLogin()) {
            val oldValue = MMKVUtil.getString("${UserHolder.getUserId()}_doc_cache")
            val map = if (oldValue.isNotBlank()) {
                GsonUtil.fromJson2(oldValue, object : TypeToken<HashMap<String, Int>>() {}.type)
                        ?: hashMapOf()
            } else {
                hashMapOf<String, Int>()
            }
            val subMap: HashMap<String, Int>? = GsonUtil.fromJson2(data, object : TypeToken<HashMap<String, Int>>() {}.type)
            if (!subMap.isNullOrEmpty()) {
                map.putAll(subMap)
                MMKVUtil.saveString("${UserHolder.getUserId()}_doc_cache", GsonUtil.toJson(map))
            }
        }
    }

    override fun onGetData() {
        if (UserHolder.isLogin()) {
            val data = MMKVUtil.getString("${UserHolder.getUserId()}_doc_cache")
            if (data.isNotBlank()) {
                webGetData(data)
            } else {
                webGetData(JSONObject().toString())
            }
        }
    }

    override fun webGetData(data: String) {
        invokeJs("pan_getStorage", data)
    }

    override fun showTransList() {
        startActivity(Intent(mActivity, FileTransListActivity::class.java))
    }

    override fun showBottomBar(show: Boolean) {
        if (subPage) {
            EventBusUtils.sendEvent(EventBusEvent("home_show_bottom_bar", if (show) 0 else 1))
        }
    }

    override fun selectPic(parentId: String, type: Int) {
        currentDir = parentId
//        if (this::selector.isInitialized) {
//            selector.onSelect(state = "", selectedCount = 0, compress = false, type = type)
//        }
        // 选择图片和视频
        PictureNewHelper.beforeSelectPhoto(this, maxSelectNum = 9, selectType = type)
    }

    override fun takePhoto(parentId: String) {
        currentDir = parentId
        // 拍照
        PictureNewHelper.beforeSelectPhoto(this, ConsKeys.TAKE_PHOTO)
//        if (this::selector.isInitialized) {
//            selector.onSelect(ConsKeys.TAKE_PHOTO, compress = false, type = 0)
//        }
    }

    override fun onFileUploaded() {
        if (UserHolder.isLogin()) {
            invokeJs("pan_clientUploadSuccess", "")
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == IMAGE_CUT_CODE) {
                if (data != null) {
                    val selectList = PictureNewHelper.afterSelectPhotoInfo(data)
//                    val selectList = PictureSelector.obtainMultipleResult(data)
                    if (selectList.isNotEmpty()) {
                        // TODO: 2020/7/31 15:15 检查存储空间，空间满足，获取文件存储id相关信息，之后执行文件上传 ，上传结束后回调给web
                        val list = selectList.map { media ->
//                            {"fileId":"2494972231247987709","fileName":"Screenshot_20191107_182116_com.vmall.client.jpg","fileType":0,"hash":"3743259caf87d3f793f5ec3c720eb575"}
                            AddFileBean(fileName = media["fileName"] as String,
                                    fileUrl = media["compressPath"] as String,
                                    createTime = System.currentTimeMillis(),
                                    mimeType = media["mimeType"] as String)
                        }.toList()
                        onUpload(list)
                    }
                }
            }
        } else {
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

    /**
     * 当前页面检查存储空间
     * 获取文件id等信息
     * 之后通过文件传输上传文件，
     * 通过文件传输，可以获取传输状态和进度
     * todo opt 不上传时直接添加到队列，标记为不上传
     * todo opt 添加文件时，如果本地缓存队列中存在要上传文件处理
     * */
    private fun onUpload(selectList: List<AddFileBean>) {
        checkDialog(onConfirm = {
            val companyId = CompanyHolder.getCurrentOrg()?.companyId
            if (!companyId.isNullOrBlank()) {
                FileStorage().checkCapacity(
                        activity = mActivity,
                        companyId = companyId,
                        uploadFiles = selectList,
                        onResult = { data, msg ->
                            if (data.isNullOrEmpty()) {
                                if (!msg.isNullOrBlank()) {
                                    showToast(msg)
                                }
                            } else {
                                val temp = arrayListOf<AddFileBean>()
                                // 所有文件信息，包含云空间已存在的文件，所有文件添加到传输工具类中，用以记录不同组织上传文件信息
                                temp.addAll(data)
                                FileUpTransferManager.addUploadFiles(
                                        companyId = companyId, parentId = currentDir,
                                        files = temp,
                                        state = msg)
                            }
                        }
                )
            }
        }, onCancel = {
            // 不上传时，默认添加到任务集合
            // 不上传，也要判断云空间是否可用这些信息
            val companyId = CompanyHolder.getCurrentOrg()?.companyId
            if (!companyId.isNullOrBlank()) {
                FileStorage().checkCapacity(
                        activity = mActivity,
                        companyId = companyId,
                        uploadFiles = selectList,
                        onResult = { data, msg ->
                            if (data.isNullOrEmpty()) {
                                if (!msg.isNullOrBlank()) {
                                    showToast(msg)
                                }
                            } else {
                                val temp = arrayListOf<AddFileBean>()
                                // 所有文件信息，包含云空间已存在的文件，所有文件添加到传输工具类中，用以记录不同组织上传文件信息
                                temp.addAll(data)
                                FileUpTransferManager.addUploadFiles(
                                        companyId = companyId, parentId = currentDir,
                                        files = temp,
                                        state = msg)
                            }
                        }
                )
            }
        })

    }

    /**@param data 图片 url and name
     * {"url":"https://ddbes-pan-test-1257239906.cos.ap-beijing.myqcloud.com/2495439385177102333?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLRJwTlAa8QUIqCi8j4C4g5HDuLWVnjy1%26q-sign-time%3D1596683798%3B1596684698%26q-key-time%3D1596683798%3B1596684698%26q-header-list%3D%26q-url-param-list%3Dresponse-cache-control%3Bresponse-content-disposition%3Bresponse-content-language%3Bresponse-expires%26q-signature%3D2eed28df395779b34c9390f0a6b4369b37d593e1&response-cache-control=no-cache&response-content-disposition=attachment%3B%20filename%3D%22IMG_20200805_110037.jpg.jpg%22&response-content-language=zh-CN&response-expires=Fri%2C%2007%20Aug%202020%2003%3A16%3A38%20GMT",
     * "name":"IMG_20200805_110037.jpg.jpg"
     * "id":"2495532204789072893"// fileId
     * }
     * */
    override fun saveToGallery(json: String) {
        val data = JSONObject(json)
        val name = data.optString("name")
        val url = data.optString("url")
        val fileId = data.optString("id")
        val companyId = CompanyHolder.getCurrentOrg()?.companyId ?: ""
        if (!companyId.isNullOrBlank()) {
            checkDialog({
                FileDownTransferManager.addDownLoadFile(companyId, AddFileBean(fileUrl = url, fileName = name, fileId = fileId, pauseAdd = false))
            }, {
                FileDownTransferManager.addDownLoadFile(companyId, AddFileBean(fileUrl = url, fileName = name, fileId = fileId, pauseAdd = true))
            })
        }
    }

    /**
     * 保存图片到相册
     * 上传文件
     * */
    @SuppressLint("CheckResult")
    fun checkDialog(onConfirm: () -> Unit, onCancel: () -> Unit) {
        fun showDialog() {
            val helper = object : CenterDialogHelper(
                    mActivity, layoutId = R.layout.dialog_transfer_tip_layout,
                    onConfirm = onConfirm,
                    onCancel = onCancel) {
                override fun bindView(dialogView: View) {
                    dialogView.findViewById<TextView>(R.id.tv_trans_continue).setOnClickListener {
                        dialog?.dismiss()
                        onConfirm.invoke()
                    }
                    dialogView.findViewById<View>(R.id.tv_trans_pause).setOnClickListener {
                        dialog?.dismiss()
                        onCancel.invoke()
                    }
                }
            }
            helper.show()
        }

        val tips = "需要你同意使用存储权限，才能使用云文档"
        PermissionUtils.requestPermissionFragment(this,
                arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
                "请允许云文档需要访问存储空间",
                onSuccess = {
                    if (DeviceUtil.getNetType(mActivity) != "WIFI") {
                        showDialog()
                    } else {
                        onConfirm.invoke()
                    }
                },
                onError = {
                    ToastUtil.show(mActivity, "请允许云文档需要访问存储空间")
                }, preTips = tips)
//        RxPermissions(mActivity).request(Manifest.permission.WRITE_EXTERNAL_STORAGE).subscribe {
//            if (it) {
//                if (DeviceUtil.getNetType(mActivity) != "WIFI") {
//                    showDialog()
//                } else {
//                    onConfirm.invoke()
//                }
//            } else {
//                ToastUtil.show(mActivity, "请允许云文档需要访问存储空间")
//            }
//        }
    }

    /**
     * {"type":2,
     * "data":"https://api.ddbes.com/preview/onlinePreview?url=https://ddbes-pan-1257239906.cos.ap-beijing.myqcloud.com/4300761418302466.pdf?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLRJwTlAa8QUIqCi8j4C4g5HDuLWVnjy1%26q-sign-time%3D1597654734%3B1597658334%26q-key-time%3D1597654734%3B1597658334%26q-header-list%3D%26q-url-param-list%3Dresponse-cache-control%3Bresponse-content-disposition%3Bresponse-content-language%3Bresponse-expires%26q-signature%3Df014bcee2705da67ba1d98eb9c35294d5600c11c&response-cache-control=no-cache&response-content-disposition=attachment%3B%20filename%3D%22%25E9%2592%2589%25E9%2592%2589%25E4%25BA%25A7%25E5%2593%2581%25E4%25BB%258B%25E7%25BB%258D%25E6%2589%258B%25E5%2586%258C.pdf%22&response-content-language=zh-CN&response-expires=Tue%2C%2018%20Aug%202020%2008%3A58%3A54%20GMT&ext=pdf",
     * "name":"钉钉产品介绍手册.pdf"}
     * */
    override fun previewFile(json: String) {
        // 预览文件{type，data}
//        {"type":1,
//        "data":"https://ddbes-pan-test-1257239906.cos.ap-beijing.myqcloud.com/2495502770304451581?sign=
//        q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLRJwTlAa8QUIqCi8j4C4g5HDuLWVnjy1%26q-sign-time%3D1596676805%3B1596677705%26q-key-time%3D1596676805%3B1596677705%26q-header-list%3D%26q-url-param-list%3Dresponse-cache-control%3Bresponse-content-disposition%3Bresponse-content-language%3Bresponse-expires%26q-signature%3D861f622893c68c1da9267d86c4325b9ce82ae2c2
//        &response-cache-control=no-cache
//        &response-content-disposition=attachment%3B%20filename%3D%22EVA%25E5%25A4%25A7%25E5%25B1%2595%25E4%25B8%25AD%25E5%259B%25BD%25E9%25A3%258E.jpg%22
//        &response-content-language=zh-CN&response-expires=Fri%2C%2007%20Aug%202020%2001%3A20%3A05%20GMT",
//        "name":"EVA大展中国风.jpg"}

        val data = JSONObject(json)
        val fileType = data.optInt("type")
        if (fileType == 1) {
            val fileUrl = data.optString("data")
            if (StringUtils.isNotBlankAndEmpty(fileUrl)) {
                val intent = Intent(mActivity, TaskImagePreviewActivity::class.java)
                val previewDataBean = TaskImagePreviewActivity.PreviewDataBean(
                        0, arrayListOf(fileUrl), isManager = true, isPanFile = true)
                val bundle = Bundle()
                bundle.putSerializable("previewData", previewDataBean)
                intent.putExtras(bundle)
                mActivity.startActivity(intent)
            }
        } else if (fileType == 2) {
            val fileUrl = data.optString("link")
            val name = data.optString("name")
//                FilePreviewProvider.getPreviewPost(name, 0L, fileUrl).navigation(mActivity)
            FilePreviewProvider.getPreviewPost(
                    name, 0L, fileUrl, data.optString("ext")
            ).withBoolean("onlyShow", true).navigation(mActivity)
        }
    }

    override fun previewFileOnline(json: String) {
        if (json.isNotBlank() && json.startsWith("{") && json.endsWith("}")) {
            val data = JSONObject(json)
            val name = data.optString("name")
            FilePreviewProvider.getPreviewPost(
                    name, 0L, data.optString("link"), data.optString("ext")
            ).withBoolean("onlyShow", true).navigation(mActivity)
        }
    }

    /**
     * {
     * "fileName":"PictureSelector_20200804_182917.JPEG.JPEG",
     * "fileType":"0", // 0 带后缀 1文件夹 2 多图
     * "textarea":"Julia云资产的赵阳阳，分享给你一个文件【PictureSelector_20200804_182917.JPEG.JPEG】",
     * "link":"https://pan.ddbes.com/pan.html#/share/2Ylt0JZi33n",
     * "password":"6511",
     * "ids":["2488484430818051069"]
     * }
     *
     * {
     * "fileName":"PictureSelector_20200804_182924.JPEG.JPEG等",
     * "fileType":"2",
     * "textarea":"Julia云资产的赵阳阳，分享给你一个文件【PictureSelector_20200804_182924.JPEG.JPEG等】",
     * "link":"https://pan.ddbes.com/pan.html#/share/2Ylt6PnQhdz",
     * "password":"2906",
     * "ids":["2488484430818051069"]
     * }
     *
     * {
     * "fileName":"我测试",
     * "fileType":"1",
     * "link":"https://pan.ddbes.com/pan.html#/share/2Ylt8jYtROl",
     * "password":"9582",
     * "textarea":"Julia云资产的赵阳阳，分享给你一个文件【我测试】",
     * "ids":["2468510353621921789","2488484430818051069"]
     * }
     * */
    //tcp发送云文档分享
    override fun shareToFriend(json: String) {
        // 分享文件给好友
        val data = JSONObject(json)
        if (data.has("ids")) {
            val userIdArray = data.getJSONArray("ids")
            data.remove("ids")
            val userIdList = arrayListOf<String>()
            for (i in 0 until userIdArray.length()) {
                userIdList.add(userIdArray.getString(i))
            }
            if (userIdList.isEmpty()) return

            val msgContent = data.optString("textarea")
            if (UserHolder.isLogin() && !msgContent.isNullOrBlank()) {
                val bundle = Bundle()
                bundle.putSerializable("userIdList", userIdList)
                bundle.putString("userId", userId!!)
                bundle.putString("data", json)
//                (mActivity.application as BaseApplication).imService?
                (ARouter.getInstance().build("/lib_im/service/im").navigation() as RouteServiceProvider)
                        .openPageWithResult(mActivity, "cloudDocShareToFriend", bundle) {
                            if (it.isNullOrBlank()) {
                                showToast("分享失败")
                            } else {
                                showToast(it)
                            }
                        }
            }

        }

    }

    /**
     * {
     * "fileName":"1596000353179113.jpg",
     * "fileType":0,
     * "textarea":"云服务团队的金刚，分享给你一个文件【1596000353179113.jpg】",
     * "link":"https://pan.ddbes.com/pan.html#/share/2YlVwoQG8Xz",
     * "password":"8878",
     * "ids":[]
     * }*/
    override fun shareToWx(json: String) {
        val data = JSONObject(json)
        val fileName = data.optString("fileName")
        val desc = data.optString("textarea")
        val link = data.optString("link")
        val fileType = data.optString("fileType")
        val fileIcon = FileUtil.getFileTypeIcon(fileName, fileType)
        val result = if (fileIcon > 0) {
            val thumbBmp = BitmapFactory.decodeResource(mActivity.resources, fileIcon)
            JavaUtils.bmpToByteArray(thumbBmp, true)
        } else {
            null
        }
        if (result != null && result.isNotEmpty()) {
            val wechat = WechatHelper()
            wechat.initApi(mActivity) {
                wechat.sendLinkToWx(result, fileName, desc, link)
            }
        }
    }

    override fun shareToOrgMember(msg: String) {
        // 分享文件给同事
        showLog("分享文件给同事")
    }

    override fun onBackPress() {
        showLog("云文档收到 点击系统返回键 事件 webInited = ${webInited}, canGoBack = $canGoBack")
        if (webInited == 1 || canGoBack == 1) {
            showLog("调用返回键线程 ：${Thread.currentThread().id}")
            invokeJs("onBackPress", "")
        } else {
            showLog("页面加载失败，如果页面为activity页面，则执行页面关闭操作")
            onWebGoBack()
        }
    }

    override fun onDestroyView() {
        FileUpTransferManager.onTransListener = null
        super.onDestroyView()
    }

    override fun onAction(actionCode: Int) {
        when (actionCode) {
            1 -> {
                ARouter.getInstance()
                        .build(RouteOrg.createOrgActivity)
                        .navigation()
            }
            2 -> {
                ARouter.getInstance()
                        .build(RouteOrg.searchResultActivity)
                        .withString("type", "searchOrg")
                        .navigation()
            }
            else -> {
                EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST, ""))
            }
        }
    }
}

interface OnDocInvokeListener {

    /**web不再处理返回事件时调用*/
    fun onWebGoBack()

    /**web内容初始化完成*/
    fun onWebInitFinish()

    /**web获取用户信息
     *
     * js method is :pan_getUserBaseInfo
     * */
    fun webSaveUserInfo()

    /**web获取用户信息
     *
     * js method is :ddbes_pan.pan_h5SaveToken(token:String)
     * */
    fun webGetToken()

    /**
     * web 接收token
     * js method is :ddbes_pan.pan_h5SaveToken(token:String)*/
    fun webSaveToken()

    /**
     * web获取当前公司信息
     * 页面初始化完和切换公司后要通过这个方法返回给web json
     *
     * js method is :pan_getCurrentCompanyInfo
     * */
    fun webSaveCurrentCompany()

    /**web触发公司切换，弹出侧边栏*/
    fun onChangeCompany()

    /**web触发 复制功能，增加内容到剪贴板*/
    fun onCopy(data: String)

    /**web触发 存储数据到缓存*/
    fun onSaveData(data: String)

    /**web触发 获取设置的缓存信息*/
    fun onGetData()

    /**
     * 获取web调用原生缓存的内容
     * js method is :pan_getStorage
     */
    fun webGetData(data: String)

    /**
     * web 跳转传输列表
     *
     */
    fun showTransList()

    /**显示底部标签切换栏*/
    fun showBottomBar(show: Boolean)

    /**选择照片*/
    fun selectPic(parentId: String, type: Int)

    /**拍照*/
    fun takePhoto(parentId: String)

    /**图片文件上传成功后，通知web*/
    fun onFileUploaded()

    /**保存图片到相册 */
    fun saveToGallery(json: String)

    /**预览文件 1 图片 2 文件*/
    fun previewFile(data: String)

    /**在线预览文件 新 */
    fun previewFileOnline(data: String)

    /**分享文件给好友*/
    fun shareToFriend(msg: String)

    /**分享文件到微信*/
    fun shareToWx(msg: String)

    /**分享文件给同事*/
    fun shareToOrgMember(msg: String)

    /**系统返回键按下时，调用web，触发页面回退*/
    fun onBackPress()
}
