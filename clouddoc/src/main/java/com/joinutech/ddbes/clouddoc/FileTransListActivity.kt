package com.joinutech.ddbes.clouddoc

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.common.adapter.MultiTypeAdapter
import com.joinutech.common.storage.*
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.widget.CircleProgressView
import com.joinutech.ddbes.clouddoc.databinding.ActivityFileTransListLayoutBinding
import com.joinutech.ddbeslibrary.base.SimpleBaseActivity
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.activity.TaskImagePreviewActivity
import com.luck.picture.lib.config.PictureMimeType
import java.util.*

/**
 * @PackageName: com.joinutech.ddbes.clouddoc
 * @ClassName: FileTransListActivity
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/7/27 14:28
 * @Desc: 文件传输列表页面 // TODO: 2020/7/27 14:29
 */
@Route(path = "/clouddoc/trans_list")
class FileTransListActivity : SimpleBaseActivity<ActivityFileTransListLayoutBinding>(), TransListener {
    override val contentViewResId: Int = R.layout.activity_file_trans_list_layout
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityFileTransListLayoutBinding {
        return ActivityFileTransListLayoutBinding.inflate(layoutInflater)
    }

    override fun initView() {
        initTitleBar()
        initFileList()
        
        binding.ivSelectAll.setOnClickListener {
            it.isSelected = !it.isSelected
            selectFileList.clear()
            fileList.forEach { file ->
                if (file.type == 0) {
                    if (it.isSelected) {
                        selectFileList.add(file.index)
                    }
                }
            }
            adapter.notifyDataSetChanged()
        }
       
         binding.tvSelectAll.setOnClickListener {
            binding.ivSelectAll.isSelected = !binding.ivSelectAll.isSelected
            selectFileList.clear()
            fileList.forEach { file ->
                if (file.type == 0) {
                    if ( binding.ivSelectAll.isSelected) {
                        selectFileList.add(file.index)
                    }
                }
            }
            adapter.notifyDataSetChanged()
        }
        
        binding.llDelete.setOnClickListener {
            onDelete()
        }

        selectedData.observe(this, androidx.lifecycle.Observer {
            showLog("选中项发生变化 ${it.size}")
        })
        initTab()
    }

    private fun onDelete() {
        val helper = object : CenterDialogHelper(
                this, layoutId = R.layout.dialog_transfer_delete_tip_layout,
                onConfirm = {
                    if (companyId.isNotBlank() && selectFileList.isNotEmpty()) {
                        if (binding.tvDownload.isSelected) {
                            FileDownTransferManager.onCancel(companyId, selectFileList)
                        } else {
                            FileUpTransferManager.onCancel(companyId, selectFileList)
                        }
                        updateTransList()
                        selectFileList.clear()
                    }
                },
                onCancel = {

                }) {
            @SuppressLint("SetTextI18n")
            override fun bindView(dialogView: View) {
                // 选中且传输中的数据
                val tag = fileList.find { it.index in selectFileList && it.state == 1 }
                val state = if (binding.tvDownload.isSelected) "下载" else "上传"
                if (tag != null) {
                    dialogView.findViewById<TextView>(R.id.tv_content).text = "确定要删除正在${state}的文件吗？"
                } else {
                    dialogView.findViewById<TextView>(R.id.tv_content).text = "确定要删除${state}记录吗？"
                }

                val confirm = dialogView.findViewById<TextView>(R.id.tv_confirm)
                confirm.text = "删除"
                confirm.setTextColor(ContextCompat.getColor(dialogView.context, com.joinutech.ddbeslibrary.R.color.main_blue))
                confirm.setOnClickListener {
                    dialog?.dismiss()
                    onConfirm.invoke()
                }
                val cancel = dialogView.findViewById<TextView>(R.id.tv_cancel)
                cancel.text = "下次再说"
                cancel.setTextColor(ContextCompat.getColor(dialogView.context, com.joinutech.ddbeslibrary.R.color.main_blue))
                cancel.setOnClickListener {
                    dialog?.dismiss()
                    onCancel.invoke()
                }
            }
        }
        helper.show()
    }

    lateinit var adapter: MultiTypeAdapter<FileTransferInfo>
    private val fileUpList = arrayListOf<FileTransferInfo>()
    private val fileDownList = arrayListOf<FileTransferInfo>()
    private val fileList = arrayListOf<FileTransferInfo>()

    private val selectFileList = arrayListOf<String>()

    private val selectedData: MutableLiveData<List<String>> = MutableLiveData(selectFileList)

    private fun initFileList() {
        adapter = MultiTypeAdapter(this, fileList,
                generateViewType = { _: Int, FileTransferInfo: FileTransferInfo ->
                    FileTransferInfo.type
                },
                generateLayoutId = { type ->
                    when (type) {
                        0 -> R.layout.item_file_list_layout
                        else -> R.layout.item_load_filter_layout
                    }
                },
                onBindItem = { position: Int, file: FileTransferInfo, view: View ->
                    if (file.type > 0) {
                        val tvFilterTitle = view.findViewById<TextView>(R.id.tv_file_count)
                        val tvState = view.findViewById<TextView>(R.id.tv_state)
                        val ivState = view.findViewById<ImageView>(R.id.iv_state)
                        if (file.type == 1) {
                            if (binding.tvDownload.isSelected) { // 下载中
                                tvFilterTitle.text = "正在下载...(${file.progress})"
                                if (file.progress > 0) {
                                    val process = fileDownList.find { it.state == 1 }
                                    if (process != null) {
                                        tvState.visibility = View.VISIBLE
                                        ivState.visibility = View.VISIBLE
                                        tvState.text = "全部暂停"
                                        ivState.setImageResource(R.drawable.ic_transfer_pause)
                                        tvState.setOnClickListener {
                                            val companyId = CompanyHolder.getCurrentOrg()?.companyId
                                                    ?: ""
                                            val pauseList = fileDownList.filter { it.state == 1 }.map { it.index }
                                            if (companyId.isNotBlank() && pauseList.isNotEmpty()) {
                                                FileDownTransferManager.onPause(companyId, pauseList)
                                                updateTransList()
                                            }
                                        }
                                    } else {
                                        val pause = fileDownList.find { it.state == 2 }
                                        if (pause != null) {
                                            tvState.visibility = View.VISIBLE
                                            ivState.visibility = View.VISIBLE
                                            tvState.text = "全部继续"
                                            ivState.setImageResource(R.drawable.ic_transfer_retry)
                                            tvState.setOnClickListener {
                                                val companyId = CompanyHolder.getCurrentOrg()?.companyId
                                                        ?: ""
                                                val pauseList = fileDownList.filter { it.state == 2 }.map { it.index }
                                                if (companyId.isNotBlank() && pauseList.isNotEmpty()) {
                                                    FileDownTransferManager.onResume(companyId, pauseList)
                                                    updateTransList()
                                                }
                                            }
                                        } else {
                                            tvState.visibility = View.GONE
                                            ivState.visibility = View.GONE
                                        }
                                    }
                                } else {
                                    tvState.visibility = View.GONE
                                    ivState.visibility = View.GONE
                                }
                            } else { // 上传中
                                tvFilterTitle.text = "正在上传...(${file.progress})"
                                if (file.progress > 0) {
                                    val process = fileUpList.find { it.state == 1 }
                                    if (process != null) {
                                        tvState.visibility = View.VISIBLE
                                        ivState.visibility = View.VISIBLE
                                        tvState.text = "全部暂停"
                                        ivState.setImageResource(R.drawable.ic_transfer_pause)
                                        tvState.setOnClickListener {
                                            val companyId = CompanyHolder.getCurrentOrg()?.companyId
                                                    ?: ""
                                            val pauseList = fileUpList.filter { it.state == 1 }.map { it.index }
                                            if (companyId.isNotBlank() && pauseList.isNotEmpty()) {
                                                FileUpTransferManager.onPause(companyId, pauseList)
                                                updateTransList()
                                            }
                                        }
                                    } else {
                                        val pause = fileUpList.find { it.state == 2 }
                                        if (pause != null) {
                                            tvState.visibility = View.VISIBLE
                                            ivState.visibility = View.VISIBLE
                                            tvState.text = "全部继续"
                                            ivState.setImageResource(R.drawable.ic_transfer_retry)
                                            tvState.setOnClickListener {
                                                val companyId = CompanyHolder.getCurrentOrg()?.companyId
                                                        ?: ""
                                                val pauseList = fileUpList.filter { it.state == 2 }.map { it.index }
                                                if (companyId.isNotBlank() && pauseList.isNotEmpty()) {
                                                    FileUpTransferManager.onResume(companyId, pauseList)
                                                    updateTransList()
                                                }
                                            }
                                        } else {
                                            tvState.visibility = View.GONE
                                            ivState.visibility = View.GONE
                                        }
                                    }
                                } else {
                                    tvState.visibility = View.GONE
                                    ivState.visibility = View.GONE
                                }
                            }
                        } else {
                            tvFilterTitle.text = if (binding.tvDownload.isSelected) {
                                "下载成功(${file.progress})"
                            } else {
                                "上传成功(${file.progress})"
                            }
                        }
                    } else {
                        // 文件传输中
                        view.findViewById<TextView>(R.id.tv_file_name).text = file.name
                        val ivSelect = view.findViewById<ImageView>(R.id.iv_select)
                        ivSelect.visibility = if (showManage) {
                            View.VISIBLE
                        } else {
                            View.GONE
                        }
                        ivSelect.isSelected = selectFileList.contains(file.index)
                        ivSelect.setOnClickListener {
                            if (selectFileList.contains(file.index)) {
                                selectFileList.remove(file.index)
                            } else {
                                selectFileList.add(file.index)
                            }
                            adapter.notifyItemChanged(position)
                            binding.ivSelectAll.isSelected = selectFileList.isNotEmpty() && selectFileList.size == fileList.size - 2
                        }

                        if (file.state == 3) { // 已完成
                            if (file.isDownload) {
                                // 下载成功显示本地图片
                                ImageLoaderUtils.loadImage(this, view.findViewById(R.id.iv_file_icon), file.srcPath + "/" + file.name)
                            } else {
                                // 上传成功的图片显示本地图片
                                ImageLoaderUtils.loadImage(this, view.findViewById(R.id.iv_file_icon), file.srcPath)
                            }
                            val tvStateInfo = view.findViewById<TextView>(R.id.tv_file_progress_info)
                            tvStateInfo.text =
                                    if (file.updateTime > 0) {
                                        XUtil.getTime(Date(file.updateTime), TIME_FORMAT_PATTERN1) + "," + StringUtils.getPrintSize(file.total)
                                    } else {
                                        StringUtils.getPrintSize(file.total)
                                    }
                            tvStateInfo.isSelected = file.state < 0

                            view.findViewById<View>(R.id.right_menu).visibility = View.GONE
                        } else {
                            // 进行中和等待中
                            view.findViewById<View>(R.id.right_menu).visibility = View.VISIBLE
                            if (file.isDownload) {
                                ImageLoaderUtils.loadImage(this, view.findViewById(R.id.iv_file_icon), file.cosPath)
                            } else {
                                ImageLoaderUtils.loadImage(this, view.findViewById(R.id.iv_file_icon), file.srcPath)
                            }

                            val cpv = view.findViewById<CircleProgressView>(R.id.cpv_transfer_progress)
                            val ivTransfer = view.findViewById<ImageView>(R.id.iv_transfer)
                            val tvStateInfo = view.findViewById<TextView>(R.id.tv_file_progress_info)
                            var stateInfo = StringUtils.getPrintSize(file.completed) + "/" + StringUtils.getPrintSize(file.total)
                            when {
                                file.state == 2 -> { // pause
                                    ivTransfer.visibility = View.VISIBLE
                                    cpv.visibility = View.GONE
                                    if (binding.tvDownload.isSelected) {
                                        ivTransfer.setImageResource(R.drawable.ic_transfer_download_2)
                                    } else {
                                        ivTransfer.setImageResource(R.drawable.ic_transfer_upload_2)
                                    }

                                }
                                file.state == 1 -> {// process
                                    ivTransfer.visibility = View.GONE
                                    cpv.visibility = View.VISIBLE
                                    cpv.updateProgress(file.progress)
                                    cpv.updateState(file.state)
                                }
                                file.state == 0 -> {// wait
                                    ivTransfer.visibility = View.GONE
                                    cpv.visibility = View.VISIBLE
                                    cpv.updateProgress(0)
                                    cpv.updateState(file.state)
                                }
                                file.state < 0 -> {// error
                                    ivTransfer.visibility = View.VISIBLE
                                    ivTransfer.setImageResource(R.drawable.ic_transfer_retry)
                                    cpv.visibility = View.GONE
                                    stateInfo = if (binding.tvDownload.isSelected) "下载失败" else "上传失败"
                                }
                            }
                            tvStateInfo.text = stateInfo
                            tvStateInfo.isSelected = file.state < 0

                            view.findViewById<View>(R.id.right_menu).setOnClickListener {
                                if (companyId.isNotBlank()) {
                                    if (binding.tvDownload.isSelected) {
                                        if (file.state == 1 || file.state == 0) {
                                            FileDownTransferManager.onPause(companyId, arrayListOf(file.index))
                                        } else if (file.state == 2) {
                                            FileDownTransferManager.onResume(companyId, arrayListOf(file.index))
                                        } else {
                                            FileDownTransferManager.onRetry(companyId, arrayListOf(file.index))
                                        }
                                    } else {
                                        if (file.state == 1 || file.state == 0) {
                                            FileUpTransferManager.onPause(companyId, arrayListOf(file.index))
                                        } else if (file.state == 2) {
                                            FileUpTransferManager.onResume(companyId, arrayListOf(file.index))
                                        } else {
                                            FileUpTransferManager.onRetry(companyId, arrayListOf(file.index))
                                        }
                                    }
                                }
                                updateTransList()
                            }

                        }
                        if (position != fileList.lastIndex) {
                            if (fileList[position + 1].type != file.type) {
                                view.findViewById<View>(R.id.bottom_line).visibility = View.GONE
                            } else {
                                view.findViewById<View>(R.id.bottom_line).visibility = View.VISIBLE
                            }
                        } else {
                            view.findViewById<View>(R.id.bottom_line).visibility = View.GONE
                        }
                    }
                },
                onItemClick = { _: Int, fileInfo: FileTransferInfo, _: View ->
                    showLog("点击查看文件-->>$fileInfo")
                    val intent = Intent(mContext, TaskImagePreviewActivity::class.java)
                    val filePath = if (fileInfo.isDownload) {
                        if (fileInfo.state == 3) {
                            fileInfo.srcPath + "/" + fileInfo.name
                        } else {
                            fileInfo.cosPath
                        }
                    } else {
                        fileInfo.srcPath
                    }
                    if (PictureMimeType.isSuffixOfImage(filePath.toLowerCase())) {
                        val picList = arrayListOf(filePath)
                        val previewDataBean = TaskImagePreviewActivity.PreviewDataBean(
                                0, picList, true)
                        val bundle = Bundle()
                        bundle.putSerializable("previewData", previewDataBean)
                        intent.putExtras(bundle)
                        mContext!!.startActivity(intent)
                    } else {
                        toastShort("当前文件不可预览")
                    }
                })
        
        binding.rvFileList.layoutManager = LinearLayoutManager(this)
        binding.rvFileList.adapter = adapter
    }

    private fun initTab() {

        fun showDownLoad() {
            binding.tvDownload.isSelected = true
            binding.indicatorDownload.visibility = View.VISIBLE
            
            binding.tvUpload.isSelected = false
            
            binding.indicatorUpload.visibility = View.GONE
            fileList.clear()
            fileList.addAll(fileDownList)
            checkRightAction()
            changeFileData()
        }

        fun showUpload() {
            
            binding.tvDownload.isSelected = false
            
            binding.indicatorDownload.visibility = View.GONE
            binding.tvUpload.isSelected = true
            binding.indicatorUpload.visibility = View.VISIBLE
            fileList.clear()
            fileList.addAll(fileUpList)
            checkRightAction()
            changeFileData()
        }

        binding.tvDownload.setOnClickListener {
            if (!showManage) {
                showDownLoad()
            }
        }
        binding.tvUpload.setOnClickListener {
            if (!showManage) {
                showUpload()
            }
        }
        val targetIndex = intent.getIntExtra("targetIndex", 0)
        if (targetIndex == 0) {
            showDownLoad()
        } else {
            showUpload()
        }
    }

    private var showManage = false

    private fun updateBottom() {
        if (showManage) {
            
            binding.llBottomManage.visibility = View.VISIBLE
            binding.ivSelectAll.isSelected = false
        } else {
            binding.llBottomManage.visibility = View.GONE
        }
    }

    private fun changeFileData() {
        updateBottom()
        adapter.notifyDataSetChanged()
    }

    private fun initTitleBar() {
        val titleBarContainer = findViewById<View>(com.joinutech.ddbeslibrary.R.id.title_bar_container)
        mImmersionBar?.titleBar(titleBarContainer)
        setPageTitle("传输列表")
        hideToolBarLine()
    }

    var companyId = ""
    override fun onResume() {
        super.onResume()
        companyId = CompanyHolder.getCurrentOrg()?.companyId ?: ""
        updateTransList()
//        addScheduleTask()
    }

    private fun updateTransList() {
        fileList.clear()

        fun getUploadFileTransferInfo() {
            fileUpList.clear()
            if (companyId.isNotBlank()) {
                fileUpList.addAll(FileUpTransferManager.getTransHistory(companyId))
            }
            FileStorage.showLog("获取上传文件记录数为：${fileUpList.size - 2} ")
        }

        fun getDownLoadFileTransferInfo() {
            fileDownList.clear()
            if (companyId.isNotBlank()) {
                fileDownList.addAll(FileDownTransferManager.getTransHistory(companyId))
            }
            FileStorage.showLog("获取下载文件记录数为：${fileDownList.size - 2} ")
        }

        getUploadFileTransferInfo()
        getDownLoadFileTransferInfo()

        if (binding.tvDownload.isSelected) {
            fileList.addAll(fileDownList)
        } else {
            fileList.addAll(fileUpList)
        }
        checkRightAction()

        FileUpTransferManager.onTransListener = this
        FileDownTransferManager.onTransListener = this
        adapter.notifyDataSetChanged()
    }

    private fun checkRightAction() {
        if (fileList.size == 2) {
            hideRightText()
            if (showManage) {
                showManage = false
                updateBottom()
            }
            
            binding.llEmptyLayout.visibility = View.VISIBLE
            if (binding.tvDownload.isSelected) {
                 binding.ivEmptyIcon.setImageResource(R.drawable.ic_download_empty)
                binding.tvEmptyInfo.text = "暂无下载记录"
                
            } else {
                binding.ivEmptyIcon.setImageResource(R.drawable.ic_upload_empty)
                binding.tvEmptyInfo.text = "暂无上传记录"
            }
            binding.rvFileList.visibility = View.GONE
        } else {
            binding.llEmptyLayout.visibility = View.GONE
            binding.rvFileList.visibility = View.VISIBLE
            if (!showManage) {
                setRightTitle("管理", View.OnClickListener {
                    showManage = !showManage
                    tv_rightTitle?.let {
                        it.text = if (showManage) {
                            "取消"
                        } else {
                            "管理"
                        }
                    }
                    changeFileData()
                })
            }
        }
    }

//    private var task: ScheduleTask? = null
//    private fun addScheduleTask() {
//        task = ScheduleTask()
//        task?.init {
//            updateTransList()
//        }
//        task?.start()
//    }

    override fun onPause() {
        super.onPause()
        FileUpTransferManager.onSave()
        FileDownTransferManager.onSave()
    }

    override fun onDestroy() {
//        if (task != null) {
//            task?.stop()
//        }
        FileUpTransferManager.onTransListener = null
        FileDownTransferManager.onTransListener = null
        super.onDestroy()
    }

    override fun onNoTask(companyId: String) {
        if (this.companyId.isNotBlank() && this.companyId == companyId) {
            updateTransList()
        }
    }

    override fun onTransResult(companyId: String, file: FileTransferInfo) {
        if (this.companyId.isNotBlank() && this.companyId == companyId) {
            updateTransList()
        }
    }

    override fun onTransProcess(companyId: String, file: FileTransferInfo) {
        if (this.companyId.isNotBlank() && this.companyId == companyId) {
            if (file.isDownload) {
                if (binding.tvDownload.isSelected) {
                    val downFile = fileDownList.find { it.index == file.index }
                    if (downFile != null) {
                        val index = fileDownList.indexOf(downFile)
                        fileDownList[index] = file
                        fileList[index] = file
                        runOnUiThread {
                            adapter.notifyItemChanged(index)
                        }
                    }
                }
            } else {
                if (binding.tvUpload.isSelected) {
                    val upFile = fileUpList.find { it.index == file.index }
                    if (upFile != null) {
                        val index = fileUpList.indexOf(upFile)
                        fileUpList[index] = file
                        fileList[index] = file
                        runOnUiThread {
                            adapter.notifyItemChanged(index)
                        }
                    }
                }
            }
        }
    }

}