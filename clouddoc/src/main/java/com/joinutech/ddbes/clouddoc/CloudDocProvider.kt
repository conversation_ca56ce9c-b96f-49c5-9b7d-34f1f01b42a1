package com.joinutech.ddbes.clouddoc

import android.content.Context
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.facade.template.IProvider
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.common.storage.FileDownTransferManager
import com.joinutech.common.storage.FileStorage
import com.joinutech.common.storage.FileUpTransferManager

@Route(path = "/clouddoc/service")
class CloudDocProvider : CloudDocService {

    override fun init(context: Context?) {
        FileStorage.showLog("cloud service init")
        FileUpTransferManager.onLoad()
        FileDownTransferManager.onLoad()
    }

    override fun openPage(path: String, params: Bundle) {

    }

    override fun openPageWithResult(activity: FragmentActivity, path: String, params: Bundle, result: (data: String) -> Unit) {
    }

    override fun openPageWithResult1(
        activity: FragmentActivity,
        path: String,
        params: Bundle,
        result: (data: String) -> Unit
    ) {

    }

    override fun service(path: String, params: Bundle, result: (data: String) -> Unit) {
    }

}

interface CloudDocService : RouteServiceProvider