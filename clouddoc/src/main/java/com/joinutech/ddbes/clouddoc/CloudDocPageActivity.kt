package com.joinutech.ddbes.clouddoc

import android.os.Bundle
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity

/**
 * @PackageName: com.joinutech.ddbes.clouddoc
 * @ClassName: CloutDocPageActivity
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/9/16 9:48
 * @Desc: //TODO 协作文件查看页面
 */
@Route(path = "/clouddoc/cloud_page")
class CloudDocPageActivity : MyUseBaseActivity() {
    override val contentViewResId: Int = R.layout.activity_cloud_doc_layout

    @JvmField
    @Autowired
    var targetUrl: String = ""

    override fun initImmersion() {
        whiteStatusBarBlackFont()
    }

    private var onDocBackListener: OnDocInvokeListener? = null

    override fun initView() {
        val token = UserHolder.getAccessToken()
        if (targetUrl.isNotBlank() && token.isNotBlank()) {
            val cloudDocFragment = CloudDocFragment()
            val bundle = Bundle()
            bundle.putBoolean("subPage", false)
            bundle.putString("targetUrl", targetUrl.plus("&token=$token"))
            cloudDocFragment.arguments = bundle
//            cloudDocFragment.listener = this
            onDocBackListener = cloudDocFragment
            val transaction = supportFragmentManager.beginTransaction()
            transaction.replace(R.id.page_content, cloudDocFragment)
            transaction.commit()
        } else {
            finish()
        }
    }

    override fun showToolBar(): Boolean {
        return false
    }

    override fun openArouterReceive(): Boolean {
        return true
    }

    override fun onBackPressed() {
        if (onDocBackListener != null) {
//            EventBusUtils.sendEvent(EventBusEvent("cloud_doc_back_press", ""))
            onDocBackListener?.onBackPress()
        } else {
            super.onBackPressed()
        }
    }
}