<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="42dp"
    android:id="@+id/cl_root"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView
        android:id="@+id/itemLocationIv"
        android:layout_width="10dp"
        android:layout_height="12dp"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginStart="14dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:src="@drawable/icon_location_blue"
        />
    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="公司"
        android:textColor="#ff1e87f0"
        android:textSize="12sp"
        app:layout_constraintLeft_toRightOf="@id/itemLocationIv"
        android:layout_marginStart="9dp"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="4dp"
        />
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/address"
        android:text="河北省廊坊市广阳区廊坊经济技术开发区楼庄路"
        android:textColor="#ff9a9a9a"
        android:textSize="12sp"
        android:maxEms="20"
        android:singleLine="true"
        app:layout_constraintLeft_toRightOf="@id/itemLocationIv"
        android:layout_marginStart="9dp"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="4dp"
        />
    <ImageView
        android:id="@+id/del"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/del_img"
        app:layout_constraintRight_toRightOf="parent"
        android:padding="14dp"
        app:layout_constraintTop_toTopOf="parent"
        />
</androidx.constraintlayout.widget.ConstraintLayout>