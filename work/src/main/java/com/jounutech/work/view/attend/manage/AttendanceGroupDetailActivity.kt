package com.jounutech.work.view.attend.manage

import Coordinate
import TIANDITU_URL
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.animation.CycleInterpolator
import android.widget.EditText
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.common.base.LinkBuilder
import com.joinutech.common.util.ObjectStore
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.AteHolidaySelf
import com.joinutech.ddbeslibrary.bean.AttendApprover
import com.joinutech.ddbeslibrary.bean.AttendApproverReq
import com.joinutech.ddbeslibrary.bean.AttendanceDepBean
import com.joinutech.ddbeslibrary.bean.AttendanceDetailBean
import com.joinutech.ddbeslibrary.bean.AttendanceLocationBean
import com.joinutech.ddbeslibrary.bean.AttendanceMemberBean
import com.joinutech.ddbeslibrary.bean.TimeUserIdsSender
import com.joinutech.ddbeslibrary.bean.WifiBean
import com.joinutech.ddbeslibrary.utils.ATTENDANCE_CLOCK
import com.joinutech.ddbeslibrary.utils.ATTENDANCE_DEP
import com.joinutech.ddbeslibrary.utils.ATTENDANCE_LOCATION
import com.joinutech.ddbeslibrary.utils.ATTENDANCE_PERSON
import com.joinutech.ddbeslibrary.utils.ATTENDANCE_SPECIAL_DATE
import com.joinutech.ddbeslibrary.utils.ATTENDANCE_WIFI
import com.joinutech.ddbeslibrary.utils.ATTENDANCE__DATE
import com.joinutech.ddbeslibrary.utils.COMMON_WEB_ACTIVITY
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.ConsKeys
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.MyDialog
import com.joinutech.ddbeslibrary.utils.RouteIm
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.jounutech.work.R
import com.jounutech.work.adapter.AttendanceLocationAdapter
import com.jounutech.work.constract.AttendanceConstract
import com.jounutech.work.constract.RouteAte
import com.jounutech.work.databinding.ActivityAttendanceGroupDetailBinding
import com.jounutech.work.inject.DaggerAttendanceComponent
import com.jounutech.work.presenter.AttendanceDetailPresenter
import com.jounutech.work.util.AttendanceUtil
import com.jounutech.work.view.attend.manage.outclock.AttendApproverActivityResultCode
import com.jounutech.work.view.attend.manage.outclock.AttendApproverActivityResultKey
import com.jounutech.work.view.attend.manage.outclock.OutClockApproverItemData
import com.nineoldandroids.view.ViewPropertyAnimator
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import timber.log.Timber
import wgs84ToBd09
import javax.inject.Inject
import javax.inject.Named

/**
 * 考勤组详情
 * <AUTHOR> by 黄洁如 on 2019/3/21 15:40
 * @fileName AttendanceDetailActivity
 * @describe TODO
 * @org 加优科技
 */
class AttendanceGroupDetailActivity : MyUseBindingActivity<ActivityAttendanceGroupDetailBinding>() {

    override val contentViewResId: Int
        get() = R.layout.activity_attendance_group_detail

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityAttendanceGroupDetailBinding {
       return ActivityAttendanceGroupDetailBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return false
    }

    private var type: String = ""
    private var ateId: String = ""
    private var attendanceName = ""

    private var locationList = ArrayList<AttendanceLocationBean>()
    private lateinit var locationAdapter: AttendanceLocationAdapter
    private var ateHolidaySelves = ArrayList<AteHolidaySelf>()
    private var wifiBeanList = arrayListOf<WifiBean>()
    private var depList = arrayListOf<AttendanceDepBean>()
    private var personList = arrayListOf<AttendanceMemberBean>()
    private var needPicture=0//是否是拍照打卡,0代表不需要，1代表需要
    private var needOutclockAck=0//是否开启外部审核,0代表不需要，1代表需要
    private var approverList= arrayListOf<AttendApproverReq>()//外部审批人列表

    @Inject
    @field:Named(AttendanceUtil.ATTENDANCE_PRESENTER)
    lateinit var presenter: AttendanceConstract.AttendancePresenter
    private var attendanceBean: AttendanceDetailBean? = null
    private var isRmd: Boolean = false
    private var rmdTime: Int = 0

    private var companyId: String = ""

    //    private var memberIsNull = false
    private var deptIsNull: Boolean = false//清空部门

    //    private var mChoiceFreeTimeUsers: ArrayList<AttendanceMemberBean>? = arrayListOf()
    private var mChoiceFreeTimeUsers = arrayListOf<String>()
    private var mChoiceDepartmentTimeIds: ArrayList<AttendanceDepBean>? = arrayListOf()

    @Inject
    @field:Named(AttendanceUtil.ATTENDANCE_DETAIL_PRESENTER)
    lateinit var ownPresenter: AttendanceDetailPresenter
    private var specialMap = hashMapOf<String, AteHolidaySelf>()

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        initIntent()
        when (type) {
            RouteAte.ATTEND_GROUP_TYPE_FIXED -> {
                setPageTitle("固定班次考勤组")
            }
            RouteAte.ATTEND_GROUP_TYPE_FREE -> {
                setPageTitle("自由打卡考勤组")
            }
            "fixChange", "freeChange" -> {
                setPageTitle("修改考勤组")
            }
        }
        setRightTitle("保存", this)
    }

    override fun initView() {
        showToolBarLine()
        whiteStatusBarBlackFont()
        binding.locationListRev.layoutManager = LinearLayoutManager(this)
        DaggerAttendanceComponent.builder().build().inject(this)
        ownPresenter.setActivity(this)
    }

    override fun initLogic() {
        initClickListener()
        initLocationAdapter()
        if ((type == "fixChange" || type == "freeChange") && ateId.isNotEmpty()) {
            //如果是修改考勤组就要先去拿考勤组数据
            getAttendanceDetail()
        } else {
            //这是新增考勤组
            setDefaultAteSet()
        }
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            tv_rightTitle -> {
                //点击保存，修改考勤组和新增考勤组界面
                saveData()
            }
            binding.timeLayout -> {
                ownPresenter.enterTimeSelectActivity(this, ateId, isRmd, rmdTime)
            }
            binding.dateLayout -> {
                ownPresenter.enterDateSelectActivity(this)
            }
            binding.locationLayout -> {
//                ARouter.getInstance()
//                        .build(RouteIm.locationActivity)
//                        .withString("type", "addAttendanceLocation")
//                        .navigation(this, ATTENDANCE_LOCATION)
                val url = "${TIANDITU_URL}?mapViewPageType=${MapRouterType.GET_LOCATION.type}"
                val mapUrl = LinkBuilder.generate().getResultUrl(url)
                ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                    .withString(ConsKeys.PARAMS_URL, mapUrl)
                    .withString("type", "addAttendanceLocation")
                    .withInt("backspaceKey",1)
                    .withString("titleName", "添加考勤地点")
                    .navigation(this ,ATTENDANCE_LOCATION)
            }
            binding.rangeLayout -> {
                ownPresenter.showAttendanceRange(binding.rangeText)
            }
            binding.specialDateLayout -> {
                enterSpecialDateActivity()
            }
            binding.wifiLayout -> {
                enterWifiSelectActivity()
            }
            binding.personLayout -> {
                enterPersonSelectActivity()
            }
            binding.depLayout -> {
                enterDeptSelectActivity()
            }
        }
    }

    private fun dealDateActivityReturn(data: Intent) {
        binding.dateText.text = ownPresenter.dealDateActivityReturn(data, binding.dateText.text.toString())
    }

    private fun dealTimeActivityReturn(data: Intent) {
        isRmd = data.getBooleanExtra("isRmd", false)
        if (StringUtils.isNotBlankAndEmpty(data.getStringExtra("remindTime"))) {
            rmdTime = data.getStringExtra("remindTime")!!.toInt()
        }
        if (StringUtils.isNotBlankAndEmpty(data.getStringExtra("contentTime"))) {
            ownPresenter.attendanceContent = data.getStringExtra("contentTime")!!
            ownPresenter.attendanceContent = if (ownPresenter.attendanceContent.contains("#"))
                ownPresenter.attendanceContent.substring(
                        ownPresenter.attendanceContent.lastIndexOf("#") + 1)
            else ownPresenter.attendanceContent
            binding.timeText.text = ownPresenter.attendanceContent
        }
        ownPresenter.dealTimeActivityReturn(data)
    }

    private fun initIntent() {
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("attendanceType"))) {
                type = intent.getStringExtra("attendanceType")!!
            }
            if (intent.hasExtra("ateId") && !intent.getStringExtra("ateId").isNullOrBlank()) {
                ateId = intent.getStringExtra("ateId")!!
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                companyId = intent.getStringExtra("companyId")!!
            }
        }
    }

    private fun initLocationAdapter() {
        locationAdapter = AttendanceLocationAdapter(mContext!!, locationList)
        binding.locationListRev.adapter = locationAdapter
        locationAdapter.setDelListener(object : AttendanceLocationAdapter.DelListener {
            override fun del(position: Int) {
                if (locationList.isNotEmpty()) {
                    delLocation(position)
                }
            }

        })
    }

    @SuppressLint("SetTextI18n")
    private fun setDefaultAteSet() {
        //如果是固定排班才涉及到考勤时间，日期，工作日特殊调整3个
        if (type == RouteAte.ATTEND_GROUP_TYPE_FIXED) {
            binding.attendanceTimeLayout.visibility = View.VISIBLE
            ownPresenter.initFixed()
            isRmd = false
        } else if (type == "free") {
            binding.attendanceTimeLayout.visibility = View.GONE
        }
        ownPresenter.scope = 500
        binding.rangeText.text = "${ownPresenter.scope}米"
        needPicture=0
        needOutclockAck=0
        binding.cameraSwitchBtn.setCheckedNoEvent(false)
        binding.attendSwitchBtn.setCheckedNoEvent(false)
        binding.attendChooseCl.visibility=View.GONE
    }

    private fun initClickListener() {
        binding.timeLayout.setOnClickListener(this)
        binding.dateLayout.setOnClickListener(this)
        binding.rangeLayout.setOnClickListener(this)
        binding.specialDateLayout.setOnClickListener(this)
        binding.wifiLayout.setOnClickListener(this)
        binding.locationLayout.setOnClickListener(this)
        binding.personLayout.setOnClickListener(this)
        binding.depLayout.setOnClickListener(this)
        binding.cameraSwitchBtn.setOnCheckedChangeListener { buttonView, isChecked ->
            if (isChecked) {
                needPicture = 1
            }else{
                needPicture=0
            }
        }
        binding.attendSwitchBtn.setOnCheckedChangeListener { buttonView, isChecked ->
            if (isChecked) {
                needOutclockAck = 1
                binding.attendChooseCl.visibility=View.VISIBLE
            }else{
                needOutclockAck=0
                binding.attendChooseCl.visibility=View.GONE
            }
        }

        binding.attendChooseCl.setOnClickListener {



            // todo 如果有外勤人员（部门）需要带过去 ！！
            var l = attendanceBean?.outClockReviewInfoList?.filter { StringUtils.isNotBlankAndEmpty(it.deptId) } ?: arrayListOf()

            if (l.isEmpty()){
                l = mTempList
            }

            ObjectStore.add("outClock_attends" , l)

            // 【请选择外勤审核人】： 跳转到选择审批人界面
            ARouter.getInstance()
                .build(RouteAte.outClockSelectAuditor)
                .navigation(this , 0)
        }
    }

    private fun getAttendanceDetail() {
        getLoadingDialog("获取考勤详情", false)
        presenter.getAttendanceGroupDetail(bindToLifecycle(), ateId, companyId, accessToken!!, {
            dismissDialog()
            attendanceBean = it
            attendanceBean?.let {
                parseAttendanceData(attendanceBean!!)
            }
        }, {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun parseAttendanceData(bean: AttendanceDetailBean) {
        if (type.contains(RouteAte.ATTEND_GROUP_TYPE_FIXED)) {
            binding.attendanceTimeLayout.visibility = View.VISIBLE
        } else if (type.contains("free")) {
            binding.attendanceTimeLayout.visibility = View.GONE
        }

        createDpeIdRequestBodyFromNewResult(bean.outClockReviewInfoList ?: arrayListOf())

        commonDataParse(bean)
        if (type.contains("fix")) {
            fixAttendanceTimeShow(bean)
        }
    }


    //修改考勤时，刷新界面
    @SuppressLint("SetTextI18n")
    private fun commonDataParse(bean: AttendanceDetailBean) {
        if (StringUtils.isNotBlankAndEmpty(bean.ateGroupName)) {
            attendanceName = bean.ateGroupName
            binding.nameEdit.setText(attendanceName)
        }

        needPicture=bean.needPicture
        needOutclockAck=bean.needOutclockAck
        binding.cameraSwitchBtn.setCheckedNoEvent(bean.needPicture==1)
        binding.attendSwitchBtn.setCheckedNoEvent(bean.needOutclockAck==1)
        if (bean.needOutclockAck == 1) {
            binding.attendChooseCl.visibility=View.VISIBLE
        }else{
            binding.attendContainCl.visibility=View.VISIBLE
        }

        if (bean.atePlace.isNotEmpty()) {
            binding.locationListRev.visibility = View.VISIBLE
            binding.scopeTopLine.visibility = View.VISIBLE
            locationList = bean.atePlace as ArrayList<AttendanceLocationBean>
            locationAdapter.setSourceList(locationList)
        }
        if (bean.scope != 0) {
            ownPresenter.scope = bean.scope
            binding.rangeText.text = "${ownPresenter.scope}米"
        }
        if (bean.ateWifi.isNotEmpty()) {
            binding.wifiText.text = "wifi已设置${bean.ateWifi.size}个"
            wifiBeanList = bean.ateWifi as ArrayList<WifiBean>
        } else {
            binding.wifiText.text = "未设置"
        }
        personList.addAll(bean.ateMember)
        binding.personText.text = "${personList.size}人"
        deptIsNull = bean.ateDept.isNotEmpty()

        binding.depText.text = "${bean.ateDept.size}个"

        if (deptIsNull) {
            depList = bean.ateDept as ArrayList<AttendanceDepBean>
            mChoiceDepartmentTimeIds = depList
        }
    }

    @SuppressLint("SetTextI18n")
    private fun fixAttendanceTimeShow(bean: AttendanceDetailBean) {
        if (StringUtils.isNotBlankAndEmpty(bean.content)) {
            ownPresenter.attendanceContent = if (bean.content.contains("#"))
                bean.content.substring(
                        bean.content.lastIndexOf("#") + 1)
            else bean.content
            binding.timeText.text = ownPresenter.attendanceContent
        }
        if (StringUtils.isNotBlankAndEmpty(bean.clockDateStatus)) {
            ownPresenter.attendanceDate = bean.clockDateStatus
            ownPresenter.transDate(binding.dateText)
        }
        if (bean.ateHolidaySelves.isNotEmpty()) {
            ateHolidaySelves = bean.ateHolidaySelves as ArrayList<AteHolidaySelf>
            if (ateHolidaySelves.isNotEmpty())
                binding.specialDateText.text = "已调整${ateHolidaySelves.size}天，保存后生效"
            else {
                binding.specialDateText.text = ""
            }
        }
        isRmd = bean.isRmd == 1
        if (bean.rmdTime != 0) {
            rmdTime = bean.rmdTime
        }
        ownPresenter.updateFixedData(bean)
    }

    private fun addLocation(data: Intent) {
        val bd09 = wgs84ToBd09(data.getDoubleExtra("latitude", 0.0),
            data.getDoubleExtra("longitude", 0.0))
        Log.d("test","=====考勤坐标wgs转bd09========lat==${bd09.lat}===lon===${bd09.lon}===")
        val bean = AttendanceLocationBean(
                bd09.lat,
                bd09.lon,
//            data.getDoubleExtra("latitude", 0.0),
//            data.getDoubleExtra("longitude", 0.0),
                data.getStringExtra("title"),
                data.getStringExtra("address"))
        if (bean.address.isNullOrBlank() || bean.location.isNullOrBlank()) return
        val dialog = MyDialog(mContext!!, 0, 0,
                "", needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0)
        val view = View.inflate(mContext!!, R.layout.dialog_addlocation, null)
        val name = view.findViewById<EditText>(R.id.nameEdit)
//        if (StringUtils.isNotBlankAndEmpty(bean.location)) {
//            //将光标移至文字末尾
//            name.setSelection(bean.location.length)
//        }

        name.setText(bean.location)
        view.findViewById<TextView>(R.id.addressText).text = bean.address
        dialog.setView(view, Gravity.CENTER)
        dialog.setCanceledOnTouchOutside(false)
        dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
            override fun clickRightBtn() {
                binding.scopeTopLine.visibility = View.VISIBLE
                if (StringUtils.isNotBlankAndEmpty(name.text.toString()))
                    bean.location = name.text.toString()
                locationList.add(bean)
                locationAdapter.notifyDataSetChanged()
            }

        })
        dialog.show()
    }

    private fun delLocation(position: Int) {
        val dialog = MyDialog(mContext!!, 280, 140,
                "您确定要删除这个考勤地点吗？",
                needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0)
        dialog.setCanceledOnTouchOutside(true)
        dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
            override fun clickRightBtn() {
                locationAdapter.notifyItemRemoved(position)
                locationList.removeAt(position)
                if (locationList.isEmpty()) {
                    binding.scopeTopLine.visibility = View.GONE
                    binding.locationListRev.visibility = View.GONE
                }
                ToastUtil.show(mContext!!, "删除成功")
            }

        })
        dialog.show()
    }

    //点击保存
    private fun saveData() {
        tv_rightTitle?.isClickable = false
        tv_rightTitle?.isEnabled = false
        attendanceName = binding.nameEdit.text.toString()
        if (StringUtils.isEmpty(attendanceName)) {
            ViewPropertyAnimator.animate(binding.nameLayout)
                    .translationX(20F)
                    .setInterpolator(CycleInterpolator(4F))
                    .setDuration(200)
                    .start()
            binding.nameMust.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.red))
            binding.nameTitle.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.red))
            ToastUtil.show(mContext!!, "请设置考勤组名称")
            tv_rightTitle?.isClickable = true
            tv_rightTitle?.isEnabled = true
            return
        }
        if (locationList.isEmpty() && wifiBeanList.isEmpty()) {
            binding.locationTitle.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.red))
            binding.wifiTitle.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.red))
            ToastUtil.show(mContext!!, "请设置考勤地点或者考勤WIFI")
            tv_rightTitle!!.isClickable = true
            tv_rightTitle!!.isEnabled = true
            return
        }
//        val isOpenHoliday = if (ownPresenter.holidaySelected) 1
//        else 0
        val isRmdInt = if (isRmd) 1
        else 0
        val num = when (ownPresenter.clockList.size) {
            1 -> "一次"
            2 -> "两次"
            3 -> "三次"
            else -> "一次"
        }
        ownPresenter.attendanceContent = "一天$num#${ownPresenter.attendanceContent}"
        deptIsNull = depList.isEmpty()
        val attendanceSchedule = if (type.contains("free")) {
            1
        } else 0

        // 保存之前 准备数据
        attendanceBean = ownPresenter.buildAttend(attendanceSchedule, depList, attendanceName,
                ateHolidaySelves, personList, locationList, wifiBeanList, companyId, deptIsNull,
                isRmdInt, personList.isNullOrEmpty(), rmdTime,needPicture,needOutclockAck, approverList)

        if (type.contains("Change") && ateId.isNotEmpty()) {
            attendanceBean?.ateId = ateId
            getLoadingDialog("修改考勤组...", false)
            //修改考勤组
            Log.i("moon" , "修改考勤组之前的准备数据 ${attendanceBean}")
            presenter.updateAttendanceGroup(bindToLifecycle(), attendanceBean!!, accessToken!!, {
                dismissDialog()
                ToastUtil.show(mContext!!, "修改考勤组成功")
                intent.putExtra("type", type)
                setResult(Activity.RESULT_OK, intent)
                finish()
            }, {
                dismissDialog()
                tv_rightTitle?.isClickable = true
                tv_rightTitle?.isEnabled = true
                ToastUtil.show(mContext!!, it.message)
                if (it.code == 3001) {
                    //没有权限
                    EventBusUtils.sendEvent(EventBusEvent(
                            EventBusAction.Event_ATTENDANCE_PERMISSION_LEAVE, ""))
                    val dialog = MyDialog(mContext!!, 280, 140,
                            "您的权限状态异常，无法完成操作",
                            needBtnConfirm = true, needBtnCancel = false, bgResourceId = 0)
                    dialog.setBtnLeftText("好的")
                    dialog.setCanceledOnTouchOutside(false)
                    dialog.setCancelable(false)
                    dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
                        override fun clickLeftBtn() {
                            finish()
                        }

                    })
                    dialog.show()
                }
            })
        } else {
            getLoadingDialog("增加考勤组...", false)

            Log.i("moon" , "增加考勤组之前的准备数据 ${attendanceBean?.ateOutclockReviewList}")

            presenter.addAttendanceGroup(bindToLifecycle(), attendanceBean!!, accessToken!!, {
                dismissDialog()
                ToastUtil.show(mContext!!, "添加考勤组成功")
                setResult(Activity.RESULT_OK)
                finish()
            }, {
                dismissDialog()
                tv_rightTitle!!.isClickable = true
                tv_rightTitle!!.isEnabled = true
                ToastUtil.show(mContext!!, it.message)
                if (it.code == 3001) {
                    //没有权限
                    EventBusUtils.sendEvent(EventBusEvent(
                            EventBusAction.Event_ATTENDANCE_PERMISSION_LEAVE, ""))
                    val dialog = MyDialog(mContext!!, 280, 140,
                            "您的权限状态异常，无法完成操作",
                            needBtnConfirm = true, needBtnCancel = false, bgResourceId = 0)
                    dialog.setBtnLeftText("好的")
                    dialog.setCanceledOnTouchOutside(false)
                    dialog.setCancelable(false)
                    dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
                        override fun clickLeftBtn() {
                            finish()
                        }

                    })
                    dialog.show()
                }
            })
        }
    }

    /**
     * 处理进入其他设置activity
     */

    private fun enterDeptSelectActivity() {
        val mintent = Intent(mContext!!, TimeDeptSettingActivity::class.java)
        mintent.putExtra("companyId", companyId)
        mintent.putExtra("groupId", ateId)
        if (mChoiceDepartmentTimeIds != null && mChoiceDepartmentTimeIds!!.isNotEmpty()) {
            mintent.putExtra("editDepts", mChoiceDepartmentTimeIds)
        }
        startActivityForResult(mintent, ATTENDANCE_DEP)
    }

    private fun enterPersonSelectActivity() {
        val intent = Intent(mContext!!, TimeMemberSetingActivity::class.java)
        intent.putExtra("companyId", companyId)
        intent.putExtra("groupId", ateId)
        intent.putExtra("needLoad", true)
        if (!mChoiceFreeTimeUsers.isNullOrEmpty()) {
            ObjectStore.add("editUsers" , mChoiceFreeTimeUsers)

//            intent.putExtra("editUsers", mChoiceFreeTimeUsers)
        }
        startActivityForResult(intent, ATTENDANCE_PERSON)
    }

    private fun enterWifiSelectActivity() {
        val intent = Intent(mContext!!, AttendanceWifiActivity::class.java)
        intent.putExtra(ConsKeys.KEY_INTENT_DATA, wifiBeanList)
        startActivityForResult(intent, ATTENDANCE_WIFI)
    }

    /**
     * 处理结果返回
     */

    private fun dealDeptActivityReturn(data: Intent) {
        deptIsNull = false
        if (StringUtils.isNotBlankAndEmpty(data.getStringExtra("deptIsNull")) &&
                data.getStringExtra("deptIsNull") == "true") {
            deptIsNull = true
        }
        mChoiceDepartmentTimeIds = data.getSerializableExtra(
                "choiceDepartment") as ArrayList<AttendanceDepBean>?


        Timber.i("dealDeptActivityReturn ===> 返回的部门ids ${mChoiceDepartmentTimeIds.toString()}")

        if (mChoiceDepartmentTimeIds != null && mChoiceDepartmentTimeIds!!.isNotEmpty()) {
            depList = mChoiceDepartmentTimeIds!!
        } else {
            depList.clear()
        }

//        depText.text = "${depList.size}个"
    }

    @SuppressLint("SetTextI18n")
    private fun dealPersonActivityReturn(data: Intent) {
//        if (data.getStringExtra("memberIsNull") != null &&
//                data.getStringExtra("memberIsNull") == "true") {
//            memberIsNull = true
//        }
        mChoiceFreeTimeUsers = data.getStringArrayListExtra("choiceUsers") as ArrayList<String>
        personList.clear()
        if (!mChoiceFreeTimeUsers.isNullOrEmpty()) {
            personList.addAll(mChoiceFreeTimeUsers.map { AttendanceMemberBean(companyId, it) }.toList())
        } else {
            binding.personText.text = "0人"
        }
        binding.personText.text = "${personList.size}人"
    }

    @SuppressLint("SetTextI18n")
    private fun dealWifiActivityReturn(data: Intent) {
        wifiBeanList = data.getSerializableExtra(ConsKeys.RESULT_DATA) as ArrayList<WifiBean>
        if (wifiBeanList.isNotEmpty()) {
            binding.wifiText.text = "已设置${wifiBeanList.size}个"
        } else {
            binding.wifiText.text = "未设置"
        }
    }

    private fun enterSpecialDateActivity() {
        val intent = Intent(mContext!!, AttendanceSpecialDateActivity::class.java)
        intent.putExtra(ConsKeys.MODEL_ID, ateId)
        intent.putExtra(ConsKeys.COMPANY_ID, companyId)
        intent.putExtra(ConsKeys.KEY_INTENT_DATA, specialMap)
        startActivityForResult(intent, ATTENDANCE_SPECIAL_DATE)
    }

    @SuppressLint("SetTextI18n")
    private fun dealSpecialActivityReturn(data: Intent) {
        specialMap.clear()
        ateHolidaySelves.clear()
        if (data.getSerializableExtra(ConsKeys.KEY_INTENT_DATA) != null) {
            specialMap = data.getSerializableExtra(ConsKeys.KEY_INTENT_DATA) as HashMap<String, AteHolidaySelf>
        }
        if (specialMap.isNotEmpty()) {
            if (ateHolidaySelves.isEmpty()) {
                specialMap.forEach {
                    ateHolidaySelves.add(it.value)
                }
            } else {
                val sortMap = hashMapOf<String, AteHolidaySelf>()
                ateHolidaySelves.forEach {
                    sortMap[it.ateDate] = it
                }
                specialMap.forEach {
                    sortMap[it.key] = it.value
                }
                ateHolidaySelves.clear()
                sortMap.forEach {
                    ateHolidaySelves.add(it.value)
                }
            }
        }
        binding.specialDateText.text = if (ateHolidaySelves.isNotEmpty()) {
            "已调整${ateHolidaySelves.size}天，保存后生效"
        } else {
            ""
        }
    }

    override fun onDestroy() {
        ownPresenter.destory()
        super.onDestroy()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun getUsers(bean: TimeUserIdsSender) {
        Timber.i("getUsers =======> ${bean.toString()}")

        if (bean.userIds != null && bean.userIds.isNotEmpty()) {
            val intent1 = Intent()
            intent1.putExtra("choiceUsers", bean.userIds)
            intent1.putExtra("memberIsNull", bean.memberIsNull)//不传boolean防止与子集页面冲突
            dealPersonActivityReturn(intent1)
        } else if (bean.ids != null && bean.ids.isNotEmpty()) {
            val intent1 = Intent()
            intent1.putExtra("deptIsNull", "false")
            intent1.putExtra("choiceDepartment", bean.ids)
            dealDeptActivityReturn(intent1)

            binding.depText.text = "${bean.ids.size}个"
        }
    }

    @SuppressLint("SetTextI18n")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && data != null) {
            when (requestCode) {
                ATTENDANCE_LOCATION -> {
                    binding.locationListRev.visibility = View.VISIBLE
                    addLocation(data)
                }
                ATTENDANCE__DATE -> {
                    dealDateActivityReturn(data)
                }
                ATTENDANCE_CLOCK -> {
                    dealTimeActivityReturn(data)
                }
                ATTENDANCE_SPECIAL_DATE -> {
                    dealSpecialActivityReturn(data)
                }
                ATTENDANCE_WIFI -> {
                    dealWifiActivityReturn(data)
                }
                ATTENDANCE_PERSON -> {
                    dealPersonActivityReturn(data)
                }
                ATTENDANCE_DEP -> {
                    dealDeptActivityReturn(data)
                }
            }
        }

        if (resultCode == AttendApproverActivityResultCode){
            (ObjectStore.remove(AttendApproverActivityResultKey) as? List<OutClockApproverItemData>)?.let {
                Log.i("moon" , "AttendApproverActivityResultKey >>> ${it}")

                val tempList = it.map {
                    AttendApprover(
                        it.departmentId ?:"" , it.department, it.approverMemerId ?:"", it.approverMemer
                    )
                }

                if (tempList.isNotEmpty()){

                    mTempList = tempList.filter { StringUtils.isNotBlankAndEmpty(it.deptId) }

                    attendanceBean?.outClockReviewInfoList?.clear()
                    attendanceBean?.outClockReviewInfoList?.addAll(tempList.filter { StringUtils.isNotBlankAndEmpty(it.deptId) })

                    approverList.clear()
                    approverList.addAll(tempList.filter { StringUtils.isNotBlankAndEmpty(it.deptId) }.map {
                        AttendApproverReq(it.deptId , it.userId)
                    })

                    createDpeIdRequestBodyFromNewResult(tempList)
                }

            }
        }
    }

    private var mTempList: List<AttendApprover> = arrayListOf()

//    修改/新增考勤组的时候需要组织 depid array 的数据
    private fun createDpeIdRequestBodyFromNewResult(tempList: List<AttendApprover>) {
        approverList.clear()
        approverList.addAll(tempList.filter { StringUtils.isNotBlankAndEmpty(it.deptId) }.map {
            AttendApproverReq(it.deptId , it.userId)
        })
    }
}