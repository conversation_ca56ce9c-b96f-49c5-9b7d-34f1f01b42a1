package com.jounutech.work.view.fragment

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.net.wifi.WifiManager
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import autoLocation
import bd09ToWgs84
import calculateDistanceAndroidSDK
import com.alibaba.android.arouter.launcher.ARouter
//import com.baidu.mapapi.SDKInitializer
//import com.baidu.mapapi.model.LatLng
//import com.baidu.mapapi.utils.DistanceUtil
import com.ddbes.library.im.imfile.tcpfileutil.DealFileUtil
import com.ddbes.library.im.imtcp.Logger
import com.ddbes.library.im.imtcp.imservice.uploadfilehelper.UpLoadFileUtil
import com.haibin.calendarview.Calendar
import com.joinutech.common.base.isAttendance
import com.joinutech.common.base.isFuture
import com.joinutech.common.base.version_mode
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.LocationSupport
import com.joinutech.common.util.LocationUtils
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.base.BaseBindingFragment
import com.joinutech.ddbeslibrary.base.BaseFragment
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.base.customlivedata.SuperLiveDataManager
import com.joinutech.ddbeslibrary.bean.AttenHomeBean
import com.joinutech.ddbeslibrary.bean.RecordBean
import com.joinutech.ddbeslibrary.db.data.AttendHistoryData
import com.joinutech.ddbeslibrary.db.ope.AttendHistoryOpe
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.service.LocationCallback
import com.joinutech.ddbeslibrary.service.LocationResult
import com.joinutech.ddbeslibrary.service.LocationService
import com.joinutech.ddbeslibrary.service.WifiService
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.MMKVKEY.IMEI
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.CircleImageView
import com.jounutech.work.R
import com.jounutech.work.constract.AttendanceConstract
import com.jounutech.work.constract.RouteAte
import com.jounutech.work.databinding.FragmentAttenBinding
import com.jounutech.work.inject.DaggerAttendanceComponent
import com.jounutech.work.module.AttendViewModel
import com.jounutech.work.util.*
import com.jounutech.work.util.AttendanceUtil2.NON_ADDR
import com.jounutech.work.util.AttendanceUtil2.NON_WIFI
import com.jounutech.work.view.attend.record.AttendanceCameraActivity
import com.jounutech.work.view.attend.record.AttendanceHomeActivity
import com.jounutech.work.view.calendar.CalendarTopView
import com.jounutech.work.view.calendar.CalendarTopViewListener
import com.jounutech.work.view.calendar.CheckViewTopListener
import com.jounutech.work.view.calendar.ViewHelper
import com.otaliastudios.cameraview.*
import com.otaliastudios.cameraview.controls.Facing
import com.otaliastudios.cameraview.filter.Filters
import gcj02ToBd09
import gcj02ToWgs84
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import wgs84ToBd09
import wgs84ToGcj02
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Named

/**
 *<AUTHOR>
 *@date 2019/3/20
 * 考勤首页
 */
//点击考勤，考勤首页的fragment
//考勤模块，考勤打卡页真正的页面fragment
class AttendFragment : BaseBindingFragment<FragmentAttenBinding>() {

    private var isInitLocation: Boolean = false
    private var isStart: Boolean = false //是否启动定位
    var isSmallCameraInited=false
    override fun inflateViewBinding(
        layoutInflater: LayoutInflater,
        container: ViewGroup?,
    ): FragmentAttenBinding {
        return FragmentAttenBinding.inflate(layoutInflater,container,false)
    }

    override val layoutRes: Int = R.layout.fragment_atten

    override fun openEventBus() = false

    @Inject
    @field:Named(AttendanceUtil2.ATTENDANCE_PRESENTER)
    lateinit var presenter: AttendanceConstract.AttendancePresenter
    var mListener: RefreshAttendInfo? = null

    /**考勤打卡部分显示*/
    lateinit var attendView: AttendNewView
    lateinit var calendarView: CalendarTopView

    /**定位回调*/
    private var locateListener: MyLocationListener = MyLocationListener()

    //wifi
    private var mWifiManager: WifiManager? = null
    private var mCurrentBSSID: String? = null
    private var mCurrentSSID: String? = null

    //    private lateinit var lay_date_atten: View
//    lateinit var tv_date: TextView

    lateinit var tvDeptName: TextView
    var attenBean: AttenHomeBean? = null
    private var companyId: String = ""

    /**考勤提醒、缺卡时间，如果存在，则加载指定日期的考勤数据*/
    private var specialTime = 0L
    private var ateId: String = ""

    /**当天日期信息*/
    private var today: String = ""

    /**当前日期 yyyy-MM-dd*/
    private var currentDay: String = ""
    private var currentMonth: String = ""
    private lateinit var tvAttendName: TextView
    private lateinit var tvAttendRule: TextView

    private lateinit var tvUserName: TextView
    private lateinit var ivUserIcon: ImageView

    var listener: AttendStatisticsListener? = null

    override fun initView(rootView: View) {

        DaggerAttendanceComponent.builder().build().inject(this)
        val argument=arguments
        if (argument != null) {
            if (argument.containsKey(ConsKeys.COMPANY_ID)
                    && StringUtils.isNotBlankAndEmpty(argument.getString(ConsKeys.COMPANY_ID))) {
                companyId = argument[ConsKeys.COMPANY_ID] as String
            }
            if (argument.containsKey(ConsKeys.KEY_INTENT_DATA)) {
                specialTime = argument.getLong(ConsKeys.KEY_INTENT_DATA, 0L)
            }
        }
        tvUserName = rootView.findViewById<TextView>(R.id.tv_user_name)
        ivUserIcon = rootView.findViewById<CircleImageView>(R.id.iv_avatar)

        tvDeptName = rootView.findViewById(R.id.tv_user_dept)
        tvAttendName = rootView.findViewById(R.id.tv_user_attend_name)
        tvAttendRule = rootView.findViewById(R.id.tv_user_attend_rule)
        tvAttendRule.setOnClickListener(this)

        //考勤模块，打卡按钮在这个组件里边，与fragment的交互主要依靠下边的几行代码
        attendView = AttendNewView(this)
        attendView.initView(activity as MyUseBindingActivity<*>, rootView)
        initCalendarView(rootView)
        setAttendListener(attendView)
        attendView.attendListener = attendListener

        //定位结果
        SuperLiveDataManager.getLocationResult.observeOneOff(viewLifecycleOwner){
            if(it.isNullOrEmpty()){
                refreshLocation(NON_ADDR[0], -2)
            }else{
                val poiList = it.split(",")
                val lat = poiList[0].toDouble()
                val lng = poiList[1].toDouble()
//                val gcj02 = wgs84ToGcj02(lng,lat)
//                Log.d("test","====考勤接收到位置信息====$it===转火星==${gcj02.lat}===${gcj02.lon}===")
                val address = poiList[2]
                judgeLocationRange(LocationResult(lat = lat,lng= lng,address = address))
            }

        }
    }

    override fun initActivityCreated(savedInstanceState: Bundle?) {
        super.initActivityCreated(savedInstanceState)
        //初始化窗口相机，====================================================窗口相机位置1
       /* if (isSmallCameraInited==false) {
            initCamera()
            isSmallCameraInited=true
        }*/
        Loggerr.i("窗口相机相关","==initActivityCreated==执行了====")
    }
    //======================用来测试窗口相机的使用=========开始================窗口相机位置2

    private val allFilters = Filters.values()
    private var currentFilter = 0
    //使用时需要鉴权，要参考D:\GongZuoZiLiao\...\CameraView-main项目的CameraActivity页面；更详细的布局配置查看github
    //说明，初始化只能执行一次，不然会内存泄漏
    private fun initCamera(){
        CameraLogger.setLogLevel(CameraLogger.LEVEL_VERBOSE)

//        camera?.setLifecycleOwner(this)//不让窗口相机按照生命周期自动开关了，改为在对应位置手动开关
        binding.attendUserLayout.camera.addCameraListener(object: CameraListener(){
            override fun onCameraOpened(options: CameraOptions) {
                super.onCameraOpened(options)
                Loggerr.i("窗口相机","==相机打开了====")
            }

            override fun onCameraClosed() {
                super.onCameraClosed()
                Loggerr.i("窗口相机","==相机关闭了====")
            }

            override fun onCameraError(exception: CameraException) {
                super.onCameraError(exception)
            }

            override fun onPictureTaken(result: PictureResult) {
                super.onPictureTaken(result)
                Loggerr.i("窗口相机结果","==result.data.size==${result.data.size}====")
                Loggerr.i("窗口相机结果","==result.facing==${result.facing.name}====")
                Loggerr.i("窗口相机结果","==result.name==${result.format.name}====")
                Loggerr.i("窗口相机结果","==result.isSnapshot==${result.isSnapshot}====")
                Loggerr.i("窗口相机结果","==result.size==${result.size}====")
                result.toBitmap {
                    val newPath= "${System.currentTimeMillis()}" + "." + result.format.name

                    Loggerr.i("窗口相机结果","==newPath==${newPath}====")
                    val file= DealFileUtil.saveBitmap(mActivity,it!!,newPath)
                    Loggerr.i("窗口相机结果","==file.absolutePath==${file?.absolutePath}====")
                }
            }


        })

       /* camera_picture.setOnClickListener {
            camera.takePicture()
        }*/
        binding.attendUserLayout.cameraShotTv.setOnClickListener {
            binding.attendUserLayout.camera.takePictureSnapshot()
        }
        binding.attendUserLayout.cameraCloseTv.setOnClickListener {
            binding.attendUserLayout.camera.close()
        }
       /* camera_open.setOnClickListener {
            //首先获取权限，再调用下面的代码
            camera.open()
        }*/
      /*  camera_change.setOnClickListener {
            camera.toggleFacing()
        }*/
     /*   camera_change_filter.setOnClickListener {
            //只是用来改变照片效果的
            if (camera.preview != Preview.GL_SURFACE) return@setOnClickListener run {

            }
            if (currentFilter < allFilters.size - 1) {
                currentFilter++
            } else {
                currentFilter = 0
            }
            val filter = allFilters[currentFilter]

            // Normal behavior:
            camera.filter = filter.newInstance()
        }*/

        binding.attendUserLayout.camera.facing= Facing.FRONT
        val isface=binding.attendUserLayout.camera.facing.name

        Logger.i("窗口相机","==isface==${isface}====")
        if (isface.equals("back",true)) {
            binding.attendUserLayout.camera.toggleFacing()
        }

    }
    //======================用来测试窗口相机的使用=========结束===========

    private val checkLister = object : CheckViewTopListener {
        override fun isScrollToTop(parent: ViewGroup): Boolean {
            // 0 是 日历组件，所以自己的控件从1开始计算
            if (parent.childCount > 1) {
                val childTwo: View? = parent.getChildAt(2)
                if (childTwo != null && childTwo is RecyclerView) {
                    return isRecyclerViewTop(childTwo)
                }
            }
            return true
        }
    }

    private fun initCalendarView(rootView: View) {
        calendarView = CalendarTopView(mActivity, object : CalendarTopViewListener {
            override fun onInterceptor(calendar: Calendar?, onClick: Boolean): Boolean {
                return if (onClick) {
                    ToastUtil.show(mActivity, "当前日期未考勤")
                    false
                } else {
                    calendar?.let { ViewHelper.isAfterToday(calendar) } ?: false
                }
            }

            override fun onDateSelected(year: Int, month: Int, day: Int) {
                val newDate = getDateString(year, month, day)
                showLog("日历选中后回调 $newDate , $year-$month-$day")
                if (year == 0 || month == 0) return
                if (currentDay != newDate) {//当前记录日期，选中日期不匹配时刷新考勤信息
                    showLog("new date select $newDate")
                    showLog("---> 获取考勤数据 1")
                    loadData(newDate)// 日期选中时查询考勤数据 showCalendarDate() 会调用到这个位置
                }
            }

            override fun onMonthChanged(year: Int, month: Int, day: Int) {
                val monthInfo = getDateString(year, month)
                showLog("当前月份为：$monthInfo")
                if (currentMonth != monthInfo) {
                    setPageTitle(monthInfo)
                    loadMonthData(monthInfo)
                }
            }

            override fun onInitFinished() {

            }
        })
        calendarView.initCalendarView(rootView, checkLister)
    }

    fun getDateString(year: Int, month: Int, day: Int = 0): String {
        if (year == 0 || month == 0) return ""
        var str = if (month < 10) "$year-0$month" else "$year-$month"
        if (day > 0) {
            str = str.plus(if (day < 10) "-0$day" else "-$day")
        }
        return str
    }

    private fun setPageTitle(title: String) {
        if (isVisible){
            listener?.showTitle(title)
        }
    }

    lateinit var viewModel: AttendViewModel

    override fun initLogic() {
        super.initLogic()
        if (UserHolder.isLogin()) {
            tvUserName.text = UserHolder.getCurrentUser()!!.name
            val userAvatar = UserHolder.getCurrentUser()!!.avatar
            if (StringUtils.isNotBlankAndEmpty(userAvatar)) {
                ImageLoaderUtils.loadImage(requireActivity(), ivUserIcon, userAvatar)
            }
            if (companyId.isNullOrBlank()) {
                companyId = CompanyHolder.getCurrentOrg()?.companyId ?: ""
            }

            initViewModel()
            if (specialTime > 0L) {
                val calendar = java.util.Calendar.getInstance()
                calendar.timeInMillis = specialTime
                val year = calendar.get(java.util.Calendar.YEAR)
                val month = calendar.get(java.util.Calendar.MONTH) + 1
                val day = calendar.get(java.util.Calendar.DAY_OF_MONTH)
                val currentMonth = getDateString(year, month)
                showLog("通知跳转传递指定日期为：$specialTime , $year-$month-$day $currentMonth")
                currentDay = getDateString(year, month, day)
                setPageTitle(currentMonth)
                loadMonthData(currentMonth)
            } else {
                today = dateFormat.format(Date())
                showLog("today is $today")
                val currentMonth = SimpleDateFormat(TIME_FORMAT_PATTERN5, Locale.SIMPLIFIED_CHINESE).format(Date())//记录当前月份信息
                setPageTitle(currentMonth)//设置标题
            }
            showLog("初始化页面逻辑时，加载数据")
            refreshChild()
        }
    }

    private fun initViewModel() {
        viewModel = getModel(AttendViewModel::class.java)

        /*考勤打卡信息*/
        viewModel.getAttendRecordResult.observe(this, Observer { result ->
            Loggerr.i("test","========getAttendRecordResult==========")
            mActivity.hideLoading()
            RequestHelper.onResponse(result,
                    onSuccess = { data ->
                        val bean = ParseJsonData.parseJsonAny<AttenHomeBean>(data)

                        val showCheckTab = bean.reviewAuth  // 是否显示审核 tab todo
                        if (showCheckTab){
                            (requireActivity() as? AttendanceHomeActivity)?.showCheckTab()
                        }

                        (activity as MyUseBindingActivity<*>).dismissDialog()
                        if (bean.groupStatus == 0) {
                            tvAttendRule.visibility = View.GONE
                        } else {
                            tvAttendRule.visibility = View.VISIBLE
                        }

                        tvDeptName.text = bean.deptName
                        tvAttendName.text = bean.groupName

                        currentDay = bean.clockDate// 请求成功后更新日期为记录中日期，避免前后端日期显示格式不一致
                        showCalendarDate(currentDay)
                        attenBean = bean
                        ateId = bean.ateId// 考勤id获取

                        if (bean.status == 0 && bean.recordList.size == 1
                                && bean.recordList[0].clockStatus == 0 && currentDay != "") {//当前日期未记录时，提示休息日不考勤
                            showLog("休息日:没有打卡记录")
                        }
                        //考勤模块，将考勤数据传递到这个组件中更新界面
                        attendView.showAttendList(bean, companyId)
                        // 位置和wifi都不为空时初始化定位服务
                        if (!bean.scopeList.isNullOrEmpty() || !bean.wifiList.isNullOrEmpty()) {
                            val needLocation = bean.recordList.find { it.clockStatus == 0 || it.clockStatus == 2 }
                            if (needLocation != null && !isInitLocation) {
                                // 当前可以打卡或者可以更新打卡时，初始化定位服务
                                isInitLocation = true
                                initLocationService()
                            }
                        }

                        // 显示底部栏
                        if (activity is AttendanceHomeActivity) {
                            (activity as AttendanceHomeActivity).showBottomBar(bean.groupStatus == 1)
                        }

                        (requireActivity() as? AttendanceHomeActivity)?.initUnCheckTips()
                    },
                    onError = { code, msg ->
                        // 隐藏底部栏
                        (activity as MyUseBindingActivity<*>).dismissDialog()
                        if (activity is AttendanceHomeActivity) {
                            (activity as AttendanceHomeActivity).showBottomBar(false)
                        }

                        tvAttendRule.visibility = View.GONE
                        tvDeptName.visibility = View.INVISIBLE

                        if (code == 1802) {
                            showToast(msg)
                            tvAttendName.text = msg
                            attendView.onNoData("暂无考勤组", com.joinutech.ddbeslibrary.R.drawable.ic_empty_attend) {
                                (activity as MyUseBindingActivity<*>)
                                        .getLoadingDialog("正在获取考勤信息", true)
                                showLog("---> 获取考勤数据 2")
                                loadData(currentDay)//加载当前日期数据失败后，重试加载
                            }
                        }
                        else if(code == 1809){
                            (requireActivity() as? AttendanceHomeActivity)?.showCheckTab()
                        }
                        else {
                            tvAttendName.text = ""
                            attendView.onNoData("获取考勤信息失败，点击重试", com.joinutech.ddbeslibrary.R.drawable.empty_icon_ex) {
                                (activity as MyUseBindingActivity<*>)
                                        .getLoadingDialog("正在获取考勤信息", true)
                                showLog("---> 获取考勤数据 3")
                                loadData(currentDay)//加载当前日期数据失败后，重试加载
                            }
                        }
                    },
                    onDefault = { msg ->
                        // 隐藏底部栏
                        (activity as MyUseBindingActivity<*>).dismissDialog()
                        if (activity is AttendanceHomeActivity) {
                            (activity as AttendanceHomeActivity).showBottomBar(false)
                        }
                        tvAttendRule.visibility = View.GONE
                        tvAttendName.text = ""
                        attendView.onNoData("暂无考勤组", com.joinutech.ddbeslibrary.R.drawable.ic_empty_attend) {}
                        showToast(msg)
                    })

        })

        /*考勤日历数据*/
        viewModel.getAttendHistoryResult.observe(this, Observer { result ->
            RequestHelper.onResponse(result,
                    onSuccess = { data ->
                        calendarView.setCalenderData(AttendanceUtil2.getMap(data))
                    },
                    onError = { code, msg ->
                        showToast(msg)
                    },
                    onDefault = { msg ->
                        showToast(msg)
                    })
        })
    }

    private val mCalendar = java.util.Calendar.getInstance()
    private val dateFormat = SimpleDateFormat(TIME_FORMAT_PATTERN4)

    /**选中日历*/
    private fun showCalendarDate(currentDay: String) {
        showLog("获取到考勤数据后，选中日历 $currentDay")
        mCalendar.time = dateFormat.parse(currentDay)
        val year = mCalendar.get(java.util.Calendar.YEAR)
        val month = mCalendar.get(java.util.Calendar.MONTH) + 1
        val day = mCalendar.get(java.util.Calendar.DAY_OF_MONTH)
        calendarView.setTargetDate(year, month, day)
    }

    /**加载某天的考勤信息*/
    private fun loadData(date: String) {
        currentDay = date//每次加载数据，更新记录当前日期信息

        if (StringUtils.isNotBlankAndEmpty(companyId) && StringUtils.isNotBlankAndEmpty(userId)) {
//            (activity as MyUseBindingActivity<*>).getLoadingDialog("正在获取考勤信息", true)?.show()
            showLoading("正在获取考勤信息", true)
            viewModel.getAttendRecord(bindToLifecycle(), companyId = companyId, date = date, userId = userId!!)
        }
    }

    /**获取考勤日历信息*/
    private fun loadMonthData(month: String) {
        currentMonth = month
        if (StringUtils.isNotBlankAndEmpty(companyId) && StringUtils.isNotBlankAndEmpty(userId)) {
            viewModel.getAttendHistory(bindToLifecycle(), companyId = companyId, findTime = currentMonth, userId = userId!!)
        }
    }

    interface RefreshAttendInfo {
        /**
         * @param state 定位结果状态
         * -3: 获得权限后，用户未开启 GPS
         * -2: 开启权限后，但是未获取到定位信息
         * -1：未开启定位权限
         * 0：开启权限，获取到定位信息，未进入考勤定位范围内，可以外勤打卡
         * 1：进入考勤定位范围内
         */
        fun refreshLocation(name: String, state: Int)

        /**
         * @param state WiFi结果状态
         * -2: 开启权限后，但是未获取到WiFi信息
         * -1：未开启获取WiFi权限
         * 0：开启权限，获取到WiFi信息，未进入WiFi考勤范围内，可以外勤打卡
         * 1：进入WiFi考勤范围内
         */
        fun refreshWifi(name: String, state: Int)
    }

    private fun setAttendListener(listener1: RefreshAttendInfo) {
        mListener = listener1
    }

    //定位相关，将定位结果传递给AttendNewView
    fun refreshLocation(info: String, state: Int) {
        mListener?.refreshLocation(info, state)
    }

    inner class MyLocationListener : LocationCallback {

        override fun getTag(): String = "attend"

        override fun onLocationResult(locationInfo: LocationResult) {
        }

        override fun onPoiLocationResult(result: LocationResult) {
            if (!result.locationSuccess) {
                refreshLocation(NON_ADDR[0], -2)// 定位POI解析失败后默认回调
                return
            }
            judgeLocationRange(result)//定位相关，定位成功
        }
    }

    private fun startLocation() {
        if (LocationSupport().checkGpsOpenState(mActivity)) {
            refreshLocation(NON_ADDR[0], -2)// 定位权限获得后默认回调
            lifecycleScope.launch {
                //持续获取定位  5s一次
                while(true){
                    LocationUtils.getCurrentLocation(requireContext())
                    delay(5000)
                }
            }
        } else {
            refreshLocation(NON_ADDR[0], -3)// 有定位权限，未开启gps
            showGpsConfigDialog()
        }
    }

    //定位相关，启动定位的方法
    @SuppressLint("CheckResult")
    private fun initLocationService() {
//        initLocate()
        if (isStart){
            showLog("初始化定位服务 已开启")
            return
        }
        val perms = arrayOf(Manifest.permission.ACCESS_FINE_LOCATION)
        val preTips = "需要您的地理位置,才能打卡签到和向好友分享您的位置"
        PermissionUtils.requestPermissionFragment(this, perms,
                "获取当前位置需要使用位置权限", {
            startLocation()
            isStart = true
            //  用户获取定位权限后，才可以获取wifi信息
            initWifiService()
        }, {
            isInitLocation = false
            refreshLocation(NON_ADDR[1], -1)// 未获取定位权限默认回调
        }, true , preTips = preTips)
    }

    private var locationDialog: AlertDialog? = null
    private fun showAppConfigDialog() {
        if (locationDialog != null) {
            if (locationDialog?.isShowing != true) {
                locationDialog?.show()
            }
        } else {
            //没有打开则弹出对话框
            locationDialog = AlertDialog.Builder(mActivity)
                    .setTitle("提示")
                    .setMessage("考勤打卡需要打卡定位权限。") // 拒绝, 退出应用
                    .setNegativeButton("拒绝"
                    ) { _, _ ->
//                    mActivity.finish()
                    }
                    .setPositiveButton("打开"
                    ) { _, _ -> //跳转GPS设置界面
                        startAppLocationSettings()
                    }
                    .setCancelable(false)
                    .show()
        }
    }

    var gpsDialog: AlertDialog? = null
    private fun showGpsConfigDialog() {
        //没有打开则弹出对话框
        if (gpsDialog != null) {
            if (gpsDialog?.isShowing != true) {
                gpsDialog?.show()
            }
        } else {
            gpsDialog = AlertDialog.Builder(mActivity)
                    .setTitle("提示")
                    .setMessage("考勤打卡需要打卡定位功能。") // 拒绝, 退出应用
                    .setNegativeButton("拒绝"
                    ) { _, _ ->
//                    mActivity.finish()
                    }
                    .setPositiveButton("打开"
                    ) { _, _ -> //跳转GPS设置界面
                        openGpsSetting()
                    }
                    .setCancelable(false)
                    .show()
        }
    }

    private var isPaused = false

    /**
     * 启动应用的设置
     *
     * @since 2.5.0
     */
    fun startAppLocationSettings() {
        isPaused = true
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
//        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.data = Uri.parse("package:" + mActivity.packageName)
        currentRequest = openAppConfigRequest
        startActivityForResult(intent, openAppConfigRequest)
    }

    fun startAppWifiSettings() {
        isPaused = true
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
//        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.data = Uri.parse("package:" + mActivity.packageName)
        currentRequest = openAppWifiConfigRequest
        startActivityForResult(intent, openAppWifiConfigRequest)
    }

    fun openGpsSetting() {
        isPaused = true
        val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
        currentRequest = openGpsRequest
        startActivityForResult(intent, openGpsRequest)
    }

    fun openWifiSetting() {
        isPaused = true
        val intent = Intent(Settings.ACTION_WIFI_SETTINGS)
        currentRequest = openWifiRequest
        startActivityForResult(intent, openWifiRequest)
    }

    //考勤模块，接收拍摄后的照片
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
//        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode==openAppWifiConfigRequest||
                    requestCode==openWifiRequest||
                    requestCode==openAppConfigRequest||
                    requestCode==openGpsRequest
        ) {
            checkConfig(currentRequest)
            currentRequest = 0
        }

        //接收拍摄后的照片,相机返回
        if (resultCode == Activity.RESULT_OK) {
            when(requestCode){
                10030->{
                    mActivity.showLoading("正在上传", false)
                    Logger.i("当前线程", "===拍摄返回到的地方=${Thread.currentThread().name}==")
                    val imageLocalPath=data?.getStringExtra("imagePath")
                    //上传照片然后调打卡接口
                    if (StringUtils.isNotBlankAndEmpty(imageLocalPath)) {
                       UploadFileUtil.upLoadSingleFile(mActivity,bindToLifecycle(),bindToLifecycle(),
                       File(imageLocalPath),
                       onSuccess = {
                           //上传照片成功
                           Logger.i("当前线程", "===照片上传成功后回调的线程=${Thread.currentThread().name}==")
                           mActivity.hideLoading()
                           resultSupportDate.pictureFileId=it.fileId?:""
                           punchEx()
                       },
                       onFailure = {
                               ToastUtil.show(mActivity,"照片上传失败")
                           mActivity.hideLoading()
                       },
                       onProgress = {compete,target->})
                    }

                }
            }
        }

    }

    private fun checkConfig(requestCode: Int) {
        when (requestCode) {
            openAppWifiConfigRequest,
            openWifiRequest,
            openAppConfigRequest,
            openGpsRequest -> {
                refreshChild()
            }
        }
    }

    /**Can only use lower 16 bits for requestCode*/
    private val openAppConfigRequest = 10011
    private val openGpsRequest = 10012
    private val openAppWifiConfigRequest = 10021
    private val openWifiRequest = 10022
    private var currentRequest = 0

    private var locationService: LocationService? = null

    private fun initLocate() {
//        if (locationService == null) {
//            locationService = (requireActivity().application as BaseApplication).locationService
//        }
//        locationService?.initLocation()
//        if (!locationService!!.isRegister(locateListener)) {
//            showLog("2、初始化定位服务 locationService 未注册")
//            locationService!!.registerListener(locateListener)
//            locationService?.apply {
//                val option = getOption()
//                option.setIsNeedLocationPoiList(true)
//                setLocationOption(option)
//            }
//        } else {
//            showLog("2、初始化定位服务 locationService 已注册")
//        }
    }

    //判断是否在考勤范围内并刷新ui
    fun judgeLocationRange(location: LocationResult) {
//        showLog("current position lat = ${location.getLat()} ,lng = ${location.getLng()}")
//        locationService?.apply {
//        }
        var address = location.address
        var state = 0

        if (attenBean != null && !attenBean!!.scopeList.isNullOrEmpty()) {
            for (scope in attenBean!!.scopeList) {
                val wgs = bd09ToWgs84(scope.lat, scope.lng)
                Log.d("test","===百度原始坐标======lat===${scope.lat}===lng==${scope.lng}===转换wgs84===${wgs.lat}===${wgs.lon}===")

                Log.d("test","========考勤范围==${attenBean!!.scope}===计算距离==${calculateDistanceAndroidSDK(wgs.lat, wgs.lon,location.lat, location.lng)}")
//                Log.d("test","========考勤范围==${attenBean!!.scope}===计算距离==${DistanceUtil.getDistance(LatLng(scope.lat, scope.lng), LatLng(bd09.lat, bd09.lon))}")
                if (calculateDistanceAndroidSDK(wgs.lat, wgs.lon, location.lat, location.lng) <= attenBean!!.scope) {
//                if (DistanceUtil.getDistance(LatLng(scope.lat, scope.lng), LatLng(bd09.lat, bd09.lon))<= attenBean!!.scope) {
                    address = scope.location
                    state = 1
                    break
                }
            }
        }
        if (state == 1) {
            refreshLocation(address, state)// 定位结果获得后默认回调
        } else {
            if (StringUtils.isNotBlankAndEmpty(address)) {
                refreshLocation(address, state)// 定位结果获得后默认回调
            } else {
                refreshLocation(NON_ADDR[0], -2)// 定位结果获得后默认回调
            }
        }
    }

    @SuppressLint("CheckResult")
    private fun initWifiService() {
        showLog("4、开启地图定位图层 如果设置了考勤wifi 则检查wifi获取权限")
        if (attenBean != null && attenBean!!.wifiList.isNotEmpty()) {
            val perms = arrayOf(Manifest.permission.ACCESS_WIFI_STATE)
            val preTips = "需要您同意使用则检查wifi获取权限， 才能继续考勤打卡"
            PermissionUtils.requestPermissionFragment(mFragment, perms,
                    "获取当前WIFI需要WIFI权限", {
                showLog("4、wifi权限 已获取")
                getCurrentWifiInfo()
            }, {
                showLog("4、wifi权限 未获取")
                mListener?.refreshWifi(NON_WIFI[0], -1)
            } , preTips = preTips)
        }
    }

    override fun onResume() {
        super.onResume()
        Loggerr.i("窗口相机相关","====onResume执行了====")
//        camera.open()//打开相机=======================需要鉴权判断==========================窗口相机位置3
        if (isPaused) {
            isPaused = false
//            showLog("6、页面恢复，打开gps 或者开启定位权限，跳转其他页面等")
//            refreshChild()
        }
    }

    override fun onPause() {
        isPaused = true
        Loggerr.i("窗口相机相关","==fragment的==onPause执行了====")
//        camera.close()//关闭相机===========================需要鉴权判断=========================窗口相机位置4
        super.onPause()
    }

    //获取当前连接的WiFi信息
    @SuppressLint("WifiManagerLeak")
    fun getCurrentWifiInfo() {
        WifiService().registerWifiStateReceiver(mActivity)
        mWifiManager = mActivity.getSystemService(Context.WIFI_SERVICE) as WifiManager
        //当前的WiFi连接信息
        val currentConnectionInfo = mWifiManager!!.connectionInfo
        //当前的WiFi的名称与bssid地址
        mCurrentBSSID = currentConnectionInfo.bssid
        mCurrentSSID = currentConnectionInfo.ssid
        if (StringUtils.isNotBlankAndEmpty(mCurrentSSID) && "<unknown ssid>" != mCurrentSSID
                && StringUtils.isNotBlankAndEmpty(mCurrentBSSID) && mCurrentBSSID != "00:00:00:00:00:00") {
            if (mCurrentSSID!!.contains("\"")) {
                mCurrentSSID = mCurrentSSID!!.substring(1, mCurrentSSID!!.lastIndexOf("\""))
            }
            var state = 0
            for (wifi in attenBean!!.wifiList) {
                if (wifi.wifiId == mCurrentBSSID) {
                    state = 1
                    break
                }
            }
            mListener?.refreshWifi(mCurrentSSID ?: NON_WIFI[1], state)
        } else {
            mListener?.refreshWifi(NON_WIFI[0], -2)
        }
    }

    fun refreshChild() {
        showLog("---> 获取考勤数据 4")
        loadData(currentDay)//刷新考勤信息
    }

    fun refreshLocation() {
        showLog("---> 获取考勤数据 -2 1")
        refreshChild()//点击打卡时，无法打卡时刷新
    }

    fun refreshCurrentAttend() {
        showLog("----------- 切换标签后刷新当前考勤 ++++++++++++++")
        showLog("---> 获取考勤数据 -2 2")
        refreshChild()// 切换标签到考勤后，刷新
    }

    private var resultRecord:RecordBean? =null
    private lateinit var resultSupportDate:SupportData
    private var resultClockId:String =""

    private val attendListener = object : AttendMotionListener {

        override fun onRefresh(clockDate: String) {
            if (today == clockDate) {//自动刷新时，如果当前日期
                showLog("---> 获取考勤数据 5")
                loadData(currentDay)// 刷新时查询考勤数据
            }
        }


        //考勤模块，AttendNewView组件中点击打卡按钮后，会触发这个方法
        //正常打卡和更新打卡都是走这个方法
        override fun onAttend(record: RecordBean, supportData: SupportData) {
            showLog("考勤打卡回调 ")
            resultRecord=record
            resultSupportDate=supportData
             resultClockId = if (!resultSupportDate.isFreeze) {
                 resultRecord?.clockId ?: ""
            } else {
                "1"
            }
            if (resultSupportDate.isUpdate || !resultSupportDate.isWorkDay) {
//                punch(resultRecord!!, resultSupportDate, resultClockId)
                //更新打卡
                if (resultSupportDate.needPicture == 1) {

                    if (resultSupportDate.attendStatus == 1){
                        // 正常打卡
                        openCamera(resultSupportDate.address)
                    }else{
                        AttendanceUtil2.showEarlyDialog(activity!!,
                            resultSupportDate.attendStatus, resultSupportDate.address) { note ->
                            resultSupportDate.note = if (note == null) "" else note
                            //考勤模块，打开相机
                            openCamera(resultSupportDate.address)
                        }
                    }

                }else{

                    if (resultSupportDate.attendStatus == 4 || resultSupportDate.attendStatus == 2 || resultSupportDate.attendStatus == 3){
                        AttendanceUtil2.showEarlyDialog(activity!!,
                            resultSupportDate.attendStatus, resultSupportDate.address) { note ->
                            resultSupportDate.note = if (note == null) "" else note
                            //考勤模块，打开相机
                            punchEx()
                        }
                    }else{
                        punchEx()
                    }
                }
            } else if (resultSupportDate.isNote) {
                //更新原因
                updateAttendNote(resultRecord!!, resultSupportDate)
            } else {
                //正常打卡
                when (resultSupportDate.attendStatus) {
                    1 -> {
                        //正常
                        resultSupportDate.note = ""
                        //考勤模块，打开相机
                        if (resultSupportDate.needPicture == 1) {
                            openCamera(resultSupportDate.address)
                        }else{
                            //                        punch(resultRecord!!, resultSupportDate, resultClockId)
                            punchEx()
                        }
                    }
                    2 -> {
                        //迟到
                        AttendanceUtil2.showLateDialog(activity!!) { note ->
                            resultSupportDate.note = note

                            //考勤模块，打开相机
                            if (resultSupportDate.needPicture == 1) {
                                openCamera(resultSupportDate.address)
                            }else{
                                //                            punch(resultRecord!!, resultSupportDate, resultClockId)
                                punchEx()
                            }
                        }
                    }
                    else -> {
                        //早退或者外勤
                        AttendanceUtil2.showEarlyDialog(activity!!,
                            resultSupportDate.attendStatus, resultSupportDate.address) { note ->
                            resultSupportDate.note = note

                            //考勤模块，打开相机
                            if (resultSupportDate.needPicture == 1) {
                                openCamera(resultSupportDate.address)
                            }else{
                                //                            punch(resultRecord!!, resultSupportDate, resultClockId)
                                punchEx()
                            }
                        }
                    }
                }
            }
        }

    }

    private fun punchEx() {
        punch(resultRecord!!, resultSupportDate, resultClockId)
    }

    private fun openCamera(address: String) {
        //首先获取权限，再调用下面的代码
        val perms = arrayOf(
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )

        val tips = "拍照打卡需要照相机和存储权限"
        PermissionUtils.requestPermissionActivity(requireActivity(), perms,
            hint = "照相机和存储权限",
            onSuccess = {
                val intent=Intent(requireActivity(),AttendanceCameraActivity::class.java)
                intent.putExtra("address",address)
                startActivityForResult(intent,10030)
            },
            onError = {
                toast(requireActivity() , tips)
            })
    }


    /**
     * 考勤打卡数据提交 正常 迟到 早退 外勤信息
     * @param record 考勤打卡记录
     * @param supportData 考勤打卡需要数据
     * @param clockId 考勤记录 id 更新时用到
    //     * @param punchState 考勤状态
    //     * @param clockDate 考勤日期
    //     * @param punchTime 考勤打卡时间
    //     * @param clockId 考勤id，根据考勤组性质判断，自由传 1
    //     * @param punchLocation 考勤打卡位置信息
    //     * @param punchWifi 考勤打卡wifi信息
    //     * @param index 考勤列表位置信息
    //     * @param note 备注信息
     * */
    //考勤模块，点击打卡按钮回调到这个fragment后，调用接口提交打卡数据
    fun punch(record: RecordBean, supportData: SupportData, clockId: String) {
        //打卡
        val perms = arrayOf(Manifest.permission.READ_PHONE_STATE)
        val bean = hashMapOf<String, Any>()
        bean["ateGroupId"] = attenBean!!.ateId
        bean["clockDate"] = supportData.clockDate
        if (!supportData.address.isNullOrBlank()) {
            bean["location"] = supportData.address
        }
        if (!supportData.wifiName.isNullOrBlank()) {
            bean["wifi"] = supportData.wifiName
        }
        bean["status"] = supportData.attendStatus
        bean["userId"] = userId!!
//        bean["id"] = attenBean!!.recordList[index].id
        if (!record.id.isNullOrBlank()) {
            bean["id"] = record.id!!
        }
        //自由考勤组传1
//        if (attenBean!!.ateSchedule == 1) {
//            bean["clockId"] = 1
//        } else {
//            bean["clockId"] = attenBean!!.recordList[index].clockId
//        }
        if (!clockId.isNullOrBlank()) {
            bean["clockId"] = clockId
        }
        bean["type"] = attenBean!!.status
        bean["sortNum"] = supportData.index
        bean["client"] = 2
        if (Build.VERSION.SDK_INT >= 29) run {
            bean["deviceMark"] = mActivity.imei
            punchAttendance(bean, supportData, record)
        } else {
            val tips = "需要您同意获取手机型号权限，才能继续打卡"
            PermissionUtils.requestPermissionFragment(this, perms,
                    "打卡需要获取手机型号权限",
                    onSuccess = {
                        mActivity.imei = DeviceIdUtil.getDeviceId(mActivity)
                        MMKVUtil.saveString(IMEI, mActivity.imei)
                        bean["deviceMark"] = mActivity.imei
                        punchAttendance(bean, supportData, record)
                    },
                    onError = {
                        bean["deviceMark"] = mActivity.imei
                        punchAttendance(bean, supportData, record)
                    }, preTips = tips)
        }
    }

    @SuppressLint("SimpleDateFormat")
    /**
     * note: String,
     *  punchTime: String,
     *  punchState: Int,
     *  punchWifi: String,
     *  punchLocation: String
     *  */
    private fun punchAttendance(bean: HashMap<String, Any>, supportData: SupportData, record: RecordBean) {
        bean["deviceModel"] = ConsValue.deviceModel
        bean["companyId"] = companyId
        if (StringUtils.isNotBlankAndEmpty(supportData.note)) {
            bean["note"] = supportData.note
        }else{
            bean["note"] = supportData.note
        }
        if (supportData.needPicture==1) {
            bean["pictureFileId"]=supportData.pictureFileId
        }
        Logger.i("当前线程", "===真正打卡的地方=${Thread.currentThread().name}==")
        mActivity.showLoading("打卡", false)
//        showDialog?.setCancelable(false)
//        showDialog?.show()
        //考勤模块，真正打卡接口
        presenter.atePunch(token!!, bindToLifecycle(), bean, onSuccess = {
            mActivity.hideLoading()
            AttendanceUtil2.showPunchDialogNew(requireActivity(), supportData.attendStatus, supportData.time,
                    supportData.wifiName, supportData.address, attenBean!!.status == 1,
                    it.sentence, it.author, it.picture)
            val history = AttendHistoryData(
                    userId!!, companyId, supportData.localServerTime,
                    supportData.clockDate, supportData.attendStatus,
                    GsonUtil.toJson(bean), GsonUtil.toJson(record))
            onCacheHistory(history)// 打卡成功
//            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.EVENT_ATTEND_HISTORY, history))
            showLog("---> 获取考勤数据 -2 3")
            refreshChild()//打卡后更新考勤信息
        }, onError = {
            mActivity.run { hideLoading() }
            ToastUtil.show(requireActivity(), it)
            val history = AttendHistoryData(
                    userId!!, companyId, supportData.localServerTime,
                    supportData.clockDate, -1 * supportData.attendStatus,
                    GsonUtil.toJson(bean), GsonUtil.toJson(record))
            onCacheHistory(history)// 打卡失败
        })
    }

    fun updateAttendNote(record: RecordBean, supportData: SupportData) {
        val bean = hashMapOf<String, Any>()
        if (!record.id.isNullOrBlank()) {
            bean["id"] = record.id!!
            bean["note"] = supportData.note
            presenter.updateAttendNote(token!!, bindToLifecycle(), bean,
                    onSuccess = {
                        // 2019/10/9 更新原因打卡完成后弹窗提示
                        ToastUtil.show(mActivity, "更新成功")
                        showLog("---> 获取考勤数据 -2 4")
                        refreshChild() // 更新考勤备注后刷新考勤信息
                        val history = AttendHistoryData(
                                userId!!, companyId, supportData.localServerTime,
                                supportData.clockDate, supportData.attendStatus,
                                GsonUtil.toJson(bean), GsonUtil.toJson(record))
                        onCacheHistory(history)// 更新打卡成功
                    },
                    onError = {
                        ToastUtil.show(requireActivity(), it)
                        val history = AttendHistoryData(
                                userId!!, companyId, supportData.localServerTime,
                                supportData.clockDate, -1 * supportData.attendStatus,
                                GsonUtil.toJson(bean), GsonUtil.toJson(record))
                        onCacheHistory(history)// 更新打卡失败
                    })
        } else {

        }
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            //点击查看考勤规则
            tvAttendRule -> {
                if (StringUtils.isNotBlankAndEmpty(ateId)) {
                    ARouter.getInstance().build(if (isFuture&& isAttendance) {
                        RouteAte.ateOrderRuleActivity
                    } else {
                        RouteAte.attendanceRuleActivity
                    })
                            .withString(ConsKeys.MODEL_ID, ateId)
                            .withString(ConsKeys.COMPANY_ID, companyId)
                            .navigation()
                }
            }
        }
    }

    override fun onStop() {
//        if (locationService != null) {
//            locationService?.apply {
//                unregisterListener(locateListener)
//                stop()
//            }
//        }
        super.onStop()
    }

    /**本地缓存打卡记录*/
    private fun onCacheHistory(data: AttendHistoryData) {
        try {
            data.envName = version_mode.name
            AttendHistoryOpe.getInstance().insert(mActivity, data)
        } catch (e: Exception) {
        }
    }

    override fun onDestroyView() {
        attendView.release()
        super.onDestroyView()
    }
}