package com.jounutech.work.view

import android.content.Intent
import android.graphics.Rect
import android.view.LayoutInflater
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.common.util.CompanyHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.BulleListBean
import com.joinutech.ddbeslibrary.bean.PageInfoBean
import com.joinutech.ddbeslibrary.utils.*
import com.jounutech.work.R
import com.jounutech.work.adapter.BullListAdapter
import com.jounutech.work.constract.AttendanceConstract
import com.jounutech.work.databinding.ActivityBulletinBinding
import com.jounutech.work.inject.DaggerAttendanceComponent
import com.jounutech.work.util.AttendanceUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import javax.inject.Inject
import javax.inject.Named

/**
 *<AUTHOR>
 *@date 2019/3/27
 * 公告列表
 */
class BulletinListActivity : MyUseBindingActivity<ActivityBulletinBinding>(), BullListAdapter.OnItemClickListener {

    override val contentViewResId: Int = R.layout.activity_bulletin
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityBulletinBinding {
        return ActivityBulletinBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true
    override fun openArouterReceive(): Boolean = false

    lateinit var mAdapter: BullListAdapter
    var list = arrayListOf<BulleListBean>()

    @Inject
    @field:Named(AttendanceUtil.ATTENDANCE_PRESENTER)
    lateinit var presenter: AttendanceConstract.AttendancePresenter
    private var page = 1
    private var companyId: String = ""
    private var isRefresh = false

    override fun initImmersion() {
        setPageTitle(R.string.notice_list_title)
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        initRecycleView()
        binding.refreshLayout.setEnableLoadMore(true)
        binding.refreshLayout.setEnableRefresh(true)
        binding.refreshLayout.setRefreshHeader(CustomHeader(mContext))
        DaggerAttendanceComponent.builder().build().inject(this)
        dealOrgPermission()
    }

    private fun dealOrgPermission() {
        val workStation = CompanyHolder.getCurrentOrg() ?: return
        val isHaveAuth = intent.getIntExtra("isHaveAuth" , -1)
        if (isHaveAuth > -1){
            if (isHaveAuth == 1){
                setRightImage(R.drawable.edit_icon, this)
            }
        }else{
            if (workStation.deptId == "0" ||
                workStation.power.contains(ORG_PERMISS_TYPE.ORG_PERMISSION) ||
                ORG_PERMISS_TYPE.checkSuperPermission(workStation.power)) {
                setRightImage(R.drawable.edit_icon, this)
            }
        }
    }

    override fun initLogic() {
        companyId = intent.getStringExtra("orgId") ?:""

        mAdapter = BullListAdapter(this, list)
        mAdapter.setOnItemClickListener(this)
        binding.recyclerBulletin.adapter = mAdapter
        binding.refreshLayout.setOnRefreshListener {
            binding.refreshLayout.setEnableLoadMore(true)
            page = 1
            isRefresh = true
            getListData()
        }
        binding.refreshLayout.setOnLoadMoreListener {
            page++
            getListData()
        }
    }

    override fun onResume() {
        super.onResume()
        if (createNotice) {
            createNotice = false
            getListData()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun refreshPoint(str: String) {
        if (str == EventBusAction.REFRESH_MAIN_NOTICE) {
            getListData()
        }
    }

    private fun getListData() {
        if (!isRefresh) getLoadingDialog("", false)
        presenter.getBulleList(accessToken!!, page, companyId, bindToLifecycle(), {
            dismissDialog()
            isRefresh = false
            if (page == 1) {
                list.clear()
                if (!it.isNullOrEmpty()) {
                    list.addAll(it)
                    if (it.size < 10) {
                        binding.refreshLayout.setEnableLoadMore(false)
                    }
                } else {
                    binding.refreshLayout.finishRefresh()
                    finish()
                }
                mAdapter.setSourceList(list)
                binding.refreshLayout.finishRefresh()
            } else {
                if (!it.isNullOrEmpty()) {
                    list.addAll(it)
                    mAdapter.setSourceList(list)
                    if (it.size < 10) {
                        binding.refreshLayout.setEnableLoadMore(false)
                        ToastUtil.show(mContext!!, "抱歉没有更多数据了")
                    }
                }
            }
        }, {
            dismissDialog()
            isRefresh = false
            binding.refreshLayout.finishRefresh()
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun initRecycleView() {
        binding.recyclerBulletin.layoutManager = LinearLayoutManager(mContext!!,
                LinearLayoutManager.VERTICAL, false)
        val divider = DividerItemDecoration(this,
                DividerItemDecoration.VERTICAL)
        divider.setDrawable(ContextCompat.getDrawable(this,
                com.joinutech.ddbeslibrary.R.drawable.shape_bg_recylview_line)!!)
        binding.recyclerBulletin.addItemDecoration((object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(outRect: Rect, view: View,
                                        parent: RecyclerView, state: RecyclerView.State) {
                outRect.set(0, 0, 0, DeviceUtil.dip2px(mContext, 8f))
            }
        }))
    }

    private var createNotice = true

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            com.joinutech.ddbeslibrary.R.id.iv_right -> {//编辑
                val pageInfo = PageInfoBean()
                pageInfo.title = resources.getString(com.joinutech.ddbeslibrary.R.string.notice_create_title)
                pageInfo.pageType = 0
                pageInfo.companyId = companyId

                createNotice = true


                ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                    .withString("pageInfo", GsonUtil.toJson(pageInfo))
                    .withBoolean("isNoticeWeb", true)
                    .navigation()
            }
            com.joinutech.ddbeslibrary.R.id.empty_view -> {
                getListData()
            }
        }
    }

    override fun onItemClick(position: Int) {
        val pageInfo = PageInfoBean()
        pageInfo.title = resources.getString(com.joinutech.ddbeslibrary.R.string.notice_detail_title)
        pageInfo.noticeId = mAdapter.mData[position].noticeId
        pageInfo.pageType = 1
        pageInfo.companyId = companyId

        if (pageInfo.noticeId.isNullOrBlank() || pageInfo.companyId.isNullOrBlank()) {
            toastShort("暂时无法查看详情")
            return
        }
        ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
            .withString("pageInfo", GsonUtil.toJson(pageInfo))
            .withBoolean("isNoticeWeb", true)
            .navigation()
    }
}