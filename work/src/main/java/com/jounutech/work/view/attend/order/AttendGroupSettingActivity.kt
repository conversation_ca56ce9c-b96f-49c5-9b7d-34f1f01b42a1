package com.jounutech.work.view.attend.order

import TIANDITU_URL
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import cn.addapp.pickers.common.LineConfig
import cn.addapp.pickers.widget.WheelListView
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.common.base.LinkBuilder
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.common.util.ObjectStore
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.AteHolidaySelf
import com.joinutech.ddbeslibrary.bean.AttendanceDepBean
import com.joinutech.ddbeslibrary.bean.AttendanceLocationBean
import com.joinutech.ddbeslibrary.bean.TimeUserIdsSender
import com.joinutech.ddbeslibrary.bean.WifiBean
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.utils.BottomDialogUtil
import com.joinutech.ddbeslibrary.utils.COMMON_WEB_ACTIVITY
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.ConsKeys
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.MyDialog
import com.joinutech.ddbeslibrary.utils.RouteIm
import com.joinutech.ddbeslibrary.utils.RouteProvider
import com.joinutech.ddbeslibrary.utils.ScreenUtils
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.XUtil
import com.joinutech.ddbeslibrary.utils.toastShort
import com.jounutech.work.R
import com.jounutech.work.adapter.AttendanceLocationAdapter
import com.jounutech.work.bean.AteDayConfig
import com.jounutech.work.bean.AteDeptBean
import com.jounutech.work.bean.AteMember
import com.jounutech.work.bean.AteScheduleCycle
import com.jounutech.work.bean.AttendOrderGroupInfo
import com.jounutech.work.bean.AttendOrderListBean
import com.jounutech.work.bean.DutyClockId
import com.jounutech.work.constract.RouteAte
import com.jounutech.work.databinding.ActivityAttendGroupSettingBinding
import com.jounutech.work.util.AttendanceUtil
import com.jounutech.work.view.attend.manage.AttendanceSpecialDateActivity
import com.jounutech.work.view.attend.manage.AttendanceWifiActivity
import com.jounutech.work.view.attend.manage.TimeDeptSettingActivity
import com.jounutech.work.view.attend.manage.TimeMemberSetingActivity
import com.jounutech.work.viewModel.AttendOrderViewModel
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import wgs84ToBd09
import java.util.*
import kotlin.collections.ArrayList

/**
 * @PackageName: com.jounutech.work.view.attend.manage
 * @ClassName: AttendanceGroupDetailActivity
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/12/18 15:06
 * @Desc: //TODO 考勤组详情页面 修改为班次选择替代考勤时间选择 考勤班次 考勤组创建和预览复用
 */
class AttendGroupSettingActivity : MyUseBindingActivity<ActivityAttendGroupSettingBinding>() {

    override val contentViewResId: Int = R.layout.activity_attend_group_setting
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityAttendGroupSettingBinding {
        return ActivityAttendGroupSettingBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = false

    /**创建 或 查看 考勤组时 需要传参*/
    private var companyId: String = ""

    /**创建考勤组时 需要传参*/
    private var attendGroupType: String = ""

    /**查考勤组详情时 需要传参*/
    private var ateId: String = ""

    private var tempDetail = AttendOrderGroupInfo()

    private lateinit var locationAdapter: AttendanceLocationAdapter

    /**
     * -1 为固定考勤 已选择的班次信息
     * 0-6 为一周每天班次配置，
     * 7为法定假日调休自动排休设置
     */
    private val attendWeekDayOrder = hashMapOf<Int, AttendOrderListBean?>()

    /**周期班次数据*/
    private val intervalOrderList = arrayListOf<AttendOrderListBean>()

    /**特殊工作日设置*/
    private var specialMap = hashMapOf<String, AteHolidaySelf>()

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        initIntent()
        setRightTitle("保存", this)
    }

    private fun initIntent() {
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra(ConsKeys.MODEL_TYPE))) {
                attendGroupType = intent.getStringExtra(ConsKeys.MODEL_TYPE)!!
            }
            ateId = intent.getStringExtra(ConsKeys.MODEL_ID) ?: ""
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra(ConsKeys.COMPANY_ID))) {
                companyId = intent.getStringExtra(ConsKeys.COMPANY_ID)!!
                tempDetail.companyId = companyId
            }
        }
    }

    override fun initView() {
        showToolBarLine()
        whiteStatusBarBlackFont()
        binding.locationListRev.layoutManager = LinearLayoutManager(this)
        if (attendGroupType == RouteAte.ATTEND_GROUP_TYPE_FREE) {
            binding.attendanceTimeLayout.visibility = View.GONE
        } else {
            binding.attendanceTimeLayout.visibility = View.VISIBLE
        }
    }

    lateinit var viewModel: AttendOrderViewModel

    override fun initLogic() {
        super.initLogic()
        viewModel = getModel(AttendOrderViewModel::class.java)
        // 创建考勤组
        viewModel.createAttendGroupResult.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result,
                    onSuccess = {
                        if (ateId.isNullOrBlank()) {
                            toastShort("创建考勤组成功")
                        } else {
                            toastShort("更新考勤组成功")
                        }
                        setResult(Activity.RESULT_OK)
                        finish()
                    },
                    onError = { _, msg ->
                        toastShort(msg)
                    },
                    onDefault = { msg ->
                        toastShort(msg)
                    })
        })

        // 获取考勤组详情
        viewModel.getAttendGroupDetailResult.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result,
                    onSuccess = { data ->
                        // 已选择的班次回显
                        selectOrderList.clear()
                        val temp = hashMapOf<String, AttendOrderListBean>()
                        data.dutyClock.forEach { dutyClock ->
                            selectOrderList.add(AttendOrderListBean(dutyClock.id, dutyClock.name, content = dutyClock.content
                                    ?: ""))
                            temp[dutyClock.id] = selectOrderList.last()
                        }

                        usedOrderIds.clear()

                        // 考勤日和班次对应数据
                        attendWeekDayOrder.clear()
                        data.ateRuleDefaults.forEachIndexed { index, config ->
                            if (config.status == 1) {
                                attendWeekDayOrder[index] =
                                        AttendOrderListBean(id = config.id,
                                                name = config.name ?: "",
                                                content = temp[config.id]?.content ?: "")
                                if (usedOrderIds.isEmpty() || !usedOrderIds.contains(config.id)) {
                                    usedOrderIds.add(config.id)// 班次为空 或者 未包含该班次id
                                }
                            }
                        }

                        // 考勤周期数据回显
                        intervalOrderList.clear()
                        data.ateCycle?.let {
                            it.dutyClockIds.forEach { config ->
                                intervalOrderList.add(
                                        AttendOrderListBean(id = config.id,
                                                name = config.name,
                                                content = config.content ?: "")
                                )
                                if (usedOrderIds.isEmpty() || !usedOrderIds.contains(config.id)) {
                                    usedOrderIds.add(config.id)// 班次为空 或者 未包含该班次id
                                }
                            }
                            binding.tvAttendDate.text = it.name
                        }

                        tempDetail = data.turnToInfo(companyId)
                        ateHolidaySelves.clear()
                        ateHolidaySelves.addAll(tempDetail.ateHolidaySelves)
                        initDetailView()
                        updateDeptInfo()
                    },
                    onError = { _, _ ->
                        toastShort("获取考勤组失败")
                        finish()
                    },
                    onDefault = {
                        toastShort("获取考勤组失败")
                        finish()
                    })
        })

        // 删除考勤组
        viewModel.deleteAttendGroupResult.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result,
                    onSuccess = {
                        toastShort("删除成功")
                        setResult(Activity.RESULT_OK)
                        finish()
                    },
                    onError = { _, msg -> toastShort(msg) },
                    onDefault = { msg -> toastShort(msg) })
        })

        // 导出考勤组
        viewModel.exportAttendGroupResult.observe(this, Observer { result ->
            RequestHelper.onResponse(result,
                    onSuccess = {
                        toastShort("导出考勤组成功")
                    },
                    onError = { _, msg ->
                        if (msg.isNullOrBlank()) {
                            toastShort("导出考勤组失败")
                        } else {
                            toastShort(msg)
                        }
                    },
                    onDefault = { msg ->
                        if (msg.isNullOrBlank()) {
                            toastShort("导出考勤组失败")
                        } else {
                            toastShort(msg)
                        }
                    })

        })

        if (ateId.isNotEmpty()) {
            //如果是修改考勤组就要先去拿考勤组数据
            showLoading()
            viewModel.getAttendGroupDetail(ateId, companyId)
        } else {
            //default 考勤设置
            initDetailView()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun initDetailView() {

        binding.etGroupName.setText(tempDetail.ateGroupName)
        //如果是固定排班才涉及到考勤时间，日期，工作日特殊调整3个
        when (attendGroupType) {
            RouteAte.ATTEND_GROUP_TYPE_FIXED -> {
                binding.tvAttendDateTitle.text = "考勤日期"
                if (!ateId.isBlank()) {
                    setPageTitle("修改考勤组")
                    binding.tvGroupDel.visibility = View.VISIBLE
                    binding.llGroupManage.visibility = View.VISIBLE
                    binding.tvGroupExport.visibility = View.VISIBLE
                } else {
                    setPageTitle("固定班次考勤组设置")
                    binding.tvAttendDate.text = "每周一、二、三、四、五"
                    attendWeekDayOrder[0] = null
                    attendWeekDayOrder[1] = null
                    attendWeekDayOrder[2] = null
                    attendWeekDayOrder[3] = null
                    attendWeekDayOrder[4] = null
                }

                updateOrderInfo()// 固定考勤组 初始化显示班次信息并根据详情数据初始化班次 周几对应数据
                updateDayOrderInfo()//  初始化考勤日期 考勤班次对应
                updateSpecialDayInfo(true)
                binding.clSpecailDayConfig.visibility = View.VISIBLE
                binding.attendanceTimeLayout.visibility = View.VISIBLE
            }
            RouteAte.ATTEND_GROUP_TYPE_ORDER -> {
                if (!ateId.isBlank()) {
                    setPageTitle("修改考勤组")
                    binding.tvGroupDel.visibility = View.VISIBLE
                    binding.llGroupManage.visibility = View.VISIBLE
                    binding.tvGroupPreviewOrder.visibility = View.VISIBLE
                    binding.tvGroupExport.visibility = View.VISIBLE
                } else {
                    setPageTitle("排班制考勤组设置")
                }
                binding.tvAttendDateTitle.text = "排班周期"
                binding.tvAttendDate.hint = "请设置考勤周期"
                binding.tvAttendOrderTip.visibility = View.VISIBLE
                binding.attendanceTimeLayout.visibility = View.VISIBLE
                updateOrderInfo()//排班考勤组 初始化显示班次信息并根据详情数据初始化班次 周几对应数据
                updateUsedOrderIds()//  初始化考勤排班 班次Id信息
            }
            RouteAte.ATTEND_GROUP_TYPE_FREE -> {
                if (!ateId.isBlank()) {
                    setPageTitle("修改考勤组")
                    binding.tvGroupDel.visibility = View.VISIBLE
                    binding.llGroupManage.visibility = View.VISIBLE
                    binding.tvGroupExport.visibility = View.VISIBLE
                } else {
                    setPageTitle("自由打卡考勤组设置")
                }
                binding.attendanceTimeLayout.visibility = View.GONE
            }
            "fixChange", "freeChange" -> {
                setPageTitle("修改考勤组")
            }
        }

        /**点击事件*/
        fun initClickListener() {
            binding.clAttendOrder.setOnClickListener(this)
            binding.clAttendDate.setOnClickListener(this)
            binding.clSpecailDayConfig.setOnClickListener(this)
            binding.rangeLayout.setOnClickListener(this)
//        specialDateLayout.setOnClickListener(this)
            binding.wifiLayout.setOnClickListener(this)
            binding.locationLayout.setOnClickListener(this)
            binding.personLayout.setOnClickListener(this)
            binding.depLayout.setOnClickListener(this)

            binding.tvGroupDel.setOnClickListener(this)
            binding.tvGroupPreviewOrder.setOnClickListener(this)
            binding.tvGroupExport.setOnClickListener(this)
        }

        /**考勤定位数据显示*/
        fun initLocationAdapter() {
            if (tempDetail.atePlace.isNotEmpty()) {
                binding.locationListRev.visibility = View.VISIBLE
                binding.scopeTopLine.visibility = View.VISIBLE
            } else {
                binding.locationListRev.visibility = View.GONE
                binding.scopeTopLine.visibility = View.GONE
            }
            locationAdapter = AttendanceLocationAdapter(mContext!!, tempDetail.atePlace)
            locationAdapter.setDelListener(object : AttendanceLocationAdapter.DelListener {
                override fun del(position: Int) {
                    delLocation(position)
                }
            })
            binding.locationListRev.adapter = locationAdapter
        }

        initClickListener()

        initLocationAdapter()
        updateScopeInfo()
        updateWifiInfo()
        updateMemberInfo()
        updateDeptInfo()
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            tv_rightTitle -> {
                //保存数据
                doGroupCreate()
            }
            binding.clAttendOrder -> {
                chooseAttendOrder()
//                enterTimeSelectActivity()
            }
            binding.clAttendDate -> {
                enterDateSelectActivity()
            }
            binding.clSpecailDayConfig -> {
                enterSpecialDateActivity()
            }
            binding.locationLayout -> {
//                ARouter.getInstance()
//                        .build(RouteIm.locationActivity)
//                        .withString("type", "addAttendanceLocation")
//                        .navigation(this, 1004)
                val url = "${TIANDITU_URL}?mapViewPageType=${MapRouterType.GET_LOCATION.type}"
                val mapUrl = LinkBuilder.generate().getResultUrl(url)
                ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                    .withString(ConsKeys.PARAMS_URL, mapUrl)
                    .withString("type", "addAttendanceLocation")
                    .withInt("backspaceKey",1)
                    .withString("titleName", "添加考勤地点")
                    .navigation(this ,1004)
            }
            binding.rangeLayout -> {
                showAttendanceRange(binding.rangeText)
            }
            binding.wifiLayout -> {
                enterWifiSelectActivity()
            }
            binding.personLayout -> {
                enterPersonSelectActivity()
            }
            binding.depLayout -> {
                enterDeptSelectActivity()
            }
            binding.tvGroupPreviewOrder -> {
                val time = XUtil.turnToTimeStr(System.currentTimeMillis(), "yyyy-MM")
                ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                        .withInt(ConsKeys.MODEL_TYPE, 1)
                        .withString(ConsKeys.COMPANY_ID, companyId)
                        .withString(ConsKeys.PARAMS_URL,
                                LinkBuilder.generate(this)
                                        .getAttendOrderSetting(companyId, tempDetail.ateId, time)
                        )
                        .navigation()
            }
            binding.tvGroupExport -> {
                exportGroup()
            }
            binding.tvGroupDel -> {
                showLoading()
                viewModel.deleteAttendGroup(ateId, companyId)
            }
        }
    }

    private fun exportGroup() {

        fun dealExportNext() {
            val dialog = MyDialog(mContext, 280, 144, "",
                    true, needBtnCancel = true, bgResourceId = 0)
            val view = View.inflate(mContext, R.layout.dialog_export_attendance_next, null)
            val startTime = view.findViewById<TextView>(R.id.exportStartTime)
            val endTime = view.findViewById<TextView>(R.id.exportEndTime)
            val changeDate = view.findViewById<ImageView>(R.id.changeDate)
            endTime.text = CommonUtils.calTimeFrontDate(CommonUtils.getCurrentDate(), 1)
            startTime.text = CommonUtils.calTimeFrontDate(CommonUtils.getCurrentDate(), 30)
            changeDate.setOnClickListener {
                endTime.text = startTime.text
                startTime.text = endTime.text
            }
            startTime.setOnClickListener {
                AttendanceUtil.selectExportDateNew(this, startTime.text.toString(),
                        startTime, endTime, "start")
            }
            endTime.setOnClickListener {
                AttendanceUtil.selectExportDateNew(this, endTime.text.toString(),
                        startTime, endTime, "end")
            }
            dialog.setView(view, Gravity.CENTER)
            dialog.setCanceledOnTouchOutside(true)
            dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
                override fun clickRightBtn() {
                    dialog.dismiss()
                    viewModel.exportAttendanceGroup(companyId, tempDetail.ateId, startTime.text.toString(), endTime.text.toString())
                }

            })
            dialog.show()
        }

        fun checkEmail() {
            val bundle = Bundle()
            bundle.putString("type", "email")
            bundle.putString("name", UserHolder.getCurrentUser()?.email)
            bundle.putInt("requestCode", 1009)
            (ARouter.getInstance().build(RouteProvider.WORK_PROVIDER).navigation() as RouteServiceProvider)
                    .openPageWithResult(this, "checkEmail", bundle) {
                        if (it == "success") {
                            dealExportNext()
                        }
                    }
        }

        checkEmail()
    }

    /**已选择默认班次集合*/
    private val selectOrderList = arrayListOf<AttendOrderListBean>()

    // 默认选择班次使用中记录
    private val usedOrderIds = arrayListOf<String>()

    private fun getDefaultOrder(): AttendOrderListBean {
        return selectOrderList[0]
    }

    @SuppressLint("SetTextI18n")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && data != null) {
            when (requestCode) {
                1001 -> {
                    // 选择考勤班次
                    selectOrderList.clear()
                    val temp = data.getSerializableExtra("orderList") as ArrayList<AttendOrderListBean>?
                    if (!temp.isNullOrEmpty()) {
                        selectOrderList.addAll(temp)
                    }
                    updateOrderInfo(true)// 选择班次后，更新UI显示和对应关联数据，更新班次取值范围
                }
                1002 -> {
                    // 选择考勤日期
                    // // CHANGE_HISTORY: 2020/12/25 13:15 可以只选择周几和休息日自动排休，提交数据时校验是否关联了排班信息
                    val temp = data.getSerializableExtra("weekOrder") as HashMap<Int, AttendOrderListBean?>?
                    attendWeekDayOrder.clear()
                    if (!temp.isNullOrEmpty()) {
                        // 用户选择了考勤日期或者法定假日自动排休，否则不加入到日期班次map中
                        attendWeekDayOrder.putAll(temp)
                        updateDayOrderInfo()// 更新考勤日期 考勤班次对应
                    }
                }
                1003 -> {
                    // // TODO: 2020/12/24 17:33  选择考勤周期 需要返回周期名称
                    val name = data.getStringExtra("intervalName") ?: ""
                    binding.tvAttendDate.text = name
                    val temp = data.getSerializableExtra("intervalList") as HashMap<Int, AttendOrderListBean>?
                    intervalOrderList.clear()
                    usedOrderIds.clear()
                    if (!temp.isNullOrEmpty()) {
                        for (i in temp.keys.sorted()) {
                            val order = temp[i]!!
                            showLog("转换周期数据存储到集合中 $i $order")
                            intervalOrderList.add(order)
                        }
                        if (intervalOrderList.isNotEmpty()) {
                            usedOrderIds.addAll(intervalOrderList.map { it.id }.distinct())
//                            updateUsedOrderIds()//  更新考勤排班 班次Id信息
                        }
                    }
                }
                1004 -> {
                    // 选择定位
                    binding.locationListRev.visibility = View.VISIBLE
                    addLocation(data)
                }
                1005 -> {
                    // 选择wifi
                    dealWifiActivityReturn(data)
                }
                1006 -> {
                    // 选择人员
                    dealPersonActivityReturn(data)
                }
                1007 -> {
                    // 选择部门
                    dealDeptActivityReturn(data)
                }
                1008 -> {
                    dealSpecialActivityReturn(data)
                }
                1009 -> {
                    toastShort("设置邮箱成功")
                }
            }
        }
    }

    private fun updateUsedOrderIds() {
        val range = selectOrderList.map { it.id }
        showLog("before remove useIds is ${GsonUtil.toJson(usedOrderIds)}")
        usedOrderIds.removeAll { it !in range }
        showLog("after remove useIds is ${GsonUtil.toJson(usedOrderIds)}")
    }

    /**考勤班次信息*/
    private fun updateOrderInfo(isNewRange: Boolean = false) {
        if (!selectOrderList.isNullOrEmpty()) {
            binding.tvAttendOrder.text = selectOrderList.joinToString("、") { it.name }
//                        selectedOrderList.addAll(temp)
            if (attendGroupType == RouteAte.ATTEND_GROUP_TYPE_FIXED) {
                attendWeekDayOrder[-1] = getDefaultOrder()
                attendWeekDayOrder.keys.forEach { key ->
                    // todo check 如果新选择了班次，已经设置的班次和考勤对应数据如何处理 如果班次已经应用不会修改
//                    if (isNewRange) {
//                        attendWeekDayOrder[key] = getDefaultOrder()
//                    } else {
                    if (attendWeekDayOrder[key] != null) {
                        // 如果哪天没有设置排班，自动设置为考勤组选择的班次
                        attendWeekDayOrder[key] = getDefaultOrder()
                    }
//                    }
                }
            } else if (attendGroupType == RouteAte.ATTEND_GROUP_TYPE_ORDER) {
                // todo check 如果新选择了班次范围，已经排班的数据如何处理 如果班次已经应用不会修改
                if (isNewRange) {

                }
            }
        } else {
            binding.tvAttendOrder.text = ""
            if (attendGroupType == RouteAte.ATTEND_GROUP_TYPE_FIXED) {
                attendWeekDayOrder[-1] = null
            }
        }
    }

    /**考勤打卡和对应班次信息*/
    private fun updateDayOrderInfo() {
        usedOrderIds.clear()
        if (attendWeekDayOrder.filterKeys { it != -1 || it != 7 }.toList().isNullOrEmpty()) {
            binding.tvAttendDate.text = "请选择考勤日期"
        } else {
            fun addOrderId(orderId: String?) {
                if (!orderId.isNullOrBlank() && !usedOrderIds.contains(orderId)) {
                    usedOrderIds.add(orderId)
                }
            }

            val sb = StringBuilder("每周")
            if (attendWeekDayOrder.containsKey(0)) {
                sb.append("一、")
                addOrderId(attendWeekDayOrder[0]?.id)
            }
            if (attendWeekDayOrder.containsKey(1)) {
                sb.append("二、")
                addOrderId(attendWeekDayOrder[1]?.id)
            }
            if (attendWeekDayOrder.containsKey(2)) {
                sb.append("三、")
                addOrderId(attendWeekDayOrder[2]?.id)
            }
            if (attendWeekDayOrder.containsKey(3)) {
                sb.append("四、")
                addOrderId(attendWeekDayOrder[3]?.id)
            }
            if (attendWeekDayOrder.containsKey(4)) {
                sb.append("五、")
                addOrderId(attendWeekDayOrder[4]?.id)
            }
            if (attendWeekDayOrder.containsKey(5)) {
                sb.append("六、")
                addOrderId(attendWeekDayOrder[5]?.id)
            }
            if (attendWeekDayOrder.containsKey(6)) {
                sb.append("日、")
                addOrderId(attendWeekDayOrder[6]?.id)
            }
            if (attendWeekDayOrder.containsKey(7)) {// 假期自动排休功能
                showLog("打开了节假日自动排休")
            }
            val info = sb.toString()
            binding.tvAttendDate.text = if (info.length == 2) "" else info.substring(0, info.length - 1)
            updateUsedOrderIds()//  更新考勤日期 使用中班次Id信息
        }
    }

    private var ateHolidaySelves = ArrayList<AteHolidaySelf>()

    /**考勤特殊工作日调整信息*/
    private fun updateSpecialDayInfo(reload: Boolean = false) {
        binding.specialDateText.text = if (ateHolidaySelves.isNotEmpty()) {
            "已调整${ateHolidaySelves.size}天".plus(if (reload) "" else "，保存后生效")
        } else {
            ""
        }
    }

    /**考勤打卡范围信息*/
    private fun updateScopeInfo() {
        binding.rangeText.text = "${tempDetail.scope}米"
    }

    /**考勤wifi信息*/
    private fun updateWifiInfo() {
        if (tempDetail.ateWifi.isNotEmpty()) {
            binding.wifiText.text = "已设置${tempDetail.ateWifi.size}个"
        } else {
            binding.wifiText.text = "未设置"
        }
    }

    /**考勤组 打卡成员信息*/
    private fun updateMemberInfo() {
        binding.personText.text = "${tempDetail.ateMember.size}人"
    }

    /**考勤组 关联部门信息*/
    private fun updateDeptInfo() {
        binding.depText.text = if (tempDetail.ateDept.isNullOrEmpty()) "" else tempDetail.ateDept.size.toString().plus("个")
    }

    /**选择考勤班次*/
    private fun chooseAttendOrder() {
        val intent = Intent(this, AttendOrderListActivity::class.java)
        /**固定考勤组只能选择一个班次*/
        intent.putExtra("selectOne", attendGroupType == RouteAte.ATTEND_GROUP_TYPE_FIXED)
        // 已选中班次
        intent.putExtra("selectedList", selectOrderList)
        intent.putExtra("usedOrderIds", usedOrderIds)
        intent.putExtra(ConsKeys.COMPANY_ID, companyId)
        showLog("传递给班次页面已选择班次信息 ${GsonUtil.toJson(selectOrderList)}")
        startActivityForResult(intent, 1001)
    }

    /**选择考勤日期*/
    private fun enterDateSelectActivity() {
        if (attendGroupType == RouteAte.ATTEND_GROUP_TYPE_FIXED) {
            val intent = Intent(mContext!!, AttendanceDateActivity2::class.java)
            intent.putExtra(ConsKeys.COMPANY_ID, companyId)
            // 考勤组选定班次
            // -1 为固定考勤 已选择的班次信息
            if (selectOrderList.isNotEmpty()) {
                attendWeekDayOrder[-1] = getDefaultOrder()
                attendWeekDayOrder.keys.forEach { key ->
                    if (attendWeekDayOrder[key] == null) {
                        // 如果哪天没有设置排班，自动设置为考勤组选择的班次
                        attendWeekDayOrder[key] = getDefaultOrder()
                    }
                }
            } else {
                attendWeekDayOrder[-1] = null
                toastShort("请选择考勤班次")
                return
            }
            // 0-6 为一周每天班次配置，
            // 7为法定假日调休自动排休设置
            showLog("传递给考勤日期页面 日期班次信息 ${GsonUtil.toJson(attendWeekDayOrder)}")
            intent.putExtra("dayOrderList", attendWeekDayOrder)
            startActivityForResult(intent, 1002)
        } else if (attendGroupType == RouteAte.ATTEND_GROUP_TYPE_ORDER) {
            if (selectOrderList.isNotEmpty()) {
                val intent = Intent(mContext!!, AttendIntervalActivity::class.java)
                intent.putExtra("orderList", selectOrderList)// 已选择的多个班次，周期内天数选择
                intent.putExtra("intervalList", intervalOrderList)// 周期天和班次设置信息
                intent.putExtra("intervalName", binding.tvAttendDate.text)// 周期名称
                startActivityForResult(intent, 1003)
            } else {
                toastShort("请选择考勤班次")
            }
        }
    }

    /**考勤范围*/
    private fun showAttendanceRange(rangeText: TextView) {
        val view = View.inflate(mContext!!, R.layout.dialog_attendance_location_range, null)
        val dialog = BottomDialogUtil.showBottomDialog(mContext!!, view, Gravity.BOTTOM)
        val rangePicker = view.findViewById<WheelListView>(R.id.rangePicker)
        val confirm = view.findViewById<TextView>(R.id.confirm)
        val cancel = view.findViewById<TextView>(R.id.cancel)
        val dataList = ArrayList<String>()
        for (i in 1..10) {
            dataList.add("${i * 100}米")
        }
        rangePicker.setItems(dataList)
        if (tempDetail.scope != 0) {
            rangePicker.selectedItem = "${tempDetail.scope}米"
        } else {
            rangePicker.selectedItem = "500米"
        }
        val config = LineConfig()
        config.color = CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.line_grey)
        config.alpha = 100
        config.thick = ScreenUtils.dip2px(mContext, 1.0F).toFloat()
        config.isShadowVisible = false
        rangePicker.setLineConfig(config)
        rangePicker.setSelectedTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color1E87F0))
        rangePicker.setUnSelectedTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.colorb2b2b2))
        rangePicker.setOnWheelChangeListener { _, item ->
            tempDetail.scope = item.replace("米", "").toInt()
        }
        confirm.setOnClickListener {
            dialog.dismiss()
            updateScopeInfo()
        }
        cancel.setOnClickListener {
            dialog.dismiss()
        }
        dialog.show()
    }

    /**选择考勤位置后回调弹窗显示*/
    private fun addLocation(data: Intent) {
        val bd09 = wgs84ToBd09(data.getDoubleExtra("latitude", 0.0),
            data.getDoubleExtra("longitude", 0.0))
        Log.d("test","=====考勤坐标wgs转bd09========lat==${bd09.lat}===lon===${bd09.lon}===")
        val bean = AttendanceLocationBean(
                bd09.lat,
                bd09.lon,
                data.getStringExtra("title"),
                data.getStringExtra("address"))
        if (bean.address.isNullOrBlank() || bean.location.isNullOrBlank()) return
        val dialog = MyDialog(mContext!!, 0, 0,
                "", needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0)
        val view = View.inflate(mContext!!, R.layout.dialog_addlocation, null)
        val name = view.findViewById<EditText>(R.id.nameEdit)
//        if (StringUtils.isNotBlankAndEmpty(bean.location)) {
//            //将光标移至文字末尾
//            name.setSelection(bean.location.length)
//        }
        name.setText(bean.location)
        view.findViewById<TextView>(R.id.addressText).text = bean.address
        dialog.setView(view, Gravity.CENTER)
        dialog.setCanceledOnTouchOutside(false)
        dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
            override fun clickRightBtn() {
                binding.scopeTopLine.visibility = View.VISIBLE
                if (StringUtils.isNotBlankAndEmpty(name.text.toString())) {
                    bean.location = name.text.toString()
                }
                tempDetail.atePlace.add(bean)
                locationAdapter.notifyDataSetChanged()
            }

        })
        dialog.show()
    }

    /**删除考勤地点*/
    private fun delLocation(position: Int) {
        val dialog = MyDialog(mContext!!, 280, 140,
                "您确定要删除这个考勤地点吗？",
                needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0)
        dialog.setCanceledOnTouchOutside(true)
        dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
            override fun clickRightBtn() {
                locationAdapter.notifyItemRemoved(position)
                tempDetail.atePlace.removeAt(position)
                if (tempDetail.atePlace.isEmpty()) {
                    binding.scopeTopLine.visibility = View.GONE
                    binding.locationListRev.visibility = View.GONE
                }
                toastShort("删除成功")
            }

        })
        dialog.show()
    }

    /**特殊工作日调整*/
    private fun enterSpecialDateActivity() {
        val intent = Intent(mContext!!, AttendanceSpecialDateActivity::class.java)
        intent.putExtra(ConsKeys.MODEL_ID, ateId)
        intent.putExtra(ConsKeys.COMPANY_ID, companyId)
        intent.putExtra(ConsKeys.KEY_INTENT_DATA, specialMap)
        startActivityForResult(intent, 1008)
    }

    /**特殊工作日调整返回数据处理*/
    @SuppressLint("SetTextI18n")
    private fun dealSpecialActivityReturn(data: Intent) {
        if (data.getSerializableExtra(ConsKeys.KEY_INTENT_DATA) != null) {
            specialMap = data.getSerializableExtra(ConsKeys.KEY_INTENT_DATA) as HashMap<String, AteHolidaySelf>
            ateHolidaySelves.clear()
            if (specialMap.isNotEmpty()) {
                ateHolidaySelves.addAll(specialMap.values.toList())
            }
        }
        updateSpecialDayInfo()
    }

    /**选择wifi*/
    private fun enterWifiSelectActivity() {
        val intent = Intent(mContext!!, AttendanceWifiActivity::class.java)
        if (!tempDetail.ateWifi.isNullOrEmpty()) {
            intent.putExtra(ConsKeys.KEY_INTENT_DATA, tempDetail.ateWifi)
        }
        startActivityForResult(intent, 1005)
    }

    /**wifi选择后弹窗提示*/
    @SuppressLint("SetTextI18n")
    private fun dealWifiActivityReturn(data: Intent) {
        val temp = data.getSerializableExtra(ConsKeys.RESULT_DATA) as ArrayList<WifiBean>?
        tempDetail.ateWifi.clear()
        if (!temp.isNullOrEmpty()) {
            tempDetail.ateWifi.addAll(temp)
        }
        updateWifiInfo()
    }

    /**
     * 跳转 考勤关联部门选择页面 选择关联部门
     */
    private fun enterDeptSelectActivity() {
        val mintent = Intent(mContext!!, TimeDeptSettingActivity::class.java)
        mintent.putExtra("companyId", companyId)
        mintent.putExtra("groupId", ateId)
        if (!tempDetail.ateDept.isNullOrEmpty()) {
            mintent.putExtra("editDepts", tempDetail.ateDept.map {
                AttendanceDepBean(it.deptId, it.parentId)
            }.toMutableList() as ArrayList)
        }
        startActivityForResult(mintent, 1007)
    }

    /**
     * 处理关联部门选择 结果返回
     */
    private fun dealDeptActivityReturn(data: Intent) {
        var deptIsNull = false
        if (StringUtils.isNotBlankAndEmpty(data.getStringExtra("deptIsNull")) &&
                data.getStringExtra("deptIsNull") == "true") {
            deptIsNull = true
        }
        val temp = data.getSerializableExtra(
                "choiceDepartment") as ArrayList<AttendanceDepBean>?
        tempDetail.ateDept.clear()
        if (!temp.isNullOrEmpty()) {
            tempDetail.ateDept.addAll(temp.map { AteDeptBean(companyId, it.deptId, it.parentId) })
        }
        updateDeptInfo()

    }

    /**考勤人员选择*/
    private fun enterPersonSelectActivity() {
        val intent = Intent(mContext!!, TimeMemberSetingActivity::class.java)
        intent.putExtra("companyId", companyId)
        intent.putExtra("groupId", ateId)
        intent.putExtra("needLoad", true)
        // 跳转选人页面，在创建时，需要代入已选择临时成员，编辑时不需要，会从接口中checkMember中获取
        if (ateId.isNullOrBlank() && !tempDetail.ateMember.isNullOrEmpty()) {
            ObjectStore.add("editUsers" , tempDetail.ateMember.map { it.userId }.toMutableList() as ArrayList)
        }
        startActivityForResult(intent, 1006)
    }

    /**
     * 处理考勤组成员选择 结果返回
     */
    @SuppressLint("SetTextI18n")
    private fun dealPersonActivityReturn(data: Intent) {
        if (data.getStringExtra("memberIsNull") != null && data.getStringExtra("memberIsNull") == "true") {
            var memberIsNull = true
        }
        val temp = data.getStringArrayListExtra("choiceUsers") as ArrayList<String>
        tempDetail.ateMember.clear()
        if (!temp.isNullOrEmpty()) {
            tempDetail.ateMember.addAll(temp.map { AteMember(it) }.toList())
        }
        binding.personText.text = "${tempDetail.ateMember.size}人"
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun getUsers(bean: TimeUserIdsSender) {
        if (bean.userIds != null && bean.userIds.isNotEmpty()) {
            val intent1 = Intent()
            intent1.putExtra("choiceUsers", bean.userIds)
            intent1.putExtra("memberIsNull", bean.memberIsNull)//不传boolean防止与子集页面冲突
            dealPersonActivityReturn(intent1)
        } else if (bean.ids != null && bean.ids.isNotEmpty()) {
            val intent1 = Intent()
            intent1.putExtra("deptIsNull", "false")
            intent1.putExtra("choiceDepartment", bean.ids)
            dealDeptActivityReturn(intent1)
        }
    }

    private fun doGroupCreate() {
        // TODO: 2020/12/25 11:05 保存考勤组
        val groupName = binding.etGroupName.text?.toString() ?: ""
        if (groupName.isNullOrBlank()) {
            toastShort("请设置考勤组名称")
            return
        }
        tempDetail.ateGroupName = groupName
        when (attendGroupType) {
            RouteAte.ATTEND_GROUP_TYPE_FIXED -> {
                tempDetail.ateSchedule = 0

                if (selectOrderList.isNullOrEmpty()) {
                    toastShort("请选择考勤班次")
                    return
                }

                tempDetail.isOpenHoliday = if (attendWeekDayOrder.containsKey(7)) 1 else 0
                tempDetail.ateRuleDefaultDTOS.clear()
                val set = TreeSet<Int>()
                set.addAll(attendWeekDayOrder.keys.filter { it != -1 && it != 7 })
                arrayOf(0, 1, 2, 3, 4, 5, 6).iterator().forEach { day ->
                    val order = attendWeekDayOrder[day] ?: getDefaultOrder()
                    tempDetail.ateRuleDefaultDTOS.add(
                            if (attendWeekDayOrder.containsKey(day)) {
                                AteDayConfig(order.id, 1)
                            } else {
                                AteDayConfig("0", 0)
                            }
                    )
                }
                tempDetail.dutyClockIds.clear()
                if (tempDetail.ateRuleDefaultDTOS.isNotEmpty()) {
                    tempDetail.dutyClockIds.addAll(
                            tempDetail.ateRuleDefaultDTOS.filter { it.status == 1 }.map { it.dutyClockId }.distinct()
                    )
                }
                if (tempDetail.ateRuleDefaultDTOS.isNullOrEmpty()) {
                    toastShort("请设置考勤日期")
                    return
                }
            }
            RouteAte.ATTEND_GROUP_TYPE_ORDER -> {
                if (selectOrderList.isNullOrEmpty()) {
                    toastShort("请选择考勤班次")
                    return
                }
                tempDetail.dutyClockIds.clear()
                if (selectOrderList.isNotEmpty()) {
                    tempDetail.dutyClockIds.addAll(selectOrderList.map { it.id })
                }
                if (tempDetail.dutyClockIds.isNullOrEmpty()) {
                    toastShort("请选择考勤班次")
                    return
                }
                tempDetail.ateSchedule = 2
                if (binding.tvAttendDate.text.isNullOrBlank() || intervalOrderList.isEmpty()) {
                    toastShort("请设置考勤周期")
                    return
                }
                // 周期数据
                tempDetail.ateScheduleCycle = AteScheduleCycle(
                        dutyClockIds = intervalOrderList.map { DutyClockId(it.id) },
                        name = binding.tvAttendDate.text.toString(),
                        period = intervalOrderList.size)
            }
            else -> {
                tempDetail.ateSchedule = 1
            }
        }

        if (tempDetail.ateWifi.isNullOrEmpty() && tempDetail.atePlace.isNullOrEmpty()) {
            toastShort("请设置考勤地点或者考勤WiFi")
            return
        }

        if (!tempDetail.atePlace.isNullOrEmpty() && tempDetail.scope <= 0) {
            toastShort("请设置打卡范围")
            return
        }

        if (tempDetail.ateMember.isNullOrEmpty() && tempDetail.ateDept.isNullOrEmpty()) {
            toastShort("请设置考勤成员或者考勤关联部门")
            return
        }
        tempDetail.deptIsNull = tempDetail.ateDept.isEmpty()
        tempDetail.memberIsNull = tempDetail.ateMember.isEmpty()
        tempDetail.ateHolidaySelves.clear()
        tempDetail.ateHolidaySelves.addAll(ateHolidaySelves)
        showLog("当前提交考勤组数据为:" + GsonUtil.toJson(tempDetail))
        showLoading()
        if (ateId.isNullOrBlank()) {
            viewModel.createAttendGroup(tempDetail)
        } else {
            viewModel.updateAttendGroup(tempDetail)
        }

    }
}