package com.jounutech.work.view.fragment

import android.content.Intent
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.launcher.ARouter
import com.bumptech.glide.request.RequestOptions
import com.ddbes.library.im.imtcp.Logger
import com.joinutech.approval.utils.RouteApr
import com.joinutech.common.adapter.MultiTypeAdapter
import com.joinutech.common.adapter.MyAdapter
import com.joinutech.common.base.LinkBuilder
import com.joinutech.common.base.isAttendance
import com.joinutech.common.base.isDebug
import com.joinutech.common.base.isFuture
import com.joinutech.common.helper.OnFragmentResumeListener
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.UserHolder
import com.joinutech.common.widget.OnEmptyClickListener
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.base.BaseFragment
import com.joinutech.ddbeslibrary.bean.Banner
import com.joinutech.ddbeslibrary.bean.EntroyBean
import com.joinutech.ddbeslibrary.bean.PageInfoBean
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.org.GlobalCompanyHolder
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.EventBusAction.EVENT_WORK_BANNER_REFRESH
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.RoundImageView
import com.jounutech.work.R
import com.jounutech.work.adapter.WorkCooperationCompanyListAdapter
import com.jounutech.work.constract.RouteAte
import com.jounutech.work.constract.RouteWork
import com.jounutech.work.view.BulletinListActivity
import com.jounutech.work.viewModel.WorkFragmentViewModel
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * @PackageName: com.jounutech.work.view.fragment
 * @ClassName:
 * @Desc: 工作首页
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/3/27 16:24
 */
class WorkFragmentNew : BaseFragment(), OnEmptyClickListener {
    override val layoutRes: Int = R.layout.fragment_work_new
    var listener: OnFragmentResumeListener? = null

    private lateinit var topBannerBackground: RoundImageView//banner图片

    private lateinit var noticeLayout: View//通知消息布局
    private lateinit var noticeMoreOrCreate: View
    private lateinit var tvNoticeTitle: TextView
    private lateinit var tvNoticeMoreOrCreate: TextView
    private lateinit var ivNoticeMoreOrCreate: View

    /**未加入团队时显示*/
    private lateinit var emptyPage: PageEmptyView

    /**仅存在协作团队时显示*/
    private lateinit var noOrgNoHaveCooperationLayout: ScrollView// 无团队但是有协作关系的工作页显示

    /**存在参加或创建团队时显示*/
    private lateinit var haveOrgLayout: LinearLayout//已加入团队后显示

    private val workListData = arrayListOf<WorkGroupData>()
    private lateinit var groupAdapter: MyAdapter<WorkGroupData>

    private var cooperationCompanyList = arrayListOf<WorkStationBean>()
    private lateinit var cooperationAdapter: WorkCooperationCompanyListAdapter
    private lateinit var viewModel: WorkFragmentViewModel

    private lateinit var clLeft: View
    private lateinit var ivLeft: ImageView
    private lateinit var ivLeftDot: View
    private lateinit var toolbar_title: TextView

    /**-----------------------------视图初始化-----------------------------**/
    override fun initView(rootView: View) {
        showLog("work fragment new onInitView()")

        fun initTitleBar(rootView: View) {
            val titleBar: View = rootView.findViewById(R.id.title_bar_root)
            val topContainer: View = rootView.findViewById(R.id.title_bar_container)
            clLeft = titleBar.findViewById(com.joinutech.ddbeslibrary.R.id.cl_left)
            ivLeft = titleBar.findViewById(com.joinutech.ddbeslibrary.R.id.iv_left)
            ivLeftDot = titleBar.findViewById(com.joinutech.ddbeslibrary.R.id.iv_left_dot)
            clLeft.visibility = View.GONE
            topContainer.setOnClickListener(this)
            clLeft.setOnClickListener(this)
            toolbar_title = titleBar.findViewById(com.joinutech.ddbeslibrary.R.id.toolbar_title)
            setStatusBarView(topContainer)
        }

        fun initTopView(rootView: View) {
            topBannerBackground = rootView.findViewById(R.id.topImage)
            initTitleBar(rootView)
        }

        fun initNoticeView(rootView: View) {
            noticeMoreOrCreate = rootView.findViewById(R.id.notice_more_or_create)
            tvNoticeTitle = rootView.findViewById(R.id.notice_content)
            tvNoticeMoreOrCreate = rootView.findViewById(R.id.notice_more_or_create_tv)
            ivNoticeMoreOrCreate = rootView.findViewById(R.id.notice_create_Iv)
            tvNoticeTitle.setOnClickListener(this)
            noticeMoreOrCreate.setOnClickListener(this)
        }

        fun initEntranceList(rootView: View) {
            val groupList: RecyclerView = rootView.findViewById(R.id.rv_list)

            groupAdapter = MyAdapter(mActivity, R.layout.item_entrance_group_layout, workListData,
                onBindItem = { _: Int, group: WorkGroupData, groupView: View ->
                    val entranceList = groupView.findViewById<RecyclerView>(R.id.rv_group_list)
                    val entranceAdapter = MultiTypeAdapter(activity = mActivity,
                        generateViewType = { _, _ ->
                            group.type
                        },
                        generateLayoutId = {
                            if (it == 1) {
                                R.layout.item_entrance_manage_layout
                            } else {
                                R.layout.item_entrance_normal_layout
                            }
                        },
                        data = group.entrances.toMutableList(),
                        onBindItem = { _: Int, param: WorkEntrance, childView: View ->

                            childView.findViewById<TextView>(R.id.tv_ent_name).text = param.name

                            if (param.badge > 0) {
                                childView.findViewById<TextView>(R.id.tv_ent_tag).visibility =
                                    View.VISIBLE
//                                        childView.findViewById<TextView>(R.id.tv_ent_tag).text = param.badge.toString()// 文字设置数字时要转为字符串再赋值
                            } else {
                                childView.findViewById<TextView>(R.id.tv_ent_tag).visibility =
                                    View.GONE
                            }

                            if (param.showTag) {
                                childView.findViewById<View>(R.id.iv_ent_tag).visibility =
                                    View.VISIBLE
                            } else {
                                childView.findViewById<View>(R.id.iv_ent_tag).visibility = View.GONE
                            }

                            when {
                                StringUtils.isNotBlankAndEmpty(param.iconUrl) -> {
                                    ImageLoaderUtils.loadImage(
                                        mActivity,
                                        childView.findViewById(R.id.iv_ent_icon),
                                        param.iconUrl
                                    )
                                }
                                param.defaultIcon > 0 -> {
                                    childView.findViewById<ImageView>(R.id.iv_ent_icon)
                                        .setImageResource(param.defaultIcon)
                                }
                                else -> {
                                    childView.findViewById<ImageView>(R.id.iv_ent_icon)
                                        .setImageResource(com.joinutech.ddbeslibrary.R.drawable.icon_custom_apr_default)
                                }
                            }

                        },
                        onItemClick = { _: Int, param: WorkEntrance, _: View ->
                            //tcp点击入口
                            if (BaseApplication.currentNetState > 0) {
                                jumpPage(param)
                            } else {
                                showToast("请检查网络")
                            }
                        })
                    entranceList.layoutManager = GridLayoutManager(mActivity, 4)
                    entranceList.adapter = entranceAdapter
                    groupView.findViewById<TextView>(R.id.tv_group_title).text = group.groupName
                },
                onItemClick = { _: Int, _: WorkGroupData, _: View -> })
            groupList.layoutManager = LinearLayoutManager(mActivity)
            groupList.adapter = groupAdapter
        }

        fun initCooperationView(rootView: View) {
            val noDataJoinOrg2: View = rootView.findViewById(R.id.no_org_tv_add)
            val noDataCreateOrg2: View = rootView.findViewById(R.id.no_org_tv_creat)
            val noDataImageView: View = rootView.findViewById(R.id.cooperation_noOrgIv)
            noDataJoinOrg2.setOnClickListener(this)
            noDataCreateOrg2.setOnClickListener(this)
            noDataImageView.setOnClickListener(this)

            val cooperationList = rootView.findViewById<RecyclerView>(R.id.cooperationRv)
            cooperationList.layoutManager = LinearLayoutManager(mActivity)
            cooperationAdapter =
                WorkCooperationCompanyListAdapter(mActivity, cooperationCompanyList)
            cooperationList.adapter = cooperationAdapter
        }

        fun initNoOrgView(rootView: PageEmptyView) {
            rootView.clickListener = this
        }

        haveOrgLayout = rootView.findViewById(R.id.haveOrgLayout)
        noticeLayout = rootView.findViewById(R.id.none_notice_layout)
        noOrgNoHaveCooperationLayout = rootView.findViewById(R.id.noOrgNoHaveCooperationLayout)
        emptyPage = rootView.findViewById(R.id.layout_empty_layout)

        initTopView(rootView)

        /**通知公告入口*/
        initNoticeView(noticeLayout)
        /**优化首页功能入口显示*/
        initEntranceList(haveOrgLayout)

        /**只有协作团队时显示*/
        initCooperationView(noOrgNoHaveCooperationLayout)

        initNoOrgView(emptyPage)

        showEmptyView()//  默认显示无数据页面
    }

    /**
     * 0 default
     * 1 no net
     * 2 data error
     * */
    private fun showEmptyView(type: Int = 0) {
        if (type == 1) {
            emptyPage.setPositiveContent("点击重试")
            emptyPage.setNegativeContent("")
            topBannerBackground.visibility = View.GONE
            haveOrgLayout.visibility = View.GONE
            noticeLayout.visibility = View.GONE
            noOrgNoHaveCooperationLayout.visibility = View.GONE
        } else {
            haveOrgLayout.visibility = View.GONE
            noticeLayout.visibility = View.GONE
            noOrgNoHaveCooperationLayout.visibility = View.GONE
            topBannerBackground.visibility = View.GONE
            emptyPage.setNegativeContent("加入团队")
            emptyPage.setPositiveContent("创建团队")
        }
        emptyPage.show()

        toolbar_title.setTextColor(CommonUtils.getColor(mActivity, com.joinutech.ddbeslibrary.R.color.workTitleColor))
        toolbar_title.text = "工作"
        clLeft.visibility = View.GONE
    }

    override fun onNoDoubleClick(v: View) {
        super.onNoDoubleClick(v)
        when (v.id) {
            R.id.title_bar_container, com.joinutech.ddbeslibrary.R.id.cl_left -> {
                EventBusUtils.sendEvent(EventBusEvent(EventBusAction.Event_CLICK_HOME_WORK_LEFT, 2))
//                updateTitleBarStatus(true)
            }
//            R.id.noOrgIv -> {
//                //刷新团队
//                EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST,
//                        "无团队数据时获取所有团队"))
//            }
            R.id.no_org_tv_creat -> {
                onAction(1)
            }
            R.id.no_org_tv_add -> {
                onAction(2)
            }
            R.id.notice_more_or_create -> {
                if (StringUtils.isNotBlankAndEmpty(tvNoticeTitle.text.toString())) {
                    val intent = Intent(activity, BulletinListActivity::class.java)
                    startActivity(intent)
                } else if (currentOrg != null) {
                    val pageInfo = PageInfoBean()
                    pageInfo.title = resources.getString(com.joinutech.ddbeslibrary.R.string.notice_create_title)
                    pageInfo.pageType = 0
                    pageInfo.companyId = currentOrg!!.companyId
                    ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                        .withString("pageInfo", GsonUtil.toJson(pageInfo))
                        .withBoolean("isNoticeWeb", true)
                        .navigation()
                }
            }
            R.id.notice_content -> {
                if (currentOrg != null && !currentOrg!!.noticeId.isNullOrBlank() && currentOrg!!.noticeId != "0" && !currentOrg!!.companyId.isNullOrBlank()) {
                    val pageInfo = PageInfoBean()
                    pageInfo.title = resources.getString(com.joinutech.ddbeslibrary.R.string.notice_detail_title)
                    pageInfo.pageType = 1
                    pageInfo.noticeId = currentOrg!!.noticeId
                    pageInfo.companyId = currentOrg!!.companyId

                    ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                        .withString("pageInfo", GsonUtil.toJson(pageInfo))
                        .withBoolean("isNoticeWeb", true)
                        .navigation()
                }
            }
        }
    }

    /**-----------------------------网络处理-----------------------------**/
    override fun initLogic() {
        showLog("work fragment new initLogic()")
        viewModel = getModel(WorkFragmentViewModel::class.java)
        getObservable()
        GlobalCompanyHolder.companyUpdateResult.observe(this, Observer {
            showLog("工作首页 -- 接收团队信息更新")
            when (it.type) {
                1 -> {// 显示我的团队信息
                }
                2 -> {// 显示我的协作团队信息
                    showCooperationInfo(CompanyHolder.getCooperationOrg())// 更新协作团队信息
                }
                else -> {// 当前无任何团队信息
                    showEmptyView()//没有任何团队数据时显示无团队页面
                }
            }
        })

        //当切换当前公司的时候，会触发
        GlobalCompanyHolder.currentCompany.observe(this, Observer {
            showLog("工作首页 -- 当前团队更新")
            currentOrg = it
            if (currentOrg != null) {
                showLog("当前团队更新,更新团队列表信息")
                Logger.i("测试权限", "=currentOrg?.power=" + currentOrg?.power)
                showCompanyInfo(currentOrg!!)//更新团队列表信息
            }
        })

        //整合接口----处理数据变化
        entroyListData.observe(this, Observer {
            updateLeftRedDot()
            Logger.i("整合接口", "===刷新一次首页入口数据===")
            CompanyHolder.getCurrentOrg()?.let {
                val data = updateApplicationsData(it.companyId)
                var target = -1
                workListData.forEachIndexed { index, group ->
                    if (group.groupName == "全部应用") {
                        target = index
                    }
                }
                if (target in workListData.indices) {
                    workListData[target] = data
                    groupAdapter.notifyDataSetChanged()
                }
            }
        })
    }

    private fun getObservable() {
        viewModel.getNoticeResult.observe(this, Observer { result ->
            RequestHelper.onResponse(result,
                onSuccess = { data ->
                    showLog("++++++ fragment WorkFragment getNoticeResult()")
                    if (currentOrg != null) {
//                            val power = currentOrg?.power
//                            currentOrg!!.power = ORG_PERMISS_TYPE.checkPower(data.power, isCreator())
//                            showLog("---->>>>noticeUpdate: oldPower is $power , new power is ${currentOrg?.power}")
                        currentOrg!!.noticeId = data.noticeId
                        currentOrg!!.content = data.notice
                        Logger.i("测试权限", "=currentOrg!!.power=" + currentOrg!!.power)

                        if (needRefresh) {
                            updateEntrance(currentOrg!!.companyId, currentOrg!!.power)//更新权限后刷新显示
                        }
                    }
                    showNotice(data.notice)
                },
                onError = { _, _ ->
                    //                        showToast(msg)
                },
                onDefault = {
                    //                        showToast(msg)
                })
        })
    }

    private fun getNoticeAndPower(type: Int = 0) {
        if (type == 0) {
            needRefresh=true
        }else{
            needRefresh=false
        }
        showLog("++++++工作首页 获取当前团队权限和公告信息type = $type")
        if (currentOrg != null) {
            viewModel.getWorkNoticeAndPower(
                bindToLifecycle(),
                mActivity.accessToken!!,
                currentOrg!!.companyId
            )
        }
    }

    /**更新状态栏文字状态*/
    private fun updateTitleBarStatus(showDrawer: Boolean = false) {
//        updateStatusBar(showDrawer || (currentOrg == null && cooperationCompanyList.isNullOrEmpty()))
    }

    override fun onResume() {
        showLog("++++++ fragment Work onResume()")
        super.onResume()
        if (!receiveEvent) {
            listener?.onResumed(2)
        }
        updateTitleBarStatus()

    }

    /**-----------------------------事件处理-----------------------------**/

    var needRefresh=false
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun eventString(event: EventBusEvent<String>) {
        if (!receiveEvent) {
            receiveEvent = true
        }

        when (event.code) {
            "on_drawer_close" -> {
            }
            EventBusAction.REFRESH_MAIN_NOTICE -> {
                showLog("WorkFragment 3通知刷新")
                getNoticeAndPower()//刷新主页通知事件
            }
            else -> {
            }
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun eventBoolean(event: EventBusEvent<Boolean>) {
//        showLog("WorkFragment eventBoolean()")
        if (!receiveEvent) {
            receiveEvent = true
        }

        when (event.code) {
            EventBusAction.Event_REFRESH_HOME_LEFT_DRAW_IS_CLICK -> {
//                showLog("WorkFragment 2顶部左侧按钮可点击状态刷新")
                if (clLeft.visibility != View.VISIBLE) {
                    clLeft.visibility = View.VISIBLE
                }
                clLeft.isClickable = event.data!!
            }
            else -> {
//                showLog("WorkFragment  2未处理情况 ${event.code}")

            }
        }
    }

    var receiveEvent = false

    private val companyIdToUnreadMap = hashMapOf<String, Int>()
    private val entroyListData = MutableLiveData<ArrayList<EntroyBean>>()
    private var currentOrg: WorkStationBean? = null

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun eventData(event: EventBusEvent<Any>) {
        if (!receiveEvent) {
            receiveEvent = true
        }
        when (event.code) {
            EventBusAction.send_all_appro_undo_result -> {
                companyIdToUnreadMap.clear()
                if (event.data != null && event.data is HashMap<*, *> && (event.data as HashMap<*, *>).isNotEmpty()) {
                    companyIdToUnreadMap.putAll(event.data as HashMap<String, Int>)
                }
                //说明：companyIdToUnreadMap是所有公司的审批未读数，key代表companyId,value代表未读数
                //这是首页触发了刷新审批未读数，（可能是切换了当前公司，也可能是接收到了触发刷新的事件）
                updateLeftRedDot()
                val normalCompanys=CompanyHolder.getAllNormalOrg()
                normalCompanys.forEach {
                    it.haveApprove=companyIdToUnreadMap.get(it.companyId)?:0
                }
                CompanyHolder.saveAllNormalOrg(normalCompanys)
               val cooperCompanys= CompanyHolder.getCooperationOrg()
                cooperCompanys.forEach {
                    it.haveApprove=companyIdToUnreadMap.get(it.companyId)?:0
                }
                CompanyHolder.saveCooperationOrg(cooperCompanys)

               /* CompanyHolder.getCurrentOrg()?.let {
                    val data = updateApplicationsData(it.companyId)
                    var target = -1
                    workListData.forEachIndexed { index, group ->
                        if (group.groupName == "全部应用") {
                            target = index
                        }
                    }
                    if (target in workListData.indices) {
                        workListData[target] = data
                        groupAdapter.notifyDataSetChanged()
                    }
                }*/
            }

            //整合接口==第三步：接收整合后的数据集合，通过setValue方法更新liveDate数据tcp
            EventBusAction.event_refresh_home_entroy_list -> {
                val entroyList = arrayListOf<EntroyBean>()
                if (event.data != null && event.data is ArrayList<*> && (event.data as ArrayList<*>).isNotEmpty()) {
                    entroyList.addAll(event.data as ArrayList<EntroyBean>)
                }

                val cacheMap=UserHolder.getEntroyListMap()
                cacheMap.put(currentOrg?.companyId?:"",entroyList)
                UserHolder.saveEntroyListMap(cacheMap)
                Loggerr.i("切换公司测试", "===刷新入口---准备去执行===")
                entroyListData.value = entroyList
            }
            //刷新各模块的未读数
            EventBusAction.send_refresh_entroy_unread_result->{
                if (event.data != null && event.data is List<*> && (event.data as List<*>).isNotEmpty()) {

//                    val aa = event.data as List<EntroyBean>

                  val unreadList=  event.data as List<EntroyBean>

                    if (unreadList.isNotEmpty()) {
                        Loggerr.i("切换公司测试", "===刷新入口未读数---准备去执行===")
                        val entroyListMap=UserHolder.getEntroyListMap()
                        val entroyList=  entroyListMap.get(currentOrg?.companyId)
                        val resultList= arrayListOf<EntroyBean>()
                        entroyList?.forEach { entroy->
                            entroy.unreadInt=unreadList.find { it.key==entroy.key }?.unreadInt?:entroy.unreadInt
                            resultList.add(entroy)
                        }
                        entroyListMap.put(currentOrg?.companyId?:"",resultList)
                        UserHolder.saveEntroyListMap(entroyListMap)

                        entroyListData.value=resultList
                    }else{
                        Loggerr.i("切换公司测试", "===刷新入口未读数---没有执行刷新===")
                    }

                }else{
                    Loggerr.i("切换公司测试", "===刷新入口未读数---没有执行刷新===")
                }
            }
            EVENT_WORK_BANNER_REFRESH -> if (event.data is ArrayList<*>) {
                val temp = event.data as ArrayList<Banner>
                if (temp.isNotEmpty()) {
                    // bannerId 为 banner信息，和团队没有关系
                    updateBanner(temp[0])
                }
            }
        }
    }

    /**团队信息*/
    //切换主要团队tcp
    private fun showCompanyInfo(data: WorkStationBean, type: Int = 0) {
        showLog("初始化团队数据type = $type, ${GsonUtil.toJson(data)}")
        if (clLeft.visibility != View.VISIBLE) {
            clLeft.visibility = View.VISIBLE
        }
        toolbar_title.setTextColor(CommonUtils.getColor(mActivity, com.joinutech.ddbeslibrary.R.color.workTitleColor))
        toolbar_title.text = data.name
        if (StringUtils.isNotBlankAndEmpty(data.logo)) {
            ImageLoaderUtils.loadImage(mActivity, ivLeft, data.logo)
        }
        topBannerBackground.visibility = View.VISIBLE
        haveOrgLayout.visibility = View.VISIBLE
        noticeLayout.visibility = View.VISIBLE
        emptyPage.hide()
        noOrgNoHaveCooperationLayout.visibility = View.GONE

//        val power = data.power
//        data.power = ORG_PERMISS_TYPE.getMyPower(data.deptId, data.power)
//        showLog("---->>>>companyUpdate:oldPower is $power , new power is ${currentOrg?.power}")

        showNotice(data.content)
        getNoticeAndPower(1)//刷新用户切换团队数据产生的权限和公告内容变更
        Logger.i("测试权限", "=data.power=" + data.power)
        Loggerr.i("切换公司测试", "=showCompanyInfo导致=列表刷新=")
        updateEntrance(data.companyId, data.power)// 切换或更新团队信息时加载功能列表
    }

    /**协作人列表*/
    private fun showCooperationInfo(list: ArrayList<WorkStationBean>) {
        cooperationCompanyList = list
        topBannerBackground.visibility = View.VISIBLE
        noOrgNoHaveCooperationLayout.visibility = View.VISIBLE

        toolbar_title.setTextColor(CommonUtils.getColor(mActivity, com.joinutech.ddbeslibrary.R.color.workTitleColor))
        toolbar_title.text = "工作"
        clLeft.visibility = View.GONE
//        ivLeft.setImageResource(R.drawable.icon_home_left_select)
        haveOrgLayout.visibility = View.GONE
        noticeLayout.visibility = View.GONE
        emptyPage.hide()
        cooperationAdapter.setSourceList(cooperationCompanyList)
    }

    //点击入口tcp
    private fun jumpPage(param: WorkEntrance) {
        if (currentOrg != null && StringUtils.isNotBlankAndEmpty(param.routerPath)) {
            when (param.routerPath) {
                // 团队管理，点击管理成员
                RouteOrg.orgChartActivity -> {
                    //路由的使用，，，，直接跳转页面
                    ARouter.getInstance()
                        .build(param.routerPath)
                        .withString("depName", currentOrg!!.name)
                        .withBoolean("orgPermission", true)
                        .withString(ConsKeys.COMPANY_ID, currentOrg!!.companyId)
                        .navigation()
                }
                RouteWork.companyDigitalReportActivity -> {//点击企业数字报告
                    // TODO: 2020/3/25 17:04 数字报告改版 companyDigitalReportActivity
                    if (isFuture && isAttendance) {
                        ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                            .withString(ConsKeys.COMPANY_ID, currentOrg!!.companyId)
                            .withString(
                                ConsKeys.PARAMS_URL,
                                LinkBuilder.generate().getDigitalReportUrl3()
                            )
                            .navigation()
                    } else {
                        ARouter.getInstance()
                            .build(param.routerPath)
                            .withString(CommonKeys.COMPANY_ID, currentOrg!!.companyId)
                            .navigation()
                    }
                }
                // 日常工作
                RouteAte.attendanceHomeActivity -> {//点击考勤tcp
                    if (isFuture && isAttendance) {//都为true才用新版考勤
                        ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                            .withString(ConsKeys.COMPANY_ID, currentOrg!!.companyId)
                            .withString(
                                ConsKeys.PARAMS_URL,
                                LinkBuilder.generate().getAttendRecord()
                            )
                            .navigation()
                    } else {
                        Log.i("moon", "~~~~~~~~考勤~~~~~~~~~~ path = ${param.routerPath}")
                        // 进入考勤主页面 /work/AttendanceHomeActivity
                        ARouter.getInstance().build(param.routerPath)
                            .withString(ConsKeys.COMPANY_ID, currentOrg!!.companyId)
                            .navigation()
                    }
                }
                COMMON_WEB_ACTIVITY.plus("_health") -> {//点击健康上报
                    val ceshi="https://mbos.kdeascloud.com/mbos/page/loadPage?appid=10036&eid=K172K145K107K101K98&path=shrlightapp&name=hrserver.navui&token=15833923837"
                    ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                        .withString(ConsKeys.COMPANY_ID, currentOrg!!.companyId)
//                        .withInt("backspaceKey",1)//测试
                        .withString(
                            ConsKeys.PARAMS_URL,
                            LinkBuilder.generate().getHealthReportUrl()
//                            LinkBuilder.generate().getInteGrateUrl(ceshi,needAddEndTag = false)//测试
                        )
                        .navigation()
                }
                //整合动态入口（添加入口不用发版）点击入口
                COMMON_WEB_ACTIVITY->{
                  val builder=  ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                        .withString(ConsKeys.COMPANY_ID, currentOrg!!.companyId)
                        .withString("titleName", param.name)
                        .withInt("backspaceKey",param.backspaceKey)
                        .withString(
                            ConsKeys.PARAMS_URL,
                            if(param.key=="process"||param.key=="kingdee"||param.kingdeeTokenType==2 ){//金蝶审批或者流程入口的地址
                                LinkBuilder.generate().getInteGrateUrl(param.webUrl,needAddEndTag = false)
                            }else{

                                if (param.kingdeeTokenType == 3){
                                    LinkBuilder.generate().getInteGrateUrl(param.webUrl,needAddEndTag = true)
                                }else{
                                    LinkBuilder.generate().getInteGrateUrl(param.webUrl,needAddEndTag = false)
                                }

                            }
                        )

                    if (param.kingdeeTokenType == 2) {
                        builder.withString("plusToken", "yes")
                    }else{
                        builder.withString("plusToken", "no")
                    }
                    if (param.kingdeeTokenType == 3) {
                        val phone=UserHolder.getCurrentUser()?.mobile

                        val phonePlusUrl = param.webUrl.plus(phone)
                        builder.withString(
                            ConsKeys.PARAMS_URL,
                            LinkBuilder.generate().getInteGrateUrl(phonePlusUrl,needAddEndTag = true)
                        )

                    }else if (param.kingdeeTokenType == 2){
                        builder.withString(
                            ConsKeys.PARAMS_URL,
                            LinkBuilder.generate().getInteGrateUrl(param.webUrl,needAddEndTag = false)
                        )
                    }else{
                        builder.withString(
                            ConsKeys.PARAMS_URL,
                            LinkBuilder.generate().getInteGrateUrl(param.webUrl,needAddEndTag = true)
                        )
                    }


                    Log.i("moon" , ">>>>>builder.uri = ${builder.uri}")
                    Log.i("moon" , ">>>>>param.kingdeeTokenType = ${param.kingdeeTokenType}")
                    builder.navigation()
                }


               /* COMMON_WEB_ACTIVITY.plus("_shopping") -> {//点击商城
                    ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                        .withString(ConsKeys.COMPANY_ID, currentOrg!!.companyId)
                        .withString(
                            ConsKeys.PARAMS_URL,
//                            LinkBuilder.generate().getShoppingUrl()
                            LinkBuilder.generate().getShoppingUrl(currentOrg!!.companyId)
                        )
                        .navigation()
                }*/
               /* COMMON_WEB_ACTIVITY.plus("_visitor") -> {//访客系统，点击入口
                    ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                        .withString(ConsKeys.COMPANY_ID, currentOrg!!.companyId)
                        .withString(
                            ConsKeys.PARAMS_URL,
//                            LinkBuilder.generate().getShoppingUrl()
                            LinkBuilder.generate().getVisitorUrl(currentOrg!!.companyId)//访客系统入口地址
                        )
                        .navigation()
                }*/
              /*  COMMON_WEB_ACTIVITY.plus("_workorder") -> {//点击工单管理
                      ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                          .withString(ConsKeys.COMPANY_ID, currentOrg!!.companyId)
                          .withString(ConsKeys.PARAMS_URL,   LinkBuilder.generate().getWorkOrderUrl(currentOrg!!.companyId))
                          .navigation()
//                    ToastUtil.show(mActivity, "暂未开通")
                }*/
               /* COMMON_WEB_ACTIVITY.plus("_handover") -> {//点击交接班管理
                    ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                        .withString(ConsKeys.COMPANY_ID, currentOrg!!.companyId)
                        .withString(
                            ConsKeys.PARAMS_URL,
                            LinkBuilder.generate().getHandOverUrl(currentOrg!!.companyId)
                        )
                        .navigation()
//                    ToastUtil.show(mActivity, "暂未开通")
                }*/
               /* COMMON_WEB_ACTIVITY.plus("_king") -> {
                    val targetUrl = LinkBuilder.generate().getKingDeeUrl(currentOrg!!.companyId)
                    if (!targetUrl.isNullOrBlank()) {
                        showLoading()
                        ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                            .withString(ConsKeys.COMPANY_ID, currentOrg!!.companyId)
                            .withString(ConsKeys.PARAMS_URL, targetUrl)
                            .navigation(mActivity, object : NavigationCallback {
                                override fun onLost(postcard: Postcard?) {
                                }

                                override fun onFound(postcard: Postcard?) {
                                }

                                override fun onInterrupt(postcard: Postcard?) {
                                    showLog("已拦截，可以关闭loading了")
                                    hideLoading()
                                }

                                override fun onArrival(postcard: Postcard?) {
                                    showLog("已到达，可以关闭loading了")
                                    hideLoading()
                                }
                            })
                    }
                }*/
                else -> {
                    //点击工作汇报，音视频会议
                    ARouter.getInstance().build(param.routerPath)
                        .withString(ConsKeys.COMPANY_ID, currentOrg!!.companyId)
                        .navigation()
                }
            }
        }
    }

    /**-----------------------------数据处理-----------------------------**/
    private fun showNotice(noticeInfo: String = "") {
        if (StringUtils.isNotBlankAndEmpty(noticeInfo)) {
            tvNoticeTitle.text = noticeInfo
            tvNoticeTitle.ellipsize = TextUtils.TruncateAt.MARQUEE
            tvNoticeTitle.setSingleLine(true)
            tvNoticeTitle.isSelected = true
            tvNoticeTitle.isFocusable = true
            tvNoticeTitle.isFocusableInTouchMode = true
            noticeMoreOrCreate.visibility = View.VISIBLE
            tvNoticeMoreOrCreate.text = "更多"
        } else {
            tvNoticeTitle.text = ""

            tvNoticeMoreOrCreate.text = "立即创建"

            if (currentOrg != null && (currentOrg!!.power.contains(ORG_PERMISS_TYPE.ORG_PERMISSION)
                        || ORG_PERMISS_TYPE.checkSuperPermission(currentOrg!!.power))
            ) {//判断权限，显示创建通知入口
                noticeMoreOrCreate.visibility = View.VISIBLE
            } else {
                noticeMoreOrCreate.visibility = View.GONE
            }
        }
    }

    //六合一接口成功之后更新banner
    private fun updateBanner(bean: Banner) {
        showLog("更新banner信息")
        val url = bean.picture
        val option = RequestOptions()
            .error(R.drawable.icon_bg_default_work)
            .placeholder(R.drawable.icon_bg_default_work)
        ImageLoaderUtils.showImgWithOption(mActivity, url, topBannerBackground, option)
        if (!bean.url.isNullOrBlank()) {
            topBannerBackground.setOnClickListener {
                ARouter.getInstance()
                    .build(RouteWork.ExternalWebActivity)
                    .withString("targetUrl", bean.url)
                    .withString("title", bean.name ?: "")
                    .navigation()
            }
        } else {
            topBannerBackground.setOnClickListener(null)
        }
    }


    //tcp改版新增
    private fun updateApplicationsData(companyId: String): WorkGroupData {

        val totalCompanys=CompanyHolder.getTotalCompanies()
        val undoCount=totalCompanys.find { it.companyId==companyId }?.haveApprove?:0

        val list = arrayListOf(
            WorkEntrance(
                defaultIcon = R.drawable.icon_atten_2,
                name = "考勤", routerPath = RouteAte.attendanceHomeActivity
            ),//点击考勤tcp,本页内搜索RouteAte.attendanceHomeActivity可找到新旧版本切换位置
            WorkEntrance(
                defaultIcon = R.drawable.icon_approval_work_2,
                name = "审批", routerPath = RouteApr.APR_FUNC_PAGE,
                badge = if (undoCount>0) 1 else 0
            ),
            WorkEntrance(
                defaultIcon = R.drawable.icon_video_conference_2,
                name = "音视频会议", routerPath = RouteVc.VC_LIST_PAGE
            ),
            WorkEntrance(
                defaultIcon = R.drawable.icon_work_report_2,
                name = "工作汇报", routerPath = RouteWork.reportListActivity
            )
        )

        if (isDebug) {
            list.add( WorkEntrance(
                defaultIcon = R.drawable.ic_health_report_2,
                name = "健康上报", routerPath = COMMON_WEB_ACTIVITY.plus("_health")
            ))
        }

        //整合接口==第四步：liveDate发生变化后会执行下面的代码tcp
        //整合成添加入口不用发版的动态添加入口
//        val entroyList = entroyListData.value ?: arrayListOf()

        val entroyList = arrayListOf<EntroyBean>()
        entroyList.addAll(UserHolder.getEntroyListMap().get(currentOrg?.companyId)?: arrayListOf())
        Loggerr.i("切换公司测试", "==正在刷新UI==entroyList=${GsonUtil.toJson(entroyList)}===")

        if(!entroyList.isNullOrEmpty()){
            entroyList.forEach {
                list.add(
                    //badge大于0则显示未读数红点，showTag为true则显示new标签
                    WorkEntrance(
                        defaultIcon = 0,
                        iconUrl=it.icon,
                        name = it.name, routerPath = COMMON_WEB_ACTIVITY,
                        badge = if (it.unreadInt >= 1) 1 else 0,
                        showTag = if (it.showInt == 0) {
                            false
                        } else {
                            true
                        },
                        webUrl = it.webUrl,
                        backspaceKey = it.backspaceKey,
                        key = it.key,
                        refreshTime = it.refreshTime,
                        kingdeeTokenType = it.kingdeeTokenType
                    )
                )
                //将首页金蝶入口的地址存储起来，方便在金蝶审批列表中直接使用（浮动按钮使用）
                if (it.key == "process") {
                    UserHolder.saveKingdeeEntroyBean(it)
                }
            }
        }


        return WorkGroupData(groupName = "全部应用", entrances = list)

    }

    //刷新权限列表，团队管理tcp
    private fun updateEntrance(currentCompanyId: String = "", permission: String = "") {
        showLog("更新各功能入口信息 >$currentCompanyId - $permission<")
        //缓存权限
        BaseApplication.totalPermission = permission
        Logger.i("当前权限", "==BaseApplication.totalPermission==" + permission)
        workListData.clear()
        if (StringUtils.isNotBlankAndEmpty(currentCompanyId)) {
            val managerEnt = arrayListOf<WorkEntrance>()
            if (StringUtils.isNotBlankAndEmpty(permission)) {
                val hasSuperPermission = ORG_PERMISS_TYPE.checkSuperPermission(permission)
                if (hasSuperPermission || permission.contains(ORG_PERMISS_TYPE.ORG_PERMISSION)) {
//                    managerEnt.add(WorkEntrance(defaultIcon = R.drawable.icon_group_set_work, iconUrl = "", name = "管理成员", routerPath = orgChartActivity))
                    managerEnt.add(
                        WorkEntrance(
                            defaultIcon = R.drawable.ic_member_manage_2, iconUrl = "",
                            name = "管理成员", routerPath = RouteOrg.orgChartActivity
                        )
                    )
                }
                if (hasSuperPermission || permission.contains(ORG_PERMISS_TYPE.ATTENDANCE_PERMISSION)) {
//                    managerEnt.add(WorkEntrance(defaultIcon = R.drawable.icon_attendance_set_work, iconUrl = "", name = "考勤设置", routerPath = attendanceSetActivity))
                    managerEnt.add(
                        WorkEntrance(
                            defaultIcon = R.drawable.ic_attendance_2, iconUrl = "",
                            name = "考勤设置",//首页点击考勤设置tcp
                            routerPath = if (isFuture && isAttendance) {// TODO: 2021/3/18 16:28 考勤班次版本未发布前 保持原来功能使用
                                RouteAte.attendanceOrderListActivity
                            } else {
                                RouteAte.attendanceSetActivity
                            }
                        )
                    )
                }
                if (hasSuperPermission || permission.contains(ORG_PERMISS_TYPE.APPROVAL_PERMISSION)) {
//                    managerEnt.add(WorkEntrance(defaultIcon = R.drawable.icon_approval_set_work, iconUrl = "", name = "审批设置", routerPath = APR_MODEL))
                    managerEnt.add(
                        WorkEntrance(
                            defaultIcon = R.drawable.ic_approval_2,
                            name = "审批设置", routerPath = RouteApr.APR_MODEL
                        )
                    )
                }
                if (hasSuperPermission || permission.contains(ORG_PERMISS_TYPE.DIGITAL_REPORT)) {
//                    managerEnt.add(WorkEntrance(defaultIcon = R.drawable.icon_notice_work_report, iconUrl = "", name = "企业数字报告", routerPath = companyDigitalReportActivity))
                    managerEnt.add(
                        WorkEntrance(
                            defaultIcon = R.drawable.ic_digital_report_2,
                            name = "企业数字报告", routerPath = RouteWork.companyDigitalReportActivity
                        )
                    )
                }
            }
            // 团队管理
            if (managerEnt.isNotEmpty()) {
                workListData.add(
                    WorkGroupData(
                        groupName = "团队管理",
                        type = 1,
                        entrances = managerEnt
                    )
                )
            }
            // 团队功能
            /* workListData.add(updateWorkData(currentCompanyId))
             workListData.add(updateToolsData(currentCompanyId))*/
            workListData.add(updateApplicationsData(currentCompanyId))//修改样式，添加入口
        }
        groupAdapter.notifyDataSetChanged()
    }

    private fun updateLeftRedDot() {
        // 头部审批红点
        val entroyList=UserHolder.getEntroyListMap().get(currentOrg?.companyId)
        val count = if (!entroyList.isNullOrEmpty()) {
            entroyList.find { it.key=="kingdee" }?.unreadInt?:0
        } else 0

        val dotDismiss = count == 0 && companyIdToUnreadMap.isNullOrEmpty()
        if (dotDismiss) {
            ivLeftDot.visibility = View.GONE
        } else {
            ivLeftDot.visibility = View.VISIBLE
        }
    }

    override fun onAction(actionCode: Int) {
        when (actionCode) {
            1 -> {
                ARouter.getInstance()
                    .build(RouteOrg.createOrgActivity)
                    .navigation()
            }
            2 -> {
                ARouter.getInstance()
                    .build(RouteOrg.searchResultActivity)
                    .withString("type", "searchOrg")
                    .navigation()
            }
            else -> {
                EventBusUtils.sendEvent(EventBusEvent(EventBusAction.Event_GET_HOME_DATA, ""))
            }
        }
    }
}

/**
 * todo 20200327 工作首页业务逻辑：
 *  标题刷新：切换公司时onCompanyChanged()
 *  左侧红点：所有团队中存在审批信息时显示-》所有团队信息更新时刷新onApprovalUpdate()
 *  头部banner刷新，只是token获取，未做团队关联 getBannerData
 *  公告信息刷新：团队切换后刷新团队公告接口 onCompanyChanged()
 *  操作功能入口：切换公司时，获取当前公司权限，并刷新列表onCompanyChanged()
 *  审批信息更新时，刷新审批入口红点onApprovalUpdate()
 *
 * */
data class WorkGroupData(
    val groupName: String,
    /**
     * 0 普通功能模块入口
     * 1 涉及权限相关模块
     * */
    val type: Int = 0,
    val entrances: List<WorkEntrance>
)

data class WorkEntrance(
    val defaultIcon: Int, val iconUrl: String = "",
    val name: String, val routerPath: String,
    var badge: Int = 0, var showTag: Boolean = false,
    var webUrl:String="",var backspaceKey:Int=0,var key:String="",var refreshTime:Long=0L,
   var  kingdeeTokenType:Int=0
)