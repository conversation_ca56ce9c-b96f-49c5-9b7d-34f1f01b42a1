package com.jounutech.work.util

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.view.Gravity
import android.view.View
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.animation.AnimationSet
import android.view.animation.OvershootInterpolator
import android.view.animation.RotateAnimation
import android.view.animation.TranslateAnimation
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.bumptech.glide.request.RequestOptions
import com.haibin.calendarview.Calendar
import com.haibin.calendarview.CalendarView
import com.joinutech.ddbeslibrary.bean.AttendanceHolidayBean
import com.joinutech.ddbeslibrary.bean.RecordBean
import com.joinutech.ddbeslibrary.utils.BottomDialogUtil
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.OnNoDoubleClickListener
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.jounutech.work.R
import com.jounutech.work.view.attend.AttendanceResultShareActivity
import com.jounutech.work.view.attend.manage.AttendanceSetHomeActivity
import com.marktoo.lib.cachedweb.LogUtil
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.observers.DisposableObserver
import java.util.concurrent.TimeUnit


object AttendanceUtil2 {
    const val ATTENDANCE_PRESENTER = "attendance_presenter"
    val NON_WIFI = arrayOf("未获取到WIFI信息", "未连接考勤WiFi", "<unknown ssid>")
    val NON_ADDR = arrayOf("未获取到位置信息", "无法获取位置信息")
    private var dateListener: AttendanceUtil.DateInterface? = null
    fun setDateListener(listener: AttendanceUtil.DateInterface) {
        dateListener = listener
    }

    //迟到
    fun showLateDialog(mActivity: Activity, onConfirm: (note: String) -> Unit) {
        val view1 = View.inflate(mActivity, R.layout.dialog_belate, null)
        val cancle = view1.findViewById<TextView>(R.id.cancle_belate)
        val confirm = view1.findViewById<TextView>(R.id.confirm_belate)
        val et = view1.findViewById<EditText>(R.id.et_belate)
        val dialog = BottomDialogUtil.showBottomDialog(mActivity, view1, Gravity.CENTER)
        cancle.setOnClickListener { dialog.dismiss() }
        confirm.setOnClickListener {
            val note = et.text.toString()
            dialog.dismiss()
            onConfirm.invoke(note)
        }
    }

    //早退或外勤
    /**@param status 3早退，4外勤*/
    @SuppressLint("SetTextI18n")
    fun showEarlyDialog(mActivity: Activity, status: Int, address: String,
                        onConfirm: (note: String) -> Unit) {
        val view1 = View.inflate(mActivity, R.layout.dialog_early, null)
        val cancle = view1.findViewById<TextView>(R.id.cancle_early)
        val confirm = view1.findViewById<TextView>(R.id.confirm_early)
        val et = view1.findViewById<EditText>(R.id.et_early)
        val tip = view1.findViewById<ImageView>(R.id.iv_early)
        val tv1 = view1.findViewById<TextView>(R.id.tv1_early)
        val tv2 = view1.findViewById<TextView>(R.id.tv2_early)
        if (status == 4) {
            et.hint = "请输入外勤原因(也可打卡后再更新)"
            tip.setImageDrawable(mActivity.resources.getDrawable(R.drawable.icon_addr))
            tv1.text = address
            tv2.text = "未进入考勤区域,未连接到打卡wifi"
            confirm.text = "外勤打卡"
        }
        val dialog = BottomDialogUtil.showBottomDialog(mActivity, view1, Gravity.CENTER)
        cancle.setOnClickListener { dialog.dismiss() }
        confirm.setOnClickListener {
            dialog.dismiss()
            val note = et.editableText?.toString()
            onConfirm.invoke(note ?: "")
        }

    }

    fun showUpdateDialog(mActivity: Activity, record: RecordBean, supportData: SupportData, onConfirm: (note: String) -> Unit) {
        val view1 = View.inflate(mActivity, R.layout.dialog_update, null)
        val addrTv = view1.findViewById<TextView>(R.id.addr_update)
        val addr_layout = view1.findViewById<LinearLayout>(R.id.addr_update_layout)
        val timeTv = view1.findViewById<TextView>(R.id.time_update)
        val wifiTv = view1.findViewById<TextView>(R.id.wifi_update)
        val wifi_layout = view1.findViewById<LinearLayout>(R.id.wifi_update_layout)
        val confirm = view1.findViewById<TextView>(R.id.confirm_update)
        val cancle = view1.findViewById<TextView>(R.id.cancle_update)
        val desc = view1.findViewById<TextView>(R.id.desc_update)
        if (supportData.wifiState != 1 && supportData.locationState != 1) {
            desc.text = "未进入考勤区域"
        }
        timeTv.text = supportData.time
        if (StringUtils.isNotBlankAndEmpty(supportData.address) && record.location !in NON_ADDR) {
            addrTv.text = supportData.address
        } else {
            addr_layout.visibility = View.GONE
        }
        if (StringUtils.isNotBlankAndEmpty(supportData.wifiName) && record.wifi !in NON_WIFI) {
            wifiTv.text = supportData.wifiName
        } else {
            wifi_layout.visibility = View.GONE
        }
        val dialog = BottomDialogUtil.showBottomDialog(mActivity, view1, Gravity.CENTER)
        confirm.setOnClickListener {
            dialog.dismiss()
//            if (supportData.isWorkDay) {
//                when (supportData.attendStatus) {
//                    0, 1 -> onConfirm.invoke("")
//                    2 -> {
//                        showLateDialog(mActivity, onConfirm)
//                    }
//                    3 -> {
//                        showEarlyDialog(mActivity, 3, supportData.address, onConfirm)
//                    }
//                    4 -> {
//                        showEarlyDialog(mActivity, 4, supportData.address, onConfirm)
//                    }
//                }
//            } else {
//                onConfirm.invoke("")
//            }
            onConfirm.invoke("")
        }
        cancle.setOnClickListener { dialog.dismiss() }
    }

    //查看原因
    fun showNote(mActivity: Activity, record: RecordBean, onConfirm: (note: String) -> Unit) {
        val view1 = View.inflate(mActivity, R.layout.dialog_checknote, null)
        val title = view1.findViewById<TextView>(R.id.title_note)
        val timeTv = view1.findViewById<TextView>(R.id.time_note)
        val tv_addr = view1.findViewById<TextView>(R.id.tv_addr)
        val addrTv = view1.findViewById<TextView>(R.id.addr_note)
        val tv_wifi = view1.findViewById<TextView>(R.id.tv_wifi)
        val wifiTv = view1.findViewById<TextView>(R.id.wifi_note)
        val desc1 = view1.findViewById<TextView>(R.id.desc1_note)
        val desc2 = view1.findViewById<TextView>(R.id.desc2_note)
        val textTv = view1.findViewById<TextView>(R.id.text_note)
        val close = view1.findViewById<TextView>(R.id.close_note)
        when (record.status) {
            2 -> {
                title.text = "迟到原因"
                desc1.text = "迟到原因"
                desc2.text = "更新迟到原因"
            }
            3 -> {
                title.text = "早退原因"
                desc1.text = "早退原因"
                desc2.text = "更新早退原因"
            }
            4 -> {
                title.text = "外勤原因"
                desc1.text = "外勤原因"
                desc2.text = "更新外勤原因"
            }
        }
        timeTv.text = record.createTime

        if (StringUtils.isNotBlankAndEmpty(record.note)) {
            textTv.text = record.note ?:""
        }

        if (StringUtils.isNotBlankAndEmpty(record.wifi) && record.wifi !in NON_WIFI) {
            wifiTv.text = record.wifi
        } else {
            tv_wifi.visibility = View.GONE
            wifiTv.visibility = View.GONE
        }

        if (StringUtils.isNotBlankAndEmpty(record.location) && record.location !in NON_ADDR) {
            addrTv.text = record.location
        } else {
            tv_addr.visibility = View.GONE
            addrTv.visibility = View.GONE
        }

        val dialog = BottomDialogUtil.showBottomDialog(mActivity, view1, Gravity.CENTER)
        close.setOnClickListener { dialog.dismiss() }
        desc2.setOnClickListener {
            dialog.dismiss()
            showUpdateNoteDialog(mActivity, record, onConfirm)
        }
    }

    //更新原因
    private fun showUpdateNoteDialog(mActivity: Activity, record: RecordBean, onConfirm: (note: String) -> Unit) {
        val view1 = View.inflate(mActivity, R.layout.dialog_updatenote, null)
        val title = view1.findViewById<TextView>(R.id.title_upnote)
        val et = view1.findViewById<EditText>(R.id.et_upnote)
        val cancle = view1.findViewById<TextView>(R.id.cancle_upnote)
        val commit = view1.findViewById<TextView>(R.id.confirm_upnote)
        et.setText(record.note)
        when (record.status) {
            2 -> title.text = "更新迟到原因"
            3 -> title.text = "更新早退原因"
            4 -> title.text = "更新外勤原因"
        }
        val dialog = BottomDialogUtil.showBottomDialog(mActivity, view1, Gravity.CENTER)
        cancle.setOnClickListener { dialog.dismiss() }
        commit.setOnClickListener {
            dialog.dismiss()
            onConfirm.invoke(et.text.toString())
        }
    }

    //打卡成功
    fun showPunchDialog(mActivity: Activity, state: Int, time: String, wifi: String,
                        addr: String, isWorkDay: Boolean = true) {
        val view1 = View.inflate(mActivity, R.layout.dialog_punch, null)
        val addrTv = view1.findViewById<TextView>(R.id.addr_punch)
        val addr_layout = view1.findViewById<LinearLayout>(R.id.addr_punch_layout)
        val timeTv = view1.findViewById<TextView>(R.id.time_punch)
        val desc = view1.findViewById<TextView>(R.id.desc_punch)
        val wifiTv = view1.findViewById<TextView>(R.id.wifi_punch)
        val wifi_layout = view1.findViewById<LinearLayout>(R.id.wifi_punch_layout)
        timeTv.text = time
        if (addr == "") {
            addr_layout.visibility = View.GONE
        } else {
            addr_layout.visibility = View.VISIBLE
            addrTv.text = addr
        }

        if (wifi == "") {
            wifi_layout.visibility = View.GONE
        } else {
            wifiTv.text = wifi
            wifi_layout.visibility = View.VISIBLE
        }

        desc.text = (if (isWorkDay) "" else "休息日").plus(when (state) {
            2 -> "迟到打卡"
            3 -> "早退打卡"
            4 -> "外勤打卡"
            else -> "正常打卡"
        })
        val star1 = view1.findViewById<ImageView>(R.id.star1)
        val star2 = view1.findViewById<ImageView>(R.id.star2)
        val star3 = view1.findViewById<ImageView>(R.id.star3)
        val round = view1.findViewById<ImageView>(R.id.dialog_round_punch)
        val close = view1.findViewById<TextView>(R.id.dialog_close_punch)
        if (state == 2 || state == 3) {//迟到或者早退
            round.setImageDrawable(mActivity.resources.getDrawable(R.drawable.atten_c1))
            close.background = mActivity.resources.getDrawable(R.drawable.atten_range2)
        }
        val dialog = BottomDialogUtil.showBottomDialog(mActivity, view1, Gravity.CENTER)
        close.setOnClickListener {
            dialog.dismiss()
        }
        val aphla = AlphaAnimation(0.5f, 1.0f)
        aphla.duration = 1500
        round.startAnimation(aphla)
        val rotateAnim = RotateAnimation(0f, 180f, Animation.RELATIVE_TO_SELF,
                0.5f, Animation.RELATIVE_TO_SELF, 0.5f)
        val transAnim1 = TranslateAnimation(60f, 0f, 0f, 0f)
        val transAnim2 = TranslateAnimation(-60f, 0f, 0f, 0f)
        val set1 = AnimationSet(true)
        set1.addAnimation(transAnim1)
        set1.addAnimation(rotateAnim)
        set1.interpolator = OvershootInterpolator()
        set1.duration = 3000
        val set2 = AnimationSet(true)
        set2.addAnimation(transAnim2)
        set2.addAnimation(rotateAnim)
        set2.interpolator = OvershootInterpolator()
        set2.duration = 3000
        star1.startAnimation(set1)
        star2.startAnimation(set1)
        star3.startAnimation(set2)
    }

    //打卡成功
    fun showPunchDialogNew(mActivity: Activity, state: Int, time: String, wifi: String,
                           addr: String, isWorkDay: Boolean = true,
                           sentence: String?, author: String?, url: String?) {
        val view1 = View.inflate(mActivity, R.layout.dialog_punch_new, null)
        val clockTv = view1.findViewById<TextView>(R.id.clockTv)
        val locationTv = view1.findViewById<TextView>(R.id.locationTv)
        val wifiTv = view1.findViewById<TextView>(R.id.wifiTv)
        val statusTv = view1.findViewById<TextView>(R.id.statusTv)
        val locationLayout = view1.findViewById<ConstraintLayout>(R.id.locationLayout)
        val wifiLayout = view1.findViewById<ConstraintLayout>(R.id.wifiLayout)
        val round = view1.findViewById<ImageView>(R.id.dialog_round_punch)
        val cancle = view1.findViewById<ImageView>(R.id.cancle)
        val shareLayout = view1.findViewById<View>(R.id.shareLayout)
        val resultContent = view1.findViewById<TextView>(R.id.resultContent)
        if (StringUtils.isNotBlankAndEmpty(sentence)) resultContent.text = sentence
        val resultName = view1.findViewById<TextView>(R.id.resultName)
        if (StringUtils.isNotBlankAndEmpty(author)) resultName.text = author
        val resultImage = view1.findViewById<ImageView>(R.id.resultImage)
        val option = RequestOptions()
                .error(com.joinutech.ddbeslibrary.R.drawable.attendance_share_result_bg)
                .placeholder(com.joinutech.ddbeslibrary.R.drawable.attendance_share_result_bg)
        if (StringUtils.isNotBlankAndEmpty(url)) {
            ImageLoaderUtils.showImgWithOption(mActivity, url, resultImage, option)
        } else {
            ImageLoaderUtils.showImgWithOption(mActivity,
                    "http://cdn.ddbes.com/LOGO/recordShare/%E5%9B%BE%E5%B1%82%201.png", resultImage, option)
        }
        if (StringUtils.isNotBlankAndEmpty(addr)) {
            locationLayout.visibility = View.VISIBLE
            locationTv.text = addr
        } else {
            locationLayout.visibility = View.GONE
        }
        if (StringUtils.isNotBlankAndEmpty(wifi)) {
            wifiLayout.visibility = View.VISIBLE
            wifiTv.text = wifi
        } else {
            wifiLayout.visibility = View.GONE
        }
        clockTv.text = time
        statusTv.text = (if (isWorkDay) "" else "休息日").plus(when (state) {
            2 -> "迟到打卡"
            3 -> "早退打卡"
            4 -> "外勤打卡"
            else -> "正常打卡"
        })
        if (state == 2 || state == 3) {//迟到或者早退
            round.setImageDrawable(mActivity.resources.getDrawable(R.drawable.atten_c1))
        }
        val dialog = BottomDialogUtil.showBottomDialog(mActivity, view1,
                Gravity.CENTER, alpha = 0.4f)
        cancle.setOnClickListener {
            dialog.dismiss()
        }
        val rootLayout = view1.findViewById<RelativeLayout>(R.id.cl_root_layout)
        rootLayout.setOnClickListener {
            dialog.dismiss()
        }
        //点击考勤分享tcp，点击分享
        shareLayout.setOnClickListener(object : OnNoDoubleClickListener {
            override fun onNoDoubleClick(v: View) {
                dialog.dismiss()
                val intent = Intent(mActivity, AttendanceResultShareActivity::class.java)
                intent.putExtra("resultImage", if (StringUtils.isNotBlankAndEmpty(url)) {
                    url
                } else {
                    "http://cdn.ddbes.com/LOGO/recordShare/%E5%9B%BE%E5%B1%82%201.png"
                })
                intent.putExtra("resultContent", sentence)
                intent.putExtra("resultName", author)
                intent.putExtra("attendanceResultTime", time)
                mActivity.startActivity(intent)
            }

        })
    }

    //日期选择
    @SuppressLint("SetTextI18n")
    fun selectExportDateNew(mContext: AttendanceSetHomeActivity,
                            date: String, startTime: TextView,
                            endTime: TextView, type: String) {
        val view = View.inflate(mContext, R.layout.dialog_attendance_export_date_new, null)
        val dialog = BottomDialogUtil.showBottomDialog(mContext, view, Gravity.BOTTOM)
        view.findViewById<TextView>(R.id.cancel).setOnClickListener {
            dialog.dismiss()
        }
        val calendarView = view.findViewById<CalendarView>(R.id.calendarView)
        val dateText = view.findViewById<TextView>(R.id.dateYearMonth)
        val split = date.split("-")
        val curYear = calendarView.curYear
        val curMonth = calendarView.curMonth
        val curDay = calendarView.curDay
        var year = split[0].toInt()
        var month = split[1].toInt()
        var day = split[2].toInt()
        dateText.text = "${year}年 ${month}月"
        calendarView.scrollToCalendar(year, month, day, true)
        view.findViewById<ImageView>(R.id.datePrev).setOnClickListener {
            calendarView.scrollToPre(true)
        }
        view.findViewById<ImageView>(R.id.dateNext).setOnClickListener {
            calendarView.scrollToNext(true)
        }
        calendarView.setOnCalendarSelectListener(object : CalendarView.OnCalendarSelectListener {
            override fun onCalendarOutOfRange(calendar: Calendar?) {}

            override fun onCalendarSelect(calendar: Calendar, isClick: Boolean) {
                day = calendar.day
                if (year != calendar.year || month != calendar.month) {
                    year = calendar.year
                    month = calendar.month
                    dateText.text = "${year}年 ${month}月"
                }
            }

        })
        view.findViewById<TextView>(R.id.confirm).setOnClickListener {
            dialog.dismiss()
            val selectDate = year.toString().plus("-")
                    .plus(if (month < 10) {
                        "0$month"
                    } else month)
                    .plus("-").plus(if (day < 10) {
                        "0$day"
                    } else day)
            if ((year > curYear) || (year == curYear && month > curMonth) || (
                            year == curYear && month == curMonth &&
                                    day > curDay
                            )) {
                ToastUtil.show(mContext, "当前日期不可导出")
            } else {
                if (type == "start") {
                    startTime.text = selectDate
                    endTime.text = CommonUtils.calTimeBacktDate(startTime.text.toString(), 30)
                } else {
                    endTime.text = selectDate
                    startTime.text = CommonUtils.calTimeFrontDate(endTime.text.toString(), -30)
                }
            }
        }
        dialog.show()
    }

    fun getMap(list: List<AttendanceHolidayBean>?): HashMap<String, Calendar> {
        val map = hashMapOf<String, Calendar>()
        if (list != null && list.isNotEmpty()) {
            for (record in list) {
                if (StringUtils.isNotBlankAndEmpty(record.date) && record.date!!.contains("-")) {
                    val calendar = Calendar()
                    val info = record.date!!.split("-")
                    if (info != null && info.size >= 3) {
                        calendar.year = info[0].toInt()
                        calendar.month = info[1].toInt()
                        calendar.day = info[2].toInt()
//                        if (record.status == 0) {
//                            // 休息日
//                        } else {
                        // 非休息日
                        if (record.clockStatus == 2) {
//                            LogUtil.showLog("打卡异常日期： ${calendar.year} - ${calendar.month} - ${calendar.day}")
                            calendar.scheme = record.clockStatus.toString()
                            map[calendar.toString()] = calendar
                        }
//                        }
                    }
                }
            }
        }
        return map
    }

    /**
     * state
     * 1 打开应用设置 获取wifi权限
     * 2 WiFi设置
     * 3 打开应用设置 获取定位权限
     * 4 打开GPS
     * 5 刷新，重新定位授权或获取WiFi信息
     *
     * */
    fun showNonPunchDialog(mActivity: Activity, supportData: SupportData, onConfirm: (state: Int) -> Unit) {
        val view1 = View.inflate(mActivity, R.layout.dialog_nonpunch, null)
        val reason = view1.findViewById<TextView>(R.id.tv_reason)
        val cancle = view1.findViewById<TextView>(R.id.cancle_non)
        val confirm = view1.findViewById<TextView>(R.id.confirm_non)
        val dialog = BottomDialogUtil.showBottomDialog(mActivity, view1, Gravity.CENTER)
        var confirmState = 0
        var reasonText = ""

        if (supportData.locationState == -2) { // 有权限，未打开GPS或未获取到定位信息
            if (supportData.isNeedWifi) { // 需要wifi信息时
                confirmState = when (supportData.wifiState) {
                    -1 -> // 打开WiFi权限 应用设置
                        1
                    0, -2 -> // 打开wifi或者连接指定考勤wifi
                        2/*else if(supportData.wifiState==-2){
                                            // 重新获取WiFi设置

                                        }*/
                    else -> {// 重新获取wifi信息 刷新页面，重新获取WiFi信息
                        5
                    }
                }
                confirm.text = "好的"
                reasonText = "请检查网络连接并尝试重新获取您的位置；或将手机连接考勤WiFi。否则您无法正常打卡"
            } else if (supportData.locationState == -3) { // 开启权限后未打开GPS
                confirmState = 4
                confirm.text = "好的"
                reasonText = "请打开定位功能，允许担当获取您的位置"
            } else { // 不需要wifi信息时，重新获取定位信息
                confirmState = 5
                confirm.text = "好的"
                reasonText = "请检查网络连接并尝试重新获取您的位置。否则您无法正常打卡"
            }
        } else if (supportData.locationState == -1) { // 未开启定位权限
            if (supportData.isNeedWifi) { // 需要wifi信息时
                confirmState = 3
                confirm.text = "去授权"
                reasonText = "请打开定位权限，允许担当获取您的位置；或将手机连接考勤WiFi。否则您无法正常打卡"
            } else { // 不需要WiFi信息时
                confirmState = 3
                confirm.text = "去授权"
                reasonText = "请打开定位权限，允许担当获取您的位置。否则您无法正常打卡"
            }
        } else if (supportData.locationState == -3) { // 开启权限后未打开GPS
            confirmState = 4
            confirm.text = "好的"
            reasonText = "请打开定位功能，允许担当获取您的位置"
        }

        reason.text = reasonText
        cancle.setOnClickListener { dialog.dismiss() }
        confirm.setOnClickListener {
            dialog.dismiss()
            LogUtil.showLog("confirmState = $confirmState")
            onConfirm.invoke(confirmState)
        }
    }

    interface DateInterface {
        fun getDate(date: String)
    }
}

/**
 * 倒计时工具
 */
class TimeUtils private constructor() {

    //默认10S
    private var time: Long = 10L

    private var observable: Observable<Long>? = null

    private var mCompositeDisposable = CompositeDisposable()

    companion object {
        fun getInstance() = Holder.INSTANCE
    }

    private object Holder {
        val INSTANCE = TimeUtils()
    }

    /**
     * interval设置0延迟，每隔一秒发送一条数据
     * take 设置循环time次
     */
    private fun getTimeMeter(time: Long): Observable<Long> {
        return Observable.interval(0, 1, TimeUnit.SECONDS).take(time).map { t -> time - t }
                .observeOn(AndroidSchedulers.mainThread())
    }

    fun start(observable: Observable<Long>, disposable: DisposableObserver<Long>) {
        this.observable = observable
        if (mCompositeDisposable.size() > 0) {
            mCompositeDisposable.clear()
        }
        setObserver(disposable)
    }

    private fun setObserver(disposable: DisposableObserver<Long>) {
        if (observable == null) return
        observable!!.subscribe(disposable)
        mCompositeDisposable.add(disposable)
    }

    fun stop() {
        observable?.let {
            mCompositeDisposable.clear()
        }
    }

}