package com.jounutech.work.adapter

import android.content.Context
import android.util.Log
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import bd09ToWgs84
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.ddbeslibrary.bean.AttendanceLocationBean
import com.joinutech.ddbeslibrary.utils.ATTENDANCE_LOCATION
import com.joinutech.ddbeslibrary.utils.RouteIm
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter
import com.jounutech.work.R
import com.jounutech.work.view.attend.AttendanceRuleActivity
import toNavigate

class AttendanceRuleAdapter(var context: Context, dataList: ArrayList<AttendanceLocationBean>)
    : CommonAdapter<AttendanceLocationBean>(context, dataList, R.layout.item_attendance_rule) {

    private lateinit var type: String

    fun setRuleType(type: String) {
        this.type = type
    }

    override fun bindData(holder: ViewHolder, data: AttendanceLocationBean, position: Int) {
//        if (StringUtils.isNotBlankAndEmpty(data.location)) {
        val iv = holder.getView<ImageView>(R.id.iv)
//        val wifiName = holder.getView<TextView>(R.id.wifiName)
        val name = holder.getView<TextView>(R.id.name)
        val address = holder.getView<TextView>(R.id.address)
        if (type == "location") {
            iv.setImageResource(com.joinutech.ddbeslibrary.R.drawable.icon_rule_location_blue_small)
//            wifiName.visibility = View.GONE
            name.visibility = View.VISIBLE
            address.visibility = View.VISIBLE
            name.text = data.location
            address.text = data.address ?: ""
            holder.setOnItemClickListener {
//                ARouter.getInstance()
//                        .build(RouteIm.locationActivity)
//                        .withDouble("lat", data.lat)
//                        .withDouble("lng", data.lng)
//                        .withString("poiName", data.location ?: "")
//                        .withString("poiAddress", data.address ?: "")
//                        .withString("type", "")
//                        .navigation(mContext as AttendanceRuleActivity, ATTENDANCE_LOCATION)
                val wgs84 = bd09ToWgs84(data.lat,data.lng)
                val poiName = data.location ?: ""
                val poiAddress = data.address ?: ""
                toNavigate(wgs84.lat,wgs84.lon,poiName,poiAddress)
            }
        } else {
            iv.setImageResource(com.joinutech.ddbeslibrary.R.drawable.icon_rule_wifi_blue_small)
//            wifiName.visibility = View.VISIBLE
//            name.visibility = View.GONE
            address.visibility = View.GONE
            name.text = if (data.location.isNullOrBlank()) "未获取到wifi名称" else data.location
            holder.setOnItemClickListener {}
        }
//        }
    }
}