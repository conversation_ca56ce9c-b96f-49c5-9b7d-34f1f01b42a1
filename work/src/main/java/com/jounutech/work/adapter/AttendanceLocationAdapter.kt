package com.jounutech.work.adapter

import android.content.Context
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import bd09ToWgs84
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter
import com.jounutech.work.R
import com.joinutech.ddbeslibrary.bean.AttendanceLocationBean
import toNavigate

/**
 * <AUTHOR> by 黄洁如 on 2019/3/22 11:02
 * @fileName AttendanceLocationAdapter
 * @describe TODO
 * @org 加优科技
 */
class AttendanceLocationAdapter(var context: Context, list: ArrayList<AttendanceLocationBean>)
    : CommonAdapter<AttendanceLocationBean>(context, list, R.layout.item_attendance_location) {

    private var listener: DelListener? = null

    override fun bindData(holder: ViewHolder, data: AttendanceLocationBean, position: Int) {
        if (data.lng != 0.0) {
            holder.setText(R.id.name, data.location ?: "")
            holder.setText(R.id.address, data.address ?: "")
            holder.getView<ImageView>(R.id.del).setOnClickListener { listener?.del(holder.adapterPosition) }
            holder.getView<ConstraintLayout>(R.id.cl_root).setOnClickListener {
                val wgs84 = bd09ToWgs84(data.lat,data.lng)
                val poiName = data.location ?: ""
                val poiAddress = data.address ?: ""
                toNavigate(wgs84.lat,wgs84.lon,poiName,poiAddress)
            }
        }
    }

    fun setDelListener(listener: DelListener) {
        this.listener = listener
    }

    interface DelListener {
        fun del(position: Int)
    }
}