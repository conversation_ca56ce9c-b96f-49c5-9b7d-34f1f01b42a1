package com.ddbes.library.im.imfile.tcpfileutil

import android.Manifest
import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.MediaController
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.PermissionUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.library.im.R
import com.joinutech.library.im.databinding.ActivityVideoPlayingBinding

class VideoPlayingActivity : MyUseBindingActivity<ActivityVideoPlayingBinding>() {
    override val contentViewResId: Int
        get() = R.layout.activity_video_playing

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityVideoPlayingBinding {
        return ActivityVideoPlayingBinding.inflate(layoutInflater)
    }

    companion object {
        fun open(context: Context , url: String) {
            val intent = Intent(context, VideoPlayingActivity::class.java).apply {
                putExtra("videoPath" , url)
            }
            context.startActivity(intent)
        }
    }


    private var isPlayFinished=false
    private var videoPath=""//可能是网络地址或者本地地址
    private var canController=true

    override fun initImmersion() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        setPageTitle("预览")
        videoPath=intent.getStringExtra("videoPath")?:""
        canController=intent.getBooleanExtra("canController",true)
       showVideoLoading()
    }

    private fun showVideoLoading() {
        binding.videoLoadingView.visibility=View.VISIBLE
        binding.videoPauseIv.visibility=View.GONE
    }

    private fun showVideoPause() {
        binding.videoLoadingView.visibility=View.GONE
        binding.videoPauseIv.visibility=View.VISIBLE
    }

    private fun showVideoOnly() {
        binding.videoLoadingView.visibility=View.GONE
        binding.videoPauseIv.visibility=View.GONE
    }

    override fun initView() {

        if (canController) {
            //有控制按钮
            initController()
        }else{
//            没有控制按钮
            binding.videoView.setOnClickListener(this)
        }

        binding.videoView.setOnPreparedListener {
          showVideoOnly()
            binding.videoView.start()
        }

        binding.videoView.setOnCompletionListener {
            binding.videoView.suspend()
           showVideoPause()
            isPlayFinished=true
        }


       playVideo()
    }

    override fun initLogic() {
        super.initLogic()
        binding.videoPauseIv.setOnClickListener(this)
        binding.videoLandscapeIv.setOnClickListener(this)

    }

    private fun playVideo() {
        //播放
        binding.videoView.setVideoURI(Uri.parse(videoPath))
        binding.videoView.requestFocus()
        isPlayFinished=false
    }

    override fun onPause() {
        super.onPause()
        if (binding.videoView.isPlaying) {
            showVideoPause()
            binding.videoView.pause()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        binding.videoView.suspend()
    }
    private fun initController() {
       val controller= MediaController(this)
        binding.videoView.setMediaController(controller)
        controller.setMediaPlayer(binding.videoView)
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return true
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.video_pause_iv->{//点击暂停
                if (isPlayFinished==true) {
                    //已经播放结束了
                   showVideoLoading()
                    playVideo()
                }else{
                    //还没有播放完
                    showVideoOnly()
                    binding.videoView.start()
                }
            }

            R.id.video_view->{//点击视频本身
                if (binding.videoView.isPlaying) {
                    showVideoPause()
                    binding.videoView.pause()
                }
            }
            R.id.video_landscape_iv->{//点击横屏

            }
        }

    }
}