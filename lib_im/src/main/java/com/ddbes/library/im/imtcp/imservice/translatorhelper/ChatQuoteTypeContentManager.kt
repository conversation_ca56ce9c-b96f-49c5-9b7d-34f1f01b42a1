package com.ddbes.library.im.imtcp.imservice.translatorhelper

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.library.im.ext.loadImRecord
import com.ddbes.library.im.imfile.tcpfileutil.DealFileUtil
import com.ddbes.library.im.imtcp.ConstantImMsgType
import com.ddbes.library.im.imtcp.ConstantTcpUtil
import com.ddbes.library.im.imtcp.dbbean.Message
import com.ddbes.library.im.imtcp.dbope.MessageDaoOpe
import com.ddbes.library.im.imtcp.imservice.uploadfilehelper.UpLoadFileUtil
import com.ddbes.library.im.imtcp.imservice.uploadfilehelper.bean.LoadFileResultBean
import com.joinutech.common.FileThumbUtil
import com.joinutech.common.storage.FileUtil
import com.joinutech.ddbeslibrary.bean.ImageMsgParseBean
import com.joinutech.ddbeslibrary.bean.ImageTransMsgBean
import com.joinutech.ddbeslibrary.bean.LocationMessage
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.RouteIm
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.ScreenUtils
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.utils.toast
import com.joinutech.ddbeslibrary.widget.activity.TaskImagePreviewActivity
import com.joinutech.ddbeslibrary.widget.activity.WithToolBarImagePreviewActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.io.Serializable

/**
 * @Des：解析引用类型里的具体类型
 * @author: moon
 * @date: 11/24/23
 */
object ChatQuoteTypeContentManager {

    fun parseQuoteType(responseMsg: Message?): String {
        if (responseMsg == null) return ""
        Timber.i("引用类型： ${responseMsg.msgType}")
        when (responseMsg.msgType) {
            ConstantImMsgType.SSChatMessageTypeText -> {
                // 发送文本消息
                return responseMsg.text ?:""
            }

            ConstantImMsgType.SSChatMessageTypeVoice -> {
                // 发送语音消息
                return "[语音]${responseMsg.voiceTime.toString().plus(" s")}"
            }

            ConstantImMsgType.SSChatMessageTypeImage -> {
                // 发送图片消息
                return "[图片]"
            }

            ConstantImMsgType.SSChatMessageTypeGifImage -> {
                // 发gif动图
                return "[表情]"
            }

            ConstantImMsgType.SSChatMessageTypeVideo -> {
                // 发送小视频
                return "[视频]"
            }

            ConstantImMsgType.SSChatMessageTypeFile -> {
                // 发文件 word,zip之类
                return "[文件]${responseMsg.fileName}"
            }

            ConstantImMsgType.SSChatMessageTypeMap -> {
                // 发送地图定位
                val locationBean = LocationMessage(
                    responseMsg.latitude,
                    responseMsg.longitude,
                    responseMsg.addressTitle,
                    responseMsg.addressDetail,
                    responseMsg.addressImgUrl
                )
                return "[位置]${locationBean.title}"
            }

            ConstantImMsgType.SSChatMessageTypeOfflineFault -> {
                // 离线消息断层--本地使用
            }

            ConstantImMsgType.SSChatMessageTypeUndo -> {
                // 撤销的消息--指令
            }


            ConstantImMsgType.SSChatMessageTypeInitiateVoiceVideo -> {
                // 发起音视频通话
            }

            ConstantImMsgType.SSChatMessageTypeVoiceVideoEnd -> {
                // 音视频通话 正常结束

            }

            ConstantImMsgType.SSChatMessageTypeVoiceVideoCallFailure -> {

            }

            ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice -> {
                // @ 类型
                return  responseMsg.text ?: ""
            }

            ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice -> {
                // @ 类型
                return  responseMsg.text ?: ""
            }

            ConstantImMsgType.SSChatMessageTypeInvite -> {
                return "[邀请]"
            }

            ConstantImMsgType.SSChatMessageTypeForwardRecord -> {
                // 记录 ： 暂时从 extendOne 里取吧
                val content = if(responseMsg?.loadImRecord()?.sessionType == 1) "[会话记录]" else "[群聊会话记录]"
                return content
            }

            ConstantImMsgType.SSChatMessageTypeQuote ->{
                return responseMsg.text ?:""
            }

            ConstantImMsgType.SSChatMessageTypeRobot -> {
                return responseMsg.text ?:""
            }

            else -> {

            }
        }
        return ""
    }

    /**
     * 根据不同类类型，展示不同的UI，
     */
    fun parseImageView(context: Context , quoteMsg: Message?, sendContentTextView: TextView ,
                       logoImageView: ImageView , isSender: Boolean = false , click:(Message) -> Unit , layout: LinearLayout , isGroup: Boolean = false
                       , msgId: String? = ""  , progress: ((Double) -> Unit)? = null
                       , isRecord: Boolean = false
    ) {
        if (quoteMsg == null) return
        logoImageView.visibility = View.GONE
        sendContentTextView.visibility = View.GONE

        layout.visibility = View.VISIBLE

//        Timber.i("引用类型，子类型 ： ${quoteMsg.msgType}")

        when (quoteMsg.msgType) {
            ConstantImMsgType.SSChatMessageTypeQuote ,
            ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice,
            ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice
            -> {
                // 发送文本消息
                sendContentTextView.visibility = View.VISIBLE
                sendContentTextView.text = quoteMsg.text
            }
            ConstantImMsgType.SSChatMessageTypeText -> {
                // 发送文本消息
                sendContentTextView.visibility = View.VISIBLE
                sendContentTextView.text = quoteMsg.text
            }

            ConstantImMsgType.SSChatMessageTypeVoice -> {
                // 发送语音消息
                sendContentTextView.visibility = View.VISIBLE
                sendContentTextView.text = "🔊${quoteMsg.voiceTime.toString().plus(" s")}"
                layout.setOnClickListener {
                    click(quoteMsg)
                }
            }

            ConstantImMsgType.SSChatMessageTypeForwardRecord -> {
                // 引用内容
                sendContentTextView.visibility = View.VISIBLE
                sendContentTextView.text = quoteMsg.text
                layout.setOnClickListener {
                    click(quoteMsg)
                }
            }

            ConstantImMsgType.SSChatMessageTypeImage -> {
                // 发送图片消息
                logoImageView.visibility = View.VISIBLE

                //防止ios发布的图片尺寸带小数
                val imageMsgParse = ImageMsgParseBean(
                    quoteMsg.imgWidth.toString(),
                    quoteMsg.imgHeight.toString(),
                    quoteMsg.localUrl ?:"",
                    quoteMsg.msgId,
                    quoteMsg.appChatId
                )

                var viewW: Int = when {
                    imageMsgParse.w.contains(".") ->
                        (imageMsgParse.w.substring(0, imageMsgParse.w.indexOf("."))).toInt()
                    else -> imageMsgParse.w.toInt()
                }
                var viewH = when {
                    imageMsgParse.h.contains(".") ->
                        (imageMsgParse.h.substring(0, imageMsgParse.h.indexOf("."))).toInt()
                    else -> imageMsgParse.h.toInt()
                }
                val pair = dealImageSize(viewW, viewH, context)
                viewW = pair.first
                viewH = pair.second

                val path = imageMsgParse.url

                val params: FrameLayout.LayoutParams = logoImageView.layoutParams
                        as FrameLayout.LayoutParams
                params.height = com.luck.picture.lib.tools.ScreenUtils.dip2px(context , 30f)
                params.width = com.luck.picture.lib.tools.ScreenUtils.dip2px(context , 30f)
                logoImageView.layoutParams = params
                //用远程地址显示图片

                val thumb = FileThumbUtil.getThumbFileUrl(context , quoteMsg.fileId)

//                Timber.i("群聊加载图片, $thumb , 存在么${File(thumb)}")

                ImageLoaderUtils.showRoundedImg(context, thumb, logoImageView, 1)

//                ImageLoaderUtils.loadImage(context, logoImageView ,path,)

                val imageMsg = ImageTransMsgBean(w = viewW, h = viewH, url = path,
                    messageId = quoteMsg.msgId, targetId = quoteMsg.appChatId, uuid = quoteMsg.msgId)

                layout.setOnClickListener {
                    if (isRecord){
                        TaskImagePreviewActivity.open(context , url = path)
                    }else{

                        TaskImagePreviewActivity.open(context , url = thumb)

                        return@setOnClickListener

                        val intent = Intent(context, WithToolBarImagePreviewActivity::class.java)
                        intent.putExtra("imageBean", imageMsg)
                        intent.putExtra("type", "imageMsg")//群聊 查看接收图片
                        if (isGroup){
                            intent.putExtra("isGroupImage" , true)
                        }
                        context.startActivity(intent)
                    }
                }
            }

            ConstantImMsgType.SSChatMessageTypeGifImage -> {
                // 发gif动图
                sendContentTextView.visibility = View.VISIBLE
                sendContentTextView.text = "[表情]"
            }

            ConstantImMsgType.SSChatMessageTypeVideo -> {
                // 发送小视频
                logoImageView.visibility = View.VISIBLE
                sendContentTextView.visibility = View.VISIBLE
                sendContentTextView.text = "[视频]"

                val imageMsgParse = ImageMsgParseBean(
                    quoteMsg.imgWidth.toString()
                    , quoteMsg.imgHeight.toString()
                    , quoteMsg.videoImagePath ?: ""
                    , quoteMsg.msgId
                    , quoteMsg.appChatId
                )

                var viewW: Int = when {
                    imageMsgParse.w.contains(".") ->
                        (imageMsgParse.w.substring(0, imageMsgParse.w.indexOf("."))).toInt()
                    else -> imageMsgParse.w.toInt()
                }
                var viewH = when {
                    imageMsgParse.h.contains(".") ->
                        (imageMsgParse.h.substring(0, imageMsgParse.h.indexOf("."))).toInt()
                    else -> imageMsgParse.h.toInt()
                }
                val pair = dealImageSize(viewW, viewH, context)
                viewW = pair.first
                viewH = pair.second
                val path = imageMsgParse.url
                val params: FrameLayout.LayoutParams = logoImageView.layoutParams
                        as FrameLayout.LayoutParams
                params.height = com.luck.picture.lib.tools.ScreenUtils.dip2px(context , 25f)
                params.width = com.luck.picture.lib.tools.ScreenUtils.dip2px(context , 25f)
                logoImageView.layoutParams = params

                ImageLoaderUtils.showRoundedImg(context, quoteMsg.videoImagePath, logoImageView, radius = 1)

//                loadVideoImage(context , quoteMsg , msgId , call = {
//                    ImageLoaderUtils.showRoundedImg(context, it, logoImageView, radius = 11)
//                })

                (context as? Activity)?.let { mC ->
                    logoImageView.setOnClickListener {

                        val msg = quoteMsg

                        DealFileUtil.checkFileIsExist(mC, ConstantTcpUtil.VIDEO_COMPRESSED_FOLDER, msg.fileName,
                            onResult = { isExist, file ->
                                if (isExist == true) {//视频已经存在，直接播放
                                    if (file!= null){

                                        DealFileUtil.playVideoWithPermission(mC, file.absolutePath , msg.longitude)
                                    }
                                } else {
                                    //不存在就下载
                                    UpLoadFileUtil.getUpFileUtilInstance()?.downLoadFile(mC, msg.fileId,
                                        ConstantTcpUtil.VIDEO_COMPRESSED_FOLDER, msg.fileName,
                                        onSuccess = { loadFileResultBean ->//下载视频成功
                                            msg.localUrl = loadFileResultBean.localUrl
                                            msg.longitude = 100.0

                                            GlobalScope.launch {
                                                withContext(Dispatchers.Main){
                                                    progress?.invoke(msg.longitude)
                                                }
                                            }

                                            try {
                                                MessageDaoOpe.instance.updateQuoteMessage(mC , msgId ,msg)

                                            }catch (e: Exception){
                                                e.printStackTrace()
                                            }

                                            DealFileUtil.playVideoWithPermission(mC, loadFileResultBean.localUrl , msg.longitude)
                                        }, onFailure = {
                                            toast("文件下载失败")
                                        },
                                        onProgress = { complete, target ->//下载视频进度
                                            val data = complete.toDouble()
                                            val totalData = target.toDouble()

                                            if ((data / totalData * 100).toInt() % 10 == 0) {
                                                msg.longitude = data / totalData * 100

                                                GlobalScope.launch {
                                                    withContext(Dispatchers.Main){
                                                        progress?.invoke(msg.longitude)
                                                    }
                                                }
                                            }
                                        })
                                }
                            })
                    }
                }
            }

            ConstantImMsgType.SSChatMessageTypeFile -> {
                // 发文件 word,zip之类
                sendContentTextView.visibility = View.VISIBLE
                sendContentTextView.text = "[文件]${quoteMsg.fileName}"
                logoImageView.visibility = View.VISIBLE
                logoImageView.setImageResource(FileUtil.getFileTypeIcon(quoteMsg.fileName))

                val params: FrameLayout.LayoutParams = logoImageView.layoutParams
                        as FrameLayout.LayoutParams
                params.height = com.luck.picture.lib.tools.ScreenUtils.dip2px(context , 30f)
                params.width = com.luck.picture.lib.tools.ScreenUtils.dip2px(context , 30f)
                logoImageView.layoutParams = params

                layout.setOnClickListener {
                    click(quoteMsg)
                }
            }

            ConstantImMsgType.SSChatMessageTypeMap -> {
                // 发送地图定位
                logoImageView.visibility = View.VISIBLE

                val locationBean = LocationMessage(
                    quoteMsg.latitude,
                    quoteMsg.longitude,
                    quoteMsg.addressTitle,
                    quoteMsg.addressDetail,
                    quoteMsg.addressImgUrl ?:""
                )
                sendContentTextView.visibility = View.VISIBLE
                sendContentTextView.text =  "[位置]"

                ImageLoaderUtils.loadImage(context, logoImageView, locationBean.uri)
                layout.setOnClickListener {
                    jumpToLocationDetail(locationBean)
                }

            }

            ConstantImMsgType.SSChatMessageTypeOfflineFault -> {
                // 离线消息断层--本地使用
            }

            ConstantImMsgType.SSChatMessageTypeUndo -> {
                // 撤销的消息--指令
            }


            ConstantImMsgType.SSChatMessageTypeInitiateVoiceVideo -> {
                // 发起音视频通话
            }

            ConstantImMsgType.SSChatMessageTypeVoiceVideoEnd -> {
                // 音视频通话 正常结束

            }

            ConstantImMsgType.SSChatMessageTypeVoiceVideoCallFailure -> {

            }

            ConstantImMsgType.SSChatMessageTypeInvite -> {
                logoImageView.visibility = View.VISIBLE

                val content = "企业邀请${quoteMsg.extendThree}"
                sendContentTextView.text = content
                sendContentTextView.visibility = View.VISIBLE
                ImageLoaderUtils.showRoundedImg(context, quoteMsg.imgUrl, logoImageView)

                layout.setOnClickListener {
                    ARouter.getInstance().build(RouteOrg.orgIntroActivity)
                        .withString("companyId", quoteMsg.extendTwo)//公司id
                        .navigation()
                }

            }

            else -> {

            }
        }
    }

    private fun dealImageSize(w: Int, h: Int, context: Context): Pair<Int, Int> {
        val viewW: Int
        val viewH: Int
        when {
            h > w -> {
                //长图
                viewH = ScreenUtils.dip2px(context, 129F)
                if (h / w <= 2) {
                    viewW = viewH * w / h
                } else {
                    viewW = viewH / 2
                }
            }
            h < w -> {
                //宽图
                viewW = ScreenUtils.dip2px(context, 129F)
                if (w / h <= 2) {
                    viewH = viewW * h / w
                } else {
                    viewH = viewW / 2
                }
            }
            else -> {
                //方图
                viewH = ScreenUtils.dip2px(context, 108F)
                viewW = ScreenUtils.dip2px(context, 108F)
            }
        }
        return Pair(viewW, viewH)
    }

    fun jumpToLocationDetail(message: LocationMessage) {
        val locationMessage: LocationMessage = message
        val lat = locationMessage.latitude
        val lng = locationMessage.longitude
        val poiName = locationMessage.title
        val poiAddress = locationMessage.address

//        ARouter.getInstance().build(RouteIm.locationActivity)
//            .withDouble("lat", lat)
//            .withDouble("lng", lng)
//            .withString("poiName", poiName)
//            .withString("poiAddress", poiAddress)
//            .withString("type", "")
//            .navigation()
    }

    fun existAtQuoteUrl(context: Context ,msg: Message?) : Boolean {
        if (msg == null) return true
        try {
            if (msg.msgType == ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice || msg.msgType == ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice){
                val atmsg = GsonUtil.fromJson<AtQuoteMsg>(msg.extendThree)
                if (atmsg?.quoteInfo == null) return true
                val inner = atmsg.quoteInfo!!
                if (inner.msgType == ConstantImMsgType.SSChatMessageTypeImage){
                    if (!FileThumbUtil.thumbIsExist(context, inner.fileId)){
                        return false
                    }
                }
                if (inner.msgType == ConstantImMsgType.SSChatMessageTypeVideo){
                    if (!FileThumbUtil.thumbIsExist(context ,inner.videoImageId)){
                        return false
                    }
                }
            }

        }catch (e: Exception){
            return true
        }
        return true
    }

    // 消息列表引用类型的消息，的缩略图是否存在，包括是否有url ，是否改url对应的文件存在
    fun existThumbUrl(context: Context ,msg: Message?): Boolean {
        if (msg == null) return false
        try {
            val inner = GsonUtil.fromJson<Message>(msg.extendOne) ?: return false
            if (inner.msgType == ConstantImMsgType.SSChatMessageTypeImage){
                if (FileThumbUtil.thumbIsExist(context , inner.fileId)){
                    return true
                }
            }
            if (inner.msgType == ConstantImMsgType.SSChatMessageTypeVideo){
                if (FileThumbUtil.thumbIsExist(context ,inner.fileId)){
                    return true
                }
            }
        }catch (e: Exception){
            return false
        }
        return false
    }

    fun hasNullQuoteUrl(context: Context ,msg: Message) : Boolean {
        try {
            val inner = GsonUtil.fromJson<Message>(msg.extendOne)
            if (inner == null) return false
            if (inner.msgType == ConstantImMsgType.SSChatMessageTypeImage){
                if (!FileThumbUtil.thumbIsExist(context , inner.fileId)){
                    return true
                }
            }
            if (inner.msgType == ConstantImMsgType.SSChatMessageTypeVideo){
                if (!FileThumbUtil.thumbIsExist(context ,inner.videoImageId)){
                    return true
                }
            }
        }catch (e: Exception){
            return false
        }
        return false
    }


    fun offlineAtQuoteDownloadThumb(msg: Message?) : QuoteUploadParam {
        if (msg == null) return QuoteUploadParam("","","")
        var fileFolder: String = ""
        var fileName: String = ""
        var fileId: String = ""
        try {
            val innerMsg = GsonUtil.fromJson<AtQuoteMsg>(msg.extendThree)

            innerMsg?.quoteInfo?.let { quotMsg ->
                if (quotMsg.msgType == ConstantImMsgType.SSChatMessageTypeImage){
                    fileFolder = ConstantTcpUtil.IMAGE_THUMB_FOLDER
                    fileName = quotMsg.msgId + ".png"
                    fileId = quotMsg.fileId
                }

                if (quotMsg.msgType == ConstantImMsgType.SSChatMessageTypeVideo) {
                    fileFolder = ConstantTcpUtil.VIDEO_SHOT_SCREEN_FOLDER
                    fileName = quotMsg.msgId + ".png"
                    fileId = quotMsg.videoImageId
                }
                return QuoteUploadParam(
                    fileFolder, fileName, fileId
                )
            }


        }catch (e: Exception){
            e.printStackTrace()
        }
        return QuoteUploadParam("","","")
    }

    fun offlineQuoteTypeDownloadThumb(msg: Message?) : QuoteUploadParam{
        if (msg == null) return QuoteUploadParam("","","")
        var fileFolder: String = ""
        var fileName: String = ""
        var fileId: String = ""
        try {
            val innerMsg = GsonUtil.fromJson<Message>(msg.extendOne)
            if (innerMsg != null){
                if (innerMsg.msgType == ConstantImMsgType.SSChatMessageTypeImage){
                    fileFolder = ConstantTcpUtil.IMAGE_CACHE_FOLDER
                    fileName = innerMsg.msgId + ".png"
                    fileId = innerMsg.fileId
                }

                if (innerMsg.msgType == ConstantImMsgType.SSChatMessageTypeVideo) {
                    fileFolder = ConstantTcpUtil.VIDEO_SHOT_SCREEN_FOLDER
                    fileName = innerMsg.msgId + ".png"
                    fileId = innerMsg.videoImageId
                }
                return QuoteUploadParam(
                    fileFolder, fileName, fileId
                )
            }
        }catch (e: Exception){
            e.printStackTrace()
        }
        return QuoteUploadParam("","","")
    }


    fun quoteResetValue(msg: Message , it: LoadFileResultBean) {
        val innerMsg = GsonUtil.fromJson<Message>(msg.extendOne)

        Timber.i("${msg.text} , ${innerMsg?.msgType} , ${it.localUrl}")

        if (innerMsg?.msgType == ConstantImMsgType.SSChatMessageTypeVideo) {
            innerMsg?.videoImagePath = it.localUrl
            msg.extendOne = GsonUtil.toJson(innerMsg)
        } else if (innerMsg?.msgType == ConstantImMsgType.SSChatMessageTypeFile){
            innerMsg?.videoImagePath = it.localUrl
            msg.extendOne = GsonUtil.toJson(innerMsg)
        } else if (innerMsg?.msgType == ConstantImMsgType.SSChatMessageTypeImage) {
            innerMsg.imgUrl = it.localUrl
            innerMsg.localUrl = it.localUrl
            msg.extendOne = GsonUtil.toJson(innerMsg)
        }
    }


    fun atQuoteResetValue(msg: Message? , it: LoadFileResultBean) {
        if (msg == null) return
        if (msg.msgType == ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice || msg.msgType == ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice) {
            val atmsg = GsonUtil.fromJson<AtQuoteMsg>(msg.extendThree)
            if (atmsg?.quoteInfo != null){
                if (atmsg.quoteInfo?.msgType == ConstantImMsgType.SSChatMessageTypeVideo) {
                    atmsg.quoteInfo?.videoImagePath = it.localUrl
                    msg.extendThree = GsonUtil.toJson(atmsg)

                } else if (atmsg.quoteInfo?.msgType == ConstantImMsgType.SSChatMessageTypeFile){
                    atmsg.quoteInfo?.videoImagePath = it.localUrl
                    msg.extendThree = GsonUtil.toJson(atmsg)

                } else if (atmsg.quoteInfo?.msgType == ConstantImMsgType.SSChatMessageTypeImage) {
                    atmsg.quoteInfo!!.imgUrl = it.localUrl
                    atmsg.quoteInfo!!.localUrl = it.localUrl
                    msg.extendThree = GsonUtil.toJson(atmsg)
                }
            }
        }
    }

    fun atQuoteResetValueByUrl(msg: Message? , url: String) {
        if (msg == null) return
        if (msg.msgType == ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice || msg.msgType == ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice) {
            val atmsg = GsonUtil.fromJson<AtQuoteMsg>(msg.extendThree)
            if (atmsg?.quoteInfo != null){
                if (atmsg.quoteInfo?.msgType == ConstantImMsgType.SSChatMessageTypeVideo) {
                    atmsg.quoteInfo?.videoImagePath = url
                    msg.extendThree = GsonUtil.toJson(atmsg)

                } else if (atmsg.quoteInfo?.msgType == ConstantImMsgType.SSChatMessageTypeFile){
                    atmsg.quoteInfo?.videoImagePath = url
                    msg.extendThree = GsonUtil.toJson(atmsg)

                } else if (atmsg.quoteInfo?.msgType == ConstantImMsgType.SSChatMessageTypeImage) {
                    atmsg.quoteInfo!!.imgUrl = url
                    atmsg.quoteInfo!!.localUrl = url
                    msg.extendThree = GsonUtil.toJson(atmsg)
                }
            }
        }
    }

}

data class AtQuoteMsg(
    var quoteInfo: Message?
) : Serializable

data class QuoteUploadParam(
    val fileFolder: String,
    val fileName: String,
    val fileId: String,
)