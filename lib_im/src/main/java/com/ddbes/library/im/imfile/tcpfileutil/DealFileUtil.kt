package com.ddbes.library.im.imfile.tcpfileutil

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.os.Environment.MEDIA_MOUNTED
import android.provider.Settings
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.library.im.bean.ImImageInfo
import com.ddbes.library.im.bean.LocalVideoInfo
import com.ddbes.library.im.imfile.FileManagerActivity
import com.ddbes.library.im.imtcp.ConstantTcpUtil
import com.ddbes.library.im.imtcp.Logger
import com.iceteck.silicompressorr.VideoCompress
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.utils.AppManager
import com.joinutech.ddbeslibrary.utils.PermissionUtils
import com.joinutech.ddbeslibrary.utils.RouteIm
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.luck.picture.lib.PictureSelector
import com.tencent.smtt.sdk.MimeTypeMap
import com.tencent.tbs.reader.TbsFileInterfaceImpl
import io.reactivex.Observable
import io.reactivex.ObservableOnSubscribe
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
//import wseemann.media.FFmpegMediaMetadataRetriever
import java.io.File
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.text.SimpleDateFormat
import java.util.*


object DealFileUtil {
    //格式化文件大小
    fun formatFileSize(length: Long): String? {
        var result: String? = null
        var sub_string = 0
        if (length >= 1073741824) {
            sub_string = (length.toFloat() / 1073741824).toString().indexOf(
                    ".")
            result = ((length.toFloat() / 1073741824).toString() + "000").substring(0,
                    sub_string + 3) + "GB"
        } else if (length >= 1048576) {
            sub_string = (length.toFloat() / 1048576).toString().indexOf(".")
            result = ((length.toFloat() / 1048576).toString() + "000").substring(0,
                    sub_string + 3) + "MB"
        } else if (length >= 1024) {
            sub_string = (length.toFloat() / 1024).toString().indexOf(".")
            result = ((length.toFloat() / 1024).toString() + "000").substring(0,
                    sub_string + 3) + "KB"
        } else if (length < 1024) result = java.lang.Long.toString(length) + "B"

        return result
    }

    //获取文件格式
    fun getFileExtension(filename: String?): String? {
        if (filename != null && filename.length > 0) {
            val dot = filename.lastIndexOf('.')
            if (dot > -1 && dot < filename.length - 1) {
                return filename.substring(dot + 1)
            }
        }
        return null
    }

    //获取文件类型，是视频、文件、图片等情况
    fun getFileUseType(filename: String?): String {
        val fileTypeList = arrayListOf("doc", "docx", "ppt", "pptx", "xls", "xlsx", "pdf", "txt", "epub", "zip")
        val videoTypeList = arrayListOf("mp4", "flv", "avi", "3gp", "webm", "ts", "ogv", "m3u8", "asf", "wmv", "rm", "rmvb", "mov", "mkv")
        val pictureTypeList = arrayListOf("jpg", "jpeg", "png", "gif", "bmp", "WEBP")
        val voiceTypeList = arrayListOf("mp3", "wma", "aac", "amr", "ape", "wave", "aiff", "mpeg", "mpeg-4", "flac")
        if (filename == null) {
            return ""
        } else {
            val fileType = getFileExtension(filename)
            if (fileType != null) {
                when {
                    fileTypeList.contains(fileType.toLowerCase(Locale.getDefault())) -> {
                        return ConstantTcpUtil.FILE_TYPE
                    }
                    videoTypeList.contains(fileType.toLowerCase(Locale.getDefault())) -> {
                        return ConstantTcpUtil.VIDEO_TYPE
                    }
                    pictureTypeList.contains(fileType.toLowerCase(Locale.getDefault())) -> {
                        return ConstantTcpUtil.PICTURE_TYPE
                    }
                    voiceTypeList.contains(fileType.toLowerCase(Locale.getDefault())) -> {
                        return ConstantTcpUtil.VOICE_TYPE
                    }
                    else -> {
                        return ""
                    }
                }
            } else {
                return ""
            }
        }
    }

    //获取缓存的文件夹路径
    fun getFileCachePath(context: Context, dir: String): String {
        var directoryPath = ""
        //判断SD卡是否可用
        directoryPath = if (MEDIA_MOUNTED == Environment.getExternalStorageState()) {
            context.getExternalFilesDir(dir)?.absolutePath ?: context.cacheDir.absolutePath
            // directoryPath =context.getExternalCacheDir().getAbsolutePath() ;
        } else {
            //没内存卡就存机身内存
            context.filesDir.absolutePath + File.separator + dir
            // directoryPath=context.getCacheDir()+File.separator+dir;
        }
        val file = File(directoryPath)
        if (!file.exists()) { //判断文件目录是否存在
            file.mkdirs()
        }
        return directoryPath
    }

    //存储bitmap到本地
    //在使用该函数的时候，记得把文件的扩展名带上
    @Throws(IOException::class)
    fun saveBitmap(context: Context, bitmap: Bitmap?, bitName: String,
                   folderName:String=ConstantTcpUtil.VIDEO_SHOT_SCREEN_FOLDER): File? {
        if (bitmap == null) {
            return null
        }
        val file = File(getFileCachePath(context, folderName) + "${File.separator}$bitName")
        if (file.exists()) {
            file.delete()
        }
        val out: FileOutputStream
        try {
            out = FileOutputStream(file)
            if (bitmap.compress(Bitmap.CompressFormat.PNG, 90, out)) {
                out.flush()
                out.close()
            }
            return file
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
            return null
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
    }


    //删除本地文件-------开始-------
    /**
     * 删除单个文件
     * @param   filePath    被删除文件的文件名
     * @return 文件删除成功返回true，不然返回false
     */
    private fun deleteFile(filePath: String?): Boolean {
        val file = File(filePath)
        return if (file.isFile && file.exists()) {
            file.delete()
        } else false
    }

    /**
     * 删除文件夹以及目录下的文件
     * @param   filePath 被删除目录的文件路径
     * @return  目录删除成功返回true，不然返回false
     */
    private  fun deleteDirectory(filePath: String,needDeleteEmptyFolder:Boolean=false): Boolean {
        var filePath = filePath
        var flag = false
        //若是filePath不以文件分隔符结尾，自动添加文件分隔符
        if (!filePath.endsWith(File.separator)) {
//            Logger.i("删除文件","===文件夹添加了分隔符==")
            filePath = filePath + File.separator
        }
        val dirFile = File(filePath)
        if (!dirFile.exists() || !dirFile.isDirectory) {
            return false
        }
        flag = true
        val files = dirFile.listFiles()
        //遍历删除文件夹下的全部文件(包括子目录)
        for (file in files) {
            if (file.isFile) {
                //删除子文件
                Logger.i("删除文件","===path=${file.absolutePath}==")
                flag = deleteFile(file.absolutePath)
                Logger.i("删除文件","==删除结果=flag=${flag}==")
                if (!flag) break
            } else {
                //删除子目录
                flag = deleteDirectory(file.absolutePath,needDeleteEmptyFolder = needDeleteEmptyFolder)
                if (!flag) break
            }
        }

        if (needDeleteEmptyFolder) {
            return if (!flag) false else dirFile.delete() //删除当前空目录
        }else{
            return flag//不删除当前空目录
        }
    }

    /**
     * 根据路径删除指定的目录或文件，不管存在与否
     * @param filePath  要删除的目录或文件
     * @return 删除成功返回 true，不然返回 false。
     */
    fun deleteFolder(filePath: String?,needDeleteEmptyFolder:Boolean=false): Boolean {
        val file = File(filePath)
        return if (!file.exists()) {
            false
        } else {
            if (file.isFile) {
                // 为文件时调用删除文件方法
                deleteFile(filePath)
            } else {
                // 为目录时调用删除目录方法
                deleteDirectory(filePath!!,needDeleteEmptyFolder = needDeleteEmptyFolder)
            }
        }
    }

    fun checkLocalFileIsExist(localUrl: String?) : Boolean {
        if (localUrl.isNullOrEmpty()) return false
        val file = File(localUrl);
        return file.exists()
    }


    //在指定的文件夹下找指定名称的文件，返回是否存在
    //使用带扩展名的名称
    fun checkFileIsExist(context: Activity, folderName: String = "", fileName: String = "",
                         onResult: (Boolean, File?) -> Unit) {
        val perms = arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE)
        PermissionUtils.requestPermissionActivity(context, perms, "读取存储权限", {
            val file = File(getFileCachePath(context, folderName), fileName)
            if (file.exists()) {
                onResult.invoke(true, file)
            } else {
                onResult.invoke(false, null)
            }
        }, {
            ToastUtil.show(BaseApplication.joinuTechContext,
                    "下载文件需要您授权存储权限")
        })
    }

    fun getImgInfo(imgFile: File): ImImageInfo {
        //获取图片的宽高
        val opts = BitmapFactory.Options()
        //只请求图片宽高，不解析图片像素(请求图片属性但不申请内存，解析bitmap对象，该对象不占内存)
        opts.inJustDecodeBounds = true
        //String path = Environment.getExternalStorageDirectory() + "/dog.jpg";
        BitmapFactory.decodeFile(imgFile.absolutePath, opts)
        val imageWidth = opts.outWidth
        val imageHeight = opts.outHeight
        return ImImageInfo(path = imgFile.absolutePath, width = imageWidth, height = imageHeight)

    }

    //获取视频截图
    //获取截图
    fun getScreenShot(context: Context, localVideoPath: String): File? {
        val localVideoBean = File(localVideoPath)
        val fileCachePath = getFileCachePath(context, ConstantTcpUtil.VIDEO_SHOT_SCREEN_FOLDER)
        val file =
                File(fileCachePath + File.separator + localVideoBean.nameWithoutExtension + ".png")
        if (file.exists()) {
            return file
        }

        val media = MediaMetadataRetriever()
        media.setDataSource(localVideoBean.absolutePath)
        var bitmap: Bitmap? = null
        try {
            bitmap = media.getFrameAtTime(1, MediaMetadataRetriever.OPTION_CLOSEST_SYNC)
//            bitmap = media.getFrameAtTime()
            Logger.i("----验证选择图片---", "---第一次截图-bitmap===" + bitmap)
        } catch (e: Exception) {
        }

        /* if(bitmap==null){
             try {
                 bitmap = media.getFrameAtTime()
                 Logger.i("----验证选择图片---", "---第二次截图-bitmap==="+bitmap)

             } catch (e: Exception) {
             }
         }*/

        if (bitmap == null) {
//            val mediaMetadataRetriever = FFmpegMediaMetadataRetriever()
            try {
//                mediaMetadataRetriever.setDataSource(localVideoBean.absolutePath);
//                bitmap = mediaMetadataRetriever.getFrameAtTime(-1, FFmpegMediaMetadataRetriever.OPTION_CLOSEST);
                Logger.i("----验证选择图片---", "---第三次截图-bitmap===" + bitmap)
            } catch (e: Exception) {
            }
//            mediaMetadataRetriever.release();
        }

        if (bitmap == null) {
            try {
                bitmap = media.getFrameAtTime()
                Logger.i("----验证选择图片---", "---第si次截图-bitmap===" + bitmap)

            } catch (e: Exception) {
            }
        }

        val shotFile = saveBitmap(context, bitmap, localVideoBean.nameWithoutExtension + ".png")
        if (shotFile != null) {
            return shotFile
        } else {
            return null
        }
    }

    //压缩本地视频文件
    fun compressVideo(context: Context, localVideoPath: String,
                      onSuccess: (File) -> Unit, onFailer: (File) -> Unit, onProgress: (Float) -> Unit) {
        val videoFile = File(localVideoPath)
        val destPath = getFileCachePath(
                context,
                ConstantTcpUtil.VIDEO_COMPRESSED_FOLDER
        ) + File.separator + videoFile.nameWithoutExtension + ".mp4"

        val file = File(destPath)
        if (file.exists()) {
            onSuccess.invoke(file)
            return
        }

        VideoCompress.compressVideoMedium(localVideoPath, destPath, object : VideoCompress.CompressListener {
            override fun onStart() {
                var startTime = System.currentTimeMillis()
                Logger.i("---验证压缩---", "---开始压缩的时间----${formatTime(startTime)}")
            }

            override fun onSuccess() {
                var endTime = System.currentTimeMillis()
                Logger.i("---验证压缩---", "---结束压缩的时间----${formatTime(endTime)}")
                Logger.i("---验证压缩---", "---压缩后的大小----" + formatFileSize(File(destPath).length()))
                Logger.i("---验证压缩---", "---压缩后的路径---destPath-" + destPath)
                onSuccess.invoke(File(destPath))
            }

            override fun onFail() {
                var endTime = System.currentTimeMillis()
                Logger.i("---验证压缩---", "---压缩失败---onFail-的时间---" + formatTime(endTime))
                onFailer.invoke(File(localVideoPath))
            }

            override fun onProgress(percent: Float) {
                Logger.i("---验证压缩---", "---正在压缩--percent---" + percent)
                onProgress.invoke(percent)
            }
        })
    }

    private fun formatTime(time: Long): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        val date = Date(time)
        val format = dateFormat.format(date)
        return format
    }

    //播放音频文件
    fun playVoiceWithPermission(context: Activity, voicePath: String) {//申请浏览服务相关的权限
        val perms = arrayOf(
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.ACCESS_NETWORK_STATE,
                Manifest.permission.ACCESS_WIFI_STATE,
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.READ_PHONE_STATE
        )
        PermissionUtils.requestPermissionActivity(context, perms, "预览文件权限", {
            //            initConfiguration()//初始化浏览服务的配置
//            startPlay("http://111.231.191.26/See%20You%20Again.mp4")
            startPlayVoice(context, voicePath)
        }, {
            ToastUtil.show(context, "预览文件需要您授权相关权限")
        })
    }

    private fun startPlayVoice(context: Activity, voicePath: String) {
        // 预览音频
        PictureSelector.create(context)
                .externalPictureAudio(voicePath)
    }

    //播放视频文件
    fun playVideoWithPermission(context: Activity, videoPath: String ,  progress: Double) {//申请浏览服务相关的权限
        if (!AppManager.single_instance.isOpenActivity(context.javaClass)) return
        val perms = arrayOf(
                Manifest.permission.READ_EXTERNAL_STORAGE,
        )
        PermissionUtils.requestPermissionActivity(context, perms, "预览文件权限", {
            if (progress == 100.00) {
                startPlayVideo(context, videoPath)
            }
        }, {
            ToastUtil.show(context, "预览文件需要您授权相关权限")
        })
    }

    private fun startPlayVideo(context: Activity, videoUrl: String) {
        //使用图片选择库自带的预览功能播放
        PictureSelector.create(context).externalPictureVideo(videoUrl);

        //待续，重新写视频预览，在lib_im模块中写
        //说明，这种方式暂时放弃，因为当初web端要求原生视频播放可以去掉控制按钮（培训视频），后来又改为web端自己实现了；
//        val intent=Intent(context,VideoPlayingActivity::class.java)
//        intent.putExtra("videoPath",videoUrl)
//        context.startActivity(intent)

        //使用TbsVideo播放======================
//        if (TbsVideo.canUseTbsPlayer(BaseApplication.joinuTechContext)) {
//            //播放视频
//            TbsVideo.openVideo(BaseApplication.joinuTechContext, videoUrl)
//        }

        //实现自动播放
//        val method = "setVideoPlaybackRequiresUserGesture"
//        val bundle = Bundle()
//        bundle.putBoolean("require", false)
//        x5Webview.getX5WebViewExtension().invokeMiscMethod(method, bundle)

        //使用x5Webview播放======================
//        x5Webview.loadUrl("file://${videoUrl}")//播放本地视频
////        x5Webview.loadUrl(videoUrl)//播放网络视频
//        window.setFormat(PixelFormat.TRANSLUCENT)
//        x5Webview.getView().setOverScrollMode(View.OVER_SCROLL_ALWAYS)
//        x5Webview.setWebChromeClient(WebChromeClient())
    }

    fun getNameWithoutExtension(fileName: String?): String {
        if (fileName == null) {
            return ""
        }
        val lastIndexOf = fileName.lastIndexOf(".")
        return fileName.substring(0, lastIndexOf)
    }

    fun copyFile(context: Activity, oldName: String, newName: String): String {
        val oldPath = getFileCachePath(context, ConstantTcpUtil.FILE_CACHE_FOLDER) + File.separator + oldName
        val newPath = getFileCachePath(context, ConstantTcpUtil.FILE_CACHE_FOLDER) + File.separator + newName
        val oldFile = File(newPath)
        if (oldFile.exists() && oldFile.isFile) {
            oldFile.delete()
        }
        try {
            val inputStream: InputStream = FileInputStream(oldPath)
            val outputStream: OutputStream = FileOutputStream(newPath)
            val bt = ByteArray(1024)
            var d: Int = 0
            while (inputStream.read(bt).also({ d = it }) > 0) {
                outputStream.write(bt, 0, d)
            }
            inputStream.close()
            outputStream.close()
            return newPath
        } catch (e: java.lang.Exception) {
            return ""
        }
    }

    fun showFailFileDetail(filePath: String, fileShowName: String) {
        //文件预览
        ARouter.getInstance()
                .build(RouteIm.fileDeatailActivity)
                .withString("filePath", filePath)
                .withString("fileShowName", fileShowName)
                .navigation()
    }

    private fun playFileByTbsFileSdk(filePath: String, fileName: String, fileShowName: String) {
        //文件预览
        ARouter.getInstance()
                .build(RouteIm.playFileActivity)
                .withString("filePath", filePath)
                .withString("fileShowName", fileShowName)
                .withString("fileName", fileName)
                .navigation()
    }

    fun playFile(context: Activity, filePath: String, fileName: String, fileShowName: String) {
        if (!TbsFileInterfaceImpl.canOpenFileExt(getFileExtension(fileName))) {
            //不能预览
            if (fileName == fileShowName) {
                Logger.i("文件预览", "===不能本地预览===不需要复制文件就去其他应用打开===")
                showFailFileDetail(filePath, fileShowName)
            }else{
                //需要复制文件
                Observable.create(ObservableOnSubscribe<String> { it2 ->
                    Logger.i("文件预览", "===不能本地预览===并且需要复制文件再去其他应用打开===")
                    val copyFilePath = copyFile(context, fileName, fileShowName)
                    it2.onNext(copyFilePath)
                })
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe({ filePath ->
                        showFailFileDetail(filePath,fileShowName)
                    }, { throwable ->
                    })
            }

        }else{
            Logger.i("文件预览", "===使用SDK---本地预览===")
            playFileByTbsFileSdk(filePath, fileName, fileShowName)
        }
    }


    fun checkExternalStorageManagePermission(isResult: (Boolean) -> Unit): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            if (Environment.isExternalStorageManager()){
                isResult(true)
                return true
            }
            isResult(false)
            return false
        }
        isResult(true)
        return true
    }


    //isFromSystem参数决定是打开系统的文件选择器还是打开文件选择器类库，两者都可以使用；
    fun chooseFiles(activity: FragmentActivity, permissionRequestCode:Int, fileRequestCode:Int,
                    isFromSystem:Boolean=false) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
         //Android版本大于等于11时
            if (!Environment.isExternalStorageManager()) {
                val intent=Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                intent.setData(Uri.parse("package:" + activity.getPackageName()));
                activity.startActivityForResult(intent, permissionRequestCode);
                return
            }
        }

        if (isFromSystem==false) {
            FileManagerActivity.startFileManagerActivityForResult(activity, 9, fileRequestCode)
        } else {
           val fileRequestCode2 =fileRequestCode+1
            //打开系统的文件选择器
            val intent=Intent(Intent.ACTION_GET_CONTENT)
            intent.addCategory(Intent.CATEGORY_OPENABLE)
            intent.setType("*/*")
            try {
                activity.startActivityForResult(Intent.createChooser(intent,"File Chooser"),fileRequestCode2)
            }catch (e:Exception){
                ToastUtil.show(activity,"打开文件选择器失败")
            }
        }
    }

    private fun getSuffix(file: File?): String? {
        if (file == null || !file.exists() || file.isDirectory) {
            return null
        }
        val fileName = file.name
        if (fileName == "" || fileName.endsWith(".")) {
            return null
        }
        val index = fileName.lastIndexOf(".")
        return if (index != -1) {
            fileName.substring(index + 1).lowercase()
        } else {
            null
        }
    }

    fun getMimeType(file: File?): String? {
        val suffix: String = getSuffix(file) ?: return "file/*"
        val type: String? = MimeTypeMap.getSingleton().getMimeTypeFromExtension(suffix)
        if (type != null) {
            return type
        }
        return "file/*"
    }

}