package com.ddbes.library.im.imtcp.imservice

import android.annotation.SuppressLint
import android.content.Context
import android.os.*
import com.ddbes.library.im.ImService
import com.ddbes.library.im.imtcp.*
import com.ddbes.library.im.imtcp.dbbean.*
import com.ddbes.library.im.imtcp.dbbean.Message
import com.ddbes.library.im.imtcp.dbope.*
import com.ddbes.library.im.imtcp.imservice.msghelper.SendMsgHelper
import com.joinutech.common.base.isDebug
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.imbean.ImTokenBean
import com.joinutech.ddbeslibrary.request.interceptor.LoggerInterceptor
import com.joinutech.ddbeslibrary.request.interceptor.RequestEventListener
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.flutter.EventByte
import com.joinutech.flutter.EventReConnect
import com.joinutech.flutter.EventSocketStatus
import com.marktoo.lib.cachedweb.LogUtil.showLog
import okhttp3.*
import okio.ByteString
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject
import timber.log.Timber
import java.io.IOException
import java.net.UnknownHostException
import java.util.*
import java.util.concurrent.TimeUnit


// service 改为单例
@SuppressLint("StaticFieldLeak")
object OkWebSocketService{
    private val friendIdSet = hashSetOf<String>()
    val haveAtTagSet = hashSetOf<String>()
    private val chatCacheMap = hashMapOf<String, MutableList<Message>>()
    private val reConnectMostCount = 3
    private var reConnectCurrentCount = 0

    //---------测试----------注释替换-------------------
    private var longConnectionUrl: String? = null//测试时注释掉

//    private var imTokenBean: ImTokenBean? = null

    //------------------------------------------------------------
    private var mWebSocket: WebSocket? = null
    private val mHandler = Handler(Looper.getMainLooper())
    private var sendTime = 0L
    private var longConnectState = false//长链接的状态
    private var isCreatingLongConnect = false//是否正在创建长链接
    private var isHaveHeartBeat = false//是否还在维持心跳
    private var client: OkHttpClient? = null

    private lateinit var context: Context

    fun loginContext(context: Context) {
        this.context = context
    }
    
    private val HEART_BEAT_RATE = (15 * 1000).toLong()//间隔...秒进行一次对长连接的心跳检测

    private val heartBeatRunnable = object : Runnable {
        override fun run() {

            if (System.currentTimeMillis() - sendTime >= HEART_BEAT_RATE) {
                if (mWebSocket != null) {
                    val isSuccess = mWebSocket!!.send("ping")//发送一个空消息给服务器，通过发送消息的成功失败来判断长连接的连接状态
                    if (!isSuccess) {//长连接已断开
                        Logger.i("---长链接验证------", "-------心跳失败--------")
                        mHandler.removeCallbacks(this)
                        mWebSocket?.cancel()//取消掉以前的长连接
                        mWebSocket?.close(1000 , null)

                        if (!isCreatingLongConnect) {
                            InitSocketThread().start()//长连接断开后 创建一个新的连接
                        }

                    } else {//长连接处于连接状态
                        // TODO: 隐藏掉
                    }

                    sendTime = System.currentTimeMillis()
                } else {
                    mHandler.removeCallbacks(this)
                    if (!isCreatingLongConnect) {
                        InitSocketThread().start()//创建一个新的连接
                    }
                }
            }
            mHandler.postDelayed(this, HEART_BEAT_RATE)//每隔一定的时间，对长连接进行一次心跳检测
        }
    }

    // todo 当做 init
    fun onCreate() {
        showLog("执行，启动了长链接服务-->初始化OkHttpClient")
        initNetClient()
        showLog("执行，启动了长链接服务-->启动服务，兼容了8.0以后服务属性")
        BaseApplication.tcpImServiceIsRunning = true
    }

    private fun initNetClient() {
        client = OkHttpClient.Builder()
            .readTimeout(3, TimeUnit.SECONDS)//设置读取超时时间
            .writeTimeout(3, TimeUnit.SECONDS)//设置写的超时时间
            .sslSocketFactory(SSLSocketClient.getSSLSocketFactory())//配置
            .hostnameVerifier(SSLSocketClient.getHostnameVerifier())//配置
            .pingInterval(10, TimeUnit.SECONDS)
            .eventListenerFactory(RequestEventListener.RequestEventFactory)
//            .addInterceptor(logging)
            .addInterceptor(LoggerInterceptor("担当办公-日志拦截", true))
            .connectTimeout(5, TimeUnit.SECONDS)//设置连接超时时间
            .build()
    }

    internal class InitSocketThread : Thread() {
        override fun run() {
            super.run()
            try {
                initSocket()
            } catch (e: UnknownHostException) {
                e.printStackTrace()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    // 初始化长连接
    @Throws(UnknownHostException::class, IOException::class)
    private fun initSocket() {
        Logger.i("验证线程","====准备建立长链接==当前线程=${Thread.currentThread().name}== ${UserHolder.getSocketImTokne()} = ${UserHolder.getSocketUrl()}")
        isCreatingLongConnect = true//标记正在创建长链接
        reConnectCurrentCount = reConnectCurrentCount + 1
        if (StringUtils.isEmpty(UserHolder.getSocketImTokne())) {
            isCreatingLongConnect = false
            reConnectCurrentCount = 0
            return
        }
        try {
            val longConnectDataUrl = UserHolder.getSocketUrl()

            if (longConnectDataUrl.isNullOrBlank()) {
                isCreatingLongConnect = false
                mHandler.removeCallbacksAndMessages(null)
                isHaveHeartBeat = false
                if (reConnectCurrentCount < reConnectMostCount) {
                    //启动心跳
                    isHaveHeartBeat = true
                    mHandler.postDelayed(heartBeatRunnable, HEART_BEAT_RATE)//开启心跳检测
                } else {
                    reConnectCurrentCount = 0
                    isHaveHeartBeat = false
                }
            } else {
                isHaveHeartBeat = true
                if (!longConnectDataUrl.isNullOrBlank()) {
                    longConnectionUrl = longConnectDataUrl
                    ImService.longConnectUrl = longConnectionUrl
                    //建立长链接
                    try {
                        creatLongConnectionAndBeat(longConnectDataUrl)
                        Logger.i("-执行----长链接验证--", "-----开始建立长链接-url=${longConnectDataUrl}")
                    } catch (e: Exception) {
                        if (isDebug) {
//                                toast("ws链接异常 $longConnectDataUrl")
                            Logger.i(
                                "-执行--长链接验证--ws链接异常Exception--",
                                "-----longConnectDataUrl--" + longConnectDataUrl
                            )
                            mHandler.post { toast("ws链接异常 $longConnectDataUrl") }
                        }
                    }
                } else {
                    if (isDebug) {
//                                toast("ws链接异常 $longConnectDataUrl")
                        Logger.i(
                            "-执行----ws链接异常isNullOrBlank--",
                            "-----longConnectDataUrl--" + longConnectDataUrl
                        )
                        mHandler.post {
                            toast("ws链接异常2 链接为空")
                        }
                    }
                }
            }
        }catch (e : Exception){
            e.printStackTrace()
        }
    }

    var parser: ImDataParse? = null

    private fun creatLongConnectionAndBeat(longConnectionUrl: String) {
        val request = Request.Builder()
            .url(longConnectionUrl)
//            .addHeader("Sec-WebSocket-Protocol", "janus-protocol")
            .build()
        Logger.i("验证线程","====正在建立长链接==当前线程=${Thread.currentThread().name}==${longConnectionUrl}")
        client?.newWebSocket(request, object : WebSocketListener() {

            override fun onOpen(webSocket: WebSocket, response: Response) {//开启长连接成功的回调
                super.onOpen(webSocket, response)
                Logger.i("---执行--长链接验证--", "----建立长链接---成功--")
                //创建成功
                isCreatingLongConnect = false//是否正在创建长链接
                longConnectState = true
                ImService.hasConnect = true

                reConnectCurrentCount = 0
                mWebSocket = webSocket

                //长链接登录
                Logger.i("---执行--登录时使用的-imToken---", "--imtoken--" + (UserHolder.getSocketImTokne() ?: ""))
                val uuid = SendMsgHelper.getUUID()
                if (!StringUtils.isEmpty(UserHolder.getSocketImTokne())){
                    val loginRequestMsg = SendMsgHelper.getLoginRequestMsg(context , UserHolder.getSocketImTokne(), uuid)
                    if (loginRequestMsg != null) {
                        mWebSocket?.send(ByteString.of(*loginRequestMsg))

                        sendSocketStatus(1)

                    }
                }
            }

            override fun onMessage(webSocket: WebSocket, text: String) {//接收消息的回调
                super.onMessage(webSocket, text)
                Logger.i("-------执行--收到消息---String------", text)
            }

            override fun onMessage(webSocket: WebSocket, bytes: ByteString) {
                super.onMessage(webSocket, bytes)
                EventBus.getDefault().post(EventByte(bytes))
            }

            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                super.onClosing(webSocket, code, reason)
                Logger.i("-------执行-----验证退出APP成功------","--onClosing--执行了---")
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                super.onClosed(webSocket, code, reason)
                longConnectState = false
                ImService.hasConnect = false
                isCreatingLongConnect = false//不是正在创建长链接

                Logger.i("-------执行-----长连接断开了-----","--onClosed--执行了---")

                sendSocketStatus(- 1)
            }

            override fun onFailure(
                webSocket: WebSocket,
                t: Throwable,
                response: Response?
            ) {//长连接连接失败的回调
                super.onFailure(webSocket, t, response)
                longConnectState = false
                ImService.hasConnect = false

                isCreatingLongConnect = false//不是正在创建长链接
                Logger.i("---执行-长连接失败了--", "----onFailure---执行了-----")
                BaseApplication.reConnectedToGetOffLineSessionList = true
                sendSocketStatus(0)

                _handleReConnect()

            }
        })
//        client!!.dispatcher().executorService().shutdown()
        mHandler.postDelayed(heartBeatRunnable, HEART_BEAT_RATE)//开启心跳检测
        isHaveHeartBeat = true
    }

    fun _handleReConnect() {
        EventBus.getDefault().post(EventReConnect())
    }

    fun reConnect(imToken: String? , url: String?) {
        if (imToken.isNullOrEmpty() || url.isNullOrEmpty()) {
            Timber.i("从Flutter 获取到的 imToken 或 url 为空")
            return
        }
        UserHolder.storeFlutterSocketParam(imToken, url)
        ImService.doLogin(context ,imToken)
    }

    //制造进入过会话的标记
    val intoSessionTagSet = hashSetOf<String>()

    //判断这个会话是否是打开的状态
    val sessionIsOpeningTagSet = hashSetOf<String>()

    //用来标记单聊、群聊还有通知页面哪个是打开的状态
    val pageStateTagSet = hashSetOf<String>()

    fun makeIntoSessionTag(sessionId: String) {
        //元素的组成是“长链接登录的时间戳+当前用户id+sessionID”
        intoSessionTagSet.add(BaseApplication.longConnectLoginTime.toString() + UserHolder.getUserId() + sessionId)
    }

    fun makeSessionIsOpeningTag(sessionId: String, isOpening: Boolean) {
        if (isOpening == false) {
            if (sessionIsOpeningTagSet.contains(sessionId)) {
                sessionIsOpeningTagSet.remove(sessionId)
            }
        } else {
            sessionIsOpeningTagSet.add(sessionId)
        }
    }

    fun makePageIsOpeningTag(pageTag: String, isOpening: Boolean) {
        if (isOpening == false) {
            if (pageStateTagSet.contains(pageTag)) {
                pageStateTagSet.remove(pageTag)
            }
        } else {
            pageStateTagSet.add(pageTag)
        }
    }

    fun onDestroy() {
        Logger.i("----执行---", "----服务的--onDestroy--执行了-----")
        mHandler.removeCallbacksAndMessages(null)
        mWebSocket?.close(1000, null)
        mHandler.removeCallbacksAndMessages(null)
        mWebSocket = null
        BaseApplication.tcpImServiceIsRunning = false
    }

    //标记这个会话刚才点击进来了
    fun toRemoveAtTag(groupId: String) {
        if (haveAtTagSet.contains(groupId)) {
            haveAtTagSet.remove(groupId)
        }
    }

    //制造进入过会话的标记
    fun addIntoSessionTag(sessionId: String) {
        makeIntoSessionTag(sessionId)
    }


    fun cleanFriendIdSet() {
        friendIdSet.clear()
    }

    //检查targetId是否是自己的好友
    fun checkTargetIdIsFriend(targetId: String): Boolean {
        return friendIdSet.contains(targetId)
    }

    fun checkSessionIsOpening(sessionId: String?): Boolean {
        if (sessionId == null) {
            return false
        } else {
            return sessionIsOpeningTagSet.contains(sessionId)
        }
    }

    fun checkPageIsOpening(pageTag: String?): Boolean {
        return if (pageTag.isNullOrBlank()) {
            false
        } else {
            pageStateTagSet.contains(pageTag)
        }
    }

    //将好友的id存到集合
    fun addFriendIdIntoSet(friendUserId: String) {
        friendIdSet.add(friendUserId)
    }

    //发送消息
    fun sendData(bytes: ByteArray): Boolean {
        if (mWebSocket == null) {
            return false
        }
        var isSend = mWebSocket?.send(okio.ByteString.of(*bytes))
        if (isSend == null) {
            return false
        }
        return isSend
    }

    //连接状态
    fun getServiceLongConnectState(): Boolean {
        return longConnectState
    }

    //登录方法
    fun reLoginInit() {
        Logger.i(
            "---执行--验证长链接--",
            "----（!isCreatingLongConnect && !longConnectState）---" + (!isCreatingLongConnect && !longConnectState)
        )
        if (!isCreatingLongConnect && !longConnectState) {
            mHandler.removeCallbacksAndMessages(null)
            Logger.i("----执行--验证长链接真实登录----","====开始执行一次===")
            InitSocketThread().start()// 登录后 创建一个新的连接
        }
    }

    //登出方法
    fun loginOutInit() {
        mHandler.removeCallbacksAndMessages(null)
        mWebSocket?.close(1000, null)
        mHandler.removeCallbacksAndMessages(null)
        mWebSocket = null
    }

}

fun sendSocketStatus(status: Int) {
    EventBus.getDefault().post(EventSocketStatus(status))
}