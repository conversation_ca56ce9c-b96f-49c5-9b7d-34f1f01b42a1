apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply plugin: 'kotlin-kapt'

apply plugin: 'org.greenrobot.greendao'
//buf修改1
apply plugin: 'com.google.protobuf'

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    buildToolsVersion rootProject.ext.android.buildToolsVersion
    namespace = "com.joinutech.library.im" // 使用模块的实际包名

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
//        versionCode rootProject.ext.android.versionCode
//        versionName rootProject.ext.android.versionName

//        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'

        kapt {
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }
//        ndk { abiFilters "armeabi", "armeabi-v7a", "x86", "mips" }//tbs使用
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    //数据库版本
    greendao {
        schemaVersion 20 //3.2.7版本对应的是16
        daoPackage 'com.greendao.gen'
//        targetGenDir 'src/main/java'
        targetGenDir "$buildDir/generated/source/greendao"

    }
    //buf修改2
    sourceSets {
        main {
            // 定义proto文件目录
            java {
                srcDir 'src/main/java'
            }

            proto {
                srcDir 'src/main/proto'
                include '**/*.proto'
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
//        useIR = true
    }
//    compileOptions {
//        sourceCompatibility JavaVersion.VERSION_1_8
//        targetCompatibility JavaVersion.VERSION_1_8
//    }

    dexOptions {
        incremental true
        javaMaxHeapSize "4g"
    }
    viewBinding{
        enable true
    }

}

//buf修改3
protobuf {
    protoc {
        if (osdetector.os == "osx") {
            artifact = 'com.google.protobuf:protoc:3.7.0:osx-x86_64'
        } else {
            artifact = 'com.google.protobuf:protoc:3.7.0'
        }

    }
    /* plugins {
         javalite {
             artifact = 'com.google.protobuf:protoc-gen-javalite:3.0.0'
         }
     }*/
    generateProtoTasks {
        all().each { task ->
            task.builtins {
                // In most cases you don't need the full Java output
                // if you use the lite output.
                remove java
            }

            /* task.plugins {
                 javalite {}
             }*/
            task.builtins {
                java {}
            }
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation supportLibs.stdlib_jdk7//代替
//    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
//    testImplementation 'junit:junit:4.12'
//    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
//    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
//    implementation 'androidx.appcompat:appcompat:1.2.0'
//    implementation 'androidx.core:core-ktx:1.3.2'
    implementation supportLibs.core_ktx
//    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation project(path: ':silicompressor')

    api project(':ddbeslibrary')
//    implementation 'com.google.android.material:material:1.6.0'
    implementation supportLibs.material

    implementation project(':lib_picture')

    kapt supportLibs.arouter_compiler

    // 文件预览相关tbs，腾讯浏览服务
//    api 'com.tencent.tbs.tbssdk:sdk:43993'
//    api 'com.tencent.tbs.tbssdk:sdk:43967'
//    api 'com.tencent.tbs:tbssdk:44226'//老版本不支持64位CPU系统,,,,注释掉，用jar包SDK代替
    //greenDao 数据库存储
    api supportLibs.greendao
    implementation supportLibs.appcompat
    implementation supportLibs.constraintLayout

//    implementation "com.github.wseemann:FFmpegMediaMetadataRetriever:1.0.14"//部分手机获取第一帧失败时使用

//    api project(':factory_push')
//    api project(':jpush')
    compileOnly project(':LocalRepo:mi_push')

}
