<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!--    <style name="BaseTheme" parent="Theme.AppCompat.Light.NoActionBar">-->
    <!--        <item name="colorPrimary">@color/text_black</item>-->
    <!--        <item name="colorPrimaryDark">@color/text_black</item>-->
    <!--        <item name="colorAccent">@color/colorAccent</item>-->
    <!--        &lt;!&ndash;增加强制竖屏配置&ndash;&gt;-->
    <!--        <item name="android:screenOrientation">portrait</item>-->
    <!--        <item name="android:configChanges">orientation</item>-->
    <!--    </style>-->

    <style name="Splash" parent="AppTheme">
        <!--        <item name="android:windowFullscreen">true</item>-->
        <!--透明背景色-->
        <!--        <item name="android:windowIsTranslucent">true</item>-->
        <!--显示虚拟按键，并腾出空间-->
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowBackground">@drawable/shape_splash</item>
    </style>

    <style name="FullscreenTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@android:color/transparent</item>
        <item name="colorPrimaryDark">@android:color/transparent</item>
        <item name="colorAccent">@android:color/transparent</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@drawable/shape_splash</item>
    </style>

</resources>
