<resources>

    <style name="BaseTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/text_black</item>
        <item name="colorPrimaryDark">@color/text_black</item>
        <item name="colorAccent">@color/bar_grey</item>
        <!--焦点提示颜色-->
        <!--增加强制竖屏配置-->
        <item name="android:screenOrientation">portrait</item>
        <item name="android:configChanges">orientation</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="Test" parent="Theme.AppCompat.NoActionBar">
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>

    <style name="Splash" parent="Test">
        <!--        <item name="android:windowFullscreen">true</item>-->
        <!--透明背景色-->
        <!--        <item name="android:windowIsTranslucent">true</item>-->
        <item name="android:windowBackground">@drawable/shape_splash</item>
    </style>

    <style name="myDialog1">
        <item name="android:windowFrame">@null</item><!--边框-->
        <item name="android:windowIsFloating">true</item><!--是否浮现在activity之上-->
        <item name="android:windowIsTranslucent">false</item><!--半透明-->
        <item name="android:windowNoTitle">true</item><!--无标题-->
        <item name="android:windowBackground">@android:color/transparent</item><!--背景透明-->
        <item name="android:backgroundDimEnabled">false</item><!--模糊-->
        <item name="android:backgroundDimAmount">0.6</item>
    </style>


    <style name="FullscreenTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@android:color/transparent</item>
        <item name="colorPrimaryDark">@android:color/transparent</item>
        <item name="colorAccent">@android:color/transparent</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@drawable/shape_splash</item>
    </style>
</resources>
