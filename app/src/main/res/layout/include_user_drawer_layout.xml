<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_user_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:orientation="vertical">

    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/iv_user_avatar"
        android:layout_width="@dimen/dp_58"
        android:layout_height="@dimen/dp_58"
        android:layout_marginStart="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_55"
        android:src="@drawable/default_heading"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_gender"
        android:layout_width="@dimen/dp_18"
        android:layout_height="@dimen/dp_18"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_user_avatar"
        app:layout_constraintEnd_toEndOf="@id/iv_user_avatar"
        tools:src="@drawable/icon_personinfo_man_small"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/ll_user_name_sex"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingEnd="@dimen/dp_20"
        app:layout_constraintBottom_toTopOf="@id/tv_user_age_position"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_user_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_user_avatar">

        <TextView
            android:id="@+id/tv_user_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:paddingStart="@dimen/dp_15"
            android:paddingEnd="@dimen/dp_10"
            android:singleLine="true"
            android:textColor="@color/colorFF323232"
            android:textSize="@dimen/sp_20"
            tools:text="陈媛媛" />
        <!--todo 审批状态 工作状态标记-->
        <ImageView
            android:id="@+id/iv_work_tag"
            android:layout_width="@dimen/dp_45"
            android:layout_height="@dimen/dp_23"
            android:src="@drawable/ic_work_offline"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_user_age_position"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:paddingStart="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_20"
        android:singleLine="true"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_13"
        app:layout_constraintBottom_toBottomOf="@id/iv_user_avatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_user_avatar"
        app:layout_constraintTop_toBottomOf="@id/ll_user_name_sex"
        tools:text="23岁 · 产品经理" />

    <TextView
        android:id="@+id/tv_user_main_company"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawableStart="@drawable/ic_company"
        android:drawablePadding="@dimen/dp_10"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:paddingTop="@dimen/dp_25"
        android:paddingEnd="@dimen/dp_20"
        android:singleLine="true"
        android:textColor="@color/ff333333"
        android:textSize="@dimen/sp_13"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/iv_user_avatar"
        app:layout_constraintTop_toBottomOf="@id/iv_user_avatar"
        tools:text="加油科技有限公司" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_25"
        app:layout_constraintBottom_toTopOf="@id/line_user_bottom"
        app:layout_constraintTop_toBottomOf="@id/tv_user_main_company" />

    <View
        android:id="@+id/line_user_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_grey"
        app:layout_constraintBottom_toTopOf="@id/tv_user_bottom_title" />

    <TextView
        android:id="@+id/tv_user_bottom_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:paddingTop="@dimen/dp_20"
        android:paddingEnd="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_20"
        android:textColor="@color/color666666"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="客服热线 400-776-1520" />

</androidx.constraintlayout.widget.ConstraintLayout>