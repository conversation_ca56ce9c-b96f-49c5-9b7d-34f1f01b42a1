<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:paddingTop="16dp"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_weight="1"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_height="0dp">

        <TextView
            android:text="@string/privacy_title"
            android:layout_width="wrap_content"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="16dp"
            android:layout_gravity="center_horizontal"
            android:layout_height="wrap_content"/>


        <FrameLayout
            android:id="@+id/privacy_content_webview"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <TextView
            android:id="@+id/privacy_content_tv"
            android:textSize="14sp"
            android:visibility="gone"
            android:textColor="@color/black"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </LinearLayout>

<!--按钮-->
    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="horizontal"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:layout_height="30dp">

        <TextView
            android:id="@+id/privacy_cancel_tv"
            android:text="不同意"
            android:background="@color/transparent_db"
            android:textColor="@color/black"
            android:layout_width="0dp"
            android:gravity="center"
            android:textSize="14sp"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:layout_height="match_parent"/>

        <TextView
            android:id="@+id/privacy_confirm_tv"
            android:text="我已阅读并同意本条款"
            android:background="@color/blue"
            android:textColor="@color/white"
            android:layout_marginStart="8dp"
            android:layout_width="0dp"
            android:gravity="center"
            android:textSize="14sp"
            android:layout_weight="3"
            android:layout_height="match_parent"/>

    </LinearLayout>

</LinearLayout>