<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:layout_centerInParent="true"
    android:layout_marginBottom="2dp">

    <ImageView
        android:id="@+id/iv_item_home"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:focusable="false"
        android:src="@drawable/ic_placeholder"
        android:scaleType="centerInside" />

    <TextView
        android:id="@+id/point_home"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_alignParentRight="true"
        android:layout_marginRight="2dp"
        android:background="@drawable/oval_red_messagedot_bg"
        android:gravity="center"
        android:text=""
        android:textColor="@color/white"
        tools:visibility="gone"
        android:textSize="8sp"
        android:visibility="gone" />

    <View
        android:id="@+id/point_home2"
        android:layout_width="@dimen/dp_6"
        android:layout_height="@dimen/dp_6"
        android:layout_alignParentRight="true"
        android:layout_marginRight="@dimen/dp_10"
        android:background="@drawable/oval_red_messagedot_bg"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_item_home"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_item_home"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="2dp"
        tools:text="tabName"
        android:textColor="@drawable/selector_textc"
        android:textSize="@dimen/textsize_12" />

</RelativeLayout>

