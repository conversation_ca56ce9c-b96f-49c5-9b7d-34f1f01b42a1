<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_org_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:orientation="vertical">

    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/iv_company_logo"
        android:layout_width="58dp"
        android:layout_height="58dp"
        android:layout_marginStart="19dp"
        android:layout_marginTop="29dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_company_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="10dp"
        android:textColor="@color/colorFF323232"
        android:textSize="@dimen/textsize_19"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/iv_company_logo"
        app:layout_constraintTop_toBottomOf="@id/iv_company_logo" />

    <TextView
        android:layout_width="77dp"
        android:layout_height="22dp"
        android:layout_marginTop="47dp"
        android:layout_marginEnd="24dp"
        android:background="@drawable/shape_home_current_company"
        android:gravity="center"
        android:text="当前团队"
        android:textColor="#3e73f2"
        android:textSize="@dimen/textsize_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="24dp"
        android:background="@color/line_grey"
        app:layout_constraintTop_toBottomOf="@id/tv_company_name" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_work"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="19dp"
        android:background="@color/white"
        android:overScrollMode="never"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line"
        app:layout_constraintVertical_weight="1" />
</androidx.constraintlayout.widget.ConstraintLayout>