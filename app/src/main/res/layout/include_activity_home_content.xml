<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!--头部 title bar-->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:minHeight="?android:attr/actionBarSize"
        android:visibility="gone"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp"
        app:title=""
        tools:visibility="visible">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_42">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxEms="10"
                android:maxLines="1"
                android:textColor="@color/text_black"
                android:textSize="@dimen/textsize_17"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/lay_toolbar_left"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/iv_toolbar_left"
                    android:layout_width="19dp"
                    android:layout_height="19dp"
                    android:src="@drawable/icon_home_left_select"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/iv_left_dot_new"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:background="@drawable/shape_round_red_dot"
                    android:visibility="gone"
                    app:layout_constraintBottom_toTopOf="@id/iv_toolbar_left"
                    app:layout_constraintEnd_toEndOf="@id/iv_toolbar_left"
                    app:layout_constraintStart_toEndOf="@id/iv_toolbar_left"
                    app:layout_constraintTop_toTopOf="@id/iv_toolbar_left" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!--最右边-->
            <TextView
                android:id="@+id/tv_toolbar_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right|center_vertical"
                android:layout_marginEnd="@dimen/dp_15"
                android:layout_marginRight="@dimen/dp_15"
                android:text="保存"
                android:textColor="@color/text_black"
                android:textSize="@dimen/textsize_14"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <!--最右边一个 icon-->
            <ImageView
                android:id="@+id/iv_toolbar_right"
                android:layout_width="@dimen/dp_30"
                android:layout_height="@dimen/dp_19"
                android:layout_gravity="right|center_vertical"
                android:layout_marginEnd="@dimen/dp_5"
                android:paddingEnd="@dimen/dp_10"
                android:scaleType="centerInside"
                android:src="@drawable/ic_placeholder"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_toolbar_right"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginEnd="@dimen/dp_5"
                tools:visibility="visible" />

            <!--右边倒数第二个 icon-->
            <ImageView
                android:id="@+id/iv_toolbar_right2"
                android:layout_width="@dimen/dp_30"
                android:layout_height="@dimen/dp_19"
                android:layout_marginEnd="@dimen/dp_15"
                android:paddingStart="@dimen/dp_10"
                android:paddingLeft="@dimen/dp_10"
                android:scaleType="centerInside"
                android:src="@drawable/ic_placeholder"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/iv_toolbar_right"
                app:layout_constraintEnd_toStartOf="@id/iv_toolbar_right"
                app:layout_constraintTop_toTopOf="@id/iv_toolbar_right"
                tools:visibility="visible" />

            <View
                android:id="@+id/iv_dot_right_new"
                android:layout_width="@dimen/dp_5"
                android:layout_height="@dimen/dp_5"
                android:background="@drawable/shape_round_red_dot"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/iv_toolbar_right2"
                app:layout_constraintEnd_toEndOf="@id/iv_toolbar_right2"
                app:layout_constraintStart_toEndOf="@id/iv_toolbar_right2"
                app:layout_constraintTop_toTopOf="@id/iv_toolbar_right2"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>

    <!--页面内容-->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/parent_home"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <com.joinutech.common.widget.NoScrollPager
                android:id="@+id/vp_home"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/line_grey" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/tablayout_home"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    app:tabIndicatorHeight="0dp"
                    app:tabRippleColor="@color/white">

                    <com.google.android.material.tabs.TabItem
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                    <com.google.android.material.tabs.TabItem
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                    <com.google.android.material.tabs.TabItem
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                    <com.google.android.material.tabs.TabItem
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                    <com.google.android.material.tabs.TabItem
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />
                </com.google.android.material.tabs.TabLayout>

                <View
                    android:id="@+id/msg_click"
                    android:layout_width="20dp"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@+id/tablayout_home"
                    android:layout_alignBottom="@+id/tablayout_home"
                    android:layout_centerVertical="true"
                    android:background="@color/transparent"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </RelativeLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/tab_work"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="4dp"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_work_sel" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:text="工作"
                android:textColor="@color/main_blue"
                android:textSize="@dimen/textsize_10" />
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>