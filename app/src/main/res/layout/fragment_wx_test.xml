<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_54" />

    <TextView
        android:id="@+id/tv_link"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:autoLink="all"
        android:autoSizeTextType="uniform"
        android:linksClickable="false"
        android:padding="@dimen/dp_10"
        android:text="有事请百度 http://www.baidu.com ，无事请闭嘴"
        android:textColorLink="@color/blue" />

    <TextView
        android:id="@+id/tv_link2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:autoLink="all"
        android:autoSizeTextType="uniform"
        android:linksClickable="false"
        android:padding="@dimen/dp_10"
        android:text="有事请百度 http://www.baidu.com,http://www.sina.com 无事请闭嘴"
        android:textColorLink="@color/blue" />

    <TextView
        android:id="@+id/tv_link3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:autoLink="all"
        android:autoSizeTextType="uniform"
        android:linksClickable="false"
        android:padding="@dimen/dp_10"
        android:text="有事请百度http://无事请闭嘴"
        android:textColorLink="@color/blue" />

    <Button
        android:id="@+id/btn_test_link"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="测试链接拦截添加点击事件" />

    <TextView
        android:id="@+id/tv_link4"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:autoLink="all"
        android:linksClickable="false"
        android:padding="@dimen/dp_10"
        android:text="有事请百度 http://www.baidu.com,http://www.sina.com 无事请闭嘴" />

    <Button
        android:id="@+id/btn_select_file"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="select_file" />

    <Button
        android:id="@+id/btn_share_file"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="share_file"
        android:visibility="gone" />
</LinearLayout>