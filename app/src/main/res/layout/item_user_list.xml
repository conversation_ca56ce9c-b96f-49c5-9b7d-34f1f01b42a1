<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:id="@+id/line_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_grey"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:layout_marginStart="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="@id/tv_content"
        app:layout_constraintEnd_toStartOf="@id/tv_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_content" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_marginBottom="@dimen/dp_20"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:paddingStart="@dimen/dp_20"
        android:paddingEnd="@dimen/dp_20"
        android:singleLine="true"
        android:textColor="@color/colorFF323232"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toTopOf="@id/line_bottom"
        app:layout_constraintEnd_toStartOf="@id/iv_more"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toBottomOf="@id/line_top"
        app:layout_goneMarginBottom="@dimen/dp_20"
        app:layout_goneMarginTop="@dimen/dp_20"
        tools:text="个人资料" />

    <ImageView
        android:id="@+id/iv_more"
        android:layout_width="@dimen/dp_14"
        android:layout_height="@dimen/dp_14"
        android:layout_marginEnd="@dimen/dp_20"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintBottom_toBottomOf="@id/tv_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_content" />

    <View
        android:id="@+id/line_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_grey"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>