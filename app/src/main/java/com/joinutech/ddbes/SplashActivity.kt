package com.joinutech.ddbes

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import com.gyf.immersionbar.BarHide
import com.joinutech.common.helper.LoginHelper
import com.joinutech.ddbes.statics.StaticsHelper
import com.joinutech.common.util.ObjectStore
import com.joinutech.ddbes.flutter.FlutterHelper
import com.joinutech.ddbes.flutter.FlutterHomeMultiActivity
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.utils.AppManager
import com.joinutech.ddbeslibrary.utils.MMKVKEY.APP_Statistics
import com.joinutech.ddbeslibrary.utils.MMKVUtil


/**
 * Description 启动页
 * Author HJR36
 * Date 2018/6/15 11:42
 */
class SplashActivity : MyUseBaseActivity() {

    override val contentViewResId: Int = R.layout.activity_splash

    override fun showToolBar(): Boolean = false

    override fun openArouterReceive(): Boolean = false

    var privacyLauncher : ActivityResultLauncher<Intent>? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        handIntent(intent)

        StaticsHelper.preInit(this)
    }

    override fun beforeSetContent() {
        mImmersionBar?.supportActionBar(false)
                ?.hideBar(BarHide.FLAG_HIDE_BAR)
                ?.init()

        MMKVUtil.saveString("appMustUpdate", "")
        //记录app打开的次数
        MMKVUtil.saveInt(APP_Statistics, 1)
    }

    override fun initImmersion() {
    }

    override fun initView() {
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        handIntent(intent)
    }

    private fun handIntent(intent: Intent?) {
        val meetingId = intent?.getStringExtra("rtcMeetingId")
        ObjectStore.add("link_meetingId" , meetingId)
    }

    override fun initLogic() {
        mHandler = Handler()

        privacyLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            openRootAct()
            finish()
        }

        mHandler?.postDelayed({
            jumpPage()

        },1500)
    }

    private var mHandler: Handler? = null

    fun jumpPage() {
        if (!LoginHelper.isReadPrivacy() ) {
            val intent = Intent(this, PrivacyActivity::class.java)
            privacyLauncher?.launch(intent)
        } else {
            openRootAct()
            finish()

        }
    }

    private fun openRootAct() {
        try {
            AppManager.single_instance.finishActivity(FlutterHomeMultiActivity::class.java)
        }catch (e: Exception){
            e.printStackTrace()
        }
        FlutterHelper.openFlutterRootAct(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        mHandler?.removeCallbacksAndMessages(null)
    }

}