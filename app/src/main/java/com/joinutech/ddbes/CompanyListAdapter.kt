package com.joinutech.ddbes

import android.content.Context
import android.view.View
import android.widget.TextView
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.CircleImageView
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter

/**
 *<AUTHOR>
 *@date 2019/3/28
 */
class CompanyListAdapter(context: Context, var mList: ArrayList<WorkStationBean>) :
        CommonAdapter<WorkStationBean>(context, mList, com.joinutech.message.R.layout.item_company) {
    lateinit var mWorkItemClick: WorkItemClick

    fun setWorkItemListener(listener: WorkItemClick) {
        mWorkItemClick = listener
    }

    override fun bindData(holder: <PERSON>Holder, data: WorkStationBean, position: Int) {
        if (data.companyId != "0") {
            holder.setViewVisibility(com.joinutech.message.R.id.tv_cooperation_org_name, View.GONE)
            holder.setViewVisibility(com.joinutech.message.R.id.cl_root_layout, View.VISIBLE)
            if (data.isSelected) {
                holder.getView<View>(com.joinutech.message.R.id.cl_root_layout)
                        .setBackgroundColor(mContext.resources.getColor(com.joinutech.ddbeslibrary.R.color.colorF1F5F8))
                holder.getView<TextView>(com.joinutech.message.R.id.name_company)
                        .setTextColor(mContext.resources.getColor(com.joinutech.approval.R.color.color3E73F2))
            } else {
                holder.getView<View>(com.joinutech.message.R.id.cl_root_layout)
                        .setBackgroundColor(mContext.resources.getColor(com.joinutech.ddbeslibrary.R.color.white))
                holder.getView<TextView>(com.joinutech.message.R.id.name_company)
                        .setTextColor(mContext.resources.getColor(com.joinutech.ddbeslibrary.R.color.colorFF323232))
            }
            if (StringUtils.isNotBlankAndEmpty(data.logo)) {
                ImageLoaderUtils.loadImage(mContext,
                        holder.getView<CircleImageView>(com.joinutech.message.R.id.header_company), mList[position].logo)
            }
            holder.setText(com.joinutech.message.R.id.name_company, mList[position].name)
            /*团队列表红点*/
            if (data.haveApprove > 0) {
                holder.getView<View>(com.joinutech.message.R.id.approval_red_dot).visibility = View.VISIBLE
            } else {
                holder.getView<View>(com.joinutech.message.R.id.approval_red_dot).visibility = View.GONE
            }
            holder.setOnItemClickListener {
                mWorkItemClick.onWorkItemClick(position)
            }
        } else {
            holder.setViewVisibility(com.joinutech.message.R.id.tv_cooperation_org_name, View.VISIBLE)
            holder.setViewVisibility(com.joinutech.message.R.id.cl_root_layout, View.GONE)
        }
    }

    interface WorkItemClick {
        fun onWorkItemClick(position: Int)
    }
}