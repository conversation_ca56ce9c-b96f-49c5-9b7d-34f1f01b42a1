package com.joinutech.ddbes.wxapi

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.ddbes.library.im.imtcp.ConstantEventCodeUtil
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.bean.WXAccessTokenEntity
import com.joinutech.ddbeslibrary.bean.WXBaseRespEntity
import com.joinutech.ddbeslibrary.bean.WXUserInfo
import com.joinutech.ddbeslibrary.utils.*
import com.marktoo.lib.cachedweb.LogUtil
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import com.zhy.http.okhttp.OkHttpUtils
import com.zhy.http.okhttp.callback.StringCallback
import okhttp3.Call

//微信支付回调
class WXPayEntryActivity : AppCompatActivity(), IWXAPIEventHandler {
    private lateinit var api: IWXAPI

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        api = WXAPIFactory.createWXAPI(this, APP_ID_WX, true)
        api.registerApp(APP_ID_WX)
        //注意：
        //第三方开发者如果使用透明界面来实现WXEntryActivity，需要判断handleIntent的返回值，
        // 如果返回值为false，则说明入参不合法未被SDK处理，应finish当前透明界面，
        // 避免外部通过传递非法参数的Intent导致停留在透明界面，引起用户的疑惑
        val result = api.handleIntent(intent, this)
        if (!result) {
            logShow("参数不合法，未被SDK处理，退出")
            finish()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        api.handleIntent(data, this)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        setIntent(intent)
        api.handleIntent(intent, this)
        finish()
    }

    private fun logShow(text: String) {
        LogUtil.showLog(text, "wx__")
    }

    override fun onReq(baseReq: BaseReq?) {
        logShow("baseReq: ${GsonUtil.toJson(baseReq)}")
    }

    override fun onResp(resp: BaseResp?) {
        val json = GsonUtil.toJson(resp)
        Loggerr.i("微信支付回调", "====执行了===${json}")
        //根据errCode判断情况
        if (resp?.type == ConstantsAPI.COMMAND_PAY_BY_WX) {
            val payResult:String= resp?.errCode?.toString() ?:""
            EventBusUtils.sendEvent(
                EventBusEvent(PushEvent.TCP_WX_PAY_RESULT_CODE, payResult)
            )
            finish()
        }
    }

}