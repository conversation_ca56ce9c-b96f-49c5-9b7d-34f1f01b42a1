package com.joinutech.ddbes.scheme

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import com.joinu.rtcmeeting.RtcManager
import com.joinutech.ddbes.SplashActivity
import com.joinutech.flutter.RtcEventStartMeeting
import org.greenrobot.eventbus.EventBus
import timber.log.Timber


/**
 * @Des：
 * @author: moon
 * @date: 1/22/24
 */
class RtcMeetingSchemeActivity: Activity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        handIntent(intent)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        handIntent(intent)
    }

    private fun handIntent(intent: Intent?) {

        val uri = intent?.data
        val meetingId = uri?.getQueryParameter("meetingId")

        Timber.i("RtcMeetingSchemeActivity =====>   ${meetingId}")
        // 进入 直播 or 会议详情
        if (meetingId.isNullOrEmpty() || meetingId == "undefined"){
            Timber.i("链接传递过来的会议id 为空")
        }else {
            if (!isExist()) {
                // 冷启动 启动splash 然后传值
                Timber.i("冷启动： meetingId: ----->  $meetingId")
                val splashIntent = Intent(this , SplashActivity::class.java)
                splashIntent.putExtra("rtcMeetingId" , meetingId)
                startActivity(splashIntent)
            }else{
                Timber.i("热启动： meetingId: ----->  $meetingId")
                EventBus.getDefault().post(RtcEventStartMeeting(meetingId))
            }
        }
        finish()
    }

    private fun isExist() : Boolean{
       return  RtcManager.homeIsOpen
//        return AppManager.single_instance.isOpenActivity(FlutterHomeBaseActivity::class.java)
    }
}

