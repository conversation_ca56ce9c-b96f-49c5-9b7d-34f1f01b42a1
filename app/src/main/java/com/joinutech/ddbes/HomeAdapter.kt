package com.joinutech.ddbes


import android.annotation.SuppressLint
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentPagerAdapter
import com.joinutech.addressbook.view.AddressbookFragment
import com.joinutech.common.helper.OnFragmentResumeListener
import com.joinutech.ddbes.clouddoc.CloudDocFragment
import com.joinutech.message.view.tcpimpages.imfragment.SessionListFragment
//import com.jounutech.task.view.fragment.ProgramFragment
import com.jounutech.work.view.fragment.WorkFragmentNew
import com.marktoo.lib.cachedweb.LogUtil

/**
 * @PackageName: com.joinutech.ddbes
 * @ClassName:
 * @Author: liuchuchu zyy
 * @Leader: Ke
 * @CreateTime: 2020/6/2 11:12
 * @Desc: 主页中各子页面适配器
 */
@SuppressLint("WrongConstant")
class HomeAdapter(fm: androidx.fragment.app.FragmentManager,
                  private val listener: OnFragmentResumeListener)
    : FragmentPagerAdapter(fm, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT) {

    private val fragments = arrayOfNulls<Fragment>(5)

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        LogUtil.showLog("HomeActivity HomeAdapter instantiateItem(${position})")
        val obj = super.instantiateItem(container, position)
        fragments[position] = when (position) {
            4 -> {
                val address = obj as AddressbookFragment
                address.listener = listener
                address
            }
            3 -> {
                val cloudDoc = obj as CloudDocFragment
                cloudDoc.listener = listener
                cloudDoc
            }
            2 -> {
                val work = obj as WorkFragmentNew
                work.listener = listener
                work
            }
//            1 -> {
//                val program = obj as ProgramFragment
//                program.listener = listener
//                program
//            }
            else -> {
                val message = obj as SessionListFragment
                message.listener = listener
                message
            }
        }
        return fragments[position]!!
    }

    override fun getItem(position: Int): Fragment {
        LogUtil.showLog("HomeActivity HomeAdapter getItem(${position})")
        return when (position) {
            4 -> {
                val address = AddressbookFragment()
                address.listener = listener
                address
            }
            3 -> {
                val cloudDoc = CloudDocFragment()
                cloudDoc.listener = listener
                cloudDoc
            }
            2 -> {
                val work = WorkFragmentNew()
                work.listener = listener
                work
            }
//            1 -> {
//                val program = ProgramFragment()
//                program.listener = listener
//                program
//            }
            else -> {
                val message = SessionListFragment()
                message
            }
        }
    }

    override fun getCount(): Int {
        return fragments.size
    }

}
