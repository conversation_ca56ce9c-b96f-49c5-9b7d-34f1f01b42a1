package com.joinutech.ddbes

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.lib.vc.view.activity.chat.InviteMediaChatActivity
import com.ddbes.library.im.ImService
import com.ddbes.library.im.imtcp.ConstantEventCodeUtil
import com.ddbes.library.im.imtcp.Logger
import com.ddbes.library.im.netutil.OfflineNewPush
import com.ddbes.library.im.netutil.retrofit.ImRetrofitUtil
import com.joinu.rtcmeeting.RtcManager
import com.joinutech.common.base.isDebug
import com.joinutech.common.util.ObjectStore
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbes.flutter.FlutterConstant
import com.joinutech.ddbes.flutter.FlutterHelper
import com.joinutech.ddbes.flutter.MethodChannelManager
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.utils.AppManager
import com.joinutech.ddbeslibrary.utils.DeviceUtil
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.flutter.EventCallVoiceBean
import com.joinutech.flutter.EventForceRefreshWorkbench
import io.flutter.embedding.engine.FlutterEngineGroup
import io.reactivex.exceptions.UndeliverableException
import io.reactivex.plugins.RxJavaPlugins
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import timber.log.Timber

class AppApplication : BaseApplication() {

    lateinit var engines: FlutterEngineGroup

    override fun init() {
        if (isDebug || DeviceUtil.getPKgVersionName(applicationContext).contains("-")) {
            ARouter.openLog()     // 打印日志
            ARouter.openDebug()   // 开启调试模式(如果在InstantRun模式下运行，必须开启调试模式！
            // 线上版本需要关闭,否则有安全风险)
        }
        engines = FlutterEngineGroup(this)
        //ARouter 初始化
        ARouter.init(this) // 尽可能早，推荐在Application中初始化
        Logger.showLog = isDebug //im log配置
        closeAndroidPDialog()

        // 取消这个功能： 后台断开IM功 ！！！！
        checkBackgroundProcess()

        if (BuildConfig.DEBUG){
            Timber.plant(Timber.DebugTree())
        }

        FlutterHelper.initFlutter(this)

        releaseRxError()

        RtcManager.initRtcLog(this)

    }

    private fun releaseRxError() {
        try {
            if (RxJavaPlugins.getErrorHandler() != null || RxJavaPlugins.isLockdown()) return
            RxJavaPlugins.setErrorHandler {
                if (isDebug){
                    Timber.i(it)
                }
            }
        }catch (e:Exception){}
    }

    private fun closeAndroidPDialog() {
        try {
            val aClass = Class.forName("android.content.pm.PackageParser\$Package")
            val declaredConstructor = aClass.getDeclaredConstructor(String::class.java)
            declaredConstructor.isAccessible = true
        } catch (e: Exception) {
            e.printStackTrace()
        }

        try {
            val cls = Class.forName("android.app.ActivityThread")
            val declaredMethod = cls.getDeclaredMethod("currentActivityThread")
            declaredMethod.isAccessible = true
            val activityThread = declaredMethod.invoke(null)

            val targetField = cls.declaredFields.find { it.name == "mHiddenApiWarningShown" }
            if (targetField != null) {
                targetField.isAccessible = true
                targetField.setBoolean(activityThread, true)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private var appCount = 0

    private fun checkBackgroundProcess() {
        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}
            override fun onActivityStarted(activity: Activity) {
                appCount++
                if (isRunInBackground) {
                    Timber.i("回到前台")
                    isRunInBackground = false
//                    if (!ImService.hasConnect){
//                        Timber.i("回到前台 , Im 断了 重启应用")
//                        val intent = getPackageManager().getLaunchIntentForPackage(getPackageName());
//                        intent?.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
//                        startActivity(intent);
//                        android.os.Process.killProcess(android.os.Process.myPid());
//                    }
                    refreshWork()

                    MethodChannelManager.invokeMainEngineFlutter(FlutterConstant.ONAPPRESUMED)
//                    loginIM()
                    callAVoicePage()
                }
            }

            override fun onActivityResumed(activity: Activity) {}
            override fun onActivityPaused(activity: Activity) {}
            override fun onActivityStopped(activity: Activity) {
                appCount--
                if (appCount == 0) {
                    isRunInBackground = true
                    Timber.i( "退到后台")
                    MethodChannelManager.invokeMainEngineFlutter(FlutterConstant.ONAPPPAUSED)
//                    disConnectIM()
                }
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
            override fun onActivityDestroyed(activity: Activity) {}
        })
    }

    private fun refreshWork(){
        EventBus.getDefault().post(EventForceRefreshWorkbench())
    }

    private fun callAVoicePage() {
        val lastCallTime = (ObjectStore.remove("there_a_call") as? EventCallVoiceBean) ?: return
        if (lastCallTime.time <= 0L) return
        val diff = (System.currentTimeMillis() - lastCallTime.time)
        if (diff > 60 * 1000){
            ObjectStore.remove("there_a_call")
            return
        }
        val stacks = AppManager.single_instance.getAllActivity()
        if (stacks.isEmpty()) return
        val act = stacks.last()
        InviteMediaChatActivity.open(act , lastCallTime.meetingId,lastCallTime.sendName)
    }

    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    // 登录前标记 ，先判断login token是否过期
    private fun loginIM(context: Context) {
        applicationScope.launch {
            withContext(Dispatchers.IO){
                val resp = ImRetrofitUtil.getMsgTopIds()
                if (!resp.isExpired()) {
                    withContext(Dispatchers.Main){
                        Timber.i("可以进入重连逻辑")
                        val longConnectState = ImService.getLoginState()
                        if (longConnectState) return@withContext
                        ImService.doLogin(context ,UserHolder.getImToken())
                    }
                }
            }
        }



    }

    private fun disConnectIM() {
        ImService.doLogout()
    }

    companion object {
        var isRunInBackground = false
    }


    override fun onTerminate() {
        super.onTerminate()
        RtcManager.destroy()
    }

}