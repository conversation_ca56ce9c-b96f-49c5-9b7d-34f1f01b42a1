package com.joinutech.ddbes

//import com.xys.libzxing.zxing.activity.CaptureActivity
import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.ShortcutInfo
import android.content.pm.ShortcutManager
import android.graphics.Bitmap
import android.graphics.drawable.Icon
import android.net.ConnectivityManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.text.style.ForegroundColorSpan
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.drawerlayout.widget.DrawerLayout
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.library.im.imfile.tcpfileutil.DealFileUtil
import com.ddbes.library.im.imtcp.ConstantEventCodeUtil
import com.ddbes.library.im.imtcp.Logger
import com.ddbes.library.im.imtcp.dbope.DDbesDbManager
import com.ddbes.library.im.imtcp.dbope.FriendDaoOpe
import com.ddbes.library.im.netutil.ImNetUtil
import com.ddbes.library.im.netutil.ImServiceNetUtil
import com.ddbes.library.im.util.FriendCacheHolder
import com.ddbes.library.im.util.GroupService
import com.gyf.immersionbar.ImmersionBar
import com.joinutech.addressbook.AddressProvider
import com.joinutech.approval.utils.UploadFileUtil
import com.joinutech.common.adapter.MyAdapter
import com.joinutech.common.base.isDebug
import com.joinutech.common.helper.OnFragmentResumeListener
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.common.storage.FileStorage
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbes.databinding.ActivityHomeBinding
import com.joinutech.ddbeslibrary.VersionConfig
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.base.BaseApplication.Companion.currentNetState
import com.joinutech.ddbeslibrary.base.BaseApplication.Companion.currentNetType
import com.joinutech.ddbeslibrary.base.BaseApplication.Companion.imToken
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.AppStartResult
import com.joinutech.ddbeslibrary.bean.AppVersionBean
import com.joinutech.ddbeslibrary.bean.EntroyBean
import com.joinutech.ddbeslibrary.bean.FriendSelectBean
import com.joinutech.ddbeslibrary.bean.GroupCreateBean
import com.joinutech.ddbeslibrary.bean.GroupInviteUserInfoBean
import com.joinutech.ddbeslibrary.bean.InteGrateBean
import com.joinutech.ddbeslibrary.bean.UploadFileBean
import com.joinutech.ddbeslibrary.bean.UserInfo
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.org.GlobalCompanyHolder
import com.joinutech.ddbeslibrary.receiver.NetBroadcastReceiver
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.request.interceptor.RequestCache
import com.joinutech.ddbeslibrary.service.PersonService
import com.joinutech.ddbeslibrary.utils.AppVersionCheckUpdateUtil
import com.joinutech.ddbeslibrary.utils.BottomDialogUtil
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.ConsKeys
import com.joinutech.ddbeslibrary.utils.ConsValue
import com.joinutech.ddbeslibrary.utils.DeviceUtil
import com.joinutech.ddbeslibrary.utils.DialogHolder
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusAction.EVENT_WORK_BANNER_REFRESH
import com.joinutech.ddbeslibrary.utils.EventBusAction.GLOBAL_LOGIN_TRY
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.IMEvent
import com.joinutech.ddbeslibrary.utils.IM_VC_SELECT_PERSON
import com.joinutech.ddbeslibrary.utils.Loggerr
import com.joinutech.ddbeslibrary.utils.MMKVKEY
import com.joinutech.ddbeslibrary.utils.MMKVUtil
import com.joinutech.ddbeslibrary.utils.PermissionUtils
import com.joinutech.ddbeslibrary.utils.PushEvent
import com.joinutech.ddbeslibrary.utils.RouteIm
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.RouteProvider
import com.joinutech.ddbeslibrary.utils.RouteVc
import com.joinutech.ddbeslibrary.utils.ScanResultUtil
import com.joinutech.ddbeslibrary.utils.ScreenUtils
import com.joinutech.ddbeslibrary.utils.Spanny
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.XUtil
import com.joinutech.ddbeslibrary.utils.home
import com.joinutech.ddbeslibrary.utils.image.GlideEngine
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.utils.shortCutId
import com.joinutech.ddbeslibrary.utils.toastShort
import com.jounutech.work.view.attend.record.AttendanceHomeActivity
import com.luck.picture.lib.config.PictureSelectionConfig
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import kotlin.math.max

/**
 *<AUTHOR>
 *@JsonBean 2018/11/6
 */
@Suppress("DEPRECATION", "UNCHECKED_CAST")
@Route(path = home)
class HomeActivity : MyUseBindingActivity<ActivityHomeBinding>(), ViewPager.OnPageChangeListener,
    CompanyListAdapter.WorkItemClick, OnFragmentResumeListener, DrawerLayout.DrawerListener {

    override val contentViewResId: Int = R.layout.activity_home
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityHomeBinding {
        return ActivityHomeBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = false

    override fun openArouterReceive(): Boolean = true

    private val tabsTexts = arrayOf("消息", "任务", "工作", "云文档", "通讯录")

//    private val tabsTexts = arrayOf("消息", "任务", "工作", "通讯录", "我的")

    //切换底部图标
    private val imgTabId = intArrayOf(
        R.drawable.selector_mes_tab,
        R.drawable.selector_task_tab, R.drawable.selector_work_tab,
        R.drawable.selector_yun_doc, R.drawable.selector_address_tab
    )
//            R.drawable.selector_person_tab)

    /**团队数据状态 1 存在团队信息 2 存在协作团队信息 0 无数据*/
    private var orgState = 0

//    @JvmField
//    @Autowired(name = RouteIm.CHAT_SERVICE)
//    var chatService: RouteServiceProvider? = null

    @JvmField
    @Autowired(name = "/addressbook/service/org")
    var orgService: RouteServiceProvider? = null


    @JvmField
    @Autowired(name = "/Personal/provider")
    var personProvider: RouteServiceProvider? = null

    /**调用并初始化云文档相关*/
    @JvmField
    @Autowired(name = "/clouddoc/service")
    var cloudDocService: RouteServiceProvider? = null

//    @JvmField
//    @Autowired(name = "/lib_im/service/im")
//    var imService: RouteServiceProvider? = null

    /**
     * exitTime to onBackPressed()
     */
    private var exitTime: Long = 0
    private val scan = 6

    /**im登录状态*/
    private var imLoginSuccess = false

    //    private var retryImLoginNum = 3
    private var receiver: NetBroadcastReceiver? = NetBroadcastReceiver()
    private var immersionBar: ImmersionBar? = null

    override fun initImmersion() {
//        Log.e("app_start", "app_start :: 进入主页面,耗时${System.currentTimeMillis() - BaseApplication.start}毫秒")
        // 进入页面后再初始化相关服务，避免启动过慢
        (application as BaseApplication).initConfig()
        //进入home页之后，首先启动tcpim前台服务
        if (!BaseApplication.tcpImServiceIsRunning) {
            showLog("当前未启动im，开始初始化im调用--->>")
            //路由的使用，，，，获取provider对象
            (ARouter.getInstance().build(RouteProvider.IM_PROVIDER)
                .navigation() as RouteServiceProvider)
                .openPage("imInit", Bundle())
            (ARouter.getInstance().build(RouteProvider.IM_PROVIDER)
                .navigation() as RouteServiceProvider)
                .openPage("initTBS", Bundle())
        }

        if (immersionBar == null) {
            immersionBar = ImmersionBar.with(this)
        }
//        immersionBar?.titleBar(R.id.toolbar)?.init()

        if (PictureSelectionConfig.imageEngine == null) {
            PictureSelectionConfig.imageEngine = GlideEngine.createGlideEngine()
        }

//        XUtil.initBugly()
        getDeviceVersion()
        XUtil.initLocation()
    }

    private fun getDeviceVersion() {
        val dbVersion = DDbesDbManager.getInstance(this)?.getReadableDatabase(this)?.version
        ConsValue.deviceModel = DeviceUtil.getPlatformInfo(this).plus("/dbv_$dbVersion")
        showLog("deviceModel info = ${ConsValue.deviceModel}")
        MMKVUtil.saveString(MMKVKEY.VERSION_NAME, BuildConfig.VERSION_NAME)
        MMKVUtil.saveInt(MMKVKEY.VERSION_CODE, BuildConfig.VERSION_CODE)
        val versionConfig = VersionConfig()
        val imei = MMKVUtil.getString(MMKVKEY.IMEI)
        showLog(
            "保存应用功能版本号: deviceImei = $imei," +
                    " vName = ${versionConfig.versionName} ," +
                    " vCode =${versionConfig.versionCode} ," +
                    " vCode2 =${versionConfig.versionCode2} ," +
                    "deviceModel = ${ConsValue.deviceModel}"
        )
    }

    private var statusBarHeight: Int = 0

    override fun initView() {
        statusBarHeight = ScreenUtils.getStatusBarHeight(this)
        //防止进程被突然杀死没有去除进入了视频房间的标志，在此做初始化
        MMKVUtil.saveInt(MMKVKEY.ENTRY_VIDEO_ROOM, 0)
        binding.activityHomeContent.vpHome.adapter = HomeAdapter(supportFragmentManager, this)
        binding.activityHomeContent.vpHome.offscreenPageLimit = tabsTexts.size
        binding.activityHomeContent.vpHome.currentItem = 2
        binding.activityHomeContent.vpHome.addOnPageChangeListener(this)
        binding.activityHomeContent.tablayoutHome.setupWithViewPager(binding.activityHomeContent.vpHome)
        initDrawer()
        initTab()
        //一进来是工作首页，初始化一些控件
//        binding.activityHomeContent.tablayoutHome.getTabAt(2)!!.customView?.visibility = View.INVISIBLE
        hideToolBarLine()
        binding.activityHomeContent.tabWork.visibility = View.GONE
        dealPushEvent()
//        Upgrade.checkUpdate(this, false)
    }

    private var workList: ArrayList<WorkStationBean> = arrayListOf()//团队信息集合，，，即WorkStationBean的集合
    private lateinit var workAdapter: CompanyListAdapter

    private lateinit var userInfoAdapter: MyAdapter<UserEntrance>
    private val userEntrances = arrayListOf(
        UserEntrance("个人资料", R.drawable.ic_user_detail),
        UserEntrance("二维码", R.drawable.ic_user_qrcode),
        UserEntrance("设置", R.drawable.ic_user_setting),
        UserEntrance("关于担当", R.drawable.ic_app_about)
    )

    private val userEnts = arrayListOf<UserEntrance>()

    data class UserEntrance(val name: String, val icon: Int)

    /**初始化侧滑页面 并锁定侧滑，只有点击左上角按钮才会展开侧滑菜单页*/
    private fun initDrawer() {
        fun initUserDrawer() {
            userInfoAdapter = MyAdapter(this, R.layout.item_user_list,
                userEnts,
                onBindItem = { position: Int, data: UserEntrance, view: View ->
                    if (position == 0 || position == 2) {
                        // show top line
                        view.findViewById<View>(R.id.line_top).visibility = View.VISIBLE
                    } else {
                        view.findViewById<View>(R.id.line_top).visibility = View.GONE
                    }
                    view.findViewById<TextView>(R.id.tv_content).text = data.name
                    view.findViewById<ImageView>(R.id.iv_icon).setImageResource(data.icon)
                },
                onItemClick = { position: Int, _: UserEntrance, _: View ->
                    val bundle = Bundle()
                    when (position) {
                        0 -> {//全局搜索“跳转到指定页面”
                            personProvider?.openPageWithResult(this, "user_detail", bundle) {}
                        }
                        1 -> {
                            UserHolder.getCurrentUser()?.let {
                                bundle.putString("avatar", it.avatar)
                                bundle.putString("name", it.name)
                                bundle.putString("userId", it.userId)
//                                    personProvider?.openPage("user_qr", bundle)
                                personProvider?.openPageWithResult(this, "user_qr", bundle) {}
                            }
                        }
                        2 -> {
                            personProvider?.openPageWithResult(this, "user_setting", bundle) {}
//                                personProvider?.openPage("user_setting", bundle)
                        }
                        else -> {
                            personProvider?.openPageWithResult(this, "app_about", bundle) {}
//                                personProvider?.openPage("app_about", bundle)
                        }
                    }
                    closeDrawerContent()
                })

            val userEntList =  binding.activityHomeDrawer.userDrawerLayout.clUserLayout.findViewById<RecyclerView>(R.id.rv_list)
            userEntList.layoutManager = LinearLayoutManager(this)
            userEntList.adapter = userInfoAdapter

            val contact = binding.activityHomeDrawer.userDrawerLayout.clUserLayout.findViewById<TextView>(R.id.tv_user_bottom_title)
//            val telephone = MMKVUtil.getString(MMKVKEY.TEL_NUM)
            if (!telephone.isNullOrBlank()) {
                contact.visibility = View.VISIBLE
                contact.text = Spanny().append("客服热线 ").append(
                    telephone,
                    ForegroundColorSpan(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.main_blue))
                )
                contact.setOnClickListener {
                    val intent = Intent(Intent.ACTION_DIAL)
                    val data = Uri.parse("tel:$telephone")
                    intent.data = data
                    startActivity(intent)
                }
            } else {
                contact.visibility = View.GONE
            }

            updateUserInfo()
        }

        fun initOrgDrawer() {
            binding.activityHomeDrawer.orgDrawerLayout.recyclerWork.layoutManager = LinearLayoutManager(this)
            workAdapter = CompanyListAdapter(mContext!!, workList)
            workAdapter.setWorkItemListener(this)
            binding.activityHomeDrawer.orgDrawerLayout.recyclerWork.adapter = workAdapter
        }

        initOrgDrawer()
        initUserDrawer()

        binding.activityHomeDrawer.userDrawerLayout.clUserLayout.visibility = View.GONE
        binding.activityHomeDrawer.orgDrawerLayout.clOrgLayout.visibility = View.VISIBLE

        binding.drawerHome.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED)
        binding.drawerHome.addDrawerListener(this)
    }

    //初始化标签
    private var firstTab: TextView? = null
    private var addressTab: View? = null

    private fun initTab() {
        for (i in tabsTexts.indices) {
            val view = View.inflate(this, R.layout.item_tab_home, null)
            val tvTab = view.findViewById(R.id.tv_item_home) as TextView
            val imgTab = view.findViewById(R.id.iv_item_home) as ImageView
            if (i == 0) {
                firstTab = view.findViewById(R.id.point_home)
            } else if (i == 4) {
                addressTab = view.findViewById(R.id.point_home2)
            }
            tvTab.text = tabsTexts[i]
            imgTab.setImageResource(imgTabId[i])
            binding.activityHomeContent.tablayoutHome.getTabAt(i)!!.customView = view
        }

        //双击消息图标
        /* val msgView = binding.activityHomeContent.tablayoutHome.getTabAt(0)?.customView
         if (msgView != null) {
             msgView.setOnLongClickListener(object : View.OnLongClickListener {
                 override fun onLongClick(v: View?): Boolean {
                     ToastUtil.show(this@HomeActivity, "长按了消息")
                     return true
                 }
             })
         }*/

        val layoutParams = binding.activityHomeContent.msgClick.layoutParams as RelativeLayout.LayoutParams
        layoutParams.width = ScreenUtils.widthPixels(this) / 5
        binding.activityHomeContent.msgClick.requestLayout()
        binding.activityHomeContent.msgClick.setOnClickListener(object : View.OnClickListener {
            override fun onClick(v: View?) {
                Logger.i("---执行---", "-----点击了左下角---发送滚动通知一次--")
                EventBusUtils.sendEvent(
                    EventBusEvent(
                        ConstantEventCodeUtil.TCP_SESSION_MOVE_TO,
                    )
                )
            }
        })

    }

    /**App启动接口是否调用正常*/
    private var appStartState = false

    private fun registerNetReceiver() {
        receiver?.let {
            val filter = IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION)
            it.setNetConnectedListener { netState, netType ->
                Logger.i("网络状态监听", "===监听执行了=netState=${netState}====netType=${netType}=")
                if (currentNetType != netType) {
                    currentNetType = netType
                    RequestCache.changeNetType(currentNetType)
                }
                if (netState > 0 && currentNetState <= 0) {
                    // 无网络变成有网络
                    currentNetState = netState
                    Logger.i("网络状态监听", "===网络连接上了==currentNetState=${currentNetState}=")
                    //有网络，并且之前是无网络
                    onNetEnable(currentNetState > 0)//切换到有网络
                }
                if (netState == 0 && currentNetState != 0) {
                    // 有网络或未初始化变成无网络
                    currentNetState = netState
                    //无网络
                    Logger.i("网络状态监听", "===网络断开了==currentNetState=${currentNetState}=")
                    onNetEnable(currentNetState > 0)//切换到无网络
                }
                if (currentNetState == -1) {
                    currentNetState = 0
                    Logger.i("网络状态监听", "===默认无网络==currentNetState=${currentNetState}=")
                    onNetEnable(currentNetState > 0)//默认无网络
                }
            }
            registerReceiver(it, filter)
        }
    }

    private fun onNetEnable(isEnable: Boolean = false) {
        if (isEnable) {
            //网络连接成功后重新刷新数据
            Logger.i("网络状态监听", "==网络连接后执行的一系列操作=====")

            val pushToken = UserHolder.getCurrentPushToken()
            if (!appStartState) {
                showLog("网络状态变化，网络可用时，需要提交start/v2")
                onStartApp(pushToken)
            }
            Logger.i("---执行-验证离线会话-im长链接登录--", "----网络连接成功时---")
            firstLoadPage()// 网络恢复后获取团队数据
            updateStatusBar()
        } else {
            imLoginSuccess = false //网络断开后需要重新连接im
            Logger.i("网络状态监听", "==网络断开后执行的一系列操作=====")
            getAllCompany(false)// 初始化首页信息
            updateStatusBar()
        }
        notifyNetState(isEnable)
    }

    private fun setHomeLeftDrawIsClick(isClick: Boolean) {
        showLog("设置首页侧滑页 可展开可点击")
        EventBusUtils.sendEvent(
            EventBusEvent(
                EventBusAction.Event_REFRESH_HOME_LEFT_DRAW_IS_CLICK, isClick
            )
        )
    }

    private fun getHomeData() {
        showLog("当前网络可用，发送统计数据，检查版本更新，获取首页数据")
        showLoading()
        getAllCompany(false)// 初始化首页信息
        getFriendsList()//IM单聊消息信息使用
        getGroupInfoList()

        //获取自己的sessionID
        userId?.let {
            ImNetUtil.getImSessionId(bindToLifecycle(), it, onSuccess = { sessionId ->
                Logger.i(
                    "---验证消息发送---当前用户---",
                    "--自己的--userId===${userId}---自己的-sessionId===${sessionId}"
                )
            })
        }

        registerPush()
    }

    private fun registerPush() {
        //旧push,位置2
       /* try {
            PushCenter.registerPush(application)
        } catch (e: Exception) {
            LogUtil.addDebugTags("push__")
            showLog("推送sdk初始化错误 ${e.message}", "push__")
            EventBusUtils.sendEvent(EventBusEvent(PushEvent.PUSH_TOKEN_UPDATE, ""))
        }*/
    }

    private fun notifyNetState(enable: Boolean) {
        showLog("刷新网络状态 $enable")
        if (enable) {
            EventBusUtils.sendEvent(
                EventBusEvent(
                    IMEvent.CONNECT_SUCCESS,
                    currentNetState.toString()
                )
            )// 有网络
        } else {
            EventBusUtils.sendEvent(
                EventBusEvent(
                    IMEvent.CONNECT_ERROR,
                    currentNetState.toString()
                )
            )// 无网络
        }
    }

    override fun initLogic() {
        super.initLogic()
        AddressProvider.getUnProcessCount().observe(this, Observer { count ->
            showLog("需要展示红点了")
            if (addressTab == null) {
                addressTab = binding.activityHomeContent.tablayoutHome.getTabAt(4)?.customView?.findViewById(R.id.point_home2)
            }
            addressTab?.let {
                if (count > 0) {
//                    it.text = count.toString()
                    it.visibility = View.VISIBLE
                } else {
                    it.visibility = View.GONE
                }
            }
        })
        onOffPushMsg(intent)//通知事件到页面时判断 intentUri打开指定页面携带参数 和 小米打开首页携带参数打开主页面

        GlobalCompanyHolder.companyUpdateResult.observe(this, Observer {
            showLog("首页 -- 接收团队信息更新")
            orgState = it.type
            when (it.type) {
                1 -> {// 显示我的团队信息

                }
                2 -> {// 显示我的协作团队信息

                }
                else -> {// 当前无任何团队信息
                    showEmpty()
                }
            }
            updateStatusBar()
        })
        GlobalCompanyHolder.currentCompany.observe(this, Observer {
            showLog("首页 -- 当前团队更新")
            if (it != null) {
                showOrg(it, 1)// 初始化缓存数据
            }
        })

    }

    /**
     * 获取im连接token
     * 获取到imToken后才能登录和获取离线数据
     * */
    private fun getImTokenBean() {

    }

    /**获取离线数据*/
    private fun getOfflineData() {
//        if (BaseApplication.needGetOffLineSessionList) {
            getOffLineSessionListFromNet()
//        }
//        if (BaseApplication.needGetOffLineNoticeSessionList) {
            getOffLineNoticeSessionListFromNet()
//        }
    }

    /**新功能提示窗 不再提示*/
    private fun showTip(page: Int = -1) {
        if ((page == 0 || page == 3) && !MMKVUtil.containKey("cloud_doc_tip_$page")) {
//        if ((page == 0 || page == 3) && !MMKVUtil.containKey("cloud_doc_tip")) {
            val helper = object : DialogHolder(
                this, layoutId = com.joinutech.ddbes.clouddoc.R.layout.dialog_cloud_doc_tip_layout,
                gravity = Gravity.FILL, tag = "cloudDoc"
            ) {
                @SuppressLint("SetTextI18n")
                override fun bindView(dialogView: View) {
                    val topView = dialogView.findViewById<View>(com.joinutech.ddbes.clouddoc.R.id.iv_cd_top)
                    val params = ConstraintLayout.LayoutParams(topView.layoutParams)
                    params.setMargins(0, statusBarHeight, 0, 0)
                    topView.layoutParams = params
                    topView.setOnClickListener {
                        dialog?.dismiss()
                        MMKVUtil.saveBoolean("cloud_doc_tip_$page", true)
                    }
                    val bottom = dialogView.findViewById<View>(com.joinutech.ddbes.clouddoc.R.id.iv_cd_bottom)
                    bottom.setOnClickListener {
                        dialog?.dismiss()
                        MMKVUtil.saveBoolean("cloud_doc_tip_$page", true)
                    }
                    if (page == 3) {
                        topView.visibility = View.GONE
                        bottom.visibility = View.VISIBLE
                    } else if (page == 0) {
                        bottom.visibility = View.GONE
                        topView.visibility = View.VISIBLE
                    }
                }
            }
            helper.show(style = com.joinutech.ddbeslibrary.R.style.dialogFullScreen, matchHeight = true)
        }
    }

    /**
     * 音视频会议邀请事件
     * 小米推送通过打开首页通知携带参数而来
     * */
    private fun dealPushEvent() {
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("isPushEnter"))) {
                val meetingId = intent.getStringExtra("isPushEnter")!!
//                val handlerIcon = intent.getStringExtra("icon")
//                val handlerName = intent.getStringExtra("handlerName")
//                val companyId = intent.getStringExtra(ConsKeys.COMPANY_ID)
//                val companyName = intent.getStringExtra("companyName")
                showLog("收到 跳转页面参数 meetingId = $meetingId ")
                jumpToInvitePage(meetingId)
            }
        }
    }

    /**跳转通话邀请页面*/
    private fun jumpToInvitePage(meetingId: String) {
        if (!meetingId.isNullOrBlank()) {
            ARouter.getInstance()
                .build(RouteVc.VC_INVITE_PAGE)
                .withString("vcId", meetingId)
                .navigation()
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        onOffPushMsg(intent)// 多个相同通知打开时使用，华为返回
    }

    /**
     * 离线推送厂商消息处理
     * 华为
     * */
    private fun onOffPushMsg(intent: Intent?) {
        if (intent != null) {
            intent.data?.let {
                val data = it.getQueryParameter("data")
                showLog("获取到推送uri参数：$data")
                dealOffPushData(data)//华为
            }

            intent.extras?.let { bundle ->
                for (key in bundle.keySet()) {
                    val content = bundle.getString(key)
                    showLog("获取到推送携带参数：, key = $key, content = $content")
                }
            }
        }
    }

    private var tag: Int = 0
    private fun firstLoadPage() {
        showLog("获取首页数据，执行im登录")
        tag++
        getHomeData()//启动页面时刷新所有团队数据
    }

    var lastTime = 0L
    private fun onStartApp(token: String) {
        if (System.currentTimeMillis() - lastTime < 2000) {
            return
        }
        lastTime = System.currentTimeMillis()
        //旧push,位置3
       /* val sys = PushCenter.getSystemType()
        if (sys != PushCenter.NO_SUPPORT && sys != PushCenter.HUAWEI && sys != PushCenter.OPPO) {
            PushCenter.setAlias(this, userId ?: "")
        }*/
        if (MMKVUtil.getInt(MMKVKEY.APP_Statistics, 0) != 0) {
            Logger.i("推送", "===推送的token=${token}==开始上传=")
            //说明是app打开次数的第一次记录，记录后恢复为不记录，防止多次登录重复记录
//            val clientCode = DeviceUtil.getVersionCode(applicationContext)

            //tcp_versionCode,这个clientCode是发版时在config.gradle中手动加1的versionCode
//            val clientCode = 106 //发版修改位置5
            val clientCode = VersionConfig().versionCode2  //发版修改位置6，config.gradle中的versionCode,每次发版手动加一
            Logger.i("版本信息", "===clientCode=${clientCode}")
            PersonService.userStartApp(token, currentNetType, clientCode)//APP启动后检查更新，六合一接口调用
                .compose(bindToLifecycle())
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<AppStartResult>() {
                    override fun onError(ex: ApiException) {
                        showLog("首页 启动接口 调用失败 [${ex.code}-${ex.message}]")
                    }

                    override fun onComplete() {}

                    override fun onNext(result: AppStartResult?) {
                        showLog("首页 启动接口 调用成功")
                        if (result != null) {
//                                appStartState = true
                            showLog("statisticsHomeData app次数统计成功")
                            MMKVUtil.saveInt(MMKVKEY.APP_Statistics, 0)
                            Loggerr.i("验证线程","===开始处理新版本的当前线程=${Thread.currentThread().name}==")
                            dealVersionSuccess(
                                AppVersionBean(
//                                        downUrl = "https://cdn.ddbes.com/Android/app-release-20210304-3.0.3.apk",
//                                        status = 3,
//                                        auditing = 1,
//                                        versionName = "3.0.3",
                                    downUrl = result.downUrl,
                                    status = result.status,
                                    auditing = result.auditing,
                                    versionName = result.versionName,
                                    currentDesc = result.currentDesc,
                                    desc = result.desc
                                )
                            )
//                                appStartResult = result
                            // 同步banner信息
                            //更新banner大图
                            EventBusUtils.sendEvent(
                                EventBusEvent(
                                    EVENT_WORK_BANNER_REFRESH,
                                    result.bannerList
                                )
                            )
                            // // CHANGE_HISTORY: 2021/2/2 10:52 群消息同步方式发生变更，原有同步不再使用
//                                saveGroupOffData(result.group)
                            // TODO: 2020/8/25 16:33 2.4.2 优化通知免打扰设置，系统，团队，审批
                            val pushSetting = CompanyHolder.getPushSetting()
                            pushSetting.ids = result.ids// 团队，在这个列表中时为开启了免打扰
                            pushSetting.systemPushSwitch = result.systemPushSwitch// 系统消息免打扰
                            pushSetting.approvalPushSwitch = result.approvalPushSwitch// 审批消息免打扰
                            pushSetting.ticketPushSwitch = result.ticketPushSwitch// 工单消息免打扰
                            pushSetting.inventorySwitch = result.inventorySwitch// 库存消息免打扰
                            CompanyHolder.savePushSetting(pushSetting)
                            // TODO: 2021/1/13 17:44 2.4.6 之后版本增加，用于动态获取云存储区域和AppId
                            if (result.region.isNullOrBlank()) {
                                showLog("动态云存储信息失败")
                            } else {
                                FileStorage.COS_REGION = result.region ?: ""
                                FileStorage.COS_APPID = result.appId ?: ""
                            }
                            // TODO: 2021/4/13 客服热线通过接口回调
                            telephone = result.tel ?: ""
//                                MMKVUtil.saveString(MMKVKEY.TEL_NUM, result.tel ?: "")
                            if (result.pre.isNullOrBlank()) {
                                showLog("动态云存储前缀信息失败")
                            } else {
                                FileStorage.TOS_DEFAULT_URL_PRE = result.pre ?: ""
                            }
                            //缓存点击超链接时的一些数据
                            UserHolder.saveLinkWarn(result.linkWarn)
                            UserHolder.saveLinkWhiteList(result.linkWhiteList)
                        }
                    }
                })
        }
    }

    private fun dealVersionSuccess(it: AppVersionBean) {
        MMKVUtil.saveString("appNewVersion", GsonUtil.toJson(it))
        Loggerr.i("软件更新", "====${GsonUtil.toJson(it)}")
        //先判断有没有当前版本的版本描述，因为不变，所以只需存储一次即可
        if (StringUtils.isNotBlankAndEmpty(it.currentDesc)) {
//            val versionName = MMKVUtil.getString(MMKVKEY.VERSION_NAME)
            val versionName = VersionConfig().versionName
            val versionContent = MMKVUtil.getString("appVersionNum$versionName")
            if (StringUtils.isEmpty(versionContent) || versionContent != it.currentDesc) {
                MMKVUtil.saveString("appVersionNum$versionName", it.currentDesc)
            }
        }
        AppVersionCheckUpdateUtil.updateNewVersionDialog(this@HomeActivity, it)
    }

    override fun onPageScrollStateChanged(p0: Int) {

    }

    //滚动了viewpage
    override fun onPageScrolled(p0: Int, p1: Float, p2: Int) {

    }

    //切换了viewpage
    override fun onPageSelected(position: Int) {
        binding.drawerHome.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED)
        if (position <= 4) {
            if (workList.isNullOrEmpty() || orgState == 0) {
                getLoadingDialog("正在加载", true)
                /*切换fragment到项目时，如果当前团队列表为空时刷线团队列表*/
                getAllCompany(false)//首页加载失败且团队信息为空时，刷新所有团队列表
            }
            if (position == 0) {
                binding.activityHomeContent.msgClick.visibility = View.VISIBLE
            } else {
                binding.activityHomeContent.msgClick.visibility = View.GONE
            }
        }
        /* if (position != 2) {
             binding.activityHomeContent.tablayoutHome.getTabAt(2)!!.customView?.visibility = View.VISIBLE
             binding.activityHomeContent.tabWork.visibility = View.GONE
             if (position == 0) {
                 binding.activityHomeContent.msgClick.visibility = View.VISIBLE
             } else {
                 binding.activityHomeContent.msgClick.visibility = View.GONE
             }
         } else {
             binding.activityHomeContent.tablayoutHome.getTabAt(2)!!.customView?.visibility = View.INVISIBLE
             binding.activityHomeContent.tabWork.visibility = View.VISIBLE
         }*/
//        showTip(position)
        updateStatusBar()
    }

    override fun onResume() {
        super.onResume()
//        LogUtil.showLog("app_start :: 主页面 已可见,耗时${System.currentTimeMillis() - BaseApplication.start}毫秒")
        registerNetReceiver()// 恢复页面时重新初始化
    }

    //fragment中设置的监听
    override fun onResumed(index: Int) {
        when (index) {
            0 -> {
                showLog("++++++ home activity 被动触发刷新 message fragment")
//                notifyNetState()
            }
            1 -> {
                showLog("++++++ home activity 被动触发刷新 program fragment")
//                notifyNetState()
            }
            2 -> {
                showLog("++++++ home activity 被动触发刷新 work fragment")
//                notifyNetState(index)
//                appStartResult?.let {
//                    // 同步工作页banner信息
//                    EventBusUtils.sendEvent(EventBusEvent(EVENT_WORK_BANNER_REFRESH, it.bannerList))
////                    // 同步群组离线消息
////                    saveGroupOffData(it.group)
//                }
            }
            3 -> {
                showLog("++++++ home activity 被动触发刷新 document fragment")
//                notifyNetState()
            }
            4 -> {
                showLog("++++++ home activity 被动触发刷新 addressbook fragment")
//                EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ADDRESS_UNDO_DOT, ""))
            }
        }
    }

    //刷新审批未读数----在侧滑菜单点击某个公司，即切换公司时候触发
    @SuppressLint("RtlHardcoded")
    override fun onWorkItemClick(position: Int) {
        if (!workList.isNullOrEmpty()) {
            GlobalCompanyHolder.setCurrentCompany(workList[position])
//            CompanyHolder.saveCurrentOrg(workList[position])
//            GlobalCompanyHolder.currentCompany.value = workList[position]
//            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.CURRENT_COMPANY_UPDATE, 1))
        }
        closeDrawerContent()
    }

    /**获取好友列表 version检查*/
    private fun getFriendsList(state: String = "") {
        Logger.i("---执行---获取好友列表", "--开始---")
        ImNetUtil.getFriendVersion { version ->
            if (version.isNullOrBlank()) {
                FriendCacheHolder.saveFriendDataVersion("")
                Logger.i("---执行---获取好友列表", "--版本为空---重新获取---")
                ImNetUtil.getAppFriendList(this, bindToLifecycle(), bindToLifecycle(),
                    onSuccess = {
                        //发送好友列表刷新完成的通知
                        EventBusUtils.sendEvent(
                            EventBusEvent(
                                ConstantEventCodeUtil.TCP_REFRESH_FRIEND_LIST_FINISH,
                                ""
                            )
                        )
                    },
                    onFailer = {
                        Logger.i("---执行--获取好友列表失败--", "---" + it)
                    })
            } else {
                // 对比当前好友版本数据，如果版本不相同，则更新好友信息
                if (FriendCacheHolder.checkFriendVersion(version)) {
                    Logger.i("---执行---获取好友列表", "--版本不相同---重新获取---")
                    ImNetUtil.getAppFriendList(this, bindToLifecycle(), bindToLifecycle(),
                        onSuccess = {
                            FriendCacheHolder.saveFriendDataVersion(version)
                            //发送好友列表刷新完成的通知
                            EventBusUtils.sendEvent(
                                EventBusEvent(
                                    ConstantEventCodeUtil.TCP_REFRESH_FRIEND_LIST_FINISH,
                                    ""
                                )
                            )
                        },
                        onFailer = { errorString ->
                            Logger.i("---执行--获取好友列表失败--", "---" + errorString)
                        })
                } else {
                    Logger.i("---执行---获取好友列表", "--版本相同---不重新获取---")
                }
            }
        }
    }

    /**获取群组信息列表*/
    private fun getGroupInfoList() {
        Logger.i("---执行---获取群组列表", "--开始---")
        ImNetUtil.getAppGroupList(bindToLifecycle(), bindToLifecycle(), {
            Logger.i("---执行---获取群组数量", "--size---" + it.size)
        }, {
            Logger.i("---执行--获取群组列表失败--", "---" + it)
        })
    }

    /**处理离线推送数据*/
    private fun dealOffPushData(info: String?) {
        if (!info.isNullOrBlank() && info.startsWith("{")) {
            val json = JSONObject(info)
            val meetingId = json.optString("meetingId")
//            val handlerIcon = json.optString("icon")
//            val handlerName = json.optString("handlerName")
//            val createUserId = json.optString("userId")
//            val companyId = json.optString(ConsKeys.COMPANY_ID)
//            val companyName = json.optString("companyName")
            showLog("收到 离线推送数据 meetingId = $meetingId ")
            if (!meetingId.isNullOrBlank()) {
                jumpToInvitePage(meetingId)
            }
        }
    }

    private fun updateStatusBar() {
        if (binding.activityHomeContent.vpHome.currentItem == 2) {
            //修改工作页状态栏tcp
//            immersionBar?.statusBarDarkFont(CompanyHolder.getAllCompanies().isEmpty())?.init()
            immersionBar?.statusBarDarkFont(true)?.init()
        } else {
            immersionBar?.statusBarDarkFont(true)?.init()
        }
    }

    /**当前客服号码*/
    private var telephone: String? = null

    /**展开侧滑页面*/
    private fun showDrawerContent() {
        val contact = binding.activityHomeDrawer.userDrawerLayout.clUserLayout.findViewById<TextView>(R.id.tv_user_bottom_title)
        if (!telephone.isNullOrBlank()) {
            contact.visibility = View.VISIBLE
            contact.text = Spanny().append("客服热线 ").append(
                telephone,
                ForegroundColorSpan(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.main_blue))
            )
            contact.setOnClickListener {
                val intent = Intent(Intent.ACTION_DIAL)
                val data = Uri.parse("tel:$telephone")
                intent.data = data
                startActivity(intent)
            }
        } else {
            contact.visibility = View.GONE
        }

        binding.drawerHome.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNLOCKED)
        binding.drawerHome.openDrawer(Gravity.LEFT)
    }

    /**关闭侧滑页面*/
    private fun closeDrawerContent() {
        binding.drawerHome.closeDrawer(Gravity.LEFT) // 关闭侧滑页面
//        binding.drawerHome.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED)
        EventBusUtils.sendEvent(EventBusEvent("on_drawer_close", ""))
    }

    /**----------------------------------事件处理----------------------------------*/
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun receiveStringEvent(event: EventBusEvent<String>) {
//        showLog("收到String消息事件 ${event.data.toString()}")
        when (event.code) {
            ConsKeys.USER_INFO_UPDATE -> {
                if (event.data != null && event.data == UserHolder.getUserId()) {
//                    showLog("收到用户信息更新事件，更新侧滑页面")
                    updateUserInfo()
                }
            }
            PushEvent.OFF_PUSH_RECEIVE -> {
                if (!event.data.isNullOrBlank()) {
                    if (event.data!!.startsWith("ddbes://com.joinutech.ddbes/master?data=")) {
                        val data =
                            event.data!!.replace("ddbes://com.joinutech.ddbes/master?data=", "")
                        showLog("收到小米推送通知（App打开后点击通知栏数据接收） \n: $data")
                        dealOffPushData(data)
                    }
                }
            }
            PushEvent.PUSH_TOKEN_UPDATE -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    //建立hmsPush专属的消息channel通道
                    val notificationManager =
                        mContext?.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                    val channel =
                        NotificationChannel("hmsPush", "担当推送", NotificationManager.IMPORTANCE_HIGH)
                    notificationManager.createNotificationChannel(channel)
                }
                UserHolder.setCurrentPushToken(event.data!!)
                Loggerr.i("推送", "===推送token更新currentNetState=${currentNetState}==")
                if (currentNetState > 0) {
                    Loggerr.i("推送", "===推送token更新，需要重新提交start/v2===")
                    onStartApp(event.data!!)
                }
            }
            IMEvent.LOGIN_SUCCESS -> {
                showLog("im login success1")
//                retryImLoginNum = 0
            }
            IMEvent.LOGIN_FAILED -> {
                imLoginSuccess = false
//                updateImState()
                /**im连接失败后尝试重新连接*/
//                retryImLoginNum--
//                if (retryImLoginNum > 0) {
//                    showLog("发起重新连接 ${3 - retryImLoginNum}")
//                    if (userId.isNullOrBlank() || accessToken.isNullOrBlank()) {
//                        return
//                    }
//                    imLogin()
//                } else {
//                    showLog("收到im登录失败，不再重试")
//                }
            }
            EventBusAction.Event_GET_HOME_DATA -> {
                //得到首页数据,首页数据为空时刷新首页面（工作和项目）
                showLog("工作首页为空时，点击重新获取所有公司信息")
                Logger.i("---执行-验证离线会话-im长链接登录--", "----首页数据为空时---")
                firstLoadPage()//工作首页触发 获取首页数据
            }
            EventBusAction.REFRESH_GROUP_LIST -> {//群组信息变动
                getGroupInfoList()
            }
            // 好友信息刷新
            IMEvent.CHANGE_FRIEND -> {
                val state = event.data ?: ""
                getFriendsList(state)// 好友信息更新触发好友数据刷新
            }

            EventBusAction.REFRESH_ORGANLIST -> {
                /*事件刷新团队列表*/
                showLog("事件刷新团队列表")
                Logger.i("----执行--验证组织--", "------收到通知开始刷新组织----")
                getAllCompany(true)// 其它页面刷新团队列表
            }
            EventBusAction.Event_REFRESH_COOPERATION_APPLICATION -> {
                showLog("外部协作公司邀请，同意后刷新团队列表")
                val allCom = arrayListOf<WorkStationBean>()
                allCom.addAll(CompanyHolder.getTotalCompanies())
                if (allCom.isNotEmpty()) {
                    val target = allCom.find { it.companyId == event.data }
                    if (target == null) {
                        getAllCompany(false)
                    }
                } else {
                    getAllCompany(false)
                }
            }
            EventBusAction.Event_DISSOLVED_ORG, EventBusAction.Event_LEAVE_ORG -> {
                showLog("离开或者请离事件刷新团队列表")
                // 请离或者离开团队时，刷新团队列表
                getAllCompany(false) {
                    hideLoading()
//                    val bundle = Bundle()
//                    bundle.putString("userId", userId!!)
//                    bundle.putString("data", event.data)
//                    chatService?.service("org_delete", bundle) {
//                    }
                }
            }
            EventBusAction.HOME_CREATE_GROUP -> {//home页收到通知---创建群组
                createGroup()
            }
            EventBusAction.HOME_SCAN -> {
                doScan()
            }
//            IMEvent.BROADCAST_MSG -> {
//                val bundle = Bundle()
//                bundle.putString("userId", userId!!)
//                bundle.putString("data", event.data)
////                chatService?.service(event.code, bundle) {}
//            }
            else -> {

            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReceiveSignatureResult(event:EventBusEvent<Bitmap>) {
        if (event.data != null&&event.data is Bitmap) {
            when (event.code) {
                "scan_signature_result"->{
                    val targetBitmap=event.data
                    val targetFile=DealFileUtil.saveBitmap(this,targetBitmap,
                        "${System.currentTimeMillis()}.png", folderName = "signature")
                    val bundle=Bundle()
                    bundle.putSerializable("signature",targetFile)
                    bundle.putString("companyId",CompanyHolder.getCurrentOrg()?.companyId?:"")
                    showLoading()
                    (ARouter.getInstance().build(RouteProvider.APR_FUN_PROVIDER)
                        .navigation() as RouteServiceProvider)
                        .openPageWithResult(this,"upload_signature", bundle, result = {
                            if (it != "0"&&StringUtils.isNotBlankAndEmpty(it)) {
                                //扫码后上传手写签名成功
                                val uploadFileBean=GsonUtil.fromJson(it,UploadFileBean::class.java)
                                val key=UserHolder.getScanKey()
                                val map= linkedMapOf<String,Any>()
                                val map1= linkedMapOf<String,Any>()
                                map1.put("fileId",uploadFileBean?.fileId?:"")
                                map1.put("hash",uploadFileBean?.hash?:"")
                                map1.put("fileName",uploadFileBean?.fileName?:"")
                                map.put("key",key)
                                map.put("signatureFile",map1)
                                val resultJson=GsonUtil.toJson(map)
                                UploadFileUtil.sendSignatureId(resultJson, onSuccess = {
                                    ToastUtil.show(BaseApplication.joinuTechContext,it)
                                                hideLoading()
                                },
                                    onFailer = {
                                        ToastUtil.show(BaseApplication.joinuTechContext,it)
                                        hideLoading()
                                    })
                            }else{
                                hideLoading()
                            }
                        })
                }
            }
        }
    }


    /**登录长链接方法*/
    private fun loginLongConnect() {
        if (!imToken.isBlank()) {
            val bundle = Bundle()
            bundle.putString("imLoginToken", imToken)
            (ARouter.getInstance().build(RouteProvider.IM_PROVIDER)
                .navigation() as RouteServiceProvider)
                .openPage("imLogin", bundle)
//            (application as BaseApplication).imService?.openPage("imLogin", bundle)
        }
    }

    private fun showSessionCount(num: Int?) {
        if (firstTab != null) {
            if (num != null && num > 0) {
                firstTab?.visibility = View.VISIBLE
                if (num > 99) {
                    firstTab?.text = "99+"
                } else {
                    firstTab?.text = num.toString()
                }
            } else {
                firstTab?.text = "0"
                firstTab?.visibility = View.GONE
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun refreshData(event: EventBusEvent<Int>) {
        when (event.code) {
            EventBusAction.Event_CLICK_HOME_WORK_LEFT -> {
                if (binding.activityHomeContent.vpHome.currentItem == 0) {
                    updateDrawerData()
                    showDrawerContent()
                } else {
                    if (!workList.isNullOrEmpty()) {
                        updateDrawerData()
                        showDrawerContent()
                    } else {
                        setHomeLeftDrawIsClick(false)
                        /*显示团队列表，切换团队时，如果团队列表不存在，需要更新团队列表数据*/
                        getAllCompany(true)// 展开团队切换侧滑页面时，如果所有团队列表为空时 刷新所有团队列表
                    }
                }
            }
            "home_show_bottom_bar" -> {
                if (event.data == 0) {
                    binding.activityHomeContent.tablayoutHome.visibility = View.VISIBLE
                    binding.activityHomeContent.vpHome.noScroll = false
                } else {
                    binding.activityHomeContent.tablayoutHome.visibility = View.GONE
                    binding.activityHomeContent.vpHome.noScroll = true
                }
            }
            EventBusAction.HOME_POINT -> {
                showLog("未读消息红点信息 ${event.data}")
                val num = if (event.data != null) {
                    event.data!!
                } else {
                    0
                }
                showSessionCount(num)
            }
            EventBusAction.refresh_all_appro_undo -> {// 刷新未处理审批数量
                updateAprWaitData(1)//主动刷新审批未处理数量
            }


            EventBusAction.REFRESH_COM_APPLY -> {// 刷新通讯录待处理提示
                AddressProvider.changeUnProcessCount(1)
                EventBusUtils.sendEvent(
                    EventBusEvent(
                        EventBusAction.REFRESH_APPLIY_LIST, "org"
                    )
                )
            }
        }
    }

    //接收刷新入口未读数的指令（多数是为了刷新金蝶审批的未处理数）
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun receiveKingdeeUpdate(event: EventBusEvent<String>) {
        when (event.code) {
            EventBusAction.refresh_entroy_unread_count -> {// 刷新首页入口未读数
                val updateCompanyId=event.data as String
                if (!updateCompanyId.isNullOrBlank()) {
                    getEntroyListUnRead(updateCompanyId)
                }
            }
        }
    }
    //tcp接收event通知
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun receiveTcpImNotice(event: EventBusEvent<String>) {
        when (event.code) {
            ConstantEventCodeUtil.TCP_REFRESH_FRIEND_ON_DELETE, // 删除好友后触发好友列表刷新
            ConstantEventCodeUtil.TCP_REFRESH_FRIEND_LIST -> { //刷新好友列表
                getFriendsList()//im通知刷新好友列表
            }
            ConstantEventCodeUtil.TCP_REFRESH_GROUP_LIST -> {//刷新群组列表
                getGroupInfoList()
            }
            ConstantEventCodeUtil.TCP_RECEIVE_KICK_TO_LOGIN_OUT -> {//多端登录被顶替退出
                showLoginOutDialog()
            }
            ConstantEventCodeUtil.TCP_LOGIN_LONGCONNECT_SUCCESS -> {//长链接断线重连后刷新会话列表，防止断线重连后丢失消息
                showLog("事件 = ConstantEventCodeUtil.TCP_LOGIN_LONGCONNECT_SUCCESS")
//                if (BaseApplication.reConnectedToGetOffLineSessionList == true) {
                    getOfflineData()
//                }
            }

        }

    }

    private fun showLoginOutDialog() {//对话框使用示例
        val view = View.inflate(this, com.joinutech.message.R.layout.dialog_tcp_login_out, null)
        val dialog = BottomDialogUtil.showBottomDialog(this, view, Gravity.CENTER)
        view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.confirm).setOnClickListener {
            dialog.dismiss()

            BaseApplication.needGetImToken = true
            onUserLogout()
            UserHolder.clearImTokenBean()//清空imToken,
            imToken = ""
        }
        //设置点击返回键不消失
        dialog.setCancelable(false)
        //设置点击屏幕不消失
        dialog.setCanceledOnTouchOutside(false)
        dialog.show()
        (ARouter.getInstance().build(RouteProvider.IM_PROVIDER)
            .navigation() as RouteServiceProvider)
            .openPage("imLogout", Bundle())
//        (application as BaseApplication).imService?.openPage("imLogout", Bundle())//弹框只要一弹出来，就先断掉长链接，防止新登录的设备也被顶下去
    }

    /**----------------------------------事件处理----------------------------------*/

    private fun showEmpty() {

    }

    private fun showCooperation() {
        CompanyHolder.getCooperationOrg()?.let {
            //            cooperationCompanyList.clear()
//            cooperationCompanyList.addAll(it)
        }
    }

    /**设置所有团队中，当前团队状态为选中，更新所有团队列表;刷新团队审批未处理数量*/
    private fun showOrg(currentWorkStation: WorkStationBean, type: Int = 0) {
        showLog("切换团队信息 type = $type, ${GsonUtil.toJson(currentWorkStation)}")
        CompanyHolder.getAllNormalOrg().let { allOrg ->
            if (workList.isNotEmpty()) {
                workList.clear()
            }
            if (allOrg.isNotEmpty()) {
                val result = allOrg.map { org ->
                    org.isSelected = (org.companyId == currentWorkStation.companyId)
                    org
                }.toList()
                workList.addAll(result)
            }
            //在侧滑菜单点击公司切换时，和首页getAllCompany执行时都会触发下面的刷新审批未读数和拉取整合接口
            updateAprWaitData()// 被动刷新审批未处理数
            // 拉取整合接口
            checkToRefreshEntroyList(currentWorkStation.companyId)
        }
    }

    //判断是不是要重新拉取入口列表
    private fun checkToRefreshEntroyList(companyId: String) {
        val bundle = Bundle()
        bundle.putString(ConsKeys.COMPANY_ID, companyId)
        Logger.i("整合接口", "===检查时间戳==开始=${System.currentTimeMillis()}=")
        orgService?.service("getGrateDataLastTime",bundle, result = {
            Logger.i("整合接口", "===检查时间戳==结束=${System.currentTimeMillis()}=")
            val lastTime=UserHolder.getGrateDataLastTimeMap().get(companyId)
            Loggerr.i("切换公司测试", "===companyId=${companyId}==接口返回时间戳=${it}==")
            Loggerr.i("切换公司测试", "===companyId=${companyId}==本地上次时间戳=${lastTime}==")
            if (lastTime != it) {
                //需要刷新入口列表
                checkHomeEntroyList(companyId,it)
            }else{
                //不需要刷新入口列表，那就只刷新入口未处理数
                getEntroyListUnRead(companyId)
            }
        })
    }

    //金蝶相关,整合接口tcp,调用位置
    //说明：整合接口的拉取会刷新金蝶审批的未读数，其他的审批未读数是通过另一个接口刷新；
    private fun checkHomeEntroyList(companyId: String,lastTime:String) {
        if (UserHolder.isLogin()) {
            Logger.i("整合接口", "=添加入口调用一次==开始=${System.currentTimeMillis()}==")
            val bundle = Bundle()
            bundle.putString(ConsKeys.COMPANY_ID, companyId)

            //整合接口==第一步：拉取数据，整合数据结构tcp
            //目前是金蝶和交接班使用一个接口，商城使用另一个接口，后期会整合成一个接口
            //访客管理也整合到这个接口
            orgService?.service("getInteGrateData", bundle, result = {
                Loggerr.i("切换公司测试", "=刷新入口===json=${it}==")
                if (StringUtils.isNotBlankAndEmpty(it)) {
                    val totalBean =
                        GsonUtil.fromJson<InteGrateBean>(it, InteGrateBean::class.java)
                    val entroyList = totalBean?.toolsList
                    entroyBeanList.clear()

                    val lastMap=UserHolder.getGrateDataLastTimeMap()
                    lastMap.put(companyId,lastTime)
                    UserHolder.saveGrateDataLastTime(lastMap)

                    if (!entroyList.isNullOrEmpty()) {
                        //整合成添加入口不用发版的动态入口
                        entroyBeanList.addAll(entroyList)
                        //商城相关----------------------------
                        if (isDebug) {
                            getShoppingData(companyId)
                        } else {
                            updateInteGrateData(entroyBeanList)
                        }
                        getEntroyListUnRead(companyId)
                    } else {
                        if (isDebug) {
                            getShoppingData(companyId)
                        } else {
                            updateInteGrateData(entroyBeanList)
                        }
                    }
                }
            })

        } else {
            hideLoading()
        }
    }

    //请求所有应用入口的未读数
    fun getEntroyListUnRead(companyId: String) {
        val bundle = Bundle()
        bundle.putString(ConsKeys.COMPANY_ID, companyId)
        orgService?.service("entroyListUnRead",bundle, result = {
            Loggerr.i("切换公司测试", "=刷新入口未读数===json=${it}==")
            Logger.i("刷新金蝶", "==入口未读数==执行一次==json=${it}==")
            if (StringUtils.isNotBlankAndEmpty(it)) {
                val totalBean =
                    GsonUtil.fromJson<InteGrateBean>(it, InteGrateBean::class.java)
                val unreadList = totalBean?.toolsList

                //将首页金蝶入口的地址存储起来，方便在金蝶审批列表中直接使用（浮动按钮使用）
               val unreadKing= unreadList?.find { it.key=="process" }
                if (unreadKing != null) {
                    //刷新浮动按钮未处理数
                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.refresh_float_count_tag, unreadKing.unreadInt))
                }

                if (companyId==CompanyHolder.getCurrentOrg()?.companyId) {
                    //刷新首页入口红点
                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.send_refresh_entroy_unread_result, unreadList))
                }
            }
        })

    }

    private fun getShoppingData(companyId: String) {
        val bundle = Bundle()
        bundle.putString(ConsKeys.COMPANY_ID, companyId)
        orgService?.service("getShopingData", bundle, result = {
            Logger.i("商城结果", "====json=${it}")
            if (StringUtils.isNotBlankAndEmpty(it)) {
                val totalBean =
                    GsonUtil.fromJson<InteGrateBean>(it, InteGrateBean::class.java)
                val entroyList = totalBean?.toolsList
                if (!entroyList.isNullOrEmpty()) {
                    //商城相关----------------------------
                    entroyBeanList.addAll(entroyList)
                    updateInteGrateData(entroyBeanList)
                } else {
                    updateInteGrateData(entroyBeanList)
                }
            }
        })
    }

    private fun createGroup() {
        //创建群组tcp
        val allFriendList = FriendDaoOpe.instance.getAllFriendList(mContext)
        if (!allFriendList.isNullOrEmpty()) {
            val toList = allFriendList.map {
                if (StringUtils.isNotBlankAndEmpty(it.remark)) {
                    FriendSelectBean(
                        userId = it.userId,
                        name = it.remark,
                        avatar = it.avatar,
                        logout = it.logout
                    )
                } else FriendSelectBean(
                    userId = it.userId,
                    name = it.name,
                    avatar = it.avatar,
                    logout = it.logout
                )
            }.toMutableList() as ArrayList
            ARouter.getInstance().build(RouteOrg.SelectSearchListWithBottomShowActivity)
                .withString("title", "请选择成员")
                .withInt("maxSelectNum", 20)
                .withBoolean("isNeedPersonInfo", true)
                .withSerializable("outPersonList", toList)
                .navigation(this@HomeActivity, IM_VC_SELECT_PERSON)
        } else {
            ToastUtil.show(this, "你还没有好友，无法创建群组")
        }
    }

    private fun doScan() {
        //跳转扫描页面
        val perms = arrayOf(
            Manifest.permission.CAMERA,
            Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE
        )
        val preTips = "需要你同意使用存储权限和相机权限，才能使用二维码扫描功能"
        PermissionUtils.requestPermissionActivity(
            this@HomeActivity, perms, "", {
//                startActivityForResult(Intent(mContext, CaptureActivity::class.java), scan)
            }, {
                ToastUtil.show(mContext!!, "二维码扫描需要相机权限")
            } , preTips = preTips)
    }

    private fun updateUserInfo() {
        val user = UserHolder.getCurrentUser()
        if (user != null) {
            user.let {
                if (!it.avatar.isNullOrBlank()) {
                    ImageLoaderUtils.loadImage(this, binding.activityHomeDrawer.userDrawerLayout.ivUserAvatar, it.avatar)
                }
                binding.activityHomeDrawer.userDrawerLayout.tvUserName.text = it.name
                when (it.gender) {
                    "1" -> {
                        binding.activityHomeDrawer.userDrawerLayout.ivGender.setImageResource(com.ddbes.personal.R.drawable.icon_personinfo_man_small)
                        binding.activityHomeDrawer.userDrawerLayout.ivGender.visibility = View.VISIBLE
                    }
                    "2" -> {
                        binding.activityHomeDrawer.userDrawerLayout.ivGender.setImageResource(com.ddbes.personal.R.drawable.icon_personinfo_girl_small)
                        binding.activityHomeDrawer.userDrawerLayout.ivGender.visibility = View.VISIBLE
                    }
                    else -> {
                        binding.activityHomeDrawer.userDrawerLayout.ivGender.visibility = View.GONE
                    }
                }

                fun getAge(): String {
                    return if (!it.birthday.isNullOrBlank() && it.birthday!!.length > 9) {
                        var age = it.birthday!!.substring(0, 10)
                        if (StringUtils.getAgeByBirth(age) != null)
                            StringUtils.getAgeByBirth(age).toString()
                        else {
                            "未设置"
                        }
                    } else {
                        "未设置"
                    }
                }

                binding.activityHomeDrawer.userDrawerLayout.tvUserAgePosition.text = getAge() + ".${it.profession ?: "未设置"}"
                binding.activityHomeDrawer.userDrawerLayout.tvUserMainCompany.text = CompanyHolder.getMainOrg()?.name ?: "未设置"
                userEnts.clear()
                userEnts.addAll(userEntrances)
                userInfoAdapter.notifyDataSetChanged()
            }
        } else {
            binding.activityHomeDrawer.userDrawerLayout.ivUserAvatar.visibility = View.INVISIBLE
            binding.activityHomeDrawer.userDrawerLayout.tvUserName.text = ""
            binding.activityHomeDrawer.userDrawerLayout.ivGender.visibility = View.GONE

            binding.activityHomeDrawer.userDrawerLayout.tvUserAgePosition.text = ""
            binding.activityHomeDrawer.userDrawerLayout.tvUserMainCompany.text = ""
            userEnts.clear()
            userInfoAdapter.notifyDataSetChanged()
        }
    }

    private fun updateDrawerData() {
        if (binding.activityHomeContent.vpHome.currentItem == 0) {
            updateUserInfo()
            if (binding.activityHomeDrawer.userDrawerLayout.clUserLayout.visibility != View.VISIBLE) {
                binding.activityHomeDrawer.userDrawerLayout.clUserLayout.visibility = View.VISIBLE
            }
            if (binding.activityHomeDrawer.orgDrawerLayout.clOrgLayout.visibility != View.GONE) {
                binding.activityHomeDrawer.orgDrawerLayout.clOrgLayout.visibility = View.GONE
            }
        } else {
            workList.forEach {
                if (it.isSelected) {
                    if (StringUtils.isNotBlankAndEmpty(it.logo)) {
                        ImageLoaderUtils.loadImage(mContext!!, binding.activityHomeDrawer.orgDrawerLayout.ivCompanyLogo, it.logo)
                    }
                    binding.activityHomeDrawer.orgDrawerLayout.tvCompanyName.text = it.name
                }
                // 审批待处理数量
                val aprCount =
                    if (companyIdToUnreadMap.isNotEmpty() && companyIdToUnreadMap.containsKey(it.companyId)) {
                        companyIdToUnreadMap[it.companyId]!!
                    } else {
                        0
                    }
                // 金蝶审批待处理数量
                val kingDeeEntroyBean=entroyBeanList.find { it.name=="金蝶审批" }
                val kingDeeCount =
                    if (entroyBeanList.isNotEmpty() &&kingDeeEntroyBean!=null) {
                       kingDeeEntroyBean.unreadInt
                    } else 0

                it.haveApprove = if (aprCount > 0 || kingDeeCount > 0) {
                    max(aprCount, kingDeeCount)
                } else {
                    0
                }
            }
            workAdapter.notifyDataSetChanged()
            if (binding.activityHomeDrawer.userDrawerLayout.clUserLayout.visibility != View.GONE) {
                binding.activityHomeDrawer.userDrawerLayout.clUserLayout.visibility = View.GONE
            }
            if (binding.activityHomeDrawer.orgDrawerLayout.clOrgLayout.visibility != View.VISIBLE) {
                binding.activityHomeDrawer.orgDrawerLayout.clOrgLayout.visibility = View.VISIBLE
            }
        }
    }

    /**更新所有团队的审批未读数量，返回结果和团队id顺序对应*/
    //刷新审批未读数======1，每次切换当前公司后都会触发，2，接收到刷新事件时也会触发；3，首页getAllCompany方法执行时也会触发
    private fun updateAprWaitData(type: Int = 0) {
        showLog("刷新首页未读审批数量 type = $type")
        showLoading()
        if (UserHolder.isLogin()) {
            orgService?.service("getAprWaitCount", Bundle()) {
                hideLoading()
                companyIdToUnreadMap.clear()
                if (!it.isNullOrBlank()) {
                    val json = JSONObject(it)
                    workList.forEach { org ->
                        val count = json.optInt(org.companyId)
                        if (count > 0) {
                            companyIdToUnreadMap[org.companyId] = count
                        }
                    }
                }
                updateApprovalUnreadCount()
            }
        } else {
            hideLoading()
        }
    }

    private val companyIdToUnreadMap = hashMapOf<String, Int>()//key是companyId,value是审批未读数
    private val entroyBeanList = arrayListOf<EntroyBean>()


    private fun updateApprovalUnreadCount() {
        showLog("工作首页 更新顶部红点显示 $companyIdToUnreadMap")
        if (companyIdToUnreadMap.isNullOrEmpty()) {
            EventBusUtils.sendEvent(
                EventBusEvent(
                    EventBusAction.send_all_appro_undo_result, null
                )
            )
        } else {
            EventBusUtils.sendEvent(
                EventBusEvent(
                    EventBusAction.send_all_appro_undo_result, companyIdToUnreadMap
                )
            )
        }
    }


    //整合接口==第二步：将整合后的数据集合发送到工作页tcp
    private fun updateInteGrateData(data: ArrayList<EntroyBean>) {
        showLog("工作首页 更新金蝶数量")
        if (data.isNullOrEmpty()) {
            EventBusUtils.sendEvent(
                EventBusEvent(
                    EventBusAction.event_refresh_home_entroy_list, null
                )
            )
        } else {
            EventBusUtils.sendEvent(
                EventBusEvent(
                    EventBusAction.event_refresh_home_entroy_list, data
                )
            )
        }
    }

    private fun createGroupEvent(
        selectUserIds: List<String>, groupName: String,
        selectUserInfo: List<GroupInviteUserInfoBean>
    ) {
        getLoadingDialog("", false)
        GroupService.createGroup(selectUserIds, groupName, accessToken!!)
            .compose(bindToLifecycle())
            .compose(ErrorTransformer.getInstance())
            .subscribe(object : BaseSubscriber<GroupCreateBean>() {
                override fun onError(ex: ApiException) {
                    dismissDialog()
                    toastShort(ex.message)
                }

                override fun onComplete() {

                }

                override fun onNext(t: GroupCreateBean?) {
                    dismissDialog()
                    if (t != null) {
                        ARouter.getInstance()
                            .build(RouteIm.groupChat)
                            .withString("targetId", t.groupId)
                            .withString("targetLogo", t.logo)
                            .withString("targetName", groupName)
                            .navigation()
                    }
                }

            })
    }

    /**获取团队信息，会触发审批未读数量更新，团队内容切换：任务，工作首页，通讯录等*/
    private fun getAllCompany(isPop: Boolean) {
        BaseApplication.reConnectedToGetOffLineSessionList = false
        showLog("首页::更新所有团队列表")
        getAllCompany(isPop) {
            showLog("获取所有团队列表成功后，处理im相关请求逻辑")
            hideLoading()
            //获取所有组织（不包含合作团队）和所有合作团队信息成功，此时再获取离线的通知会话
            // im 登录
            if (BaseApplication.needGetImToken || imToken.isBlank()) {
                getImTokenBean()
            } else {
                Logger.i("会话列表", "===imtoken已经有值==去执行=getOfflineData==")
                getOfflineData()
                loginLongConnect()
            }
        }
    }

    private fun getAllCompany(isPop: Boolean, onResult: (String) -> Unit) {
        showLog("首页::团队解散或请离后，更新所有团队列表")
        if (UserHolder.isLogin()) {
            val bundle = Bundle()
            bundle.putString("token", UserHolder.getAccessToken())
            Logger.i("accessToken", "===[${UserHolder.getAccessToken()}]===")
            bundle.putString("userId", UserHolder.getUserId())
            if (orgService != null) {
                if (isPop) {
                    showLoading()
                }
                orgService?.service("getAllCompany", bundle, onResult)
            } else {
                showLog("org service 初始化失败")
            }
        } else {
            showLog("当前用户未登录，需要重新登录")
            hideLoading()
        }

    }

    // 创建快捷方式
    @RequiresApi(Build.VERSION_CODES.N_MR1)
    private fun setDynamicShortCuts(companyId: String) {
        val shortcutManager = getSystemService(ShortcutManager::class.java)
        val shortcutInfo = arrayListOf<ShortcutInfo>()
        shortcutInfo.add(createShortcutInfo(companyId))
        if (shortcutManager != null) {
            shortcutManager.dynamicShortcuts = shortcutInfo
            MMKVUtil.saveString(shortCutId, companyId)
        }
    }

    @RequiresApi(Build.VERSION_CODES.N_MR1)
    private fun createShortcutInfo(companyId: String): ShortcutInfo {
        val intent1 = Intent(mContext!!, SplashActivity::class.java)
        intent1.action = Intent.ACTION_VIEW
        val intent = Intent(mContext!!, AttendanceHomeActivity::class.java)
        intent.action = Intent.ACTION_VIEW
        intent.putExtra(ConsKeys.COMPANY_ID, companyId)
        val colorSpan = ForegroundColorSpan(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.text_black))
        val label = "考勤打卡"
        val spanny = Spanny().append(label, colorSpan)
        val intents = arrayOf(intent1, intent)
        return ShortcutInfo.Builder(this, "attendance")
            .setShortLabel(spanny)
            .setLongLabel(spanny)
            .setIcon(Icon.createWithResource(mContext!!, com.jounutech.work.R.drawable.icon_atten))
            .setIntents(intents)
            .build()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == scan && resultCode == Activity.RESULT_OK) {//扫描后处理数据
            ScanResultUtil.dealScanResult(data, mContext!!)
        } else if (requestCode == IM_VC_SELECT_PERSON && resultCode == Activity.RESULT_OK
            && data != null
        ) {
            val hashSet =
                data.getSerializableExtra("selectUserIds") as HashSet<UserInfo>
            val selectUserIds: HashSet<String> = hashSet.map { it.userId }.toHashSet()
            val selectUserInfo = hashSet.map {
                GroupInviteUserInfoBean(it.userId, it.userName)
            }.toList()
            var selectUserName = ""
            if (StringUtils.isNotBlankAndEmpty(data.getStringExtra("selectUserName")))
                selectUserName = data.getStringExtra("selectUserName")!!
            if (!selectUserIds.isNullOrEmpty()) {
                createGroupEvent(
                    selectUserIds.filter { it != userId }.toList(),
                    selectUserName, selectUserInfo
                )
            }
        } else /*if (requestCode == 0x22 && resultCode == 0x22)*/ {
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

    @SuppressLint("RtlHardcoded")
    override fun onBackPressed() {
        if (binding.drawerHome.isDrawerOpen(Gravity.LEFT)) {
            closeDrawerContent()
        }
        if (binding.activityHomeContent.tablayoutHome.visibility == View.VISIBLE) {
            if (System.currentTimeMillis() - exitTime > 2000) {
                Toast.makeText(this, "再按一次返回键退出", Toast.LENGTH_SHORT).show()
                exitTime = System.currentTimeMillis()
            } else {
                // 不关闭App，只是回到桌面
                val launcherIntent = Intent(Intent.ACTION_MAIN)
                launcherIntent.addCategory(Intent.CATEGORY_HOME)
                startActivity(launcherIntent)
            }
        } else {
            if (binding.activityHomeContent.vpHome.currentItem == 3) {
                showLog("当前显示云文档，点击了系统返回键")
                EventBusUtils.sendEvent(EventBusEvent("cloud_doc_back_press", ""))
            }
        }
    }

    //及时注销掉广播
    override fun onDestroy() {
        if (receiver != null) {
            unregisterReceiver(receiver)
            receiver = null
        }
        //旧push,位置4
       /* try {
            PushCenter.stopPush(this)
        } catch (e: Exception) {

        }*/
        super.onDestroy()
    }

    /**tcp获取离线会话(单聊群聊)*/
    private fun getOffLineSessionListFromNet() {
        Logger.i("会话列表", "======开始拉取离线会话数据====单聊群聊==imToken=" + imToken)
        if (!imToken.isBlank()) {
//            ImServiceNetUtil.getOffLineSessionList(bindToLifecycle(), this, imToken!!,
//                onSuccess = {
//                    Logger.i("---会话列表-", "--获取离线会话(单聊群聊)-----成功--it.size--" + it.size)
//                    ImServiceNetUtil.saveOffLineSessionList(this, userId!!, it , callBack = {})
//                },
//                onFailer = {
//                    Logger.i("---会话列表---", "--获取离线会话(单聊群聊)--失败-" + it)
//                })
        }
    }

    /**tcp获取离线会话(通知)*/
    private fun getOffLineNoticeSessionListFromNet() {

    }

    /** drawer listener*/
    override fun onDrawerStateChanged(newState: Int) {
    }

    override fun onDrawerSlide(drawerView: View, slideOffset: Float) {
    }

    override fun onDrawerClosed(drawerView: View) {
        binding.drawerHome.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED)
        updateStatusBar()
    }

    override fun onDrawerOpened(drawerView: View) {
        immersionBar?.statusBarDarkFont(true)?.init()
    }
    /** drawer listener*/

}

/**
 * todo HomeActivity
 * 推送消息打开页面
 * 初始化页面UI
 * 注册网络监听
 * 初始化侧滑页面，响应展开（请求成功团队列表后才可展开）
 * 更新头部显示，响应头部点击事件
 * 获取首页数据：所有公司数据，--好友列表，--设置推送
 *
 * 首页业务：--统计，--版本更新检测，im登录
 * 扫一扫，添加好友，创建群组，创建项目等弹窗处理
 * 返回键关闭app
 * 设置快捷方式
 *
 * */