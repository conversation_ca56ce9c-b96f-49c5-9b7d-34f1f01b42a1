package com.joinutech.ddbes.flutter

import android.content.Context
import android.content.ContextWrapper
import android.view.View
import androidx.core.view.doOnAttach
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentContainerView
import androidx.fragment.app.commit
import com.joinutech.message.view.tcpimpages.session.SessionFragment
import io.flutter.plugin.common.BinaryMessenger
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.StandardMessageCodec
import io.flutter.plugin.platform.PlatformView
import io.flutter.plugin.platform.PlatformViewFactory
import java.util.Random


/**
 * @Des：
 * @author: moon
 * @date: 9/8/23
 */

//messenger：用于消息传递，后面介绍 Flutter 与 原生通信时用到此参数。
//viewId：View 生成时会分配一个唯一 ID。
//args：Flutter 传递的初始化参数
class IMFlutterView(context: Context, messenger: Binary<PERSON><PERSON><PERSON><PERSON>, viewId: Int, args: Map<String, Any>?) : PlatformView,
    MethodChannel.MethodCallHandler{

    private val view: FragmentContainerView

    var mContext: Context? = null

    init {
        this.mContext = context
        val fragment = SessionFragment.newInstance()
        val view = FragmentContainerView(mContext!!)
        view.id = Random().nextInt()
        view.doOnAttach {
            val activity = it.context.getFragmentActivityOrThrow()
            activity.supportFragmentManager.findFragmentByTag("flutter_fragment")?.let { flutterFragment ->
                flutterFragment.childFragmentManager.commit {
                    replace(it.id, fragment)
                }
            }
        }
        this.view = view
    }

    override fun getView(): View {
        return view
    }

    override fun dispose() {

    }

    private fun Context.getFragmentActivityOrThrow(): FragmentActivity {
        if (this is FragmentActivity) {
            return this
        }

        var currentContext = this
        while (currentContext is ContextWrapper) {
            if (currentContext is FragmentActivity) {
                return currentContext
            }
            currentContext = currentContext.baseContext
        }

        throw IllegalStateException("Unable to find activity")
    }

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {

    }

}


class MyFlutterViewFactory(val mContext : Context , val messenger: BinaryMessenger) : PlatformViewFactory(
    StandardMessageCodec.INSTANCE) {

    override fun create(context: Context, viewId: Int, args: Any?): PlatformView {
        return IMFlutterView(mContext, messenger, viewId, args as Map<String, Any>?)
    }

}
