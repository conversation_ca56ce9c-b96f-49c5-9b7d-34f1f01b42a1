package com.joinutech.ddbes.flutter

import android.content.Context
import android.content.Intent
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.library.im.ImService
import com.ddbes.library.im.imtcp.ConstantEventCodeUtil
import com.ddbes.library.im.imtcp.Logger
import com.ddbes.library.im.imtcp.dbope.MessageDaoOpe
import com.ddbes.library.im.imtcp.dbope.SessionDaoOpe
import com.ddbes.library.im.imtcp.imservice.msghelper.SendMsgHelper
import com.ddbes.library.im.imtcp.imservice.msghelper.SendMsgToDBMsgUtil
import com.ddbes.library.im.imtcp.imservice.sessionhelper.SessionToDBUtil
import com.ddbes.library.im.netutil.OfflineNewPush
import com.ddbes.library.im.util.GroupCacheHolder
import com.ddbes.personal.view.AboutAppActivity
import com.ddbes.personal.view.PersonalSettingActivity
import com.google.common.eventbus.EventBus
import com.joinu.im.protobuf.MsgBean
import com.joinutech.addressbook.view.OrgPermissionActivity
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.NotifyUtil
import com.joinutech.common.util.ObjectStore
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.bean.PageInfoBean
import com.joinutech.ddbeslibrary.utils.COMMON_WEB_ACTIVITY
import com.joinutech.ddbeslibrary.utils.ConsKeys
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.RouteIm
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.RoutePersonal
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.toast
import com.joinutech.flutter.EventRefreshOfflineData
import com.joinutech.message.view.tcpimpages.SingleChatActivity
import com.joinutech.message.view.tcpimpages.historymsg.SearchHistoryMsgActivity
import com.jounutech.work.R
import com.jounutech.work.constract.RouteAte
import com.jounutech.work.view.BulletinListActivity
import io.flutter.plugin.common.MethodCall
import timber.log.Timber
import java.util.logging.Handler

/**
 * @Des：
 * @author: moon
 * @date: 9/12/23
 */
class FlutterRouter {

    companion object {

        fun routeSettingPage(context: Context) {
            val intent = Intent(context, PersonalSettingActivity::class.java)
            context.startActivity(intent)
        }

        fun routeAboutPage(context: Context) {
            val intent = Intent(context, AboutAppActivity::class.java)
            context.startActivity(intent)
        }

        fun routePersonProfile(context: Context) {
            ARouter.getInstance().build(RoutePersonal.personalInfo).navigation()
        }

        fun routeSingleChatPage(context: Context , call: MethodCall) {
            val jumpSingleChat = call.arguments
            // {single={positionId=, headimg=https://cdn.ddbes.com/HEAD/liX1OWQKPP19lTCEIL1678757198956.png, level=1, initial=, name=李征, remark=, userId=2468510355769625597, imId=20thFrKGFtK}}
            Timber.i("jumpSingleChat --> $jumpSingleChat")
            val body = call.argument("single") as? Map<String,Any>?
            val headimg = (body?.get("headimg") as? String) ?:""
            val name = (body?.get("name") as? String) ?:""
            val userId = (body?.get("userId") as? String) ?:""
            val imId = body?.get("imId") as? String
            val judgeFriend = (body?.get("judgeFriend") as? Int) ?:0

            if (judgeFriend == 1){
                sendMsgafterAddFriend(context , imId ?:"", userId ?:"" , UserHolder.getCurrentUser()?.userId ?:"", name ,  headimg)
            }
            // context: Context , imId: String ,userId :String , otherUserId:String ,name :String , headimg : String

            val intent = Intent(context, SingleChatActivity::class.java)
            intent.putExtra("targetId", userId)
            intent.putExtra("targetName", name)
            intent.putExtra("targetLogo", headimg)
            intent.putExtra("sessionId", imId)
            context.startActivity(intent)

            sendNewSession(imId , userId , headimg , name)

            android.os.Handler().postDelayed({
                org.greenrobot.eventbus.EventBus.getDefault().post(EventRefreshOfflineData(1))
            },2000)
        }

        private fun sendNewSession(sessionId: String? ,targetUserId: String , logo: String , name: String) {
            if (sessionId == null) return
            try {
                val map = linkedMapOf<String, Any>()
                map.put("handleType", 4)
                map.put("handleId", sessionId)
                map.put("appChatId", targetUserId)
                map.put("name", name)
                map.put("headerUrl", logo)
                val jsonData = GsonUtil.toJson(map)
                val uuid = SendMsgHelper.getUUID()
                val newSessionMsg = MsgBean.ExtMsg.newBuilder()
                    .setExt1(jsonData)
                    .build()
                val sendMsg = SendMsgHelper.getC2CMsgRequestMsgFromMutilSynchronization(sessionId, newSessionMsg, uuid)
                val isSucceed = ImService.sendMsg(sendMsg)
            }catch (e: Exception){}
        }

        //
        fun routeGroupChatPage(context: Context , call: MethodCall) {
            val body = call.arguments
            // {groupId: 2596642393209976829, name: 加优带学生是, logo: , createUserId: 2538270819834921982, type: 2, orgId: , initial: J}
            Timber.i("jumpGroupChat --> $body")
            val map = (call.argument("group") as? Map<String,Any>)
            val groupId = map?.get("groupId") as? String
            val name = map?.get("name") as? String
            val userId = map?.get("createUserId") as? String
            val headimg = map?.get("logo") as? String
            val orgId = map?.get("orgId") as? String

            val groupInfo = GroupCacheHolder.getGroupInfo(userId ?:"")

            ARouter.getInstance()
                .build(RouteIm.groupChat)
                .withString("targetId", groupId)
                .withString("targetName", name)
                .withString("targetLogo", headimg)
                .withString("companyId", orgId) // 可以为空
                .navigation()
        }

        // todo 未完成
        fun routeInviteIM(context: Context , call: MethodCall) {
            val body = call.arguments
            val model = call.argument("model") as? Map<String,Any>
            if (model == null) return
            val type = call.argument("addOrgMsg") as? String
            val companyName = model.get("name") as? String
            val companyId = model.get("companyId") as? String
            val companyLogo = model.get("logo") as? String

            ARouter.getInstance()
                .build(RouteOrg.searchFriendToTranslateActivity)
                .withString("type", "addOrgMsg")
                .withString("companyName", companyName)
                .withString("companyId", companyId)
                .withString("companyLogo", companyLogo)
                .navigation()
        }

        // 通用打开原生 webView TODO
        fun routeWebView(context: Context , url: String) {
            ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                .withString(ConsKeys.PARAMS_URL, url)
                .navigation()
        }

        fun jumpImSearch(context: Context ) {
            val intent = Intent(context, SearchHistoryMsgActivity::class.java)
            context.startActivity(intent)
        }

        fun flutterLogOut(context: Context) {
            // Flutter 调用原生退出登录：
            try {
                NotifyUtil.instance.clearAllNotify()
                BaseApplication.currentNetState = 0
                UserHolder.clearImTokenBean()
                ImService.doLogout()
//                PushUtil.endManufacturerPush(context)
                UserHolder.onLogout()
                CompanyHolder.onLogout()
            }catch (e: Exception){}
        }

        fun routeOrgPermissionPage(context: Context , call: MethodCall) {
            val companyId = (call.argument("companyId") as? String) ?: ""
            val deptId = (call.argument("deptId") as? String) ?: ""
            val intent = Intent(context, OrgPermissionActivity::class.java)
            intent.putExtra("companyId", companyId)
            intent.putExtra("isCreator", deptId == "0")
            context.startActivity(intent)
        }

        fun routeAttendancePage(context: Context , call: MethodCall) {
            val companyId = (call.argument("companyId") as? String) ?: ""
            ARouter.getInstance().build(RouteAte.attendanceHomeActivity)
                .withString(ConsKeys.COMPANY_ID, companyId)
                .navigation()
        }

        fun routeAttendanceSettingPage(context: Context , call: MethodCall) {

            ARouter.getInstance()
                .build(RouteAte.attendanceSetActivity)
                .navigation()
        }

        // type 0立即创建1更多2详情 isHaveAuth是否有组织权限 orgId公司id
        fun routeAnnouncement(context: Context , call: MethodCall) {
            val type = (call.argument("type") as? Int) ?: ""
            val isHaveAuth = (call.argument("isHaveAuth") as? Int) ?: 0
            val orgId = (call.argument("orgId") as? String) ?: ""
            val noticeId = (call.argument("noticeId") as? String) ?: ""

            Timber.i("type: $type , isHaveAuth: $isHaveAuth , orgId: $orgId , noticeId: $noticeId")

            if (type == 1) {
                // 列表
                val intent = Intent(context, BulletinListActivity::class.java)
                intent.putExtra("isHaveAuth" , isHaveAuth)
                intent.putExtra("orgId" , orgId)
                context.startActivity(intent)
            } else if (type == 0) {
                // 新增
                val pageInfo = PageInfoBean()
                pageInfo.title = context.resources.getString(com.joinutech.ddbeslibrary.R.string.notice_create_title)
                pageInfo.pageType = 0
                pageInfo.companyId = orgId

                ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                    .withString("pageInfo", GsonUtil.toJson(pageInfo))
                    .withBoolean("isNoticeWeb", true)
                    .navigation()

            } else if(type == 2){
                // 详情
                val pageInfo = PageInfoBean()
                pageInfo.title = context.resources.getString(com.joinutech.ddbeslibrary.R.string.notice_detail_title)
                pageInfo.noticeId = noticeId
                pageInfo.pageType = 1
                pageInfo.companyId = orgId

                if (noticeId.isNullOrBlank() || orgId.isNullOrBlank()) {
                    toast(context, "暂时无法查看详情")
                    return
                }
                ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                    .withString("pageInfo", GsonUtil.toJson(pageInfo))
                    .withBoolean("isNoticeWeb", true)
                    .navigation()

            }
        }

        fun openNativeWebView(context: Context , call: MethodCall) {
            val title = (call.argument("title") as? String) ?: ""
            val isWebNavigation = (call.argument("isWebNavigation") as? Int) ?: 0   // 0 显示原生导航栏  1 不显示
            val url = (call.argument("url") as? String) ?: ""
            val orgId = (call.argument("orgId") as? String) ?: ""

            val fileInfo = (call.argument("fileInfo") as? Map<String, String>) ?: mapOf()

            Timber.i("openNativeWebView: $isWebNavigation , $title , $url")

            ObjectStore.add("wpsfileInfo" , fileInfo)

//            url = "http://10.0.1.10:9000/ddbes-train-mobile/index.html#/?platform=1&appVersion=40001"

            ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
                .withString("titleName", title)
                .withString(ConsKeys.PARAMS_URL,url)
                .withString(ConsKeys.COMPANY_ID, orgId)
                .withInt("backspaceKey",isWebNavigation)
                .navigation()

        }

        private fun sendMsgafterAddFriend(context: Context , imId: String ,userId :String , otherUserId:String ,name :String , headimg : String) {
            EventBusUtils.sendEvent(EventBusEvent(
                ConstantEventCodeUtil.TCP_REFRESH_FRIEND_LIST, ""))
            // 首页待处理红点，好友请求列表，所有待处理，
            EventBusUtils.sendEvent(EventBusEvent(
                EventBusAction.REFRESH_APPLIY_LIST, "friend"))


            ////给对方发送一条添加成功的文本消息
            val sendMsgContent = "我们已经是好友了,快来和我一起聊天吧!"
            val uuid = SendMsgHelper.getUUID()
            val sendMsg = SendMsgHelper.getC2CMsgRequestMsg(imId, sendMsgContent, uuid)
            val isSucceed = ImService.sendMsg(sendMsg)
            //发送的消息存入数据库
            val dbMessage = SendMsgToDBMsgUtil.getDbMsgFromText(userId!!, otherUserId, imId, sendMsgContent, uuid)
            Logger.i("--执行-发送文本-", "--isSucceed-" + isSucceed)
            if (!isSucceed) {
                dbMessage.isSuccess = 0
            }
            MessageDaoOpe.instance.insertMessage(context, dbMessage)
            //发送数据之后保存会话
            val sessionDB = SessionToDBUtil.getSessionDB(userId, name, headimg, 0, dbMessage)
            SessionDaoOpe.instance.insertSession(context, sessionDB)
            //发送会话变化的通知
            EventBusUtils.sendEvent(EventBusEvent(ConstantEventCodeUtil.TCP_SESSION_CHANGE))
        }
    }


}