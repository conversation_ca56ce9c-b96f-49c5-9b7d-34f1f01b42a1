package com.joinutech.ddbes.flutter

/**
 * @Des：
 * @author: moon
 * @date: 9/8/23
 */
class FlutterConstant {

    companion object {

        // method Channel ： flutter -> native
        const val methodChannel = "samples.flutter.io/flutterCallNative"

        // todo im 使用的 Channel
        const val imMethodChannel = "im_platform_plugin"

        // im 的viewType
        const val imViewType = "im_platform_view"

        // 0:公司数据 1:群组数据 2:好友数据 3:6合一数据 4:IM数据
        const val FLUTTER_DATA_COMPANY = 0
        const val FLUTTER_DATA_GROUP = 1
        const val FLUTTER_DATA_FRIENDS = 2
        const val FLUTTER_DATA_SYNTHETIC = 3
        const val FLUTTER_DATA_IM_TOKEN = 4


        // ------------ FLUTTER -> NATIVE -------------
        val login = "login"
        const val minePageJumpNext = "minePageJumpNext"
        const val jumpSingleChat = "jumpSingleChat" // 进入单聊界面
        const val jumpGroupChat = "jumpGroupChat" // 进入群聊界面
        const val jumpChooseIM = "jumpChooseIM" // 通过聊天邀请
        const val jumpIMSearch = "jumpIMSearch" // 通过聊天邀请
        const val FlutterLogOut = "logOut" // 通过聊天邀请
        const val ContactJumpAuthSetting = "contactJumpAuthSetting" //
        const val openWebview = "jumpWebView" //  TODO 待定， 进入webview
        const val openAttendancePage = "native_page/attendance" //   进入考勤
        const val openAttendanceSettingPage = "native_page/attendance_setting" //   进入考勤设置
        const val openAnnounce = "native_page/announce" //   进入公告： type 0立即创建1更多2详情 isHaveAuth是否有组织权限 orgId公司id
        const val call_native_backPage = "call_native_backPage" //
        const val openNativeSignature = "native_page/signatrue" //  跳转签名

        const val getNativeLocation = "native_page/position_auto" //  直接获取定位
        const val openNativeLocationPage = "native_page/position" //  打开原生定位界面
        const val nativePageimVideo = "native_page/im_video" //  跳转 音视频
        const val channel_open_map = "channel_open_map" //  打开原生定位界面
        const val nativeClosePage = "native_page/goback" //
        const val nativeTxUpload = "native_uploadFile" //  腾讯云上传(单个)
        const val nativeTxUploadList = "native_uploadFileList" //  腾讯云上传(多个)
        const val getKingDeeToken = "native_page/getKingDeeToken" //  Flutter 获取金蝶的token， （判断是否过期）

        const val f2nRefreshFriend = "native_page/main_refreshFriend" //
        const val f2nRefreshreOrgData = "native_page/native_page/main_refreshOrgData" //
        const val f2nRefreshPending = "native_page/main_refreshPending" //
        const val f2nUpgradeApp = "native_page/update" // 更新升级app
        const val f2nRefreshCurrentOrg = "native_page/refreshCurrentOrg"
        const val getNativeDeviceId = "getNativeDeviceId"
        const val getNativeHeader = "native_page/getHeader"
        const val getNativeDeviceModel = "getNativeDeviceModel"
        const val getNativeUnReadCount = "getNativeUnReadCount"
        const val main_approveUnread = "native_page/main_approveUnread"  //
        const val sendWeChatCode = "sendWeChatCode"  //
        const val Channel_version_name = "Channel_version_name"  //
        const val Channel_version_code = "Channel_version_code"  //
        const val about_check_update = "about_check_update"  //
        const val refresh_group_info = "refresh_group_info"  //
        const val NATIVE_POP_ROOT = "native_popRoot"  //
        const val CALLNATIVE_JUMPROUTE = "callNative_jumpRoute"  //
        const val TAKE_MEAL_SUCCESS = "takeMealSuccess"  //取餐成功刷新页面

        // android 专门使用
        const val GETANDROIDDEVICEMODE = "getAndroidDeviceMode"

        // rtc 会议
        const val RTC_MEETING = "jumpMeeting"
        const val REPORT_EXCEPTION = "report_exception"
        const val native_pageermissions = "native_page/permissions"
        const val native_pageattendance_rule = "native_page/attendance_rule"
        const val native_pageattendance_adjust = "native_page/attendance_adjust"


        ///  参数：
        // {
        //      'list': forwardList,
        //      'forwardType': forwardType,
        //      'fromSessionId': fromSessionId,
        //      'text':text //合并转发 留言
        //    }


        const val USERDEFAULT_TOKENKEY = "userdefault_tokenKey"  // token 更新
        const val FLUTTER_TO_NATIVE_DATA = "data_toNative"  // {'data':'返回数据','type':0}//0:公司数据 1:群组数据 2:好友数据 3:6合一数据 4:IM数据
        const val NATIVE_PUSHTOKEN = "getNativeImToken"
        const val QR_SIGN = "native_page/qr_sign"  // 扫码签名
        const val RE_START_APP = "ChannelReStartApp"
        const val SWITCH_ENV = "ChannelSwitchEnv"

        // 转发相关逻辑
        const val REQUESTANDROIDLAUNCHPERMISSION = "requestAndroidLaunchPermission"   // param  {'fromSessionId':fromSessionId}

        const val NATIVE_PAGERECEIVE_CALL_MSG = "native_page/receive_call_msg"   //  接收到了音视频消息

        const val CHANEL_CLEAR_NATIVE_VC = "chanel_clear_native_vc"  // 清栈
        const val BRIDGET_TO_MAIN_ENGINE = "bridget_to_main_native_engine";

        //离线跳转
        const val FLUTTER_HOME_ONREADY = "flutter_home_onready";
        const val START_WEB_SOCKET = "startWebSocket";
        const val FLUTTER_SEND_SOCKET_DATA = "flutter_send_socket_data";
        const val FETCH_SOCKET_PARAM = "fetch_socket_param";
        const val DISCONNECT_SOCKET = "disconnect_socket";


        // ------------ FLUTTER -> NATIVE -------------



        // ------------ NATIVE -> FLUTTER -------------
        val loginOut = "loginOut"
        val backSignatrueBase64 = "backSignatrueBase64";
        val passSixMergeData = "sixMergeData";  // 六合一接口传递给Flutter
        val refreshOrgData = "refreshOrgData";  // 刷新公司数据
        val refreshUserData = "refreshUserData";  // 刷新用户信息
        val refreshFriendData = "refreshFriendData";  // 刷新好友数据
        val nativeJumpArgument = "nativeJumpArgument";  //
        val refreshContactPendingData = "refreshContactPendingData";  // 刷新通讯录红点数据
        val flutterBackPage = "androidOnPressed";  //
        val refreshGroupData = "refreshGroupData";  //
        val unReadMsgCountUpdate = "unReadMsgCountUpdate";  // im 未读数
        val refreshApproveUnreadData = "refreshApproveUnreadData";  // 审批红点
        val refreshWorkRedPoint = "refreshWorkRedPoint";  // //刷新工作台模块红点与所有公司红点
        val newsHomeShowMemu = "newsHomeShowMemu";  // 展开菜单
        val refreshDataWithType = "refreshDataWithType";  //
        val receiveMeetingDeleted = "receiveMeetingDeleted";  //
        val enterMeeting = "enterMeeting";  //
        val refreshIMData = "refreshIMData";  //
        val clearCache = "clearMemoryImageCache";  //
        val tokenError = "tokenError";  //

        val FORCE_REFRESH_WORKBENCH = "forceRefreshWorkbench"
        val CHANNEL_OFFLINE_NOTIFICATION_ROUTE = "channel_offline_notification_route"
        val ONAPPRESUMED = "onAppResumed"
        val ONAPPPAUSED = "onAppPaused"
        val CHANNEL_SEND_IM_MSG = "channel_send_im_msg"    // 原生向 Flutter 发送 im 消息 场景1 「」
        val CHANNEL_REFRESH_MEETING_CHECK = "channel_refresh_meeting_check"    // 原生向 Flutter 发送 刷新群组音视频状态「」 参数 sessionId
        val DELETE_KINGDEEAPPROVE = "deleteKingdeeApprove"
        val CLOSE_WEB_VIEW = "close_current_page"
        val REOPEN_WEB_VIEW = "re_open_web"
        val GET_TIANDITU_POSITION = "get_tianditu_position"
        val OPEN_FLUTTER = "open_flutter"
        val FLUTTER_SOCKET_BYTE = "flutter_socket_byte"
        val FLUTTER_SOCKET_STATUS = "flutter_socket_status"
        // create-and-join
        // ------------ NATIVE -> FLUTTER -------------



        // Flutter 定义的路由名字
        const val SPLASH_PAGE = "/splash-page"
        const val USER_INFO = "/user-info"
        const val RTC_MEETING_DETAIL = "/meeting-detail"
        const val APPROVE_DETAIL = "/approve-detail"
        const val APPROVE_LIST = "/approve-list"
        const val JUMPROUTE_CREATE_AND_JOIN = "/create-and-join";
        const val GROUP_CHANGE_INFO = "/group-change-info";
        const val FRIEND_APPLY_DETAIL = "/friend-apply-detail";
        const val CHOOSE_FRIEND_AND_ORG = "/group-add-members"; // 群二维码
        const val INVITE_BY_SESSION = "/invite_by_session"; //
        const val QR_CODE = "/qr-code"; //

        fun getCacheId(entryPoint: String): Int {
            return when(entryPoint){
                SPLASH_PAGE -> 10102
                APPROVE_DETAIL -> 10103
                USER_INFO -> 10104
                JUMPROUTE_CREATE_AND_JOIN -> 10105
                FRIEND_APPLY_DETAIL -> 10106
                CHOOSE_FRIEND_AND_ORG -> 10107
                APPROVE_LIST -> 10108
                RTC_MEETING_DETAIL -> 10109
                GROUP_CHANGE_INFO -> 10110
                INVITE_BY_SESSION -> 10111
                QR_CODE -> 10112
                else -> 10102
            }
        }
    }



}