package com.joinutech.ddbes.flutter.webview

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout

/**
 * @Des：
 * @author: moon
 * @date: 4/18/25
 */
class DDWebView : FrameLayout {


    constructor(context: Context) : super(context) {
        initView(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView(context)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        initView(context)
    }


    fun initView(context: Context) {

    }

}