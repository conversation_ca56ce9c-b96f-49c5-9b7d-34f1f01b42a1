package com.joinutech.ddbes.flutter

import android.app.Activity
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import android.os.Bundle
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.library.im.imtcp.dbope.DDbesDbManager
//import com.joinu.jpush.DDJpushManager
import com.joinu.rtcmeeting.utils.logger
import com.joinutech.common.helper.LoginHelper
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.common.storage.FileStorage
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbes.BuildConfig
import com.joinutech.ddbeslibrary.VersionConfig
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.bean.TencentSessionBean
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.UploadFileService
import com.joinutech.ddbeslibrary.utils.ConsValue
import com.joinutech.ddbeslibrary.utils.DeviceIdUtil
import com.joinutech.ddbeslibrary.utils.DeviceUtil
import com.joinutech.ddbeslibrary.utils.Loggerr
import com.joinutech.ddbeslibrary.utils.MMKVKEY
import com.joinutech.ddbeslibrary.utils.MMKVUtil
import com.joinutech.ddbeslibrary.utils.RouteProvider
import com.joinutech.ddbeslibrary.utils.XUtil
import com.joinutech.ddbeslibrary.utils.image.GlideEngine
import com.luck.picture.lib.config.PictureSelectionConfig
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers

/**
 * @Des：
 * @author: moon
 * @date: 2/18/25
 */
object DDAppInit {

    var imei: String = ""

    fun initApp(context: Context , vm: FlutterHomeViewModel) {
        initImageLoader()
        requestPermission(context ,vm)
    }

    private fun initImageLoader() {
        if (PictureSelectionConfig.imageEngine == null) {
            PictureSelectionConfig.imageEngine = GlideEngine.createGlideEngine()
        }
    }


    fun requestPermission(context: Context ,vm: FlutterHomeViewModel) {
        if (LoginHelper.isReadPrivacy(context)){
            dealPhoneImeiPermission(context)
            getDeviceVersion(context)
//            XUtil.initLocation()
            XUtil.initBugly()
            getPanBucket()
            initTBS(context)
//            initJpush(context ,vm)
        }
    }

    fun getDeviceVersion(context: Context) {
        if (context is Activity){
            val dbVersion = DDbesDbManager.getInstance(context)?.getReadableDatabase(context)?.version
            ConsValue.deviceModel = DeviceUtil.getPlatformInfo(context).plus("/dbv_$dbVersion")
            logger("deviceModel info = ${ConsValue.deviceModel}")
            MMKVUtil.saveString(MMKVKEY.VERSION_NAME, BuildConfig.VERSION_NAME)
            MMKVUtil.saveInt(MMKVKEY.VERSION_CODE, BuildConfig.VERSION_CODE)
            val versionConfig = VersionConfig()
            val imei = MMKVUtil.getString(MMKVKEY.IMEI)
            logger("保存应用功能版本号: deviceImei = $imei," +
                    " vName = ${versionConfig.versionName} ," +
                    " vCode =${versionConfig.versionCode} ," +
                    " vCode2 =${versionConfig.versionCode2} ," +
                    "deviceModel = ${ConsValue.deviceModel}"
            )
        }

    }

    fun getPanBucket() {
        UploadFileService.getPanTencentSessionV2()
            .compose(ErrorTransformer.getInstance<TencentSessionBean>())
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(object : BaseSubscriber<TencentSessionBean>() {
                override fun onError(ex: ApiException) {

                }

                override fun onComplete() {
                }

                override fun onNext(result: TencentSessionBean) {
                    FileStorage.cosSession = result
                }
            })
    }

    fun initJpush(context: Context , vm: FlutterHomeViewModel) {
//        DDJpushManager.init(context , tokenCallBack = {
//            onStartApp(it ,vm)
//        })
//        PushUtil.initManufacturerPush(context,object: PushUtil.OnNewTokenListener{
//            override fun onClick(token: String?) {
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//                    //建立hmsPush专属的消息channel通道
//                    // fix https://bugly.qq.com/v2/crash-reporting/crashes/1673faa759/843534?pid=1
//                    val notificationManager =
//                        context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
//                    val channel =
//                        NotificationChannel("hmsPush", "担当推送", NotificationManager.IMPORTANCE_DEFAULT)
//                    notificationManager.createNotificationChannel(channel)
//                }
//                val pushToken = UserHolder.getCurrentPushToken()
//                Loggerr.i("DDAPPInit", "===DDAPPInit   推送token更新currentNetState=${BaseApplication.currentNetState}==")
//                Loggerr.i("DDAPPInit", "===DDAPPInit   推送token更新，需要重新提交start/v2===")
//                onStartApp(pushToken,vm)
//            }
//        })
    }

    var lastTime = 0L

//    private fun onStartApp(token: String , vm: FlutterHomeViewModel) {
//        if (token == null || token == "") return
//        lastTime = System.currentTimeMillis()
//        val clientCode = VersionConfig().versionCode2  //发版修改位置6，config.gradle中的versionCode,每次发版手动加一
//
//        vm.delayExecute {
//            MethodChannelManager.call6in1()
//        }
//    }

    fun initTBS(context: Context) {
        (context.applicationContext as BaseApplication).initConfig()
        //进入home页之后，首先启动tcpim前台服务
        if (!BaseApplication.tcpImServiceIsRunning) {

            (ARouter.getInstance().build(RouteProvider.IM_PROVIDER)
                .navigation() as RouteServiceProvider)
                .openPage("initTBS", Bundle())
        }
    }

    fun dealPhoneImeiPermission(context: Context) {
        if (Build.VERSION.SDK_INT >= 29) run {
            imei = DeviceIdUtil.getDeviceId(context, false)
        } else {
            imei = DeviceIdUtil.getDeviceId(context)
        }
        MMKVUtil.saveString(MMKVKEY.IMEI, imei)
    }


}