package com.joinutech.ddbes.flutter.mutiengine

import android.content.Context
import com.joinutech.ddbes.AppApplication
import com.joinutech.ddbes.flutter.FlutterConstant
import com.joinutech.ddbes.flutter.MethodChannelManager
import com.joinutech.ddbes.flutter.MyFlutterViewFactory
import com.joinutech.ddbes.flutter.DDbesFlutterPlugin
import com.joinutech.ddbes.flutter.dispatchMethodCall
import com.joinutech.ddbes.flutter.webview.DDWebViewiewFactory
import com.joinutech.ddbes.flutter.webview.webViewType
import com.joinutech.ddbeslibrary.utils.AppManager
import io.flutter.FlutterInjector
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineGroup
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import java.lang.ref.WeakReference

/**
 * @Des：
 * @author: moon
 * @date: 9/18/23
 */
class EngineBindings(context: Context, entrypoint: String, val id: Int) : MethodChannel.MethodCallHandler {

    val channel: MethodChannel
    val engine: FlutterEngine
    val entrypoint:String

    init {
        this.entrypoint = entrypoint
        // Represents a collection of FlutterEngines who share resources to allow them to be created faster and with less memory
        val engineGroup: FlutterEngineGroup = (context.applicationContext as AppApplication).engines // 全局的 FEG
        // The path within the AssetManager where the app will look for assets
        val pathToBundle: String = FlutterInjector.instance().flutterLoader().findAppBundlePath()
        // This has to be lazy to avoid creation before the FlutterEngineGroup
        val dartEntrypoint = DartExecutor.DartEntrypoint(pathToBundle, "main")
        // Creates a FlutterEngine in this group and run its DartExecutor with the specified DartEntrypoint
        engine = engineGroup.createAndRunEngine(context, dartEntrypoint , entrypoint)

        engine.plugins.add(DDbesFlutterPlugin(context))

        channel = MethodChannel(engine.dartExecutor.binaryMessenger, FlutterConstant.methodChannel)

//        GeneratedPluginRegister.registerGeneratedPlugins(engine)
    }



    fun attach() {
        channel.setMethodCallHandler(this)
        val topAct = AppManager.single_instance.currentActivity()
//        engine.platformViewsController.registry.registerViewFactory(
//            webViewType, DDWebViewiewFactory(topAct!! , engine.dartExecutor.binaryMessenger))
//        DataModel.addObserver(::observer)
        MethodChannelManager.createMethod(this.entrypoint,channel)
    }

    fun detach() {
//        engine.destroy() // Cleans up all components within this FlutterEngine and destroys the associated Dart Isolate
//        DataModel.removeObserver(::observer)
        channel.setMethodCallHandler(null)
    }

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        dispatchMethodCall(AppManager.single_instance.currentActivity()!! , call , result)
    }

//    private fun observer(data: Int): Unit = channel.invokeMethod("setData", data)
}


// A singleton/observable data model for the data shared between Flutter and the host platform.
object DataModel {
    private val observers: MutableList<WeakReference<(Int) -> Unit>> = mutableListOf()

    var data = 0
        set(value) {
            field = value
            observers.mapNotNull { it.get() }.forEach { it.invoke(value) }
        }

    fun addObserver(observer: (Int) -> Unit) = observers.add(WeakReference(observer))

    fun removeObserver(observer: (Int) -> Unit) {
        observers.forEach {
            if (it.get() != null){
                if (observer == it.get()){
                    observers.remove(it)
                }
            }
        }
    }
}

interface EngineBindingsDelegate {
    fun onNext()
}