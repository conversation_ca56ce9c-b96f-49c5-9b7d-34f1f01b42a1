package com.joinutech.ddbes.flutter

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.joinutech.common.util.CacheHelper
import com.joinutech.common.util.ObjectStore
import com.joinutech.ddbes.flutter.mutiengine.EngineBindings
import com.joinutech.ddbeslibrary.utils.AppManager
import com.joinutech.ddbeslibrary.utils.toast
import com.joinutech.flutter.EventCheckTimeUpdate
import com.joinutech.flutter.EventCloseFlutterEngine
import com.joinutech.flutter.EventRefreshGroupInfo
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.plugin.common.MethodChannel
import me.jessyan.autosize.internal.CancelAdapt
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import timber.log.Timber

/**
 * @Des：
 * @author: moon
 * @date: 9/8/23
 */
const val MULTI_ENGINE_PAGE_ROUTE = "route_name"
class FlutterMultiActivity() : FlutterFragmentActivity() , CancelAdapt {

    var pointName:String? = null

    private val bindings: EngineBindings by lazy {
        val point = (ObjectStore.remove(MULTI_ENGINE_PAGE_ROUTE) as? String) ?: FlutterConstant.SPLASH_PAGE
        pointName = point
        EngineBindings(this , point, FlutterConstant.getCacheId(point))
    }

    companion object {

        fun open(context: Context , routeName: String = FlutterConstant.SPLASH_PAGE ) {

            ObjectStore.add(MULTI_ENGINE_PAGE_ROUTE , routeName)

            val intent = Intent(context , FlutterMultiActivity::class.java)
            intent.putExtra(MULTI_ENGINE_PAGE_ROUTE , routeName)
            context.startActivity(intent)
        }

        fun open(routeName: String = FlutterConstant.SPLASH_PAGE) {
            val stacks = AppManager.single_instance.getAllActivity()
            if (stacks.isEmpty()) return
            val act = stacks.last()

            ObjectStore.add(MULTI_ENGINE_PAGE_ROUTE , routeName)

            val intent = Intent(act , FlutterMultiActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK
            intent.putExtra(MULTI_ENGINE_PAGE_ROUTE , routeName)
            act.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
        AppManager.single_instance.addActivity(this)
        bindings.attach()
    }

    override fun onResume() {
        super.onResume()
        pointName?.let {
            Timber.i("flutter 当前的 currentRoute： $pointName")
            MethodChannelManager.currentRoute = pointName!!
        }

        EventBus.getDefault().post(EventCheckTimeUpdate())
    }

    override fun onPause() {
        super.onPause()
    }

    override fun provideFlutterEngine(context: Context): FlutterEngine? {
        return bindings.engine
    }


    @Subscribe
    fun closeCurrentEngine(b: EventCloseFlutterEngine) {
        Timber.i("closeCurrentEngine 当前的 currentRoute： $pointName , ${b.name}")
        if (b.name == pointName){

            MethodChannelManager.invokeFlutterWithBack(FlutterConstant.flutterBackPage , mapOf(), object :MethodChannel.Result{
                override fun success(result: Any?) {
                    destroyEngine()
                    finish()
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                }

                override fun notImplemented() {
                }
            })

        }
    }


    private var exitTime = 0L
    @SuppressLint("RtlHardcoded")
    override fun onBackPressed() {
        if (System.currentTimeMillis() - CacheHelper.globalBackListenerTime < 800) {
            CacheHelper.globalBackListenerTime = System.currentTimeMillis()
            return
        }

//        if (AppManager.single_instance.isOneMoreMainAct(FlutterHomeBaseActivity::class.java)){
//            AppManager.single_instance.finishTopActivity(FlutterHomeBaseActivity::class.java)
//        }

        val stacks = AppManager.single_instance.getAllActivity()
        if (stacks.size > 2){
            super.onBackPressed()
        }else{
            if (pointName == FlutterConstant.QR_CODE){
                finish()
            }
            Timber.i("onBackPressed:  flutter 当前的 currentRoute： $pointName")
            MethodChannelManager.invokeFlutterWithBack(FlutterConstant.flutterBackPage , mapOf(), object :MethodChannel.Result{
                override fun success(result: Any?) {
                    if(result == 1){
                        if (System.currentTimeMillis() - exitTime > 2000) {
                            toast(this@FlutterMultiActivity , "再按一次返回键退出")
                            exitTime = System.currentTimeMillis()
                        } else {
                            // 不关闭App，只是回到桌面
                            val launcherIntent = Intent(Intent.ACTION_MAIN)
                            launcherIntent.addCategory(Intent.CATEGORY_HOME)
                            startActivity(launcherIntent)
                        }
                    }
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                }

                override fun notImplemented() {
                }
            })
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
//        destroyEngine()
        bindings.detach()
        AppManager.single_instance.finishActivity(this)
    }


    private fun destroyEngine(){
        val entry_point = intent.getStringExtra(MULTI_ENGINE_PAGE_ROUTE) ?: FlutterConstant.SPLASH_PAGE
        val engine = FlutterEngineCache.getInstance().get(FlutterConstant.getCacheId(entry_point).toString())
        if (engine == null){
            Timber.i("Engine 没有了？")
        }else{
            engine.destroy()
        }
    }
}