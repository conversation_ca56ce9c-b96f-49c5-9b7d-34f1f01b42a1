package com.joinutech.ddbes.flutter

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ddbes.library.im.SessionUpgradeHelper
import com.ddbes.library.im.imtcp.imservice.msghelper.UnReadUtil
import com.ddbes.library.im.imtcp.tcpcacheutil.DisturbCacheHolder
import com.ddbes.library.im.netutil.ImNetUtil
import com.ddbes.library.im.netutil.retrofit.ImRetrofitUtil
import com.ddbes.library.im.push_parse.offlineRoute
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.bean.PersonInfoBean
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.PersonService
import com.joinutech.ddbeslibrary.utils.DeviceUtil
import com.joinutech.flutter.EventAcceptImData
import com.xiaomi.mipush.sdk.MiPushMessage
import com.xiaomi.mipush.sdk.PushMessageHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


/**
 * @Des：·
 * @author: moon
 * @date: 7/4/24
 */
class FlutterHomeViewModel: ViewModel() {

    fun fetchUserInfo() {

        viewModelScope.launch {
            PersonService.getPersonInfo(UserHolder.getAccessToken())
                .compose(ErrorTransformer.getInstance<PersonInfoBean>())
                .subscribe(object : BaseSubscriber<PersonInfoBean>() {
                    override fun onError(ex: ApiException) {
                    }

                    override fun onComplete() {}

                    override fun onNext(t: PersonInfoBean?) {
                        if (t == null) return
                        UserHolder.onLogin(t, true)
                    }
                })
        }
    }

    fun loadOfflineRoute(context: Context? ,intent: Intent) {
        if (!UserHolder.isLogin()) return
        delayExecute(3000) {
            val json = intent.offlineRoute(context)
//            json.offlineRoute(context)
            json.offline2Flutter(context)
        }
    }

    fun loadJPushRoute(context: Context,isReady:Boolean = false) {
//        val json = ObjectStore.remove("jpushData") as? String
        Log.d("test","========型号===${DeviceUtil.getSystem(context)}========")
        var json = when(DeviceUtil.getSystem(context)){
            DeviceUtil.HUAWEI ->{
                val uri = (context as FlutterHomeMultiActivity).intent.data
                val parseUri = Uri.parse(uri.toString().replace("#","%23").replace("&","%26"))
                parseUri.getQueryParameter("data")
            }
            DeviceUtil.XIAOMI ->{
                val pushMessage = (context as FlutterHomeMultiActivity)
                    .intent.getSerializableExtra(PushMessageHelper.KEY_MESSAGE)
                if (pushMessage != null ){
                    (pushMessage as MiPushMessage).extra["extra_data"]
                }else{
                    ""
                }

            }
            DeviceUtil.OPPO ->{
                val extras = (context as FlutterHomeMultiActivity).intent.extras
                Log.d("test","========oppo数据extra=====${(context as FlutterHomeMultiActivity).intent.extras}")
                Log.d("test","========oppo数据data=====${(context as FlutterHomeMultiActivity).intent.data}")
//                val builder = StringBuilder()
//                builder.append("{")
//                extras?.keySet()?.forEach { key->
//                   builder.append("\"$key\":${extras.getString(key)}")
//
//                }
//                builder.append("}")
//                builder.toString()
                extras?.get("jumpData").toString()

            }
            DeviceUtil.VIVO -> {
                Log.d("test","========vivo数据data=====${(context as FlutterHomeMultiActivity).intent.data}")
                val uri = (context as FlutterHomeMultiActivity).intent.data
                val parseUri = Uri.parse(uri.toString().replace("#","%23").replace("&","%26"))
                parseUri?.getQueryParameter("data")
            }
            else ->{
                ""
            }
        }
        Log.d("test","==========推送数据==${json}========")
        if (isReady){
            json.offline2Flutter(context)
        }else{
            (context as FlutterHomeMultiActivity).jumpData = json?:""
        }




    }


    fun JpushOnNewIntent(context: Context) {
//        FlutterMultiActivity.open(context , FlutterConstant.SPLASH_PAGE)
        loadJPushRoute(context, true)
    }

    // 延迟执行方法
    fun delayExecute(delayTime: Long = 2000 , execute: () -> Unit) {
        viewModelScope.launch {
            withContext(Dispatchers.Main){
                delay(delayTime)
                execute()
            }
        }
    }

    fun lazyLoadSessions(context: Context , eventImData : EventAcceptImData) {
        val userId = UserHolder.getUserId()
        if (userId.isNullOrEmpty()) return


        ImNetUtil.getImSessionId(userId ) { sessionId -> }

        viewModelScope.launch {
            withContext(Dispatchers.IO){

                if (eventImData.body == null) return@withContext
                val imtokenBean = eventImData.body!!

                BaseApplication.imToken = imtokenBean.token

                UserHolder.saveImTokenBean(imtokenBean)

                val imToken = BaseApplication.imToken

                SessionUpgradeHelper.loginIm(imToken)//开始登录im

//                loadSessions(context , userId , imToken)
            }
        }
    }

    suspend fun loadSessions(context: Context , userId: String,  imToken: String) {

        withContext(Dispatchers.IO){
            val reqParam = SessionUpgradeHelper.createSessionParam(context)
            val noticeParam = SessionUpgradeHelper.createSessionNoticeParam(context)

            val offlineChatDefer = async { ImRetrofitUtil.getOffLineSessionList( imToken, reqParam) }
            val offlineNoticeRespDefer = async { ImRetrofitUtil.getOffLineNoticeSessionList( imToken, noticeParam) }
            val singleDisturbRespDefer = async { ImRetrofitUtil.getSingleDisturbState( imToken) }
            val groupDisturbRespDefer = async { ImRetrofitUtil.getGroupDisturbState( imToken) }

            val offlineChatResp = offlineChatDefer.await()
            val offlineNoticeResp = offlineNoticeRespDefer.await()
            val singleDisturbResp = singleDisturbRespDefer.await()
            val groupDisturbResp = groupDisturbRespDefer.await()

            // 获取单聊/群聊免打扰状态
            if (singleDisturbResp.success()){
                DisturbCacheHolder.saveSingleDisturbIdList(singleDisturbResp.data ?: arrayListOf())
            }
            if (groupDisturbResp.success()){
                DisturbCacheHolder.saveGroupDisturbIdList(groupDisturbResp.data ?: arrayListOf())
            }

            if (offlineChatResp.success()){
                SessionUpgradeHelper.saveOfflineSessions(context, userId, offlineChatResp.data ?: arrayListOf())
            }

            if (offlineNoticeResp.success()){
                SessionUpgradeHelper.saveOfflineNoticeDb(context, userId, offlineNoticeResp.data ?: arrayListOf())
            }

            SessionUpgradeHelper.adapterDisturbConfig2Db(context, userId)

            UnReadUtil.sendUnReadCountChanged(context)
        }


    }


}

fun String?.offline2Flutter(context: Context?) {
    if (this.isNullOrEmpty()) return
    val bodJson = this
    MethodChannelManager.offlineNotificationToFlutter(bodJson)
//    EventBus.getDefault().post(EventOffline2Flutter(bodJson))

}