package com.joinutech.ddbes.flutter

import android.annotation.SuppressLint
import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.joinutech.ddbeslibrary.BuildConfig
import com.joinutech.ddbeslibrary.bean.AppStartResult
import com.joinutech.ddbeslibrary.utils.AppManager
import com.joinutech.ext.jackSonToMap
import com.joinutech.flutter.EventBackHandSignFile
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okio.ByteString
import org.json.JSONException
import org.json.JSONObject
import timber.log.Timber

/**
 * @Des：
 * @author: moon
 * @date: 9/12/23
 */
@SuppressLint("StaticFieldLeak")  // todo context 泄露会提示
object MethodChannelManager : MethodChannel.MethodCallHandler {

    var currentRoute:String = FlutterConstant.SPLASH_PAGE

    val map: HashMap<String,MethodChannel?> = hashMapOf()


//    var channel: MethodChannel? = null

    var mContext : Context? = null

    fun createMethod(path:String , channel: MethodChannel){
        map[path] = channel
    }

    fun channel() :MethodChannel? {
       return map[currentRoute]
    }

    fun mainChannel(): MethodChannel? {
        return map[FlutterConstant.SPLASH_PAGE]
    }

    fun init(context: Context) {
        this.mContext = context
        channel()?.setMethodCallHandler(this)
    }

    fun invokeFlutterWithBack(method: String, params : Map<String,Any> = mapOf() , callBack: MethodChannel.Result) {
        channel()?.invokeMethod(method , params , callBack)
    }

    fun invokeFlutter(method: String, params : Map<String,Any> = mapOf()) {
        GlobalScope.launch {
            withContext(Dispatchers.Main){
                channel()?.invokeMethod(method , params)
            }
        }
    }

    fun invokeFlutterWithCallBack(method: String, params : Map<String,Any> = mapOf() , callBack:(Any?) -> Unit) {
        GlobalScope.launch {
            withContext(Dispatchers.Main){
                channel()?.invokeMethod(method , params , object : MethodChannel.Result {
                    override fun success(result: Any?) {
                        callBack.invoke(result)
                    }

                    override fun error(
                        errorCode: String,
                        errorMessage: String?,
                        errorDetails: Any?
                    ) {
                    }

                    override fun notImplemented() {
                    }
                })
            }
        }
    }

    fun invokeMainEngineFlutter(method: String, params : Map<String,Any?> = mapOf()) {
        GlobalScope.launch {
            withContext(Dispatchers.Main){
                mainChannel()?.invokeMethod(method , params)
            }
        }
    }


    fun invokeMainEngineFlutterByObj(method: String, params : Any?) {
        GlobalScope.launch {
            withContext(Dispatchers.Main){
                mainChannel()?.invokeMethod(method , params)
            }
        }
    }

    fun loginOut() {
        invokeFlutter(FlutterConstant.loginOut)
    }

    fun refreshFriendApplyArguments(route:String = FlutterConstant.FRIEND_APPLY_DETAIL,  map: Map<String,Any>) {
        invokeFlutter(FlutterConstant.nativeJumpArgument , mapOf(
            "route" to route,
            "arguments" to map,
        ))
    }

    fun openFlutterPage(route: String , map: Map<String, Any>) {
        invokeFlutter(FlutterConstant.OPEN_FLUTTER , mapOf(
            "route" to route,
            "arguments" to map
        ))
    }

    fun translateByte(data: ByteArray?) {
        if (data == null) return
        invokeFlutter(FlutterConstant.FLUTTER_SOCKET_BYTE , mapOf(
            "data" to data
        ))
    }



    fun refreshCommonArguments(route:String = FlutterConstant.FRIEND_APPLY_DETAIL,  map: Object) {
        invokeFlutter(FlutterConstant.nativeJumpArgument , mapOf(
            "route" to route,
            "arguments" to map ,
        ))
    }

    fun refreshUserInfo(userId: String , type:Int , companyId : String = "") {
        invokeFlutter(FlutterConstant.nativeJumpArgument , mapOf(
            "route" to FlutterConstant.USER_INFO,
            "arguments" to mapOf(
                "userId" to userId,
                "type" to type,
                "companyId" to companyId,
            ),
        ))
    }

    // type 0 二维码， 1 详情
    fun toRtcMeetingDetail(meetingId: String , type:Int) {
        invokeFlutter(FlutterConstant.nativeJumpArgument , mapOf(
            "route" to FlutterConstant.RTC_MEETING_DETAIL,
            "arguments" to mapOf(
                "meetingId" to meetingId,
                "type" to type,
            ),
        ))
    }

    fun toApproveDetail(routePath: String , approveId: String , orgId: String?) {
        invokeFlutter(FlutterConstant.nativeJumpArgument , mapOf(
            "route" to routePath,
            "arguments" to mapOf(
                "approveId" to approveId,
                "orgId" to orgId,
            ),
        ))
    }

    fun groupNameRoute( groupId: String? , groupName: String? , groupLogo: String?) {
        invokeFlutter(FlutterConstant.nativeJumpArgument , mapOf(
            "route" to "/group-change-info",
            "arguments" to mapOf(
                "groupId" to groupId,
                "groupName" to groupName,
                "groupLogo" to groupLogo,
            ),
        ))
    }

    fun toApproveList(routePath: String , listType: Int , orgId: String?) {
        invokeFlutter(FlutterConstant.nativeJumpArgument , mapOf(
            "route" to routePath,
            "arguments" to mapOf(
                "listType" to listType,
                "orgId" to orgId,
            ),
        ))
    }

    //
    fun callHandSigature(body: EventBackHandSignFile) {
        invokeFlutter(FlutterConstant.backSignatrueBase64 , mapOf(
            "approveId" to body.approveId,
            "fileInfo" to mapOf(
                "filePath" to body.file.absolutePath,
                "fileName" to body.file.name,
            )
        ))
    }

    fun passSixMergeData(app: AppStartResult?) {
        if (app == null) return

        val banners = app.bannerList.map {banner ->
            mapOf(
                "picture" to (banner.picture ?:""),
                "url" to (banner.url ?:""),
                "name" to (banner.name ?:""),
            )
        }

        val map = mapOf(
            "pre" to app.pre,
            "bannerList" to banners,
            "versionName" to BuildConfig.VERSION_NAME,
            "desc" to app.currentDesc,
            "auditing" to (app.auditing ?:1), //** 0审核中 1审核通过 **/
            "status" to app.status,  /** 0是代表是当前版本 1是强制更新 2是非强制更新 **/

        )

        invokeFlutter(FlutterConstant.passSixMergeData , mapOf(
            "data" to map
        ))
    }

    fun refreshUserData() {
        invokeFlutter(FlutterConstant.refreshUserData)
    }

    fun refreshCompanyData() {
        invokeFlutter(FlutterConstant.refreshOrgData)
    }

    fun refreshGroupList(){
        AppManager.single_instance.getAllActivity()?.last()?.let {
            it.runOnUiThread {
                invokeMainEngineFlutter(FlutterConstant.refreshGroupData)
            }
        }
    }

    fun refreshFriendList() {
        invokeMainEngineFlutter(FlutterConstant.refreshFriendData)
    }

    fun refreshContactDot() {
        invokeMainEngineFlutter(FlutterConstant.refreshContactPendingData)
    }

    fun refreshUnreadMsg(unread: Int , type: Int) {
        invokeFlutter(FlutterConstant.unReadMsgCountUpdate , mapOf(
            "type" to type,
            "count" to unread
        ))
    }

    fun clearFlutterCache() {
        invokeFlutter(FlutterConstant.clearCache)
    }

    fun showFlutterLogout(msg: String?) {
        invokeMainEngineFlutter(FlutterConstant.tokenError , mapOf(
            "msg" to (msg ?:"")
        ))
    }

    fun disMissroom(meetingId: String?) {
        if (meetingId == null) return
        invokeFlutter(FlutterConstant.receiveMeetingDeleted , mapOf(
            "meetingId" to meetingId
        ))
    }

    fun linkEnterMeeting(meetingId: String) {
        Timber.i("调用 flutter方法 enterMeeting： $meetingId")
        invokeFlutter(FlutterConstant.enterMeeting , mapOf(
            "meetingId" to meetingId
        ))
    }

    fun closeFlutterPage() {
        Timber.i("调用 flutter方法 ： CLOSE_WEB_VIEW")
        invokeFlutter(FlutterConstant.CLOSE_WEB_VIEW)
    }

//    fun reOpenFlutterPage() {
//        Timber.i("调用 flutter方法 ： reOpenFlutterPage")
//        invokeFlutter(FlutterConstant.REOPEN_WEB_VIEW)
//    }

    fun translateTiandiTuParam(json: String) {
        val map = jsonToMap(json)
        Timber.i("translateTiandiTuParam = $map")
        invokeFlutter(FlutterConstant.GET_TIANDITU_POSITION , map)
    }

    // 通知Flutter 刷新审批红点
    fun refreshApproveRedDot(params: HashMap<String, Any> = hashMapOf()) {
        invokeMainEngineFlutter(FlutterConstant.refreshApproveUnreadData , params)
    }

    fun refreshWorkRedDot(params: HashMap<String, Any> = hashMapOf()) {
        invokeMainEngineFlutter(FlutterConstant.refreshWorkRedPoint, params)
    }

    fun forceFresh() {
        invokeMainEngineFlutter(FlutterConstant.FORCE_REFRESH_WORKBENCH)
    }

    fun offlineNotificationToFlutter(json: String) {
        val param = json2Map(json)
        if (param == null){
            Timber.i("offlineNotificationToFlutter 1: $json")
            return
        }
        Timber.i("offlineNotificationToFlutter 2: $param")

        invokeMainEngineFlutter(FlutterConstant.CHANNEL_OFFLINE_NOTIFICATION_ROUTE , param.jackSonToMap())
    }

    //收到IM刷新审批红点等数据的通知 sessionType 9000(刷新类,不计离线)  msgType:9000 刷新审批红点 9001全部审批 9002待办(同时刷已办)   9003已办 9004已发起 9005抄送
    fun refreshDataWithType(msgType: Int , dataP: String) {
        invokeFlutter(FlutterConstant.refreshDataWithType , mapOf(
            "msgType" to msgType,
            "data" to dataP
        ))
    }

    fun alertSessionListCreateDialog(offsetY : Float) {
        invokeFlutter(FlutterConstant.newsHomeShowMemu , mapOf(
            "offsetY" to offsetY
        ))
    }

    // native 主动调用接口
    fun fetchImData() {
        Timber.i("执行 ==》 fetchImData......")
        invokeFlutter(FlutterConstant.refreshIMData , mapOf(
            "loginInt" to 1
        ))
    }

    fun deleteKingdeeApprove(data: String?) {
        val map = jsonToMap(data ?:"")
        Timber.i("deleteKingdeeApprove ==> $map")
        invokeMainEngineFlutter(FlutterConstant.DELETE_KINGDEEAPPROVE , map)
    }


    fun jsonToMap(jsonString: String): Map<String, String> {
        val type = object : TypeToken<Map<String, String>>() {}.type
        return Gson().fromJson(jsonString, type)  // 非空 String 类型自动过滤 null[1](@ref)
    }


    fun sendFlutterImData(map: Map<String, Any?>) {
        invokeMainEngineFlutter(FlutterConstant.CHANNEL_SEND_IM_MSG , map)
    }

    fun channelRefreshMeetingCheck(sessionId: String) {
        val map = HashMap<String, Any>()
        map.put("sessionId", sessionId)
        invokeMainEngineFlutter(FlutterConstant.CHANNEL_REFRESH_MEETING_CHECK , map)
    }



    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        if (mContext == null) return
        dispatchMethodCall(mContext!! , call , result)
    }


    fun json2Map(jsonString: String?): HashMap<String, Any>? {
        val jsonObject: JSONObject
        try {
            jsonObject = JSONObject(jsonString)
            val keyIter: Iterator<String> = jsonObject.keys()
            var key: String
            var value: Any
            var valueMap = HashMap<String, Any>()
            while (keyIter.hasNext()) {
                key = keyIter.next()
                value = jsonObject[key] as Any
                valueMap[key] = value
            }
            return valueMap
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        return null
    }


}