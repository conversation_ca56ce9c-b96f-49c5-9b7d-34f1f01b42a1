package com.joinutech.ddbes.flutter

import android.content.Context
import android.content.Intent
import com.joinutech.addressbook.OrgServiceImpl
import com.joinutech.common.storage.CosService
import com.joinutech.common.storage.FileStorage
import com.joinutech.common.storage.FileUpTransferManager
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.bean.TencentSessionBean
import com.joinutech.ddbeslibrary.bean.UploadFileBean
import com.joinutech.ddbeslibrary.bean.WXAccessTokenEntity
import com.joinutech.ddbeslibrary.bean.WXUserInfo
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.UploadFileService
import com.joinutech.ddbeslibrary.utils.APP_ID_WX
import com.joinutech.ddbeslibrary.utils.APP_SECRET_WX
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.FileUploadUtil
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.Loggerr
import com.joinutech.ddbeslibrary.utils.MyCredentialProvider
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.TosFileType
import com.joinutech.ddbeslibrary.utils.toast
import com.joinutech.flutter.EventBindWeChat
import com.marktoo.lib.cachedweb.LogUtil
import com.zhy.http.okhttp.OkHttpUtils
import com.zhy.http.okhttp.callback.StringCallback
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import okhttp3.Call
import org.greenrobot.eventbus.EventBus
import timber.log.Timber

/**
 * @Des：
 * @author: moon
 * @date: 9/7/23
 */
object FlutterHelper {

    // 初始化 flutter application
    fun initFlutter(context: Context) {
//        FlutterMain.startInitialization(context)
//        preLoadFirstEngine(context)

//        QtFlutterEngine.init(context ,FlutterConstant.SPLASH_PAGE)
//        ApprovalFlutterEngine.init(context ,FlutterConstant.APPROVE_DETAIL)

    }

    fun openFlutterRootAct(context: Context) {
        val i = Intent(context , FlutterHomeMultiActivity::class.java)
        context.startActivity(i)
    }

    fun getKingdeeToken(context: Context , result: MethodChannel.Result) {
        LoadingManager.showLoadingDialog(context)
        OrgServiceImpl().getKingDeeToken(
            CompanyHolder.getCurrentOrg()?.companyId ?: "" ,
            UserHolder.getCurrentUser()?.userId ?: ""){ accessToken ->
            LoadingManager.dismissLoadingDialog()
            result.success(mapOf(
                "token" to accessToken
            ))
        }
    }

    fun cosUploadFiles(context: Context, call: MethodCall, resultChannel: MethodChannel.Result) {
        val type = (call.argument("type") as? Int) ?: 0  // 0: 审批附件上传；  1 ：企业logo
        val fileList = (call.argument("fileList") as? ArrayList<Any>) ?: arrayListOf()  // 0: 审批附件上传；  1 ：企业logo

        fileList.forEach {
            val fileId= ((it as? Map<String,Any>)?.get("cosObject") as? String) ?:""
            val filePath= ((it as? Map<String,Any>)?.get("filePath") as? String) ?:""
            Timber.i("${fileId} --- ${filePath}")
        }

        val tosFileType = if (type == 0) TosFileType.PAN else TosFileType.LOGO

        UploadFileService.getPanTencentSessionV2()
            .compose(ErrorTransformer.getInstance<TencentSessionBean>())
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(object : BaseSubscriber<TencentSessionBean>() {
                override fun onError(ex: ApiException) {
                    toast(context , ex.message)
                    ex.printStackTrace()
                }

                override fun onComplete() {
                }

                override fun onNext(result: TencentSessionBean) {
                    FileStorage.cosSession = result
                    val provider = MyCredentialProvider(result.credentials.tmpSecretId
                        , result.credentials.tmpSecretKey, result.credentials.sessionToken, result.expiredTime)

                    val service = getCosService(provider)
                    val bucket = when(type){
                        0 -> result.panBucket
                        1 -> result.openBucket
                        else -> result.panBucket
                    }

                    val uploadList = fileList.map {
                        val fileId= ((it as? Map<String,Any>)?.get("cosObject") as? String) ?:""
                        val filePath= ((it as? Map<String,Any>)?.get("filePath") as? String) ?:""

                        UploadFileBean(
                            fileId = fileId, fileUrl = filePath ,
                            bucket = bucket ?:"",
                        )
                    }
                    FileUploadUtil.uploadMultiFileWithProgress(onProgress = {
                        _,_,_,_ ->
                    } , onSuccess = { uploadResult ->
                        if (!uploadResult.isNullOrEmpty()) {
                            // 文件上传结果转型为map，key为本地文件路径
                            val map = uploadResult.map { file -> file.filePath to file }.toMap()
                            if (!map.isNullOrEmpty()) {
                                // 上传文件结果返回
                                uploadList.forEach { file ->
                                    if (map.containsKey(file.fileUrl)) {
                                        file.isUploadFlag = true
                                    }
                                }

                                val resultList = uploadList.filter { it.isUploadFlag }.map { it.fileId }
                                val resultMap = hashMapOf<String,Any>()
                                resultMap.put("list" , resultList)
                                resultChannel.success(resultMap)
                            }

                        } else {
                            LogUtil.showLog("无图片上传成功")
                        }

                    } , onError = {

                    } , tosFileType = tosFileType , hashList = ArrayList(uploadList) , service)

                }
            })
    }

    private fun getCosService(qCloudCredentialProvider: MyCredentialProvider): CosService? {
        FileStorage.showLog("PanService 初始化")
        val t = FileStorage.cosSession ?: return null
        val secretId = t.credentials.tmpSecretId
        val secretKey = t.credentials.tmpSecretKey
        val sessionToken = t.credentials.sessionToken
        val expiredTime: Long = t.expiredTime//临时密钥有效截止时间戳
        //初始化 config
        val serviceConfig = FileStorage.getTencentCloudConfig() ?: return null
        //使用临时密钥初始化QCloudCredentialProvider
//                val qCloudCredentialProvider = MyCredentialProvider(secretId, secretKey, sessionToken, expiredTime)
        //初始化CosXmlService
        FileUpTransferManager.instanceCosService = CosService(BaseApplication.joinuTechContext, serviceConfig, qCloudCredentialProvider)
        return FileUpTransferManager.instanceCosService
    }

    fun settingWechatGetCode(code: String) {
        OkHttpUtils.get().url("https://api.weixin.qq.com/sns/oauth2/access_token")
            .addParams("appid", APP_ID_WX)
            .addParams("secret", APP_SECRET_WX)
            .addParams("code", code)
            .addParams("grant_type", "authorization_code")
            .build()
            .execute(object : StringCallback() {
                override fun onResponse(response: String?, id: Int) {
                    Timber.i("==使用上一步的code====微信返回的access_token==${response}")
                    val accessTokenEntity = GsonUtil.fromJson(response, WXAccessTokenEntity::class.java)
                    if (accessTokenEntity != null) {
                        getUserInfo(accessTokenEntity)
                    } else {
//                        logShow("获取失败")
                    }
                }

                override fun onError(p0: Call?, p1: Exception?, p2: Int) {
//                    logShow("请求错误")
                }

            })
    }



    private fun getUserInfo(accessTokenEntity: WXAccessTokenEntity) {
        OkHttpUtils.get()
            .url("https://api.weixin.qq.com/sns/userinfo")
            .addParams("access_token", accessTokenEntity.access_token)
            .addParams("openid", accessTokenEntity.openid)//openid:授权用户唯一标识
            .build()
            .execute(object : StringCallback() {
                override fun onResponse(response: String?, id: Int) {
                    Timber.i("==使用上一步的access_token====微信返回的用户信息==${response}")
                    val wxResponse: WXUserInfo? = GsonUtil.fromJson(response, WXUserInfo::class.java)
                    var nickName = ""
                    if (wxResponse != null) {
                        if (StringUtils.isNotBlankAndEmpty(wxResponse.nickname)) {
                            nickName = wxResponse.nickname!!
                        }
                        EventBus.getDefault().post(EventBindWeChat(json = "${wxResponse.openid}:${wxResponse.unionid}:$nickName"))

                        EventBusUtils.sendEvent(
                            EventBusEvent(
                            EventBusAction.BIND_BY_WECHAT, "${wxResponse.openid}:${wxResponse.unionid}:$nickName")
                        )
                    }

                }

                override fun onError(p0: Call?, p1: Exception?, p2: Int) {
//                    logShow("请求错误")
                }

            })
    }

}