package com.joinutech.ddbes.flutter

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.util.Log
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.FragmentActivity
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.lib.vc.service.VcServiceImpl
import com.ddbes.lib.vc.view.activity.chat.ChatControlActivity
import com.ddbes.library.im.ImService
import com.ddbes.library.im.bean.AppFriendBean
import com.ddbes.library.im.imtcp.ConstantEventCodeUtil
import com.ddbes.library.im.imtcp.ConstantImMsgType
import com.ddbes.library.im.imtcp.Logger
import com.ddbes.library.im.imtcp.dbbean.GroupInfoDbBean
import com.ddbes.library.im.imtcp.dbbean.Message
import com.ddbes.library.im.imtcp.dbope.SessionDaoOpe
import com.ddbes.library.im.imtcp.imservice.OkWebSocketService
import com.ddbes.library.im.imtcp.imservice.audiovideohelper.AudioVideoReceiveUtil
import com.ddbes.library.im.imtcp.notice.NoticeUtil
import com.ddbes.library.im.netutil.imclientutil.ImOffLineRetrofitClient
import com.ddbes.library.im.netutil.imclientutil.ImRetrofitClient
import com.ddbes.library.im.util.FriendCacheHolder
import com.ddbes.library.im.util.GroupCacheHolder
import com.google.gson.reflect.TypeToken
import com.joinu.rtcmeeting.RtcManager
import com.joinutech.addressbook.view.SelectWithSearchListWithBottomShowActivity2
import com.joinutech.approval.AprSignatureActivity
import com.joinutech.approval.R
import com.joinutech.approval.SCAN_SIGNATURE
import com.joinutech.approval.aprhistory.Codes
import com.joinutech.approval.utils.AutoLocationUtil
import com.joinutech.common.helper.LoginHelper
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.common.startContractForResult
import com.joinutech.common.storage.CosService
import com.joinutech.common.storage.FileStorage
import com.joinutech.common.storage.FileUpTransferManager
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.ObjectStore
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbes.flutter.model.convertTokenAndUserInfo
import com.joinutech.ddbes.flutter.webview.DDWebViewiewFactory
import com.joinutech.ddbes.flutter.webview.webViewType
import com.joinutech.ddbes.flutter.webview.webViewType1
import com.joinutech.ddbes.statics.StaticsHelper
import com.joinutech.ddbeslibrary.BuildConfig
import com.joinutech.ddbeslibrary.VersionConfig
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.bean.AppStartResult
import com.joinutech.ddbeslibrary.bean.CompanyListBean
import com.joinutech.ddbeslibrary.bean.FriendSelectBean
import com.joinutech.ddbeslibrary.bean.GroupInfoBean
import com.joinutech.ddbeslibrary.bean.TencentSessionBean
import com.joinutech.ddbeslibrary.imbean.ImTokenBean
import com.joinutech.ddbeslibrary.imbean.MultilSynchronizeMsgBean
import com.joinutech.ddbeslibrary.request.CommonResult
import com.joinutech.ddbeslibrary.request.RetrofitClient
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.request.exception.ErrorType
import com.joinutech.ddbeslibrary.service.LocationResult
import com.joinutech.ddbeslibrary.service.UploadFileService
import com.joinutech.ddbeslibrary.utils.AppManager
import com.joinutech.ddbeslibrary.utils.ConsKeys
import com.joinutech.ddbeslibrary.utils.ConsValue
import com.joinutech.ddbeslibrary.utils.CosWrapperV2
import com.joinutech.ddbeslibrary.utils.DeviceIdUtil
import com.joinutech.ddbeslibrary.utils.DeviceUtil
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.MMKVKEY
import com.joinutech.ddbeslibrary.utils.MMKVUtil
import com.joinutech.ddbeslibrary.utils.MyCredentialProvider
import com.joinutech.ddbeslibrary.utils.MyDialog
import com.joinutech.ddbeslibrary.utils.PermissionUtils
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.RouteProvider
import com.joinutech.ddbeslibrary.utils.RouteVc
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.SystemHelper
import com.joinutech.ddbeslibrary.utils.XUtil
import com.joinutech.ddbeslibrary.utils.toast
import com.joinutech.ext.toObject
import com.joinutech.flutter.EventAboutCheckTimeUpdate
import com.joinutech.flutter.EventAccept6in1Data
import com.joinutech.flutter.EventAcceptImData
import com.joinutech.flutter.EventCloseFlutterEngine
import com.joinutech.flutter.EventCloseWeb
import com.joinutech.flutter.EventHomeReady
import com.joinutech.flutter.EventReOpenWeb
import com.joinutech.flutter.EventRefreshGroupInfo
import com.joinutech.flutter.EventRequestPermission
import com.joinutech.flutter.EventResetLoginIM
import com.joinutech.flutter.EventStartGroupChat
import com.joinutech.flutter.EventTakeMealSuccess
import com.joinutech.flutter.EventToRecord
import com.joinutech.flutter.EventUpdateSession
import com.joinutech.flutter.EventWebViewGoBack
import com.joinutech.flutter.TranslateData
import com.joinutech.message.view.IMLocationDetailActivity
import com.joinutech.message.view.tcpimpages.dobean.createForwardPerson
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject
import timber.log.Timber
import java.io.Serializable

/**
 * @Des：
 * @author: moon
 * @date: 9/8/23
 */
class DDbesFlutterPlugin(context: Context) : FlutterPlugin , MethodChannel.MethodCallHandler {

    lateinit var mContext: Context

    init {
        this.mContext = context
    }

    private lateinit var channel: MethodChannel

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
//        channel = MethodChannel(flutterPluginBinding.binaryMessenger, FlutterConstant.methodChannel)
//        channel.setMethodCallHandler(this)

//        MethodChannelManager.init(mContext)
//        MethodChannelManager.channel = channel

        flutterPluginBinding.platformViewRegistry
            .registerViewFactory(
                webViewType, DDWebViewiewFactory(mContext , flutterPluginBinding.binaryMessenger))


//        flutterPluginBinding.platformViewRegistry
//            .registerViewFactory(
//                webViewType1, DDWebViewiewFactory(mContext , flutterPluginBinding.binaryMessenger))




    }

    companion object {
    }

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        Timber.i("onMethodCall.... $call")
        dispatchMethodCall(mContext , call , result)
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
//        channel.setMethodCallHandler(null)
    }
}

fun dispatchMethodCall(mContext: Context , call: MethodCall, result: MethodChannel.Result) {
    Timber.i("dispatchMethodCall ===> ${call.method} , ${call.arguments}")
    when(call.method){
        FlutterConstant.DISCONNECT_SOCKET -> {
            // 主动断开连接
            ImService.doLogout()
        }

        FlutterConstant.START_WEB_SOCKET -> {
//            val imToken = call.argument("imToken") as? String?
//            val url = call.argument("url") as? String?
//            UserHolder.storeFlutterSocketParam(imToken , url)
            ImService.tryConnect()
        }

        FlutterConstant.FLUTTER_SEND_SOCKET_DATA -> {
            try {
                val data = call.argument<ByteArray>("data")
                OkWebSocketService.sendData(data!!)
            }catch (e: Exception){
                e.printStackTrace()
            }

        }

        FlutterConstant.REOPEN_WEB_VIEW -> {
            val msg = (call.arguments as HashMap<String,Any>)
            Timber.i("REOPEN_WEB_VIEW ===> $msg")
            EventBus.getDefault().post(EventReOpenWeb(msg))
        }

        "webviewGoBack" -> {
            EventBus.getDefault().post(EventWebViewGoBack())
        }

        FlutterConstant.FLUTTER_HOME_ONREADY ->{
            //点击离线推送后等待homecontroller初始化
            EventBus.getDefault().post(EventHomeReady())
        }
        FlutterConstant.login -> {
            // 登录后调用，原生保存
            // {birthday=2022-09-14, profession=证券经理/总监, address=北京市东城区, gender=1, openId=, nickName=花开七步, mobile=18513024241, avatar=https://cdn.ddbes.com/HEAD/BloRIwNJsRwK0l20hJ1640763850772.png, token_type=bearer, userId=2468510354695853053, imId=20tJ6rO9TRx, access_token=3665d87f-7b5c-4a6d-b304-a1e7be805887, refresh_token=881559bf-72fb-4b3f-8f0b-3415f042adf8, companyId=2468597963438425084, scope=all, intro=, name=iOS孙超, expires_in=2591999, email=<EMAIL>}
            val tokenDic = call.arguments
            Timber.i("login ------ > $tokenDic")

            val body = convertTokenAndUserInfo(call)
            UserHolder.saveToken(body.tokenBean)
            UserHolder.onLogin(body.person , true)
            EventBus.getDefault().post(EventResetLoginIM())
        }

        FlutterConstant.minePageJumpNext -> {
            val itemIndex = call.argument("index") as Int?
            // 0 个人资料；  1 二维码；  2 设置；  3 关于
            when(itemIndex){
                0 -> FlutterRouter.routePersonProfile(mContext)
//                1 -> {  //  FLUTTER 实现的
//                }
                2 -> FlutterRouter.routeSettingPage(mContext)
                3 -> FlutterRouter.routeAboutPage(mContext)
            }
        }

        //  @Deprecated("弃用")
        FlutterConstant.jumpSingleChat -> {
            // to 单聊
            FlutterRouter.routeSingleChatPage(mContext , call)
        }

        //  @Deprecated("弃用")
        FlutterConstant.jumpGroupChat -> {
            // 创建群组后进入群聊页面
            FlutterRouter.routeGroupChatPage(mContext , call)
        }

        //  @Deprecated("弃用")
        FlutterConstant.jumpChooseIM -> {
            FlutterRouter.routeInviteIM(mContext , call)
        }

        //  @Deprecated("弃用")
        FlutterConstant.jumpIMSearch -> {
            FlutterRouter.jumpImSearch(mContext)
        }

        FlutterConstant.FlutterLogOut -> {
            FlutterRouter.flutterLogOut(mContext)
        }

        //  @Deprecated("弃用")
        FlutterConstant.ContactJumpAuthSetting -> {
            FlutterRouter.routeOrgPermissionPage(mContext , call)
        }

        FlutterConstant.openWebview -> {
            FlutterRouter.openNativeWebView(mContext , call)
        }

        FlutterConstant.openAttendancePage -> {
            FlutterRouter.routeAttendancePage(mContext ,call)
        }

        FlutterConstant.openAttendanceSettingPage -> {
            FlutterRouter.routeAttendanceSettingPage(mContext ,call)
        }

        FlutterConstant.openAnnounce -> {
            FlutterRouter.routeAnnouncement(mContext ,call)
        }

        FlutterConstant.call_native_backPage -> {
            val stacks = AppManager.single_instance.getAllActivity()
            if (stacks.size <=2) return
            stacks.last().finish()
        }

        FlutterConstant.openNativeSignature -> {
            // 手写签名
            val approveId = (call.argument("approveId") as? String) ?: ""

            val intent = Intent(mContext, AprSignatureActivity::class.java)
            intent.putExtra("approveId" , approveId)
            intent.putExtra("action","for_signature")
            (mContext as? Activity)?.startActivityForResult(intent, Codes.SIGN_REQUEST)
        }

        FlutterConstant.getNativeLocation -> {

            (mContext as? FragmentActivity)?.runOnUiThread {
                AutoLocationUtil.autoLocation(mContext as FragmentActivity , object :AutoLocationUtil.LocationListener{
                    override fun location(l: LocationResult?) {
                        val param = mapOf<String, Any?>(
                            "address" to (l?.address ?:""),
                            "positioning" to (l?.name ?:""),
                            "latitude" to (l?.lat ?: 0.0),
                            "longitude" to (l?.lng ?: 0.0),
                        )
                        Timber.i(">>>>>>>>>>>>>> 自动定位 $param")
                        result.success(param)
                    }
                })
            }
        }

        // 由百度地图改为 天地图
        FlutterConstant.openNativeLocationPage -> {



            val intent = Intent(mContext , IMLocationDetailActivity::class.java)
            intent.putExtra("type", "selectLocation")
            intent.putExtra("title", "选择地点")
            intent.putExtra("rightTitle", "完成")

            (mContext as FragmentActivity).startContractForResult(ActivityResultContracts.StartActivityForResult(),
                intent) {data ->
                run {
                    data.data?.let {
                        val latitude = it.getDoubleExtra("latitude" , 0.0)
                        val longitude = it.getDoubleExtra("longitude" , 0.0)
                        val title = it.getStringExtra("title") ?: ""
                        val address = it.getStringExtra("address") ?: ""
                        val uri = it.getStringExtra("uri") ?: ""

                        val param = mapOf<String, Any?>(
                            "address" to address,
                            "positioning" to title,
                            "latitude" to latitude,
                            "longitude" to longitude,
                            "uri" to uri,
                        )
                        Timber.i("$param")
                        result.success(param)
                    }
                }

            }
        }

        FlutterConstant.channel_open_map -> {
            // flutter 携带位置参数进入native 地图显示 位置和导航等
            try {
                val lat = (call.argument("lat") as? Double) ?: 0.0
                val lng = (call.argument("lon") as? Double) ?: 0.0
                val poiName = (call.argument("poiName") as? String) ?: ""
                val poiAddress = (call.argument("poiAddress") as? String) ?: ""
                Timber.i("channel_open_map => $lat , $lng , $poiAddress , $poiName")
                val intent = Intent(mContext, IMLocationDetailActivity::class.java)
                intent.putExtra("lat", lat)
                intent.putExtra("lng", lng)
                intent.putExtra("poiName", poiName)
                intent.putExtra("poiAddress", poiAddress)
                intent.putExtra("type", "")
                mContext.startActivity(intent)

            }catch (e: Exception){
                e.printStackTrace()
            }
        }

        // 发起音视频
        FlutterConstant.nativePageimVideo -> {
            // 打开原生的音视频
            /// Map<String, dynamic> map = {
            //      'sessionType': session.sessionType, //会话类型
            //      'type': type, //  1语音2视频 3 加入
            //      'userId': ownerId, //自己的userId,
            //      'appChatId': session.appChatId, //单聊为对方的userId,群组为groupId
            //      ’groupInfo‘ ：   GroupItemResp
            //    };

            try {

                Timber.i("nativePageimVideo ==> ${call.arguments}")

                val isVoice : Boolean = (call.argument("type") ?: 1) == 1
                val sessionType = call.argument("sessionType") ?: 1
                val userId = call.argument("userId") ?: ""  // 自己的id
                val appChatId = call.argument("appChatId") ?: ""   //单聊为对方的userId,群组为groupId
                val groupInfo = call.argument("groupInfo") ?: HashMap<String,Any>()   // GroupItemResp
                val targetName = call.argument("targetName") ?: ""   //
                val targetLogo = call.argument("targetLogo") ?: ""   //
                val sessionId = call.argument("sessionId") ?: ""   //
                val type = call.argument("type") ?: 0   //
                val meetingId = call.argument("meetingId") ?: ""   // meetingId 只是担当业务需要，没有这个 meetingId 需要创建，有了直接加入
                val roomId = call.argument("roomId") ?: 0

                val topAct = AppManager.single_instance.currentActivity()

                val isGroup = sessionType == 2
                if (!isGroup){
                    (topAct as? Activity)?.let {
                        PermissionUtils.checkNetTypePermission(topAct , callBack = {
                            UserHolder.saveUserName(appChatId, targetName)
                            UserHolder.saveUserLogo(appChatId, targetLogo)
                            UserHolder.saveSessionId(appChatId, sessionId)
                            Logger.i("--执行---", "-----开启语音视频通话----")


                            // 进入 ChatControlActivity 页面
                            ARouter.getInstance().build(RouteVc.VC_CREATE_CALL)//===创建通话
                                .withInt("callType", (call.argument("type") ?: 1))
                                .withStringArrayList("inviteUserIds", arrayListOf(appChatId))
                                .navigation()
                        })
                    }
                }else{
                    val groupInfoBody = groupInfo.toObject<GroupInfoBean>()
//                    val groupInfoBody = GsonUtil.fromJson<GroupInfoBean>(groupInfo)

                    val noSupportUserIds = arrayListOf<String>()

                    val selectedUserIds = arrayListOf<String>()
                    val a  = groupInfoBody?.users?.filter {
                        it.id == userId
                    }?.map { it.id } ?: arrayListOf()
                    selectedUserIds.addAll(a)

                    val outPersons = groupInfoBody?.users?.map { m ->
                        FriendSelectBean().let {
                            it.userId = m.id
                            it.name = m.name
                            it.avatar = m.headimg
                            it
                        }
                    } ?: arrayListOf()

                    if(!StringUtils.isEmpty(meetingId)){
                        // 有了直接加入

                        val topAct = AppManager.single_instance.currentActivity()
                        joinGroupCall(topAct as FragmentActivity,
                            meetingId,
//                            appChatId,
                            userId,
                            sessionId,
                            roomId,
                            targetName,
                            targetLogo
                            )

                    }else {
                        // 没有 先选人 创建
                        val intent = Intent(mContext , SelectWithSearchListWithBottomShowActivity2::class.java).apply {
                            putExtra("title" , "添加成员")
                            putExtra("maxSelectNum" , 30)
                            putExtra("isMultiType" , true)
                            putExtra("rightTitle" , "确定")
                            putExtra("selectedUserIds" , selectedUserIds)  //
                            putExtra("noSupportUserIds" , noSupportUserIds)  //
                            putExtra("outPersonList" , outPersons as Serializable)  //
                        }

//                        ObjectStore.add("startGroupCall" ,EventSelectMember1(appChatId , targetLogo , targetName))
//
//                        (mContext as FragmentActivity).startActivity(intent)
                        val topAct = AppManager.single_instance.currentActivity()
                        (topAct as FragmentActivity).startContractForResult(ActivityResultContracts.StartActivityForResult(),
                            intent) {data ->
                            run {
                                data.data?.let {
                                    if (it.getStringArrayListExtra("selectUserIds") != null) {
                                        val list = it.getStringArrayListExtra("selectUserIds")
                                        if (!list.isNullOrEmpty()) {
                                            val topAct = AppManager.single_instance.currentActivity()
                                            createGroupCall(topAct as FragmentActivity ,
                                                list,
                                                appChatId,
                                                targetLogo,
                                                targetName,
                                                companyId ="",
                                                2,
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }



                }

            }catch (e: Exception){
                e.printStackTrace()
            }
        }

        // 接收音视频 各种指令
        FlutterConstant.NATIVE_PAGERECEIVE_CALL_MSG -> {
            val msgType = (call.argument("msgType") as? Int) ?: 1
            when(msgType){
                ConstantImMsgType.SSChatMessageTypeMoreLoginNoticeChange -> {
                    val ext = (call.argument("ext") as? String) ?: ""
                    val multilMsg = GsonUtil.fromJson<MultilSynchronizeMsgBean>(ext) ?: return
                    if (multilMsg.handleType == 1){
                        // pc  handleType 1 接听
                        EventBusUtils.sendEvent(
                            EventBusEvent(
                                ConstantEventCodeUtil.TCP_MULTIL_SYNCHRONIZATION,
                                multilMsg
                            )
                        )
                    }else if (multilMsg.handleType == 2){
                        // pc  handleType 2 拒绝
                        EventBusUtils.sendEvent(
                            EventBusEvent(
                                ConstantEventCodeUtil.TCP_MULTIL_SYNCHRONIZATION,
                                multilMsg
                            )
                        )
                    }
                }

                ConstantImMsgType.SSChatMessageTypeInitiateVoiceVideo,
                ConstantImMsgType.SSChatMessageTypeVoiceVideoTwoInitiate,
                ConstantImMsgType.SSChatMessageTypeVoiceVideoCallFailure,
                ->{

                    val sessionType = (call.argument("sessionType") as? Int) ?: 1

                    if (sessionType == 1){
                        // 单聊
                        try {
                            val msg = (call.arguments as HashMap<String,Any>).toObject<Message>()
                            AudioVideoReceiveUtil.dealSingleAudioVideoMsg(msg)
                        }catch (e: Exception){
                            e.printStackTrace()
                        }
                    }else {
                        // 群聊
                        val meetingId = (call.argument("meetingId") as? String) ?: ""
                        val sessionId = (call.argument("sessionId") as? String) ?: ""
                        val sendName = (call.argument("sendName") as? String) ?: ""
                        val groupName = (call.argument("groupName") as? String) ?: ""
                        val groupLogo = (call.argument("groupLogo") as? String) ?: ""
                        val sendId = (call.argument("sendId") as? String) ?: ""
                        ARouter.getInstance().build(RouteVc.VC_INVITE_CHAT_PAGE)
                            .withString("groupId", sessionId)
                            .withString("meetingId", meetingId)
                            .withString("targetName", sendName)
                            .withString("groupName", groupName)
                            .withString("targetId", sendId)
                            .withString("groupLogo", groupLogo)
                            .withBoolean("isGroup", true)
                            .navigation()
                    }
                }

                ConstantImMsgType.SSChatMessageTypeVoiceVideoRefused -> {
                    // 只有单聊有
                    /**
                     * Channel().invoke(Channel_Receive_Call_Msg, {
                     *           'userId': imMsg.senderInfo.userId,
                     *           'content': imMsg.c2cMsg.call.content,
                     *           'msgType': imMsg.type
                     *         });
                     */
                    // 拒绝接听和通话中挂断，回调给发起人，发起人根据发送15消息
                    val userId = (call.argument("userId") as? String) ?: ""
                    val msgType = (call.argument("msgType") as? Int) ?: 0
                    val content = (call.argument("content") as? String) ?: ""

                    Timber.i("SSChatMessageTypeVoiceVideoRefused --> userId: $userId , msgType: $msgType , content: $content")

                    val msg = Message().let {
                        it.msgType = msgType
                        it.callContent = content
                        it.sendId = userId
                        it
                    }
                    EventBusUtils.sendEvent(EventBusEvent(ConstantEventCodeUtil.TCP_RECEIVE_AUDIO_VIDEO_MSG, msg))//接收到消息后发送消息接收事件
                }


            }

        }

        // 作用： 分引擎想调用主引擎的一些功能 ，必须夸回主引擎再桥接一次，fuck ！！！
        FlutterConstant.BRIDGET_TO_MAIN_ENGINE -> {
            val argument = (call.arguments as? HashMap<String,Any>) ?: hashMapOf()
            MethodChannelManager.invokeMainEngineFlutter("channel_receive_main_engine_bridge" , argument)
        }

        FlutterConstant.CHANEL_CLEAR_NATIVE_VC -> {
            AppManager.single_instance.getAllActivity().forEach {
                if (it !is FlutterHomeMultiActivity && it !is FlutterMultiActivity){
                    it.finish()
                }
            }
            result.success("1")
        }

        // flutter -> 进入会议
        FlutterConstant.RTC_MEETING -> {
            val isVoice = (call.argument("isVoice") as? Int) ?: 1
            val isVideo = (call.argument("isVideo") as? Int) ?: 1
            val isSpeaker = (call.argument("isSpeaker") as? Int) ?: 1
            val roomId = (call.argument("roomId") as? String) ?: ""
            val userId = (call.argument("userId") as? String) ?: ""
            val appID = (call.argument("appId") as? String) ?: ""
            val signature = (call.argument("userSig") as? String) ?: ""
            RtcManager.TRTC_SDK_APPID = appID
            val c = (mContext as? Activity) ?: return

            RtcManager.openPageAndEnterRoom(mContext, isVoice, isVideo, isSpeaker, roomId, userId, signature)
        }

        FlutterConstant.nativeClosePage -> {
            val route = (call.argument("route") as? String) ?: ""
            if (StringUtils.isEmpty(route)){
                AppManager.single_instance.getAllActivity()?.last()?.finish()
            }else {
                EventBus.getDefault().post(EventCloseFlutterEngine(route))
            }

        }


        // @Deprecated 腾讯云上传 ，单个文件（弃用）
        FlutterConstant.nativeTxUpload -> {
            singleUpload(mContext , call ,result)
        }

        // 腾讯云上传，多个文件
        FlutterConstant.nativeTxUploadList -> {
            FlutterHelper.cosUploadFiles(mContext , call ,result)
        }

        FlutterConstant.getKingDeeToken -> {
            // 获取金蝶考勤
            FlutterHelper.getKingdeeToken(mContext , result)
        }

        FlutterConstant.f2nRefreshFriend -> {
            MethodChannelManager.refreshFriendList()
        }

        FlutterConstant.f2nRefreshreOrgData -> {
            MethodChannelManager.refreshCompanyData()
        }

        FlutterConstant.f2nRefreshPending -> {
            MethodChannelManager.refreshContactDot()
        }

        FlutterConstant.f2nUpgradeApp -> {
            val approveId = (call.argument("approveId") as? String) ?: ""
        }

        FlutterConstant.f2nRefreshCurrentOrg -> {
            val companyId = (call.argument("companyId") as? String) ?: ""
            val name = (call.argument("name") as? String) ?: ""

            Timber.i("companyId = ${companyId}")
            CompanyHolder.setCurrentOrg(companyId)

        }

        FlutterConstant.getNativeDeviceId -> {
            val deviceId = DeviceIdUtil.getDeviceId(mContext, false)
            result.success(mapOf(
                "deviceId" to deviceId
            ))
        }

        FlutterConstant.getNativeHeader -> {
            val deviceId = DeviceIdUtil.getDeviceId(mContext, false)
            val appVersion = BuildConfig.VERSION_NAME
            val platform = "android"
            val version = Build.VERSION.RELEASE
            val clientCode = VersionConfig().versionCode2

            val map = HashMap<String,Any>()
            map.put("deviceId", deviceId)
            map.put("device", deviceId)
            map.put("appVersion", appVersion)
            map.put("platform", platform)
            map.put("version", version)
            map.put("clientCode", clientCode)
            result.success(map)

//            result.success(mapOf(
//                "deviceId" to deviceId,
//                "device" to deviceId,
//                "appVersion" to appVersion,
//                "platform" to platform,
//                "version" to version,
//                "clientCode" to clientCode,
//            ))
        }

        FlutterConstant.getNativeDeviceModel -> {
            val deviceModel = DeviceUtil.getValueEncoded(ConsValue.deviceModel)
            Timber.i("deviceModel = ${deviceModel}")
            result.success(deviceModel)
        }


        // @Deprecated("弃用")
        FlutterConstant.getNativeUnReadCount -> {
            val type = (call.argument("type") as? Int) ?: 0
            when(type){
                0 -> {
                    // 消息未读数
                    val total_unRead =
                        SessionDaoOpe.instance.queryUnReadTotalCountByUid(
                            mContext,
                            UserHolder.getCurrentUser()?.userId
                        )
                    Timber.i("total_unRead = $total_unRead")
                    val map = HashMap<String,Any>()
                    map.put("count" , total_unRead)
                    result.success(map)
                }

                else ->{

                }
            }
        }

        FlutterConstant.main_approveUnread -> {
            val argument = (call.arguments as? HashMap<String,Any>) ?: hashMapOf()
            MethodChannelManager.refreshApproveRedDot(argument)
        }

        FlutterConstant.sendWeChatCode -> {
            // 发送到 wechat
            val code = (call.argument("wechat_code") as? String) ?: ""
            FlutterHelper.settingWechatGetCode(code)
        }

        FlutterConstant.Channel_version_name -> {
            val versionName = BuildConfig.VERSION_NAME
            result.success(versionName)
        }

        FlutterConstant.Channel_version_code -> {
            val clientCode = VersionConfig().versionCode2
            result.success(clientCode)
        }

        FlutterConstant.about_check_update -> {
            EventBus.getDefault().post(EventAboutCheckTimeUpdate())
        }

        // @Deprecated("弃用")
        FlutterConstant.refresh_group_info -> {
            EventBus.getDefault().post(EventRefreshGroupInfo())
        }

        FlutterConstant.NATIVE_POP_ROOT -> {
            EventBus.getDefault().post(EventStartGroupChat())
        }

        FlutterConstant.CALLNATIVE_JUMPROUTE -> {
            val route = (call.argument("route") as? String) ?: ""
            val argument = call.argument("arguments") as? Object
            if (argument != null){
                FlutterMultiActivity.open(route)
                Handler().postDelayed({
                    MethodChannelManager.refreshCommonArguments(route, argument)
                }, 1000)
            }
        }

        FlutterConstant.GETANDROIDDEVICEMODE -> {
            val tag= DeviceUtil.getSystem(mContext)
            result.success(tag)
        }

        FlutterConstant.REPORT_EXCEPTION -> {
            try {
                val error = (call.argument("error_stack") as? String) ?: ""
                XUtil.reportException(error)
            }catch (e: Exception){
                e.printStackTrace()
            }
        }

        // 通知： 进入权限详情
        FlutterConstant.native_pageermissions -> {
            try {
                val permissions = (call.argument("permissions") as? String) ?: ""
                val noticeTitle = (call.argument("noticeTitle") as? String) ?: ""
                val context = (call.argument("context") as? String) ?: ""
                Timber.i("native_pageermissions : $permissions , $context , $noticeTitle")
                ARouter.getInstance().build(RouteOrg.permissionNoticeDetailActivity)
                    .withString("permission", permissions)
                    .withString("managerName", noticeTitle)
                    .withString("context", context)
                    .navigation()

            }catch (e: Exception){
                e.printStackTrace()
            }
        }

        // 通知： 进入考勤规则
        FlutterConstant.native_pageattendance_rule -> {
            try {
                val routeId = (call.argument("routeId") as? String)
                val bundle = Bundle()
                bundle.putString(ConsKeys.MODEL_ID, routeId)// 考勤提醒 考勤规则id
//                bundle.putString(ConsKeys.COMPANY_ID, companyId)// 考勤规则页面增加考勤排班查询，需要companyId

                (ARouter.getInstance().build(RouteProvider.WORK_PROVIDER)
                    .navigation() as RouteServiceProvider)
                    .openPage("attend_rule", bundle)
            }catch (e: Exception){

            }
        }

        FlutterConstant.native_pageattendance_adjust -> {
            try {
                val routeId = (call.argument("routeId") as? String)
                val companyId = (call.argument("companyId") as? String)
                val bundle = Bundle()
                bundle.putString(ConsKeys.MODEL_ID, routeId)// 考勤提醒 考勤规则id
                bundle.putString(ConsKeys.COMPANY_ID, companyId)// 考勤规则页面增加考勤排班查询，需要companyId

                (ARouter.getInstance().build(RouteProvider.WORK_PROVIDER)
                    .navigation() as RouteServiceProvider)
                    .openPage("attend_special_date", bundle)
            }catch (e: Exception){

            }
        }

        FlutterConstant.USERDEFAULT_TOKENKEY -> {
            // token 更新
            try {
                val accessToken =  call.argument("access_token") as? String
                val refreshToken =  call.argument("refresh_token") as? String
                val expires_in =  call.argument("expires_in") as? Int
                Timber.i("USERDEFAULT_TOKENKEY , $accessToken , $refreshToken , $expires_in")

                if (accessToken.isNullOrEmpty() || refreshToken.isNullOrEmpty()) return
                UserHolder.updateRefreshToken(accessToken ?: "" , refreshToken ?: "" , expires_in ?:0)
            }catch (e: Exception){}
        }

        FlutterConstant.NATIVE_PUSHTOKEN -> {
            result.success(UserHolder.getCurrentPushToken())
        }
        FlutterConstant.TAKE_MEAL_SUCCESS ->{
            //取餐成功
            val json = call.argument("json") as? String
            Log.d("test","=========flutter 取餐成功 json =========${json}")
            EventBus.getDefault().post(EventTakeMealSuccess(json))
        }
        FlutterConstant.FLUTTER_TO_NATIVE_DATA -> {
            // flutter 向 native 传递数据
            val type = (call.argument("type") as? Int) ?: -1
            when(type){
                FlutterConstant.FLUTTER_DATA_COMPANY -> {
                    try {

                        val orgListJson = (call.argument("data") as? String) ?:""

                        val orgList = GsonUtil.fromJson<CompanyListBean>(orgListJson , CompanyListBean::class.java)

                        if (orgList == null) return

                        val mainCompanyId = orgList.mainCompanyId
                        val companies = orgList.companies
                        val externalCompanies = orgList.externalCompanies

                        Timber.i("flutter -> native 获取公司数据： $mainCompanyId , ${companies.toString()} , ${externalCompanies.toString()}")

                        CompanyHolder.saveMainOrgId(mainCompanyId)
                        if (!CompanyHolder.getTotalCompanies().isNullOrEmpty()) {
                            //先将缓存中的公司的审批未读数取出来
                            val haveApprovalCompanies = CompanyHolder.getTotalCompanies().filter { it.haveApprove != 0 }
                            haveApprovalCompanies.forEach { work ->
                                companies.find { it.companyId == work.companyId }?.let {
                                    it.haveApprove = work.haveApprove
                                }
                                externalCompanies.find { it.companyId == work.companyId }?.let {
                                    it.haveApprove = work.haveApprove
                                }
                            }
                        }
                        CompanyHolder.saveAllNormalOrg(companies)
                        CompanyHolder.saveCooperationOrg(externalCompanies)
                        var workTagIdChange = 20001
                        var approvalTagIdChange = 30001
                        companies.forEach { work ->
                            // 创建相关公司的通知id，分别创建审批和工作通知的
                            NoticeUtil.saveWorkTagId(work.companyId, workTagIdChange)
                            workTagIdChange++
                            NoticeUtil.saveApprovalTagId(work.companyId, approvalTagIdChange)
                            approvalTagIdChange++
                        }

                        var workTagIdChange1 = 22001
                        var approvalTagIdChange1 = 32001
                        externalCompanies.forEach { cooperationWork ->

                            NoticeUtil.saveWorkTagId(cooperationWork.companyId, workTagIdChange1)
                            workTagIdChange1++
                            NoticeUtil.saveApprovalTagId(cooperationWork.companyId, approvalTagIdChange1)
                            approvalTagIdChange1++
                        }
                        //只有刷新组织的时候才创建通知的tagId========结束==========

                    }catch (e: Exception){
                        e.printStackTrace()
                    }
                }
                FlutterConstant.FLUTTER_DATA_GROUP -> {
                    try {
                        val groupsString = (call.argument("data") as? String) ?: ""
                        Timber.i("flutter -> native 获取群组数据1 ${groupsString}")
                        val type = object : TypeToken<List<GroupInfoDbBean>>(){}.type
                        val groups = GsonUtil.fromJson<GroupInfoDbBean>(groupsString , type) ?: arrayListOf()
                        Timber.i("flutter -> native 获取群组数据2 ${groups.toString()}")
                        GroupCacheHolder.cacheGroupList.clear()
                        GroupCacheHolder.cacheGroupList.addAll(groups)
                    }catch (e: Exception){
                        e.printStackTrace()
                    }
                }
                FlutterConstant.FLUTTER_DATA_FRIENDS -> {
                    try {
                        FriendCacheHolder.saveFriendDataVersion("")
                        val currentUserId = UserHolder.getUserId()
                        val friendListJson = (call.argument("data") as? String) ?: ""
                        val type = object : TypeToken<List<AppFriendBean>>(){}.type
                        val friends = GsonUtil.fromJson<AppFriendBean>(friendListJson , type) ?: arrayListOf()
                        val list = friends.map {
                            val bean = GsonUtil.fromJson(friendListJson , AppFriendBean::class.java)
                            bean?.toFriendBean(currentUserId = currentUserId)
                        }

                        Timber.i("flutter -> native: 获取好友数据${list.toString()}")

                        FriendCacheHolder.saveAppFriendCacheData(mContext, list)
                    }catch (e: Exception){
                        e.printStackTrace()
                    }
                }
                FlutterConstant.FLUTTER_DATA_SYNTHETIC -> {
                    // todo 六合一
                    try {
                        val sixIn1Json = (call.argument("data") as? String) ?: ""
                        val data = GsonUtil.fromJson(sixIn1Json , AppStartResult::class.java)

                        Timber.i("flutter -> native: 六合一接口数据  $data")

                        if (data != null){
                            EventBus.getDefault().post(EventAccept6in1Data(data))
                        }
                    }catch (e: Exception){
                        e.printStackTrace()
                    }
                }
                FlutterConstant.FLUTTER_DATA_IM_TOKEN -> {
                    // IM 相关数据   relation/im/users/v1/mine
                    try {
                        val IMjson = (call.argument("data") as? String) ?: ""
                        val imData = GsonUtil.fromJson<ImTokenBean>(IMjson , ImTokenBean::class.java)
                        Timber.i("flutter -> native: Im接口数据  $imData")
                        if (imData != null) {
                            EventBus.getDefault().post(EventAcceptImData(imData))
                        }
                    }catch (e: Exception){
                        e.printStackTrace()
                    }
                }

                else -> {}
            }
        }


        /// 扫码签名
        FlutterConstant.QR_SIGN -> {
            val key = (call.argument("key") as? String) ?: ""
            val userId = (call.argument("userId") as? String) ?: ""
            val companyId = (call.argument("companyId") as? String) ?: ""

            Timber.i("扫码签名 --->  $key , $userId , $companyId")
            if(UserHolder.getUserId() != userId){
                val dialog = MyDialog(mContext as FragmentActivity, 309, 171,
                    "", needBtnConfirm = true, needBtnCancel = false, bgResourceId = 0,
                    imageResourceId = R.drawable.ic_error_image)
                dialog.setContentText("请使用本人担当账号进行扫一扫完成审批")
                dialog.show()
                return
            }

            val action = SCAN_SIGNATURE
            AprSignatureActivity.startPage(mContext ,
                key ,
                approveId = "",
                action ,
                companyId)
        }

        FlutterConstant.SWITCH_ENV -> {
            val envIndex = (call.argument("env_index") as? Int) ?: -1
            Timber.i("envIndex = $envIndex")
            if (envIndex > -1){
                MMKVUtil.saveInt("env_index",envIndex)
            }
            RetrofitClient.single_intance.resetLoginClearInstance()
            ImOffLineRetrofitClient.single_intance.resetLoginClearInstance()
            ImRetrofitClient.single_intance.resetLoginClearInstance()
        }

        FlutterConstant.RE_START_APP -> SystemHelper.restart(mContext)

        FlutterConstant.REQUESTANDROIDLAUNCHPERMISSION -> {
            // 申请Android所需的权限
            val imei = DeviceIdUtil.getDeviceId(mContext, false)
            MMKVUtil.saveString(MMKVKEY.IMEI, imei)
            LoginHelper.onPrivacyRead()
            StaticsHelper.preInit(mContext)
            result.success(1)

            EventBus.getDefault().post(EventRequestPermission())
        }



        else -> result.notImplemented()
    }
}

//tcp创建群聊音视频
fun createGroupCall(context: Activity , inviteUsers: ArrayList<String> , targetId: String , targetLogo: String , targetName: String , companyId: String , callType: Int) {
    if (inviteUsers.isNotEmpty()) {

        PermissionUtils.checkNetTypePermission(context, callBack = {

            ChatControlActivity.openChatControlPage(context , targetId , targetLogo,
                targetName , companyId , callType , inviteUsers)

//            ARouter.getInstance().build(RouteVc.VC_CREATE_CALL)
//                .withString("groupId", targetId)
//                .withString("groupLogo", targetLogo)
//                .withString("groupName", targetName)
//                .withString("companyId", companyId)
//                .withInt("callType", callType)
//                .withStringArrayList("inviteUserIds", inviteUsers)
//                .navigation()
        }, cancel = {

        })
    }
}

val vcService = VcServiceImpl()

private fun joinGroupCall(
    context: FragmentActivity,
    meetingId: String , userId: String , groupId: String ,roomId: Int ,
                          targetName: String,targetLogo: String
                          ) {

    val bundle = Bundle()
    bundle.putString("meetingId", meetingId)
    bundle.putString("userId", userId)
    vcService?.service("joinGroupCall", bundle) { result ->
        if (!result.isNullOrBlank()) {
            val commonResult = GsonUtil.fromJson<CommonResult<Any>>(result)
            if (commonResult != null) {
                if (commonResult.errorCode == ErrorType.SUCCESS) {
                    try {
                        val joinResult = JSONObject(GsonUtil.toJson(commonResult.success))
                        val appid = joinResult.optInt("appid")
                        val sig = joinResult.optString("sig")
                        enterRoom(
                            context,
                            groupId = groupId,
                            meetingId = meetingId,
                            roomId = roomId,
                            appid = appid,
                            sig = sig ,
                            targetName = targetName,
                            targetLogo = targetLogo,)
                    } catch (e: Exception) {
                        toast("进入通话失败")
                    }
                } else {
                    toast("进入通话异常")
                }
            } else {
                toast("进入通话失败")
            }
        } else {
            toast("进入通话失败")
        }
    }
}

private fun enterRoom(context: FragmentActivity ,groupId: String,
                      meetingId: String, roomId: Int, appid: Int, sig: String?,
            targetName: String,targetLogo: String
) {
    val bundle = Bundle()
    bundle.putInt("callType", 2)
    bundle.putString("meetingId", meetingId)
    bundle.putString("groupId", groupId)
    bundle.putInt("roomId", roomId)
    bundle.putInt("appid", appid)
    bundle.putString("sig", sig)

    bundle.putString("groupName", targetName)
    bundle.putString("groupLogo", targetLogo)

    vcService?.openPageWithResult1(context, "enterRoom", bundle) {}
}


fun singleUpload(mContext: Context ,call: MethodCall , result: MethodChannel.Result) {
    val type = (call.argument("type") as? Int) ?: 0
    val fileId = (call.argument("object") as? String) ?: ""  // fileId
    val filePath = (call.argument("filePath") as? String) ?: ""

    Timber.i("nativeTxUpload : , $fileId , $filePath")

    fun uploadTx(bucket:String , service: CosService? = null) {
        CosWrapperV2.uploadFileByBucket(filePath,fileId , bucket , onProgress = {
                filePath, _, _, percent ->
        },
            onSuccess = { filePath: String, url: String, hash: String, bucket: String ->

                Timber.i("上传成功 url $url , bucket = $bucket")
                val param = mapOf<String, Any?>(
                    "upLoadState" to 1,
                )
                result.success(param)
            } ,
            onError = {
                toast(mContext , it)
                val param = mapOf<String, Any?>(
                    "upLoadState" to 0,
                )
                result.success(param)
            }, isPanFlag = true , service
        )
    }

    fun getCosService(qCloudCredentialProvider: MyCredentialProvider): CosService? {
        FileStorage.showLog("PanService 初始化")
        val t = FileStorage.cosSession ?: return null
        val secretId = t.credentials.tmpSecretId
        val secretKey = t.credentials.tmpSecretKey
        val sessionToken = t.credentials.sessionToken
        val expiredTime: Long = t.expiredTime//临时密钥有效截止时间戳
        //初始化 config
        val serviceConfig = FileStorage.getTencentCloudConfig() ?: return null
        //使用临时密钥初始化QCloudCredentialProvider
//                val qCloudCredentialProvider = MyCredentialProvider(secretId, secretKey, sessionToken, expiredTime)
        //初始化CosXmlService
        FileUpTransferManager.instanceCosService = CosService(BaseApplication.joinuTechContext, serviceConfig, qCloudCredentialProvider)
        return FileUpTransferManager.instanceCosService
    }

    if (false){
        // 有效
        Timber.i("腾讯云 session 有效")
    }else{
        UploadFileService.getPanTencentSessionV2()
            .compose(ErrorTransformer.getInstance<TencentSessionBean>())
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(object : BaseSubscriber<TencentSessionBean>() {
                override fun onError(ex: ApiException) {
                    toast(mContext , ex.message)
                    ex.printStackTrace()
                }

                override fun onComplete() {
                }

                override fun onNext(result: TencentSessionBean) {
                    FileStorage.cosSession = result
                    val provider = MyCredentialProvider(result.credentials.tmpSecretId
                        , result.credentials.tmpSecretKey, result.credentials.sessionToken, result.expiredTime)
                    val bucketString = if (type == 0) result.panBucket else result.openBucket
                    uploadTx(result.openBucket ?:"" ,getCosService(provider))
                }
            })
    }
}