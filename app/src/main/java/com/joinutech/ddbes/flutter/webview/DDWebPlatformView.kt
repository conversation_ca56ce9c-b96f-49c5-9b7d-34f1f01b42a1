package com.joinutech.ddbes.flutter.webview


import android.content.Context
import android.content.ContextWrapper
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.View
import android.view.ViewParent
import android.view.Window
import androidx.activity.ComponentActivity
import androidx.core.view.doOnAttach
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentContainerView
import androidx.fragment.app.commit
import com.joinutech.ddbeslibrary.base.CommonWebFragment
import com.joinutech.message.view.tcpimpages.session.SessionFragment
import io.flutter.plugin.common.BinaryMessenger
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.StandardMessageCodec
import io.flutter.plugin.platform.PlatformView
import io.flutter.plugin.platform.PlatformViewFactory
import timber.log.Timber
import java.lang.reflect.Field
import java.util.Random



const val webViewType = "dd_webview_type"
const val webViewType1 = "dd_webview_type1"


//messenger：用于消息传递，后面介绍 Flutter 与 原生通信时用到此参数。
//viewId：View 生成时会分配一个唯一 ID。
//args：Flutter 传递的初始化参数
class DDWePlatView(context: Context, messenger: BinaryMessenger, viewId: Int, args: Map<String, Any> = HashMap<String,Any>()) : PlatformView,
    MethodChannel.MethodCallHandler{

    private val view: FragmentContainerView

    var mContext: Context? = null

    var mArgs: Map<String,Any> = HashMap<String,Any>()

    /**
     * val title = (call.argument("title") as? String) ?: ""
     *             val isWebNavigation = (call.argument("isWebNavigation") as? Int) ?: 0   // 0 显示原生导航栏  1 不显示
     *             val url = (call.argument("url") as? String) ?: ""
     *             val orgId = (call.argument("orgId") as? String) ?: ""
     *             val fileInfo = (call.argument("fileInfo") as? Map<String, String>) ?: mapOf()
     *             Timber.i("openNativeWebView: $isWebNavigation , $title , $url")
     *             ObjectStore.add("wpsfileInfo" , fileInfo)
     */

    init {
        this.mContext = context
        this.mArgs = args
        // 参数
        val url = (mArgs.get("url") ?:"").toString()
        val companyId = (mArgs.get("orgId") ?:"").toString()
        val isShowBack = ((mArgs.get("isWebNavigation") ?:0) as? Int) ?: 0
        val title = (mArgs.get("title") ?: "") as? String

        Timber.i("ddwebview 原生初始化： $args")

        val fragment = CommonWebFragment.newInstance(url, companyId , isShowBack ,title ?: "" )
        val view = FragmentContainerView(mContext!!)
        view.id = Random().nextInt()
        view.doOnAttach {
            val activity = it.context.getFragmentActivityOrThrow()

            activity.supportFragmentManager.findFragmentByTag("flutter_fragment")?.let { flutterFragment ->
                flutterFragment.childFragmentManager.commit {
                    replace(it.id, fragment)
                }
            }
        }
        this.view = view
    }

    override fun onFlutterViewAttached(flutterView: View) {
        super.onFlutterViewAttached(flutterView)
        Timber.i("onFlutterViewAttached...")
    }

    override fun onFlutterViewDetached() {
        super.onFlutterViewDetached()
        Timber.i("onFlutterViewDetached...")
    }

    override fun getView(): View {
        Timber.i("ddwebPluginView....$view")
//        makeWindowTransparent(view)
        return view
    }

    private fun makeWindowTransparent(imView: View) {
        if (imView == null) return
        imView.post {
            try {
                var parent: ViewParent? = imView.getParent() ?: return@post
                while (parent!!.parent != null) {
                    parent = parent.parent
                }
                val decorView =
                    parent.javaClass.getDeclaredMethod("getView").invoke(parent)
                val windowField: Field = decorView.javaClass.getDeclaredField("mWindow")
                windowField.setAccessible(true)
                val window = windowField.get(decorView) as Window
                windowField.setAccessible(false)
                window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            } catch (e: Exception) {
                e.printStackTrace()
                // log the exception
            }
        }
    }

    override fun dispose() {
        Timber.i("webviewPlatform dispose...")
    }

    private fun Context.getFragmentActivityOrThrow(): FragmentActivity {
        if (this is FragmentActivity) {
            return this
        }

        var currentContext = this
        while (currentContext is ContextWrapper) {
            if (currentContext is FragmentActivity) {
                return currentContext
            }
            currentContext = currentContext.baseContext
        }

        throw IllegalStateException("Unable to find activity")
    }

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {

    }

}


class DDWebViewiewFactory(val mContext : Context , val messenger: BinaryMessenger) : PlatformViewFactory(
    StandardMessageCodec.INSTANCE) {

    override fun create(context: Context, viewId: Int, args: Any?): PlatformView {
        return DDWePlatView(mContext, messenger, viewId, (args as? Map<String, Any>) ?: HashMap<String,Any>())
    }

}
