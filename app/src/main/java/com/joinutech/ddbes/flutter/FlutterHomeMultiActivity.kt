package com.joinutech.ddbes.flutter

import android.annotation.SuppressLint
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import com.ddbes.library.im.imtcp.ConstantEventCodeUtil
import com.joinu.rtcmeeting.utils.logger
import com.joinutech.common.helper.LoginHelper
import com.joinutech.common.storage.FileStorage
import com.joinutech.common.util.CacheHelper
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.ObjectStore
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbes.flutter.DDAppInit.dealPhoneImeiPermission
import com.joinutech.ddbes.flutter.DDAppInit.getDeviceVersion
import com.joinutech.ddbes.flutter.DDAppInit.getPanBucket
import com.joinutech.ddbes.flutter.DDAppInit.initJpush
import com.joinutech.ddbes.flutter.mutiengine.EngineBindings
import com.joinutech.ddbeslibrary.VersionConfig
import com.joinutech.ddbeslibrary.bean.AppStartResult
import com.joinutech.ddbeslibrary.bean.AppVersionBean
import com.joinutech.ddbeslibrary.utils.AppManager
import com.joinutech.ddbeslibrary.utils.AppVersionCheckUpdateUtil
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.Loggerr
import com.joinutech.ddbeslibrary.utils.MMKVKEY
import com.joinutech.ddbeslibrary.utils.MMKVUtil
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.XUtil
import com.joinutech.ddbeslibrary.utils.toast
import com.joinutech.flutter.EventAccept6in1Data
import com.joinutech.flutter.EventCheckTimeUpdate
import com.joinutech.flutter.EventResetLoginIM
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.plugin.common.MethodChannel
import me.jessyan.autosize.internal.CancelAdapt
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import timber.log.Timber

/**
 * @Des：
 * @author: moon
 * @date: 9/8/23
 */
class FlutterHomeMultiActivity() : FlutterFragmentActivity() , CancelAdapt {

    var pointName:String? = null

    var flutterEventHandler: FlutterEventHandler? = null

    val vm: FlutterHomeViewModel by viewModels()

    var jumpData :String = ""//离线推送跳转数据

    private val bindings: EngineBindings by lazy {
        val point = (ObjectStore.remove(MULTI_ENGINE_PAGE_ROUTE) as? String) ?: FlutterConstant.SPLASH_PAGE
        pointName = point
        EngineBindings(this , point, FlutterConstant.getCacheId(point))
    }

    companion object {

        fun open(context: Context , routeName: String = FlutterConstant.SPLASH_PAGE ) {

            ObjectStore.add(MULTI_ENGINE_PAGE_ROUTE , routeName)

            val intent = Intent(context , FlutterHomeMultiActivity::class.java)
            intent.putExtra(MULTI_ENGINE_PAGE_ROUTE , routeName)
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        AppManager.single_instance.addActivity(this)

        flutterEventHandler = FlutterEventHandler(this)
        EventBus.getDefault().register(flutterEventHandler)

        EventBusUtils.register(this)

        DDAppInit.initApp(this , vm)


        vm.fetchUserInfo()
        bindings.attach()

        clearAllNotification()
    }

    private fun clearAllNotification() {
       val manager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        manager.cancelAll()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        vm.JpushOnNewIntent(this)
    }

    override fun onResume() {
        super.onResume()
        pointName?.let {
            Timber.i("flutter 当前的 currentRoute： $pointName")
            MethodChannelManager.currentRoute = pointName!!
        }

        EventBus.getDefault().post(EventCheckTimeUpdate())
    }

    //tcp接收event通知
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun receiveTcpImNotice(event: EventBusEvent<String>) {
        when (event.code) {
            ConstantEventCodeUtil.TCP_REFRESH_FRIEND_ON_DELETE -> {
                Timber.i("im 推送 ， 删除好友。。。")
                MethodChannelManager.refreshFriendList()
            }

            ConstantEventCodeUtil.TCP_REFRESH_FRIEND_LIST -> { //刷新好友列表
                Timber.i("im 推送 ， 添加了好友。。。")
//                MethodChannelManager.refreshFriendList()
            }
            ConstantEventCodeUtil.TCP_REFRESH_GROUP_LIST -> {//刷新群组列表
                MethodChannelManager.refreshCompanyData()
                MethodChannelManager.refreshGroupList()
            }

            EventBusAction.REFRESH_ORGANLIST -> {
                MethodChannelManager.refreshCompanyData()
            }

            ConstantEventCodeUtil.TCP_REFRESH_APPLY_ACTION -> {
                MethodChannelManager.refreshContactDot()
            }

            ConstantEventCodeUtil.TCP_RECEIVE_KICK_TO_LOGIN_OUT -> {
                // 你被踢掉了，多端登录
            }
        }
    }

    @Subscribe
    fun resetLoginIM(body: EventResetLoginIM){

        MMKVUtil.saveInt(MMKVKEY.APP_Statistics, 1)

        DDAppInit.getPanBucket()

        DDAppInit.initJpush(this ,vm)
    }


    @Subscribe
    fun accept6in1(body: EventAccept6in1Data) {
        // 接收到了六合一接口数据
        delay6In1(body.body!!)
    }

    private fun delay6In1(result: AppStartResult) {
        MMKVUtil.saveInt(MMKVKEY.APP_Statistics, 0)
        dealVersionSuccess(
            AppVersionBean(
            downUrl = result.downUrl,
            status = result.status,
            auditing = result.auditing,
            versionName = result.versionName,
            currentDesc = result.currentDesc,
            desc = result.desc
        ))

        EventBusUtils.sendEvent(
            EventBusEvent(
                EventBusAction.EVENT_WORK_BANNER_REFRESH,
                result.bannerList
            )
        )
        // TODO: 2020/8/25 16:33 2.4.2 优化通知免打扰设置，系统，团队，审批
        Timber.i("六合一接口 返回值 设置 推送开关......")
        val pushSetting = CompanyHolder.getPushSetting()
        pushSetting.ids = result.ids// 团队，在这个列表中时为开启了免打扰
        pushSetting.systemPushSwitch = result.systemPushSwitch// 系统消息免打扰
        pushSetting.approvalPushSwitch = result.approvalPushSwitch// 审批消息免打扰
        pushSetting.ticketPushSwitch = result.ticketPushSwitch// 工单消息免打扰
        pushSetting.inventorySwitch = result.inventorySwitch// 库存消息免打扰
        pushSetting.trainingSwitch = result.trainingSwitch  // 培训
        pushSetting.kingdeePushSwitch = result.kingdeePushSwitch  // 金蝶(hr  )
        pushSetting.managementSwitch = result.managementSwitch  // 综合管理平台(hr  )
        CompanyHolder.savePushSetting(pushSetting)

        if (result.region.isNullOrBlank()) {
            logger("动态云存储信息失败")
        } else {
            FileStorage.COS_REGION = result.region ?: ""
            FileStorage.COS_APPID = result.appId ?: ""
        }
        // TODO: 2021/4/13 客服热线通过接口回调
        val telephone = result.tel ?: ""
        if (result.pre.isNullOrBlank()) {
            logger("动态云存储前缀信息失败")
        } else {
            FileStorage.TOS_DEFAULT_URL_PRE = result.pre ?: ""
        }
        //缓存点击超链接时的一些数据
        UserHolder.saveLinkWarn(result.linkWarn)
        UserHolder.saveLinkWhiteList(result.linkWhiteList)

        //
        vm.delayExecute {
            MethodChannelManager.passSixMergeData(result)
        }
    }

    /**
     * 获取im连接token
     * 获取到imToken后才能登录和获取离线数据
     * */
    private fun dealVersionSuccess(it: AppVersionBean , tip: Boolean = false) {
        MMKVUtil.saveString("appNewVersion", GsonUtil.toJson(it))
        Loggerr.i("软件更新", "====${GsonUtil.toJson(it)}")
        //先判断有没有当前版本的版本描述，因为不变，所以只需存储一次即可
        if (StringUtils.isNotBlankAndEmpty(it.currentDesc)) {
//            val versionName = MMKVUtil.getString(MMKVKEY.VERSION_NAME)
            val versionName = VersionConfig().versionName
            val versionContent = MMKVUtil.getString("appVersionNum$versionName")
            if (StringUtils.isEmpty(versionContent) || versionContent != it.currentDesc) {
                MMKVUtil.saveString("appVersionNum$versionName", it.currentDesc)
            }
        }

        val stacks = AppManager.single_instance.getAllActivity()
        val act = if (stacks.isEmpty() ) this else stacks.last()
        AppVersionCheckUpdateUtil.updateNewVersionDialog(act, it , tip )
    }


    override fun onPause() {
        super.onPause()
    }

    override fun provideFlutterEngine(context: Context): FlutterEngine? {
        return bindings.engine
    }

    fun requestPermission(context: Context ,fvm: FlutterHomeViewModel? = null) {
        if (LoginHelper.isReadPrivacy(context)){

            if (fvm == null){
                initJpush(context ,vm)
            }else{
                initJpush(context , fvm)
            }
            dealPhoneImeiPermission(context)
            getDeviceVersion(context)
//            XUtil.initLocation()
            XUtil.initBugly()
            getPanBucket()


        }
    }

    var exitTime = 0L
    @SuppressLint("RtlHardcoded")
    override fun onBackPressed() {
        if (System.currentTimeMillis() - CacheHelper.globalBackListenerTime < 800) {
            CacheHelper.globalBackListenerTime = System.currentTimeMillis()
            return
        }

//        if (AppManager.single_instance.isOneMoreMainAct(FlutterHomeBaseActivity::class.java)){
//            AppManager.single_instance.finishTopActivity(FlutterHomeBaseActivity::class.java)
//        }

        val stacks = AppManager.single_instance.getAllActivity()
        if (stacks.size > 2){
            super.onBackPressed()
        }else{
            MethodChannelManager.invokeFlutterWithBack(FlutterConstant.flutterBackPage , mapOf(), object :MethodChannel.Result{
                override fun success(result: Any?) {
                    if(result == 1){
                        if (System.currentTimeMillis() - exitTime > 2000) {
                            toast(this@FlutterHomeMultiActivity , "再按一次返回键退出")
                            exitTime = System.currentTimeMillis()
                        } else {
                            // 不关闭App，只是回到桌面
                            val launcherIntent = Intent(Intent.ACTION_MAIN)
                            launcherIntent.addCategory(Intent.CATEGORY_HOME)
                            startActivity(launcherIntent)
                        }
                    }
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                }

                override fun notImplemented() {
                }
            })
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        flutterEventHandler?.let { EventBus.getDefault().unregister(it) }
        EventBusUtils.unregister(this)
        bindings.detach()
        AppManager.single_instance.finishActivity(this)
    }

    private fun destroyEngine(){
        val entry_point = intent.getStringExtra(MULTI_ENGINE_PAGE_ROUTE) ?: FlutterConstant.SPLASH_PAGE
        val engine = FlutterEngineCache.getInstance().get(FlutterConstant.getCacheId(entry_point).toString())
        if (engine == null){
            toast(this , "Engine 没有了？")
        }else{
            engine.destroy()
        }
    }
}