package com.joinutech.ddbes.flutter

//import com.joinu.jpush.DDJpushManager

import androidx.activity.ComponentActivity
import androidx.lifecycle.coroutineScope
import com.ddbes.library.im.imtcp.dbbean.Message
import com.ddbes.library.im.imtcp.imservice.OkWebSocketService
import com.joinu.rtcmeeting.RtcHasNewMeeting
import com.joinu.rtcmeeting.RtcManager
import com.joinu.rtcmeeting.RtcStartMeeting
import com.joinu.rtcmeeting.activities.EventFlutterDismissMeeting
import com.joinu.rtcmeeting.utils.logger
import com.joinutech.common.util.ObjectStore
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbes.flutter.FlutterConstant.Companion.QR_CODE
import com.joinutech.ddbes.flutter.MethodChannelManager.invokeFlutter
import com.joinutech.ddbes.flutter.MethodChannelManager.invokeFlutterWithCallBack
import com.joinutech.ddbes.flutter.MethodChannelManager.invokeMainEngineFlutterByObj
import com.joinutech.ddbeslibrary.VersionConfig
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.bean.AppStartResult
import com.joinutech.ddbeslibrary.bean.AppVersionBean
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.LoginService
import com.joinutech.ddbeslibrary.service.PersonService
import com.joinutech.ddbeslibrary.utils.AppManager
import com.joinutech.ddbeslibrary.utils.AppVersionCheckUpdateUtil
import com.joinutech.ddbeslibrary.utils.ConsValue
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.Loggerr
import com.joinutech.ddbeslibrary.utils.MMKVUtil
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.toast
import com.joinutech.ext.jackSonToMap
import com.joinutech.flutter.EventAboutCheckTimeUpdate
import com.joinutech.flutter.EventApplyDetailPage
import com.joinutech.flutter.EventApplyInfoData
import com.joinutech.flutter.EventApproveDeleteKingdee
import com.joinutech.flutter.EventBackHandSignFile
import com.joinutech.flutter.EventBindWeChat
import com.joinutech.flutter.EventByte
import com.joinutech.flutter.EventClearFlutterCache
import com.joinutech.flutter.EventCloseWeb
import com.joinutech.flutter.EventDoneWith
import com.joinutech.flutter.EventFlutterIM
import com.joinutech.flutter.EventFlutterRefreshMeetingTopbar
import com.joinutech.flutter.EventFluttertranslateLocalPath
import com.joinutech.flutter.EventForceRefreshWorkbench
import com.joinutech.flutter.EventGroupAddFriend
import com.joinutech.flutter.EventGroupNameAction
import com.joinutech.flutter.EventHomeReady
import com.joinutech.flutter.EventLoginOut
import com.joinutech.flutter.EventOpenFlutterForward
import com.joinutech.flutter.EventReConnect
import com.joinutech.flutter.EventReOpenWeb
import com.joinutech.flutter.EventRefreshApprove
import com.joinutech.flutter.EventRefreshApproveRedDot
import com.joinutech.flutter.EventRefreshContactPendingDataRedDot
import com.joinutech.flutter.EventRefreshGroupInfo
import com.joinutech.flutter.EventRefreshNotice
import com.joinutech.flutter.EventRefreshOrg
import com.joinutech.flutter.EventRefreshWorkRed
import com.joinutech.flutter.EventRequestPermission
import com.joinutech.flutter.EventRouteFlutter
import com.joinutech.flutter.EventShowLogoutDialog
import com.joinutech.flutter.EventSocketStatus
import com.joinutech.flutter.EventStartUserInfoPage
import com.joinutech.flutter.EventSyncFlutter
import com.joinutech.flutter.EventTiandituParam
import com.joinutech.flutter.EventToApproveDetail
import com.joinutech.flutter.EventToApproveList
import com.joinutech.flutter.EventToFlutterCreateFriend
import com.joinutech.flutter.EventToQrCode
import com.joinutech.flutter.EventToRecord
import com.joinutech.flutter.EventToRtcMeetingDetail
import com.joinutech.flutter.EventUpdateUserInfo
import com.joinutech.flutter.RtcEventStartMeeting
import com.joinutech.message.view.tcpimpages.multi_select.ImChatRecordActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import timber.log.Timber

/**
 * @Des：
 * @author: moon
 * @date: 9/4/24
 */
class FlutterEventHandler(private val vm: ComponentActivity) {

    // 指定名字
    @Subscribe
    fun openApproveDetail(b: EventToApproveDetail) {

        FlutterMultiActivity.open(FlutterConstant.APPROVE_DETAIL)
        vm.delayExecute (1000){
            MethodChannelManager.toApproveDetail(FlutterConstant.APPROVE_DETAIL, b.approveId?:"" , b.orgId)
        }
    }

    // 指定名字
    @Subscribe
    fun openApproveListPage(b: EventToApproveList) {
        FlutterMultiActivity.open(FlutterConstant.APPROVE_LIST)
        vm.delayExecute (1000){
            MethodChannelManager.toApproveList(FlutterConstant.APPROVE_LIST, b.listType ?:0 , b.orgId)
        }
    }

    @Subscribe
    fun openApproveInfo(body: EventApplyInfoData) {
        logger("EventApplyInfoData....")
        FlutterMultiActivity.open(body.routeName)
        vm.delayExecute (1000) {
            MethodChannelManager.refreshCommonArguments(body.routeName, body.argument)
        }
    }

    @Subscribe
    fun toFlutterCreateFriend(body: EventToFlutterCreateFriend) {
        val barHeight =  ConsValue.getStatusBarHeight(vm , false)
        MethodChannelManager.alertSessionListCreateDialog(barHeight+ 10f)
    }

    @Subscribe
    fun groupAddNewFriend(b : EventGroupAddFriend) {
        FlutterMultiActivity.open(FlutterConstant.CHOOSE_FRIEND_AND_ORG)
        val userList = b.userList.map {banner ->
            mapOf(
                "name" to (banner.name ?:""),
            )
        }
        vm.delayExecute (1000){
            val map = mapOf(
                "selectList" to b.selectList,
                "type" to b.type,
                "groupId" to b.groupId,
                "userList" to userList,
                "index" to b.index
            )
            MethodChannelManager.refreshFriendApplyArguments(FlutterConstant.CHOOSE_FRIEND_AND_ORG, map)
        }
    }

    @Subscribe
    fun refreshFlutterGroup(b: EventRefreshGroupInfo) {
        MethodChannelManager.refreshGroupList()
    }

    @Subscribe
    fun toFlutterFriendApplyPage(body : EventApplyDetailPage) {
        FlutterMultiActivity.open(FlutterConstant.FRIEND_APPLY_DETAIL)
        var params = mapOf<String,Any>()
        when(body.type){
            0 ,1 -> {
                params = mapOf(
                    "recordId" to body.recordId,
                    "type" to body.type
                )
            }
            2 -> {
                params = mapOf(
                    "recordId" to body.recordId,
                    "type" to body.type,
                    "companyId" to body.companyId
                )

            }
        }

        vm.delayExecute(1000){
            MethodChannelManager.refreshFriendApplyArguments(FlutterConstant.FRIEND_APPLY_DETAIL ,params)
        }
    }

    @Subscribe
    fun openDoneWith(body: EventDoneWith) {
        FlutterMultiActivity.open(body.route)
        val map = mapOf<String,Any>(
            "orgId" to body.orgId,
            "approveId" to body.approveId
        )
        vm.delayExecute(1000){
            MethodChannelManager.refreshFriendApplyArguments(body.route , map)
        }
    }


    @Subscribe
    fun signFile(f: EventBackHandSignFile) {
        MethodChannelManager.callHandSigature(f)
    }

    @Subscribe
    fun refreshFlutterApproveRedDot(body: EventRefreshApprove) {
        MethodChannelManager.refreshApproveRedDot(body.map)
    }

    @Subscribe
    fun refreshWorkRedPoint(b: EventRefreshWorkRed) {
        val map = hashMapOf<String,Any>()
        map.put("orgId" , b.orgId ?:"")
        MethodChannelManager.refreshWorkRedDot(map)
    }

    @Subscribe
    fun refreshApproveRedDot(b: EventRefreshApproveRedDot) {
        MethodChannelManager.refreshDataWithType(b.msgType , b.data ?:"")
    }

    @Subscribe
    fun refreshWork(b: EventForceRefreshWorkbench){
        MethodChannelManager.forceFresh()
    }

    @Subscribe
    fun refreshContactRedDot(b: EventRefreshContactPendingDataRedDot) {
        vm.runOnUiThread {
            MethodChannelManager.refreshContactDot()
        }
    }

    @Subscribe
    fun disMissRoomId(b: EventFlutterDismissMeeting) {
        vm.runOnUiThread {
            MethodChannelManager.disMissroom(b.roomId)
        }
    }


    @Subscribe
    fun linkStartRtc(b: RtcEventStartMeeting) {
        if (RtcManager.hasUnFinishMeeting()) {
            EventBus.getDefault().post(RtcHasNewMeeting(b.meetingId))
        }else {
            MethodChannelManager.linkEnterMeeting(b.meetingId)
        }
    }

    @Subscribe
    fun requestLaunch(b: EventRequestPermission) {
        if (vm is FlutterHomeMultiActivity){
            vm.requestPermission(vm)
        }

    }

    @Subscribe
    fun startMeeting(b: RtcStartMeeting) {
        MethodChannelManager.linkEnterMeeting(b.meetingId)
    }

    // 回调方法 native_im_forward
    @Deprecated("")
    @Subscribe
    fun openImForward(body: EventOpenFlutterForward) {
        ObjectStore.add("flutter_forward_type", body.type)
        FlutterMultiActivity.open(FlutterConstant.CHOOSE_FRIEND_AND_ORG)
        vm.delayExecute (1000){

            val map = mapOf<String ,Any>(
//                "selectList" to b.selectList,
                "type" to 2,
                "forwardType" to if (body.isMerge) 2 else 1,   // 1 逐条  2 合并 ， 0
                "forwardText" to (body.forwardText ?:""),   // 1 逐条  2 合并 ， 0
//                "groupId" to b.groupId,
//                "userList" to userList,
//                "index" to b.index
            )

            MethodChannelManager.refreshFriendApplyArguments(FlutterConstant.CHOOSE_FRIEND_AND_ORG, map)
        }
    }

    @Subscribe
    fun refreshUserinfo(body: EventUpdateUserInfo){
        MethodChannelManager.refreshUserData()
    }

    @Subscribe
    fun loginOut(body : EventLoginOut) {
        @Synchronized
        fun loginOutAction() {
            if (vm is FlutterHomeMultiActivity){
                if (System.currentTimeMillis() - vm.exitTime < 1000) {
                    vm.exitTime = System.currentTimeMillis()
                    return
                }
                Timber.i("执行了 loginOut 。。。。")
                MethodChannelManager.loginOut()
            }
        }

        loginOutAction()
    }

    @Subscribe
    fun updateOrg(body: EventRefreshOrg){
        MethodChannelManager.refreshCompanyData()
    }

    @Subscribe
    fun openFlutterUserInfoPage(body: EventStartUserInfoPage) {
        FlutterMultiActivity.open(FlutterConstant.USER_INFO)

        vm.delayExecute(1000) {
            MethodChannelManager.refreshUserInfo(body.userId
                ,body.type , body.companyId)
        }
    }

    @Subscribe
    fun openRtcMeetingDetail(body: EventToRtcMeetingDetail) {
        FlutterMultiActivity.open(FlutterConstant.RTC_MEETING_DETAIL)
        vm.delayExecute (1000){
            MethodChannelManager.toRtcMeetingDetail(body.meetingId ?:""
                ,body.type)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun refreshData(event: EventBusEvent<Int>) {
        when (event.code) {
            EventBusAction.HOME_POINT -> {
                logger("未读消息红点信息 ${event.data}")
                val num = if (event.data != null) {
                    event.data!!
                } else {
                    0
                }
                MethodChannelManager.refreshUnreadMsg(num , 0)
//                DeviceUtil.setDeviceBadgeNum(vm , num)
            }
        }
    }

    @Subscribe
    fun clearFlutter(event: EventClearFlutterCache) {
        MethodChannelManager.clearFlutterCache()
    }

    @Subscribe
    fun showFlutterQuiteDialog(body: EventShowLogoutDialog) {
        MethodChannelManager.showFlutterLogout(body.body)
    }

    @Subscribe
    fun eventGroupNameAction(body: EventGroupNameAction) {
        FlutterMultiActivity.open(FlutterConstant.GROUP_CHANGE_INFO)
        vm.delayExecute (1000){
            MethodChannelManager.groupNameRoute(body.groupId , body.groupName , body.groupLogo)
        }
    }

    @Subscribe
    fun eventTransLateToRecord(body: EventToRecord) {
        ImChatRecordActivity.openLocalMsg(vm)
    }

    @Subscribe
    fun deleteKingdeeApprove (body: EventApproveDeleteKingdee) {
        MethodChannelManager.deleteKingdeeApprove(body.name)
    }

    @Subscribe
    fun checkUpgrade(body: EventAboutCheckTimeUpdate) {
        checkUpgrade1(true)
    }

    // 向flutter 发送 Message 类型的消息，目前用于音视频
    @Subscribe
    fun sendFlutterIm(body: EventFlutterIM) {
        Timber.i("flutter 发送 Im 指令 =》  ${body.data}")
        val msg = GsonUtil.fromJson<Message>(body.data)

        if (msg == null) return
        MethodChannelManager.sendFlutterImData(msg.jackSonToMap())
    }

    // 发送同步指令
    @Subscribe
    fun sendFlutterSyncIm(body: EventSyncFlutter) {
        MethodChannelManager.sendFlutterImData(body.data)
    }

    // 发送同步指令
    @Subscribe
    fun sendRefreshFlutterMeetingTopBar(body: EventFlutterRefreshMeetingTopbar) {
        MethodChannelManager.channelRefreshMeetingCheck(body.sessionId)
    }


    @Subscribe
    fun translateLocalImage(body: EventFluttertranslateLocalPath) {

        FlutterMultiActivity.open(FlutterConstant.INVITE_BY_SESSION)
        Timber.i("translateLocalImage ==> ${body.localPath}")
        vm.delayExecute (2000){
            val map = mapOf(
                "localPath" to body.localPath,
            )
            MethodChannelManager.refreshFriendApplyArguments(FlutterConstant.INVITE_BY_SESSION, map)
        }
    }

    @Subscribe
    fun onHomeReady(body: EventHomeReady){
//        isHomeReady = true
//        if (jumpData.isNotEmpty()) {
//            jumpData.offline2Flutter(this)
//            jumpData = ""
//        }
        (vm as FlutterHomeMultiActivity).vm.loadJPushRoute(vm,true)
    }

    @Subscribe
    fun closeFlutterCurrentPage(body: EventCloseWeb) {
        MethodChannelManager.closeFlutterPage()
    }

    @Subscribe
    fun getTiandituParam(b: EventTiandituParam) {
        MethodChannelManager.translateTiandiTuParam(b.json ?:"")
    }

    @Subscribe
    fun toQrCode(body: EventToQrCode) {
        MethodChannelManager.openFlutterPage(QR_CODE , mapOf(
            "type" to 2
        ))
    }
    @Subscribe
    fun refreshNotice(body: EventRefreshNotice) {
        MethodChannelManager.refreshNotice(body.type,body.orgId?:"")
    }

    @Subscribe
    fun getImByte(data: EventByte) {
        MethodChannelManager.translateByte(data.status?.toByteArray())
    }


    @Subscribe
    fun longConnectStatue(status: EventSocketStatus) {
        invokeFlutter(FlutterConstant.FLUTTER_SOCKET_STATUS , mapOf(
            "status" to (status.status ?: 0)
        ))
    }

    @Subscribe
    fun reConnect(b: EventReConnect) {
        invokeFlutterWithCallBack(FlutterConstant.FETCH_SOCKET_PARAM , callBack = {
            try {
                if (it == null) return@invokeFlutterWithCallBack
                Timber.i("reConnect.......$it")
                if (it is Map<*, *>){
                    val imtoken = it.get("imToken") as? String
                    val url = it.get("url") as? String
                    OkWebSocketService.reConnect(imtoken , url)
                }
            }catch (e: Exception){
                e.printStackTrace()
            }
        })
    }


    @Subscribe
    fun webb22Flutter(json: EventRouteFlutter) {
        invokeMainEngineFlutterByObj(FlutterConstant.OPEN_FLUTTER , json.json)
    }

    @Subscribe()
    fun bindOrUnBindWechat(b: EventBindWeChat) {
        Timber.i(" 绑定微信反馈到主页========== ${b.json}")
        val data: String = b.json ?:""
        val token = UserHolder.getAccessToken()
        if (StringUtils.isEmpty(token)) {
            toast("微信关联失败,请稍后重试")
            return
        }
        if (StringUtils.isNotBlankAndEmpty(data) && data.contains(":")) {
            val infoList = data.split(":")
            if (infoList.size < 3) return
            val openId = infoList[0]
            val unionid = infoList[1]
            val wxName = infoList[2]

            if (StringUtils.isNotBlankAndEmpty(unionid) && StringUtils.isNotBlankAndEmpty(openId)
                && StringUtils.isNotBlankAndEmpty(wxName)) {
                val map = hashMapOf<String, Any>()
                map["nickName"] = wxName
                map["wxAuthId"] = unionid
                map["wxOpenId"] = openId
                //type:1担当 2云资产
                map["type"] = 1
                LoginService.wxBindSafe(token, map)
                    .compose(ErrorTransformer.getInstance())
                    .subscribe(object : BaseSubscriber<Any>() {
                        override fun onError(ex: ApiException) {
                            toast("未关联")
                        }

                        override fun onComplete() {
                        }

                        override fun onNext(t: Any?) {
                            if (t != null) {
                                toast("绑定微信成功")
                                UserHolder.getCurrentUser()?.nickName = wxName
                            }
                        }
                    })
            } else {
                toast( "微信获取信息失败")
            }
        } else {
            toast("微信获取信息失败")
        }
    }


    // 仅供外部调用
    private fun checkUpgrade1(tip: Boolean) {
        Timber.i("checkUpgrade1.......")
        if (vm is FlutterHomeMultiActivity){
            val stacks = AppManager.single_instance.getAllActivity()
            val act = if (stacks.isEmpty() ) this else stacks.last()
            LoadingManager.showLoadingDialog(vm)
            val clientCode = VersionConfig().versionCode2  //发版修改位置6，config.gradle中的versionCode,每次发版手动加一
            PersonService.userStartApp("", BaseApplication.currentNetType, clientCode)//APP启动后检查更新，六合一接口调用
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<AppStartResult>() {
                    override fun onError(ex: ApiException) {
                        LoadingManager.dismissLoadingDialog()
                    }

                    override fun onComplete() {
                        LoadingManager.dismissLoadingDialog()
                    }

                    override fun onNext(result: AppStartResult?) {
                        LoadingManager.dismissLoadingDialog()
                        if (result != null) {
                            dealVersionSuccess(
                                AppVersionBean(
                                    downUrl = result.downUrl,
                                    status = result.status,
                                    auditing = result.auditing,
                                    versionName = result.versionName,
                                    currentDesc = result.currentDesc,
                                    desc = result.desc) , tip
                            )
                        }
                    }
                })
        }
    }

    /**
     * 获取im连接token
     * 获取到imToken后才能登录和获取离线数据
     * */
    private fun dealVersionSuccess(it: AppVersionBean , tip: Boolean = false) {
        MMKVUtil.saveString("appNewVersion", GsonUtil.toJson(it))
        Loggerr.i("软件更新", "====${GsonUtil.toJson(it)}")
        //先判断有没有当前版本的版本描述，因为不变，所以只需存储一次即可
        if (StringUtils.isNotBlankAndEmpty(it.currentDesc)) {
            val versionName = VersionConfig().versionName
            val versionContent = MMKVUtil.getString("appVersionNum$versionName")
            if (StringUtils.isEmpty(versionContent) || versionContent != it.currentDesc) {
                MMKVUtil.saveString("appVersionNum$versionName", it.currentDesc)
            }
        }

        if (vm is FlutterHomeMultiActivity){
            val stacks = AppManager.single_instance.getAllActivity()
            val act = if (stacks.isEmpty() ) vm else stacks.last()
            AppVersionCheckUpdateUtil.updateNewVersionDialog(act, it , tip )
        }

    }

}

fun ComponentActivity.delayExecute(delayTime: Long = 2000, execute: () -> Unit) {
    lifecycle.coroutineScope.launch {
        withContext(Dispatchers.Main) {
            delay(delayTime)
            execute()
        }
    }
}



