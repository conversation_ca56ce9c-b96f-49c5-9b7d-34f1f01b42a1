package com.joinutech.ddbes.flutter.model

import com.joinutech.ddbeslibrary.bean.PersonInfoBean
import com.joinutech.ddbeslibrary.bean.TokenBean
import io.flutter.plugin.common.MethodCall

/**
 * @Des：
 * @author: moon
 * @date: 9/11/23
 */

// flutter login成功后 传递给 原生的user和token 实体，并赋值
fun convertTokenAndUserInfo(call : MethodCall) : TokenBody {
    val birthday = call.argument("birthday") as String?
    val profession = call.argument("profession") as String?
    val address = call.argument("address") as String?
    val gender = (call.argument("gender") as Int?)?: 1
    val nickName = call.argument("nickName") as String?
    val mobile = (call.argument("mobile") as String?) ?:""
    val avatar = (call.argument("avatar") as String?) ?:""
    val token_type = (call.argument("token_type") as String?) ?:""
    val userId = call.argument("userId") as String?
    val imId = call.argument("imId") as String?
    val access_token = call.argument("access_token") as String?
    val refresh_token = call.argument("refresh_token") as String?
    val companyId = call.argument("companyId") as String?
    val name = (call.argument("name") as String?) ?:""
    val expires_in = (call.argument("expires_in") as Int?) ?: 0
    val email = call.argument("email") as String?
    val openId = (call.argument("openId") as String?) ?:""
    val intro = (call.argument("intro") as String?) ?:""
    val scope = (call.argument("scope") as String?) ?:""

    val userPerson = PersonInfoBean(userId ?:"",
        avatar,mobile,name, gender.toString() ,birthday,address, intro, email, profession ,companyId,nickName, openId)

    val tokenBean = TokenBean(token_type, access_token,refresh_token ,"" ,scope ,(expires_in.toLong()), 0)
    return TokenBody(userPerson , tokenBean)
}

class TokenBody(
    val person: PersonInfoBean, val tokenBean: TokenBean
)



