package com.joinutech.ddbes

import android.Manifest
import android.content.Context
import android.graphics.Bitmap
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.webkit.WebSettings
import android.widget.FrameLayout
import android.widget.TextView
import androidx.activity.viewModels
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.joinutech.common.helper.LoginHelper
import com.joinutech.ddbes.databinding.ActivityPrivacyBinding
import com.joinutech.ddbes.statics.StaticsHelper
import com.joinutech.ddbeslibrary.BuildConfig
import com.joinutech.ddbeslibrary.VersionConfig
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.AppManager
import com.joinutech.ddbeslibrary.utils.BottomDialogUtil
import com.joinutech.ddbeslibrary.utils.ConsValue
import com.joinutech.ddbeslibrary.utils.DeviceIdUtil
import com.joinutech.ddbeslibrary.utils.DeviceUtil
import com.joinutech.ddbeslibrary.utils.MMKVKEY
import com.joinutech.ddbeslibrary.utils.MMKVUtil
import com.joinutech.ddbeslibrary.utils.PermissionUtils
import com.joinutech.ddbeslibrary.utils.XUtil
import com.tencent.smtt.export.external.interfaces.WebResourceResponse
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers

class PrivacyActivity : MyUseBindingActivity<ActivityPrivacyBinding>() {

    val vm: PrivacyViewModel by viewModels()

    var webViewContainer: FrameLayout? = null

    override val contentViewResId: Int
        get() = R.layout.activity_privacy

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityPrivacyBinding {
        return ActivityPrivacyBinding.inflate(layoutInflater)
    }

    private var timeCount = 3
    private val mHandler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                0 -> {
                    if (timeCount == 0) {
                        binding.privacyConfirmTv.text = "我已阅读并同意本条款"
                        binding.privacyConfirmTv.isClickable = true
                        binding.privacyConfirmTv.setBackgroundResource(com.joinutech.ddbeslibrary.R.drawable.shape_2_main_blue)
                    } else {
                        binding.privacyConfirmTv.text = "我已阅读并同意本条款(${timeCount}s)"
                        binding.privacyConfirmTv.isClickable = false
                        binding.privacyConfirmTv.setBackgroundResource(com.joinutech.ddbeslibrary.R.drawable.shape_2_d5dfed)
                        val refreshMsg = Message.obtain()
                        refreshMsg.what = 0
                        timeCount--
                        sendMessageDelayed(refreshMsg, 1000)
                    }
                }
            }
        }
    }

    override fun initImmersion() {
        whiteStatusBarBlackFont()
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        setPageTitle("隐私政策")
    }

    override fun initView() {
        binding.privacyConfirmTv.setOnClickListener(this)
        binding.privacyCancelTv.setOnClickListener(this)

        webViewContainer = findViewById(R.id.privacy_content_webview)

        val webView = com.tencent.smtt.sdk.WebView(this)
        webView.setLayerType(View.LAYER_TYPE_HARDWARE,null)

        webViewContainer?.addView(webView)

        webView?.webViewClient=  object: com.tencent.smtt.sdk.WebViewClient(){
            override fun onPageStarted(p0: com.tencent.smtt.sdk.WebView?, p1: String?, p2: Bitmap?) {
                super.onPageStarted(p0, p1, p2)
            }

            override fun onPageFinished(p0: com.tencent.smtt.sdk.WebView?, p1: String?) {
            }

            override fun onReceivedError(view: com.tencent.smtt.sdk.WebView?, errorCode: Int, description: String?, url: String?) {
                showLog("--->加载页面 onReceivedError 错误码：$errorCode -- $description")

            }

            override fun onReceivedSslError(
                view: com.tencent.smtt.sdk.WebView?,
                handler: com.tencent.smtt.export.external.interfaces.SslErrorHandler?,
                error: com.tencent.smtt.export.external.interfaces.SslError?
            ) {
                handler?.proceed()
            }

            override fun onReceivedHttpError(
                view: com.tencent.smtt.sdk.WebView?,
                request: com.tencent.smtt.export.external.interfaces.WebResourceRequest?,
                errorResponse: WebResourceResponse?
            ) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    showLog("--->加载页面 onHttpError ${request?.url} -- ${errorResponse?.statusCode}")

                }
            }

        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP){
            webView.settings?.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW;
        }
        webView.loadUrl("https://mobile.ddbes.com/h5/privacy_android.html");
//        webView?.loadUrl("https://www.baidu.com/");
    }

    private fun getDeviceVersion() {
        ConsValue.deviceModel = DeviceUtil.getPlatformInfo(this)
        showLog("deviceModel info = ${ConsValue.deviceModel}")
        MMKVUtil.saveString(MMKVKEY.VERSION_NAME, BuildConfig.VERSION_NAME)
        MMKVUtil.saveInt(MMKVKEY.VERSION_CODE, BuildConfig.VERSION_CODE)
        val versionConfig = VersionConfig()
        val imei = MMKVUtil.getString(MMKVKEY.IMEI)
        showLog(
            "保存应用功能版本号: deviceImei = $imei," +
                    " vName = ${versionConfig.versionName} ," +
                    " vCode =${versionConfig.versionCode} ," +
                    " vCode2 =${versionConfig.versionCode2} ," +
                    "deviceModel = ${ConsValue.deviceModel}"
        )
    }


    override fun initLogic() {

        vm.privacyContentLiveData.observe(this) {

            binding.privacyContentTv.text = it

            val refreshMsg = Message.obtain()
            refreshMsg.what = 0
            mHandler.sendMessageDelayed(refreshMsg, 100)
        }

//        vm.loadPrivacyContent(this@PrivacyActivity)
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.privacy_cancel_tv -> {//点击拒绝
                AppManager.single_instance.AppExit(this , false)
            }
            R.id.privacy_confirm_tv -> {//点击同意

//                applyPermission()

                imei = DeviceIdUtil.getDeviceId(mContext, false)
                MMKVUtil.saveString(MMKVKEY.IMEI, imei)

                LoginHelper.onPrivacyRead()

                StaticsHelper.preInit(this)

                setResult(RESULT_OK)
                finish()
            }
        }
    }

    // 点击同意之后 申请一部分权限
    private fun applyPermission() {
        val perms = arrayOf(Manifest.permission.READ_PHONE_STATE ,
            Manifest.permission.READ_EXTERNAL_STORAGE ,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
        PermissionUtils.requestPermissionActivity(this, perms, hint = "",
            onSuccess =  {
                dealPhoneImeiPermission()

                getDeviceVersion()

                LoginHelper.onPrivacyRead()

                StaticsHelper.preInit(this)

                setResult(RESULT_OK)
                finish()

            }, onError = {
                imei = DeviceIdUtil.getDeviceId(mContext, false)
                MMKVUtil.saveString(MMKVKEY.IMEI, imei)

                preTipPermissionDialog(this , onAgree = {
                    LoginHelper.onPrivacyRead()
                    setResult(RESULT_OK)
                    finish()
                })

            } , preTips = "正常使用担当办公需要获取手机mac地址（Android Id），存储卡读写等权限",)
    }


    fun preTipPermissionDialog(context: Context , onAgree: () -> Unit) {
        val tips = "禁用权限后可能会导致某些功能不可用"
        val view = View.inflate(context, com.joinutech.ddbeslibrary.R.layout.dialog_pre_dialog_permission, null)

        val dialog = BottomDialogUtil.showBottomDialog(context, view, Gravity.CENTER)

        view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.confirm).text = "进入担当"
        view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.confirm).setOnClickListener {
            onAgree()
            dialog.dismiss()
        }

        view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.later).visibility = View.GONE

        view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.tv_version_code).text = tips
        //设置点击返回键不消失
        dialog.setCancelable(false)
        //设置点击屏幕不消失
        dialog.setCanceledOnTouchOutside(false)
        dialog.show()
    }


    private fun dealPhoneImeiPermission() {
        if (Build.VERSION.SDK_INT >= 29) run {
            imei = DeviceIdUtil.getDeviceId(mContext, false)
        } else {
            imei = DeviceIdUtil.getDeviceId(mContext)
        }
        MMKVUtil.saveString(MMKVKEY.IMEI, imei)
    }


    override fun showToolBar(): Boolean {
        return false
    }

    override fun openArouterReceive(): Boolean {
        return false
    }

    override fun onBackPressed() {

    }

    override fun onDestroy() {
        super.onDestroy()
        mHandler.removeCallbacksAndMessages(null)
    }

//    private val webviewClient =
}


class PrivacyViewModel : ViewModel() {

    val privacyContentLiveData = MutableLiveData<String>()

    fun loadPrivacyContent(context: Context) {
        viewModelScope.launch {
            withContext(Dispatchers.IO){
                val privacyContent= XUtil.readPrivacy(context)
                privacyContentLiveData.postValue(privacyContent)
            }
        }
    }

}