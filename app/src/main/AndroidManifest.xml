<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.joinutech.ddbes">


    <!--    在应用的AndroidManifest.xml添加如下<queries>标签-->
    <queries>
        <package android:name="com.tencent.mm" />
    </queries>

    <!--        android:label="@string/app_name"-->


    <application
        android:name="com.joinutech.ddbes.AppApplication"
        android:allowBackup="true"
        android:icon="@mipmap/icon"
        android:label="${flavor_name}"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:requestLegacyExternalStorage="true"
        android:theme="@style/AppTheme"
        android:testOnly="false"
        android:usesCleartextTraffic="true"
        tools:ignore="AllowBackup,GoogleAppIndexingWarning"
        tools:replace="android:allowBackup,label"
        tools:targetApi="m">

        <activity
            android:name="com.joinutech.ddbes.SplashActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/FullscreenTheme">


            <!--<intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="app.rtc_meeting.com"
                    android:pathPrefix="/app/activityCenter"
                    android:scheme="ddbes" />
            </intent-filter>-->

        </activity>

        <activity android:name=".PrivacyActivity" />

        <!--微信登录配置 start-->
        <activity
            android:name="com.joinutech.ddbes.wxapi.WXEntryActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:taskAffinity="com.joinutech.ddbes"
            android:theme="@style/wx_login_style" >

        </activity>

        <!--微信支付配置 start-->
        <activity
            android:name="com.joinutech.ddbes.wxapi.WXPayEntryActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:taskAffinity="com.joinutech.ddbes"
            android:theme="@style/wx_login_style" />
        <!--            android:theme="@android:style/Theme.Translucent.NoTitleBar" />-->

        <activity
            android:name="com.joinutech.ddbes.HomeActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait">

            <!--1、支持action参数需要EMUI 10.0.0以上，推送服务App版本为10.1.0及以上。-->
            <!--2、Push SDK 4.0版本支持将action参数与intent参数通过SDK提供的RemoteMessage.Notification.getClickAction()、
            RemoteMessage.Notification.getIntentUri()方法直接透传给应用，由应用自己处理。-->


        </activity>

        <activity android:name=".scheme.RtcMeetingSchemeActivity"
            android:launchMode="standard"
            android:theme="@style/RtcTranslucentStyle">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="app.rtc_meeting.com"
                    android:pathPrefix="/app/activityCenter"
                    android:scheme="ddbes" />
            </intent-filter>

        </activity>

        <!--微信登录配置 end-->
        <!-- 百度定位 -->
        <!--        <service-->
        <!--            android:name="com.baidu.location.f"-->
        <!--            android:enabled="true"-->
        <!--            android:process=":remote"/>-->
        <!-- publish -->
        <meta-data
            android:name="com.baidu.lbsapi.API_KEY"
            android:value="${baidu_ak}" />
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/rc_file_path"
                tools:replace="android:resource" />
        </provider>
        <!--华为-->
        <!--必须-->
        <!--        <meta-data-->
        <!--            android:name="push_kit_auto_init_enabled"-->
        <!--            android:value="false" />-->
        <!--        <service-->
        <!--            android:name=".HmsPushService"-->
        <!--            android:exported="false">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />-->
        <!--            </intent-filter>-->
        <!--        </service>-->
        <!--华为-->

        <!-- flutter start ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <activity
            android:name = "io.flutter.embedding.android.FlutterActivity"
            android:configChanges = "orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated = "true"
            android:theme = "@style/Theme.AppCompat.Light"
            android:windowSoftInputMode = "adjustResize"/>

        <activity
            android:name = "io.flutter.embedding.android.FlutterFragmentActivity"
            android:configChanges = "orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated = "true"
            android:theme = "@style/Theme.AppCompat.Light"
            android:windowSoftInputMode = "adjustResize"/>

        <activity
            android:name=".flutter.FlutterMultiActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait"
            android:exported="true"
            android:theme="@style/FullscreenTheme">
        </activity>

        <activity
            android:name=".flutter.FlutterHomeMultiActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait"
            android:launchMode="singleTop"
            android:exported="true"
            android:windowSoftInputMode = "adjustResize"
            android:theme="@style/FullscreenTheme">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:host="com.joinutech.ddbes"
                    android:path="/master"
                    android:scheme="ddbes" />
            </intent-filter>

            <intent-filter >
                <action android:name="ddbes.push.main.action" />
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>

        </activity>

        <!-- flutter   end ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->


<!--        <meta-data-->
<!--            android:name="com.huawei.hms.client.appid"-->
<!--            android:value="appid=101190163">-->
<!--        </meta-data>-->

<!--        <meta-data-->
<!--            android:name="sdk_version_vivo"-->
<!--            android:value="484"/>-->


        <receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver" />
        <receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED"/>
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON"/>
            </intent-filter>
        </receiver>


        <meta-data
            android:name="io.flutter.embedding.android.EnableImpeller"
            android:value="false"/>

    </application>

</manifest>