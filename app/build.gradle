apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply plugin: 'kotlin-kapt'
//apply plugin: 'AndResGuard'
//apply plugin: 'bugly'
apply plugin: 'com.huawei.agconnect'//华为推送

apply from: "${project.rootProject.file('projectDependencyGraph.gradle')}"



def checkTaskResult = false
project.gradle.startParameter.taskNames.forEach({ info ->
    println("current info = [" + info + "]")
    if (info.contains("base") || info.contains("Base")) {
        checkTaskResult = true
    }
})

project.afterEvaluate {
    getTasksByName("compileDebugKotlin", false).forEach {
        it.mustRunAfter("greendao")
    }
}

//bugly {
//    appId = "1673faa759"
//    appKey = "6b85bda5-86f7-427a-8987-1a5f263f7962"
//    appPackage = "com.joinutech.ddbes"
//    appVersion = rootProject.ext.android.versionName
//}

/**获取版本名，debug增加构建时间*/
//如果是发版，就返回config.gradle中的versionName
def getCurrentName() {
    def calendar = Calendar.getInstance()
    def build_time2 = String.format("-%d%02d%02d-%02d:%02d",
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH) + 1,
            calendar.get(Calendar.DAY_OF_MONTH),
            calendar.get(Calendar.HOUR_OF_DAY),
            calendar.get(Calendar.MINUTE)
    )
    def build_time = String.format("-%d%02d%02d",
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH) + 1,
            calendar.get(Calendar.DAY_OF_MONTH)
    )
    def runTasks = gradle.startParameter.taskNames
    def isRelease = false
    for (task in runTasks) {
        if (task.contains("release") || task.contains("Release")) {
            isRelease = true
            break
        }
    }
    if (isRelease) {
        return rootProject.ext.android.versionName
    } else {
        return rootProject.ext.android.versionName + build_time
    }
}

/**自动增长versionCode*/
//如果是发版，就返回version.properties中的versionCode自增1后的结果，
// 不是发版就返回不自增的原值。
def getCurrentCode() {
    def versionFile = file("../version.properties")
    if (versionFile.canRead()) {
        def props = new Properties()
        props.load(new FileInputStream(versionFile))
        def code = props["VERSION_CODE"].toInteger()
        def runTasks = gradle.startParameter.taskNames
        def isRelease = false
        for (task in runTasks) {
            if (task.contains("release") || task.contains("Release")) {
                isRelease = true
                break
            }
        }
        if (isRelease) {
            props["VERSION_CODE"] = (++code).toString()
            props.store(versionFile.newWriter(), null)
            return code
        } else {
            return code
        }
    } else {
        return rootProject.ext.android.versionCode
    }
}

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    dexOptions {
        incremental true
        javaMaxHeapSize "4g"
//        preDexLibraries = false

    }
    buildFeatures {
        buildConfig = true
    }

    namespace = "com.joinutech.ddbes" // 使用模块的实际包名

    android {
        packaging {
            dex {
                useLegacyPackaging true
            }
            jniLibs {
                useLegacyPackaging true
            }
        }
    }

    defaultConfig {
        applicationId "com.joinutech.ddbes"
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
//        versionCode rootProject.ext.android.versionCode
        versionCode getCurrentCode() //todo appUpdate 此处为应用市场发布时code
//        versionCode 101
        versionName getCurrentName()

        multiDexEnabled true

//        versionNameSuffix "_" + versionCode

        if (!rootProject.ext.isModule) {
            kapt {
                arguments {
                    arg("AROUTER_MODULE_NAME", project.getName())
                }
            }
        }
        //指定只打包哪个语言包
        resConfig("zh")
        ndk {
            //32位和64位的想打哪种包，就使用哪种架构即可，安装包位置1
            //cup类型.so库架构说明如下：
            //"armeabi"第5代、第6代的32位ARM处理器，早期的手机在使用，现在基本很少了
            //armeabi-v7a  第7代及以上的 32位ARM 处理器
            //'arm64-v8a',第8代、64位ARM处理器
            //"x86"，Intel 32位处理器，在平板、模拟器用得比较多
            // 'x86_64'  Intel 64位处理器，在平板、模拟器用得比较多
            //mips，基本没见过（极少用于手机可以忽略 ）
            //mips64，64位，基本没见过（极少用于手机可以忽略 ）
            //其他的架构酌情考虑
            //说明：你的应用不一定要支持所有 64 位架构，但对于支持的每种原生 32 位架构，应用都必须包含相应的 64 位架构
            // 对于 ARM 架构，32 位库位于 armeabi-v7a 中。 对应的 64 位库则位于 arm64-v8a 中
            // 对于 x86 架构，32 位库位于 x86 中，64 位库则位于 x86_64 中;
            //特殊说明，老版本的腾讯浏览的X5内核不能使用arm64-v8a架构，现在已经将文件预览SDK升级了

//            abiFilters "armeabi-v7a",'arm64-v8a'
            abiFilters 'arm64-v8a'//魅族市场已经不允许包含32位了
        }
        //配合上面32位和64位的配置，因为ddbeslibrary模块中已经有这个配置了，所以此处注释掉，安装包位置2
        /*sourceSets {
            main {
                jniLibs.srcDirs = ['libs']
            }
        }*/

        resConfigs "zh-rCN"

        manifestPlaceholders = [
                JPUSH_PKGNAME: "com.joinutech.ddbes",
                JPUSH_APPKEY :  "f46d3a39796a23b576ce2716",
                JPUSH_CHANNEL:  "ddbes_jpush_channel",
                MEIZU_APPKEY:  "MZ-05d4fa27a41c40d38f5e16dcc3460d0c",
                MEIZU_APPID:    "MZ-121946",
                XIAOMI_APPID:   "MI-2882303761518057550",
                XIAOMI_APPKEY :  "MI-5681805763550",
                OPPO_APPKEY :    "OP-a6d00b6c00af4ad9ab8aada13ae1e4d4",
                OPPO_APPID :     "OP-30205941",
                OPPO_APPSECRET: "OP-95ab619512df40e593dd6fcbd799dcc7",
                VIVO_APPKEY :    "8556caca-46f4-4d5b-9ed4-5d39d3bfa40a",
                VIVO_APPID :     "16578"
        ]

    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
//        useIR = true
    }

    repositories {
        flatDir {
            dirs 'libs', '../ddbeslibrary/libs', '../task/libs', '../vc_lib/libs',
                    '../addressbook/libs'

        }
    }

    packagingOptions {
        exclude 'AndroidManifest.xml'
        merge 'classes.jar'
        pickFirst '**/libc++_shared.so'
//        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
//        pickFirst 'lib/arm64-8a/libc++_shared.so'
//        pickFirst 'lib/x86/libc++_shared.so'
//        pickFirst 'lib/x86_64/libc++_shared.so'
//        exclude 'lib/arm64-v8a/libc++_shared.so'
        doNotStrip '*/armeabi/libYTCommon.so'
        doNotStrip '*/armeabi-v7a/libYTCommon.so'
        doNotStrip '*/x86/libYTCommon.so'
        doNotStrip '*/arm64-v8a/libYTCommon.so'

//        Flutter 依赖
//        pickFirst 'com/tencent/cos/xml/model/tag/eventstreaming/RequestProgress.class'
//        pickFirst 'com/tencent/cos/xml/CosXmlSimpleService.class'
//        pickFirst 'com/tencent/cos/xml/CosXmlSimpleService$1.class'
//        pickFirst 'com/tencent/cos/xml/SimpleCosXml.class'
    }

    signingConfigs {
        // 生产签名
        relealse {
            keyAlias 'ddbes'
            keyPassword 'joinu666'
            storeFile file("../keystore/joinutech_ddbes_ketstore.jks")
            storePassword 'joinu666'
            v1SigningEnabled true
            v2SigningEnabled true
        }

        debug {
            keyAlias 'ddbes'
            keyPassword 'joinu666'
            storeFile file("../keystore/joinutech_ddbes_ketstore.jks")
            storePassword 'joinu666'
        }
    }

//    flavorDimensions "version"
//
//    productFlavors {
//        dev {
//            dimension "version"
//            versionNameSuffix "-demo" + versionName + "_" + versionCode
//        }
//        prod {
//            dimension "version"
//            versionNameSuffix "-prod" + versionName + "_" + versionCode
//        }
//    }

    buildTypes {
        release {
            buildConfigField "boolean", "LOG_DEBUG", "false"
            minifyEnabled true
            zipAlignEnabled true
            shrinkResources true
            pseudoLocalesEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.relealse
//            multiDexKeepProguard file('multidex-config.pro')
            android.applicationVariants.all { variant ->
                variant.outputs.all {
//                    outputFileName = "CBA_${variant.versionName}_${releaseTime()}_${variant.flavorName}_${variant.buildType.name}.apk"
                    outputFileName = "${getReleaseName()}.apk"
                }
            }
        }

        debug {
            // 显示Log
            buildConfigField "boolean", "LOG_DEBUG", "true"
            minifyEnabled true//false
            zipAlignEnabled false
            shrinkResources false//false
            pseudoLocalesEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debug
//            defaultConfig.versionName = defaultConfig.versionName + build_time
//            multiDexKeepProguard file('multidex-config.pro')
//            versionNameSuffix "_debug_${getTime()}"
        }

        profile {
            initWith debug
        }

    }

    testOptions {
        unitTests {
            returnDefaultValues = true
        }
    }

    lintOptions {
        checkReleaseBuilds true
        abortOnError true
    }
    flavorDimensions 'app'
    productFlavors {
        base {
            manifestPlaceholders flavor_name: '担当办公', baidu_ak: 'Gl21Mn0u1xZy56aRjy75TZWTToQSoY9h'
            dimension 'app'
            applicationId "com.joinutech.ddbes"
        }
        future {
            manifestPlaceholders flavor_name: '班次打卡', baidu_ak: 'EDGGIBwNXG73eBKu3SujdBewMDXDdXkM'
            dimension 'app'
            applicationId "com.joinutech.ddbes.attend"
        }
        kingdee {
            manifestPlaceholders flavor_name: '金蝶测试', baidu_ak: 'jToiNXrfupC5GteWyx9nX2EtCbe9v7by'
            dimension 'app'
            applicationId "com.joinutech.ddbes.test"
        }
    }
//    packagingOptions {//单独加的，因为报错 More than one file was found with OS independent path
//        pickFirst 'res/values/values.xml'
//    }

    viewBinding{
        enable true
    }

}

def getTime() {
    return new Date().format("yyyyMMdd-HHmm", TimeZone.getDefault());
}

def getReleaseName() {
    def runTasks = gradle.startParameter.taskNames
    def tag = "ddbes"
    for (task in runTasks) {
        if (task.contains("release") || task.contains("Release")) {
            if (task.contains("future") || task.contains("Future")) {
                tag = "future"
            } else if (task.contains("kingdee") || task.contains("Kingdee")) {
                tag = "kingdee"
            }
            break
        }
    }

    return tag + "_" + android.defaultConfig.versionName + '_' + android.defaultConfig.versionCode + '_' + getTime()
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
//    androidTestImplementation('androidx.test.espresso:espresso-core:3.1.0-alpha4', {
//        exclude group: 'com.android.support', module: 'support-annotations'
//    })

    api project(':ddbeslibrary')

    if (!rootProject.ext.isModule) {
        kapt supportLibs.arouter_compiler
        implementation project(':login')
        implementation project(':message')
        implementation project(':work')
        implementation project(':addressbook')
        implementation project(':Personal')
//        implementation project(':task')
        implementation project(':clouddoc')
        implementation project(':rtcmeeting')
    }
    kapt supportLibs.dagger_compiler
    implementation supportLibs.core_ktx
    implementation supportLibs.viewmodel_ktx
    implementation supportLibs.stdlib_jdk7//代替
//    compile "androidx.core:core-ktx:+"
//    compile "androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0"
//    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"

//    开发过程中启用，直接用debugImplementation 需要切换版本类型到release才会起作用
    if (rootProject.ext.isDebug) {
        debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.4'
    }

    def flutter_version = dd_flutter_model_version // dev
    debugImplementation "com.joinutech.ddbes:flutter_debug:$flutter_version"
    profileImplementation "com.joinutech.ddbes:flutter_profile:$flutter_version"
    releaseImplementation "com.joinutech.ddbes:flutter_release:$flutter_version"


}

def isWindows() {
    return org.gradle.internal.os.OperatingSystem.current().isWindows()
}

/*andResGuard {
    // mappingFile = file("./resource_mapping.txt")
    mappingFile = null
    // 当你使用v2签名的时候，7zip压缩是无法生效的。
    use7zip = true
    useSign = true
    // 打开这个开关，会keep住所有资源的原始路径，只混淆资源的名字
    keepRoot = false
    // 白名单
    whiteList = [
            // for your icon
            "R.mipmap.icon",
//            "R.mipmap.select_*",
            *//*hms AndResGuard清单 home*//*
            "R.string.hms*",
            "R.string.connect_server_fail_prompt_toast",
            "R.string.getting_message_fail_prompt_toast",
            "R.string.no_available_network_prompt_toast",
            "R.string.third_app_*",
            "R.string.upsdk_*",
            "R.layout.hms*",
            "R.layout.upsdk_*",
            "R.drawable.upsdk*",
            "R.color.upsdk*",
            "R.dimen.upsdk*",
            "R.style.upsdk*",
            "R.string.agc*",
            *//*hms AndResGuard清单 end*//*
            "R.id.scroll_layout",
            "R.id.divider",
            "R.id.name_textview",
            "R.id.version_textview",
            "R.id.appsize_textview",
            "R.id.allsize_textview",
            "R.id.content_textview",
            "R.id.third_app_dl_progress_text",
            "R.id.cancel_bg",
            "R.id.third_app_dl_progressbar",
            "R.id.upsdk*",
            "R.string.upsdk*",
            "R.layout.upsdk*"
    ]
    // 压缩文件类型
    compressFilePattern = [
            "*.png",
            "*.jpg",
            "*.jpeg",
            "*.gif",
    ]

    use7zip = isWindows()

    sevenzip {
//        if (isWindows()) {
//            artifact = 'com.tencent.mm:SevenZip:1.2.16'
//        }
        artifact = 'io.github.leon406:SevenZip:********'
    }

    finalApkBackupPath = "${project.rootDir}/release/${getReleaseName()}.apk"

}*/

String storageUrl = System.env.FLUTTER_STORAGE_BASE_URL ?: "https://storage.googleapis.com"

repositories {
    mavenCentral()

    maven {
//        url '/Users/<USER>/Documents/joinu/flutter_mixed/flutter_mixed/build/host/outputs/repo'
        url 'http://**********:8081/repository/maven-releases/'
        credentials {
            username 'admin'
            password '123456'
        }
        allowInsecureProtocol = true
    }

    maven {
        url "$storageUrl/download.flutter.io"
    }
}


