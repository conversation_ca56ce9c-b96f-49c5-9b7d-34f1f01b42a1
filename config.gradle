ext {
    isModule = false//false:作为Lib组件存在， true:作为application独立组件存在
    isDebug = false//是否开发模式

    // 配置修改
    dd_version_code = 169
    dd_version_name = "5.0.8"

//    dd_flutter_model_version = "1.1.35"  //dev
    dd_flutter_model_version = "2.2.5"  //prod

    android = [
            compileSdkVersion: 34,
            buildToolsVersion: "30.0.0",
            minSdkVersion    : 24,
            //说明，targetSdkVersion的作用是兼容旧的api版本，
            // 防止同一api在更高的Android系统版本的手机上运行时出现问题，
            // 原理就是通过targetSdkVersin字段来区分同一api在不同版本系统上的行为，
            // 所以更新targetSdkVersion的值，就意味着要对变更区间内的Android系统版本做充分的兼容性测试,
            // 一般来讲最主要的就是要对目标版本（targetSdkVersion）的Android系统做兼容性测试；
            //compileSdkVersion >= targetSdkVersion > minSdkVersion
            targetSdkVersion : 30,
            versionCode      : dd_version_code,  // 后端判断标准
            versionName      : dd_version_name // 后端判断标准
    ]
    supportLibVersion = '1.1.0'
    retrofitVersion = "2.7.2"
    rxlifecycle = "3.1.0"
    glide = "4.11.0"
    gson = "2.8.6"
    glide_transformations = "4.1.0"
    logging_interceptor = "3.10.0"
    glassfish_annotation = "10.0-b28"
//    SmartRefreshLayout = "1.1.2"
    eventbus = "3.2.0"
//    dagger = "2.27"
    dagger = "2.46.1"
    greendao = "3.3.0"
    multidex = "2.0.1"
    nineoldandroids = "2.4.0"
    arouter = "1.5.2"
    arouter_compiler = "1.5.2"
    immersionBar = "3.0.0"
    wechat_mta = "5.4.0"
    wechat = "6.7.0"
    autosize = "1.2.1"
    mmkv = "1.0.24"
    pickers = "1.0.3"
//    hms_version = '4.0.2.300'
    hms_version = '6.3.0.302'
    meizu_push = '3.9.0'
    appcompat = '1.3.0'
    flowlayout = '1.1.2'
//    bugly = '3.1.0'
    bugly = '4.1.9'
//    bugly_native = '3.8.0'
    viewPager2 = "1.0.0"
    easyFloat = "1.3.0"
    calendar_view = "3.6.7"
    constraintLayout = "2.0.4"
//    kotlin_version = "1.4.32"
    kotlin_version = "1.8.10"
//    kotlin_reflect = "1.4.32"
    kotlin_reflect = "1.8.10"

    //rootProject.ext.cfgs.compileSdkVersion
    cfgs = [
            compileSdkVersion             : 31,
            buildToolsVersion             : "30.0.0",
            minSdkVersion                 : 24,
            targetSdkVersion              : 30,
            versionCode                   : 30,
            versionName                   : "2.6.0",
            ucropVersionCode              : 24,
            ucropVersionName              : "2.2.4-non-native",

            //open version control
            androidSupportVersion         : "27.0.2",
            version_recyclerview          : "1.1.0",
            version_appcompat             : "1.3.0",
            localbroadcastmanager         : "1.0.0",

            camerax_view                  : "1.0.0-alpha22",
            camerax_version               : "1.1.0-alpha02",
            camera_core_version           : "1.0.0-beta03",
            experimental_version          : "1.1.0-rc01",
            futures_version               : "1.1.0",

            // ucrop
            androidx_appcompat_version    : "1.2.0",
            androidx_exifinterface_version: "1.3.2",
            androidx_transition_version   : "1.3.1",

            // okio
            okio_version                  : "2.9.0",

            // glide
            glide_version                 : "4.11.0",

            // picasso
            picasso_version               : "2.71828"

    ]
//supportLibs.stdlib_jdk7
//supportLibs.hms_push
    supportLibs = [
            material              : "com.google.android.material:material:$appcompat",
            appcompat             : "androidx.appcompat:appcompat:$appcompat",
            constraintLayout      : "androidx.constraintlayout:constraintlayout:$constraintLayout",
            multidex              : "androidx.multidex:multidex:$multidex",
            nineoldandroids       : "com.nineoldandroids:library:$nineoldandroids",
            // Retrofit
            retrofit2             : "com.squareup.retrofit2:retrofit:$retrofitVersion",
            converter_gson        : "com.squareup.retrofit2:converter-gson:${retrofitVersion}",
            adapter_rxjava2       : "com.squareup.retrofit2:adapter-rxjava2:${retrofitVersion}",
            gson                  : "com.google.code.gson:gson:$gson",
            rxlifecycle_components: "com.trello.rxlifecycle3:rxlifecycle-components:$rxlifecycle",
            //glide
            glide                 : "com.github.bumptech.glide:glide:$glide",
            glide_compiler        : "com.github.bumptech.glide:compiler:$glide",
            glide_transformations : "jp.wasabeef:glide-transformations:$glide_transformations",
            logging_interceptor   : "com.squareup.okhttp3:logging-interceptor:$logging_interceptor",
            glassfish_annotation  : "org.glassfish:javax.annotation:$glassfish_annotation",
//            SmartRefreshLayout    : "com.scwang.smartrefresh:SmartRefreshLayout:$SmartRefreshLayout",
            refresh_layout        : 'io.github.scwang90:refresh-layout-kernel:2.0.5',      //核心必须依赖
            refresh_header        : 'io.github.scwang90:refresh-header-classics:2.0.5',      //经典刷新头
            refresh_footer        : 'io.github.scwang90:refresh-footer-classics:2.0.5',      //经典加载
            eventbus              : "org.greenrobot:eventbus:$eventbus",
            immersionBar          : "com.gyf.immersionbar:immersionbar:$immersionBar",
            //Dagger
            dagger                : "com.google.dagger:dagger:$dagger",
            dagger_compiler       : "com.google.dagger:dagger-compiler:$dagger",
            //Arouter
            arouter               : "com.alibaba:arouter-api:$arouter",
            arouter_compiler      : "com.alibaba:arouter-compiler:$arouter_compiler",
            //greenDao
            greendao              : "org.greenrobot:greendao:$greendao",
            autosize              : "me.jessyan:autosize:$autosize",
            mmkv                  : "com.tencent:mmkv:$mmkv",
            pickers               : "com.github.addappcn:android-pickers:$pickers",
            flowlayout            : "com.hyman:flowlayout-lib:$flowlayout",
            bugly                 : "com.tencent.bugly:crashreport:$bugly",//bugly使用
//            nativecrashreport     : "com.tencent.rqd:nativecrashreport:$bugly_native",//bugly使用
            viewPager2            : "androidx.viewpager2:viewpager2:$viewPager2",
            easyFloat             : "com.github.princekin-f:EasyFloat:$easyFloat",
            switch_button         : 'com.kyleduo.switchbutton:library:2.0.0',
            core_ktx              : 'androidx.core:core-ktx:1.3.2',
            viewmodel_ktx         : 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.4.0-alpha01',
//            calendar_view         : "com.haibin:calendarview:$calendar_view",
            //LiveData
            lifecycle_extensions  : 'androidx.lifecycle:lifecycle-extensions:2.2.0',
            recyclerview          : 'androidx.recyclerview:recyclerview:1.1.0',
//            wechat                : "com.tencent.mm.opensdk:wechat-sdk-android-without-mta:$wechat",
            wechat                : "com.tencent.mm.opensdk:wechat-sdk-android:6.8.0",//接入微信支付时升级SDK版本，适配android11
            meizu                 : "com.meizu.flyme.internet:push-internal:$meizu_push",
            hms_push              : "com.huawei.hms:push:$hms_version",

            //经过测试，kotlin-stdlib-jdk7的版本是跟随kotlin-gradle-plugin设置的版本走的
            stdlib_jdk7           : "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version",
//            stdlib_jdk7           : "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version",
            kotlin_reflect        : "org.jetbrains.kotlin:kotlin-reflect:$kotlin_reflect",

//            ktx 扩展
            act_ktx               : "androidx.activity:activity-ktx:1.1.0",
            fragment_ktx          : "androidx.fragment:fragment-ktx:1.3.4"

    ]

    push_config = [
            "MI_APP_ID"    : "appId=2882303761518057550",
            "MI_APP_KEY"   : "appKey=5681805763550",
            "MEIZU_APP_ID" : "121946",
            "MEIZU_APP_KEY": "05d4fa27a41c40d38f5e16dcc3460d0c",
            //oppo的注册推送是用secret和key完成
            "OPPO_APP_ID"  : "a6d00b6c00af4ad9ab8aada13ae1e4d4",
            "OPPO_APP_KEY" : "95ab619512df40e593dd6fcbd799dcc7",
            "VIVO_APP_ID"  : "16578",
            "VIVO_APP_KEY" : "8556caca-46f4-4d5b-9ed4-5d39d3bfa40a",
            "PACKAGE_NAME" : "com.joinutech.ddbes"
    ]
}