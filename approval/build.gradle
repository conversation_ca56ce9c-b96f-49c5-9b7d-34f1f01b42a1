if (rootProject.ext.isModule) {
    apply plugin: 'com.android.application'
} else {
    apply plugin: 'com.android.library'
}
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply plugin: 'kotlin-kapt'

android {
    namespace = "com.joinutech.approval" // 使用模块的实际包名
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    defaultConfig {
        if (rootProject.ext.isModule) {
            applicationId "com.joinutech.approval"
        }
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
//        versionCode rootProject.ext.android.versionCode
//        versionName rootProject.ext.android.versionName
//        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
        kapt {
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }
    }

    repositories {
        flatDir {
            dirs '../ddbeslibrary/libs'
        }
    }

    lintOptions {
        abortOnError true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
//        useIR = true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    dexOptions {
        incremental true
        javaMaxHeapSize "4g"
    }
    viewBinding{
        enable true
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api project(':ddbeslibrary')

    if (!rootProject.ext.isModule) {
        kapt supportLibs.arouter_compiler
    }
    api supportLibs.arouter
    implementation 'io.github.luizgrp.sectionedrecyclerviewadapter:sectionedrecyclerviewadapter:2.1.0'
    implementation supportLibs.core_ktx
    implementation supportLibs.viewmodel_ktx
    implementation supportLibs.stdlib_jdk7
    implementation supportLibs.appcompat
    implementation supportLibs.material
    implementation supportLibs.constraintLayout//代替
//    compile "androidx.core:core-ktx:+"
//    compile "androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0"
//    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
}
repositories {
    mavenCentral()
}
