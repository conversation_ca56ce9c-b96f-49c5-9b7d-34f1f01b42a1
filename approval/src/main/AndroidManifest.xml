<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.joinutech.approval">

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.INTERNET" />

    <application
        android:allowBackup="true"
        android:supportsRtl="true">
        <activity
            android:name=".AprSignatureActivity"
            android:configChanges="orientation"
            android:screenOrientation="landscape"
            android:exported="false" />
        <activity
            android:name=".AprApproveAddActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".AprHelpActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".temp.AprModelSettingActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".org.SelectPersonActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".org.SelectDeptActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".quick.QuickAprResultActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".AprCommentActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".AprLaunchActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".AprDetailActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" /> <!-- android:windowSoftInputMode="adjustPan|stateHidden" -->
        <activity
            android:name=".quick.QuickAprActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />

        <activity
            android:name=".func.AprFuncActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".func.ApproveWebActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".func.AprFuncSetActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".temp.AprTempSetActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".temp.AprTempActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".SelectApprovalPersonActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".CooperationCompanyDetailActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_path"
                tools:replace="android:resource" />
        </provider>
    </application>

</manifest>