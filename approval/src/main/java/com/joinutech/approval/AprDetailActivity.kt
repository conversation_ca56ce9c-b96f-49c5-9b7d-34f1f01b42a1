package com.joinutech.approval

import android.annotation.SuppressLint
import android.app.Activity
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.view.*
import android.widget.ImageView
import android.widget.PopupWindow
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.Postcard
import com.alibaba.android.arouter.facade.callback.NavigationCallback
import com.alibaba.android.arouter.launcher.ARouter
import com.google.gson.reflect.TypeToken
import com.joinutech.approval.adapter.ApprovalTypeSelectDialogAdapter
import com.joinutech.approval.aprhistory.AprInputDialog
import com.joinutech.approval.aprhistory.Codes
import com.joinutech.approval.data.*
import com.joinutech.approval.databinding.ActivityAprDetailBinding
import com.joinutech.approval.temp.ApprovalTypeDialogBean
import com.joinutech.approval.utils.MsgType
import com.joinutech.approval.utils.RouteApr
import com.joinutech.common.adapter.MultiTypeAdapter
import com.joinutech.common.provider.FilePreviewProvider
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.joinutech.ddbeslibrary.bean.TaskDetailMemberBean
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.widget.activity.TaskImagePreviewActivity
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.lang.ref.WeakReference


/**
 * @className: AprDetailActivity
 * @desc: 审批详情页面
 * @author: zyy
 * @date: 2019/8/2 9:12
 * @company: joinUTech
 * @leader: ke
 */
@Deprecated("no used , change to web")
//@Route(path = RouteApr.APR_DETAIL_PAGE)//========已经不用了，被web替换了tcp
class AprDetailActivity(override val contentViewResId: Int = R.layout.activity_apr_detail)
    : AprBaseActivity<ActivityAprDetailBinding>() {

    override fun initImmersion() {
        mImmersionBar?.apply {
            keyboardEnable(true).keyboardMode(
                    WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        }
    }

    private var companyId: String? = null
    private var approveId: String = ""
    private var isCooperationCompany: Boolean = false

    /**通知跳转审批详情*/
    private var isFromNotice = false

    /**审批评论提醒跳转，加载数据完成后，显示评论信息*/
    private var isComment = false

    /**大类型*/
    private var modelIndex: Int = 1

    /**返回时更新列表项索引*/
    private var listIndex: Int = -1

    /**详情页状态变化
     * -1 未加载成功
     * 0 加载成功，更新已读，更新列表项
     * 1 加审在后边 2 加审在前边
     * 3 撤回
     * 4 重新提交       成功，刷新列表
     * 5 处理完成，刷新列表
     * */
    private var resultType = -1

    private lateinit var refreshLayout: SmartRefreshLayout
    private lateinit var rvList: RecyclerView
    private val propertyList = arrayListOf<WidgetInfo>()
    private var handler = MyStaticHandler(WeakReference(this))
    private var isAddExternalContact = 0

    override fun initView() {
        if (intent.extras != null) {
            if (intent.extras!!.containsKey("companyId")) {
                companyId = intent.extras!!.getString("companyId")
            }
            if (intent.extras!!.containsKey("approveId")) {
                approveId = intent.extras!!.getString("approveId") ?: ""
            }
            if (intent.extras!!.containsKey("modelIndex")) {
                modelIndex = intent.extras!!.getInt("modelIndex")
            }
            if (intent.extras!!.containsKey("listIndex")) {
                listIndex = intent.extras!!.getInt("listIndex")
            }
            if (intent.extras!!.containsKey("isCooperationCompany")) {
                isCooperationCompany = intent.extras!!.getBoolean(
                        "isCooperationCompany", false)
            }
            isFromNotice = intent.extras!!.getBoolean("isFromNotice", false)
            isComment = intent.extras!!.getBoolean("isComment", false)
        }
        XUtil.hideView(findViewById<View>(R.id.iv_apr_result))
        refreshLayout = findViewById(R.id.srl_layout)
        refreshLayout.setEnableRefresh(true)
        refreshLayout.setEnableLoadMore(true)
        refreshLayout.setRefreshHeader(CustomHeader(this))
        refreshLayout.setOnRefreshListener {
            page = startPage
            refreshLayout.finishRefresh(1200)
            loadComment()//下拉刷新评论信息
        }

        refreshLayout.setOnLoadMoreListener {
            page++
            refreshLayout.finishLoadMore(1200)
            loadComment()// 上拉加载评论信息
        }
        rvList = findViewById(R.id.rv_list)
    }

    private var pop: PopupWindow? = null

    @SuppressLint("InflateParams")
    private fun showPop() {
        if (pop == null) {
            val mLayoutInflater = LayoutInflater.from(this)
            val contentView = mLayoutInflater.inflate(R.layout.layout_pop_more, null)
            contentView.findViewById<View>(R.id.one_select).visibility = View.VISIBLE
            contentView.findViewById<View>(R.id.two_select).visibility = View.GONE
            contentView.findViewById<View>(R.id.cl_export).setOnClickListener {
                pop!!.dismiss()
                recallApr()
            }
            pop = PopUtil.buildPop(contentView)
        }
        PopUtil.showAtDown(pop!!, iv_rightTitle!!)
    }

    private var aprDetailData: ApprovalDetailData? = null

    override fun initLogic() {
        super.initLogic()
        initObserve()
        if (StringUtils.isNotBlankAndEmpty(approveId)) {
            loadAprDetail()//加载详情
        } else {
            toastShort("未找到对应的审批详情")
            finish()
        }
        binding.bottomApprovalToDoHint.setOnClickListener(this)
    }

    @SuppressLint("SetTextI18n")
    private fun initObserve() {
        /**modelIndex:1.我发起的；2.我审批的，3.抄送我的*/
        /**详情回调*/
        viewModel?.getAprDetailResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                aprDetailData = it
                isAddExternalContact = it.approveInfo.outApprove
                if (isCooperationCompany && it.endTime != 0L && it.endTime != 1L) {
                    binding.topEndTime.visibility = View.VISIBLE
                    val time = XUtil.turnToTimeStr(it.endTime, DEFAULT_TIME_FORMAT_PATTERN)
                    binding.topEndTime.text = "此审批将于${time}失效"
                    if (it.endTime < System.currentTimeMillis() && isCooperationCompany) {
                        //身份是外部联系人的审批，失效时间小于当前时间,则此审批已失效
                        ToastUtil.showCustomToast(null, mContext!!, true,
                                "此审批已失效，不可查看")
                        EventBusUtils.sendEvent(EventBusEvent(
                                EventBusAction.Event_REFRESH_COOPERATION_COMPANY_DETAIL, ""))
                        finish()
                    }
                } else {
                    binding.topEndTime.visibility = View.GONE
                }
                page = startPage
                pageInited = false
                loadComment()//获取详情后获取评论信息

                // 抄送我的，我发起已完成的，待我审批的（需要处理完成后才会更新），
                if (modelIndex == 3 || (modelIndex == 1 && aprDetailData!!.approveInfo.stteId == 1)) {// 抄送或者我发起的已完成,这个.size是为了不报错随便加的
                    showLog("刷新已读状态，更新我发起已完成或者抄送我的列表页面")
                    EventBusUtils.sendEvent(EventBusEvent(MsgType.APR_DETAIL_READED.name, null))
                }
                if (resultType < 0) {// 初始状态时设置为0，其他状态时，以其他状态为准
                    resultType = 0//标记已读
                }
            }, onError = { _, _ ->
                toastShort("未找到对应的审批详情")
                finish()
            }, onDefault = { msg ->
                toastShort("未找到对应的审批详情")
                finish()
            })
        })

        /**评论回调*/
        viewModel?.aprCommentListResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                if (page == 1) {
                    commentList.clear()
                }
                commentList.addAll(it)
                refreshLayout.setEnableLoadMore(it.size == pageLength)
                if (!pageInited) {
                    iniViewWithData()//审批评论获取成功后，判断是否初始化审批内容部分，没有初始化则初始化它
                } else {
                    propertyAdapter.notifyItemChanged(propertyList.lastIndex)
                }
            }, onError = { code, msg ->
                toastShort(msg)
                if (page > 1) {
                    page--
                }
                if (!pageInited) {
                    iniViewWithData()//评论获取失败后判断是否初始化，如果未初始化，则初始化审批内容部分显示
                }
            }, onDefault = { msg ->
                toastShort(msg)
                if (page > 1) {
                    page--
                }
                if (!pageInited) {
                    iniViewWithData()//评论获取失败后判断是否初始化，如果未初始化，则初始化审批内容部分显示
                }
            })

        })

        /**撤回回调*/
        viewModel?.recallAprResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                toastShort("撤回成功")
                //            EventBusUtils.sendEvent(EventBusEvent(MsgType.APR_RECALL_SUCCESS.name, null))
                if (resultType < 3) {
                    resultType = 3// 撤回成功后需要刷新列表
                }
                loadAprDetail()//撤回后更新详情信息
            }, onError = { _, msg ->
                toastShort(msg)
            }, onDefault = { msg ->
                toastShort(msg)
            })
        })

        /**处理审批*/
        viewModel?.dealAprResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                toastShort("审批成功")
                if (currentAprProcessType == -1 || currentAprProcessType == -2) {
//                    ApprovalUtil().updateAprData(this, approveId, currentAprProcessType) {}
                }
//            EventBusUtils.sendEvent(EventBusEvent(MsgType.APR_PROCESS_OVER.name, data = approveId!!))
//            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST, ""))
                if (resultType < 5) {
                    resultType = 5
                }
                if (isFromNotice) {
                    //从消息进来的审批详情，当前审批完成后需知还有多少审批跳至审批列表
                    loadUnCompleteApprovalCount()
                } else {
                    showApprovalToDoHint(false)
                }
                loadAprDetail()//审批成功后刷新详情
            }, onError = { _, msg ->
                toastShort(msg)
            }, onDefault = { msg ->
                toastShort(msg)
            })
        })

        /**催办审批*/
        viewModel?.remindAprResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                if (it) {
                    toastShort("成功对当前待处理人员进行催办")
                } else {
                    toastShort("30分钟内已使用过催办，请勿连续使用")
                }
            }, onError = { _, msg ->
                toastShort(msg)
            }, onDefault = { msg ->
                toastShort(msg)
            })

        })

        /**重新提交校验回调*/
        viewModel?.reCommitCheckResult!!.observe(this, Observer { result ->
//            hideLoading()
//            RequestHelper.onResponse(result, onSuccess = {
//                if (it) {
//                    // TODO 只传递审批id，重新提交传递文本控件内容
//                    val reCommitType = arrayOf(1, 2)
//                    val reCommitData = hashMapOf<String, String>()
//                    for (widget in propertyList) {
//                        if (widget.type in reCommitType && widget.content != null
//                                && widget.content!!.isNotBlank()) {
//                            reCommitData[widget.widgetId!!] = widget.content!!
//                        }
//                    }
//                    val bundle = Bundle()
//                    if (reCommitData.size > 0) {
//                        bundle.putString("widgets", GsonUtil.toJson(reCommitData))
//                    }
//                    bundle.putString("modelId", aprDetailData!!.approveInfo.modelId)
//                    bundle.putString("approveId", approveId)
//                    jump(RouteApr.APR_LAUNCH_CREATE, bundle)
//                } else {
//                    showTip("此审批模板已停用、修改或删除\n无法重新提交")
//                }
//            }, onError = { _, msg ->
//                toastShort(msg)
//            }, onDefault = { msg ->
//                toastShort(msg)
//            })
        })

        /**未处理审批数量获取*/
        viewModel?.unCompleteApprovalCountResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                showApprovalToDoHint(it != 0L, it.toInt())
            }, onError = { _, msg ->
                toastShort(msg)
                showApprovalToDoHint(false)
            }, onDefault = { msg ->
                toastShort(msg)
                showApprovalToDoHint(false)
            })
        })

    }

    private fun loadUnCompleteApprovalCount() {
        if (StringUtils.isNotBlankAndEmpty(companyId)) {
            getLoadingDialog("", false)
            viewModel?.waitDealApproveCount(companyId!!)
        } else {
            showApprovalToDoHint(false)
        }
    }

    @SuppressLint("SetTextI18n")
    fun showApprovalToDoHint(isShow: Boolean, num: Int = 0) {
        if (isShow) {
            binding.bottomApprovalToDoHint.visibility = View.VISIBLE
            binding.approvalNum.text = "当前团队中还有${num}条审批待处理"
            //3秒后如果不处理的话自动消失此按钮
            handler.postDelayed({
                handler.sendEmptyMessage(0)
            }, 3000)
        } else {
            binding.bottomApprovalToDoHint.visibility = View.GONE
        }
    }

    private fun showTip(info: String, outSizeEnable: Boolean = true, finish: Boolean = false) {
        val helper = object : CenterDialogHelper(
                this,
                onConfirm = {
                    if (finish) {
                        onBackPressed()//模板停用或者审批已加审给他人，提示并关闭页面
                    }
                },
                onCancel = { }
        ) {
//            override fun bindView(dialogView: View) {
//                super.bindView(dialogView)
//                dialogView.findViewById<View>(R.id.tv_hint).visibility = View.GONE
//            }
        }

        helper.initView()
        helper.onConfig(DialogConfig(content = info, confirm = "好的", showHLine = true,
                showLine = false, useDefault = false))
        helper.show(outSizeEnable, onDismissListener = DialogInterface.OnDismissListener {
            if (modelIndex == 2 && aprDetailData!!.approveInfo.stteId == 1//已弃用,这个.size是为了不报错随便加的
                    && userId in unProcessNodes && unProcessNodes.indexOf(userId!!) != 0) {
                onBackPressed()
            }
        })
    }

    private val commentList = arrayListOf<CommentListData>()

    private fun loadComment() {
        viewModel?.getAprCommentList(approveId!!, page, pageLength)
    }

    private val startPage = 1
    private val pageSize = 20
    private var page: Int = startPage
    private var pageLength = pageSize
    private var pageInited = false

    private lateinit var propertyAdapter: MultiTypeAdapter<WidgetInfo>

    private val processedNodes = arrayListOf<String>()
    private val unProcessNodes = arrayListOf<String>()
    private var currentAsignUserId = ""

    private fun iniViewWithData() {
        propertyList.clear()
        processedNodes.clear()
        unProcessNodes.clear()
        aprDetailData?.apply {
            if (aprDetailData!!.content.isNotEmpty()) {
                // 全部属性和提交的内容
                propertyList.addAll(aprDetailData!!.content)
            }

            // 审批人数据
            val nodeList = arrayListOf<SearchMemberBean>()
            nodeList.add(aprDetailData!!.approveProcess.approveCreate)
            if (aprDetailData!!.approveProcess.assignee.isNotEmpty()) {
                for (assignee in aprDetailData!!.approveProcess.assignee) {
                    if (assignee.approveType == 1) {
                        if (assignee.assigneeUser.isNotEmpty()) {
                            val member = assignee.assigneeUser[0]
                            member.approveType = 1
                            nodeList.add(member)
                            if (member.opinion in arrayOf(0, 1, 4)) {
                                processedNodes.add(member.userId)
                            } else if (member.opinion in 2..3) {
                                if (member.opinion == 3) {
                                    currentAsignUserId = member.userId
                                }
                                unProcessNodes.add(member.userId)
                            }
                            if (member.opinion == 4) {//已退回状态后不会再有审批状态
                                break
                            }
                        }
                    } else {
                        // TODO: 2019/9/16 审批分组 或审 会审节点处理
//                        nodeList.add(SearchMemberBean(approveType = assignee.approveType, assignee = assignee.assigneeUser))
                    }
                }
            }
            propertyList.add(WidgetInfo(type = 20, selectNodeResult = nodeList))

            // 抄送人
            if (aprDetailData!!.approveInfo.status in arrayListOf(1, 2)
                    && aprDetailData!!.approveProcess.carbonCopy.isNotEmpty()) {
                // TODO: 2019/9/30 增加审批默认抄送人数据，两者拼接显示
                val approvalFinishTime = nodeList.last().createTime.toString()
                propertyList.add(WidgetInfo(type = 30, title = "审批已抄送",
                        prompt = approvalFinishTime,
                        selectCopyResult = aprDetailData!!.approveProcess.carbonCopy))
            }
            // 评论
            if (commentList.isNotEmpty()) {
                propertyList.add(WidgetInfo(type = 40, commentList = commentList))
            }
        }
        val layoutManager = object : LinearLayoutManager(mContext) {
            override fun generateDefaultLayoutParams(): RecyclerView.LayoutParams {
                return RecyclerView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT)
            }
        }
        layoutManager.orientation = LinearLayoutManager.VERTICAL
        rvList.layoutManager = layoutManager
        propertyAdapter = MultiTypeAdapter(
                this,
                propertyList,
                generateViewType = { _: Int, data: WidgetInfo ->
                    if (data.type == 9 && data.wordType == 0) {
                        8
                    } else {
                        data.type
                    }
                },
                generateLayoutId = { type ->
                    when (type) {
                        1 -> {
                            // 短文本输入
                            R.layout.property_text_input_detail
                        }
                        2 -> {
                            // 长文本输入
                            R.layout.property_text_input_detail_multi
                        }
                        3, 4 -> {
                            // 时间点和时间段
                            R.layout.property_time_selector_detail
                        }
                        5 -> {
                            // 选择持续时间
                            R.layout.property_text_selector_detail
                        }
                        6, 7 -> {
                            // 单选框多选框
                            R.layout.property_text_selector_detail
                        }
                        8 -> {
                            // 人员选择，跳转页面选择人员，personType：单选0 多选1
                            R.layout.property_select_workmate_detail
                        }
                        9 -> {
                            // 选择关键字类型，人员，职位，部门自动获取，手动选择部门
                            R.layout.property_text_selector_detail
                        }
                        10 -> {
                            // 图片选择布局
                            R.layout.property_select_picture_detail
                        }
                        11 -> {
                            // 定位类型
                            R.layout.property_location_text_detail
                        }
                        // 自由流程时，自己增加两项
                        20 -> {
                            // 自定义审批流程
                            R.layout.property_select_node
                        }
                        30 -> {
                            // 抄送人员选择
                            R.layout.property_select_workmate
                        }
                        40 -> {
                            // 评论内容显示布局
                            R.layout.property_comment
                        }
                        12 -> {
                            // 审批附件显示布局
                            R.layout.property_file
                        }
                        else -> {
                            // 自动获取（当前人，团队职位，团队部门）
                            R.layout.property_text_selector_detail
                        }
                    }
                },
                onBindItem = { _: Int, data: WidgetInfo, view: View ->
                    data.isEdit = false
                    showLog(data)
                    when (data.type) {
                        1 -> {
                            // 单行输入 发起和预览不一致，发起：短文本右对齐，预览都左对齐
                            // 短文本
                            val editProperty = EditProperty(data, view)
                            editProperty.initView()
                        }
                        2 -> {
                            // 多行输入 发起和预览不一致，发起：短文本右对齐，预览都左对齐
                            // 长文本
                            val editProperty = EditPropertyDetail(data, view)
                            editProperty.initView()
                        }
                        // 时间点3和时间段4 发起右对齐，预览左对齐
                        3 -> {
                            // 时间点 long.toString()
                            val timeProperty = TimeProperty(data, view, true)
                            timeProperty.initView()
                        }
                        4 -> {
                            // 时间段
                            val timeProperty = TimeProperty(data, view, false)
                            timeProperty.initView()
                        }
                        8 -> {
                            // 人员选择，跳转页面选择人员，personType：单选0 多选1
                            // 发起时只显示名字，右对齐
                            // 预览时显示头像（圆形）和名字，左对齐，需要用另一个布局
                            val selectPerson = SelectPersonProperty(data, view) {
                                if (it.headimg == "--" && !data.selectCopyResult.isNullOrEmpty()) {
                                    val memberList = arrayListOf<TaskDetailMemberBean>()
                                    for (member in data.selectCopyResult!!) {
                                        memberList.add(TaskDetailMemberBean(member.headimg,
                                                0, 0,
                                                0, member.userId, member.name))
                                    }
                                    val bundle = Bundle()
                                    bundle.putSerializable("gridViewList", memberList)
                                    bundle.putString("title", data.title)
                                    jump(RouteTask.taskAllJoiner, bundle)
                                }
                            }
                            selectPerson.initView()
                        }
                        5, 6, 7, 9 -> {
                            // 选择持续时间(5) 发起右对齐，预览左对齐
                            // 单选框(6)多选框(7) 发起右对齐，预览左对齐
                            // 选择关键字类型(9)，人员，职位，部门自动获取，手动选择部门
                            // 发起右对齐，预览左对齐
                            // 持续时间选择
                            if (data.type == 9 && data.wordType == 0) {
                                val selectPerson = SelectPersonProperty(data, view) {
                                }
                                selectPerson.initView()
                            } else {
                                val textProperty = TextProperty(data, view)
                                textProperty.initView()
                            }
                        }
                        10 -> {
                            // 图片选择布局
                            // 发起时：显示提示文本
                            // 预览时：不显示提示文本
                            val selectPicProperty = SelectPicPreviewProperty(data, view)
                            selectPicProperty.initView()
                            selectPicProperty.listener = object : OnSelectPicListener {
                                override fun onResult(list: List<String>) {
                                }

                                override fun onSelect(property: AprProperty, selectedCount: Int) {
                                }

                                override fun onPreview(position: Int, picList: ArrayList<String>) {
                                    val previewDataBean = TaskImagePreviewActivity.PreviewDataBean(position, picList, true)
                                    val bundle = Bundle()
                                    bundle.putSerializable("previewData", previewDataBean)
                                    val intent = Intent(mContext, TaskImagePreviewActivity::class.java)
                                    intent.putExtras(bundle)
                                    mContext!!.startActivity(intent)
                                }
                            }
                        }
                        11 -> {
                            // 定位信息选择
                            // 定位类型
                            // 发起右对齐，自动显示定位logo，预览左对齐，显示定位logo
                            val locationProperty = LocationProperty(data, view)
                            locationProperty.initView()
                        }
                        20 -> {
                            // 选择节点
                            val selectNodeProperty = SelectNodeProperty(data, view)
                            selectNodeProperty.initView()
                        }
                        30 -> {
                            // 抄送人员选择
                            // 发起时：显示头像和名字，上传数据只存储id数组
                            // 预览时：显示头像和名字，预览时解析数据，包含头像、userId和名字
                            val selectPerson = SelectPersonProperty(data, view) {
                                if (it.headimg == "--" && !data.selectCopyResult.isNullOrEmpty()) {
                                    val memberList =
                                            arrayListOf<TaskDetailMemberBean>()
                                    for (member in data.selectCopyResult!!) {
                                        memberList.add(TaskDetailMemberBean(member.headimg,
                                                0, 0, 0,
                                                member.userId, member.name))
                                    }
                                    val bundle = Bundle()
                                    bundle.putSerializable("gridViewList", memberList)
                                    bundle.putString("title", "抄送人")
                                    jump(RouteTask.taskAllJoiner, bundle)
                                }
                            }
                            selectPerson.initView()
                        }
                        40 -> {
                            val commentProperty = CommentProperty(this, data, view)
                            commentProperty.initView()
                        }
                        12 -> {
                            val fileProperty = ApprovalDetailFileProperty(data, view, object
                                : ItemClickListener {
                                override fun onItemClick(position: Int) {
                                    val fileList: List<ApprovalDetailFileBean>? =
                                            GsonUtil.fromJson(
                                                    data.content,
                                                    object : TypeToken<
                                                            List<ApprovalDetailFileBean>>() {}.type)
                                    if (fileList.isNullOrEmpty()) return
                                    val bean = fileList[position]
                                    dealFile(bean.fileName, bean.url, bean.fileSize, approveId!!)
                                }

                            })
                            fileProperty.initView()
                        }
                        else -> {
                            val textProperty = TextProperty(data, view)
                            textProperty.initView()
                        }
                    }
                },
                onItemClick = { _: Int, _: WidgetInfo, _: View ->
                }
        )
        rvList.adapter = propertyAdapter

        iniTopView(aprDetailData!!.approveInfo)
        initBottomView(aprDetailData!!.approveInfo.stteId, aprDetailData!!.approveInfo.status)//已弃用
//        if (modelIndex == 2 && aprDetailData!!.approveInfo.stteId == 1) {
//            /**待审批状态*/
//            if (userId in unProcessNodes && unProcessNodes.indexOf(userId!!) != 0) {
//                if (resultType < 1) {
//                    resultType = 1//我审批待审批，加审给他人后，需要刷新列表
//                }
//                showTip("此审批已加审给其他用户", outSizeEnable = false, finish = true)
//            }
//        }
        if (isComment) {
            rvList.scrollToPosition(propertyList.lastIndex)
        }
        pageInited = true
    }

    private fun dealFile(fileName: String = "", path: String = "", size: Long = 0L,
                         commentId: String = "") {
        if (CommonUtils.checkSuffix(fileName, resources.getStringArray(
//                        R.array.rc_image_file_suffix))) {
                        com.joinutech.ddbeslibrary.R.array.rc_image_file_suffix))) {
            // 任务图片预览逻辑
            val intent = Intent(mContext, TaskImagePreviewActivity::class.java)
            val previewDataBean = TaskImagePreviewActivity.PreviewDataBean(
                    0, arrayListOf(path), true)
            val bundle = Bundle()
            bundle.putSerializable("previewData", previewDataBean)
            intent.putExtras(bundle)
            mContext!!.startActivity(intent)
        } else {
            //非图片文件
            if (StringUtils.isNotBlankAndEmpty(path)) {
                FilePreviewProvider.getPreviewPost(fileName, size, path)
                        .navigation(mContext!!, object : NavigationCallback {

                            override fun onLost(postcard: Postcard?) {

                            }

                            override fun onFound(postcard: Postcard?) {
                            }

                            override fun onInterrupt(postcard: Postcard?) {
                                runOnUiThread {
                                    ToastUtil.show(mContext!!, "此类型不支持在线预览，请到WEB端查看")
                                }
                            }

                            override fun onArrival(postcard: Postcard?) {
                            }
                        })

            } else {
                ToastUtil.show(mContext!!, "暂不支持此文件的打开")
            }
        }
    }

    private fun iniTopView(approveInfo: ApproveInfo) {
        pop = null
        // TODO: 2019/10/11 当前版本不启用导出功能
        if (modelIndex == 1 && aprDetailData!!.approveInfo.status == 3) {
//            setRightImage(R.drawable.icon_more_black, View.OnClickListener {
            setRightImage(com.joinutech.ddbeslibrary.R.drawable.icon_more_black, View.OnClickListener {
                showPop()
            })
        } else {
            hideRightImage()
        }
        setPageTitle(approveInfo.approveName)
        XUtil.loadImage(this, findViewById(R.id.iv_apr_icon), XUtil.getAprIcon(approveInfo.modelLogo))

        XUtil.setText(window.decorView, R.id.tv_apr_title, approveInfo.approveName)
        XUtil.setText(window.decorView, R.id.tv_apr_time,
                "发布时间：" + XUtil.turnToTimeStr(approveInfo.createTime, TIME_FORMAT_PATTERN1))

        if (approveInfo.status in arrayListOf(1, 2)) {
            val ivResult = findViewById<ImageView>(R.id.iv_apr_result)
            XUtil.showView(ivResult)
            ivResult.setImageResource(if (approveInfo.status == 1) R.drawable.icon_passed else R.drawable.icon_no_pass)
            XUtil.hideView(findViewById(R.id.tv_apr_state))
        } else {
            val tvState = findViewById<TextView>(R.id.tv_apr_state)
            XUtil.showView(tvState)
            AprUtil.getAprDetailState(
                    modelIndex, approveInfo.stteId, approveInfo.status,//已弃用
                    (userId != null && userId!! in unProcessNodes && unProcessNodes.indexOf(userId!!) == 0))
            { title: String, color: Int ->
                tvState.text = title
                tvState.setTextColor(CommonUtils.getColor(mContext!!, color))
            }
            XUtil.hideView(findViewById<ImageView>(R.id.iv_apr_result))
        }
    }

    /**通知栏：
     * 我的审批已完成：modelIndex = 1 subType = 2
     * 审批提醒消息：modelIndex = 2 subType = 1 跳转待审批页面，如果是待审批状态，判断当前审批人是否是当前用户，如果不是则隐藏底部操作栏
     * 抄送给我的审批：进入审批详情 modelIndex = 3
     * */
    private fun initBottomView(subType: Int, status: Int) {
        when (modelIndex) {
            /**发起人：
             *      未完成：提醒和评论、撤回和导出，
             *      已完成：评论和导出；
             *      已终止: 已撤回：重新提交，已退回：无底部栏
             */
            1 -> {
                when (subType) {
                    3 -> {
                        onInitBottom(BottomType.REMIND_COMMENT)
                    }
                    1 -> {
                        /**审批状态：0.删除，1通过；2.拒绝；3.进行中；4.未提交；5.撤回；6.退回*/
                        if (status == 2) {
                            onInitBottom(BottomType.RETRY_COMMENT)
                        } else {
                            onInitBottom(BottomType.COMMENT)
                        }
                    }
                    else -> {
                        /**审批状态：0.删除，1通过；2.拒绝；3.进行中；4.未提交；5.撤回；6.退回*/
                        val type = when (status) {
                            5 -> BottomType.RETRY
                            6 -> BottomType.NONE
                            else -> BottomType.COMMENT
                        }
                        onInitBottom(type)
                    }
                }

            }
            /**审批人：
             * 待审批：评论、加审、拒绝、同意，右上角有导出，
             * 已审批：评论和导出；
             * 已完成：已通过已拒绝：评论；已撤回：无底部栏，已退回：无底部栏*/
            2 -> {
                when (subType) {
                    1 -> {
                        if (userId in unProcessNodes && unProcessNodes.indexOf(userId!!) == 0) {// 用户在未处理中，且是第一个审批人
                            onInitBottom(BottomType.FOUR)
                        } else if (userId in processedNodes) {
                            onInitBottom(BottomType.COMMENT)
                        } else {
                            onInitBottom(BottomType.NONE)
                        }
                    }
                    2 -> {
                        onInitBottom(BottomType.COMMENT)
                    }
                    else -> {
                        /**审批状态：0.删除，1通过；2.拒绝；3.进行中；4.未提交；5.撤回；6.退回*/
                        val type = when (status) {
                            1, 2 -> BottomType.COMMENT
                            else -> BottomType.NONE
                        }
                        onInitBottom(type)
                    }
                }
            }
            /**抄送人：
             * 已完成：已通过已拒绝：评论；已撤回：无底部栏，已退回：无底部栏*/
            else -> {
                /**审批状态：0.删除，1通过；2.拒绝；3.进行中；4.未提交；5.撤回；6.退回*/
                val type = when (status) {
                    1, 2 -> BottomType.COMMENT
                    else -> BottomType.NONE
                }
                onInitBottom(type)
            }
        }
    }

    /**底部操作栏状态*/
    enum class BottomType {
        /**提醒和评论*/
        REMIND_COMMENT,

        /**重新提交和评论*/
        RETRY_COMMENT,

        /**重新提交：已撤回*/
        RETRY,

        /**评论：已完成（当前用户的审批右上角有导出功能)*/
        COMMENT,

        /**评论加审拒绝同意*/
        FOUR,

        /**没有底部栏：我审批已完成的：已撤回；抄送我的已退回，已撤回；*/
        NONE
    }

    /**
     * @param type
     *             10 发起人 未完成        提醒和评论
     *             11 发起人 已完成 已通过  评论
     *             12 发起人 已完成 未通过  重新提交和评论（重新提交需要知道模板是否可用）
     *             13 发起人 已终止 已撤回  重新提交（重新提交需要知道模板是否可用）
     *             14 发起人 已终止 已退回  无底部栏
     *
     *             20 审批人 待审批        评论+加审+拒绝+同意
     *             21 审批人 已审批        评论
     *             22 审批人 已完成 已通过  评论
     *             23 审批人 已完成 未通过  评论
     *             24 审批人 已完成 已撤回  无底部栏
     *
     *             30 抄送人 已完成        评论
     *             31 抄送人 未通过        评论
     *             32 抄送人 未完成        评论
     *             33 抄送人 已撤回        无底部栏
     *             34 抄送人 已退回        无底部栏
     *
     * */
    private fun onInitBottom(type: BottomType) {
        XUtil.hideView(
                findViewById(R.id.detail_bottom_bar),
                findViewById(R.id.ll_remind),
                findViewById(R.id.ll_retry),
                findViewById(R.id.ll_comment),
                findViewById(R.id.ll_add_apr),
                findViewById(R.id.ll_agree),
                findViewById(R.id.ll_refuse)
        )
        when (type) {
            BottomType.REMIND_COMMENT -> {
                XUtil.showView(findViewById(R.id.detail_bottom_bar),
                        findViewById(R.id.ll_remind),
                        findViewById(R.id.ll_comment))
                findViewById<View>(R.id.ll_remind).setOnClickListener(this)
                findViewById<View>(R.id.ll_comment).setOnClickListener(this)
            }
            BottomType.RETRY -> {
                if (aprDetailData!!.approveInfo.version != aprDetailData!!.approveInfo.nowVersion) {
                    XUtil.hideView(findViewById(R.id.detail_bottom_bar))
                } else {
                    XUtil.showView(findViewById(R.id.detail_bottom_bar), findViewById(R.id.ll_retry))
                    findViewById<View>(R.id.ll_retry).setOnClickListener(this)
                }
            }
            BottomType.RETRY_COMMENT -> {
                XUtil.showView(findViewById(R.id.detail_bottom_bar),
                        findViewById(R.id.ll_comment))
                if (aprDetailData!!.approveInfo.version != aprDetailData!!.approveInfo.nowVersion) {
                    XUtil.hideView(findViewById(R.id.ll_retry))
                } else {
                    XUtil.showView(findViewById(R.id.ll_retry))
                    findViewById<View>(R.id.ll_retry).setOnClickListener(this)
                }
                findViewById<View>(R.id.ll_comment).setOnClickListener(this)
            }
            BottomType.COMMENT -> {
//                if (isFinishAprDeal) {
//                    //因为当时审批成功不关页面，所以要隐藏之前显示的底部布局，再重新显示改显示的
//                    XUtil.hideView(
////                            findViewById(R.id.detail_bottom_bar),
//                            findViewById(R.id.ll_add_apr),
//                            findViewById(R.id.ll_comment)
////                            findViewById(R.id.ll_agree),
////                            findViewById(R.id.ll_refuse)
//                    )
//                }
                XUtil.showView(findViewById(R.id.detail_bottom_bar), findViewById(R.id.ll_comment))
                findViewById<View>(R.id.ll_comment).setOnClickListener(this)
            }
            BottomType.FOUR -> {
                XUtil.showView(
                        findViewById(R.id.detail_bottom_bar),
                        findViewById(R.id.ll_comment),
                        findViewById(R.id.ll_agree),
                        findViewById(R.id.ll_refuse))
                if (aprDetailData!!.approveInfo.retrial == 1 && !isCooperationCompany) {
                    XUtil.showView(findViewById(R.id.ll_add_apr))
                    findViewById<View>(R.id.ll_add_apr).setOnClickListener(this)
                } else {
                    XUtil.hideView(findViewById(R.id.ll_add_apr))
                }

                findViewById<View>(R.id.ll_comment).setOnClickListener(this)
                findViewById<View>(R.id.ll_agree).setOnClickListener(this)
                findViewById<View>(R.id.ll_refuse).setOnClickListener(this)
            }
            BottomType.NONE -> {
                XUtil.hideView(findViewById(R.id.detail_bottom_bar))
            }
        }
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.ll_remind -> {
                remindApr()
            }
            R.id.ll_retry -> {
                reCommitCheck()
            }
            R.id.ll_comment -> {
                createAprComment()
            }
            R.id.ll_add_apr -> {
//                preAprAddOld()
                preAprAddNew()
            }
            R.id.ll_refuse -> {
                showAprConfirmDialog(false)
            }
            R.id.ll_agree -> {
                showAprConfirmDialog(true)
            }
            R.id.bottomApprovalToDoHint -> {
                //底部有待处理的审批快速去审批列表
                if (isCooperationCompany) {
                    //如果是合作团队的审批消息要进入合作团队详情页
//                    ARouter.getInstance()
//                            .build(RouteApr.companyCooperationCompanyDetailActivity)
//                            .withString("companyId", companyId)
//                            .navigation()
                } else {
//                    val bundle = Bundle()
//                    bundle.putString("companyId", companyId)
//                    jump(RouteApr.APR_HANDLE_PAGE, bundle)
                }
                finish()
            }
        }
    }

    private fun preAprAddNew() {
//        var ownIsOuter = false
//        val assignes = aprDetailData?.approveProcess!!.assignee
//        if (assignes.isNotEmpty()) {
//            for (i in assignes.indices) {
//                val assignee = assignes[i]
//                if (assignee.approveType == 1) {
//                    ownIsOuter = (userId!! == assignes[i].assigneeUser[0].userId && assignes[i].assigneeUser[0].isOuter == 1)
//                    if (ownIsOuter) {
//                        break
//                    }
//                } else {
//                    //TODO 审批组成员处理
//
//                }
//            }
//        }
        /**外部联系人不可以加审，团队内部人员可选择内部或者外部协作人员加审*/
        if (!currentUserIsOuter() && StringUtils.isNotBlankAndEmpty(companyId)) {
            selectApprovalPerson(companyId!!)
        }
    }

    /**判断用户是不是外部协作人*/
    private fun currentUserIsOuter(): Boolean {
        val assignes = aprDetailData?.approveProcess!!.assignee
        if (assignes.isNotEmpty()) {
            for (i in assignes.indices) {
                val assignee = assignes[i]
                if (assignee.approveType == 1) {
                    return (userId!! == assignes[i].assigneeUser[0].userId && assignes[i].assigneeUser[0].isOuter == 1)
                } else {
                    //TODO 审批组成员处理

                }
            }
        }
        return false
    }

    private fun showAprConfirmDialog(isAgree: Boolean) {
        val helper = AprInputDialog(this, if (isAgree) "同意审批意见" else "拒绝审批意见",
                onCancel = {

                },
                onConfirm = { content ->
                    if (StringUtils.isNotBlankAndEmpty(companyId)) {
                        if (currentUserIsOuter()) {
                            aprProcess("0", content, isAgree)
                        } else {
                            loadUserOrgInfo(userId!!) { userInfo ->
                                aprProcess(userInfo.deptId, content, isAgree)
                            }
                        }
                    }
                }, gravity = Gravity.BOTTOM, confirmText = if (isAgree) "确认同意" else "确认拒绝"
        )
        helper.initView()
        helper.show()
    }

    @Suppress("UNCHECKED_CAST")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun refreshPoint(msgEvent: EventBusEvent<Any>) {
        when (msgEvent.code) {
            "apr_notice_receive_event" -> {
                if (msgEvent.data != null) {
                    val map = msgEvent.data as Map<*, *>
                    if (map.containsKey("approveId")
                            && map["approveId"] == approveId
                            && map.containsKey("companyId")
                            && map["companyId"] == companyId) {
                        showLog("接收到当前审批相关通知，触发审批相关页面刷新,刷新审批详情")
                        loadAprDetail()//收到当前审批相关通知后刷新当前页面
                    }
                }
            }
            MsgType.COMMENT_SUCCESS.name -> {
                loadAprDetail()//评论成功后刷新审批详情
            }
            MsgType.APR_RE_COMMIT_SUCCESS.name -> {
                if (resultType < 4) {
                    resultType = 4//重新提交成功后回调，关闭当前页面
                }
                onBackPressed()//重新提交成功后关闭页面，同时更新来源处，如果是列表则刷新列表
            }
        }
    }

    /**----------------------网络请求-----------------------**/
    /**加载审批详情数据，刷新页面配置*/
    private fun loadAprDetail() {
        getLoadingDialog("", true)
        viewModel?.getAprDetailByType(approveId!!, modelIndex)
    }

    /**获取用户团队信息，处理审批和添加审批人都需要自己查询接口，获得deptId*/
    private fun loadUserOrgInfo(userId: String, result: (userInfo: UserOrgInfo) -> Unit) {
        showLog("获取用户团队信息")
        viewModel?.userOrgInfoResult?.observe(this, Observer { data ->
            hideLoading()
            RequestHelper.onResponse(data, onSuccess = {
                result.invoke(it)
            }, onError = { code, msg ->
                toastShort(msg)
            }, onDefault = { msg ->
                toastShort(msg)
            })
        })
        showLoading()
        viewModel?.getUserOrgInfo(companyId!!, userId)
    }

    /**记录审批操作 -1 同意 -2 拒绝*/
    private var currentAprProcessType = 0

    /**处理审批*/
    private fun aprProcess(deptId: String, content: String, opinion: Boolean) {
        val userAssignId = getUserAssignId()
        if (userAssignId != null && userAssignId.isNotEmpty()
                && StringUtils.isNotBlankAndEmpty(companyId)) {
            val data = DealApprovalData(
                    approveAssigneeId = userAssignId,
                    organizationId = companyId!!,
                    approveId = aprDetailData!!.approveInfo.approveId,
                    deployId = aprDetailData!!.approveInfo.deployId,
                    deptId = deptId,
                    opinion = opinion,
                    taskId = aprDetailData!!.approveInfo.taskId,
                    content = content)
            showLoading()
            currentAprProcessType = if (opinion) -1 else -2
            viewModel?.dealApr(data)
        }
    }

    private fun getUserAssignId(): String? {
        if (aprDetailData != null && aprDetailData!!.approveProcess.assignee.isNotEmpty()) {
            for (assignee in aprDetailData!!.approveProcess.assignee) {
                if (assignee.approveType == 1) {
                    val member = assignee.assigneeUser[0]
                    if (member.userId == userId!! && member.opinion == 3
                            && member.userId in unProcessNodes
                            && unProcessNodes.indexOf(member.userId) == 0) {
                        return member.approveAssigneeId
                    }
                } else {
                    //TODO 会审或审处理
                }
            }
        }
        return null
    }

    // 加审人员类型判断：团队内和外部协作人判断
    // TODO: 2020/3/16 8:55 加审新逻辑：判断选择人员类型-》加审位置选择（之前或之后）-》选择人员-》排序结果返回-》拼接人员数组，提交加审。
    // 不再过滤筛选判断
    private fun selectApprovalPerson(companyId: String) {
        fun selectPerson(position: Int = 0) {
            val bundle = Bundle()
            bundle.putInt("personType", position)
            bundle.putString("companyId", companyId)
            bundle.putString("detailInfo", GsonUtil.toJson(aprDetailData))
            jump(RouteApr.PAGE_APR_ADD_PERSON, bundle, Codes.REQUEST_APRDETAIL_ADD_NODE)
        }

        if (isAddExternalContact == 0) {
            selectPerson()
        } else {
            val dialog = object : DialogHolder(this,
//                    R.layout.dialog_white_top_corner, Gravity.BOTTOM) {
                    com.joinutech.ddbeslibrary.R.layout.dialog_white_top_corner, Gravity.BOTTOM) {
                override fun bindView(dialogView: View) {
                    val cancel = dialogView.findViewById<TextView>(R.id.cancel)
                    cancel.setOnClickListener {
                        dialog?.dismiss()
                    }
                    val typeListRv = dialogView.findViewById<RecyclerView>(R.id.rv_list)
                    typeListRv.layoutManager = LinearLayoutManager(mContext)
                    val titleList = arrayListOf<ApprovalTypeDialogBean>()
                    titleList.add(ApprovalTypeDialogBean("团队内部人员"))
                    titleList.add(ApprovalTypeDialogBean("外部协作人员"))
                    val adapter = ApprovalTypeSelectDialogAdapter(mContext!!, titleList)
                    typeListRv.adapter = adapter
                    adapter.setItemClickListener(object : ItemClickListener {
                        override fun onItemClick(position: Int) {
                            selectPerson(position)
                            dialog?.dismiss()
                        }
                    })
                }
            }
            dialog.initView()
            dialog.show(true)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == Codes.REQUEST_APRDETAIL_ADD_COMMENT) {
                showLog("发表评论成功后刷新web页面")
                loadAprDetail()
            } else if (requestCode == Codes.REQUEST_APRDETAIL_ADD_NODE) {
                data?.let {
                    val result = it.getIntExtra("resultType", 1)
                    if (resultType < result) {
                        resultType = result
                    }
                    if (resultType == 2) {
                        showTip("此审批已加审给其他用户", outSizeEnable = false, finish = true)
                    }
                }
                loadAprDetail()// 加审后回调
            }
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    /**催办*/
    private fun remindApr() {
        showLoading()
        val userAssignId = getUserAssignId()
        viewModel?.remindApr(aprDetailData!!.approveInfo.approveId, userAssignId ?: "")
    }

    /**撤回*/
    private fun recallApr() {
        val helper = CenterDialogHelper(
                this,
                onConfirm = { tryRecallApr() }, onCancel = { })

        helper.initView()
        helper.onConfig(
                DialogConfig(
                        content = "你确定要撤回这条审批吗？",
                        cancel = "取消",
                        confirm = "确定",
                        useDefault = false
                )
        )
        helper.show()
    }

    private fun tryRecallApr() {
        showLoading()
        viewModel?.recallApr(aprDetailData!!.approveInfo.approveId)
    }

    /**发审批评论*/
    private fun createAprComment() {
        ARouter.getInstance().build(RouteApr.APR_COMMENT_PAGE)
                .withString(ConsKeys.TARGET_ID, aprDetailData!!.approveInfo.approveId)
                .withString(ConsKeys.COMPANY_ID, companyId)
                .navigation(this, Codes.REQUEST_APRDETAIL_ADD_COMMENT)
    }

    /**重新提交检测*/
    private fun reCommitCheck() {
        viewModel?.reCommitCheck(aprDetailData!!.approveInfo.approveId)
    }

    override fun onBackPressed() {
        if (listIndex >= 0) {// 列表索引，标识从列表到达详情页面
            val intent = intent
            intent.putExtra("listIndex", listIndex)
            intent.putExtra("resultType", resultType)
            setResult(Activity.RESULT_OK, intent)
        } else if (resultType > 0) {

        }
        finish()
    }

    /**
     * 静态内部类handler
     */
    class MyStaticHandler(ref: WeakReference<AprDetailActivity>) : Handler() {

        private var activity: AprDetailActivity? = ref.get()

        override fun handleMessage(msg: Message) {
            if (activity != null) {
                when (msg.what) {
                    0 -> {
                        activity?.showApprovalToDoHint(false)
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        handler.removeCallbacksAndMessages(null)
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityAprDetailBinding {
        return ActivityAprDetailBinding.inflate(layoutInflater)
    }
}

