package com.joinutech.approval

import android.os.Bundle
import android.view.View
import androidx.viewbinding.ViewBinding

import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.approval.request.ApprovalModel2
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity

/**
 * @className: AprBaseActivity
 * @desc: 审批Activity基类
 * @author: zyy
 * @date: 2019/8/9 13:25
 * @company: joinUTech
 * @leader: ke
 */
abstract class AprBaseActivity<VB: ViewBinding> : MyUseBindingActivity<VB>() {
    override fun initImmersion() {

    }

    override fun showToolBar(): Bo<PERSON>an {
        return true
    }


    override fun openArouterReceive(): <PERSON><PERSON><PERSON> {
        return true
    }

    protected var viewModel: ApprovalModel2? = null

    override fun initLogic() {
        super.initLogic()
        viewModel = getModel(ApprovalModel2::class.java)
    }

    override fun setPageTitle(title: String) {
        super.setPageTitle(title)
        whiteStatusBarBlackFont()
        showToolBarLine()
        showBackButton(com.tea.fileselectlibrary.R.drawable.back_grey, View.OnClickListener { onBackPressed() })
    }

    fun jump(path: String, bundle: Bundle? = null, requestCode: Int = 0) {
        val postCard = ARouter.getInstance().build(path)
        if (bundle != null) {
            postCard.with(bundle)
        }
        if (requestCode > 0) {
            postCard.navigation(this, requestCode)
        } else {
            postCard.navigation()
        }
    }
}
