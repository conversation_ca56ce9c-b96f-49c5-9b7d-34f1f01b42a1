package com.joinutech.approval

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.approval.databinding.ActivityAprSignatureBinding
import com.joinutech.approval.utils.UploadFileUtil
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.UploadFileBean
import com.joinutech.ddbeslibrary.utils.BitmapUtil
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.RouteProvider
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.toast
import com.joinutech.flutter.EventBackHandSignFile
import org.greenrobot.eventbus.EventBus
import java.io.ByteArrayOutputStream
import java.io.File


const val SCAN_SIGNATURE = "app_scan_signature"
class AprSignatureActivity : MyUseBindingActivity<ActivityAprSignatureBinding>() {

    var approveId: String? = ""

    var action = ""

    var key: String? = ""

    var companyId: String? = ""

    override val contentViewResId: Int = R.layout.activity_apr_signature
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityAprSignatureBinding {
        return ActivityAprSignatureBinding.inflate(layoutInflater)
    }

    companion object {

        fun startPage(context: Context , key: String , approveId: String , action: String , companyId: String) {
            val intent = Intent(context, AprSignatureActivity::class.java).apply {
                putExtra("action", action)
                putExtra("key", key)
                putExtra("approveId", approveId)
                putExtra("companyId", companyId)
            }
            context.startActivity(intent)
        }
    }


    override fun initImmersion() {
        mImmersionBar?.fitsSystemWindows(true)//为true，则状态栏本身占据位置
            ?.statusBarDarkFont(true, 0.2f)
            ?.init()
    }

    override fun initView() {

        binding.signTitleTv.text = "手写签名"
        binding.signBackIv.setOnClickListener { finish() }
        action = intent.getStringExtra("action") ?: ""
        approveId = intent.getStringExtra("approveId") ?: ""

        companyId = intent.getStringExtra("companyId") ?: ""
        key = intent.getStringExtra("key") ?: ""
    }

    override fun initLogic() {
        super.initLogic()

        binding.signatureCancelTv.setOnClickListener(this)
        binding.signatureRetryTv .setOnClickListener(this)
        binding.signatureConfirmTv .setOnClickListener(this)
        binding.signatureUndoTv.setOnClickListener(this)
    }

    //点击事件
    override fun onNoDoubleClick(v: View) {
        super.onNoDoubleClick(v)
        when (v.id) {
            R.id.signature_cancel_tv -> {//点击取消
                setResult(RESULT_CANCELED)
                finish()
            }

            R.id.signature_retry_tv -> {//点击重写
                binding.signatureView.redo()

            }

            R.id.signature_undo_tv -> {//点击撤销
                binding.signatureView.undo()
            }

            R.id.signature_confirm_tv -> {//点击确定
                if (binding.signatureView.getBitmap() != null) {
                    val intent = Intent()
                    val bitmap: Bitmap = binding.signatureView.getBitmap()
                    if (action == SCAN_SIGNATURE){
                        // todo 待测试
                        val file = BitmapUtil.saveBitmap(
                            this, bitmap,
                            "${System.currentTimeMillis()}.png", folderName = "signature"
                        )

                        if (file != null){
                            sigUpload(key ?:"" ,file , companyId)
                        }

                        return
                    }


                    if (action == "for_signature") {
                        EventBusUtils.sendEvent(EventBusEvent("signature_result", bitmap))

                        if (StringUtils.isNotBlankAndEmpty(approveId)) {
                            val f = BitmapUtil.saveBitmap(
                                this, bitmap,
                                "${System.currentTimeMillis()}.png", folderName = "signature"
                            )
                            f?.let {
                                EventBus.getDefault().post(EventBackHandSignFile(approveId!!, f))
                            }
                        }

                        finish()
                        return
                    }
                    if (action == "scan_signature") {
                        EventBusUtils.sendEvent(EventBusEvent("scan_signature_result", bitmap))
                        finish()
                        return
                    }

                    val baos = ByteArrayOutputStream()
                    /*下面方法表示压缩图片，中间的值越小，压缩比例越大，失真也约厉害，100表示不压缩*/
                    bitmap.compress(
                        Bitmap.CompressFormat.PNG,
                        100,
                        baos
                    )
                    val byteArray = baos.toByteArray()
                    intent.putExtra("bitmap", byteArray)
                    setResult(RESULT_OK, intent)
                    finish()
                }
            }
        }
    }

    private fun sigUpload(key: String , targetFile: File , companyId: String?) {
        val bundle= Bundle()
        bundle.putSerializable("signature",targetFile)
        bundle.putString("companyId", companyId)
        showLoading()
        (ARouter.getInstance().build(RouteProvider.APR_FUN_PROVIDER)
            .navigation() as RouteServiceProvider)
            .openPageWithResult(this,"upload_signature", bundle, result = {
                if (it != "0" && StringUtils.isNotBlankAndEmpty(it)) {
                    //扫码后上传手写签名成功
                    val uploadFileBean= GsonUtil.fromJson(it, UploadFileBean::class.java)
                    val map= linkedMapOf<String,Any>()
                    val map1= linkedMapOf<String,Any>()
                    map1.put("fileId",uploadFileBean?.fileId?:"")
                    map1.put("hash",uploadFileBean?.hash?:"")
                    map1.put("fileName",uploadFileBean?.fileName?:"")
                    map.put("key",key)

                    map.put("orgId", companyId ?:"")
                    map.put("signatureFile",map1)
                    map.put("type",2)
                    val resultJson= GsonUtil.toJson(map)
                    UploadFileUtil.sendSignatureId(resultJson, onSuccess = {
//                        toast(it)
                        hideLoading()
                        finish()
                    },
                        onFailer = {
                            toast(it)
                            hideLoading()
                        })
                }else{
                    hideLoading()
                }
            })
    }



    override fun showToolBar(): Boolean {
        return false
    }

    override fun openArouterReceive(): Boolean {
        return false
    }
}