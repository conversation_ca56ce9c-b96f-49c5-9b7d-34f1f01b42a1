package com.joinutech.approval.temp

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter

/**
 * @className: MyFragmentAdapter
 * @desc: 审批ViewPage 适配器
 * @author: zyy
 * @date: 2019/8/2 9:18
 * @company: joinUTech
 * @leader: ke
 */
class MyFragmentAdapter(fm: FragmentManager) : FragmentPagerAdapter(fm) {

    private val pages = hashMapOf<Int, AprTempFragment>()

    val titles = arrayListOf("预置模版", "自定义模板")
    override fun getItem(position: Int): Fragment {
        if (!pages.containsKey(position)) {
            pages[position] = AprTempFragment.newInstance(when (position) {
                0 -> 1
                else -> 2
            })
        }
        return pages[position]!!
    }

    override fun getCount(): Int {
        return titles.size
    }

    override fun getPageTitle(position: Int): CharSequence? {
        return titles[position]
    }

}