package com.joinutech.approval.temp

import android.graphics.Color
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.View
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.approval.AprBaseFragment
import com.joinutech.approval.R
import com.joinutech.approval.data.ModelData
import com.joinutech.approval.data.ModelGroup
import com.joinutech.approval.utils.MySection
import com.joinutech.approval.utils.OnSectionListener
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.widget.OnEmptyClickListener
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.utils.*
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter

/**
 * @className: AprTempFragment
 * @desc: 模板列表页面 type ：0 预置模板 ，type：1 自定义模板
 * @author: zyy
 * @date: 2019/8/2 9:12
 * @company: joinUTech
 * @leader: ke
 */
class AprTempFragment(override val layoutRes: Int = com.joinutech.ddbeslibrary.R.layout.common_list_layout)
    : AprBaseFragment(), OnSectionListener, OnEmptyClickListener {

    override fun openEventBus() = false

    private lateinit var noDataViewPage: PageEmptyView
    private lateinit var rvTempList: RecyclerView

    private val list = arrayListOf<ModelGroup>()
    private lateinit var adapter: SectionedRecyclerViewAdapter

    /**预置模板：1，自定义模板：2*/
    var modelType = 1

    override fun initView(rootView: View) {
        showLog("initView")
        modelType = arguments?.getInt(ARG_SECTION_NUMBER) ?: 0
        noDataViewPage = rootView.findViewById(R.id.layout_empty_layout)
        noDataViewPage.setContent("暂无审批模板")
        noDataViewPage.setEmptyIcon(com.joinutech.ddbeslibrary.R.drawable.ic_empty_approval)
        noDataViewPage.hide()
        noDataViewPage.clickListener = this
        rvTempList = rootView.findViewById(R.id.rv_list)
        adapter = SectionedRecyclerViewAdapter()
        rvTempList.layoutManager = LinearLayoutManager(mActivity)
        rvTempList.adapter = adapter
    }


    override fun onAction(actionCode: Int) {
        loadData()
    }

    override fun initLogic() {
        super.initLogic()
        viewModel?.modelListResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                list.clear()
                if (it.isNotEmpty()) {
                    for (group in it) {
                        if (group.models != null && group.models.isNotEmpty()) {
                            list.add(group)
                        }
                    }
                }
                if (list.isNotEmpty()) {
                    noDataViewPage.hide()
                    rvTempList.visibility = View.VISIBLE
                } else {
                    noDataViewPage.show()
                    rvTempList.visibility = View.GONE
                }
                initData(list)
            }, onError = { _, msg ->
                showToast(msg)
                noDataViewPage.show()
                rvTempList.visibility = View.GONE
            }, onDefault = { defaultMsg ->
                showToast(defaultMsg)

            })

        })
        loadData()
    }

    private fun initData(data: List<ModelGroup>) {
        val params = SectionParameters.builder().itemResourceId(R.layout.item_temp_layout).headerResourceId(R.layout.item_temp_group_layout).build()
        if (adapter.sectionCount > 0) {
            adapter.removeAllSections()
        }
        for (element in data) {
            val section = MySection(element, params, this)
            adapter.addSection(section)
        }
        adapter.notifyDataSetChanged()
    }

    private fun loadData() {
        val companyBean = CompanyHolder.getCurrentOrg()
        if (companyBean != null) {
            showLoading()
            viewModel?.getModelList(companyBean.companyId, modelType)
        }
    }

    fun onChangeModelState(modelId: String, type: Int = 1) {
        viewModel?.changeModelResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result,
                    onSuccess = {
                        loadData()
                    },
                    onError = { _, _ ->
                        EventBusUtils.sendEvent(EventBusEvent(
                                EventBusAction.Event_APR_PERMISSION_LEAVE, ""))
                        val dialog = MyDialog(mActivity, 280, 140,
                                "你的权限状态异常，无法完成操作",
                                needBtnConfirm = true, needBtnCancel = false, bgResourceId = 0)
                        dialog.setBtnLeftText("好的")
                        dialog.setCanceledOnTouchOutside(false)
                        dialog.setCancelable(false)
                        dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
                            override fun clickLeftBtn() {
                                mActivity.finish()
                            }
                        })
                        dialog.show()
                    },
                    onDefault = { msg ->
                        showToast(msg)
                    })
        })
        showLoading()
        viewModel?.changeModel(modelId, type)
    }

    override fun initHeader(itemView: View): RecyclerView.ViewHolder {
        return MySecHeader(itemView, modelType)
    }

    override fun initChild(itemView: View): RecyclerView.ViewHolder {
        return MySecHolder(itemView, modelType)

    }

    override fun bindHeader(holder: RecyclerView.ViewHolder, group: ModelGroup) {
        val header = holder as MySecHeader
        header.onBind(group, this)
    }

    override fun bindChild(holder: RecyclerView.ViewHolder, child: ModelData) {
        val header = holder as MySecHolder
        header.onBind(child, this)
    }

    override fun onGroupClick(resId: Int, group: ModelGroup) {
    }

    override fun onChildClick(resId: Int, child: ModelData) {
        if (child.modelName.isNotBlank()) {
            if (resId == R.id.temp_notify) {
                notify(child)
            } else {
                showTip(child)
            }
        }
    }

    private fun notify(child: ModelData) {
        viewModel?.modelNotifyResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                showToast("通知成功")
            }, onError = { code, msg ->
                showToast(msg)

            }, onDefault = { msg ->
                showToast(msg)
            })
        })
        showLoading()
        viewModel?.modelNotify(child.modelId)
    }

    private fun showTip(child: ModelData) {
        val content = "你确认要${if (child.status == 2) "启用" else "停用"}模板 ${child.modelName} 吗？"
        val startIndex = content.indexOf(child.modelName)
        val endIndex = startIndex + child.modelName.length
        val spannableString = SpannableString(content)
        val colorSpan = ForegroundColorSpan(Color.parseColor("#1E87F0"))
        spannableString.setSpan(colorSpan, startIndex, endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        val helper = object : CenterDialogHelper(
                mActivity,
                onConfirm = {
                    onChangeModelState(child.modelId, if (child.status == 2) 1 else 2)
                }, onCancel = {

        }
        ) {
            override fun bindView(dialogView: View) {
                super.bindView(dialogView)
                dialogView.findViewById<TextView>(R.id.tv_content).text = spannableString
                if (child.status == 2) {
                    dialogView.findViewById<TextView>(R.id.tv_hint).visibility = View.GONE
                } else {
                    dialogView.findViewById<TextView>(R.id.tv_hint).visibility = View.VISIBLE
                }
            }
        }

        helper.initView()
        helper.onConfig(DialogConfig(useDefault = true))
        helper.show()
    }

//
//    private fun showTip2(aprName: String) {
////        val aprName = "自定义审批2"
//        val content = "你确认要启用模板 $aprName 吗？"
//        val startIndex = content.indexOf(aprName)
//        val endIndex = startIndex + aprName.length
//        val spannableString = SpannableString(content)
//        val colorSpan = ForegroundColorSpan(Color.parseColor("#1E87F0"))
//        spannableString.setSpan(colorSpan, startIndex, endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
//        val helper = object : BottomDialogHelper(mActivity, R.layout.dialog_bottom_select_layout, {
//
//        }, {
//
//        }) {
//            override fun bindView(dialogView: View) {
//                super.bindView(dialogView)
////                dialogView.findViewById<TextView>(R.id.tv_content).text = spannableString
//            }
//        }
//
//        helper.initView()
//        helper.show()
//    }
//
//    private fun showTip3(data: MutableList<AprItem>) {
//
//        val helper: BottomListDialogHelper<AprItem> = BottomListDialogHelper(mActivity, R.layout.item_page_list, "选择下边任何一项", data,
//                onBindItem = { i: Int, childBean: AprItem, view: View ->
//                    view.findViewById<TextView>(R.id.page_name).text = childBean.modelName
//                }, onConfirm = { i: Int, childBean: AprItem ->
//            Toast.makeText(mActivity, "select position is $i", Toast.LENGTH_SHORT).show()
//        },
//                onCancel = {}
//        )
//        helper.initView()
//        helper.show()
//
//    }

    class MySecHeader(item: View, val modelType: Int) : RecyclerView.ViewHolder(item) {
        fun onBind(group: ModelGroup, clickListener: OnSectionListener) {
            itemView.findViewById<TextView>(R.id.tv_item_group_name).text = group.groupName
            itemView.setOnClickListener {
                clickListener.onGroupClick(0, group)
            }
        }
    }

    class MySecHolder(val item: View, private val modelType: Int) : RecyclerView.ViewHolder(item) {

        fun onBind(child: ModelData, clickListener: OnSectionListener) {
            itemView.findViewById<TextView>(R.id.temp_name).text = child.modelName
            itemView.findViewById<TextView>(R.id.temp_desc).text = child.modelDesc
            itemView.findViewById<View>(R.id.temp_notify).visibility = View.GONE
            XUtil.loadImage(item.context, itemView.findViewById(R.id.temp_icon), XUtil.getAprIcon(child.modelLogo))
            val toggle = itemView.findViewById<TextView>(R.id.temp_toggle)
            toggle.text = if (child.status == 2) "启用" else "停用"
            toggle.setOnClickListener {
                clickListener.onChildClick(R.id.temp_toggle, child)
            }
        }
    }

    companion object {
        /**
         * The fragment argument representing the section number for this
         * fragment.
         */
        private const val ARG_SECTION_NUMBER = "apr_model_type"

        /**
         * Returns a new instance of this fragment for the given section
         * number.
         */
        @JvmStatic
        fun newInstance(sectionNumber: Int): AprTempFragment {
            Log.e("fragment", "newInstance()")
            return AprTempFragment().apply {
                arguments = Bundle().apply {
                    putInt(ARG_SECTION_NUMBER, sectionNumber)
                }
            }
        }
    }

}