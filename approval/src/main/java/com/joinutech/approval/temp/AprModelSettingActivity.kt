package com.joinutech.approval.temp

import android.view.LayoutInflater
import android.view.View
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.approval.*
import com.joinutech.approval.databinding.ActivityAprModelSettingBinding
import com.joinutech.approval.utils.RouteApr
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.utils.*
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

@Route(path = RouteApr.APR_MODEL)
class AprModelSettingActivity(override val contentViewResId: Int = R.layout.activity_apr_model_setting)
    : AprBaseActivity<ActivityAprModelSettingBinding>() {
    @Autowired
    @JvmField
    var companyId = ""

    override fun initView() {
        setPageTitle("审批设置")
        findViewById<View>(R.id.cl_model_manage).setOnClickListener {
            jump(RouteApr.APR_MODEL_MANAGE)
        }
        findViewById<View>(R.id.cl_model_set).setOnClickListener {
            jump(RouteApr.APR_MODEL_SET_PAGE)
        }
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityAprModelSettingBinding {
        return ActivityAprModelSettingBinding.inflate(layoutInflater)
    }

    //团队已解散
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun orgDissolved(event: EventBusEvent<String>) {
        if (event.code == EventBusAction.Event_DISSOLVED_ORG ||
                event.code == EventBusAction.Event_LEAVE_ORG) {
            if (BaseApplication.getCurrentActivity() == AprModelSettingActivity::class.java.name) {
                if (StringUtils.isNotBlankAndEmpty(event.data) &&
                        StringUtils.isNotBlankAndEmpty(companyId)
                        && event.data == companyId) {
                    val dialog = if (event.code == EventBusAction.Event_DISSOLVED_ORG) {
                        MyDialog(mContext!!, 0, 0,
                                "该团队已解散", needBtnConfirm = true, needBtnCancel = false, bgResourceId = 0)
                    } else {
                        MyDialog(mContext!!, 0, 0,
                                "你已被请离该团队", needBtnConfirm = true, needBtnCancel = false, bgResourceId = 0)
                    }
                    dialog.setBtnLeftText("好的")
                    dialog.setCanceledOnTouchOutside(false)
                    dialog.setCancelable(false)
                    dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
                        override fun clickLeftBtn() {
                            finish()
                        }

                    })
                    dialog.show()
                }
            }
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun eventDeal(event: EventBusEvent<String>) {
        when (event.code) {
            EventBusAction.Event_APR_PERMISSION_LEAVE -> {
                finish()
            }
        }
    }
}
