package com.joinutech.approval.temp

import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.viewpager.widget.ViewPager
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.approval.AprBaseActivity
import com.joinutech.approval.R
import com.joinutech.approval.databinding.CommonTabViewpagerLayoutBinding
import com.joinutech.common.base.LinkBuilder
import com.joinutech.ddbeslibrary.base.NoticeWebActivity
import com.joinutech.approval.utils.RouteApr

/**
 * @className: AprTempActivity
 * @desc: 审批模板管理页面
 * @author: zyy
 * @date: 2019/8/2 9:12
 * @company: joinUTech
 * @leader: ke
 */
@Route(path = RouteApr.APR_MODEL_MANAGE)
class AprTempActivity(override val contentViewResId: Int = R.layout.common_tab_viewpager_layout)
    : AprBaseActivity<CommonTabViewpagerLayoutBinding>(), ViewPager.OnPageChangeListener {

    var myTabLayout: MyTabLayout? = null
    private lateinit var vpContainer: ViewPager

    override fun initView() {
        setPageTitle("审批模板管理")
        setRightTitle("创建新模板", View.OnClickListener {
            val intent = Intent(mContext, NoticeWebActivity::class.java)
            intent.putExtra("targetUrl", LinkBuilder.generate().getResolveUrl())
            intent.putExtra("title", "创建新模板")
            mContext!!.startActivity(intent)
        })
        val adapter = MyFragmentAdapter(supportFragmentManager)
        val titles = adapter.titles

        val tabLayout = findViewById<View>(R.id.tab_title)
        myTabLayout = MyTabLayout(tabLayout)
        myTabLayout?.init()
        myTabLayout?.setupTab(titles[0], titles[1])
        myTabLayout?.setIndex(0)
        myTabLayout?.addListener {
            vpContainer.currentItem = it
        }

        vpContainer = findViewById(R.id.vp_page_container)
        vpContainer.adapter = adapter
        vpContainer.addOnPageChangeListener(this)
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): CommonTabViewpagerLayoutBinding {
        return CommonTabViewpagerLayoutBinding.inflate(layoutInflater)
    }

    override fun onPageScrollStateChanged(state: Int) {

    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {

    }

    override fun onPageSelected(position: Int) {
        myTabLayout?.setIndex(position)
    }
}

/**tablayout view 管理*/
class MyTabLayout(val view: View?) {
    lateinit var tabOne: TextView
    lateinit var tabTwo: TextView
    lateinit var tabThree: TextView

    lateinit var tabOneIndicator: View
    lateinit var tabTwoIndicator: View
    lateinit var tabThreeIndicator: View

    lateinit var tvOneCount: TextView
    lateinit var tvTwoCount: TextView
    lateinit var tvThreeCount: TextView

    fun init() {
        view?.let {
            tabOne = it.findViewById(R.id.tab_one)
            tabTwo = it.findViewById(R.id.tab_two)
            tabThree = it.findViewById(R.id.tab_three)

            tvOneCount = it.findViewById(R.id.tv_one_count)
            tvTwoCount = it.findViewById(R.id.tv_two_count)
            tvThreeCount = it.findViewById(R.id.tv_three_count)

            tvOneCount.visibility = View.GONE
            tvTwoCount.visibility = View.GONE
            tvThreeCount.visibility = View.GONE

            tabOneIndicator = it.findViewById(R.id.indicator_one)
            tabTwoIndicator = it.findViewById(R.id.indicator_two)
            tabThreeIndicator = it.findViewById(R.id.indicator_three)
        }
    }

    fun setupTab(one: String? = null, two: String? = null, three: String? = null) {
        if (one == null) {
            tabOne.visibility = View.GONE
            tabOneIndicator.visibility = View.GONE
        } else {
            tabOne.text = one
        }

        if (two == null) {
            tabTwo.visibility = View.GONE
            tabTwoIndicator.visibility = View.GONE
        } else {
            tabTwo.text = two
        }

        if (three == null) {
            tabThree.visibility = View.GONE
            tabThreeIndicator.visibility = View.GONE
        } else {
            tabThree.text = three
        }
    }

    fun setIndex(index: Int = 0) {
        when (index) {
            1 -> {
                tabOne.isSelected = false
                tabTwo.isSelected = true
                tabThree.isSelected = false
                tabOneIndicator.visibility = View.GONE
                tabTwoIndicator.visibility = View.VISIBLE
                tabThreeIndicator.visibility = View.GONE
            }
            2 -> {
                tabOne.isSelected = false
                tabTwo.isSelected = false
                tabThree.isSelected = true
                tabOneIndicator.visibility = View.GONE
                tabTwoIndicator.visibility = View.GONE
                tabThreeIndicator.visibility = View.VISIBLE
            }
            else -> {
                tabOne.isSelected = true
                tabTwo.isSelected = false
                tabThree.isSelected = false
                tabOneIndicator.visibility = View.VISIBLE
                tabTwoIndicator.visibility = View.GONE
                tabThreeIndicator.visibility = View.GONE
            }
        }
    }

    fun setCount(index: Int, count: Int) {
        when (index) {
            0 -> {
                if (count > 0) {
                    tvOneCount.visibility = View.VISIBLE
                    tvOneCount.text = "$count"
                } else {
                    tvOneCount.visibility = View.GONE
                }
            }
            1 -> {
                if (count > 0) {
                    tvTwoCount.visibility = View.VISIBLE
                    tvTwoCount.text = "$count"
                } else {
                    tvTwoCount.visibility = View.GONE
                }
            }
            else -> {
                if (count > 0) {
                    tvThreeCount.visibility = View.VISIBLE
                    tvThreeCount.text = "$count"
                } else {
                    tvThreeCount.visibility = View.GONE
                }
            }
        }
    }

    fun addListener(onSelect: (index: Int) -> Unit) {
        tabOne.setOnClickListener {
            setIndex(0)
            onSelect.invoke(0)
        }
        tabTwo.setOnClickListener {
            setIndex(1)
            onSelect.invoke(1)
        }
        tabThree.setOnClickListener {
            setIndex(2)
            onSelect.invoke(2)
        }
    }
}