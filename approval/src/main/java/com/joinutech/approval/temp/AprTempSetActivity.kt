package com.joinutech.approval.temp

import android.view.LayoutInflater
import androidx.lifecycle.Observer
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.approval.AprBaseActivity
import com.joinutech.approval.R
import com.joinutech.approval.databinding.ActivityAprTempSetBinding
import com.joinutech.common.util.CompanyHolder
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.approval.utils.RouteApr
import com.joinutech.ddbeslibrary.utils.XUtil
import com.joinutech.ddbeslibrary.utils.toastShort
import com.kyleduo.switchbutton.SwitchButton

/**
 * @className: AprTempSetActivity
 * @desc: 审批模板设置页面
 * @author: zyy
 * @date: 2019/8/2 9:12
 * @company: joinUTech
 * @leader: ke
 */
@Route(path = RouteApr.APR_MODEL_SET_PAGE)
class AprTempSetActivity(override val contentViewResId: Int = R.layout.activity_apr_temp_set) : AprBaseActivity<ActivityAprTempSetBinding>() {

    override fun initView() {
        setPageTitle("个性化设置")
        XUtil.setText(window.decorView, R.id.set_toggle_name, "允许发起人使用提醒审批功能")
        btnToggle = findViewById(R.id.btn_toggle)
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityAprTempSetBinding {
        return ActivityAprTempSetBinding.inflate(layoutInflater)
    }

    override fun initLogic() {
        super.initLogic()
        btnToggle.setOnCheckedChangeListener { _, isChecked ->
            changePushStatus(isChecked)
        }
        companyBean = CompanyHolder.getCurrentOrg()
//        viewModel?.aprPushChangeError!!.observe(this, Observer {
//            hideLoading()
//            toastShort(it.message)
//            btnToggle.isChecked = !btnToggle.isChecked
//        })
        /**设置审批推送*/
        viewModel?.setAprPushStatusResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                toastShort("设置成功")
            }, onError = { code, msg ->
                toastShort(msg)
                btnToggle.isChecked = !btnToggle.isChecked
            }, onDefault = { msg ->
                toastShort(msg)
                btnToggle.isChecked = !btnToggle.isChecked
            })
        })
        if (companyBean != null && companyBean!!.companyId.isNotEmpty()) {
//            viewModel?.aprPushStatusError!!.observe(this, Observer {
//                hideLoading()
//                toastShort(it.message)
//            })
            /**获取审批推送该设置*/
            viewModel?.getAprPushStatusResult!!.observe(this, Observer { result ->
                hideLoading()
                RequestHelper.onResponse(result, onSuccess = {
                    btnToggle.setCheckedNoEvent(it == 1)
                }, onError = { code, msg ->
                    toastShort(msg)
                }, onDefault = { msg ->
                    toastShort(msg)
                })

            })
            showLoading()
            viewModel?.getAprPushStatus(companyBean!!.companyId)
        }
    }

    private var companyBean: WorkStationBean? = null
    private lateinit var btnToggle: SwitchButton

    private fun changePushStatus(turnOn: Boolean) {
        if (companyBean != null && companyBean!!.companyId.isNotEmpty()) {
            showLoading()
            viewModel?.setAprPushStatus(companyBean!!.companyId, if (turnOn) 1 else 0)
        }
    }

}