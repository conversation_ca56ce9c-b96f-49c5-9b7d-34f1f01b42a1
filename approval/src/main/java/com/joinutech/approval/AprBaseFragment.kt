package com.joinutech.approval

import android.os.Bundle
import android.view.View
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.approval.request.ApprovalModel2
import com.joinutech.ddbeslibrary.base.BaseFragment

/**
 * @className: AprBaseFragment
 * @desc: 审批 fragment 基类
 * @author: zyy
 * @date: 2019/8/23 17:08
 * @company: joinUTech
 * @leader: ke
 */
abstract class AprBaseFragment : BaseFragment() {
    var viewModel: ApprovalModel2? = null

    override fun initView(rootView: View) {

    }

    override fun initLogic() {
        super.initLogic()

        viewModel = getModel(ApprovalModel2::class.java)
    }

    fun jump(path: String, bundle: Bundle? = null, requestCode: Int = 0) {
        val postCard = ARouter.getInstance().build(path)
        if (bundle != null) {
            postCard.with(bundle)
        }
        if (requestCode > 0) {
            postCard.navigation(mActivity, requestCode)
        } else {
            postCard.navigation()
        }
    }

}