package com.joinutech.approval.adapter

import android.content.Context
import android.view.LayoutInflater
import android.widget.ImageView
import com.joinutech.approval.R
import com.joinutech.approval.data.ApprovalDetailFileBean
import com.joinutech.approval.databinding.ItemApprovalFileBinding
import com.joinutech.common.storage.FileUtil
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.ItemClickListener
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter
import com.joinutech.common.adapter.CommonBindingAdapter

/**
 * @Description: 审批文件adapter
 * @Author: hjr
 * @Time: 2020/3/2 11:08
 * @packageName: com.joinutech.approval.adapter
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
class ApprovalFileAdapter(context: Context, dataList: ArrayList<ApprovalDetailFileBean>,
                          listener: ItemClickListener)
    : CommonBindingAdapter<ApprovalDetailFileBean,ItemApprovalFileBinding>(context, dataList, R.layout.item_approval_file) {

    private var clickListener = listener


    override fun inflateViewBinding(layoutInflater: LayoutInflater): ItemApprovalFileBinding {
        return ItemApprovalFileBinding.inflate(layoutInflater)
    }

    override fun onBindItem(
        binding: ItemApprovalFileBinding,
        item: ApprovalDetailFileBean?,
        position: Int,
    ) {
        item?.let { data->
            val icon = binding.fileIcon
            if (CommonUtils.checkSuffix(data.fileName,
//                        mContext.resources.getStringArray(R.array.rc_image_file_suffix))) {
                    mContext.resources.getStringArray(com.joinutech.ddbeslibrary.R.array.rc_image_file_suffix))) {
                ImageLoaderUtils.loadImage(mContext, icon, data.url)
            } else {
                icon.setImageResource(FileUtil.getFileTypeIcon(data.fileName))
//            showDownLoadFileIcon(data.fileName, icon, mContext)
            }
            binding.fileName.text = data.fileName
            binding.root.setOnClickListener {
                clickListener.onItemClick(position)
            }
        }
    }
}