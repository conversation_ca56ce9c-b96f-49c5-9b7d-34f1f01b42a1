package com.joinutech.approval.adapter

import android.content.Context
import android.view.View
import android.widget.TextView
import com.joinutech.approval.AprUtil
import com.joinutech.approval.R
import com.joinutech.approval.data.MyAprListData
import com.joinutech.ddbeslibrary.utils.ItemClickListener
import com.joinutech.ddbeslibrary.utils.XUtil
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/3 14:20
 * @packageName: com.joinutech.approval.adapter
 * @Company: JoinuTech
 */
class CooperationCompanyApprovalAdapter(context: Context, dataList: ArrayList<MyAprListData>) :
        CommonAdapter<MyAprListData>(context, dataList, R.layout.item_apr_list_layout) {

    private var subModelType = 0

    fun setSubModelType(subModelType: Int) {
        this.subModelType = subModelType
    }

    private var modelType = 0

    fun setModelType(modelType: Int) {
        this.modelType = modelType
    }

    private var itemClickListener: ItemClickListener? = null

    fun setItemClickListener(itemClickListener: ItemClickListener) {
        this.itemClickListener = itemClickListener
    }

    override fun bindData(holder: ViewHolder, data: MyAprListData, position: Int) {
        if (position == 0) {
            holder.getView<View>(R.id.line_top).visibility = View.GONE
        } else {
            holder.getView<View>(R.id.line_top).visibility = View.VISIBLE
        }
        holder.setText(R.id.tv_apr_title, data.approveName)
        ImageLoaderUtils.loadImage(mContext, holder.getView(R.id.iv_apr_icon),
                XUtil.getAprIcon(data.modelLogo))
        holder.setText(R.id.tv_apr_time, XUtil.getShowTime(data.createTime))

        if (!data.oneApproveContent.isNullOrBlank()) {
            XUtil.showView(holder.itemView.findViewById(R.id.tv_pro_one))
            XUtil.setText(holder.itemView, R.id.tv_pro_one, data.oneApproveContent)
        } else {
            XUtil.hideView(holder.itemView.findViewById(R.id.tv_pro_one))
        }

        if (!data.twoApproveContent.isNullOrBlank()) {
            XUtil.showView(holder.itemView.findViewById(R.id.tv_pro_two))
            XUtil.setText(holder.itemView, R.id.tv_pro_two, data.twoApproveContent)
        } else {
            XUtil.hideView(holder.itemView.findViewById(R.id.tv_pro_two))
        }

        if (!data.threeApproveContent.isNullOrBlank()) {
            XUtil.showView(holder.itemView.findViewById(R.id.tv_pro_three))
            XUtil.setText(holder.itemView, R.id.tv_pro_three, data.threeApproveContent)
        } else {
            XUtil.hideView(holder.itemView.findViewById(R.id.tv_pro_three))
        }

        AprUtil.getAprState(modelType, subModelType, data.status) { title: String, color: Int ->
            XUtil.showView(holder.itemView.findViewById(R.id.tv_apr_node))
//            if (modelType == 1 && subModelType == 3) {
//                XUtil.setText(holder.itemView, R.id.tv_apr_node, data.approver + title)
//            } else {
            XUtil.setText(holder.itemView, R.id.tv_apr_node, title)
//            }
            holder.itemView.findViewById<TextView>(R.id.tv_apr_node).setTextColor(
                    holder.itemView.resources.getColor(color))
        }

        val redDot = holder.getView<View>(R.id.iv_red_dot)
        if (data.isRead == 0) {
            redDot.visibility = View.VISIBLE
        } else {
            redDot.visibility = View.GONE
        }
        holder.setOnItemClickListener {
            itemClickListener?.onItemClick(holder.adapterPosition)
        }
    }

}