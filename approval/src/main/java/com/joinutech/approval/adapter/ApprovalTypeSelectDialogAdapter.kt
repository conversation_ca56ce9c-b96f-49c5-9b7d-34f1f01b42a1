package com.joinutech.approval.adapter

import android.content.Context
import android.view.View
import com.joinutech.approval.R
import com.joinutech.approval.temp.ApprovalTypeDialogBean
import com.joinutech.ddbeslibrary.utils.ItemClickListener
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter

/**
 * <AUTHOR>
 * @className: ExternalContactTypeSelectAdapter
 *@Description: 类作用描述
 */
class ApprovalTypeSelectDialogAdapter (var context:Context,
                                       dataList:ArrayList<ApprovalTypeDialogBean>)
//    : CommonAdapter<ApprovalTypeDialogBean>(context,dataList, R.layout.item_center_text_with_dot){
    : CommonAdapter<ApprovalTypeDialogBean>(context,dataList, com.joinutech.ddbeslibrary.R.layout.item_center_text_with_dot){

    private var listener :ItemClickListener? =null

    override fun bindData(holder: ViewHolder, data: ApprovalTypeDialogBean, position: Int) {

        if (StringUtils.isNotBlankAndEmpty(data.title)){
//            holder.setText(R.id.content,data.title)
            holder.setText(com.joinutech.ddbeslibrary.R.id.content,data.title)
            if (data.isHaveRed){
                holder.setViewVisibility(com.joinutech.ddbeslibrary.R.id.contentRed, View.VISIBLE)
            }else {
                holder.setViewVisibility(com.joinutech.ddbeslibrary.R.id.contentRed, View.GONE)
            }
            if (listener!=null){
                holder.setOnItemClickListener {
                    listener?.onItemClick(holder.adapterPosition)
                }
            }
        }
    }

    fun setItemClickListener(listener: ItemClickListener) {
        this.listener = listener
    }
}