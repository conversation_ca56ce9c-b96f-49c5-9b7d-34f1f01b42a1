package com.joinutech.approval.adapter

import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.joinutech.approval.R
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.joinutech.ddbeslibrary.utils.DeviceUtil
import com.joinutech.ddbeslibrary.utils.PROPERTY_DETAIL_TITLE_MODEL
import com.joinutech.ddbeslibrary.utils.XUtil
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter
import com.joinutech.ddbeslibrary.utils.StringUtils

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/18 10:15
 * @packageName: com.joinutech.approval.adapter
 * @Company: JoinuTech
 */
class AprDetailCopyPersonAdapter(
    context: Context, dataList: ArrayList<SearchMemberBean>,
    private val onShowMore: (member: SearchMemberBean) -> Unit
) : CommonAdapter<SearchMemberBean>(context, dataList, R.layout.item_person_list) {
    private val spanCount = 5
    override fun bindData(holder: ViewHolder, data: SearchMemberBean, position: Int) {
        val itemView = holder.itemView
        val picture = itemView.findViewById<ImageView>(R.id.iv_pic)
        val name = itemView.findViewById<TextView>(R.id.tv_name)

        DeviceUtil.getTextWidth(
            PROPERTY_DETAIL_TITLE_MODEL, name.textSize
        )

        if (position == (spanCount * 2 - 1) && data.headimg == "--") {
            XUtil.hideView(name)
            picture.setImageResource(R.drawable.ic_person_more)
            picture.setOnClickListener {
                onShowMore.invoke(data)
            }
        } else {
            XUtil.showView(name)
            name.text = data.name
            if (StringUtils.isNotBlankAndEmpty(data.headimg) && data.headimg != "--") {
                XUtil.loadRoundImage(
                    mContext, data.headimg,
                    picture, DeviceUtil.dip2px(mContext, 4f)
                )
            }
        }
        val externalTag = itemView.findViewById<TextView>(R.id.externalTag)
        if (data.isOuter == 0) {
            externalTag.visibility = View.GONE
        } else {
            externalTag.visibility = View.VISIBLE
        }
    }

}