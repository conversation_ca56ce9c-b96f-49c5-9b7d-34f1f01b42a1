package com.joinutech.approval.func

import android.os.Bundle
import android.view.LayoutInflater
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.approval.R
import com.joinutech.approval.databinding.ActivityWebApproveLayoutBinding
import com.joinutech.approval.utils.RouteApr
import com.joinutech.common.util.NotifyUtil
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.*

@Route(path = RouteApr.APR_FUNC_WEB_PAGE)
class ApproveWebActivity : MyUseBindingActivity<ActivityWebApproveLayoutBinding>() {
    var targetUrl: String = ""

    var companyId: String? = null

    override fun initImmersion() {

    }

    private var backListener: OnApproveInvokeListener? = null

    var approveId = ""

    override fun initView() {
        val token = UserHolder.getAccessToken()
        approveId = intent.getStringExtra(ConsKeys.TARGET_ID) ?: ""
        companyId = intent.getStringExtra(ConsKeys.COMPANY_ID) ?: ""
        targetUrl = intent.getStringExtra(ConsKeys.PARAMS_URL) ?: ""
        if (!targetUrl.isNullOrBlank() && token.isNotBlank()) {
            if (!targetUrl.contains("&height=")) {
                val statusBarHeight = ScreenUtils.px2dip(this, ScreenUtils.getStatusBarHeight2(this))
                targetUrl = targetUrl.plus("&height=${statusBarHeight}")
            }
            val approveWebFragment = ApproveWebFragment.newInstance(companyId ?: "", targetUrl)
            backListener = approveWebFragment
            val transaction = supportFragmentManager.beginTransaction()
            transaction.replace(R.id.page_content, approveWebFragment, "awf")
            transaction.commitAllowingStateLoss()
        } else {
            finish()
        }
    }

    override fun onResume() {
        super.onResume()
        if (!approveId.isNullOrBlank()) {
            // 2021/1/6 16:16  如果当前审批在通知栏有消息的话，审批id作为通知id，清理通知栏消息
            NotifyUtil.instance.setCurrentTarget(approveId)
        }
    }

    override val contentViewResId: Int = R.layout.activity_web_approve_layout
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityWebApproveLayoutBinding {
        return ActivityWebApproveLayoutBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = false

    override fun openArouterReceive(): Boolean = true

    override fun onBackPressed() {
        if (backListener != null) {
            backListener?.onBackPress()
        } else {
            super.onBackPressed()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    fun closePage() {
        supportFragmentManager.findFragmentByTag("awf")?.let {
            val transaction = supportFragmentManager.beginTransaction()
            transaction.remove(it)
//            transaction.commit()
            transaction.commitAllowingStateLoss()
        }
        if (!this.isDestroyed) {
            finish()
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        if (outState != null && clearFragmentTag()) {
            outState.remove("android:support:fragments")
        }
    }

    private fun clearFragmentTag(): Boolean {
        return true
    }

}