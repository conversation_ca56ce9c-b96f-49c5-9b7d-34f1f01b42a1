package com.joinutech.approval.func

import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.approval.AprBaseActivity
import com.joinutech.approval.R
import com.joinutech.approval.data.ApproveAlertSetting
import com.joinutech.approval.databinding.ActivityAprFuncSetBinding
import com.joinutech.common.util.CompanyHolder
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.utils.DialogHolder
import com.joinutech.approval.utils.RouteApr
import com.joinutech.ddbeslibrary.utils.toastShort
import com.joinutech.ddbeslibrary.widget.contrarywind.view.CustomWheelView
import com.joinutech.ddbeslibrary.widget.wheel.adapter.ArrayWheelAdapter
import com.kyleduo.switchbutton.SwitchButton

/**
 * @className: AprFuncSetActivity
 * @desc: 审批功能设置页面
 * @author: zyy
 * @date: 2019/8/2 9:12
 * @company: joinUTech
 * @leader: ke
 */
@Route(path = RouteApr.APR_FUNC_SET_PAGE)
class AprFuncSetActivity(override val contentViewResId: Int = R.layout.activity_apr_func_set)
    : AprBaseActivity<ActivityAprFuncSetBinding>() {

    private lateinit var btnToggle: SwitchButton
    private lateinit var tvTime: TextView

    var companyBean: WorkStationBean? = null

    override fun initView() {
        setPageTitle("审批使用设置")
        btnToggle = findViewById(R.id.btn_toggle)
        findViewById<View>(R.id.set_time_name).setOnClickListener {
            showDialog()
        }
        tvTime = findViewById(R.id.tv_time)
        tvTime.setOnClickListener {
            showDialog()
        }
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityAprFuncSetBinding {
        return ActivityAprFuncSetBinding.inflate(layoutInflater)
    }

    private fun updateView(switchOn: Boolean) {
        findViewById<View>(R.id.line_middle).visibility = if (switchOn) View.VISIBLE else View.GONE
        findViewById<View>(R.id.set_time_name).visibility = if (switchOn) View.VISIBLE else View.GONE
        tvTime.visibility = if (switchOn) View.VISIBLE else View.GONE
    }

    override fun initLogic() {
        super.initLogic()
        btnToggle.setOnCheckedChangeListener { _, isChecked ->
            showLog("点击切换开关状态")
            updateView(isChecked)
            changePushStatus(isChecked, 9, 0)
            tvTime.text = getTimeString(9, 0)
        }
        companyBean = CompanyHolder.getCurrentOrg()
//        viewModel?.setAprFuncSettingError!!.observe(this, Observer {
//            hideLoading()
//            toastShort(it.message)
//            btnToggle.isChecked = !btnToggle.isChecked
//            updateView(btnToggle.isChecked)
//        })

        viewModel?.setAprFuncSettingResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                toastShort("设置成功")
            }, onError = { code, msg ->
                toastShort(msg)
                btnToggle.isChecked = !btnToggle.isChecked
                updateView(btnToggle.isChecked)
            }, onDefault = { msg ->
                toastShort(msg)
                btnToggle.isChecked = !btnToggle.isChecked
                updateView(btnToggle.isChecked)
            })

        })

        viewModel?.getAprFuncSettingResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                val switchState = it.approvalSwitch == 1
                btnToggle.setCheckedNoEvent(switchState)
                updateView(switchState)
                if (switchState) {
                    tvTime.text = getTimeString(it.approvalRemindHour, it.approvalRemindMinute)
                }
            }, onError = { code, msg ->
                toastShort(msg)
            }, onDefault = { msg ->
                toastShort(msg)
            })

        })
        if (companyBean != null) {
            showLoading()
            viewModel?.getAprFuncSetting(userId!!, companyBean!!.companyId)
        }
    }

    private fun getTimeString(hour: Int, minute: Int): String {
        val hourStr = if (hour > 9) {
            hour.toString()
        } else {
            "0${hour}"
        }
        val minuteStr = if (minute > 9) {
            minute.toString()
        } else {
            "0${minute}"
        }
        return "$hourStr：$minuteStr"
    }

    private fun changePushStatus(turnOn: Boolean, hourIndex: Int, minuteIndex: Int) {
        var data = ApproveAlertSetting(
                approvalSwitch = if (turnOn) 1 else 0,
                approvalRemindHour = hourIndex,
                approvalRemindMinute = minuteIndex,
                companyId = companyBean!!.companyId,
                userId = userId!!
        )
        showLoading()
        viewModel?.setAprFuncSetting(data)
    }

    private fun showDialog() {
        val helper = AprAlertTimeDialog(this, "请设置审批提醒时间",
                tvTime.text.toString(), "00") { hourStr, _ ->
            tvTime.text = hourStr
            val result = hourStr.split("：")
            changePushStatus(true, result[0].toInt(), result[1].toInt())
        }
        helper.initView()
        helper.show()
    }
}

class AprAlertTimeDialog(activity: AppCompatActivity,
                         private val titleInfo: String,
                         private val hour: String,
                         private val minute: String,
                         private val onConfirm: (hour: String, minute: String) -> Unit) : DialogHolder(
        activity = activity,
        layoutId = R.layout.dialog_switch_time_selector,
        tag = "time_dialog"
) {
    lateinit var title: TextView

    lateinit var cancel: TextView
    lateinit var confirm: TextView
    lateinit var lineH: View
    lateinit var lineV: View
    lateinit var wvHour: CustomWheelView

    override fun bindView(dialogView: View) {
        title = dialogView.findViewById(R.id.tv_title)
        title.text = titleInfo
        cancel = dialogView.findViewById(R.id.cancel)
        confirm = dialogView.findViewById(R.id.confirm)

        lineH = dialogView.findViewById(R.id.line_ho)
        lineV = dialogView.findViewById(R.id.line_v)

        wvHour = dialogView.findViewById(R.id.wv_hour)

        cancel.setOnClickListener {
            dialog?.dismiss()
        }
        confirm.setOnClickListener {
            dialog?.dismiss()
            onConfirm.invoke(hourRange[wvHour.currentItem], "00")
        }
        initData()

        val hourIndex = hourRange.indexOf(hour)

        setWheel(hourIndex)
    }

    private fun setWheel(hourIndex: Int) {
        wvHour.adapter = ArrayWheelAdapter(hourRange)
        wvHour.currentItem = hourIndex
        wvHour.setIsOptions(true)
        wvHour.setCyclic(false)
        setTextContentSize(14)
        setTextColorOut()
        setTextColorCenter()
        setDividerColor()
    }

    private val hourRange = arrayListOf<String>()
    private val minuteRange = arrayListOf<String>()
    private fun initData() {
        for (i in 6 until 22) {
            if (i < 10) {
                hourRange.add("0$i：00")
            } else {
                hourRange.add("$i：00")
            }
        }
        for (i in 0 until 60) {
            if (i < 10) {
                minuteRange.add("0$i")
            } else {
                minuteRange.add("$i")
            }
        }
    }

    private fun setDividerColor() {
        wvHour.setDividerColor(0xFFFFFFFF.toInt())
    }

    private fun setTextContentSize(textSize: Int) {
        wvHour.setTextSize(textSize.toFloat())
    }

    private fun setTextColorOut() {
        wvHour.setTextColorOut(0xFFAEAEAE.toInt())
    }

    private fun setTextColorCenter() {
        wvHour.setTextColorCenter(0xFF0082F2.toInt())
    }
}
