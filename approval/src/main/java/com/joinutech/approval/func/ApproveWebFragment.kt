package com.joinutech.approval.func

import android.Manifest
import android.app.Activity
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.net.http.SslError
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.provider.Settings
import android.text.Html
import android.util.Base64
import android.view.Gravity
import android.view.View
import android.webkit.*
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.launcher.ARouter
import com.google.gson.reflect.TypeToken
import com.joinutech.approval.AprApproveAddActivity
import com.joinutech.approval.AprCommentActivity
import com.joinutech.approval.AprSignatureActivity
import com.joinutech.approval.R
import com.joinutech.approval.adapter.ApprovalTypeSelectDialogAdapter
import com.joinutech.approval.aprhistory.Codes
import com.joinutech.approval.data.ApprovalDetailData
import com.joinutech.approval.request.ApprovalModel2
import com.joinutech.approval.temp.ApprovalTypeDialogBean
import com.joinutech.approval.utils.MsgType
import com.joinutech.approval.utils.RouteApr
import com.joinutech.common.base.isWebDebug
import com.joinutech.common.helper.WechatHelper
import com.joinutech.common.provider.FilePreviewProvider
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.common.storage.FileDownTransferManager
import com.joinutech.common.storage.FileStorage
import com.joinutech.common.storage.FileTransferInfo
import com.joinutech.common.storage.FileUtil
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.UserHolder
import com.joinutech.common.widget.OnEmptyClickListener
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.api.PersonApi
import com.joinutech.ddbeslibrary.base.BaseFragment
import com.joinutech.ddbeslibrary.base.ShareFileBean
import com.joinutech.ddbeslibrary.bean.OrgImportPeopleBean
import com.joinutech.ddbeslibrary.bean.UserInfo
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.request.CommonResult
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.request.RetrofitClient
import com.joinutech.ddbeslibrary.request.RxScheduleUtil
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.request.interceptor.RequestCache
import com.joinutech.ddbeslibrary.service.LoginService
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.activity.TaskImagePreviewActivity
import com.marktoo.lib.cachedweb.CommonWebConfig
import com.marktoo.lib.cachedweb.LogUtil
import com.marktoo.lib.cachedweb.WebListener
import com.zhy.http.okhttp.OkHttpUtils
import com.zhy.http.okhttp.callback.FileCallBack
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import io.reactivex.FlowableEmitter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import okhttp3.Call
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import java.io.ByteArrayOutputStream
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * @PackageName: com.joinutech.ddbes.clouddoc
 * @ClassName:ApproveWebFragment
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/7/27 11:03
 * @Desc: 审批web列表页面
 */
class ApproveWebFragment : BaseFragment(), OnApproveInvokeListener, OnEmptyClickListener {

    companion object {
        fun newInstance(companyId: String, targetUrl: String): ApproveWebFragment {
            return ApproveWebFragment().apply {
                arguments = Bundle().apply {
                    putString(ConsKeys.PARAMS_URL, targetUrl)
                    putString(ConsKeys.COMPANY_ID, companyId)
                }
            }
        }
    }

    private var targetUrl: String = ""
    private var currentCompany: WorkStationBean? = null

//    override val layoutRes: Int = R.layout.fragment_common_web_layout
    override val layoutRes: Int = com.joinutech.ddbeslibrary.R.layout.fragment_common_web_layout
    private var commWeb: CommonWebConfig? = null

//    private var resultType: Int = 0

    /** 公共标题栏 */
    lateinit var llTitleBar: View
    lateinit var ivLeft: ImageView
    lateinit var tvLeft: TextView
    lateinit var tvTitle: TextView
    lateinit var tvSubTitle: TextView
    lateinit var ivRight: ImageView
    lateinit var tvRight: TextView

    private lateinit var webDoc: WebView

    private lateinit var emptyView: PageEmptyView

    lateinit var loadingLayout: View
    lateinit var progressBar: ProgressBar

    var statusHeight: Int = 0

    //    lateinit var ceshibaseiv: ImageView
    override fun initView(rootView: View) {
        val topPx = ScreenUtils.getStatusBarHeight2(requireContext())
        statusHeight = ScreenUtils.px2dip(requireContext(), topPx)

//        ceshibaseiv=rootView.findViewById(R.id.ceshibaseiv)

//        llTitleBar = rootView.findViewById(R.id.ll_title_bar)
        llTitleBar = rootView.findViewById(com.joinutech.ddbeslibrary.R.id.ll_title_bar)
        llTitleBar.visibility = View.GONE
        ivLeft = rootView.findViewById(com.joinutech.ddbeslibrary.R.id.iv_left)
        ivLeft.setOnClickListener(this)
        tvLeft = rootView.findViewById(R.id.tv_left)

        tvTitle = rootView.findViewById(R.id.tv_title)
        tvSubTitle = rootView.findViewById(com.joinutech.ddbeslibrary.R.id.tv_sub_title)

        ivRight = rootView.findViewById(com.joinutech.ddbeslibrary.R.id.iv_right)
        tvRight = rootView.findViewById(R.id.tv_right)

        webDoc = rootView.findViewById(com.joinutech.ddbeslibrary.R.id.lfw_webview)

        emptyView = rootView.findViewById(com.joinutech.ddbeslibrary.R.id.empty_layout)
        emptyView.clickListener = this

        loadingLayout = rootView.findViewById(com.joinutech.ddbeslibrary.R.id.loadingLayout)
        progressBar = rootView.findViewById(com.joinutech.ddbeslibrary.R.id.pb_web_load_progress)
//        val params = progressBar.layoutParams as LinearLayout.LayoutParams
//        params.topMargin = topPx.toInt()
//        progressBar.layoutParams = params

        val perms = arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )
        PermissionUtils.requestPermissionActivity(mActivity, perms, "存储文件权限",
            onSuccess = {
                initWebView(webDoc, hasPermission = true)
                showLoading(outSizeCancel = true)
                showCloudDoc()
            },
            onError = {
                initWebView(webDoc, hasPermission = false)
                showLoading(outSizeCancel = true)
                showCloudDoc()
            })


    }

    private val testInfo =
        "{\"approvePdfId\":\"2526691170319340541\",\"fileId\":\"2526691170319339517\",\"fileName\":\"2021-07-08 13:34:37 小四-请假申请.pdf\",\"fileSize\":266201,\"fileUrl\":\"https://ddbes-pan-test-**********.cos.ap-chongqing.myqcloud.com/2526691170319336445?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKID0oNgiTYBdrob6n8RU7TYYHPJfYAYtpqp%26q-sign-time%3D1625722478%3B1625808878%26q-key-time%3D1625722478%3B1625808878%26q-header-list%3D%26q-url-param-list%3Dresponse-cache-control%3Bresponse-content-disposition%3Bresponse-content-language%3Bresponse-expires%26q-signature%3D0ae8c65fb05a28aeef527753884f2571a6bf2e8d&response-cache-control=no-cache&response-content-disposition=attachment%3B%20filename%3D%222021-07-08%2B13%253A34%253A37%25E5%25AF%25BC%25E5%2587%25BA%25E5%25B0%258F%25E5%259B%259B-%25E8%25AF%25B7%25E5%2581%2587%25E7%2594%25B3%25E8%25AF%25B7.pdf%22&response-content-language=zh-CN&response-expires=Fri%2C%2009%20Jul%202021%2005%3A34%3A38%20GMT\",\"fileType\":1,\"path\":\"9\"}"

    private fun testShare() {
        val json = JSONObject(testInfo)
        LogUtil.showLog("当前跳转页面参数为：$json")
        GsonUtil.fromJson<ShareFileBean>(testInfo)?.let {
            shareFile(it)
        }
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.tv_right, com.joinutech.ddbeslibrary.R.id.iv_right -> {
//                testShare()
            }
            com.joinutech.ddbeslibrary.R.id.iv_left->{
                goBack()
            }
        }
    }

    private fun goBack() {
        if (webDoc?.canGoBack()?:false) {
            webDoc?.goBack()
        } else {
            mActivity.finish()
        }
    }

    private var canGoBack = 0
    private var isReady = 0
    private var companyId = ""

    private fun showCloudDoc() {
        // 设置距离顶部高度，避免延伸到状态栏
        targetUrl = arguments?.getString(ConsKeys.PARAMS_URL, "") ?: ""
        companyId = arguments?.getString(ConsKeys.COMPANY_ID, "") ?: ""
        if (companyId.isNullOrBlank()) return
        currentCompany = getCompany(companyId)
        if (CompanyHolder.getTotalCompanies().isNotEmpty()) {
            if (webDoc.visibility != View.VISIBLE) {
                webDoc.visibility = View.VISIBLE
                emptyView.visibility = View.GONE
            }
            if (!pageInited && targetUrl.isNotBlank()) {
                loadUrl(targetUrl)
            } else {
                closePage()
            }
        } else {
            emptyView.visibility = View.VISIBLE
            webDoc.visibility = View.GONE
        }
    }

    private fun closePage() {
        if (mActivity is ApproveWebActivity) {
            (mActivity as ApproveWebActivity).closePage()
        } else {
            mActivity.finish()
        }
    }

    val request_code_permission = 200004
    val request_code_file_chooser_for_web = 200003
    private fun initWebView(webView: WebView, hasPermission: Boolean = true) {
        clearCahce()
        commWeb = CommonWebConfig(mActivity, webView)
        commWeb?.let {
            it.debug = isWebDebug
            it.cacheable = false
            it.autoWide = true
            it.zoomable = true
            it.multiWindow = false
            it.defaultEncoding = "utf-8"
//            it.userAgent = "ddbes"
            it.jsBridge = true
            it.applyWebSettings()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                webView.settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            }
            webView.settings.domStorageEnabled = hasPermission
            it.addInterceptor()
            it.addDefaultClient()
            it.webListener = object : WebListener() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    if (!loadFinished) {
                        loadFinished = true
                        if (delayed.get()) {
                            handler.removeCallbacks(runnable)
                        } else {
                            delayed.compareAndSet(false, true)
                        }
                        checkLoadError()
                    }
                }

                override fun onProgressChanged(view: WebView?, newProgress: Int) {
                    if (newProgress < 100) {
                        if (progressBar.visibility == View.VISIBLE && progressBar.progress < newProgress) {
                            progressBar.progress = newProgress
                        }
                    } else {
                        if (!loadFinished) {
                            if (delayed.compareAndSet(false, true)) {
                                showLog("--->加载页面 进度达到百分之百后，发送一次延时处理结果情况，避免不回调 finished方法")
                                handler.postDelayed(runnable, 20000)
                            }
                        }
                    }
                }

                override fun onHttpError(
                    view: WebView?, request: WebResourceRequest?,
                    errorResponse: WebResourceResponse?
                ) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        showLog("--->加载页面 onHttpError ${request?.url} -- ${errorResponse?.statusCode}")
                        if (!loadFinished && !loadAssetsError && errorResponse?.statusCode!! >= 400) {
                            loadAssetsError = true
                        }
                    }
                }

                override fun onReceivedError(
                    view: WebView?,
                    errorCode: Int?,
                    description: String?,
                    url: String?
                ) {
                    showLog("--->加载页面 onReceivedError 错误码：$errorCode -- $description")
                    val noError = description?.contains("net::ERR_FAILED") == true
                    val err = description?.startsWith("ERR_") ?: false
                    val code = errorCode ?: 0 < 0
                    if (!loadFinished && !loadPageError && ((err && !noError) || code)) {
                        loadPageError = true
                        Loggerr.i("审批webview", "=onReceivedError执行了==加载失败===")
                        pageErrorInfo = "$url--$errorCode--$description"
                    }
                }

                override fun onReceivedTitle(view: WebView?, title: String?, hasError: Boolean) {
                    showLog("--->加载页面 onReceivedTitle  $title - $hasError")
                    val titleError = title?.contains("网页无法打开") ?: false
                            || title?.contains("error", true) ?: false
                            || title?.contains("failed", true) ?: false
                    if (!loadFinished && !loadTitleError && (titleError || hasError)) {
                        Loggerr.i("审批webview", "=onReceivedTitle执行了==加载失败===")
                        loadTitleError = true
                        titleErrorInfo = "$targetUrl--$title"
                    }
                }

                override fun onSslError(
                    view: WebView?,
                    handler: SslErrorHandler?,
                    error: SslError?
                ) {
//                super.onSslError(view, handler, error)
                    handler?.proceed()
                }

                override fun onShowFileChooser(
                    webView: WebView?,
                    filePathCallback: ValueCallback<Array<Uri>>?,
                    fileChooserParams: WebChromeClient.FileChooserParams?
                ): Boolean {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        //Android版本大于等于11时,申请全部文件的权限，文件选择，选择文件，，，调用系统文件选择器===========
                        if (!Environment.isExternalStorageManager()) {
                            val intent =
                                Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                            intent.setData(Uri.parse("package:" + mActivity.getPackageName()));
                            startActivityForResult(intent, request_code_permission);
                            return false
                        }
                    }

                    fileChooseCallback = filePathCallback
                    val intent = Intent(Intent.ACTION_GET_CONTENT)
                    intent.addCategory(Intent.CATEGORY_OPENABLE)
                    intent.setType("*/*")
                    try {
                        startActivityForResult(
                            Intent.createChooser(intent, "File Chooser"),
                            request_code_file_chooser_for_web
                        )
                    } catch (e: Exception) {
                        fileChooseCallback = null
                        ToastUtil.show(mActivity, "打开文件选择器失败")
                        return false
                    }

                    return true
                }
            }
            it.useCached(false)
            webView.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
            it.addMutualInterface(ApproveMutualInterface(mActivity, this), ConsKeys.JS_TRANS_PORT)
        }
    }

    private fun clearCahce() {
        try {
            webDoc.clearCache(true)
        } catch (e: Exception) {
        }
        try {
            webDoc.clearHistory()
        } catch (e: Exception) {
        }
        try {
            webDoc.clearFormData()
        } catch (e: Exception) {
        }
    }

    private fun updateTitleBar(useTitleBar: Boolean, title: String, subTitle: String = "") {
        if (useTitleBar) {
            immersionBar?.titleBar(llTitleBar)?.init()
            tvTitle.text = title
            llTitleBar.visibility = View.VISIBLE
            ivLeft.visibility = View.VISIBLE
            tvTitle.visibility = View.VISIBLE
            if (!subTitle.isNullOrBlank()) {
                tvSubTitle.visibility = View.VISIBLE
                tvSubTitle.text = subTitle
            } else {
                tvSubTitle.visibility = View.GONE
            }
        } else {
            immersionBar?.titleBar(null)?.init()
            llTitleBar.visibility = View.GONE
            ivLeft.visibility = View.GONE
            tvTitle.visibility = View.GONE
            updateStatusBarHeight()
        }
    }

    private var loadTitleError = false
    private var titleErrorInfo = ""
    private var loadPageError = false
    private var pageErrorInfo = ""
    private var loadAssetsError = false
    private var loadFinished = false
    val cache = RequestCache()

    val handler = Handler()
    private val delayed = AtomicBoolean(false)
    val runnable = {
        if (!loadFinished) {
            loadFinished = true
            checkLoadError()
        }
    }

    private fun showLoadView() {
        loadingLayout.visibility = View.VISIBLE
        progressBar.visibility = View.VISIBLE
        emptyView.hide()
        delayed.compareAndSet(true, false)
        loadTitleError = false
        titleErrorInfo = ""
        loadPageError = false
        pageErrorInfo = ""
        loadAssetsError = false
        loadFinished = false
    }

    private fun checkLoadError() {
        showLog("加载完成，检查页面是否包含错误")
        loadingLayout.visibility = View.GONE
        if (loadTitleError || loadPageError) {
            canGoBack = 1
            if (targetUrl.contains("kingdee", true)) {
                updateTitleBar(true, "金蝶审批", "")
            } else {
                updateTitleBar(true, "")
            }
            emptyView.setContent("加载失败，请稍后重试！")
            emptyView.setPositiveContent("")
            emptyView.setNegativeContent("")
            emptyView.show()
            // TODO: 2021/8/24 记录网页加载异常信息
            val info = if (!titleErrorInfo.isNullOrBlank() && !pageErrorInfo.isNullOrBlank()) {
                titleErrorInfo.plus(";").plus(pageErrorInfo)
            } else if (!titleErrorInfo.isNullOrBlank()) {
                titleErrorInfo
            } else if (!pageErrorInfo.isNullOrBlank()) {
                pageErrorInfo
            } else {
                ""
            }
            showLog("-->>加载出现异常，需要记录日志信息")
            cache.log(0L, info, 6, start, System.currentTimeMillis(), 1)
        } else {
            Loggerr.i("审批webview", "===加载成功===")
            emptyView.hide()
        }
    }

    private var fileChooseCallback: ValueCallback<Array<Uri>>? = null

//    private val selector = PicSelector2(this, 20)

    private var start = 0L

    private fun loadUrl(realUrl: String) {
        start = System.currentTimeMillis()
        showLog("加载链接 [ $realUrl ]")
        showLoadView()
        commWeb?.loadUrl(realUrl, ConsValue.getWebHeaders())
    }

    lateinit var viewModel: ApprovalModel2

    override fun initLogic() {
        super.initLogic()
        viewModel = getModel(ApprovalModel2::class.java)
    }

    override fun onResume() {
        super.onResume()
        showLog("++++++ fragment Approve onResume()")
        updateStatusBar(true)
        // showCloudDoc()
    }

    //交互
    private fun invokeJs(method: String, data: String) {
        showLog("调用js：$method ：$data")
        commWeb?.invokeJS("javascript:window.${objName}.$method(${data})")
    }

    fun invokeJsGetData(method: String, params: Any?, result: (String) -> Unit) {
        val code = if (params != null) {
            "$method($params)"
        } else {
            "$method()"
        }
        commWeb?.invokeJS(code, resultCallback = ValueCallback { value ->
            result.invoke(value)
        }, inJavaBridge = true)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: EventBusEvent<Any>) {
        showLog("aprWebPage onEvent() ${event.code}")
        when (event.code) {
            "apr_notice_receive_event" -> {
                showLog("审批web页面，收到审批相关通知后，告知web页审批id，如果是当前为审批详情页面且是当前审批id，则刷新页面")
                if (event.data != null) {
//                    val map = event.data as Map<*, *>
                    onApprovalNotice(GsonUtil.toJson(event.data))
                }
            }
            MsgType.APR_RE_COMMIT_SUCCESS.name -> {
                onBackPress()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == Codes.REQUEST_APRDETAIL_ADD_COMMENT) {
                showLog("发表评论成功后刷新web页面")
                invokeJs("refreshComment", "")
            } else if (requestCode == Codes.REQUEST_APRDETAIL_ADD_NODE) {
                FileStorage.showLog("加审成功后 回退页面")
                data?.let {
                    val resultType = it.getIntExtra("resultType", 1)
                    if (resultType == 2) {
                        showTip("此审批已加审给其他用户", outSizeEnable = false, finish = true)
                    } else {
                        loadAprDetail()// 加审后回调
                    }
                }
            } else if (requestCode == IMAGE_CUT_CODE) {
                if (data != null) {
                    val filePaths = arrayListOf<Uri>()
                    val selectList = PictureNewHelper.afterSelectPhotoPaths(data)
                    if (selectList.isNotEmpty()) {
                        selectList.forEach { element ->
                            filePaths.add(Uri.fromFile(File(element)))
                        }
                    }
                    showLog("返回选择文件信息")
                    fileChooseCallback?.onReceiveValue(filePaths.toTypedArray())// web 直选文件回调
                    fileChooseCallback = null // 不释放不能下次选取图片
                    showLog("返回选择文件信息 之后")
                } else {
                    fileChooseCallback?.onReceiveValue(arrayOf())
                    fileChooseCallback = null // 不释放不能下次选取图片
                }
            } else if (requestCode == Codes.SIGN_REQUEST && data != null) {
                //手写签名的结果返回
                val byteArray = data.getByteArrayExtra("bitmap")
                val bitmap = BitmapFactory.decodeByteArray(byteArray, 0, byteArray!!.size)

//                ceshibaseiv.setImageBitmap(bitmap)//测试用------------------------------------------------------------------------
                //反馈给web端
                val base64List = arrayListOf<String>()

                val baos = ByteArrayOutputStream()
                bitmap.compress(
                    Bitmap.CompressFormat.PNG,
                    100,
                    baos
                )//如果使用JPEG格式的话，从base64转化过来的图片显示为黑屏

                base64List.add(Base64.encodeToString(baos.toByteArray(), Base64.DEFAULT))

                val jsonObject = JSONObject()
                jsonObject.put("images", GsonUtil.toJson(base64List))

                invokeJs("getBase64String", jsonObject.toString())
            } else if (requestCode == request_code_file_chooser_for_web) {
                //web调用系统的文件选择器
                afterFileChooseGoing(resultCode, data)
            }
        } else if (resultCode == Activity.RESULT_CANCELED) {
            if (requestCode == request_code_file_chooser_for_web) {
                afterFileChooseGoing(resultCode, data)
            }
        } else {
            if (fileChooseCallback != null) {
                fileChooseCallback?.onReceiveValue(arrayOf())
            }
            fileChooseCallback = null // 不释放不能下次选取图片
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

    private fun afterFileChooseGoing(resultCode: Int, data: Intent?) {
        Loggerr.i("web文件选择", "===回调执行了===")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            if (fileChooseCallback == null) {
                return
            }
            val uris = WebChromeClient.FileChooserParams.parseResult(resultCode, data)
            fileChooseCallback?.onReceiveValue(uris)
        }
        fileChooseCallback = null
    }

    /**todo 重新刷新获取审批详情*/
    private fun loadAprDetail() {
        onBackPress()
    }

    private var pageInited = false

    override fun onWebGoBack() {
        EventBusUtils.sendEvent(EventBusEvent(MsgType.APR_DETAIL_READED.name, null))
        EventBusUtils.sendEvent(EventBusEvent(EventBusAction.refresh_all_appro_undo, 0))
        closePage()
    }

    private var objName = "ddbes_approve"

    /**web初始化准备后交互，实现对头部title栏的控制，增加标题，子标题，右侧按钮文字 右侧图标等设置*/
    override fun onWebReady(json: String) {
        val obj = JSONObject(json)

        if (!obj.optString("objName").isNullOrBlank()) {
            this.objName = obj.optString("objName")
        }
        showLog("web 调用 web准备完毕回调 $objName")
        if (obj.optBoolean("nativeTitle")) {
            llTitleBar.visibility = View.VISIBLE
            tvTitle.text = obj.optString("title")
            if (!obj.optString("subTitle").isNullOrBlank()) {
                tvSubTitle.visibility = View.VISIBLE
                tvSubTitle.text = obj.optString("subTitle")
            } else {
                tvSubTitle.visibility = View.GONE
            }
            if (!obj.optString("rightText").isNullOrBlank()) {
                ivRight.visibility = View.GONE
                tvRight.visibility = View.VISIBLE
                tvRight.text = obj.optString("rightText")

                ivRight.setOnClickListener(null)
                tvRight.setOnClickListener(this)
            } else if (!obj.optString("rightImage").isNullOrBlank()) {
                tvRight.visibility = View.GONE
                ivRight.visibility = View.VISIBLE
                ImageLoaderUtils.loadImage(mActivity, ivRight, obj.optString("rightImage"))
                ivRight.setOnClickListener(this)
                tvRight.setOnClickListener(null)
            } else {
                // TODO: 2021/7/7 test
                tvRight.visibility = View.GONE
                ivRight.visibility = View.GONE

                ivRight.setOnClickListener(null)
                tvRight.setOnClickListener(null)
            }
        } else {
            llTitleBar.visibility = View.GONE
            updateStatusBarHeight()
        }
        // web初始化完成后，回调token，用户信息，当前团队信息到web页面
        updateClientInfo()
    }

    /**web实现标题栏时，通知web状态栏高度*/
    private fun updateStatusBarHeight() {
        if (statusHeight == 0) {
            statusHeight = ScreenUtils.px2dip(
                requireContext(),
                ScreenUtils.getStatusBarHeight2(requireContext())
            )
        }
        invokeJs("updateStatusBarHeight", statusHeight.toString())
    }

    private fun updateClientInfo() {
        showLog("web 调用 获取客户端信息")
        val map = hashMapOf<String, Any>()
        map["token"] = mActivity.accessToken
        if (UserHolder.isLogin()) {
            map["user"] = UserHolder.getCurrentUser()!!
        }
        if (CompanyHolder.getCurrentOrg() != null) {
            map["company"] = CompanyHolder.getCurrentOrg()!!
        }
        if (statusHeight == 0) {
            statusHeight = ScreenUtils.px2dip(
                requireContext(),
                ScreenUtils.getStatusBarHeight2(requireContext())
            )
        }
        map["statusHeight"] = statusHeight
        invokeJs("updateClientInfo", GsonUtil.toJson(map))
    }

    override fun onWebInitFinish() {
        showLog("web初始化完成，需要返回数据给web，用户数据和当前公司数据 wasteTime ${System.currentTimeMillis() - start}")
        hideLoading()
        webSaveToken()
        webSaveUserInfo()
        webSaveCurrentCompany()
        isReady = 1
    }

    override fun webGetToken() {
        // 刷新token后返回token给web
        webSaveToken()
    }

    override fun webSaveToken() {
        fun saveToWeb(token: String) {
            showLog("回调给web用户token [$token]")
            invokeJs("saveToken", "\"" + token + "\"")
        }
        // 保存token到h5
        if (!UserHolder.isTokenExpired()) {
            Flowable.create({ emitter: FlowableEmitter<String> ->
                val call = LoginService.refreshToken(UserHolder.getRefreshToken())
                val tokenBean = call.execute().body()
                if (tokenBean != null) {
                    UserHolder.saveToken(tokenBean)
                    emitter.onNext(UserHolder.getAccessToken())
                } else {
                    emitter.onNext("-2")
                }
            }, BackpressureStrategy.BUFFER)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(object : BaseSubscriber<String>() {
                    override fun onError(ex: ApiException) {
//                        EventBusUtils.sendEvent(EventBusEvent(IMEvent.MULTIUSER_LOGIN, "请重新登录"))
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(t: String?) {
                        if (!t.isNullOrBlank() && t != "-2") {
                            showLog("回调给web用户token $t")
                            saveToWeb(t)
                        } else {
//                            EventBusUtils.sendEvent(EventBusEvent(IMEvent.MULTIUSER_LOGIN, "请重新登录"))
                        }
                    }
                })
        } else {
            saveToWeb(UserHolder.getAccessToken())
        }
    }

    override fun webSaveUserInfo() {
        if (UserHolder.isLogin()) {
            invokeJs("saveUserinfo", GsonUtil.toJson(UserHolder.getCurrentUser()))
        } else {
            invokeJs("saveUserinfo", JSONObject().toString())
        }
    }

    private fun getCompany(companyId: String): WorkStationBean? {
        return if (companyId.isNotBlank()) {
            if (CompanyHolder.getCurrentOrg() != null
                && companyId == CompanyHolder.getCurrentOrg()?.companyId
            ) {
                CompanyHolder.getCurrentOrg()
            } else if (CompanyHolder.getAllNormalOrg().isNotEmpty()
                && CompanyHolder.getAllNormalOrg().find { it.companyId == companyId } != null
            ) {
                CompanyHolder.getAllNormalOrg().find { it.companyId == companyId }
            } else if (CompanyHolder.getCooperationOrg().isNotEmpty()
                && CompanyHolder.getCooperationOrg().find { it.companyId == companyId } != null
            ) {
                CompanyHolder.getCooperationOrg().find { it.companyId == companyId }
            } else {
                null
            }
        } else {
            CompanyHolder.getCurrentOrg()
        }
    }

    override fun webSaveCurrentCompany() {
        if (UserHolder.isLogin()) {
            if (currentCompany != null) {
                invokeJs("saveCurrentCompany", GsonUtil.toJson(currentCompany))
            } else {
                invokeJs("saveCurrentCompany", JSONObject().toString())
            }
        }
    }

    override fun onCopy(data: String) {
        XUtil.toClipboard(mActivity, data) {
            showToast("复制成功")
        }
    }

    override fun onSaveData(data: String) {
        if (UserHolder.isLogin()) {
            val oldValue = MMKVUtil.getString("${UserHolder.getUserId()}_doc_cache")
            val map = if (oldValue.isNotBlank()) {
                GsonUtil.fromJson2(oldValue, object : TypeToken<HashMap<String, Int>>() {}.type)
                    ?: hashMapOf()
            } else {
                hashMapOf<String, Int>()
            }
            val subMap: HashMap<String, Int>? =
                GsonUtil.fromJson2(data, object : TypeToken<HashMap<String, Int>>() {}.type)
            if (!subMap.isNullOrEmpty()) {
                map.putAll(subMap)
                MMKVUtil.saveString("${UserHolder.getUserId()}_doc_cache", GsonUtil.toJson(map))
            }
        }
    }

    override fun onGetData() {
        if (UserHolder.isLogin()) {
            val data = MMKVUtil.getString("${UserHolder.getUserId()}_doc_cache")
            if (data.isNotBlank()) {
                webSaveData(data)
            } else {
                webSaveData(JSONObject().toString())
            }
        }
    }

    override fun webSaveData(data: String) {
        invokeJs("pan_getStorage", data)
    }

    override fun selectPic(parentId: String, type: Int) {

    }

    override fun takePhoto(parentId: String) {

    }

    override fun onFileUploaded() {

    }

    override fun previewFile(json: String) {
        // 预览文件{type，data}
//        {"type":1,
//        "data":"https://ddbes-pan-test-1257239906.cos.ap-beijing.myqcloud.com/2495502770304451581?sign=
//        q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLRJwTlAa8QUIqCi8j4C4g5HDuLWVnjy1%26q-sign-time%3D1596676805%3B1596677705%26q-key-time%3D1596676805%3B1596677705%26q-header-list%3D%26q-url-param-list%3Dresponse-cache-control%3Bresponse-content-disposition%3Bresponse-content-language%3Bresponse-expires%26q-signature%3D861f622893c68c1da9267d86c4325b9ce82ae2c2
//        &response-cache-control=no-cache
//        &response-content-disposition=attachment%3B%20filename%3D%22EVA%25E5%25A4%25A7%25E5%25B1%2595%25E4%25B8%25AD%25E5%259B%25BD%25E9%25A3%258E.jpg%22
//        &response-content-language=zh-CN&response-expires=Fri%2C%2007%20Aug%202020%2001%3A20%3A05%20GMT",
//        "name":"EVA大展中国风.jpg"}

        val data = JSONObject(json)

        val fileUrl = data.optString("data")

        if (StringUtils.isNotBlankAndEmpty(fileUrl)) {
            if (data.optInt("type") == 1) {
                val intent = Intent(mActivity, TaskImagePreviewActivity::class.java)
                val previewDataBean = TaskImagePreviewActivity.PreviewDataBean(
                    0, arrayListOf(fileUrl), isManager = true, isPanFile = true
                )
                val bundle = Bundle()
                bundle.putSerializable("previewData", previewDataBean)
                intent.putExtras(bundle)
                mActivity.startActivity(intent)
            } else {
                val name = data.optString("name")
                FilePreviewProvider.getPreviewPost(
                    name, 0L, fileUrl, data.optString("ext")
                ).withBoolean("onlyShow", true).navigation(mActivity)
            }
        }
    }

    override fun previewFileArray(json: String) {
        if (json.isNotBlank() && json.startsWith("{") && json.endsWith("}")) {
            val data = JSONObject(json)
            val index = data.optInt("index")
            val temp = arrayListOf<String>()
            val array = data.optJSONArray("list") ?: return
            if (array.length() > 0) {
                for (i in 0 until array.length()) {
                    temp.add(array[i] as String)
                }
            }
            val intent = Intent(mActivity, TaskImagePreviewActivity::class.java)
            val previewDataBean = TaskImagePreviewActivity.PreviewDataBean(
                index, temp, isManager = true, isPanFile = true
            )
            val bundle = Bundle()
            bundle.putSerializable("previewData", previewDataBean)
            intent.putExtras(bundle)
            mActivity.startActivity(intent)
        }
    }

    override fun previewFileOnline(json: String) {
        if (json.isNotBlank() && json.startsWith("{") && json.endsWith("}")) {
            val data = JSONObject(json)
            val name = data.optString("name")
            FilePreviewProvider.getPreviewPost(
                name, 0L, data.optString("link"), data.optString("ext")
            ).withBoolean("onlyShow", true).navigation(mActivity)
        }
    }

    private fun showTip(info: String, outSizeEnable: Boolean = true, finish: Boolean = false) {
        val helper = object : CenterDialogHelper(
            mActivity,
            onConfirm = {
                if (finish) {
                    //模板停用或者审批已加审给他人，提示并关闭页面
                    onBackPress()
                }
            },
            onCancel = { }
        ) {
        }

        helper.initView()
        helper.onConfig(
            DialogConfig(
                content = info, confirm = "好的", showHLine = true,
                showLine = false, useDefault = false
            )
        )
        helper.show(outSizeEnable, onDismissListener = DialogInterface.OnDismissListener {
//                    if (modelIndex == 2 && aprDetailData!!.approveInfo.stteId == 1
//                            && userId in unProcessNodes && unProcessNodes.indexOf(userId!!) != 0) {
//                        onBackPressed()
//                    }
        })
    }

    override fun reCommitApprove(json: String) {
        if (json.isNotBlank() && json.startsWith("{") && json.endsWith("}")) {
            val aprDetailData = GsonUtil.fromJson<ApprovalDetailData>(json) ?: return

            /**重新提交校验回调*/
            val observer = object : Observer<CommonResult<Boolean>> {
                override fun onChanged(result: CommonResult<Boolean>) {
                    hideLoading()
                    RequestHelper.onResponse(result,
                        onSuccess = {
                            if (it) {
                                // 只传递审批id，重新提交传递文本控件内容
                                val reCommitType = arrayOf(1, 2)
                                val reCommitData = hashMapOf<String, String>()
                                for (widget in aprDetailData.content) {
                                    if (widget.type in reCommitType && widget.content != null
                                        && widget.content!!.isNotBlank()
                                    ) {
                                        reCommitData[widget.widgetId!!] = widget.content!!
                                    }
                                }
                                val bundle = Bundle()
                                if (reCommitData.size > 0) {
                                    bundle.putString("widgets", GsonUtil.toJson(reCommitData))
                                }
                                bundle.putString(
                                    ConsKeys.TARGET_ID,
                                    aprDetailData.approveInfo.approveId
                                )
                                bundle.putString(
                                    ConsKeys.MODEL_ID,
                                    aprDetailData.approveInfo.modelId
                                )
                                bundle.putString(ConsKeys.COMPANY_ID, companyId)
                                ARouter.getInstance()
                                    .build(RouteApr.APR_LAUNCH_CREATE)// 重新发起审批
                                    .with(bundle)
                                    .navigation()
                            } else {
                                showTip("此审批模板已停用、修改或删除\n无法重新提交")
                            }
                        },
                        onError = { _, msg ->
                            showToast(msg)
                        },
                        onDefault = { msg ->
                            showToast(msg)
                        })
                    viewModel.reCommitCheckResult.removeObserver(this)
                }
            }

            viewModel.reCommitCheckResult.observe(this, observer)
            viewModel.reCommitCheck(aprDetailData.approveInfo.approveId)
        }
    }

    //点击加审
    override fun addTrial(json: String) {
        val companyId = currentCompany?.companyId ?: return
        if (json.isNotBlank() && json.startsWith("{") && json.endsWith("}")) {
            val aprDetailData = GsonUtil.fromJson<ApprovalDetailData>(json) ?: return

            // 2020/3/16 8:55 加审新逻辑：判断选择人员类型-》加审位置选择（之前或之后）-》
            //  选择人员-》排序结果返回-》拼接人员数组，提交加审。
            // 不再过滤筛选判断
            fun onPreAddTrial() {
                fun selectApprovalPerson(companyId: String) {
                    fun selectPerson(position: Int = 0) {
                        val bundle = Bundle()
                        bundle.putInt("personType", position)
                        bundle.putString("companyId", companyId)
                        bundle.putString("detailInfo", GsonUtil.toJson(aprDetailData))
                        val intent = Intent(mActivity, AprApproveAddActivity::class.java)
                        intent.putExtras(bundle)
                        startActivityForResult(intent, Codes.REQUEST_APRDETAIL_ADD_NODE)
//                        ARouter.getInstance().build(RouteApr.PAGE_APR_ADD_PERSON)
//                                .with(bundle).navigation(mActivity, Codes.REQUEST_APRDETAIL_ADD_NODE)
                    }

                    if (aprDetailData.approveInfo.outApprove == 0) {
                        selectPerson()
                    } else {
                        val dialog = object : DialogHolder(
                            mActivity,
//                            R.layout.dialog_white_top_corner, Gravity.BOTTOM
                            com.joinutech.ddbeslibrary.R.layout.dialog_white_top_corner, Gravity.BOTTOM
                        ) {
                            override fun bindView(dialogView: View) {
                                val cancel = dialogView.findViewById<TextView>(R.id.cancel)
                                cancel.setOnClickListener {
                                    dialog?.dismiss()
                                }
                                val typeListRv = dialogView.findViewById<RecyclerView>(R.id.rv_list)
                                typeListRv.layoutManager = LinearLayoutManager(mActivity)
                                val titleList = arrayListOf<ApprovalTypeDialogBean>()
                                titleList.add(ApprovalTypeDialogBean("团队内部人员"))
                                titleList.add(ApprovalTypeDialogBean("外部协作人员"))
                                val adapter = ApprovalTypeSelectDialogAdapter(mActivity, titleList)
                                typeListRv.adapter = adapter
                                adapter.setItemClickListener(object : ItemClickListener {
                                    override fun onItemClick(position: Int) {
                                        selectPerson(position)
                                        dialog?.dismiss()
                                    }
                                })
                            }
                        }
                        dialog.initView()
                        dialog.show(true)
                    }
                }

                // 加审人员类型判断：团队内和外部协作人判断
                var ownIsOuter = false
                val assignes = aprDetailData.approveProcess.assignee
                if (assignes.isNotEmpty()) {
                    for (i in assignes.indices) {
                        val assignee = assignes[i]
                        if (assignee.approveType == 1) {
                            ownIsOuter =
                                (userId!! == assignes[i].assigneeUser[0].userId && assignes[i].assigneeUser[0].isOuter == 1)
                            if (ownIsOuter) {
                                break
                            }
                        } else {
                            //TODO 审批组成员处理
                        }
                    }
                }
                /**外部联系人不可以加审，团队内部人员可选择内部或者外部协作人员加审*/
                if (!ownIsOuter && StringUtils.isNotBlankAndEmpty(companyId)) {
                    selectApprovalPerson(companyId)
                } else {
                    showToast("你暂时不能加审")
                }
            }

            onPreAddTrial()
        }
    }

//    /**
//     * {
//     * "fileName":"PictureSelector_20200804_182917.JPEG.JPEG",
//     * "fileType":"0", // 0 带后缀 1文件夹 2 多图
//     * "textarea":"Julia云资产的赵阳阳，分享给你一个文件【PictureSelector_20200804_182917.JPEG.JPEG】",
//     * "link":"https://pan.ddbes.com/pan.html#/share/2Ylt0JZi33n",
//     * "password":"6511",
//     * "ids":["2488484430818051069"]
//     * }
//     *
//     * {
//     * "fileName":"PictureSelector_20200804_182924.JPEG.JPEG等",
//     * "fileType":"2",
//     * "textarea":"Julia云资产的赵阳阳，分享给你一个文件【PictureSelector_20200804_182924.JPEG.JPEG等】",
//     * "link":"https://pan.ddbes.com/pan.html#/share/2Ylt6PnQhdz",
//     * "password":"2906",
//     * "ids":["2488484430818051069"]
//     * }
//     *
//     * {
//     * "fileName":"我测试",
//     * "fileType":"1",
//     * "link":"https://pan.ddbes.com/pan.html#/share/2Ylt8jYtROl",
//     * "password":"9582",
//     * "textarea":"Julia云资产的赵阳阳，分享给你一个文件【我测试】",
//     * "ids":["2468510353621921789","2488484430818051069"]
//     * }
//     * */
//    override fun shareToFriend(json: String) {
//    }

//    /**
//     * {
//     * "fileName":"1596000353179113.jpg",
//     * "fileType":0,
//     * "textarea":"云服务团队的金刚，分享给你一个文件【1596000353179113.jpg】",
//     * "link":"https://pan.ddbes.com/pan.html#/share/2YlVwoQG8Xz",
//     * "password":"8878",
//     * "ids":[]
//     * }*/
//    override fun shareToWx(json: String) {
//    }
//
//    override fun shareToOrgMember(msg: String) {
//    }

    override fun onBackPress() {
        showLog("页面回退处理 :: 回调给web onBackPress方法处理后退逻辑")
        if (canGoBack == 1 || isReady == 1) {
            invokeJs("onBackPress", "")
        } else {
            onWebGoBack()
        }
    }

    /**
     * dealApproval() web 审批结果回调
     * status:-1同意
     * -2拒绝
     * paramId:审批id
     * */
    override fun dealApproval(data: String) {
        // TODO: 2020/8/31 8:55 审批结果回调后，更新本地审批消息状态
        if (data.isNotBlank() && data.startsWith("{") && data.endsWith("}")) {
            val json = JSONObject(data)
            val approveId = json.optString("paramId")
            val status = json.optInt("status")
            if (status == -1 || status == -2) {
//                ApprovalUtil().updateAprData(mActivity, approveId, status) {}
            }
        }
    }

    /**approveRefreshStatus({approveId: '', status: })*/
    override fun onApprovalNotice(json: String) {
        // TODO: 2020/8/31 8:55 审批消息收到后通知到web页面
        invokeJs("approveRefreshStatus", json)
//        val map = hashMapOf<String, Any>()
//        map["companyId"] = msg.companyId
//        map["approveId"] = msg.paramId
//        map["typeId"] = aprNotice.typeId
////                showLog("IM :: 接收到审批通知，触发审批相关页面刷新，触发团队列表刷新更新团队红点")
//        EventBusUtils.sendEvent(EventBusEvent("apr_notice_receive_event", map))
    }

    override fun onApprovalComment(companyId: String, approvalId: String) {
        val intent = Intent(mActivity, AprCommentActivity::class.java)
        intent.putExtra(ConsKeys.TARGET_ID, approvalId)
        intent.putExtra(ConsKeys.COMPANY_ID, companyId)
        startActivityForResult(intent, Codes.REQUEST_APRDETAIL_ADD_COMMENT)
    }

    private var shareInfo: ShareFileBean? = null

    //审批详情---点击导出---点击右上角分享图标---点击担当、微信或者邮箱后执行；
    override fun shareFile(fileBean: ShareFileBean) {
        showLog("$fileBean")
        when (fileBean.path) {
            "9" -> {
//                showToast("分享到担当")
                if (fileBean.fileId.isNullOrBlank() || fileBean.fileUrl.isNullOrBlank()) return

                fun selectMember() {
                    shareInfo = fileBean

                    ARouter.getInstance().build(RouteOrg.selectMemberShareActivity)
                        .withString("pageFlag", "select_share_target")
                        .navigation()
                }

                if (fileBean.fileSize >= 1024 * 1024 * 100) {
                    fileBean.msgType = 0
                    // 分享链接到担当
                    selectMember()
                } else {
                    fileBean.msgType = 1
                    // TODO: 2021/7/1 15:05  转存文件到Im存储桶，选择目标成员，查询对应的imId，最后发送im消息
                    fun transFileToImBucket() {
                        val bundle = Bundle()
                        bundle.putString("fileId", fileBean.fileId)
                        bundle.putString("fileName", fileBean.fileName)
                        if (StringUtils.isNotBlankAndEmpty(fileBean.approvePdfId)) {
                            bundle.putString("typePdf", "approve")
                        } else if (StringUtils.isNotBlankAndEmpty(fileBean.ticketPdfId)) {
                            bundle.putString("typePdf", "ticket")
                        }
                        (ARouter.getInstance().build(RouteProvider.APR_FUN_PROVIDER)
                            .navigation() as RouteServiceProvider)
                            .service("transFileToImBucket", bundle) {
                                showLog("result is $it")
                                if (!it.isNullOrBlank() && !it.startsWith("error")) {
                                    fileBean.fileId = it // 更新为转存到im中的fileId
                                    selectMember()
                                } else {
                                    showToast("转存到im存储失败")
                                }
                            }
                    }
                    transFileToImBucket()
                }
            }
            "11" -> {
//                showToast("分享到微信")
                val fileIcon = FileUtil.getFileTypeIcon(fileBean.fileName)

                if (fileBean.fileId.isNullOrBlank() || fileBean.fileUrl.isNullOrBlank()) return
                val helper = WechatHelper()
                if (fileBean.fileSize >= 1024 * 1024 * 10) {
                    // 分享链接到微信
                    val thumbBmp = BitmapFactory.decodeResource(mActivity.resources, fileIcon)
                    val icon = JavaUtils.bmpToByteArray(thumbBmp, true)
                    if (icon != null && icon.isNotEmpty()) {
                        helper.initApi(requireContext()) {
//                            helper.sendLinkToWx(icon, fileBean.fileName, CommonUtils.convertFileSize(fileBean.fileSize), fileBean.fileUrl)
                            helper.sendTextToWx(
                                "分享审批附件《${fileBean.fileName}》有效时间3天：\n${Html.fromHtml(fileBean.fileUrl)}",
                                fileBean.fileName
                            )
                        }
                    }
                } else {
                    val localPath =
                        requireContext().cacheDir.absolutePath + File.separator + "share_file" + File.separator + fileBean.fileName

                    // 下载文件后分享到微信
                    fun getDownLoadUrl3() {
//                    val targetDir = DOWNLOAD_DIR
                        val targetDir =
                            requireContext().cacheDir.absolutePath + File.separator + "share_file"
                        val targetName = fileBean.fileName
                        showLog("downloadUrl = [${fileBean.fileUrl}]")
                        showLog("downloadUrl2 = [${Html.fromHtml(fileBean.fileUrl)}]")
                        showLoading()
                        OkHttpUtils.get().url(fileBean.fileUrl).build()
                            .execute(object : FileCallBack(
                                targetDir, targetName
                            ) {

                                override fun onResponse(file: File?, p1: Int) {
                                    //当文件下载完成后回调
                                    LogUtil.showLog("文件下载 完成 ${file?.absolutePath}")
                                    hideLoading()
                                    if (file != null && file.exists()) {
                                        helper.initApi(requireContext()) {
                                            helper.shareFileToWx(file, fileIcon)
                                        }
                                    }
                                }

                                override fun onError(p0: Call?, e: Exception?, code: Int) {
                                    hideLoading()
                                    LogUtil.showLog("文件下载 失败 $code -- ${e?.message}")
                                    showToast("分享失败")
                                }

                                override fun inProgress(progress: Float, total: Long, id: Int) {
                                    //progress*100为当前文件下载进度，total为文件大小
                                    LogUtil.showLog("文件下载 进行中 $progress / $total")
                                    if ((progress * 100).toInt() % 10 == 0) {
                                        //避免频繁刷新View，这里设置每下载10%提醒更新一次进度
                                    }
                                }
                            })
                    }

                    fun getDownLoadUrl2() {
                        if (fileBean.approvePdfId.isNullOrBlank()) return
                        val bundle = Bundle()
                        bundle.putString("approvePdfId", fileBean.approvePdfId)
                        (ARouter.getInstance().build(RouteProvider.APR_FUN_PROVIDER)
                            .navigation() as RouteServiceProvider)
                            .service("getExportFileUrl", bundle) {
                                showLog("下载地址为：${it}")
                                // {"code":1,"msg":"请求成功","data":{"approvePdfId":"2526516646470750205","exportName":"2021-07-06 16:25:39导出小四-请假申请","approveId":"2522239838296474621","userId":"2488484430818051069","createTime":1625559939464,"fileId":"2526516646470749181","type":1,"organizationId":"2518723558234915837","fileName":"2021-07-06 16:25:39导出小四-请假申请.pdf","url":"https://ddbes-pan-test-**********.cos.ap-chongqing.myqcloud.com/2526516645397005309?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKID0oNgiTYBdrob6n8RU7TYYHPJfYAYtpqp%26q-sign-time%3D1625621318%3B1625623118%26q-key-time%3D1625621318%3B1625623118%26q-header-list%3D%26q-url-param-list%3Dresponse-cache-control%3Bresponse-content-disposition%3Bresponse-content-language%3Bresponse-expires%26q-signature%3D3c8de3d19cac434d840d02baed63622355270129&response-cache-control=no-cache&response-content-disposition=attachment%3B%20filename%3D%222021-07-06%2B16%253A25%253A39%25E5%25AF%25BC%25E5%2587%25BA%25E5%25B0%258F%25E5%259B%259B-%25E8%25AF%25B7%25E5%2581%2587%25E7%2594%25B3%25E8%25AF%25B7.pdf%22&response-content-language=zh-CN&response-expires=Thu%2C%2008%20Jul%202021%2001%3A28%3A38%20GMT","fileSize":0}}

                            }
                    }

                    fun getDownLoadUrl1() {
                        FileDownTransferManager.singleFileDownload(
                            11,
                            FileTransferInfo(
                                fileId = fileBean.fileId,
                                srcPath = localPath,
                                total = fileBean.fileSize
                            ),
                            onSuccess = {
                                val file = File(localPath)
                                if (file.exists()) {
                                    helper.initApi(requireContext()) {
                                        helper.shareFileToWx(
                                            file,
                                            com.joinutech.ddbeslibrary.R.mipmap.ic_file_type_pdf
                                        )
                                    }
                                }
                            },
                            onError = {
                                requireContext().toastShort("分享失败")
                            },
                            onCreateTask = {},
                            onTransfer = {})
                    }

                    getDownLoadUrl3()
                }
            }
            "10" -> {
//                showToast("分享到邮箱")
                if (fileBean.fileId.isNullOrBlank()) return
                val bundle = Bundle()
                bundle.putString("type", "email")
                bundle.putString("name", UserHolder.getCurrentUser()?.email)
                bundle.putInt("requestCode", 1009)
                (ARouter.getInstance().build(RouteProvider.WORK_PROVIDER)
                    .navigation() as RouteServiceProvider)
                    .openPageWithResult(mActivity, "checkEmail", bundle) {
                        if (it == "success") {
                            // // TODO: 2021/7/1 15:07  调用接口分享到邮箱
                            val bundle = Bundle()
//                                bundle.putString("approvePdfId", fileBean.fileId)
                            if (StringUtils.isNotBlankAndEmpty(fileBean.approvePdfId)) {
                                bundle.putString("approvePdfId", fileBean.approvePdfId)
                            } else if (StringUtils.isNotBlankAndEmpty(fileBean.ticketPdfId)) {
                                bundle.putString("ticketPdfId", fileBean.ticketPdfId)
                            }
                            (ARouter.getInstance().build(RouteProvider.APR_FUN_PROVIDER)
                                .navigation() as RouteServiceProvider)
                                .service("transFileToEmail", bundle) { result ->
                                    if ("success" == result) {
                                        showToast("已发送到邮箱，请注意查收！")
                                    }
                                }
                        }
                    }
            }
            else -> {

            }
        }
    }

    override fun toSignaturePage() {
        val intent = Intent(mActivity, AprSignatureActivity::class.java)
        startActivityForResult(intent, Codes.SIGN_REQUEST)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSelectMemberResult(event: EventBusEvent<List<OrgImportPeopleBean>>) {
        if (event.code == "select_share_target" && !event.data.isNullOrEmpty() && event.data is List<OrgImportPeopleBean>) {
//            requireContext().toastShort("分享给 ${event.data?.joinToString { it.name }}")

            val bundle = Bundle()
            bundle.putString("userId", userId)
            val method = if (1 == shareInfo?.msgType) {
                bundle.putString("fileId", shareInfo?.fileId)
                bundle.putString("fileName", shareInfo?.fileName)
                bundle.putLong("fileSize", shareInfo?.fileSize ?: 0L)
                "sendFileMsg"
            } else {
                bundle.putString(
                    "content",
                    "分享审批附件《${shareInfo?.fileName}》有效时间3天：\n${Html.fromHtml(shareInfo?.fileUrl)}"
                )
                "sendTextMsg"
            }

            /**向用户发送im消息 文件消息或链接（文本）消息*/
            fun sendMessage(bean: OrgImportPeopleBean) {
                bundle.putSerializable("userInfo", UserInfo(bean.userId, bean.headimg, bean.name))
                (ARouter.getInstance().build(RouteProvider.IM_PROVIDER)
                    .navigation() as RouteServiceProvider)
                    .service(method, bundle) {
                        if ("success" == it) {
                            LogUtil.showLog("分享到担当成功 $it")
                        } else {
                            LogUtil.showLog("分享到担当失败 $it")
                        }
                    }
            }

            /**获取用户的imId*/
            fun findUserImId(userIds: List<String>) {
                // TODO: 2021/7/2 9:12 获取imId 后 调用im发送消息
                val api =
                    RetrofitClient.single_intance.getRxRetrofit().create(PersonApi::class.java)
                RxScheduleUtil.rxSchedulerHelper(api.getUserImId(userIds))
                    .compose(ErrorTransformer.getInstance())
                    .subscribe(object : BaseSubscriber<Any>() {
                        override fun onError(ex: ApiException) {
                        }

                        override fun onComplete() {

                        }

                        override fun onNext(t: Any?) {
//                                if (1 == shareInfo["msgType"]) {
//                                    bundle.putString("fileId", shareInfo["fileId"] as String)
//                                    bundle.putString("fileName", shareInfo["fileId"] as String)
//                                    bundle.putLong("fileSize", shareInfo["fileSize"] as Long)
//                                } else {
//                                    bundle.putString("content", shareInfo["fileUrl"] as String)
//                                }
//                                sendMessage()
                        }
                    })
            }
//
//            findUserImId(event.data!!.map { it.userId }.toList())

            event.data?.forEach {
                sendMessage(it)
            }
        }
    }

    //交互
    class ApproveMutualInterface(
        private val activity: AppCompatActivity,
        private val listener: OnApproveInvokeListener?
    ) {

        @JavascriptInterface
        fun client_goBack() {
            FileStorage.showLog("审批web版本 页面---  点击返回键")
            activity.runOnUiThread {
                listener?.onWebGoBack()
            }
        }

        @JavascriptInterface
        fun isAlready() {
            FileStorage.showLog("审批web版本 页面---  加载完毕回调")
            activity.runOnUiThread {
                listener?.onWebInitFinish()
            }
        }

        @JavascriptInterface
        fun getToken() {
            FileStorage.showLog("审批web版本 页面---  token过期后主动请求token")
            activity.runOnUiThread {
                listener?.webGetToken()
            }
        }

        @JavascriptInterface
        fun pan_CopyLink(data: String) {
            FileStorage.showLog("审批web版本 页面---  复制内容到剪贴板 $data")
            activity.runOnUiThread {
                listener?.onCopy(data)
            }
        }

        @JavascriptInterface
        fun pan_SetStorage(data: String) {
            FileStorage.showLog("审批web版本 页面---  存储内容到移动端 $data")
            activity.runOnUiThread {
                listener?.onSaveData(data)
            }
        }

        @JavascriptInterface
        fun pan_GetStorage() {
            FileStorage.showLog("审批web版本 页面---  获取存储到移动端的内容")
            activity.runOnUiThread {
                listener?.onGetData()
            }
        }

        /**
         * 分享文件给好友
         * fileType:0带后缀1文件夹2多图
         *
         * {
         * "fileName":"PictureSelector_20200804_182917.JPEG.JPEG",
         * "fileType":"0",
         * "textarea":"Julia云资产的赵阳阳，分享给你一个文件【PictureSelector_20200804_182917.JPEG.JPEG】",
         * "link":"https://pan.ddbes.com/pan.html#/share/2Ylt0JZi33n",
         * "password":"6511",
         * "ids":["2488484430818051069"]
         * }
         * */
        @JavascriptInterface
        fun pan_shareFileToFriend(json: String) {
            FileStorage.showLog("审批web版本 页面---  分享文件给好友--$json")
            activity.runOnUiThread {
//                listener?.shareToFriend(json)
            }
        }

        /**
         * 分享文件到微信
         * {
         * "fileName":"1596000353179113.jpg",
         * "fileType":0,
         * "textarea":"云服务团队的金刚，分享给你一个文件【1596000353179113.jpg】",
         * "link":"https://pan.ddbes.com/pan.html#/share/2YlVwoQG8Xz",
         * "password":"8878",
         * "ids":[]
         * }*/
        @JavascriptInterface
        fun pan_shareFileToWX(json: String) {
            FileStorage.showLog("审批web版本 页面---  分享文件到微信--$json")
            activity.runOnUiThread {
//                listener?.shareToWx(json)
            }
        }

        /**分享文件给同事*/
        @JavascriptInterface
        fun pan_shareFileToOrgMember(json: String) {
            FileStorage.showLog("审批web版本 页面---  分享文件给同事--$json")
            activity.runOnUiThread {
//                listener?.shareToOrgMember(json)
            }
        }

        /**
         * 预览文件 type 1 图片 2 文件
         *          data url
         *          name 文件名
         */
        @Deprecated("no used")
        @JavascriptInterface
        fun client_previewFile(json: String) {
            FileStorage.showLog("审批web版本 页面---  预览图片 $json")
            activity.runOnUiThread {
                listener?.previewFile(json)
            }
        }

        /**list，index*/
        @Deprecated("no used")
        @JavascriptInterface
        fun client_previewFileArray(json: String) {
            FileStorage.showLog("审批web版本 页面---  预览图片 $json")
            activity.runOnUiThread {
                listener?.previewFileArray(json)
            }
        }

        /*{link,name,ext}*/
        @JavascriptInterface
        fun client_previewFileOnLine(json: String) {
            FileStorage.showLog("审批web版本 页面---  预览文件 $json")
            activity.runOnUiThread {
                listener?.previewFileOnline(json)
            }
        }

        @JavascriptInterface
        fun client_resubmitApproval(data: String) {
            FileStorage.showLog("审批web版本 页面---  重新发起审批 $data")
            activity.runOnUiThread {
                listener?.reCommitApprove(data)
            }
        }

        //点击加审
        @JavascriptInterface
        fun client_addTrial(data: String) {
            FileStorage.showLog("审批web版本 页面---  审批加审 $data")
            activity.runOnUiThread {
                listener?.addTrial(data)
            }
        }

        /**
         * dealApproval()
         * status:-1同意
         * -2拒绝
         * paramId:审批id
         * 收到审批结果后，更新本地审批消息状态
         * */
        @JavascriptInterface
        fun dealApproval(data: String) {
            FileStorage.showLog("审批web版本 页面---  审批结果回调 $data")
            activity.runOnUiThread {
                listener?.dealApproval(data)
            }
        }

        @JavascriptInterface
        fun client_comment(data: String) {
            val json = JSONObject(data)
            val companyId = json.optString("companyId")
            val approId = json.optString("approId")
            FileStorage.showLog("审批web版本 页面---  跳转审批评论原生页面 $companyId ,$approId")
            if (!companyId.isNullOrBlank() && !approId.isNullOrBlank()) {
                activity.runOnUiThread {
                    listener?.onApprovalComment(companyId, approId)
                }
            }
        }

        //调用原生的手写签名页面
        @JavascriptInterface
        fun client_goSignatureVC() {
            activity.runOnUiThread {
                listener?.toSignaturePage()
            }
        }

        /**
         * web 调用原生页面
         * path：页面路由 1->发起审批
         *              2->跳转考勤规则页面
         *              3->选择部门信息
         *              4->考勤提醒设置页面
         *              5->考勤结果分享页面
         *              6->打开应用设置
         *              7->打开GPS开关设置页面
         *              8->打开wifi设置页面
         *              9->分享到担当
         *              10->分享到邮箱
         *              11->分享到微信
         *              12->跳转审批加审页面
         * params：跳转页面需要参数json，详细请看对应发送数据节点
         *
         * 发起审批，
         *  发送：{"path":"1", "modelId":"modelId"}
         *  接收：审批创建成后通知到web objName.refreshApproval()
         *
         * 跳转考勤规则页面，
         *  发送：{"path":"2", "ateId":"考勤组id", "version":"考勤组版本"}
         *
         * 选择部门信息
         *  发送：{"path":"3", "date":"部门数据历史版本时间戳","dept":["deptId"]}
         *  接收：objName.deptChoose(data)// 格式同发送
         *
         * 考勤提醒页面
         *  发送：{"path":"4"}
         *  接收：无
         *
         * 考勤分享页面
         *  发送：{"path":"5", "url":"打卡结果图片","content":"名言内容","name":"名言发布人名",
         *  "time":"打卡时间 时分秒"]}
         *
         *  应用设置页面
         *  发送：{"path":"6"}
         *  接收：无
         *
         *  GPS开关设置页面
         *  发送：{"path":"7"}
         *  接收：无
         *
         *  wifi设置页面
         *  发送：{"path":"8"}
         *  接收：无
         *
         *  分享到担当
         *  发送：{"path":"9","fileId":"","fileName":"","fileSize":0L,"fileUrl":""}
         *  fileSize >100M 分享链接到担当 否则 分享文件到担当
         *  接收：无
         *
         *  分享到邮箱
         *  发送：{"path":"10","fileId":"","fileName":"","fileSize":0L,"fileUrl":""}
         *  接收：无
         *
         *  分享到微信
         *  发送：{"path":"11","fileId":"","fileName":"","fileSize":0L,"fileUrl":""}
         *  fileSize >10M 分享链接到担当 否则 分享文件到担当
         *  接收：无
         *
         *  跳转审批加审页面
         *  发送：{"path":"12","companyId":"","data":"{}"} data 为审批详情数据的json
         *  接收：无
         *
         *  跳转审批评论页面
         *  发送：{"path":"13","companyId":"","approveId":""}
         *  接收：无
         *
         *  重新发起审批 重新发起之前需要验证是否可以发起
         *  发送：{"path":"14","data":"{}"} data 为审批详情数据的json
         *  接收：无
         */
        @JavascriptInterface
        fun client_jumpPage(params: String) {
            activity.runOnUiThread {
                try {
                    LogUtil.showLog("当前跳转页面参数为：$params")
                    if (JSONObject(params).optString("path") in arrayOf("9", "10", "11")) {
                        GsonUtil.fromJson<ShareFileBean>(params)?.let {
                            listener?.shareFile(it)
                        }
                    }
                } catch (e: Exception) {
                }
            }
        }
    }

    override fun onAction(actionCode: Int) {
        when (actionCode) {
            1 -> {
                ARouter.getInstance()
                    .build(RouteOrg.createOrgActivity)
                    .navigation()
            }
            2 -> {
                ARouter.getInstance()
                    .build(RouteOrg.searchResultActivity)
                    .withString("type", "searchOrg")
                    .navigation()
            }
            else -> {
                EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST, ""))
            }
        }
    }

}

interface OnApproveInvokeListener {

    /**web不再处理返回事件时调用*/
    fun onWebGoBack()

    /**web初始化准备后交互，实现对头部title栏的控制，增加标题，子标题，右侧按钮文字 右侧图标等设置*/
    fun onWebReady(json: String)

    /**web内容初始化完成*/
    fun onWebInitFinish()

    /**
     * web接收用户信息
     *
     * js method is :pan_getUserBaseInfo
     * */
    fun webSaveUserInfo()

    /**web获取用户token
     *
     * js method is :ddbes_pan.pan_h5SaveToken(token:String)
     * */
    fun webGetToken()

    /**
     * web 接收token
     * js method is :ddbes_pan.pan_h5SaveToken(token:String)*/
    fun webSaveToken()

    /**
     * web接收当前公司信息
     * 页面初始化完和切换公司后要通过这个方法返回给web json
     *
     * js method is :pan_getCurrentCompanyInfo
     * */
    fun webSaveCurrentCompany()

    /**web触发 复制功能，增加内容到剪贴板*/
    fun onCopy(data: String)

    /**web触发 存储数据到缓存*/
    fun onSaveData(data: String)

    /**web触发 获取设置的缓存信息*/
    fun onGetData()

    /**
     * web接收原生缓存的内容
     * js method is :pan_getStorage
     */
    fun webSaveData(data: String)

    /**选择照片*/
    fun selectPic(parentId: String, type: Int)

    /**拍照*/
    fun takePhoto(parentId: String)

    /**图片文件上传成功后，通知web*/
    fun onFileUploaded()

//    /**保存图片到相册 */
//    fun saveToGallery(json: String)

    /**预览文件 1 图片 2 文件*/
    @Deprecated("no used")
    fun previewFile(data: String)

    /**图片数组*/
    fun previewFileArray(data: String)

    /**在线预览文件 新 */
    fun previewFileOnline(data: String)

    /**重新发起审批 */
    fun reCommitApprove(data: String)

    /**审批加审*/
    fun addTrial(data: String)

    /**
     * dealApproval()
     * status:-1同意
     * -2拒绝
     * paramId:审批id
     * */
    fun dealApproval(json: String)

    //跳转到原生的手写签名页
    fun toSignaturePage()

    /**
     * 新审批消息收到后通知给审批页面，
     * web判断当前是否在审批页面和审批id是否为当前
     * approveRefreshStatus({approveId: '', status: })
     * */
    fun onApprovalNotice(json: String)

    /**
     * 跳转审批评论原生页面
     * @param companyId 公司id
     * @param approvalId 审批id
     */
    fun onApprovalComment(companyId: String, approvalId: String)

    /**type 9 分享给担当 11 分享给微信好友 10 邮箱*/
    fun shareFile(file: ShareFileBean)

//    /**分享文件给好友*/
//    fun shareToFriend(msg: String)
//
//    /**分享文件到微信*/
//    fun shareToWx(msg: String)
//
//    /**分享文件给同事*/
//    fun shareToOrgMember(msg: String)

    fun onBackPress()

}
