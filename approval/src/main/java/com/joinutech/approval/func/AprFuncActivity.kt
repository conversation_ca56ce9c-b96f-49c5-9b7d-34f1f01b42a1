package com.joinutech.approval.func

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.approval.AprBaseActivity
import com.joinutech.approval.R
import com.joinutech.approval.data.AprGroup
import com.joinutech.approval.data.AprItem
import com.joinutech.approval.databinding.ActivityAprFuncBinding
import com.joinutech.approval.utils.MsgType
import com.joinutech.approval.utils.RouteApr
import com.joinutech.common.base.LinkBuilder
import com.joinutech.common.base.isApproval
import com.joinutech.common.base.isFuture
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.utils.*
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * @className: AprFuncActivity
 * @desc: 审批功能列表页面,审批列表页tcp
 * @author: zyy
 * @date: 2019/8/2 9:12
 * @company: joinUTech
 * @leader: ke
 */
//在工作页，点击审批进入的页面
@Route(path = RouteApr.APR_FUNC_PAGE)
class AprFuncActivity(override val contentViewResId: Int = R.layout.activity_apr_func) :
    AprBaseActivity<ActivityAprFuncBinding>() {

    private lateinit var noDataViewPage: PageEmptyView
    val list = arrayListOf<AprGroup>()
    lateinit var adapter: MyListAdapter
    lateinit var rvList: RecyclerView
    lateinit var tvLaunchCount: TextView
    lateinit var tvHandleCount: TextView
    lateinit var tvCopyCount: TextView
    lateinit var tvTotalApprovalCount: TextView

    @Autowired
    @JvmField
    var companyId: String = ""

    override fun initView() {
        setPageTitle("审批")
       /* setRightTitle("设置", View.OnClickListener {
            jump(RouteApr.APR_FUNC_SET_PAGE)
        })*/
        setRightImage(R.mipmap.ic_setting_2,object:View.OnClickListener{
            override fun onClick(v: View?) {
                jump(RouteApr.APR_FUNC_SET_PAGE)
            }
        })


        noDataViewPage = findViewById(R.id.layout_empty_layout)
        noDataViewPage.setContent("暂无审批模板")
//        noDataViewPage.setEmptyIcon(R.drawable.ic_empty_approval)
        noDataViewPage.setEmptyIcon(com.joinutech.ddbeslibrary.R.drawable.ic_empty_approval)
        noDataViewPage.hide()
        noDataViewPage.setOnClickListener(this)
        noDataViewPage.visibility = View.GONE
        rvList = findViewById(R.id.rv_list)
        rvList.layoutManager = LinearLayoutManager(this)
//        rvList.addItemDecoration(ListItemDecoration(DeviceUtil.dip2px(this, 10f)))
        adapter = MyListAdapter(context = this, layoutId = R.layout.item_apr_fun_group_layout,
            data = list,
            onItemClick = { onItemClick(it) },
            onToggle = { position: Int, opened: Boolean ->
                if (opened) {
                    rvList.scrollToPosition(position)
                }
            })
        rvList.adapter = adapter

        tvLaunchCount = findViewById(R.id.tv_launch_count)
        tvHandleCount = findViewById(R.id.tv_handle_count)
        tvCopyCount = findViewById(R.id.tv_copy_count)
        tvTotalApprovalCount = findViewById(R.id.tv_total_approval_count)

        tvLaunchCount.visibility = View.GONE
        tvHandleCount.visibility = View.GONE
        tvCopyCount.visibility = View.GONE
        tvTotalApprovalCount.visibility = View.GONE

        //说明tcp，mainConmpanyId是主要公司，显示位于通讯录页中间的公司
        //当前公司指的是工作页选中的公司 ，也就是说主要公司和当前公司有可能并不是同一个
        //首页获取到所有公司的集合时，其中每一个公司的deptid字段为“0”就代表是我创建的公司；
        val currentOrg = CompanyHolder.getCurrentOrg()
        val isCreater = currentOrg?.deptId == "0"//判断是不是创建者tcp
        Loggerr.i("公司创建者", "====是否是创建者=deptId=" + currentOrg?.deptId)
        val hasSuperPermission =
            ORG_PERMISS_TYPE.checkSuperPermission(BaseApplication.totalPermission)
        if (BaseApplication.totalPermission.contains("7") || hasSuperPermission || isCreater) {
            binding.llTotalApproval.visibility = View.VISIBLE
            binding.llTotalApproval.setOnClickListener {
                //点击全部审批
                ARouter.getInstance().build(RouteApr.APR_FUNC_WEB_PAGE)
                    .withString(ConsKeys.COMPANY_ID, companyId)
                    .withString(
                        ConsKeys.PARAMS_URL,
                        LinkBuilder.generate().getTotalApproveListUrl()
                    )
                    .navigation()
            }
        } else {
            binding.llTotalApproval.visibility = View.GONE
        }


        findViewById<View>(R.id.ll_launch).setOnClickListener {
            //点击我发起的
//            ARouter.getInstance().build(RouteApr.APR_LAUNCH_PAGE).navigation()
            ARouter.getInstance().build(RouteApr.APR_FUNC_WEB_PAGE)
                .withString(ConsKeys.COMPANY_ID, companyId)
                .withString(ConsKeys.PARAMS_URL, LinkBuilder.generate().getStartApproveListUrl())
                .navigation()
        }
        findViewById<View>(R.id.ll_handle).setOnClickListener {
            //点击我审批的
//            ARouter.getInstance().build(RouteApr.APR_HANDLE_PAGE).navigation()
            ARouter.getInstance().build(RouteApr.APR_FUNC_WEB_PAGE)
                .withString(ConsKeys.COMPANY_ID, companyId)
                .withString(
                    ConsKeys.PARAMS_URL,
                    LinkBuilder.generate().getHandleApproveListUrl()
                )
                .navigation()
        }
        findViewById<View>(R.id.ll_copy).setOnClickListener {
//            ARouter.getInstance().build(RouteApr.APR_COPY_PAGE).navigation()
            ARouter.getInstance().build(RouteApr.APR_FUNC_WEB_PAGE)
                .withString(ConsKeys.COMPANY_ID, companyId)
                .withString(ConsKeys.PARAMS_URL, LinkBuilder.generate().getCopyApproveListUrl())
                .navigation()
        }
//        自定义json解析器设置 解析map内容时，避免int变成double
//        if (!MyJsonAdapter.listeners.contains(ModelAdapter.FACTORY)) {
//            MyJsonAdapter.listeners.add(ModelAdapter.FACTORY)
//        }
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityAprFuncBinding {
        return ActivityAprFuncBinding.inflate(layoutInflater)
    }

    private var currentClickItem: AprItem? = null
    private fun onItemClick(it: AprItem) {//点击加班或者其他tcp,这里是审批模板页
        currentClickItem = it
        viewModel?.checkFixProcessPersonLeave(bindToLifecycle(), it.modelId, companyId)
    }

    //团队已解散
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun orgDissolved(event: EventBusEvent<String>) {
        if (event.code == EventBusAction.Event_DISSOLVED_ORG ||
            event.code == EventBusAction.Event_LEAVE_ORG
        ) {
            if (BaseApplication.getCurrentActivity() == AprFuncActivity::class.java.name) {
                if (StringUtils.isNotBlankAndEmpty(event.data) &&
                    StringUtils.isNotBlankAndEmpty(companyId) && event.data == companyId
                ) {
                    val dialog = if (event.code == EventBusAction.Event_DISSOLVED_ORG) {
                        MyDialog(
                            mContext!!, 0, 0,
                            "该团队已解散", needBtnConfirm = true, needBtnCancel = false, bgResourceId = 0
                        )
                    } else {
                        MyDialog(
                            mContext!!, 0, 0,
                            "你已被请离该团队", needBtnConfirm = true, needBtnCancel = false, bgResourceId = 0
                        )
                    }
                    dialog.setBtnLeftText("好的")
                    dialog.setCanceledOnTouchOutside(false)
                    dialog.setCancelable(false)
                    dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
                        override fun clickLeftBtn() {
                            finish()
                        }

                    })
                    dialog.show()
                }
            }
        }
    }

    override fun onNoDoubleClick(v: View) {
        if (v.id == R.id.layout_empty_layout) {
            loadDetail()
        } else {
            super.onNoDoubleClick(v)
        }
    }

    class MyListAdapter(
        val context: Context, private val layoutId: Int,
        private val data: List<AprGroup>, val onItemClick: (child: AprItem) -> Unit,
        private val onToggle: (position: Int, opened: Boolean) -> Unit
    ) :
        RecyclerView.Adapter<MyListHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyListHolder {
            return MyListHolder(
                context, LayoutInflater.from(context).inflate(
                    layoutId,
                    parent, false
                ), onItemClick, onToggle
            )
        }

        override fun getItemCount(): Int {
            return data.size
        }

        override fun onBindViewHolder(holder: MyListHolder, position: Int) {
            holder.bind(position, data[position])
        }
    }

    class MyListHolder(
        val context: Context, itemView: View,
        val onItemClick: (child: AprItem) -> Unit,
        private val onToggle: (position: Int, opened: Boolean) -> Unit
    ) :
        RecyclerView.ViewHolder(itemView) {
        var isToggle: Boolean = true
        fun bind(position: Int, big: AprGroup) {
            val grid: RecyclerView = itemView.findViewById(R.id.gv_apr_grid_list)
            val groupName: TextView = itemView.findViewById(R.id.tv_group_name)
            val toggle: TextView = itemView.findViewById(R.id.tv_group_toggle)
            groupName.text = big.title
            grid.layoutManager = GridLayoutManager(context, 4)
            val aprItemList = arrayListOf<AprItem>()
            if (!big.items.isNullOrEmpty()) {
                aprItemList.addAll(big.items)
            }
            val adapter = MyGridAdapter(
                context, R.layout.item_apr_fun_grid_layout,
                aprItemList, onItemClick
            )
            grid.adapter = adapter

            val line: View = itemView.findViewById(R.id.line_apr_func_group_name)

            toggle.setOnClickListener {
                if (isToggle) {
                    toggle.text = "展开"
                    grid.visibility = View.GONE
                    line.visibility = View.GONE
                    onToggle.invoke(position, true)
                } else {
                    toggle.text = "收起"
                    grid.visibility = View.VISIBLE
                    line.visibility = View.VISIBLE
                    onToggle.invoke(position, false)
                }
                isToggle = !isToggle
            }
        }
    }

    class MyGridAdapter(
        val context: Context, private val layoutId: Int,
        private val dataList: List<AprItem>,
        val onItemClick: (child: AprItem) -> Unit
    ) :
        RecyclerView.Adapter<MyGridHolder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyGridHolder {
            return MyGridHolder(
                LayoutInflater.from(context).inflate(
                    layoutId, parent,
                    false
                )
            )
        }

        override fun getItemCount(): Int {
            return dataList.size
        }

        override fun onBindViewHolder(holder: MyGridHolder, position: Int) {
            holder.itemView.tag = dataList[position]
            holder.itemView.setOnClickListener {
                val child: AprItem = it.tag as AprItem
                onItemClick.invoke(child)
            }
            holder.bind(dataList[position])
        }
    }

    class MyGridHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        fun bind(child: AprItem) {
            itemView.findViewById<TextView>(R.id.tv_apr_launch_port).text = child.modelName
            XUtil.loadImage(
                itemView.context, itemView.findViewById(R.id.iv_apr_launch_port),
                XUtil.getAprIcon(child.modelLogo)
            )
        }
    }

    override fun initLogic() {
        super.initLogic()
        viewModel?.checkProcessMemberLeaveResult?.observe(this, Observer { result ->
            RequestHelper.onResponse(result, onSuccess = { isAvailable ->
                if (currentClickItem != null) {
                    if (isAvailable) {
                        val bundle = Bundle()
                        bundle.putString(ConsKeys.MODEL_ID, currentClickItem!!.modelId)
                        bundle.putString(ConsKeys.COMPANY_ID, companyId)
                        jump(RouteApr.APR_LAUNCH_CREATE, bundle)
                    } else {
                        //审批模板不可用
                        ToastUtil.showCustomToast(
                            null, mContext!!, true,
                            "该审批流程中有人员不可用，暂不可\n使用此审批，" +
                                    "请联系管理员修改审批流程"
                        )
                    }
                }
            }, onError = { code, msg ->
                when (code) {
                    2 -> {
                        ToastUtil.showCustomToast(
                            null, mContext!!, false,
                            "此审批有必填的文件类属性，请登录担当办公网页版使用"
                        )
                        //测试===========开始=============
                        //测试审批附件
                       /* val bundle = Bundle()
                        bundle.putString(ConsKeys.MODEL_ID, currentClickItem!!.modelId)
                        bundle.putString(ConsKeys.COMPANY_ID, companyId)
                        jump(RouteApr.APR_LAUNCH_CREATE, bundle)*/
                        //测试===========结束=============
                    }
                    3 -> {
                        if (currentClickItem != null) {
                            val bundle = Bundle()
                            bundle.putString(ConsKeys.MODEL_ID, currentClickItem!!.modelId)
                            bundle.putString(ConsKeys.COMPANY_ID, companyId)
                            jump(RouteApr.APR_LAUNCH_CREATE, bundle)
                        }
                    }
                    else -> toastShort(msg)
                }
            }, onDefault = { msg ->
                toastShort(msg)
            })
        })

//        viewModel?.errorResult!!.observe(this, Observer {
//            hideLoading()
//            toastShort(it.message)
//        })
        viewModel?.aprListResult?.observe(this, Observer { result ->
            hideLoading()
            showLog("页面接收 审批项数据")

            list.clear()
            RequestHelper.onResponse(result, onSuccess = {
                showLog("处理 审批项数据")

                /**处理审批未处理 未读数量*/
                fun dealWaitCount() {
                    var count = 0
                    if (it.approveCount > 0) {
                        tvLaunchCount.text = "${it.approveCount}"
                        if (tvLaunchCount.visibility == View.GONE) {
                            tvLaunchCount.visibility = View.VISIBLE
                        }
                        count += it.approveCount
                    } else {
                        if (tvLaunchCount.visibility == View.VISIBLE) {
                            tvLaunchCount.visibility = View.GONE
                        }
                    }
                    if (it.assigneeCount > 0) {
                        tvHandleCount.text = "${it.assigneeCount}"
                        if (tvHandleCount.visibility == View.GONE) {
                            tvHandleCount.visibility = View.VISIBLE
                        }
                        count += it.assigneeCount
                    } else {
                        if (tvHandleCount.visibility == View.VISIBLE) {
                            tvHandleCount.visibility = View.GONE
                        }
                    }
                    if (it.crbonCopyCount > 0) {
                        tvCopyCount.text = it.crbonCopyCount.toString()
                        tvCopyCount.visibility = View.VISIBLE
                        if (tvCopyCount.visibility == View.GONE) {
                            tvCopyCount.visibility = View.VISIBLE
                        }
                    } else {
                        if (tvCopyCount.visibility == View.VISIBLE) {
                            tvCopyCount.visibility = View.GONE
                        }
                    }
                    count += it.crbonCopyCount
                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.refresh_all_appro_undo, count))
                }

                dealWaitCount()

                if (!it.approveModel.isNullOrEmpty()) {
                    it.approveModel.forEach { group ->
                        if (!group.items.isNullOrEmpty()) {
                            list.add(group)
                        }
                    }
                }

                if (list.isNotEmpty()) {
                    noDataViewPage.visibility = View.GONE
                    rvList.visibility = View.VISIBLE
                } else {
                    noDataViewPage.visibility = View.VISIBLE
                    rvList.visibility = View.GONE
                }
                adapter.notifyDataSetChanged()
            }, onError = { _, msg ->
                showLog("获取失败 审批项数据")
                noDataViewPage.visibility = View.VISIBLE
                rvList.visibility = View.GONE
                toastShort(msg)
            }, onDefault = { msg ->
                toastShort(msg)
            })
        })
        loadDetail()
    }

    private fun showCopyRedPoint() {

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun refreshPoint(msgEvent: EventBusEvent<Map<String, Any>>) {
        when (msgEvent.code) {
            "apr_notice_receive_event" -> {
                showLog("审批功能列表::接收到审批通知，触发审批相关页面刷新，刷新审批功能列表")
                val map = msgEvent.data
                if (map != null && map.containsKey("companyId") && map["companyId"] == companyId) {
                    showLog("接收到审批通知消息，刷新审批功能页面，更新头部三个红点状态")
                    loadDetail()
                }
            }
            MsgType.APR_PROCESS_OVER.name,
            MsgType.APR_ADD_NODE_SUCCESS.name,
            MsgType.APR_DETAIL_READED.name -> {
                showLog("审批功能列表::接收到加审或已读事件，刷新审批功能页面，更新头部三个红点状态")
                loadDetail()
            }
        }
    }

    private fun loadDetail() {
        if (!companyId.isNullOrBlank()) {
            showLoading()
            if (isFuture && isApproval) {
                viewModel?.getAprListV2(companyId)
            } else {
                viewModel?.getAprList(companyId)
            }
        }
    }
}