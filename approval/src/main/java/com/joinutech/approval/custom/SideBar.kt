package com.joinutech.approval.custom

import android.content.Context
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.animation.AnimationSet
import android.view.animation.TranslateAnimation
import android.widget.LinearLayout
import android.widget.TextView
import com.joinutech.approval.R

/**
 * @className: SideBar
 * @desc: 侧边栏
 * @author: zyy
 * @date: 2019/9/2 11:32
 * @company: joinUTech
 * @leader: ke
 */
class SidebarItemView(context: Context, attributeSet: AttributeSet) : LinearLayout(context, attributeSet) {
    lateinit var mContext: Context
    private lateinit var vLayout: LinearLayout
    private lateinit var vTitle: TextView
    private var mScreenWidth: Int = 0

    init {
        init(context)
    }

    private fun init(context: Context) {
        mContext = context
        LayoutInflater.from(mContext).inflate(R.layout.item_side_bar_list, this)
        vLayout = findViewById(R.id.wg_sidebar_item_root)
        vTitle = findViewById(R.id.wg_sidebar_item_title)
        mScreenWidth = getScreenWidth()
        val lp = LinearLayout.LayoutParams(mScreenWidth / 4, LayoutParams.WRAP_CONTENT)
        vLayout.layoutParams = lp
    }

    /**
     * 获取屏幕的宽度（单位：px）
     *
     * @return 屏幕宽px
     */
    fun getScreenWidth(): Int {
        val windowManager: WindowManager = mContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        // 创建了一张白纸
        val dm: DisplayMetrics = DisplayMetrics()
        // 给白纸设置宽高
        windowManager.defaultDisplay.getMetrics(dm)
        return dm.widthPixels
    }

    /**
     * 设置值
     *
     * @param text
     */
    fun setText(text: String) {
        vTitle.text = text
    }

    /**
     * 设置动画
     */
    fun setItemAnimation() {
        setAnimation((-(mScreenWidth / 12)).toFloat(), (-(mScreenWidth / 12)).toFloat())
    }

    /**
     * 设置动画
     */
    fun setItemAnimation2() {
        setAnimation((-(mScreenWidth / 22)).toFloat(), (-(mScreenWidth / 22)).toFloat())
    }

    /**
     * 设置动画
     * @param fromXDelta
     * @param toXDelta
     */
    private fun setAnimation(fromXDelta: Float, toXDelta: Float) {
        val animationSet = AnimationSet(true)
        val animation = TranslateAnimation(fromXDelta, toXDelta, 0f, 0f)
        //设置动画持续时间
        animation.duration = 50
        //动画结束后是否回到原位
        animation.fillAfter = false
        animationSet.addAnimation(animation)
        vTitle.startAnimation(animation)
        animation.startNow()
    }
}

class SidebarView(context: Context, val attributeSet: AttributeSet) : LinearLayout(context, attributeSet), View.OnTouchListener {
    lateinit var mContext: Context
    private lateinit var vLayout: LinearLayout

    init {
        init(context)
    }

    private fun init(context: Context) {
        mContext = context
        LayoutInflater.from(mContext).inflate(R.layout.layout_side_bar_view, this)
        vLayout = findViewById(R.id.side_bar_container)

        vLayout.setOnTouchListener(this)
        val listData = ArrayList<String>()
        listData.add("A")
        listData.add("B")
        listData.add("C")
        listData.add("D")
        listData.add("E")
        listData.add("F")
        listData.add("G")
        listData.add("H")
        listData.add("I")
        listData.add("J")
        listData.add("K")
        listData.add("L")
        listData.add("M")
        listData.add("N")
        listData.add("O")
        listData.add("P")
        listData.add("Q")
        listData.add("R")
        listData.add("S")
        listData.add("T")
        listData.add("U")
        listData.add("V")
        listData.add("W")
        listData.add("X")
        listData.add("Y")
        listData.add("Z")
        setSideData(listData)
    }

    /**
     * 设置侧栏数据
     *
     * @param listData
     */
    fun setSideData(listData: List<String>) {
        vLayout.removeAllViews()
        for (str in listData) {
            val item = SidebarItemView(mContext, attributeSet)
            item.setTag(str)
            item.setText(str)
            vLayout.addView(item)
        }
    }

    private var isDown = false

    override fun onTouch(view: View, motionEvent: MotionEvent): Boolean {
        if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
            isDown = true
        } else if (motionEvent.getAction() == MotionEvent.ACTION_UP || motionEvent.getAction() == MotionEvent.ACTION_CANCEL) {
//            vTitle.setVisibility(View.GONE)
            isDown = false
        }
        transformValue(view as LinearLayout, motionEvent)
        return true
    }

    /**
     * 转换获取值
     *
     * @param layout
     * @param motionEvent
     */
    private fun transformValue(layout: LinearLayout, motionEvent: MotionEvent) {
        val textView = layout.getChildAt(0) as SidebarItemView
        val fistViewTop = textView.getTop()
        val itemHeight = textView.getMeasuredHeight()
        val currentY = motionEvent.getY()
        //不超过顶部和顶部
        if (motionEvent.getY() > 0 && motionEvent.getY() < layout.getMeasuredHeight()) {
            //不超过子控件的范围值
            if (currentY > fistViewTop && currentY < (fistViewTop + itemHeight * layout.getChildCount())) {
                val rang = currentY - fistViewTop
                val index = (rang / itemHeight).toInt()
                Log.e("kawa", "rang:" + rang + "_index:" + index + "_itemHeight:" + itemHeight)
                val tag = layout.getChildAt(index).getTag() as String
//                vTitle.setText(tag)
                if (listener != null) {
                    listener!!.onSelectItem(index, tag)
                }
                if (isDown) {
//                    vTitle.setVisibility(View.VISIBLE)
                    setAnimation(index, layout)
                }
            }
        }
    }

    /**
     * 设置动画
     *
     * @param index
     * @param layout
     */
    private fun setAnimation(index: Int, layout: LinearLayout) {
        if (index > 1 && index < layout.getChildCount() - 2) {
            val tv1 = layout.getChildAt(index - 1) as SidebarItemView
            val tv2 = layout.getChildAt(index) as SidebarItemView
            val tv3 = layout.getChildAt(index + 1) as SidebarItemView
            tv1.setItemAnimation2()
            tv2.setItemAnimation()
            tv3.setItemAnimation2()
        } else {
            //第一个的
            if (index == 0) {
                val tv1 = layout.getChildAt(index) as SidebarItemView
                val tv2 = layout.getChildAt(index + 1) as SidebarItemView
                tv1.setItemAnimation()
                tv2.setItemAnimation2()
            }
            //最后一个的
            else {
                val tv1 = layout.getChildAt(index) as SidebarItemView
                val tv2 = layout.getChildAt(index - 1) as SidebarItemView
                tv1.setItemAnimation()
                tv2.setItemAnimation2()
            }
        }
    }

    var listener: OnSidebarViewListener? = null

}


interface OnSidebarViewListener {
    fun onSelectItem(index: Int, value: String)
}