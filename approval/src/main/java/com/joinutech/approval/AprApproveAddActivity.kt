package com.joinutech.approval

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.approval.data.ApprovalDetailData
import com.joinutech.approval.data.AprAddNodeData3
import com.joinutech.approval.databinding.ActivityAprApproveAddBinding
import com.joinutech.approval.utils.RouteApr
import com.joinutech.common.adapter.MyDragAdapter
import com.joinutech.common.adapter.MyHolder
import com.joinutech.common.adapter.OnListDragCheckListener
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.CircleImageView

/**
 * @PackageName: com.joinutech.approval
 * @ClassName: AprApproveAddActivity
 * @Desc: 新加审功能页面，加审详情选择人员类型后进入此页面
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/3/16 9:11
 */
//选择加审人员
@Route(path = RouteApr.PAGE_APR_ADD_PERSON)
class AprApproveAddActivity : AprBaseActivity<ActivityAprApproveAddBinding>(), OnListDragCheckListener {

    override val contentViewResId: Int = R.layout.activity_apr_approve_add
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityAprApproveAddBinding {
        return ActivityAprApproveAddBinding.inflate(layoutInflater)
    }

    /**
     * 0 团队内成员
     * 1 外部协作人
     */
    private var addPersonType = 0
    private var companyId = ""

    /**审批详情*/
    private var aprDetailData: ApprovalDetailData? = null

    lateinit var adapter: MyDragAdapter<SearchMemberBean>
    lateinit var data: ArrayList<SearchMemberBean>

    //    private var itemTouchHelper: ItemTouchHelper? = null
    private val holderData = SearchMemberBean(name = "添加加审人员")

    override fun initView() {
        setPageTitle("进行加审")
//        setRightTitleColor(CommonUtils.getColor(mContext!!, R.color.color1E87F0),
        setRightTitleColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color1E87F0),
                "完成", this)
        updateState(aprAddPosition > 0)

        intent.extras?.let {
            addPersonType = it.getInt("personType", addPersonType)
            companyId = it.getString("companyId", companyId)
            val detailInfo = it.getString("detailInfo", "")
            if (StringUtils.isNotBlankAndEmpty(detailInfo)) {
                aprDetailData = GsonUtil.fromJson(detailInfo, ApprovalDetailData::class.java)
            }
        }

        
        binding.tvAddBefore.setOnClickListener(this)
        binding.tvAddAfter.setOnClickListener(this)
        
        binding.tvAprAddHelp.setOnClickListener(this)

        data = arrayListOf(holderData)
        adapter = MyDragAdapter(this,
                R.layout.item_apr_add_member_layout,
                R.id.iv_drag,
                data,
                onBindItem = { holder: MyHolder<SearchMemberBean>, _: Int, member: SearchMemberBean, itemView: View ->
                    itemView.findViewById<TextView>(R.id.tv_name).text = member.name
                    val avatar = itemView.findViewById<CircleImageView>(R.id.iv_avatar)
                    val del = itemView.findViewById<View>(R.id.iv_del)
                    val drag = itemView.findViewById<View>(R.id.iv_drag)
                    if (StringUtils.isNotBlankAndEmpty(member.userId)) {
                        ImageLoaderUtils.loadImage(this, avatar, member.headimg)
                        del.visibility = View.VISIBLE
                        drag.visibility = View.VISIBLE
                        del.setOnClickListener {
                            onDel(member)
                        }
                        if (data.size > 2) {
//                            drag.setOnTouchListener { _, event ->
//                                if (event.action == MotionEvent.ACTION_DOWN) {
//                                    onStartDrag(holder)
//                                }
//                                true
//                            }
                        } else {
                            drag.setOnTouchListener(null)
                        }
                    } else {
                        avatar.setImageResource(R.drawable.iv_add_apr)
                        del.visibility = View.GONE
                        drag.visibility = View.GONE
                        drag.setOnTouchListener(null)
                    }
                },
                onItemClick = { position: Int, _: SearchMemberBean, _: View ->
                    if (position == data.lastIndex) {
                        selectPerson()
                    }
                },
                listener = this
        )
//        itemTouchHelper = ItemTouchHelper(ItemCallBack(this))
//        itemTouchHelper!!.attachToRecyclerView(binding.rvList)
        adapter.attachDragEvent(binding.rvList)

        binding.rvList.layoutManager = LinearLayoutManager(this)
        binding.rvList.adapter = adapter
    }

    private fun onDel(member: SearchMemberBean) {
        val position = data.indexOf(member)
        if (position >= 0 && position < data.size) {
            data.remove(member)
            adapter.notifyItemRemoved(position)
        }
    }

    /**
     * 加审位置
     * 在之前 -1
     * 在之后 1
     * */
    private var aprAddPosition = -1

    override fun onNoDoubleClick(v: View) {
        super.onNoDoubleClick(v)
        when (v.id) {
            com.joinutech.ddbeslibrary.R.id.tv_toolbar_right -> {//点击完成
                if (aprAddPosition != 0) {
                    if(data.size>1){
                        val temp = data
                        temp.remove(holderData)
                        dealAddApr(temp)
                    }else{
                        toastShort("请选择加审人员")
                    }
                } else {
                    toastShort("请选择加审方式")
                }
            }
            R.id.tv_add_before -> {
                aprAddPosition = -1
                updateState(aprAddPosition > 0)
            }
            R.id.tv_add_after -> {
                aprAddPosition = 1
                updateState(aprAddPosition > 0)
            }
            R.id.tv_apr_add_help -> {
                startActivity(Intent(this, AprHelpActivity::class.java))
            }
            R.id.v_add_member -> {

            }
        }
    }

    private fun updateState(after: Boolean) {
        if (after) {
            
            binding.ivAddBefore.visibility = View.GONE
             binding.ivAddAfter.visibility = View.VISIBLE
        } else {
            binding.ivAddBefore.visibility = View.VISIBLE
            binding.ivAddAfter.visibility = View.GONE
        }
    }

    private fun getSelectedPerson(): ArrayList<String> {
        val selected = arrayListOf<String>()
        if (data.isNotEmpty() && data.size > 1) {
            selected.addAll(data.filter { StringUtils.isNotBlankAndEmpty(it.userId) }.map { it.userId }.distinct().toList())
        }
        if (!selected.contains(userId)) {
            selected.add(userId!!)
        }
        return selected
//        return arrayListOf(userId!!)
    }

    private fun selectPerson() {
        val bundle = Bundle()
        bundle.putInt("personType", addPersonType)
        bundle.putString("title", "选择加审人员")
        bundle.putString("companyId", companyId)
        if (aprDetailData != null) {
            val lessCount = 50 - aprDetailData!!.approveProcess.assignee.size
            if (lessCount > 10) {
                bundle.putInt("maxSelect", 10)
            } else {
                bundle.putInt("maxSelect", lessCount)
            }
        } else {
            bundle.putInt("maxSelect", 10)
        }
        bundle.putStringArrayList("unSelectPerson", getSelectedPerson())
        jump(RouteApr.APR_SELECT_PERSON, bundle, 3001)//选择加审人员，过滤当前人，多选，最多十人
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK && requestCode == 3001) {
            val resultData = data?.getStringExtra("members")
            try {
                if (StringUtils.isNotBlankAndEmpty(resultData)) {
                    val list = GsonUtil.fromJson(resultData, Array<SearchMemberBean>::class.java)?.toMutableList()
                    if (list != null && list.isNotEmpty()) {
                        updateList(list)
                    } else {
                        showLog("返回数据数组为空")
                    }
                } else {
                    showLog("未返回数据")
                }
            } catch (e: Exception) {
            }
        } else {
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

    private fun updateList(list: MutableList<SearchMemberBean>) {
//        data.clear()
        data.remove(holderData)
        data.addAll(list)
        data.add(holderData)
        adapter.notifyDataSetChanged()
    }

    override fun isSwapable(fromPosition: Int, targetPosition: Int): Boolean {
        return targetPosition != data.lastIndex
    }

    override fun initLogic() {
        super.initLogic()
        initNodeData()
        /**加审回调*/
        viewModel?.addAprNodeResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result,
                    onSuccess = {
                        toastShort("加审成功")
                        showLog("加审成功，发送加审成功事件")
                        val intent = intent
                        intent.putExtra("resultType", if (aprAddPosition > 0) 1 else 2)
                        setResult(Activity.RESULT_OK, intent)
                        finish()
                    },
                    onError = { _, msg ->
                        toastShort(msg)
                    },
                    onDefault = { msg ->
                        toastShort(msg)
                    })
        })
    }

    /**选择人员后返回信息*/
    private fun dealAddApr(list: ArrayList<SearchMemberBean>) {
        // TODO: 2020/3/18 8:53 加审事件回调

        if (StringUtils.isNotBlankAndEmpty(companyId) && list.isNotEmpty()) {
            Loggerr.i("加审","==执行加审=")
            val userIds = list.filter { StringUtils.isNotBlankAndEmpty(it.userId) }.map { it.userId }.toList()
            /*加审流程*/
            getAprId3(userIds.size) { approveIds ->
                preAddApr(userIds, "0", approveIds, addPersonType, if (aprAddPosition > 0) 1 else 0)
            }
        }else{
            Loggerr.i("加审","==等于没有执行=")
        }
    }

//    /**获取用户团队信息，处理审批和添加审批人都需要自己查询接口，获得deptId*/
//    private fun loadUserOrgInfo(userId: String, result: (userInfo: UserOrgInfo) -> Unit) {
//        showLog("获取用户团队信息")
//        viewModel?.userOrgInfoError!!.observe(this, Observer {
//            hideLoading()
//            toastShort(it.message)
//
//        })
//        viewModel?.userOrgInfoResult?.observe(this, Observer {
//            hideLoading()
//            result.invoke(it)
//        })
//        viewModel?.getUserOrgInfo(companyId!!, userId)
//        showLoading()
//    }

    /**获取审批id*/
    private fun getAprId3(count: Int, callback: (assignId: List<String>) -> Unit) {
        showLog("获取添加审批人的审批id")
        /**审批id回调*/
        viewModel?.getAprIdResult3?.observe(this, Observer { result ->
            RequestHelper.onResponse(result, onSuccess = {
                callback.invoke(it)
            }, onError = { code, msg ->
                toastShort(msg)
            }, onDefault = { msg ->
                toastShort(msg)
            })
            viewModel?.getAprIdResult!!.removeObservers(this)
        })
        viewModel?.getAprId3(count)
    }

    private val unProcessNodes = arrayListOf<String>()

    private fun initNodeData() {
        aprDetailData?.let {
            val nodeList = arrayListOf<SearchMemberBean>()
            nodeList.add(it.approveProcess.approveCreate)
            if (it.approveProcess.assignee.isNotEmpty()) {
                for (assignee in it.approveProcess.assignee) {
                    if (assignee.approveType == 1) {
                        if (assignee.assigneeUser.isNotEmpty()) {
                            val member = assignee.assigneeUser[0]
                            member.approveType = 1
                            nodeList.add(member)
                            if (member.opinion in 0..1) {
//                                processedNodes.add(member.userId)
                            } else {
                                unProcessNodes.add(member.userId)
                            }
                            if (member.opinion == 0) {
                                break
                            } else if (member.opinion == 3) {
                                // 找到第一个审批中节点退出循环
                                break
                            }
                        }
                    } else {
                        // TODO: 2019/9/16 审批分组 或审 会审节点处理
//                        nodeList.add(SearchMemberBean(approveType = assignee.approveType, assignee = assignee.assigneeUser))
                    }
                }
            }
        }
    }

    private fun preAddApr(addUserIds: List<String>, addUserDeptId: String, addUserAssignIds: List<String>, isOuter: Int, location: Int) {
        showLog("按照添加位置，加入新增审批人员的审批id，得到最终审批人数组")
        var oldAssignId = ""
        val nodeResult = arrayListOf<String>()
        aprDetailData?.let {
            for (index in it.approveProcess.assignee.indices) {
                val assignee = it.approveProcess.assignee[index]
                if (assignee.approveType == 1) {
                    val member = assignee.assigneeUser[0]
                    if (member.userId == userId!! && member.opinion == 3
                            && member.userId in unProcessNodes
                            && unProcessNodes.indexOf(member.userId) == 0) {
                        oldAssignId = member.approveAssigneeId
                        if (location > 0) {
                            nodeResult.add(oldAssignId)
                            nodeResult.addAll(addUserAssignIds)
                        } else {
                            nodeResult.addAll(addUserAssignIds)
                            nodeResult.add(oldAssignId)
                        }
                    } else {
                        nodeResult.add(member.approveAssigneeId)
                    }
                } else {
                    //TODO 会审和或审处理

                }
            }
            if (oldAssignId.isNotEmpty()) {
                tryAddAprPerson(oldAssignId, addUserIds, addUserAssignIds, addUserDeptId, nodeResult, isOuter, location)
            }
        }

    }

    /**加审网络请求*/
    private fun tryAddAprPerson(
            currentUserAssignId: String,
            addUserIds: List<String>,
            addUserAssignIds: List<String>,
            addUserDeptId: String,
            nodeResult: ArrayList<String>,
            outer: Int,
            location: Int
    ) {
        showLog("执行添加审批人")
        if (StringUtils.isNotBlankAndEmpty(companyId) && aprDetailData != null) {
            val data = AprAddNodeData3(
                    approveId = aprDetailData!!.approveInfo.approveId,
                    approveAssigneeId = addUserAssignIds,
                    approveOldAssigneeId = currentUserAssignId,
                    assigneeId = addUserIds,
                    assignees = nodeResult,
                    deptId = addUserDeptId,
                    organizationId = companyId,
                    isOuter = outer,
                    location = location
            )
            showLoading()
            viewModel?.addAprNode3(data)
        }
    }
}
