package com.joinutech.approval

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.approval.viewModel.CooperationCompanyViewModel
import com.joinutech.common.adapter.MyAdapter
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.wavesidebar.PinnedHeaderDecoration
import com.joinutech.ddbeslibrary.widget.wavesidebar.WaveSideBarView

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/4 10:12
 * @packageName: com.joinutech.approval
 * @Company: JoinuTech
 */
class SelectApprovalPersonFragment : AprBaseFragment() {

    override val layoutRes: Int
        get() = R.layout.fragment_select_approval_person

    override fun openEventBus() = false

    private lateinit var dataLayout: ConstraintLayout
    private lateinit var selectAllLinearLayout: LinearLayout
    private lateinit var rvList: RecyclerView
    private lateinit var mainSideBar: WaveSideBarView
    private lateinit var topTitle: TextView
    private lateinit var noResultLayout: PageEmptyView
    private var personListType = 0
    private var companyId = ""

    /**显示数据*/
    private lateinit var memberList: MutableList<SearchMemberBean>
    private lateinit var mAdapter: MyAdapter<SearchMemberBean>

    /**
     * 选择人员类型
     * 默认选择后返回
     * 其他时需要判断
     */
    private var selectType: Int = 0
    private var unSelectPerson: ArrayList<String>? = null
    private var singleSelect: Boolean = true
    private val selectMembers = mutableMapOf<String, SearchMemberBean>()
    private var oldSelectIndex = 0
    private lateinit var ivSelectList: ImageView
    private lateinit var cooperationViewModel: CooperationCompanyViewModel

    companion object {
        private const val INDEX = "index"
        private const val COMPANY_ID = "company_id"
        fun newInstance(position: Int, companyId: String, selectType: Int, singleSelect: Boolean,
                        unSelectPerson: ArrayList<String>?)
                : SelectApprovalPersonFragment {
            return SelectApprovalPersonFragment().apply {
                arguments = Bundle().apply {
                    putInt(INDEX, position)
                    putString(COMPANY_ID, companyId)
                    putInt("selectType", selectType)
                    putBoolean("singleSelect", singleSelect)
                    putStringArrayList("unSelectPerson", unSelectPerson)
                }
            }
        }
    }

    override fun initView(rootView: View) {
        personListType = arguments?.getInt(INDEX)!!
        companyId = arguments?.getString(COMPANY_ID)!!
        selectType = arguments?.getInt("selectType", 0)!!
        singleSelect = arguments?.getBoolean("singleSelect", true)!!
        unSelectPerson = arguments?.getStringArrayList("unSelectPerson")
        dataLayout = rootView.findViewById(R.id.cl_data_layout)
        selectAllLinearLayout = rootView.findViewById(R.id.ll_select_all)
        rvList = rootView.findViewById(R.id.rv_list)
        mainSideBar = rootView.findViewById(R.id.main_side_bar)

        noResultLayout = rootView.findViewById(R.id.layout_empty_layout)
        noResultLayout.hide()

        ivSelectList = rootView.findViewById(R.id.iv_select_list)
        topTitle = rootView.findViewById(R.id.topTitle)
        val decoration = PinnedHeaderDecoration()
        decoration.registerTypePinnedHeader(1) { _, _ -> true }
        rvList.addItemDecoration(decoration)
        if (personListType == 1) {
            topTitle.visibility = View.VISIBLE
        } else {
            topTitle.visibility = View.GONE
        }
        cooperationViewModel = getModel(CooperationCompanyViewModel::class.java)
    }

    override fun initLogic() {
        super.initLogic()

        if (!singleSelect) {
            XUtil.showView(selectAllLinearLayout)
            selectAllLinearLayout.setOnClickListener(this)
        } else {
            XUtil.hideView(selectAllLinearLayout)
        }
        initViewShow()
        getObserve()
        getDataList()
    }

    private fun getObserve() {
        cooperationViewModel.searchDepMemberResult.observe(this, Observer {
            hideLoading()
            if (!it.isNullOrEmpty()) {
                noResultLayout.hide()
                dataLayout.visibility = View.VISIBLE
                /**处理公司成员数据，转换为好友数据后显示到页面*/
                memberList.clear()
                memberList.addAll(it)
                mAdapter.notifyDataSetChanged()
            } else {
                dataLayout.visibility = View.GONE
                noResultLayout.show()
            }
        })
        cooperationViewModel.searchDepMemberError.observe(this, Observer {
            hideLoading()
            ToastUtil.show(mActivity, it)
        })
        cooperationViewModel.searchDepExternalMemberResult.observe(this, Observer {
            hideLoading()
            if (!it.isNullOrEmpty()) {
                noResultLayout.hide()
                dataLayout.visibility = View.VISIBLE
                /**处理公司成员数据，转换为好友数据后显示到页面*/
                memberList.clear()
                it.forEach { item ->
                    run {
                        val bean = SearchMemberBean(headimg = item.avatar,
                                name = item.name, userId = item.userId, isOuter = 1,
                                positionName = when (item.type.toString()) {
                                    "1" -> "客户"
                                    "2" -> "渠道商"
                                    "3" -> "供应商"
                                    "4" -> "合作伙伴"
                                    "5" -> "其他类型"
                                    else -> "客户"
                                }, initial = item.initial)
                        memberList.add(bean)
                    }
                }
                mAdapter.notifyDataSetChanged()
            } else {
                dataLayout.visibility = View.GONE
                noResultLayout.show()
            }
        })
        cooperationViewModel.searchDepExternalMemberError.observe(this, Observer {
            hideLoading()
            ToastUtil.show(mActivity, it)
        })
    }

    fun getSelectPersonList(): ArrayList<SearchMemberBean> {
        val result = arrayListOf<SearchMemberBean>()
        if (!selectMembers.isNullOrEmpty()) {
            for (item in selectMembers.entries) {
                result.add(item.value)
            }
        }
        return result
    }

    fun getDataList() {
        showLoading()
        if (personListType == 0) {
            //团队成员部分
            cooperationViewModel.searchDepMember(bindToLifecycle(), companyId)
        } else {
            cooperationViewModel.searchDepExternalMember(bindToLifecycle(), companyId)
        }
    }

    private fun initViewShow() {
        memberList = ArrayList()
        mAdapter = MyAdapter(mActivity, R.layout.item_person_dept_layout, memberList,
                onBindItem = { position: Int, data: SearchMemberBean, view: View ->
                    val tvIndex = view.findViewById<TextView>(R.id.tv_index)
                    val ivSelect = view.findViewById<View>(R.id.iv_select)
                    val ivAvatar = view.findViewById<ImageView>(R.id.iv_avatar)
                    val tvName = view.findViewById<TextView>(R.id.tv_name)
                    val tvInfo = view.findViewById<TextView>(R.id.tv_info)
                    val line = view.findViewById<View>(R.id.line)
                    if (singleSelect) {
                        ivSelect.visibility = View.GONE
                    } else {
                        ivSelect.visibility = View.VISIBLE
                        ivSelect.isSelected = data.select
                    }
                    if (StringUtils.isNotBlankAndEmpty(data.headimg))
                        ImageLoaderUtils.loadImage(mActivity, ivAvatar, data.headimg)
                    tvName.text = data.name
                    tvInfo.text = data.positionName

                    line.visibility = View.GONE

                    if (position == 0 || memberList[position - 1].initial != data.initial) {
                        tvIndex.visibility = View.VISIBLE
                        tvIndex.text = data.initial
                        if (data.isCurrentIndex) {
//                            tvIndex.setTextColor(CommonUtils.getColor(mActivity, R.color.color1E87F0))
                            tvIndex.setTextColor(CommonUtils.getColor(mActivity, com.joinutech.ddbeslibrary.R.color.color1E87F0))
                        } else {
                            tvIndex.setTextColor(CommonUtils.getColor(mActivity, com.joinutech.ddbeslibrary.R.color.colorBDBDBD))
                        }
                    } else {
                        tvIndex.visibility = View.GONE
                    }
                },
                onItemClick = { _: Int, member: SearchMemberBean, view: View ->
                    if (checkUser(member.userId)) {
                        if (!singleSelect) {
                            val ivSelect = view.findViewById<View>(R.id.iv_select)
                            member.select = !member.select
                            ivSelect.isSelected = member.select
                        }
                        onClick(member)
                    } else {
                        mActivity.toastShort("该用户不可选择")
                    }
                })
        // RecyclerView设置相关
        rvList.layoutManager = LinearLayoutManager(mActivity)
        rvList.adapter = mAdapter
        // 侧边设置相关
        mainSideBar.setOnSelectIndexItemListener { letter ->
            // TODO: 2020/7/23 8:59 优化后需要验证
            if (memberList.isNotEmpty()) {
                val first = memberList.find { it.initial == letter }
                if (first != null) {
                    first.isCurrentIndex = true
                    memberList[oldSelectIndex].isCurrentIndex = false
                    oldSelectIndex = memberList.indexOf(first)
                    mAdapter.notifyDataSetChanged()
                    showLog("切换联系人索引后 更新 联系人列表----")
                    (rvList.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(oldSelectIndex, 0)
                }
            }
//            if (memberList.isNotEmpty()) {
//                for (i in 0 until memberList.size) {
//                    memberList[i].isCurrentIndex = false
//                    if (memberList[i].initial == letter) {
//                        oldSelectIndex = i
//                        memberList[i].isCurrentIndex = true
//                        mAdapter.notifyDataSetChanged()
//                        (rvList.layoutManager as LinearLayoutManager)
//                                .scrollToPosition(oldSelectIndex)
//                    }
//                }
//            }
        }

//        rvList.addOnScrollListener(object : RecyclerView.OnScrollListener() {
//            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
//                super.onScrollStateChanged(recyclerView, newState)
//                val layoutManager = recyclerView.layoutManager
//                //判断是当前layoutManager是否为LinearLayoutManager
//                // 只有LinearLayoutManager才有查找第一个和最后一个可见view位置的方法
//                if (layoutManager is LinearLayoutManager) {
//                    //获取第一个可见view的位置
//                    val firstItemPosition = layoutManager.findFirstVisibleItemPosition()
//                    memberList[oldSelectIndex].isCurrentIndex = false
//                    memberList[firstItemPosition].isCurrentIndex = true
//
//                    oldSelectIndex = firstItemPosition
//                    mAdapter.notifyDataSetChanged()
//                }
//            }
//        })

    }

    private fun checkUser(userId: String): Boolean {
        if (unSelectPerson != null && unSelectPerson!!.isNotEmpty()) {
            if (userId in unSelectPerson!!) {
                return false
            }
        }
        return true
    }

    private fun onClick(member: SearchMemberBean) {
        if (selectType != 0) {
            showTip(member)
        } else {
            if (singleSelect) {
                val intent = Intent()
                intent.putExtra("members", GsonUtil.toJson(arrayListOf(member)))
                mActivity.setResult(Activity.RESULT_OK, intent)
                mActivity.finish()
            } else {
                onSelect(member)
            }
        }
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.ll_select_all -> {
                selectAll(!ivSelectList.isSelected)
            }
        }
    }

    private fun selectAll(select: Boolean) {
        ivSelectList.isSelected = select
        if (memberList.isNotEmpty()) {
            for (member in memberList) {
                if (checkUser(member.userId)) {
                    member.select = select
                    if (select) {
                        selectMembers[member.userId] = member
                    }
                }
            }
        }
        if (!select) {
            selectMembers.clear()
        }
        mAdapter.notifyDataSetChanged()
    }

    private fun onSelect(member: SearchMemberBean) {
        if (member.select) {
            selectMembers[member.userId] = member
        } else {
            if (selectMembers.containsKey(member.userId)) {
                selectMembers.remove(member.userId)
            }
        }
        ivSelectList.isSelected = selectMembers.size == memberList.size
    }

    private fun showTip(member: SearchMemberBean) {
        val content = "你确认要邀请用户 ${member.name} 进入审批节点进行审批操作吗？"
        val startIndex = content.indexOf(member.name)
        val endIndex = startIndex + member.name.length
        val spannableString = SpannableString(content)
        val colorSpan = ForegroundColorSpan(Color.parseColor("#1E87F0"))
        member.isOuter = personListType
        spannableString.setSpan(colorSpan, startIndex, endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        val helper = object : CenterDialogHelper(
                mActivity,
                onConfirm = {
                    //                    EventBusUtils.sendEvent(EventBusEvent(
//                            MsgType.APR_ADD_PERSON.name, data = arrayListOf(member)))
                    mActivity.finish()
                }, onCancel = {

        }
        ) {
            override fun bindView(dialogView: View) {
                super.bindView(dialogView)
                dialogView.findViewById<TextView>(R.id.tv_content).text = spannableString
                dialogView.findViewById<View>(R.id.tv_hint).visibility = View.GONE
            }
        }

        helper.initView()
        helper.onConfig(DialogConfig(useDefault = true))
        helper.show()
    }

    override fun onDestroy() {
        super.onDestroy()
        hideLoading()
    }
}