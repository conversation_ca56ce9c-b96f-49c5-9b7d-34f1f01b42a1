package com.joinutech.approval

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.approval.data.AprCommentData
import com.joinutech.approval.databinding.ActivityAprCommentBinding
import com.joinutech.approval.utils.RouteApr
import com.joinutech.common.adapter.MyAdapter
import com.joinutech.common.base.isDebug
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.bean.CapacityBean
import com.joinutech.ddbeslibrary.bean.CreateTaskCommentUploadFileBean
import com.joinutech.ddbeslibrary.bean.PanFileBean
import com.joinutech.ddbeslibrary.bean.UploadFileBean
import com.joinutech.ddbeslibrary.request.DdbesApiUtil
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.request.RxScheduleUtil
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.UploadFileService
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.widget.activity.TaskImagePreviewActivity
import com.marktoo.lib.cachedweb.LogUtil
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import io.reactivex.FlowableOnSubscribe
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import java.io.File

/**
 * @className: AprCommentActivity
 * @desc: 审批评论页面
 * @author: zyy
 * @date: 2019/8/2 9:12
 * @company: joinUTech
 * @leader: ke
 */
@Route(path = RouteApr.APR_COMMENT_PAGE)
class AprCommentActivity(override val contentViewResId: Int = R.layout.activity_apr_comment) : AprBaseActivity<
        ActivityAprCommentBinding>() {

    private lateinit var mActivity: AppCompatActivity
    private var approveId: String = ""

    private lateinit var commentInput: EditText
    private lateinit var ivAddPic: View
    private lateinit var rvList: RecyclerView

    /**图片限制数量*/
    private val limit = 9

    /**图片选择器*/
//    private val selector = PicSelector(this, limit)

    // 处理文件上传完成判断时，需要根据 isUploadFlag 判断
    private var selectPicList: ArrayList<UploadFileBean> = arrayListOf()
    private val placeHolderData = UploadFileBean(isUploadFlag = true)

    /**文件上传进度*/
    private val progressHash = HashMap<String, Int>()

    private lateinit var adapter: MyAdapter<UploadFileBean>
    private var companyId = ""

    override fun initView() {
        setPageTitle("发表评论")
        whiteStatusBarBlackFont()
        showToolBarLine()
        // 隐藏返回键
        hideBackButton()
        // 显示返回名称
        setLeftTitle("取消", View.OnClickListener {
            finish()
        })
        setRightTitle("发表", View.OnClickListener {
            commitComment()
        })
        mActivity = this
        if (intent.extras != null) {
            approveId = intent.getStringExtra(ConsKeys.TARGET_ID) ?: ""
            if (approveId.isNullOrBlank()) {
                if (isDebug) {
                    toastShort("缺少审批id")
                } else {
                    toastShort("评论失败")
                }
                finish()
            }
            if (StringUtils.isNotBlankAndEmpty(intent.extras!!.getString(ConsKeys.COMPANY_ID))) {
                companyId = intent.extras!!.getString(ConsKeys.COMPANY_ID)!!
            }
        }

        commentInput = findViewById(R.id.et_comment_input)
        ivAddPic = findViewById(R.id.iv_add_pic)
        rvList = findViewById(R.id.rv_list)

        ivAddPic.visibility = View.VISIBLE
        rvList.visibility = View.GONE

        selectPicList.add(placeHolderData)

        ivAddPic.setOnClickListener {
            dealPicSelect(selectPicList.size - 1)
        }

        adapter = MyAdapter(this, R.layout.item_pic_list, selectPicList,
                onBindItem = { position: Int, path: UploadFileBean, itemView: View ->

                    val del = itemView.findViewById<ImageView>(R.id.iv_del)
                    val picture = itemView.findViewById<ImageView>(R.id.iv_pic)
                    val ivProgress = itemView.findViewById<ImageView>(R.id.iv_progress)
                    val tvProgress = itemView.findViewById<TextView>(R.id.tv_progress)
                    ivProgress.visibility = View.GONE
                    tvProgress.visibility = View.GONE
                    if (position == selectPicList.size - 1 && selectPicList[position] == placeHolderData) {
                        del.visibility = View.GONE
                        picture.setImageResource(R.drawable.icon_add_pic)
                        picture.setOnClickListener {
                            dealPicSelect(selectPicList.size - 1)
                        }
                    } else {
                        if (progressHash.containsKey(path.fileUrl) && progressHash[path.fileUrl] == 100) {
                            ivProgress.visibility = View.GONE
                            tvProgress.visibility = View.GONE
                            del.visibility = View.VISIBLE
                        } else {
                            del.visibility = View.GONE
                            ivProgress.visibility = View.VISIBLE
                            tvProgress.visibility = View.VISIBLE
                            val progress = if (progressHash.isNotEmpty() && progressHash.containsKey(path.fileUrl)) {
                                progressHash[path.fileUrl]!!
                            } else {
                                0
                            }
                            tvProgress.text = "$progress%"
                        }
                        XUtil.loadImage(itemView.context, picture, path.fileUrl)
                        del.setOnClickListener {
                            onDel(position)
                        }
                        picture.setOnClickListener {
                            val intent = Intent(mContext, TaskImagePreviewActivity::class.java)
                            val previewPicList: ArrayList<String> = selectPicList.map { it.fileUrl }
                                    .toList() as ArrayList<String>
                            val previewDataBean = TaskImagePreviewActivity.PreviewDataBean(
                                    position, previewPicList, true)
                            val bundle = Bundle()
                            bundle.putSerializable("previewData", previewDataBean)
                            intent.putExtras(bundle)
                            mContext!!.startActivity(intent)
                        }
                    }
                },
                onItemClick = { _: Int, _: UploadFileBean, _: View ->

                })
        rvList.layoutManager = GridLayoutManager(this, 5)
        rvList.adapter = adapter
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityAprCommentBinding {
        return ActivityAprCommentBinding.inflate(layoutInflater)
    }

    override fun initLogic() {
        super.initLogic()
        //增加评论字数判断
        commentInput.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (s!!.length > 200) {
                    ToastUtil.showCustomToast(null, mContext!!,
                            true, "最多输入200字")
                    s.delete(200, s.length)
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            }

        })
//        viewModel?.aprCommentError!!.observe(this, Observer {
//            hideLoading()
//            toastShort(it.message)
//        })
        viewModel?.aprCommentResult!!.observe(this, Observer {
            hideLoading()
            RequestHelper.onResponse(it,
                    onSuccess = {
                        toastShort("发表成功")
//                EventBusUtils.sendEvent(EventBusEvent(MsgType.COMMENT_SUCCESS.name, null))
                        setResult(Activity.RESULT_OK)
                        BaseApplication.mMainThreadHandler.postDelayed({ finish() }, 1000)
                    },
                    onError = { _, msg ->
                        toastShort(msg)
                    },
                    onDefault = { msg ->
                        toastShort(msg)
                    })

        })
    }

    /**创建审批评论*/
    private fun createAprComment(content: String, pics: ArrayList<CreateTaskCommentUploadFileBean>) {
        val data = AprCommentData(
                approveId = approveId,
                content = content,
                organizationId = companyId,
                pics = pics
        )
        showLoading()
        viewModel?.createAprComment(data)
    }

    private fun dealPicSelect(selectedCount: Int) {
//        val view = View.inflate(mContext, R.layout.dialog_personicon_bottom_layout, null)
        val view = View.inflate(mContext, com.joinutech.ddbeslibrary.R.layout.dialog_personicon_bottom_layout, null)
        val dialog = BottomDialogUtil.showBottomDialog(mContext!!, view, Gravity.BOTTOM)
        val mTakePicture = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.takePicture)
        val mSelectPicture = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.selectPicture)
        val mDismiss = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.dismiss)
        mTakePicture.setOnClickListener {
            PictureNewHelper.beforeSelectPhoto(this, maxSelectNum = limit - selectedCount)
//            selector.onSelect(ConsKeys.TAKE_PHOTO, selectedCount)
            dialog.dismiss()
        }
        mSelectPicture.setOnClickListener {
            PictureNewHelper.beforeSelectPhoto(this, maxSelectNum = limit - selectedCount)
//            selector.onSelect("", selectedCount)
            dialog.dismiss()
        }
        mDismiss.setOnClickListener {
            dialog.dismiss()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == IMAGE_CUT_CODE) {
                if (data != null) {
                    val selectList = PictureNewHelper.afterSelectPhotoInfo(data)
                    if (selectList.isNotEmpty()) {
                        val hadCount = if (selectPicList.last() == placeHolderData) {
                            selectPicList.size - 1
                        } else {
                            selectPicList.size
                        }
                        if ((hadCount + selectList.size) > limit) {
                            toastShort("超过图片上限 $limit 张")
                            return
                        }

                        if (!selectPicList.isNullOrEmpty() && selectPicList.last() == placeHolderData) {
                            selectPicList.removeAt(selectPicList.lastIndex)
                        }

                        for (media in selectList) {
                            selectPicList.add(
                                    UploadFileBean(fileId = "", hash = "",
                                            fileName = media["fileName"] as String,
                                            fileUrl = media["compressPath"] as String)
                            )
                        }
                        if (selectPicList.size < limit) {
                            selectPicList.add(placeHolderData)
                        }
                        updateData2()
                    }
                }
            }
        } else {
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

    private fun updateData2() {
        updateView()// 选择图片后调
        uploadFile2()// 上传图片
    }

    private fun onDel(position: Int) {
        selectPicList.removeAt(position)
        if (selectPicList.size < limit && selectPicList.last() != placeHolderData) {
            selectPicList.add(placeHolderData)
        }
        updateView()
    }

    private fun updateView() {
        adapter.notifyDataSetChanged()
        if (selectPicList.size > 1) {
            ivAddPic.visibility = View.GONE
            rvList.visibility = View.VISIBLE
        } else {
            ivAddPic.visibility = View.VISIBLE
            rvList.visibility = View.GONE
        }
    }

    /**开始上传文件
     * 过滤上传中文件，初始化文件上传信息
     * 检查云存储控件
     * 云存储控件完成*/
    private fun uploadFile2() {
        val temp = mutableListOf<UploadFileBean>()
        if (!selectPicList.isNullOrEmpty()) {
            temp.addAll(selectPicList)
            if (temp.last() == placeHolderData) {
                temp.removeAt(temp.size - 1)
            }
        }
        if (temp.isEmpty()) {
            return
        }
        val currentUploadFiles = arrayListOf<UploadFileBean>()
        for (file in temp) {
            /**已完成或者正在上传的文件不再加入上传队列*/
            /*上传已完成和上传进行中都不包含时，才加入上传队列*/
            if (progressHash.isNotEmpty() && progressHash.containsKey(file.fileUrl)) {
                continue
            } else {
                // 添加到上传记录中，进度为0
                currentUploadFiles.add(file)
                progressHash[file.fileUrl] = 0
            }
        }
        adapter.notifyDataSetChanged()
        if (currentUploadFiles.isNotEmpty()) {
            checkCapacity2(currentUploadFiles)
        }
    }

    /**
     * 检查文件上传空间是否满足
     * @param currentUploadFiles 需要上传的文件信息
     */
    private fun checkCapacity2(currentUploadFiles: ArrayList<UploadFileBean>) {
        val tag = "检查存储空间"
        RxScheduleUtil.rxSchedulerHelper(
                DdbesApiUtil.getTaskService().searchCompanyCapacity(UserHolder.getAccessToken(), companyId)
        ).compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<CapacityBean>() {
                    override fun onError(ex: ApiException) {
                        RequestHelper.showLogLine(msg = ">>>$tag 返回错误<<<")
                        try {
                            ToastUtil.show(BaseApplication.joinuTechContext, "上传图片失败")
                        } catch (e: Exception) {
                        }
                    }

                    override fun onComplete() {
                        RequestHelper.showLogLine(msg = ">>>$tag 获取结束<<<")
                    }

                    override fun onNext(result: CapacityBean?) {
                        if (result != null) {
                            RequestHelper.showLogLine(msg = ">>>$tag 返回数据<<<")
                            onCapacityResponse2(currentUploadFiles, result)
                        } else {
                            RequestHelper.showLogLine(msg = ">>>$tag 未返回数据<<<")
                        }
                    }
                })
    }

    /**判断选择的控件是否满足上传文件，确认是否执行上传*/
    private fun onCapacityResponse2(currentUploadFiles: java.util.ArrayList<UploadFileBean>, capacity: CapacityBean) {
        LogUtil.showLog("查看容量结果回调 ----", "file_up__")
        val canUsed = capacity.capacity - capacity.used
        var computerFileTotalSize = 0L
        currentUploadFiles.forEach { bean ->
            computerFileTotalSize += File(bean.fileUrl).length()
        }
        //判断是否容量可用
        if (computerFileTotalSize <= canUsed) {
            (this@AprCommentActivity as? MyUseBaseActivity)?.let {
                haveCapacity2(this, currentUploadFiles)
            }
        } else {
            val dialog = BaseCenterDialogHelper(
                    activity = this,
                    onBindView = { view ->
                        run {
                            val cancel = view.findViewById<TextView>(R.id.cancel)
                            val lineV = view.findViewById<View>(R.id.line_v)
                            val tvContent = view.findViewById<TextView>(R.id.tv_content)
                            tvContent.text = "团队云盘存储空间已满，无法上传更多图片/附件。" +
                                    "请登录pan.ddbes.com了解更多存储详情"
                            val tvTitle = view.findViewById<TextView>(R.id.tv_title)
                            val tvHint = view.findViewById<TextView>(R.id.tv_hint)
                            tvTitle.visibility = View.GONE
                            tvHint.visibility = View.GONE
                            cancel.visibility = View.GONE
                            lineV.visibility = View.GONE
                        }
                    },
                    onConfirm = {}, onCancel = {})
            dialog.initView()
            dialog.show(true)
        }
    }

    /**控件充足时，请求文件存储fileId信息，之后开始执行上传*/
    private fun haveCapacity2(activity: MyUseBaseActivity, currentUploadFiles: java.util.ArrayList<UploadFileBean>) {
        LogUtil.showLog("容量够用准备上传 获取md5 获取文件id 上传文件 ----", "file_up__")
        val perms = arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE)
        val tips = "选取图片需要你授权读写"
        PermissionUtils.requestPermissionActivity(activity, perms, tips,
                onSuccess = {
                    //先获取文件的md5，再通过后台获取上传文件的id值，再去上传文件
                    Flowable.create(FlowableOnSubscribe<HashMap<String, String>> { emitter ->
                        try {
                            // 去重需要上传的文件，hashMap中key为要上传文件的路径，value为文件对应的hash
                            val hashMap = hashMapOf<String, String>()
                            currentUploadFiles.forEach { bean ->
                                if (hashMap.isEmpty() || !hashMap.containsKey(bean.fileUrl)) {
                                    val hashValue = StringUtils.getFileMD5(File(bean.fileUrl))
                                    if (StringUtils.isNotBlankAndEmpty(hashValue)) {
                                        hashMap[bean.fileUrl] = hashValue
                                    }
                                }
                            }
                            // 上传文件转换为map记录即将上传的数据，文件路径:对应文件的hash
                            if (hashMap.isNotEmpty()) {
                                emitter.onNext(hashMap)
                            } else {
                                emitter.onNext(hashMapOf())
                            }
                        } catch (e: Exception) {
                            emitter.onNext(hashMapOf())
                        }
                    }, BackpressureStrategy.BUFFER)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe { hashValues ->
                                LogUtil.showLog("获取hash完成，开始请求文件id ${GsonUtil.toJson(hashValues)}", "file_up__")
                                if (!hashValues.isNullOrEmpty()) {
                                    // 取出需要上传文件的 hash 值集合，请求fileId信息，判断文件在云盘是否存在
                                    val hashList = hashValues.values.toList()
                                    val map = hashMapOf<String, Any>()
                                    map["hash"] = hashList
                                    UploadFileService.getPanTencentId(UserHolder.getAccessToken(), map)
                                            .compose(activity.bindToLifecycle())
                                            .compose(ErrorTransformer.getInstance())
                                            .subscribe(object : BaseSubscriber<List<PanFileBean>>() {
                                                override fun onError(ex: ApiException) {
                                                    activity.dismissDialog()
                                                    ToastUtil.show(BaseApplication.joinuTechContext, ex.message)
                                                }

                                                override fun onComplete() {

                                                }

                                                override fun onNext(cloudFileInfoList: List<PanFileBean>?) {
                                                    LogUtil.showLog("获取文件id 结果回调 ${GsonUtil.toJson(cloudFileInfoList)} ----", "file_up__")
                                                    if (!cloudFileInfoList.isNullOrEmpty()) {
//                                                        // 更新需要上传文件的云端信息
                                                        // 更新已选择文件的云端信息
                                                        val temp = hashMapOf<String, UploadFileBean>()
                                                        selectPicList.forEach { file ->
                                                            val hash = hashValues[file.fileUrl]
                                                            if (!hash.isNullOrBlank()) {
                                                                val data = cloudFileInfoList[hashList.indexOf(hash)]
                                                                file.fileId = data.key
                                                                file.isUploadFlag = data.exist
                                                                file.hash = hash
                                                                // 要上传的文件在云端已经存在时，设置上传记录中进度为100，已上传文件中设置文件路径对应云端id即fileId
                                                                if (data.exist) {
                                                                    progressHash[file.fileUrl] = 100
                                                                } else {
                                                                    // 云端不存在的文件加入需要上传文件的集合中
                                                                    temp[file.fileUrl] = file
                                                                }
                                                            }
                                                        }
                                                        // 过滤出需要上传文件信息，云端不存在且hash不为空
//                                                        val needUploadFile = currentUploadFiles.filter { !it.isUploadFlag && !it.hash.isNullOrBlank() }.toMutableList()
                                                        //有需要上传的文件上传，没有需要的话刷新列表
                                                        if (!temp.isNullOrEmpty())
                                                            dealUploadEvent(activity, temp.values.toMutableList() as ArrayList<UploadFileBean>)
                                                        else {
                                                            adapter.notifyDataSetChanged()
                                                        }
                                                    }
                                                }
                                            })
                                }
                            }
                },
                onError = {
                    ToastUtil.show(BaseApplication.joinuTechContext, tips)
                })
    }

    /**真实处理文件上传*/
    private fun dealUploadEvent(activity: MyUseBaseActivity, files: ArrayList<UploadFileBean>) {
        FileUploadUtil.uploadMultiFileWithProgress(
                hashList = files,
                onProgress = { filePath, _, _, percent ->
                    activity.runOnUiThread {
                        // 更新进度时是按文件路径更新，所有列表中根据元素文件路径获取进度即可，多个相同文件时也不需要多个上传任务
                        progressHash[filePath] = percent
                        adapter.notifyDataSetChanged()
                    }
                },
                onSuccess = { uploadResult ->
                    if (!uploadResult.isNullOrEmpty()) {
                        // 文件上传结果转型为map，key为本地文件路径
                        val map = uploadResult.map { file -> file.filePath to file }.toMap()
                        if (!map.isNullOrEmpty()) {
                            // 上传文件结果返回
                            files.forEach { file ->
                                if (map.containsKey(file.fileUrl)) {
                                    // 更新全局上传记录
                                    // 此处为上一步操作中请求的文件存储fileId，来自过滤后仅需要上传的文件中数据files
                                    // 更新上传进度记录
                                    progressHash[file.fileUrl] = 100
                                    file.isUploadFlag = true
                                }
                            }

                            // 上传文件结果更新到选中文件记录缓存中
                            selectPicList.forEach { bean ->
                                if (map.containsKey(bean.fileUrl)) {
                                    // 标记文件上传完成
                                    bean.isUploadFlag = true
                                }
                            }
                        }
                        if (files.size == uploadResult.size) {
                            LogUtil.showLog("全部图片上传成功")
                        } else {
                            LogUtil.showLog("有图片未上传成功")
                        }
                        adapter.notifyDataSetChanged()
                    } else {
                        LogUtil.showLog("无图片上传成功")
                    }
                },
                onError = {
                    /*图片上传失败处理*/
                    LogUtil.showLog("图片上传失败")
                    LogUtil.showLog(it)
                },
                tosFileType = TosFileType.PAN
        )
    }

    private fun commitComment() {
        if (selectPicList.isNullOrEmpty()) {
            createAprComment(content = commentInput.text?.toString() ?: "", pics = arrayListOf())
        } else {
            val hasUnUploadFile = selectPicList.find { !it.isUploadFlag }
            if (hasUnUploadFile != null) {
                toastShort("还有图片未上传完成，请稍等！")
                return
            }

            val temp = arrayListOf<UploadFileBean>()
            temp.addAll(selectPicList)
            if (temp.contains(placeHolderData)) {
                temp.remove(placeHolderData)
            }
            val upFileList =
                    temp.filter { it.isUploadFlag }.map {
                        CreateTaskCommentUploadFileBean(it.fileId, it.hash, it.fileName)
                    }.toMutableList()
            // 创建审批评论
            createAprComment(
                    content = commentInput.text?.toString() ?: "",
                    pics = if (upFileList.isNullOrEmpty()) {
                        arrayListOf()
                    } else {
                        upFileList as ArrayList
                    })
        }

    }

}