package com.joinutech.approval

import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.approval.utils.RouteApr
import com.joinutech.ddbeslibrary.widget.wheel.builder.TimePickerBuilder
import com.joinutech.ddbeslibrary.widget.wheel.listener.OnTimeSelectListener
import com.joinutech.ddbeslibrary.widget.wheel.view.TimePickerView
import com.joinutech.common.adapter.MyAdapter
import com.joinutech.ddbeslibrary.utils.XUtil
import com.joinutech.ddbeslibrary.utils.*
import java.util.*

/**
 * @PackageName: com.joinutech.approval
 * @ClassName: MainActivity
 * @Desc: 审批模块运行时测试页
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/3/18 13:18
*/
@Deprecated("module type used")
class MainActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        ARouter.getInstance().inject(this)
        val pages = mutableListOf<PageBean>()
        pages.add(PageBean("审批功能列表", RouteApr.APR_FUNC_PAGE))
        pages.add(PageBean("审批功能设置", RouteApr.APR_FUNC_SET_PAGE))
        pages.add(PageBean("审批模板管理", RouteApr.APR_MODEL_MANAGE))
        pages.add(PageBean("审批模板设置", RouteApr.APR_MODEL_SET_PAGE))
        pages.add(PageBean("快速审批", RouteApr.APR_QUICK_PAGE))
//        pages.add(PageBean("我发起的", APR_LAUNCH_PAGE))
//        pages.add(PageBean("待我审批", APR_HANDLE_PAGE))
//        pages.add(PageBean("抄送我的", APR_COPY_PAGE))
        pages.add(PageBean("审批详情", RouteApr.APR_DETAIL_PAGE))
        pages.add(PageBean("发起审批", RouteApr.APR_DETAIL_PAGE))
        pages.add(PageBean("审批评论", RouteApr.APR_COMMENT_PAGE))

//        val popAdapter = MyAdapter(this, pages)
        rv_pages = findViewById(R.id.rv_pages)
        rv_pages.layoutManager = LinearLayoutManager(this)
        rv_pages.adapter = MyAdapter(this, R.layout.item_page_list, pages, onBindItem = { position: Int, pageBean: PageBean, itemView: View ->
            itemView.findViewById<TextView>(R.id.page_name).text = pageBean.name
        }, onItemClick = { position: Int, pageBean: PageBean, view: View ->
            if (position == pages.lastIndex) {
                showCustomPicker { toastShort("当前选择时间为：${XUtil.getTime(it)}") }
            } else {
                ARouter.getInstance().build(pageBean.path).navigation()
            }
        })
        initCustomTimePicker()
    }

    private lateinit var rv_pages: RecyclerView
    private var pvCustomTime: TimePickerView? = null
    private var title_text = arrayOfNulls<TextView>(2)
    private var title_indicator = arrayOfNulls<View>(2)

    private var selectDate: Date? = null
    private fun initCustomTimePicker() {
        if (pvCustomTime == null) {

            /**
             * @description
             *
             * 注意事项：
             * 1.自定义布局中，id为 optionspicker 或者 timepicker 的布局以及其子控件必须要有，否则会报空指针.
             * 具体可参考demo 里面的两个自定义layout布局。
             * 2.因为系统Calendar的月份是从0-11的,所以如果是调用Calendar的set方法来设置时间,月份的范围也要是从0-11
             * setRangDate方法控制起始终止时间(如果不设置范围，则使用默认时间1900-2100年，此段代码可注释)
             */
            //系统当前时间
            val selectedDate = Calendar.getInstance()
            val startDate = Calendar.getInstance()
            startDate.set(2017, 1, 23)
            val endDate = Calendar.getInstance()
            endDate.set(2047, 2, 28)
            //时间选择器 ，自定义布局
            pvCustomTime = TimePickerBuilder(this, OnTimeSelectListener { date, v ->
                //选中事件回调
                //关闭弹窗时回调结果到页面
                Log.e("time selctor", "关闭弹窗时回调结果到页面")
                selectDate = date
            })
                    .setTimeSelectChangeListener { date ->
                        //时间选择后回调
                        Log.e("time selctor", "时间选择后回调")
                        title_text[0]?.text = XUtil.getMyDate(date)
                        title_text[1]?.text = XUtil.getMyTime(date)
                    }
                    .setDate(selectedDate)
                    .setRangDate(startDate, endDate)
//                    .setLayoutRes(R.layout.pickerview_custom_time) { v ->
                    .setLayoutRes(com.joinutech.ddbeslibrary.R.layout.pickerview_custom_time) { v ->
                        val tvSubmit = v.findViewById<View>(com.joinutech.ddbeslibrary.R.id.tv_finish)
                        val ivCancel = v.findViewById<View>(R.id.tv_cancel)
                        title_text[0] = v.findViewById(com.joinutech.ddbeslibrary.R.id.tv_date_title)
                        title_text[1] = v.findViewById(com.joinutech.ddbeslibrary.R.id.tv_time_title)
                        title_indicator[0] = v.findViewById(com.joinutech.ddbeslibrary.R.id.indicator_date)
                        title_indicator[1] = v.findViewById(com.joinutech.ddbeslibrary.R.id.indicator_time)

                        title_text[0]?.text = XUtil.getMyDate(Date())
                        title_text[1]?.text = XUtil.getMyTime(Date())
                        //                        title_text[0]?.setSelected(true);
                        title_text[0]?.setTextColor(Color.parseColor("#1E87F0"))
                        title_indicator[1]?.visibility = View.GONE

                        title_text[0]?.setOnClickListener {
                            //                                title_text[0]?.setSelected(true);
                            //                                title_text[1]?.setSelected(false);
                            title_text[0]?.setTextColor(Color.parseColor("#1E87F0"))
                            title_text[1]?.setTextColor(Color.parseColor("#323232"))

                            title_indicator[0]?.visibility = View.VISIBLE
                            title_indicator[1]?.visibility = View.GONE
                            pvCustomTime?.setUpdate(1)
                        }
                        title_text[1]?.setOnClickListener {
                            //                                title_text[1]?.setSelected(true);
                            //                                title_text[0]?.setSelected(false);
                            title_text[1]?.setTextColor(Color.parseColor("#1E87F0"))
                            title_text[0]?.setTextColor(Color.parseColor("#323232"))
                            title_indicator[1]?.visibility = View.VISIBLE
                            title_indicator[0]?.visibility = View.GONE
                            pvCustomTime?.setUpdate(2)
                        }
                        tvSubmit.setOnClickListener {
                            pvCustomTime?.returnData()
                            pvCustomTime?.dismiss()
                        }
                        ivCancel.setOnClickListener { pvCustomTime?.dismiss() }
                    }
                    .setContentTextSize(17)
//                切换控制显示年月日时分秒
                    .setType(booleanArrayOf(true, true, true, false, false, false))
//                .setLabel("年", "月", "日", "时", "分", "秒")
                    .setLabel("年", "月", "日", ":", "", "")
                    .setLineSpacingMultiplier(1.2f)
                    .setTextXOffset(30, 0, -30, 30, -30, 0)
//是否只显示中间选中项的label文字，false则每项item全部都带有label。
                    .isCenterLabel(true)
//                    .setDividerColor(Color.parseColor("#FFDBDBDB"))
//                    .setTextColorOut(Color.parseColor("#FFA5A5A5"))
//                    .setTextColorCenter(Color.parseColor("#ff333333"))
//                    .setTextColorCenter2(Color.parseColor("#ff0082f2"))
                    .build()
        }
    }

    private fun showCustomPicker(result: (date: Date) -> Unit) {
        title_text[0]?.setTextColor(Color.parseColor("#1E87F0"))
        title_text[1]?.setTextColor(Color.parseColor("#323232"))

        title_indicator[0]?.visibility = View.VISIBLE
        title_indicator[1]?.visibility = View.GONE
        pvCustomTime?.setUpdate(1)
        pvCustomTime?.selectListener = OnTimeSelectListener { date, v -> result.invoke(date) }
//弹出自定义时间选择器
        pvCustomTime?.show()
    }
}

class PageBean(val name: String, val path: String)