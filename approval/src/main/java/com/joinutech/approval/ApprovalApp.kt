package com.joinutech.approval

import android.app.Application
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.common.storage.FileUtil

/**
 * @className: ApprovalApp
 * @desc: TODO
 * @author: zyy
 * @date: 2019/7/31 10:59
 * @company: joinUTech
 * @leader: ke
 */
class ApprovalApp : Application(){

    override fun onCreate() {
        super.onCreate()
        ARouter.openLog()     // 打印日志
        ARouter.openDebug()   // 开启调试模式(如果在InstantRun模式下运行，必须开启调试模式！
        // 线上版本需要关闭,否则有安全风险)
        //ARouter 初始化
        ARouter.init(this) // 尽可能早，推荐在Application中初始化
//        CustomActivityOnCrash.install(this)
        FileUtil.init()
    }
}