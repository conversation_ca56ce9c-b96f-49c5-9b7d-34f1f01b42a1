package com.joinutech.approval

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.viewpager.widget.ViewPager
import com.joinutech.approval.databinding.ActivitySelectApprovalPersonBinding
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.approval.utils.RouteApr
import com.joinutech.ddbeslibrary.utils.SEARCH_APPROVAL_PERSON_LIST

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/4 13:12
 * @packageName: com.joinutech.approval
 * @Company: JoinuTech
 */
@Deprecated("no used class")
//@Route(path = APR_WITH_EXTERNAL_SELECT_PERSON)
class SelectApprovalPersonActivity : AprBaseActivity<ActivitySelectApprovalPersonBinding>() {

    override val contentViewResId: Int
        get() = R.layout.activity_select_approval_person

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivitySelectApprovalPersonBinding {
        return ActivitySelectApprovalPersonBinding.inflate(layoutInflater)
    }

    /**
     * 选择人员类型
     * 默认选择后返回
     * 其他时需要判断
     */
    private var selectType: Int = 0
    private var unSelectPerson: ArrayList<String>? = null
    private var singleSelect: Boolean = true
    private var companyId: String = ""
    private lateinit var adapter: MyPagerAdapter
    private var currentPosition = 0

    override fun initImmersion() {
        setPageTitle("选择审批人员")
        setLeftTitle("取消")
        val bundle = intent.extras
        if (bundle != null) {
            companyId = bundle.getString("companyId", "")
            selectType = bundle.getInt("selectType", 0)
            singleSelect = bundle.getBoolean("singleSelect", true)
            unSelectPerson = bundle.getStringArrayList("unSelectPerson")
            if (!singleSelect) {
                setRightTitle("确定", this)
            }
        }
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        adapter = object : MyPagerAdapter(supportFragmentManager) {

            override fun getItem(position: Int): Fragment {
                if (fragments[position] == null) {
                    fragments[position] = SelectApprovalPersonFragment.newInstance(position,
                            companyId, selectType, singleSelect, unSelectPerson)
                }
                return fragments[position]!!
            }
        }
        binding.contentViewPager.adapter = adapter
        binding.contentViewPager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrollStateChanged(state: Int) {

            }

            override fun onPageScrolled(position: Int, positionOffset: Float,
                                        positionOffsetPixels: Int) {
            }

            override fun onPageSelected(position: Int) {
                currentPosition = position
                setTabIndex(currentPosition)
                adapter.fragments[position]!!.getDataList()
            }
        })
    }

    override fun initLogic() {
        super.initLogic()

        setTabIndex(0)
        binding.orgMemberLayout.setOnClickListener(this)
        binding.externalLayout.setOnClickListener(this)
        binding.searchTv.setOnClickListener(this)
    }

    private fun setTabIndex(position: Int) {
        binding.orgMemberLine.visibility = View.GONE
        binding.externalMemberLine.visibility = View.GONE
        binding.orgMemberTv.isSelected = false
        binding.externalMemberTv.isSelected = false
        when (position) {
            0 -> {
                binding.orgMemberLine.visibility = View.VISIBLE
                binding.orgMemberTv.isSelected = true
            }
            1 -> {
                binding.externalMemberLine.visibility = View.VISIBLE
                binding.externalMemberTv.isSelected = true
            }
        }
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            tv_rightTitle -> {
                saveSelectPerson()
            }
            binding.orgMemberLayout -> {
                setTabIndex(0)
                binding.contentViewPager.currentItem = 0
            }
            binding.externalLayout -> {
                setTabIndex(1)
                binding.contentViewPager.currentItem = 1
            }
            binding.searchTv -> {
                val bundle = Bundle()
                bundle.putString("companyId", companyId)
                bundle.putInt("personType", currentPosition)
                bundle.putInt("maxSelect", if (singleSelect) 1 else -1)
                bundle.putStringArrayList("unSelectPerson", unSelectPerson)
                jump(RouteApr.APR_SELECT_PERSON, bundle, SEARCH_APPROVAL_PERSON_LIST)//跳转人员列表，实现搜索功能
            }
        }
    }

    private fun saveSelectPerson() {
        val selectPersonList =
                adapter.fragments[currentPosition]!!.getSelectPersonList()
        val intent = Intent()
        intent.putExtra("members", GsonUtil.toJson(selectPersonList))
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                SEARCH_APPROVAL_PERSON_LIST -> {
                    if (data != null) {
                        val intent = Intent()
                        intent.putExtra("members", data.getStringExtra("members"))
                        setResult(Activity.RESULT_OK, intent)
                    }
                    finish()
                }
            }
        }
    }



    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return true
    }

    @Suppress("DEPRECATION")
    abstract class MyPagerAdapter(fragmentManager: FragmentManager) :
            FragmentPagerAdapter(fragmentManager) {
        val fragments = arrayOfNulls<SelectApprovalPersonFragment>(2)
        override fun getCount(): Int {
            return fragments.size
        }
    }
}