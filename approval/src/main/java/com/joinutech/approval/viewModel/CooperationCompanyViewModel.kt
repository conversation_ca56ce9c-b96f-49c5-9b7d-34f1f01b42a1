package com.joinutech.approval.viewModel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.joinutech.ddbeslibrary.bean.CompanyExternalMemberSearchBean
import com.joinutech.ddbeslibrary.bean.CooperationCopmanyDetailBean
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.trello.rxlifecycle3.LifecycleTransformer

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/10 9:52
 * @packageName: com.joinutech.approval.viewModel
 * @Company: JoinuTech
 */
class CooperationCompanyViewModel : ViewModel() {

    val quitCooperationCompanyResult = MutableLiveData<Any>()
    val quitCooperationCompanyError = MutableLiveData<String>()
    val getCooperationCompanyDetailResult = MutableLiveData<CooperationCopmanyDetailBean>()
    val getCooperationCompanyDetailError = MutableLiveData<String>()
    val searchDepMemberResult = MutableLiveData<List<SearchMemberBean>>()
    val searchDepMemberError = MutableLiveData<String>()
    val searchDepExternalMemberResult = MutableLiveData<List<CompanyExternalMemberSearchBean>>()
    val searchDepExternalMemberError = MutableLiveData<String>()

    fun quitCooperationCompany(life: LifecycleTransformer<Result<Any>>,
                               companyId: String, token: String) {
        AddressbookService.quitCooperationCompany(companyId, token)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        quitCooperationCompanyError.value = ex.message
                    }

                    override fun onComplete() {}

                    override fun onNext(t: Any?) {
                        quitCooperationCompanyResult.value = t
                    }

                })
    }

    fun searchDepMember(life: LifecycleTransformer<Result<List<SearchMemberBean>>>,
                        companyId: String, keyWord: String = "") {
        AddressbookService.searchDepMember(companyId, keyWord)
                .compose(life)
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<List<SearchMemberBean>>() {
                    override fun onError(ex: ApiException) {
                        searchDepMemberError.value = ex.message
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: List<SearchMemberBean>?) {
                        searchDepMemberResult.value = t
                    }
                })
    }

    //搜索外部联系人列表
    fun searchDepExternalMember(life: LifecycleTransformer<Result<
            List<CompanyExternalMemberSearchBean>>>, companyId: String, keyWord: String = "") {
        AddressbookService.searchDepExternalMember(companyId, keyWord)
                .compose(life)
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<List<CompanyExternalMemberSearchBean>>() {
                    override fun onError(ex: ApiException) {
                        searchDepExternalMemberError.value = ex.message
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: List<CompanyExternalMemberSearchBean>?) {
                        searchDepExternalMemberResult.value = t
                    }
                })
    }

    //得到合作团队详情
    fun getCooperationCompanyDetail(life: LifecycleTransformer<Result<CooperationCopmanyDetailBean>>,
                                    companyId: String, token: String) {
        AddressbookService.getCooperationCompanyDetail(companyId, token)
                .compose(life)
                .compose(ErrorTransformer.getInstance<CooperationCopmanyDetailBean>())
                .subscribe(object : BaseSubscriber<CooperationCopmanyDetailBean>() {
                    override fun onError(ex: ApiException) {
                        getCooperationCompanyDetailError.value = ex.message
                    }

                    override fun onComplete() {}

                    override fun onNext(t: CooperationCopmanyDetailBean?) {
                        getCooperationCompanyDetailResult.value = t
                    }

                })
    }
}