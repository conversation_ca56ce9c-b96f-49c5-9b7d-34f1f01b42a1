package com.joinutech.approval

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.appbar.CollapsingToolbarLayout
import com.gyf.immersionbar.ImmersionBar
import com.joinutech.approval.adapter.ApprovalTypeSelectDialogAdapter
import com.joinutech.approval.databinding.ActivityCooperationCompanyDetailBinding
import com.joinutech.approval.temp.ApprovalTypeDialogBean
import com.joinutech.approval.utils.MsgType
import com.joinutech.approval.utils.RouteApr
import com.joinutech.approval.viewModel.CooperationCompanyViewModel
import com.joinutech.common.util.CompanyHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.EventBusAction.Event_REFRESH_COOPERATION_COMPANY_DETAIL
import com.joinutech.ddbeslibrary.utils.EventBusAction.Event_REFRESH_COOPERATION_ISDELETED
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.AppBarStateChangerListener
import com.luck.picture.lib.tools.ScreenUtils
//import kotlinx.android.synthetic.main.activity_cooperation_company_detail.*
//import kotlinx.android.synthetic.main.include_cooperation_bottom.*
//import kotlinx.android.synthetic.main.include_cooperation_company_top.*
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * @Description: 合作团队首页
 * @Author: 黄洁如
 * @Time: 2019/11/24 15:39
 * @packageName: com.joinutech.approval.aprhistory
 * @Company: JoinuTech
 */
@Route(path = RouteApr.companyCooperationCompanyDetailActivity)
class CooperationCompanyDetailActivity : MyUseBindingActivity<ActivityCooperationCompanyDetailBinding>() {

    override val contentViewResId: Int = R.layout.activity_cooperation_company_detail
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityCooperationCompanyDetailBinding {
        return ActivityCooperationCompanyDetailBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = true

    private var approvalType = 1
    private var undoType = 0
    private var dialog: AlertDialog? = null

    var companyId = ""

    private var isSeeUndo = false
    private lateinit var pagerAdapter: MyCooperationCompanyPageAdapter
    private var currentPosition = 0
    private lateinit var viewModel: CooperationCompanyViewModel
    private lateinit var fragments: Array<Fragment?>

    override fun initImmersion() {
        commonToolBarId!!.visibility = View.GONE
        ImmersionBar.with(this)
                .statusBarColor(com.tea.fileselectlibrary.R.color.white)
                .init()
    }

    private var company: WorkStationBean? = null
    override fun initView() {
        whiteStatusBarBlackFont()
        companyId = intent.getStringExtra(ConsKeys.COMPANY_ID) ?: company?.companyId ?: ""
        company = intent.getSerializableExtra(ConsKeys.KEY_INTENT_DATA) as WorkStationBean?
        if (companyId.isNullOrBlank()) {
            finish()
        }
        if (company == null) {
            company = CompanyHolder.getCompanyByCompanyId(companyId) ?: return
        }
        
        val lp = binding.tbTopBar.layoutParams as CollapsingToolbarLayout.LayoutParams
        lp.topMargin = ScreenUtils.getStatusBarHeight(mContext) -
                ScreenUtils.dip2px(mContext!!, 5f)
        binding.tbTopBar.layoutParams = lp
        
        binding.tvCooperTitle.text = "我的合作团队"
        viewModel = getModel(CooperationCompanyViewModel::class.java)
    }

    override fun initLogic() {
        super.initLogic()

        
        binding.ivBackIcon.setOnClickListener(this)
        binding.includeCooperationTop.tvEmailEdit.setOnClickListener(this)
        
        
        binding.includeCooperationBottom.clUndoLayout.setOnClickListener(this)
        
        binding.includeCooperationBottom.clApprovalTypeLayout.setOnClickListener(this)
        
        binding.ivMoreIcon.setOnClickListener(this)
        binding.includeCooperationBottom.launch.setOnClickListener(this)
        binding.includeCooperationBottom.copy.setOnClickListener(this)
        fragments = arrayOfNulls(2)
        for (i in fragments.indices) {
            if (fragments[i] == null) {
                fragments[i] = if (i == 0) {
                    CooperationCompanyApprovalFragment.newInstance(companyId)
                } else {
                    CooperationCompanyCopyFragment.newInstance(companyId)
                }
            }
        }
        pagerAdapter = object : MyCooperationCompanyPageAdapter(this) {
            override fun createFragment(position: Int): Fragment {
                return fragments[position]!!
            }

        }
        
        binding.includeCooperationBottom.vpViewpager.adapter = pagerAdapter
        binding.includeCooperationBottom.vpViewpager.offscreenPageLimit = 1
        binding.includeCooperationBottom.vpViewpager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                currentPosition = position
                setTabIndex(position)
            }

        })
        getObserver()
        getDetailData()
        binding.ablTopBar.addOnOffsetChangedListener(object : AppBarStateChangerListener() {
            override fun onStateChanged(appBarLayout: AppBarLayout?, state: State?) {
                when (state) {
                    State.EXPANDED -> {
                        //展开状态
                        Log.e("AppBarLayout的状态", "展开状态")
                        dealExpandStatus(currentPosition, 0)
                    }
                    State.COLLAPSED -> {
                        //折叠状态
                        Log.e("AppBarLayout的状态", "折叠状态")
                        dealExpandStatus(currentPosition, 2)
                    }
                    else -> {
                        //中间状态
                        Log.e("AppBarLayout的状态", "中间状态")
                        dealExpandStatus(currentPosition, 1)
                    }
                }
            }
        })
    }

    private fun getDetailData() {
        getLoadingDialog("", false)
        viewModel.getCooperationCompanyDetail(bindToLifecycle(), companyId, accessToken!!)
    }

    private fun getObserver() {
        viewModel.quitCooperationCompanyResult.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, "删除合作团队成功")
            //退出团队后需要刷新团队列表和通讯录首页，我的其他团队
            // 删除协作团队后更新团队列表，团队列表刷新后会触发通讯录页面刷新，这里不需要再单独调用
//            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ADDRFRAGMENT, 0))
            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST, "-2"))
            finish()
        })
        viewModel.quitCooperationCompanyError.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
        viewModel.getCooperationCompanyDetailResult.observe(this, Observer {
            dismissDialog()
            if (StringUtils.isNotBlankAndEmpty(it.companyLogo)) {
                ImageLoaderUtils.loadImage(mContext!!, binding.includeCooperationTop. logo, it.companyLogo)
            }
            binding.includeCooperationTop. name.text = it.companyName
            if (StringUtils.isNotBlankAndEmpty(it.companyIndustry)) {
                binding.includeCooperationTop.tvType.text = it.companyIndustry
            } else {
                 binding.includeCooperationTop.tvType.text = "未填写"
            }
            if (StringUtils.isNotBlankAndEmpty(it.companyType)) {
                binding.includeCooperationTop.tvKind.text = it.companyType
            } else {
                 binding.includeCooperationTop.tvKind.text = "未填写"
            }
            
            binding.includeCooperationTop.tvPersonName.text = it.externalContactsName
            binding.includeCooperationTop.tvPersonType.text =
                    when (it.externalContactsType.toString()) {
                        "1" -> "客户"
                        "2" -> "渠道商"
                        "3" -> "供应商"
                        "4" -> "合作伙伴"
                        "5" -> "其他类型"
                        else -> "客户"
                    }
             binding.includeCooperationTop.tvPersonPhone.text = it.mobile
            if (StringUtils.isNotBlankAndEmpty(it.externalContactsEmail))
                 binding.includeCooperationTop.tvEmail.text = it.externalContactsEmail
            else binding.includeCooperationTop.tvEmail.text = "邮箱未填写"
            if (StringUtils.isNotBlankAndEmpty(it.externalContactsDesc))
                 binding.includeCooperationTop.tvRemarkName.text = it.externalContactsDesc
            else binding.includeCooperationTop.tvRemarkName.text = "描述未填写"
        })
        viewModel.getCooperationCompanyDetailError.observe(this, Observer {
            dismissDialog()
            if (company != null) {
                if (StringUtils.isNotBlankAndEmpty(company!!. logo)) {
                    ImageLoaderUtils.loadImage(mContext!!, binding.includeCooperationTop. logo, company!!.logo)
                }
                binding.includeCooperationTop. name.text = company!!.name
                binding.includeCooperationTop.tvType.text = "未填写"
                binding.includeCooperationTop.tvKind.text = "未填写"
            }
            ToastUtil.show(mContext!!, it)
        })
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.ivBackIcon -> {
                onBackPressed()
            }
            binding.includeCooperationTop.tvEmailEdit -> {
                ARouter.getInstance()
                        .build(RoutePersonal.personNameOrEmailActivity)
                        .withString("type", "cooperationEmail")
                        .withString("companyId", companyId)
                        .withString("personName", binding.includeCooperationTop.tvEmail.text.toString())
                        .navigation(this, COMPANY_INTRO_CHANGE)
            }
            binding.includeCooperationBottom.clUndoLayout -> {
                isSeeUndo = !isSeeUndo
                 binding.includeCooperationBottom.ivUndoIcon.isSelected = isSeeUndo
                undoType = if (isSeeUndo) 1 else 0
                refreshPage(currentPosition, undoType)
            }
            binding.includeCooperationBottom.clApprovalTypeLayout -> {
                selectApprovalType()
            }
            binding.ivMoreIcon -> {
                orgDealPop()
            }
            binding.includeCooperationBottom.launch -> {
                setTabIndex(0)
                binding.includeCooperationBottom.vpViewpager.currentItem = 0
            }
            binding.includeCooperationBottom.copy -> {
                setTabIndex(1)
                binding.includeCooperationBottom.vpViewpager.currentItem = 1
            }
        }
    }

    private fun selectApprovalType() {
        val dialog = object : DialogHolder(this,
//                R.layout.dialog_white_top_corner, Gravity.BOTTOM) {
                com.joinutech.ddbeslibrary.R.layout.dialog_white_top_corner, Gravity.BOTTOM) {
            override fun bindView(dialogView: View) {
                val cancel = dialogView.findViewById<TextView>(R.id.cancel)
                cancel.setOnClickListener {
                    dialog?.dismiss()
                }
                val typeListRv = dialogView.findViewById<RecyclerView>(R.id.rv_list)
                typeListRv.layoutManager = LinearLayoutManager(mContext)
                val titleList = arrayListOf<ApprovalTypeDialogBean>()
                titleList.add(ApprovalTypeDialogBean("待处理"))
                titleList.add(ApprovalTypeDialogBean("已审批"))
                titleList.add(ApprovalTypeDialogBean("已完成"))
                val adapter = ApprovalTypeSelectDialogAdapter(mContext!!, titleList)
                typeListRv.adapter = adapter
                adapter.setItemClickListener(object : ItemClickListener {
                    override fun onItemClick(position: Int) {
                        approvalType = position + 1
                         binding.includeCooperationBottom.tvApprovalHandle.text = adapter.mData[position].title
                        refreshPage(currentPosition, approvalType)
                        dialog?.dismiss()
                    }

                })
            }

        }
        dialog.initView()
        dialog.show(true)
    }

    private fun setTabIndex(position: Int) {
         binding.includeCooperationBottom.lineLaunch.visibility = View.INVISIBLE
        binding.includeCooperationBottom.lineCopy.visibility = View.GONE
        binding.includeCooperationBottom.clUndoLayout.visibility = View.GONE
        binding.includeCooperationBottom.clApprovalTypeLayout.visibility = View.GONE
        when (position) {
            0 -> {
                binding.includeCooperationBottom.lineLaunch.visibility = View.VISIBLE
                binding.includeCooperationBottom.clApprovalTypeLayout.visibility = View.VISIBLE
            }
            1 -> {
                binding.includeCooperationBottom.lineCopy.visibility = View.VISIBLE
                binding.includeCooperationBottom.clUndoLayout.visibility = View.VISIBLE
            }
        }
    }

    @SuppressLint("InflateParams")
    private fun orgDealPop() {
        val mAddPopup: PopupWindow
        val mAddPopupLayout = LayoutInflater.from(mContext)
//                .inflate(R.layout.pop_single_button_toolbar_down, null)
                .inflate(com.joinutech.ddbeslibrary.R.layout.pop_single_button_toolbar_down, null)
        mAddPopup = PopupWindow(mAddPopupLayout, ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT)
        mAddPopup.isOutsideTouchable = true
        mAddPopup.isFocusable = true
        mAddPopup.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        mAddPopup.showAtLocation(baseView!!, Gravity.NO_GRAVITY, 0, 0)
        val dealTv = mAddPopupLayout.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.showTv)
        dealTv.text = "删除合作团队"
        dealTv.setOnClickListener(object : OnNoDoubleClickListener {
            override fun onNoDoubleClick(v: View) {
                if (mAddPopup.isShowing) mAddPopup.dismiss()
                showDeleteCooperationOrg()
            }

        })
        mAddPopupLayout?.setOnClickListener {
            if (mAddPopup.isShowing) mAddPopup.dismiss()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun showDeleteCooperationOrg() {
        val dialog = BaseCenterDialogHelper(
                activity = this,
                onBindView = { view ->
                    run {
                        val tvTitle = view.findViewById<TextView>(R.id.tv_title)
                        tvTitle.text = "你确认要主动删除这个合作团队么？"
                        val tvConfirm = view.findViewById<TextView>(R.id.confirm)
                        tvConfirm.text = "确认删除"
                        val tvContent = view.findViewById<TextView>(R.id.tv_content)
                        tvContent.text = "解除之后，你将从该团队的外部联系人列表移除，无法再参与本团队的工作执行环节"
                        val tvHint = view.findViewById<View>(com.joinutech.ddbeslibrary.R.id.tv_hint)
                        tvHint.visibility = View.GONE
                    }
                },
                onConfirm = {
                    getLoadingDialog("", false)
                    viewModel.quitCooperationCompany(bindToLifecycle(), companyId, accessToken)
                },
                onCancel = {
                })
        dialog.initView()
        dialog.show(true)
//        val view1 = View.inflate(mContext, R.layout.dialog_base, null)
//        dialog = BottomDialogUtil.showBottomDialog(mContext!!, view1, Gravity.CENTER, false)
//        val confirm = view1.findViewById<TextView>(R.id.confirm_base)
//        val cancel = view1.findViewById<TextView>(R.id.cancel_base)
//        confirm.text = "确认删除"
//        val title = view1.findViewById<TextView>(R.id.tv1)
//        title.text = "你确认要主动删除这个合作团队么？"
//        val content = view1.findViewById<TextView>(R.id.tv2)
//        content.text = "解除之后，你将从该团队的外部联系人列表移除，无法再参与本团队的工作执行环节"
//        content.gravity = Gravity.START
//        confirm.setOnClickListener {
//            dialog?.dismiss()
//            getLoadingDialog("", false)?.show()
//            viewModel.quitCooperationCompany(bindToLifecycle(), companyId, accessToken)
//        }
//        cancel.setOnClickListener {
//            dialog?.dismiss()
//        }
    }

    private var needRefreshHome = false
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                COMPANY_INTRO_CHANGE -> {
                    val email = data?.getStringExtra("email")
                    binding.includeCooperationTop.tvEmail.text = email
                    ToastUtil.show(mContext!!, "修改成功")
                }
                1010 -> {
                    //需要刷新红点
                    if (data != null && data.hasExtra("listIndex")) {
                        val listIndex = data.getIntExtra("listIndex", -1)
                        if (listIndex >= 0) {
                            if (data.hasExtra("resultType")) {
                                val resultType = data.getIntExtra(
                                        "resultType", -1)
                                if (resultType >= 0) {
                                    refreshPage(currentPosition,
                                            if (currentPosition == 0) approvalType
                                            else undoType)
                                    needRefreshHome = true
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun receiveMsg(msg: EventBusEvent<Int>) {
        when (msg.code) {
            MsgType.COORPERATION_APR_NO_PROCESS_REFRESH.name -> {
                if (msg.data!! > 0) {
                    
                    binding.includeCooperationBottom.dotLaunch.visibility = View.VISIBLE
                } else {
                    binding.includeCooperationBottom.dotLaunch.visibility = View.GONE
                }
            }
            MsgType.COORPERATION_APR_COPY_REFRESH.name -> {
                if (msg.data!! > 0) {
                     binding.includeCooperationBottom.dotCopy.visibility = View.VISIBLE
                } else {
                    binding.includeCooperationBottom.dotCopy.visibility = View.GONE
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun cooperationIsDeleted(msg: EventBusEvent<String>) {
        when (msg.code) {
            Event_REFRESH_COOPERATION_ISDELETED -> {
                if (StringUtils.isNotBlankAndEmpty(msg.data) && msg.data == companyId) {
                    //当前所在的合作团队被删除，需要关闭当前页
                    val dialog = MyDialog(mContext!!, 280, 140,
                            "合作团队状态异常，暂不可操作",
                            needBtnConfirm = true, needBtnCancel = false, bgResourceId = 0)
                    dialog.setBtnLeftText("好的")
                    dialog.setCanceledOnTouchOutside(false)
                    dialog.setCancelable(false)
                    dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
                        override fun clickLeftBtn() {
                            finish()
                        }

                    })
                    dialog.show()
                }
            }
            Event_REFRESH_COOPERATION_COMPANY_DETAIL -> {
                //经过了快速审批，审批数量有变化，刷新审批列表
                if (currentPosition == 0) {
                    refreshPage(currentPosition, approvalType)
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun refreshPoint(msgEvent: EventBusEvent<Any>) {
        when (msgEvent.code) {
            "apr_notice_receive_event" -> {
                showLog("接收到审批通知，触发审批相关页面刷新,刷新审批列表数据")
                if (msgEvent.data != null) {
                    val map = msgEvent.data as Map<*, *>
                    if (map.containsKey("companyId") && map["companyId"] == companyId) {
                        refreshPage(currentPosition,
                                if (currentPosition == 0) approvalType
                                else undoType)
                    }
                }
            }
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        if (needRefreshHome) {
            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.refresh_all_appro_undo, 0))
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        dialog?.show()
    }

    fun refreshPage(position: Int, approvalType: Int) {
        if (position == 0) {
            (fragments[0] as CooperationCompanyApprovalFragment)
                    .setApprovalTypeRefreshData(approvalType)
        } else {
            (fragments[1] as CooperationCompanyCopyFragment)
                    .setApprovalTypeRefreshData(approvalType)
        }
    }

    fun dealExpandStatus(position: Int, status: Int) {
        if (position == 0) {
            (fragments[0] as CooperationCompanyApprovalFragment)
                    .dealExpandStatus(status)
        }
    }

    abstract class MyCooperationCompanyPageAdapter(fragmentActivity: FragmentActivity)
        : FragmentStateAdapter(fragmentActivity) {

        override fun getItemCount(): Int {
            return 2
        }
    }
}