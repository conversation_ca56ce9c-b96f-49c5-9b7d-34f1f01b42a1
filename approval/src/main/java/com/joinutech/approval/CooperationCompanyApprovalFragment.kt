package com.joinutech.approval

import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.approval.adapter.CooperationCompanyApprovalAdapter
import com.joinutech.approval.data.GetAprList
import com.joinutech.approval.data.MyAprListData
import com.joinutech.approval.utils.MsgType
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.ItemClickListener
import com.joinutech.approval.utils.RouteApr
import com.scwang.smart.refresh.layout.SmartRefreshLayout

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/3 14:06
 * @packageName: com.joinutech.approval
 * @Company: JoinuTech
 */
class CooperationCompanyApprovalFragment : AprBaseFragment() {

    override val layoutRes: Int
        get() = R.layout.fragment_cooperation_company_approval

    override fun openEventBus(): Boolean {
        return false
    }

    private lateinit var rvTempList: RecyclerView
    private lateinit var adapter: CooperationCompanyApprovalAdapter
    private lateinit var srlRefresh: SmartRefreshLayout
    private lateinit var noDataViewPage: PageEmptyView
    private lateinit var fastApproval: TextView
    private var approvalList = arrayListOf<MyAprListData>()
    private var companyId = ""
    private var approvalType = 1
    private val startPage = 1
    private val pageSize = 20
    private var page: Int = startPage
    private var pageLength = pageSize

    companion object {
        private const val COMPANY_ID = "company_id"
        fun newInstance(companyId: String): CooperationCompanyApprovalFragment {
            return CooperationCompanyApprovalFragment().apply {
                arguments = Bundle().apply {
                    putString(COMPANY_ID, companyId)
                }
            }
        }
    }

    override fun initView(rootView: View) {
        companyId = arguments?.getString(COMPANY_ID)!!
        rvTempList = rootView.findViewById(R.id.rv_list)
        srlRefresh = rootView.findViewById(R.id.srl_refresh)
        noDataViewPage = rootView.findViewById(R.id.layout_empty_layout)
        noDataViewPage.hide()
        rvTempList.layoutManager = LinearLayoutManager(mActivity)
        fastApproval = rootView.findViewById(R.id.fastApproval)
        srlRefresh.setEnableRefresh(false)
        srlRefresh.setEnableLoadMore(true)
    }

    override fun initLogic() {
        super.initLogic()
        fastApproval.setOnClickListener(this)
        adapter = CooperationCompanyApprovalAdapter(mActivity, approvalList)
        adapter.setModelType(2)
        adapter.setSubModelType(approvalType)
        adapter.setItemClickListener(object : ItemClickListener {
            override fun onItemClick(position: Int) {
                val bundle = Bundle()
                bundle.putString("companyId", companyId)
                bundle.putString("approveId", adapter.mData[position].approveId)
                bundle.putInt("modelIndex", 2)
                bundle.putBoolean("isCooperationCompany", true)
                if (approvalType == 1) {
                    bundle.putInt("listIndex", 1)
                    jump(RouteApr.APR_DETAIL_PAGE, bundle, 1010)
                } else {
                    jump(RouteApr.APR_DETAIL_PAGE, bundle)
                }
            }

        })
        rvTempList.adapter = adapter
        srlRefresh.setOnLoadMoreListener {
            if (approvalList.size > 0 && approvalList.size % pageLength > 0) {
                srlRefresh.setEnableLoadMore(false)
                srlRefresh.finishLoadMore(200)
            } else {
                page++
                srlRefresh.finishLoadMore(1200)
                getData(false)
            }
        }
        getObserver()
        getData(true)
    }

    private fun getObserver() {
        viewModel?.getMyAprListResult?.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = { data ->
                if (page == startPage) {
                    approvalList.clear()
                }
                if (!data.approveList.isNullOrEmpty()) {
                    approvalList.addAll(data.approveList)
                }
                if (!approvalList.isNullOrEmpty()) {
                    noDataViewPage.hide()
                    srlRefresh.visibility = View.VISIBLE
                } else {
                    noDataViewPage.show()
                    srlRefresh.visibility = View.GONE
                }
                /**我审批的待处理数量更新*/
                if (approvalType == 1) {
                    showLog("更新未处理审批数量")
                    EventBusUtils.sendEvent(EventBusEvent(
                            MsgType.COORPERATION_APR_NO_PROCESS_REFRESH.name,
                            data = data.count))
                }
                adapter.notifyDataSetChanged()
            }, onError = { code, msg ->
                if (page == startPage) {
                    noDataViewPage.show()
                    srlRefresh.visibility = View.GONE
                }
                if (page > startPage) {
                    page--
                }
                showToast(msg)
            }, onDefault = { msg ->
                if (page == startPage) {
                    noDataViewPage.show()
                    srlRefresh.visibility = View.GONE
                }
                if (page > startPage) {
                    page--
                }
                showToast(msg)
            })

        })
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.layout_empty_layout -> {
                page = startPage
                getData(true)
            }
            R.id.fastApproval -> {
                val bundle = Bundle()
                bundle.putString("companyId", companyId)
                bundle.putBoolean("cooperationCompanyApproval", true)
                jump(RouteApr.APR_QUICK_PAGE, bundle)
            }
        }
    }

    fun setApprovalTypeRefreshData(approvalType: Int) {
        page = startPage
        this.approvalType = approvalType
        getData(false)
    }

    fun dealExpandStatus(status: Int) {
        if (status == 2) {
            if (!approvalList.isNullOrEmpty()) fastApproval.visibility = View.VISIBLE
            else fastApproval.visibility = View.GONE
        } else {
            fastApproval.visibility = View.GONE
        }
    }

    private fun getData(isRefresh: Boolean) {
        if (isRefresh) showLoading()
        val getMyAprList = GetAprList(organizationId = companyId, type = 2,
                myApproveType = approvalType, status = 0,
                start = page, length = pageLength,
                keyword = "", modelId = "")
        viewModel?.getMyAprList(getMyAprList.getMap(getMyAprList.type))
    }


}