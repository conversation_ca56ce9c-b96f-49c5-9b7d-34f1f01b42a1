package com.joinutech.approval.launch

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.approval.AprBaseFragment
import com.joinutech.approval.R
import com.joinutech.approval.aprhistory.AprListAdapter
import com.joinutech.approval.data.GetAprList
import com.joinutech.approval.data.MyAprListData
import com.joinutech.approval.utils.MsgType
import com.joinutech.approval.utils.RouteApr
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.widget.OnEmptyClickListener
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.utils.CustomHeader
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.common.adapter.MyAdapter
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * @className: AprTempFragment
 * @desc: 审批列表页面
 * @author: zyy
 * @date: 2019/8/2 9:12
 * @company: joinUTech
 * @leader: ke
 */
@Deprecated("no used , change to web")
class MyAprListFragment : AprBaseFragment(), OnEmptyClickListener {
    override val layoutRes: Int = R.layout.common_refresh_list_layout
    private lateinit var noDataView: PageEmptyView
    private var refreshLayout: SmartRefreshLayout? = null
    private lateinit var rvTempList: RecyclerView

    private val list = arrayListOf<MyAprListData>()
    private lateinit var adapter: MyAdapter<MyAprListData>

    /**审批大分组 发起1，审批2，抄送3*/
    private var modelIndex: Int = 1

    /**审批大分组内小分组类型 我发起1 （3，1，5），我审批2（1，2，3），抄送3*/
    private var subListType: Int = 1
    private val startPage = 1
    private val pageSize = 20

    private var page: Int = startPage
    private var pageLength = pageSize

    private var currentModelId: String = ""
    private var currentKeyWord: String = ""

    //    private var isRefresh = false
    override fun initView(rootView: View) {
        showLog("initView")
        modelIndex = arguments?.getInt(APR_FUN_INDEX) ?: 1
        subListType = arguments?.getInt(APR_LIST_SUBTYPE) ?: 1
        noDataView = rootView.findViewById(R.id.layout_empty_layout)
        noDataView.clickListener = this
        noDataView.hide()
        refreshLayout = rootView.findViewById(R.id.srl_refresh)
        refreshLayout?.setEnableRefresh(true)
        refreshLayout?.setEnableLoadMore(true)
        refreshLayout?.setRefreshHeader(CustomHeader(mActivity))
        refreshLayout?.setOnRefreshListener {
            page = startPage
            refreshLayout?.finishRefresh(1200)
            loadData()
        }

        refreshLayout?.setOnLoadMoreListener {
            if (list.size > 0 && list.size % pageLength > 0) {
                refreshLayout?.setEnableLoadMore(false)
                refreshLayout?.finishLoadMore(200)
            } else {
                page++
                refreshLayout?.finishLoadMore(1200)
                loadData()
            }
        }

        rvTempList = rootView.findViewById(R.id.rv_list)
        adapter = AprListAdapter(mActivity, list, modelIndex, subListType)
        { position: Int, data: MyAprListData, _: View ->
            if (companyBean != null) {
                val bundle = Bundle()
                bundle.putString("companyId", companyBean!!.companyId)
                bundle.putString("approveId", data.approveId)
                bundle.putInt("modelIndex", modelIndex)
                if (modelIndex == 1 || (modelIndex == 2 && subListType == 1)) {// 我发起的 或者 我审批且待审批
                    bundle.putInt("listIndex", position)
                    jump(RouteApr.APR_DETAIL_PAGE, bundle, 1010)
                } else {
                    jump(RouteApr.APR_DETAIL_PAGE, bundle)
                }
            }
        }
//        rvTempList.addItemDecoration(ListItemDecoration(DeviceUtil.dip2px(this, 10f)))
        rvTempList.layoutManager = LinearLayoutManager(mActivity)
        rvTempList.adapter = adapter
    }


    override fun onAction(actionCode: Int) {
        page = startPage
        loadData()
    }

    var getMyAprList: GetAprList? = null

    override fun initLogic() {
        super.initLogic()
        viewModel?.getMyAprListResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                if (page == startPage) {
                    list.clear()
                }
                if (it.approveList.isNotEmpty()) {
                    list.addAll(it.approveList)
                }
                if (list.isNotEmpty()) {
                    noDataView.hide()
                    refreshLayout?.visibility = View.VISIBLE
                } else {
                    noDataView.show()
                    refreshLayout?.visibility = View.GONE
                }
                /**我审批的待处理数量更新*/
                if (modelIndex == 2 && subListType == 1) {
                    showLog("更新未处理审批数量")
                    EventBusUtils.sendEvent(EventBusEvent(MsgType.APR_NO_PROCESS_REFRESH.name, data = arrayOf(0, it.count)))
                }

                /**我发起已完成未读数更新*/
                if (modelIndex == 1 && subListType == 1) {
                    showLog("更新已完成审批未读数量")
                    EventBusUtils.sendEvent(EventBusEvent(MsgType.APR_FINISH_NO_READ_REFRESH.name, data = arrayOf(1, it.count)))
                }
                adapter.notifyDataSetChanged()
            }, onError = { _, msg ->
                if (page == startPage) {
                    noDataView.show()
                    refreshLayout?.visibility = View.GONE
                }
                if (page > startPage) {
                    page--
                }
                showToast(msg)
            }, onDefault = { msg ->
                if (page == startPage) {
                    noDataView.show()
                    refreshLayout?.visibility = View.GONE
                }
                if (page > startPage) {
                    page--
                }
                showToast(msg)
            })

        })

        companyBean = CompanyHolder.getCurrentOrg()

        if (companyBean != null) {
            getMyAprList = when (modelIndex) {
                1 -> {
                    // 我发起的
                    GetAprList(organizationId = companyBean!!.companyId, type = modelIndex,
                            myApproveType = 0, status = subListType,
                            start = page, length = pageLength,
                            keyword = currentKeyWord, modelId = currentModelId)
                }
                else -> {
                    // 我审批的
                    GetAprList(organizationId = companyBean!!.companyId, type = modelIndex,
                            myApproveType = subListType, status = 0,
                            start = page, length = pageLength,
                            keyword = currentKeyWord, modelId = currentModelId)
                }
            }
        }
        loadData()
    }

    var companyBean: WorkStationBean? = null

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun refreshPoint(msgEvent: EventBusEvent<Map<String, Any>>) {
        when (msgEvent.code) {
            "apr_notice_receive_event" -> {
                showLog("接收到审批通知，触发审批相关页面刷新，刷新审批列表")
                val map = msgEvent.data
                if (getMyAprList != null && map != null
                        && map.containsKey("typeId")
                        && map["typeId"] in arrayOf(1, 2)
                        && map.containsKey("companyId")
                        && map["companyId"] == getMyAprList?.organizationId) {
                    page = startPage
                    loadData()
                }
            }
//            MsgType.APR_DETAIL_READED.name -> {
//                if (modelIndex == 1 && subListType == 1) {
//                    showLog("刷新 我发起已完成 列表")
//                    page = startPage
//                    refreshLayout.finishRefresh(1200)
//                    loadData()
//                }
//            }
            MsgType.APR_ADD_NODE_SUCCESS.name -> {
                if (modelIndex == 2 && subListType == 1) {
                    showLog("刷新待我审批列表")
                    page = startPage
                    refreshLayout?.finishRefresh(1200)
                    loadData()
                }
            }
            MsgType.APR_PROCESS_OVER.name -> {
                if (modelIndex == 2 && subListType in arrayOf(1, 2)) {
                    page = startPage
                    refreshLayout?.finishRefresh(1200)
                    loadData()
                }
            }
            MsgType.APR_RECALL_SUCCESS.name, MsgType.APR_RE_COMMIT_SUCCESS.name -> {
                if (modelIndex == 1 && subListType in arrayOf(3, 5)) {
                    page = startPage
                    refreshLayout?.finishRefresh(1200)
                    loadData()
                }
            }
        }
    }

    fun getAprListCount(): Int {
        return list.size
    }

    fun setKeyWord(keyWord: String) {
        if (getMyAprList != null) {
            page = startPage
            getMyAprList!!.keyword = keyWord
            loadData()
        }
    }

    fun setFilter(modelId: String) {
        if (getMyAprList != null) {
            page = startPage
            getMyAprList!!.modelId = modelId
            loadData()
        }
    }

    fun onChangePage(modelId: String, keyWord: String) {
        if (this.currentModelId != modelId || this.currentKeyWord != keyWord) {
            this.currentKeyWord = keyWord
            this.currentModelId = modelId
            page = startPage
            if (getMyAprList != null) {
                getMyAprList!!.start = page
                getMyAprList!!.modelId = modelId
                getMyAprList!!.keyword = keyWord
                loadData()
            }
        }
    }

    fun onRefresh(listIndex: Int, needRefresh: Boolean) {
        if (modelIndex == 1 && subListType == 1 && listIndex >= 0 && listIndex in list.indices) {
            //我发起 已完成 已读回调，更新列表；
            list[listIndex].isRead = 1
            adapter.notifyItemChanged(listIndex)
        }

        if (needRefresh) {
            page = startPage
            refreshLayout?.finishRefresh(1200)
            loadData()
        }
    }

    private fun loadData() {
        if (getMyAprList != null) {
            getMyAprList!!.start = page
            showLoading()
            viewModel?.getMyAprList(getMyAprList!!.getMap(getMyAprList!!.type))
        }
    }

    companion object {
        /**
         * The fragment argument representing the section number for this
         * fragment.
         */
        private const val APR_FUN_INDEX = "temp_type"
        private const val APR_LIST_SUBTYPE = "temp_subtype"

        /**
         * Returns a new instance of this fragment for the given section
         * number.
         */
        @JvmStatic
        fun newInstance(funcTopIndex: Int, subType: Int): MyAprListFragment {
            Log.e("fragment", "newInstance()")
            return MyAprListFragment().apply {
                arguments = Bundle().apply {
                    putInt(APR_FUN_INDEX, funcTopIndex)
                    putInt(APR_LIST_SUBTYPE, subType)
                }
            }
        }
    }

}