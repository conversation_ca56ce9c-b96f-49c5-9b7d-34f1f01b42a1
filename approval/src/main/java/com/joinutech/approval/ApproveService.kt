package com.joinutech.approval

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.approval.api.ApprovalApi
import com.joinutech.approval.api.TicketApi
import com.joinutech.approval.aprhistory.Codes
import com.joinutech.approval.data.FastProcessApprovalData
import com.joinutech.approval.utils.RouteApr
import com.joinutech.approval.utils.UploadFileUtil
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.bean.UploadFileBean
import com.joinutech.ddbeslibrary.request.RetrofitClient
import com.joinutech.ddbeslibrary.request.RxScheduleUtil
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.Loggerr
import com.joinutech.ddbeslibrary.utils.MyDialog
import com.joinutech.ddbeslibrary.utils.RouteProvider.APR_FUN_PROVIDER
import com.joinutech.ddbeslibrary.utils.StringUtils
import java.io.File

/**
 * @PackageName: com.joinutech.approval
 * @ClassName:
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/8/31 11:30
 * @Desc: //TODO 审批模块对外提供功能调用入口
 */
@Route(path = APR_FUN_PROVIDER)
class ApproveServiceImpl : ApproveService {

    private var mContext: Context? = null
    override fun init(context: Context?) {
        mContext = context
    }

    override fun openPage(path: String, params: Bundle) {
        when (path) {
            "approve_detail"->{
                ARouter.getInstance()
                    .build(RouteApr.APR_FUNC_WEB_PAGE)
                    .with(params)
                    .navigation()
            }
            "cooperation_approve"->{
                ARouter.getInstance()
                    .build(RouteApr.companyCooperationCompanyDetailActivity)
                    .with(params)
                    .navigation()
            }

        }
    }

    override fun openPageWithResult(activity: FragmentActivity, path: String, params: Bundle, result: (data: String) -> Unit) {
        when (path) {
            "for_signature"->{//跳转到手写签名
                val intent= Intent(activity,AprSignatureActivity::class.java)
                intent.putExtra("action","for_signature")
                activity.startActivityForResult(intent, Codes.SIGN_REQUEST)
            }
            "scan_signature"->{
               val scanUserId= params.getString("userId")
                if (UserHolder.getUserId() != scanUserId) {
                 //提示登录的账号不对
                    val dialog = MyDialog(activity, 309, 171,
                        "", needBtnConfirm = true, needBtnCancel = false, bgResourceId = 0,
                    imageResourceId = R.drawable.ic_error_image)
                    dialog.setContentText("请使用本人担当账号进行扫一扫完成审批")
                    dialog.show()
                    return
                }
                val intent= Intent(activity,AprSignatureActivity::class.java)
                intent.putExtra("action","scan_signature")
                activity.startActivityForResult(intent, Codes.SIGN_REQUEST)
            }
            "upload_signature"->{
                val targetFile=params.getSerializable("signature") as File
                val companyId= params.getString("companyId")?:""
                val uploadFileBean=UploadFileBean()
                uploadFileBean.hash=""
                uploadFileBean.fileName=targetFile.name
                uploadFileBean.fileUrl=targetFile.absolutePath
                uploadFileBean.fileLength=targetFile.length()

                val fileListData= arrayListOf<UploadFileBean>(uploadFileBean)
                //上传手写签名
                UploadFileUtil.uploadFileList(activity,
                    fileListData,
                hashMapOf(), hashMapOf(),companyId,
                    onFinishBack = {result.invoke(GsonUtil.toJson(fileListData.get(0)))},
                    onErrorBack = {result.invoke("0")}
                )
            }
        }
    }

    override fun openPageWithResult1(
        activity: FragmentActivity,
        path: String,
        params: Bundle,
        result: (data: String) -> Unit
    ) {

    }

    override fun service(path: String, params: Bundle, result: (data: String) -> Unit) {
        if (path == "approveFast") {
            val approveId = params.getString("approveId", "")
            val opinion = params.getInt("opinion", -1)
            val submitData = FastProcessApprovalData(approveId = approveId,
                    opinion = opinion,
                    organizationId = params.getString("companyId", "")
            )
            val tag = "快速处理审批"
            showLog("$tag 开始")
            val api = RetrofitClient.single_intance.getRxRetrofit().create(ApprovalApi::class.java)
            RxScheduleUtil.rxSchedulerHelper(api.dealApproveFast(submitData))
                    .compose(ErrorTransformer.getInstance())
                    .subscribe(object : BaseSubscriber<Any>() {
                        override fun onError(ex: ApiException) {
                            result.invoke(ex.message)
                        }

                        override fun onComplete() {

                        }

                        override fun onNext(t: Any?) {
                            if (mContext != null && opinion in 0..1) {
//                                ApprovalUtil().updateAprData(mContext!!, approveId, (opinion + 1) * -1) {
                                result.invoke(GsonUtil.toJson(submitData))
//                                }
                            } else {
                                result.invoke(GsonUtil.toJson(submitData))
                            }
                        }
                    })
        } else if (path == "transFileToImBucket") {
            val fileId = params.getString("fileId", "")
            val fileName = params.getString("fileName", "")
            val typePdf = params.getString("typePdf", "")
            if (fileId.isNullOrBlank() || fileName.isNullOrBlank()) {
                result.invoke("error:approvePdfId is empty")
            } else {
                val tag = "转存文件到im桶中"
                showLog("$tag 开始")

                RxScheduleUtil.rxSchedulerHelper(
                    if(typePdf=="ticket"){
                        RetrofitClient.single_intance.getRxRetrofit().create(TicketApi::class.java)
                            .transFileToImBucket(hashMapOf("fileId" to fileId, "fileName" to fileName))
                    }else{
                        RetrofitClient.single_intance.getRxRetrofit().create(ApprovalApi::class.java)
                            .transFileToImBucket(hashMapOf("fileId" to fileId, "fileName" to fileName))
                    }

                ).compose(ErrorTransformer.getInstance())
                        .subscribe(object : BaseSubscriber<String>() {
                            override fun onError(ex: ApiException) {
                                result.invoke("error:${ex.message}")
                            }

                            override fun onComplete() {

                            }

                            override fun onNext(t: String?) {
                                result.invoke(t ?: "")
                            }
                        })
            }
        } else if (path == "transFileToEmail") {
            val approvePdfId = params.getString("approvePdfId", "")
            val ticketPdfId = params.getString("ticketPdfId", "")
            val tag = "导出审批到邮箱"
           Loggerr.i("工单导出","===ticketPdfId=${ticketPdfId}===")
           Loggerr.i("工单导出","===approvePdfId=${approvePdfId}===")
            RxScheduleUtil.rxSchedulerHelper(
                if(StringUtils.isNotBlankAndEmpty(ticketPdfId)){
                    RetrofitClient.single_intance.getRxRetrofit().create(TicketApi::class.java)
                        .shareApproveToEmail(arrayListOf(ticketPdfId))
                }else{
                    RetrofitClient.single_intance.getRxRetrofit().create(ApprovalApi::class.java)
                        .shareApproveToEmail(arrayListOf(approvePdfId))
                }
            )
                    .compose(ErrorTransformer.getInstance())
                    .subscribe(object : BaseSubscriber<Any>() {
                        override fun onError(ex: ApiException) {
                            result.invoke(ex.message)
                        }

                        override fun onComplete() {

                        }

                        override fun onNext(t: Any?) {
                            result.invoke(t?.let {
                                GsonUtil.toJson(it)
                                "success"
                            } ?: "")
                        }
                    })
        } else if (path == "getExportFileUrl") {
            val approvePdfId = params.getString("approvePdfId", "")
            if (!approvePdfId.isNullOrBlank()) {
                val tag = "获取导出文件下载地址"
                showLog("$tag 开始")
                val api = RetrofitClient.single_intance.getRxRetrofit().create(ApprovalApi::class.java)
                RxScheduleUtil.rxSchedulerHelper(api.getExportFileDownloadUrl(approvePdfId))
                        .compose(ErrorTransformer.getInstance())
                        .subscribe(object : BaseSubscriber<Any>() {
                            override fun onError(ex: ApiException) {
                                result.invoke(ex.message)
                            }

                            override fun onComplete() {

                            }

                            override fun onNext(t: Any?) {
                                result.invoke(t?.let { GsonUtil.toJson(it) } ?: "")
                            }
                        })
            }
        }
    }
}

/**
 * 定义Provider数据，类路径+类名作为 IProviderGroup 中provider的key，
 * 最终存储在Warehouse.providersIndex，方便使用Arouter直接路由调用
 */
interface ApproveService : RouteServiceProvider