package com.joinutech.approval.org

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.TextView
import androidx.lifecycle.ViewModel
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.approval.AprBaseActivity
import com.joinutech.approval.R
import com.joinutech.approval.databinding.ActivitySelectDeptBinding
import com.joinutech.approval.utils.RouteApr
import com.joinutech.common.adapter.MyAdapter
import com.joinutech.common.util.CompanyHolder
import com.joinutech.ddbeslibrary.bean.Branch
import com.joinutech.ddbeslibrary.bean.OrgImportDeptBean
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.XUtil
import com.joinutech.ddbeslibrary.utils.toastShort
import com.trello.rxlifecycle3.LifecycleTransformer

/**
 * @Desc: 选择部门，审批属性跳转使用
 * @PackageName: com.joinutech.approval.org
 * @ClassName: SelectDeptActivity
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2019/8/16 13:39
 */
@Route(path = RouteApr.APR_DEPT_SELECT)
class SelectDeptActivity(
        override val contentViewResId: Int = R.layout.activity_select_dept
) : AprBaseActivity<ActivitySelectDeptBinding>() {

    override fun initImmersion() {
    }

    override fun initView() {
        loadIntentData()
        showToolBarLine()
        binding.tvCompanyName.text = depName
        setShowEmptyView(false)
        initSearch()
        initListView()
        binding.btnSelectDept.setOnClickListener {
            val intent = Intent()
            intent.putExtra("selectDept", currentBranch?.deptName ?: "")
            setResult(Activity.RESULT_OK, intent)
            finish()
        }
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivitySelectDeptBinding {
        return ActivitySelectDeptBinding.inflate(layoutInflater)
    }

    /**搜索筛选*/
    private lateinit var searchBar: View
    private lateinit var editText: EditText
    lateinit var inputClear: View
    private val searchData = arrayListOf<OrgImportDeptBean>()
    lateinit var searchAdapter: MyAdapter<OrgImportDeptBean>
    lateinit var searchList: RecyclerView

    private fun initSearch() {
        searchList = findViewById(R.id.dept_search_list)

        searchBar = findViewById(R.id.search_bar)

        inputClear = searchBar.findViewById<View>(R.id.iv_input_clear)
        inputClear.visibility = View.GONE
        editText = searchBar.findViewById(R.id.et_search_input)

        val cancel = searchBar.findViewById<TextView>(R.id.tv_cancel)
        cancel.text = "取消"
//        cancel.setTextColor(resources.getColor(R.color.colorFF323232))
        cancel.setTextColor(resources.getColor(com.joinutech.ddbeslibrary.R.color.colorFF323232))
        cancel.visibility = View.GONE

        cancel.setOnClickListener {
            try {
                if (currentFocus != null && currentFocus?.windowToken != null) {
                    (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                            .hideSoftInputFromWindow(currentFocus!!
                                    .windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
                }
            } catch (e: java.lang.Exception) {

            }
            editText.clearFocus()
            editText.setText("")
            inputClear.visibility = View.GONE
            cancel.visibility = View.GONE
            showSearch(false)
        }
        editText.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus && cancel.visibility != View.VISIBLE) {
                cancel.visibility = View.VISIBLE
            }
        }

        editText.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {

            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (s != null && s.isNotEmpty()) {
                    inputClear.visibility = View.VISIBLE
                } else {
                    inputClear.visibility = View.GONE
                }
            }

        })

        editText.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH ||
                    (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER)) {
                searchMember(editText.text.toString().trim())
                return@setOnEditorActionListener true
            }
            false
        }

        inputClear.setOnClickListener {
            editText.setText("")
            inputClear.visibility = View.GONE
            searchMember("")
        }

        searchAdapter = MyAdapter(this, R.layout.item_person_dept_layout,
                searchData,
                onBindItem = { _: Int, branch: OrgImportDeptBean, view: View ->
                    val topView = view.findViewById<View>(R.id.tv_index)
                    val ivSelect = view.findViewById<View>(R.id.iv_select)
                    val ivAvatar = view.findViewById<View>(R.id.iv_avatar)
                    val tvName = view.findViewById<TextView>(R.id.tv_name)
                    val tvInfo = view.findViewById<TextView>(R.id.tv_info)
                    val line = view.findViewById<View>(R.id.line)
                    XUtil.hideView(topView, ivSelect, ivAvatar, tvInfo)
                    tvName.text = branch.deptName
                    XUtil.showView(line)
                },
                onItemClick = { _: Int, dept: OrgImportDeptBean, _: View ->
                    val intent = Intent()
                    intent.putExtra("selectDept", dept.deptName)
                    setResult(Activity.RESULT_OK, intent)
                    finish()
                })

        searchList.layoutManager = LinearLayoutManager(this)
        searchList.adapter = searchAdapter
        searchList.visibility = View.GONE
    }

    private fun showSearch(show: Boolean) {
        if (show) {
            searchList.visibility = View.VISIBLE
            binding.svList.visibility = View.GONE
            binding.btnSelectDept.visibility = View.GONE
        } else {
            searchList.visibility = View.GONE
            binding.svList.visibility = View.VISIBLE
            binding.btnSelectDept.visibility = View.VISIBLE
        }
    }

    private fun searchMember(searchName: String) {
        searchData.clear()
        if (StringUtils.isNotBlankAndEmpty(searchName)) {
            for (key in allNames.keys) {
                if (key.contains(searchName)) {
                    searchData.add(allNames[key]!!)
                }
            }
        }
        if (searchData.isEmpty()) {
            toastShort("未找到相关部门")
            showSearch(false)
        } else {
            showSearch(true)
            searchAdapter.notifyDataSetChanged()
        }
    }

    private val depList = arrayListOf<OrgImportDeptBean>()
    lateinit var depAdapter: MyChildAdapter
    private val levelList = arrayListOf<Branch>()
    private lateinit var levelAdapter: MyAdapter<Branch>
    private lateinit var levelView: RecyclerView
    private lateinit var levelLine: View

    private fun initListView() {
        binding.rvSubList.layoutManager = LinearLayoutManager(this)
        depAdapter = MyChildAdapter(mContext!!, depList,
                onBindItem = { _: Int, branch: OrgImportDeptBean, view: View ->
                    val topView = view.findViewById<View>(R.id.tv_index)
                    val ivSelect = view.findViewById<View>(R.id.iv_select)
                    val ivAvatar = view.findViewById<View>(R.id.iv_avatar)
                    val tvName = view.findViewById<TextView>(R.id.tv_name)
                    val tvInfo = view.findViewById<TextView>(R.id.tv_info)
                    val line = view.findViewById<View>(R.id.line)
                    XUtil.hideView(topView, ivSelect, ivAvatar, tvInfo)
                    tvName.text = branch.deptName
                    XUtil.showView(line)
                },
                onItemClick = { i: Int, dept: OrgImportDeptBean, _: View ->
                    //                    depId = depList[i].deptId
                    setPageTitle(dept.deptName)
                    viewLinked.add(dept)
                    dealSuccessResult(dept)//点击部门项
                })

        binding.rvSubList.adapter = depAdapter
        levelLine = binding.levelLayout.root.findViewById(com.joinutech.ddbeslibrary.R.id.line_level_layout)
        levelView = binding.levelLayout.root.findViewById(com.joinutech.ddbeslibrary.R.id.rv_level_list)
        levelView.layoutManager = LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        levelAdapter = MyAdapter(mContext!!, R.layout.item_level_info, levelList,
                onBindItem = { position: Int, branch: Branch, view: View ->
                    val levelName = view.findViewById<TextView>(R.id.tv_level_name)
                    if (position == levelList.lastIndex) {
                        levelName.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.colorFF333333))
                    } else {
                        levelName.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color1E87F0))
                    }
                    if (position == 0) {
                        levelName.text = branch.name
                    } else {
                        levelName.text = " > " + branch.name
                    }
                },
                onItemClick = { position: Int, branch: Branch, _: View ->
                    if (position < levelList.lastIndex) {
                        // 每选中一次，更新一次列表数据，获取对应部门的二级子部门
                        setPageTitle(branch.name)
                        val result = viewLinked.dropLast(viewLinked.size - position - 1)
                        viewLinked.clear()
                        viewLinked.addAll(result)
                        dealSuccessResult(viewLinked.last())//选中level
                    }
                })
        levelView.adapter = levelAdapter
    }

    var companyId = ""
    var depName = ""
    var companyBean: WorkStationBean? = null
    private fun loadIntentData() {
        companyBean = CompanyHolder.getCurrentOrg()
        if (companyBean != null) {
            companyId = companyBean!!.companyId
            setPageTitle(companyBean!!.name)
        }
    }

    var model: DeptViewModel? = null
    override fun initLogic() {
        model = getModel(DeptViewModel::class.java)
        refreshOrgChartData()
    }

    override fun onEmptyRefresh() {
        super.onEmptyRefresh()
        refreshOrgChartData()
    }

    private var viewLinked = arrayListOf<OrgImportDeptBean>()

    private fun refreshOrgChartData() {
        getLoadingDialog("请求团队架构数据", true)
        model?.requestDepData(bindToLifecycle(), accessToken!!, companyId, "0", {
            dismissDialog()
            viewLinked.clear()
            viewLinked.add(it)
            allNames.clear()
            allNames.putAll(initCaches(it))
            dealSuccessResult(it)//第一次初始化
        }, {
            setShowEmptyView(true)
            dismissDialog()
        })
    }

    var currentBranch: OrgImportDeptBean? = null
    private fun dealSuccessResult(result: OrgImportDeptBean) {
        //根据是否是1及部门显示面包屑导航的逻辑
        currentBranch = result
        if (result.deptId == "0") {
            levelView.visibility = View.GONE
            levelLine.visibility = View.VISIBLE
        } else {
            levelView.visibility = View.VISIBLE
            levelLine.visibility = View.GONE
        }
        binding.tvCompanyName.text = result.deptName
//        //处理层级显示逻辑
//        moreDepLevelShow(result)
        updateLevel()
//        //处理接口返回的部门列表数据处理
        dealDepDataShow(result)
////        //处理接口返回的部门人员列表数据处理
//        dealDepMemberShow(result)
    }

    private fun updateLevel() {
        if (viewLinked.size > 1) {
            if (levelView.visibility != View.VISIBLE) {
                levelView.visibility = View.VISIBLE
            }
            levelList.clear()
            levelList.addAll(viewLinked.map { Branch(deptId = it.deptId, name = it.deptName) }.toList())
            levelAdapter.notifyDataSetChanged()
        } else {
            levelView.visibility = View.GONE
        }
        binding.tvDeptLevel.text = "部门层级：${viewLinked.last().deptLevel}级部门"
    }

    private fun initCaches(rootDept: OrgImportDeptBean): HashMap<String, OrgImportDeptBean> {
        val hashmap = hashMapOf<String, OrgImportDeptBean>()
        hashmap[rootDept.deptName] = rootDept
        if (!rootDept.deptList.isNullOrEmpty()) {
            for (dep in rootDept.deptList) {
                hashmap.putAll(initCaches(dep))
            }
        }
        return hashmap
    }

    private val allNames = hashMapOf<String, OrgImportDeptBean>()

    private fun dealDepDataShow(result: OrgImportDeptBean) {
        depList.clear()
        depList.addAll(result.deptList)

        if (depList.isNotEmpty()) {
            binding.tvSubDept.visibility = View.VISIBLE
        } else {
            binding.tvSubDept.visibility = View.GONE
        }
        depAdapter.notifyDataSetChanged()
    }

    override fun onNoDoubleClick(v: View) {
        super.onNoDoubleClick(v)
        when (v.id) {
            com.joinutech.ddbeslibrary.R.id.iv_left -> {
                onBackPressed()
            }
        }
    }

    override fun onBackPressed() {
        if (viewLinked.size > 1) {
            viewLinked.removeAt(viewLinked.lastIndex)
            dealSuccessResult(viewLinked.last())//后退键
        } else {
            super.onBackPressed()
        }
    }
}

class MyChildAdapter(context: Context,
                     data: ArrayList<OrgImportDeptBean>,
                     onBindItem: (position: Int, data: OrgImportDeptBean, view: View) -> Unit,
                     onItemClick: (position: Int, data: OrgImportDeptBean, view: View) -> Unit)
    : MyAdapter<OrgImportDeptBean>(
        context,
        R.layout.item_person_dept_layout,
        data,
        onBindItem,
        onItemClick
)

class DeptViewModel : ViewModel() {

    fun requestDepData(life: LifecycleTransformer<Result<OrgImportDeptBean>>,
                       token: String,
                       companyId: String, deptId: String = "",
                       onSuccess: (OrgImportDeptBean) -> Unit,
                       onError: (String) -> Unit) {

        AddressbookService.queryOrgDept(companyId, deptId)
                .compose(life)
                .compose(ErrorTransformer.getInstance<OrgImportDeptBean>())
                .subscribe(object : BaseSubscriber<OrgImportDeptBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(t: OrgImportDeptBean?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}