package com.joinutech.approval.org

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.text.Editable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.google.gson.reflect.TypeToken
import com.joinutech.approval.AprBaseActivity
import com.joinutech.approval.R
import com.joinutech.approval.data.SelectData
import com.joinutech.approval.databinding.ActivitySelectPersonBinding
import com.joinutech.approval.utils.RouteApr
import com.joinutech.approval.viewModel.CooperationCompanyViewModel
import com.joinutech.common.adapter.MyAdapter
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.wavesidebar.PinnedHeaderDecoration

/**
 * @PackageName: com.joinutech.approval.org
 * @ClassName: SelectPersonActivity
 * @Desc: 审批人员选择页面 支持团队内和外部协作人 支持单选和多选
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2019/8/16 11:48
 */
@Route(path = RouteApr.APR_SELECT_PERSON)
class SelectPersonActivity(override val contentViewResId: Int = R.layout.activity_select_person) :
    AprBaseActivity<ActivitySelectPersonBinding>() {

    /**显示数据*/
    private lateinit var memberList: MutableList<SearchMemberBean>
    private lateinit var mAdapter: MyAdapter<SearchMemberBean>
    private var companyId: String = ""

    /**
     * 单选操作类型
     *  0 默认选择后返回
     * >0 其他时需要判断
     * */
    private var selectType: Int = 0

    /**
     * 选择人员类型
     * 0 默认选择：团队内人员
     * 1 外部协作人
     */
    private var personType: Int = 0

    private var beforeSelectUserIds: String = ""
    private var maxSelect: Int = -1 // 最大选择数量，-1 不限制
    private var unSelectPerson: ArrayList<String>? = null // 过滤人员的id数组
    private val selectMembers = mutableMapOf<String, SearchMemberBean>()
    private var oldSelectIndex = 0

    private lateinit var rvList: RecyclerView
    private lateinit var llSelectAll: View
    private lateinit var ivSelectList: View

    /**搜索筛选*/
    private lateinit var searchBar: View
    private lateinit var editText: EditText
    lateinit var inputClear: View
    private lateinit var cooperationViewModel: CooperationCompanyViewModel

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back2)
    }

    private lateinit var noResultLayout: PageEmptyView

    override fun initView() {
        showToolBarLine()
        whiteStatusBarBlackFont()
        val bundle = intent.extras
        if (bundle != null) {
            val title = bundle.getString("title", "选择人员")
            setPageTitle(title)
            companyId = bundle.getString("companyId", "")
            personType = bundle.getInt("personType", 0)
            selectType = bundle.getInt("selectType", 0)
            unSelectPerson = bundle.getStringArrayList("unSelectPerson")
            maxSelect = bundle.getInt("maxSelect", -1)

            beforeSelectUserIds = bundle.getString("selectUserIdListString", "")
        }

        initSearch()

        rvList = findViewById(R.id.rv_list)
        llSelectAll = findViewById(R.id.ll_select_all)
        ivSelectList = findViewById(R.id.iv_select_list)

        if (!isSingleSelect()) {
            XUtil.showView(llSelectAll)
            llSelectAll.setOnClickListener(this)
            setRightTitle("确定", View.OnClickListener {
                val intent = Intent()
                val result = arrayListOf<SearchMemberBean>()
                for (item in selectMembers.entries) {
                    result.add(item.value)
                }
                intent.putExtra("members", GsonUtil.toJson(result))
                setResult(Activity.RESULT_OK, intent)
                finish()
            })
        } else {
            XUtil.hideView(llSelectAll)
        }

        val decoration = PinnedHeaderDecoration()
        decoration.registerTypePinnedHeader(1) { _, _ -> true }
        rvList.addItemDecoration(decoration)
        cooperationViewModel = getModel(CooperationCompanyViewModel::class.java)

        noResultLayout = findViewById(R.id.layout_empty_layout)
        noResultLayout.hide()
    }

    private fun isSingleSelect(): Boolean = maxSelect == 1

    private fun initSearch() {
        searchBar = findViewById(R.id.search_bar)

        editText = searchBar.findViewById(R.id.et_search_input)
        inputClear = searchBar.findViewById<View>(R.id.iv_input_clear)
        inputClear.visibility = View.GONE
        searchBar.findViewById<View>(R.id.tv_cancel).visibility = View.GONE

        editText.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {

            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (s != null && s.isNotEmpty()) {
                    inputClear.visibility = View.VISIBLE
                } else {
                    inputClear.visibility = View.GONE
                }
            }

        })

        editText.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH ||
                (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER)
            ) {
                searchMember(editText.text.toString().trim())
                return@setOnEditorActionListener true
            }
            false
        }

        inputClear.setOnClickListener {
            editText.setText("")
            try {
                if (currentFocus != null && currentFocus?.windowToken != null)
                    (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                        .hideSoftInputFromWindow(
                            currentFocus!!
                                .windowToken, InputMethodManager.HIDE_NOT_ALWAYS
                        )
            } catch (e: java.lang.Exception) {

            }
            inputClear.visibility = View.GONE
            reset()
        }
    }

    private fun reset() {
        refreshFriendList()
    }

    private fun searchMember(keyWord: String) {
        refreshFriendList(keyWord)
    }

    override fun initLogic() {
        initViewShow()
        getObserve()
        refreshFriendList()
    }

    private fun getObserve() {
        cooperationViewModel.searchDepMemberResult.observe(this, Observer {
            hideLoading()
            if (!it.isNullOrEmpty()) {
                noResultLayout.visibility = View.GONE
                binding.clDataLayout.visibility = View.VISIBLE
                /**处理公司成员数据，转换为好友数据后显示到页面*/
                memberList.clear()
                memberList.addAll(it)
                if (!StringUtils.isEmpty(beforeSelectUserIds)&&!isSingleSelect()) {
                    val beforeSelectIdList = GsonUtil.fromJson2<List<String>>(beforeSelectUserIds,
                        object : TypeToken<List<String>>() {}.type
                    )
                    memberList.forEach {
                        if (beforeSelectIdList?.contains(it.userId) ?: false) {
                            it.select = true
                            onSelect(it)
                        }
                    }
                }
                mAdapter.notifyDataSetChanged()
            } else {
                binding.clDataLayout.visibility = View.GONE
                noResultLayout.visibility = View.VISIBLE
            }
        })
        cooperationViewModel.searchDepMemberError.observe(this, Observer {
            hideLoading()
            ToastUtil.show(mContext!!, it)
        })
        cooperationViewModel.searchDepExternalMemberResult.observe(this, Observer {
            hideLoading()
            if (!it.isNullOrEmpty()) {
                noResultLayout.visibility = View.GONE
                binding.clDataLayout.visibility = View.VISIBLE
                /**处理公司成员数据，转换为好友数据后显示到页面*/
                memberList.clear()
                it.forEach { item ->
                    run {
                        val bean = SearchMemberBean(
                            headimg = item.avatar,
                            name = item.name, userId = item.userId, isOuter = 1,
                            initial = item.initial, positionName = when (item.type.toString()) {
                                "1" -> "客户"
                                "2" -> "渠道商"
                                "3" -> "供应商"
                                "4" -> "合作伙伴"
                                "5" -> "其他类型"
                                else -> "客户"
                            }
                        )
                        memberList.add(bean)
                    }
                }
                mAdapter.notifyDataSetChanged()
            } else {
                binding.clDataLayout.visibility = View.GONE
                noResultLayout.visibility = View.VISIBLE
            }
        })
        cooperationViewModel.searchDepExternalMemberError.observe(this, Observer {
            hideLoading()
            ToastUtil.show(mContext!!, it)
        })
    }

    /**选中所有用户*/
    private fun selectAll() {
        if (memberList.isNotEmpty()) {
            for (member in memberList) {
                if (checkUser(member.userId)) {
                    member.select = !ivSelectList.isSelected
                    if (member.select) {
                        selectMembers[member.userId] = member
                    } else if (selectMembers.containsKey(member.userId)) {
                        selectMembers.remove(member.userId)
                    }
                }
            }
        }
        ivSelectList.isSelected = selectMembers.size == memberList.size
        mAdapter.notifyDataSetChanged()
    }

    private fun onSelect(member: SearchMemberBean) {
        if (member.select) {
            selectMembers[member.userId] = member
        } else {
            if (selectMembers.containsKey(member.userId)) {
                selectMembers.remove(member.userId)
            }
        }
        ivSelectList.isSelected = selectMembers.size == memberList.size
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.llSelectAll -> {
                selectAll()//!ivSelectList.isSelected
            }
        }
    }

    private fun initViewShow() {
        memberList = ArrayList()
        mAdapter = MyAdapter(mContext!!, R.layout.item_person_dept_layout, memberList,
            onBindItem = { position: Int, data: SearchMemberBean, view: View ->
                val tvIndex = view.findViewById<TextView>(R.id.tv_index)
                val ivSelect = view.findViewById<View>(R.id.iv_select)
                val ivAvatar = view.findViewById<ImageView>(R.id.iv_avatar)
                val tvName = view.findViewById<TextView>(R.id.tv_name)
                val tvInfo = view.findViewById<TextView>(R.id.tv_info)
                val line = view.findViewById<View>(R.id.line)
                if (isSingleSelect()) {
                    ivSelect.visibility = View.GONE
                } else {
                    ivSelect.visibility = View.VISIBLE
                    ivSelect.isSelected = data.select
                }

                ImageLoaderUtils.loadImage(mContext!!, ivAvatar, data.headimg)
                tvName.text = data.name
                tvInfo.text = data.positionName

                line.visibility = View.GONE

                if (position == 0 || memberList[position - 1].initial != data.initial) {
                    tvIndex.visibility = View.VISIBLE
                    tvIndex.text = data.initial
                    if (data.isCurrentIndex) {
//                        tvIndex.setTextColor(CommonUtils.getColor(mContext!!, R.color.color1E87F0))
                        tvIndex.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color1E87F0))
                    } else {
                        tvIndex.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.colorBDBDBD))
                    }
                } else {
                    tvIndex.visibility = View.GONE
                }
            },
            onItemClick = { _: Int, member: SearchMemberBean, view: View ->
                if (maxSelect == -1) {//加班人员，离职交接人员，用印人
                    if (checkUser(member.userId)) {
                        if (!isSingleSelect()) {
                            val ivSelect = view.findViewById<View>(R.id.iv_select)
                            member.select = !member.select
                            ivSelect.isSelected = member.select
                        }
                        onClick(member)
                    } else {
                        toastShort("该用户不可选择")
                    }
                } else {
                    if (maxSelect > 0 && selectMembers.size < maxSelect) {
                        if (checkUser(member.userId)) {
                            if (!isSingleSelect()) {
                                val ivSelect = view.findViewById<View>(R.id.iv_select)
                                member.select = !member.select
                                ivSelect.isSelected = member.select
                            }
                            onClick(member)
                        } else {
                            toastShort("该用户不可选择")
                        }
                    } else {
                        toastShort("超过本次可加审人员${maxSelect}人限制")
                    }
                }
            })
        // RecyclerView设置相关
        rvList.layoutManager = LinearLayoutManager(this)
        rvList.adapter = mAdapter
        // 侧边设置相关
        binding.mainSideBar.setOnSelectIndexItemListener { letter ->
            // TODO: 2020/7/23 8:59 优化后需要验证
            if (memberList.isNotEmpty()) {
                val first = memberList.find { it.initial == letter }
                if (first != null) {
                    first.select = true
                    memberList[oldSelectIndex].select = false
                    oldSelectIndex = memberList.indexOf(first)
                    mAdapter.notifyDataSetChanged()
                    showLog("切换联系人索引后 更新 联系人列表----")
                    (rvList.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
                        oldSelectIndex,
                        0
                    )
                }
            }
//            if (memberList.isNotEmpty()) {
//                for (i in 0 until memberList.size) {
//                    memberList[i].isCurrentIndex = false
//                    if (memberList[i].initial == letter) {
//                        oldSelectIndex = i
//                        memberList[i].isCurrentIndex = true
//                        mAdapter.notifyDataSetChanged()
//                        (rvList.layoutManager as LinearLayoutManager)
//                                .scrollToPosition(oldSelectIndex)
//                    }
//                }
//            }
        }

//        rvList.addOnScrollListener(object : RecyclerView.OnScrollListener() {
//            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
//                super.onScrollStateChanged(recyclerView, newState)
//                val layoutManager = recyclerView.layoutManager
//                //判断是当前layoutManager是否为LinearLayoutManager
//                // 只有LinearLayoutManager才有查找第一个和最后一个可见view位置的方法
//                if (layoutManager is LinearLayoutManager) {
//                    //获取第一个可见view的位置
//                    if (memberList.isNotEmpty()) {
//                        val firstItemPosition = layoutManager.findFirstVisibleItemPosition()
//                        memberList[oldSelectIndex].isCurrentIndex = false
//                        memberList[firstItemPosition].isCurrentIndex = true
//
//                        oldSelectIndex = firstItemPosition
//                        mAdapter.notifyDataSetChanged()
//                    }
//                }
//            }
//        })

    }

    /**检测用户是否可以选择*/
    private fun checkUser(userId: String): Boolean {
        if (unSelectPerson != null && unSelectPerson!!.isNotEmpty()) {
            if (userId in unSelectPerson!!) {
                return false
            }
        }
        return true
    }

    private fun onClick(member: SearchMemberBean) {
//        if (personType != 0) {
//            if (isSingleSelect()) {
//                showTip(member)
//            } else {
//                //带有外部联系人的抄送人员返回
//                onSelect(member)
//            }
//        } else {
        if (isSingleSelect()) {
            val intent = Intent()
            intent.putExtra("members", GsonUtil.toJson(arrayListOf(member)))
            setResult(Activity.RESULT_OK, intent)
            finish()
        } else {
            onSelect(member)
        }
//        }
    }

    private fun showTip(member: SearchMemberBean) {
        val content = "你确认要邀请用户 ${member.name} 进入审批节点进行审批操作吗？"
        val startIndex = content.indexOf(member.name)
        val endIndex = startIndex + member.name.length
        val spannableString = SpannableString(content)
        val colorSpan = ForegroundColorSpan(Color.parseColor("#1E87F0"))
        spannableString.setSpan(colorSpan, startIndex, endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        val helper = object : CenterDialogHelper(
            this,
            onConfirm = {
                //                    if (selectType == 805) {
                member.isOuter = personType
//                    } else {
//                        member.isOuter = 0
//                    }
                // TODO: 2020/3/18 8:52 不再事件回调人员信息
//                    EventBusUtils.sendEvent(EventBusEvent(
//                            MsgType.APR_ADD_PERSON.name, data = arrayListOf(member)))
                val intent = Intent()
                intent.putExtra("members", GsonUtil.toJson(arrayListOf(member)))
                setResult(Activity.RESULT_OK, intent)
                finish()
            }, onCancel = {

            }
        ) {
            override fun bindView(dialogView: View) {
                super.bindView(dialogView)
                dialogView.findViewById<TextView>(R.id.tv_content).text = spannableString
                dialogView.findViewById<View>(R.id.tv_hint).visibility = View.GONE
            }
        }

        helper.initView()
        helper.onConfig(DialogConfig(useDefault = true))
        helper.show()
    }

    private fun refreshFriendList(keyWord: String = "") {
        getLoadingDialog("加载中", false)
        if (personType == 0) {
            cooperationViewModel.searchDepMember(bindToLifecycle(), companyId, keyWord)
        } else {
            //外部联系人的列表
            cooperationViewModel.searchDepExternalMember(bindToLifecycle(), companyId)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        (memberList as ArrayList).clear()
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivitySelectPersonBinding {
        return ActivitySelectPersonBinding.inflate(layoutInflater)
    }

}