package com.joinutech.approval.properties

import android.app.Activity
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.Priority
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.joinutech.approval.R
import com.joinutech.common.storage.FileUtil

/**
 * @className: PictureSelector
 * @desc: 图片选择器 // TODO: 实现图片选择和拍照的封装
 * @author: zyy
 * @date: 2019/8/7 9:40
 * @company: joinUTech
 * @leader: ke
 */
@Deprecated("no used")
class PictureSelectorProperty(private val mActivity: Activity,
                              private val limit: Int = 9,
                              private val repeatable: Boolean = true) {

    private fun loadRoundImage(activity: Activity, data: String, target: ImageView, cornerRadius: Int = 5) {
        Glide.with(activity).load(data).apply(getRoundOptions(cornerRadius)).into(target)
    }

    fun loadCircleImage(activity: Activity, data: String, target: ImageView) {
        Glide.with(activity).load(data).apply(getCircleOptions()).into(target)
    }

    private fun getRoundOptions(cornerRadius: Int = 10): RequestOptions {
        return RequestOptions.bitmapTransform(RoundedCorners(cornerRadius))
                .placeholder(R.drawable.bg_image_default)
                .error(R.drawable.bg_image_default)
                //下载的优先级
                .priority(Priority.NORMAL)
                //缓存策略
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .centerCrop()
                .dontAnimate()
    }

    private fun getCircleOptions(): RequestOptions {
        return RequestOptions.circleCropTransform()
                .error(R.drawable.bg_image_default)
                .placeholder(R.drawable.bg_image_default)
                //下载的优先级
                .priority(Priority.NORMAL)
                //缓存策略
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .centerCrop()
                .dontAnimate()
    }

    private fun getCommonOptions(): RequestOptions {
        return RequestOptions().error(R.drawable.bg_image_default)
                .placeholder(R.drawable.bg_image_default)
                //下载的优先级
                .priority(Priority.NORMAL)
                //缓存策略
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .centerCrop()
                .dontAnimate()
    }

    /**
     * 自定义压缩存储地址
     * @return
     */
    private fun getPath(): String {
//        val path = Environment.getExternalStorageDirectory().path + "/upPics"
//        val file = File(path)
//        return if (file.mkdirs()) {
//            path
//        } else path
        return FileUtil.getFilePath(mActivity, "upPics")
    }

//    private fun setData(subData: List<LocalMedia>) {
//        subData.forEach { media ->
//            if (media.isCompressed) {
//                val picPathFile = File(media.compressPath)
//                Uri.fromFile(picPathFile).path!!
//            } else {
//                media.path
//            }
//        }
//    }

}