package com.joinutech.approval.api

import com.joinutech.approval.data.*
import com.joinutech.common.base.tokenKey
import com.joinutech.ddbeslibrary.bean.AprCheckLeaveLaunchBean
import com.joinutech.ddbeslibrary.request.Result
import io.reactivex.Flowable
import okhttp3.RequestBody
import retrofit2.http.*

/**
 * @className: ApprovalApi
 * @desc: 审批接口请求
 * @author: zyy
 * @date: 2019/8/16 10:22
 * @company: joinUTech
 * @leader: ke
 */
interface ApprovalApi {
    /**-------------------------------------------审批模板管理-----------------------------------------------**/

    /**获取审批模板设置列表 modelType : 1 预置模板，2 自定义模板*/
//    @GET("workflow/model/modelSet/{organizationId}/{modelType}")
    @GET("workflow/group/mobileModel/{organizationId}/{modelType}")
    fun getModelList(
            @Path("organizationId") organizationId: String,
            @Path("modelType") modelType: Int
    ): Flowable<Result<List<ModelGroup>>>

    @GET("workflow/group/mobileModel/v2/{organizationId}/{modelType}") // 审批套件
    fun getModelListV2(
            @Path("organizationId") organizationId: String,
            @Path("modelType") modelType: Int
    ): Flowable<Result<List<ModelGroup>>>

    /**审批模板设置 切换模板状态接口 启用停用审批模板 operateType : 1 启用，2 停用*/
    @GET("workflow/model/operateModel/{modelId}/{operateType}")
    fun changeModel(
            @Path("modelId") modelId: String,
            @Path("operateType") operateType: Int
    ): Flowable<Result<Any>>

    /**审批模板设置 通知全员*/
    @GET("workflow/model/notifyEveryone/{modelId}")
    fun modelNotify(
            @Path("modelId") modelId: String
    ): Flowable<Result<Any>>

    /**-------------------------------------------审批功能使用-----------------------------------------------**/
    /**获取公司 发起审批功能 列表*/
    @GET("workflow/model/modelHomePage/{organizationId}")
    fun getAprList(@Header(tokenKey) token: String, @Path("organizationId") companyId: String): Flowable<Result<AprList>>

    /**
     * 获取公司 发起审批功能 列表 // TODO: 2021/4/13 10:32 套件版更新
     */
    @GET("workflow/model/modelHomePage/v2/{organizationId}")
    fun getAprListV2(@Path("organizationId") companyId: String): Flowable<Result<AprList>>

    /**获取审批是否能推送*/
    @GET("workflow/group/pushStatus/{organizationId}")
    fun getPushStatus(@Path("organizationId") organizationId: String): Flowable<Result<Int>>

    /**修改审批推送状态*/
    @PUT("workflow/group/pushStatus/{organizationId}/{type}")
    fun putPushStatus(@Path("organizationId") organizationId: String, @Path("type") type: Int): Flowable<Result<Boolean>>

    /**-------------------------------------------审批创建-----------------------------------------------**/

    /**隐私等开关设置*/
    @GET("workflow/approve/approveRemindSetInfo/{userId}/{companyId}")
    fun getAprSettings(@Path("userId") userId: String, @Path("companyId") companyId: String): Flowable<Result<ApproveAlertSetting>>

    /**隐私等开关设置*/
    @POST("workflow/approve/approveRemindSet")
    fun setAprSettings(@Body data: ApproveAlertSetting): Flowable<Result<Any>>

    /**获取审批模板详情，获取属性信息*/
    @GET("workflow/model/normalModelInfo/{modelId}")
    fun getModelInfo(@Path("modelId") modelId: String): Flowable<Result<ModelDetail>>

    /**获取审批模板详情，获取属性信息 套件相关 isFuture*/
    @GET("workflow/model/normalModelInfo/v2/{modelId}")
    fun getModelInfoV2(@Path("modelId") modelId: String): Flowable<Result<ModelDetail>>

    /**获取审批模板控件的关联数据，列表数据，单选多选*/
    @GET("workflow/model/chooseFrame/{widget}")
    fun getWidgetData(@Path("widget") widget: String): Flowable<Result<List<SelectFrameData>>>

    /**获取审批提交人的姓名，职位，部门接口*/
    @GET("org/company/manager/deptAndManager/{companyId}/{userId}")
    fun getOrgInfo(@Path("companyId") companyId: String, @Path("userId") userId: String): Flowable<Result<UserOrgInfo>>

    /**获取审批ID*/
    @GET("workflow/approve/approveId")
    fun getAprId(): Flowable<Result<String>>

    /**获取审批ID*/
    @GET("workflow/approve/approveId/{count}")
    fun getAprId3(@Path("count") count: Int): Flowable<Result<List<String>>>

//    /**检测发起审批的人是否离职*/
//    @POST("workflow/approve/checkLeave")
//    fun checkAprUser(@Body data: Any): Flowable<Result<String>>

    //检查离职人员
    @POST("workflow/approve/checkLeave")
    fun checkLeave(@Body data: Any): Flowable<Result<AprCheckLeaveLaunchBean>>

    /**创建审批*/
    @POST("workflow/approve/v2")
    fun createApproveV2(@Body createApproval: CreateAprData): Flowable<Result<Any>>

    /**创建审批*/
    @POST("workflow/approve/v3")
    fun createApproveV3(@Body createApproval: CreateAprData): Flowable<Result<Any>>

    /**创建审批*/
    /*  @POST("workflow/approve/v4")
      fun createApprove(@Body createApproval: CreateAprData): Flowable<Result<Any>>*/

    @POST("workflow/approve/v5")//tcp已修改
    fun createApproveV5(@Body createApproval: CreateAprData): Flowable<Result<Any>>

    // todo 审批考勤关联 审批套件相关变更时增加
    @POST("workflow/approve/v6")//tcp已修改
    fun createApproveV6(@Body createApproval: CreateAprData): Flowable<Result<Any>>

    //校验限时提交审批
    @POST("workflow/approve/verity/widget")
    fun checkIsLimit(@Body limitData: LimitData): Flowable<Result<Any>>
    /**-------------------------------------------审批处理-----------------------------------------------**/
    /**获取个人审批列表*/
    @POST("workflow/approve/approveList")
    fun getMyAprList(@Body getMyAprList: Any): Flowable<Result<GetMyAprListResult>>

    /**筛选条件*/
    @GET("workflow/approve/modelList/{organizationId}")
    fun getAprFilter(@Path("organizationId") organizationId: String): Flowable<Result<List<ModelFilterData>>>

//    #define APPROVAL_SCREENING_LIST @"workflow/approve/modelList/" //

//    #define APPROVAL_GROUP_PUSH @"workflow/group/pushStatus/" // 获取审批是否能推送-修改审批推送状态{organizationId}/{type}
//    #define APPROVAL_GET_DEPT_POSITION @"organization/company/manager/deptAndManager/" //根据公司id查询个人 所在部门职位
//    #define COMPANY_MEMBER_LIST @"organization/personnel/schedule/member/sort/" //公司成员列表


    /**获取审批详情（从列表选择） type:1.我发起的；2.我审批的，3.抄送我的*/
    @GET("workflow/approve/approveInfo/v2/{approveId}/{type}")
    fun getAprDetailByType(@Path("approveId") approveId: String, @Path("type") type: Int): Flowable<Result<ApprovalDetailData>>

    /**撤回审批*/
    /*  @PUT("workflow/approve/recall/{approveId}")
      fun recallApr(@Path("approveId") approveId: String): Flowable<Result<Any>>*/
    @PUT("workflow/approve/recall/v2/{approveId}")//tcp已修改
    fun recallApr(@Path("approveId") approveId: String): Flowable<Result<Any>>

    /**催办审批*/
    /*@GET("workflow/approve/remindDeal/v2/{approveId}")
    fun remindApr(@Path("approveId") approveId: String): Flowable<Result<Boolean>>*/

    @GET("workflow/approve/remindDeal/v4/{approveId}/{approveAssigneeId}")//tcp已修改
    fun remindApr(@Path("approveId") approveId: String, @Path("approveAssigneeId") approveAssigneeId: String): Flowable<Result<Boolean>>

    /**加审审批*/
    @PUT("workflow/approve/v2/retrial")
    fun addAprNode2(@Body approveAssignee: AprAddNodeData2): Flowable<Result<Any>>

    /**加审审批*/
    /*@PUT("workflow/approve/v3/retrial")
    fun addAprNode3(@Body approveAssignee: AprAddNodeData3): Flowable<Result<Any>>*/

    @PUT("workflow/approve/v4/retrial")//tcp已修改
    fun addAprNode3(@Body approveAssignee: AprAddNodeData3): Flowable<Result<Any>>

    /**处理审批*/
    @PUT("workflow/approve")
    fun dealApprove(@Body dealApproval: DealApprovalData): Flowable<Result<Any>>

    /**审批消息列表快速处理审批*/
    /*@POST("workflow/approve/fast")
    fun dealApproveFast(@Body dealApproval: FastProcessApprovalData): Flowable<Result<Any>>*/
    @POST("workflow/approve/fast/v2")//tcp已修改
    fun dealApproveFast(@Body dealApproval: FastProcessApprovalData): Flowable<Result<Any>>

    /**重新提交 审批检查*/
    @GET("workflow/approve/verifyApprove/{approveId}")
    fun reCreateApproveCheck(@Path("approveId") approveId: String): Flowable<Result<Boolean>>

    /**评论审批*//*
    @POST("workflow/approve/comment/v2")
    fun createAprComment(@Body comment: AprCommentData): Flowable<Result<Any>>*/
    /**评论审批*/
    @POST("workflow/approve/comment/v4")//tcp已修改
    fun createAprComment(@Body comment: AprCommentData): Flowable<Result<Any>>

    /**评论审批*/
    @POST("workflow/approve/comment/v4")//tcp已修改
    fun createAprCommentV4(@Body comment: AprCommentData): Flowable<Result<Any>>

    /**评论列表*/
    @GET("workflow/approve/comment/v2/{approveId}/{start}/{length}")
    fun getAprCommentList(@Path("approveId") approveId: String,
                          @Path("start") page: Int = 0,
                          @Path("length") length: Int = 100)
            : Flowable<Result<List<CommentListData>>>

    /**获取待审批数量**/
    @GET("workflow/approve/waitDealApproveCount/{organizationId}")
    fun waitDealApproveCount(@Path("organizationId") organizationId: String)
            : Flowable<Result<Long>>

    /**校验固定流程是否存在审批人离职情况*/
    @GET("workflow/approve/checkLeave/v2/{modelId}/{organizationId}")
    fun checkFixProcessPersonLeave(
            @Path("modelId") modelId: String,
            @Path("organizationId") organizationId: String)
            : Flowable<Result<Boolean>>
    /**-------------------------------------------审批功能使用-----------------------------------------------**/

    /**导出审批后发送到邮箱*/
    @POST("workflow/approve/pdf/mail")
    fun shareApproveToEmail(@Body emails: List<String>): Flowable<Result<Any>>

    /**转移导出文件到im桶中*/
    @POST("workflow/approve/pdf/im")
    fun transFileToImBucket(@Body data: HashMap<String, String>): Flowable<Result<String>>

    //扫码签名后通知服务器
//    @POST("workflow/approve/signature/qrcode")
//    fun sendSignatureId(@Body requestBody: RequestBody): Flowable<Result<Boolean>>

    //扫码签名后通知服务器
//    @POST("workflow/approve/signature/qrcode")
    @POST("approve/instance/signature/file")
    fun sendSignatureId(@Body requestBody: RequestBody): Flowable<Result<Any>>

    /**获取导出文件下载地址*/
    @GET("workflow/approve/pdf/download/{approvePdfId} ")
    fun getExportFileDownloadUrl(@Path("approvePdfId") approvePdfId: String): Flowable<Result<Any>>



}