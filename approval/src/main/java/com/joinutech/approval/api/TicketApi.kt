package com.joinutech.approval.api

import com.joinutech.approval.data.*
import com.joinutech.common.base.tokenKey
import com.joinutech.ddbeslibrary.bean.AprCheckLeaveLaunchBean
import com.joinutech.ddbeslibrary.request.Result
import io.reactivex.Flowable
import retrofit2.http.*

/**
 * @className: ApprovalApi
 * @desc: 审批接口请求
 * @author: zyy
 * @date: 2019/8/16 10:22
 * @company: joinUTech
 * @leader: ke
 */
interface TicketApi {
    /**-------------------------------------------工单管理-----------------------------------------------**/
    /**导出审批后发送到邮箱*/
    @POST("ticket/wiporder/pdf/mail")
    fun shareApproveToEmail(@Body emails: List<String>): Flowable<Result<Any>>

    /**转移导出文件到im桶中*/
    @POST("ticket/wiporder/pdf/im")
    fun transFileToImBucket(@Body data: HashMap<String, String>): Flowable<Result<String>>



}