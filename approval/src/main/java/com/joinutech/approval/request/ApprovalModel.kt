package com.joinutech.approval.request

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.joinutech.approval.api.ApprovalApi
import com.joinutech.approval.data.*
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.bean.CapacityBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.RetrofitClient
import com.joinutech.ddbeslibrary.request.RxScheduleUtil
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.request.exception.NoDataTransformer
import com.joinutech.ddbeslibrary.service.TaskService
import com.marktoo.lib.cachedweb.LogUtil
import com.trello.rxlifecycle3.LifecycleTransformer

/**
 * @className: ApprovalModel
 * @desc: 审批网络请求
 * @author: zyy
 * @date: 2019/8/16 10:44
 * @company: joinUTech
 * @leader: ke
 */
@Deprecated("no used")
class ApprovalModel : ViewModel() {

    val errorResult: MutableLiveData<ApiException> = MutableLiveData()

    private val aprApi = RetrofitClient.single_intance.getRxRetrofit().create(ApprovalApi::class.java)

    /**----------------模板设置相关---------------**/
    val modelListResult = MutableLiveData<List<ModelGroup>>()

//    /**审批设置模板列表*/
//    fun getModelList(orgId: String, type: Int = 1) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.getModelList(orgId, type)
//        ).compose(ErrorTransformer.getInstance())
//                .subscribe(object : BaseSubscriber<List<ModelGroup>>() {
//                    override fun onError(ex: ApiException) {
//                        errorResult.value = ex
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(modelDetail: List<ModelGroup>?) {
//                        modelListResult.value = modelDetail
//                    }
//
//                })
//    }

    val changeModelError = MutableLiveData<ApiException>()
    val changeModelResult = MutableLiveData<Any>()

//    /**启用停用*/
//    fun changeModel(modelId: String, type: Int) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.changeModel(modelId, type)
//        )
//                .compose(NoDataTransformer).subscribe(object : BaseSubscriber<Any>() {
//                    override fun onError(ex: ApiException) {
//                        changeModelError.value = ex
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(result: Any?) {
//                        changeModelResult.value = result
//                    }
//
//                })
//    }

    /**通知全员*/
    val modelNotifyError = MutableLiveData<ApiException>()
    val modelNotifyResult = MutableLiveData<Any>()
//    fun modelNotify(modelId: String) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.modelNotify(modelId)
//        )
//                .compose(NoDataTransformer).subscribe(object : BaseSubscriber<Any>() {
//                    override fun onError(ex: ApiException) {
//                        modelNotifyError.value = ex
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(result: Any?) {
//                        modelNotifyResult.value = result
//                    }
//
//                })
//    }
    /**----------------模板设置相关---------------**/

    /**----------------审批首页列表---------------**/
    val aprListResult = MutableLiveData<AprList>()

//    fun getAprList(companyId: String) {
//        RxScheduleUtil.rxSchedulerHelper(aprApi.getAprList(UserHolder.getAccessToken(), companyId))
//                .compose(ErrorTransformer.getInstance()).subscribe(object : BaseSubscriber<AprList>() {
//                    override fun onError(ex: ApiException) {
//                        errorResult.value = ex
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(aprList: AprList?) {
//                        aprListResult.value = aprList
//                    }
//
//                })
//    }

    /**审批推送状态获取*/
    val aprPushStatusError = MutableLiveData<ApiException>()
    val aprPushStatusResult = MutableLiveData<Int>()

//    fun getAprPushStatus(companyId: String) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.getPushStatus(companyId)
//        )
//                .compose(ErrorTransformer.getInstance())
//                .subscribe(object : BaseSubscriber<Int>() {
//                    override fun onError(ex: ApiException) {
//                        aprPushStatusError.value = ex
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(aprList: Int?) {
//                        aprPushStatusResult.value = aprList
//                    }
//
//                })
//
//    }

    /**审批推送状态获取*/
    val aprPushChangeError = MutableLiveData<ApiException>()
    val aprPushChangeResult = MutableLiveData<Boolean>()

//    fun changeAprPushStatus(companyId: String, type: Int) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.putPushStatus(companyId, type)
//        )
//                .compose(ErrorTransformer.getInstance())
//                .subscribe(object : BaseSubscriber<Boolean>() {
//                    override fun onError(ex: ApiException) {
//                        aprPushChangeError.value = ex
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(aprList: Boolean?) {
//                        aprPushChangeResult.value = aprList
//                    }
//
//                })
//
//    }

    /**审批功能提醒状态获取*/
    val getAprFuncSettingError = MutableLiveData<ApiException>()
    val getAprFuncSettingResult = MutableLiveData<ApproveAlertSetting>()

//    fun getAprFuncSetting(userId: String, companyId: String) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.getAprSettings(userId, companyId)
//        )
//                .compose(ErrorTransformer.getInstance())
//                .subscribe(object : BaseSubscriber<ApproveAlertSetting>() {
//                    override fun onError(ex: ApiException) {
//                        getAprFuncSettingError.value = ex
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(aprList: ApproveAlertSetting?) {
//                        getAprFuncSettingResult.value = aprList
//                    }
//
//                })
//
//    }

    /**审批功能提醒状态设置*/
    val setAprFuncSettingError = MutableLiveData<ApiException>()
    val setAprFuncSettingResult = MutableLiveData<Any>()

//    fun setAprFuncSetting(data: ApproveAlertSetting) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.setAprSettings(data)
//        )
//                .compose(NoDataTransformer)
//                .subscribe(object : BaseSubscriber<Any>() {
//                    override fun onError(ex: ApiException) {
//                        setAprFuncSettingError.value = ex
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(aprList: Any?) {
//                        setAprFuncSettingResult.value = aprList
//                    }
//
//                })
//
//    }

    /**----------------审批模板详情获取结果---------------**/
    val modelDetailResult = MutableLiveData<ModelDetail>()

//    fun getAprModelInfo(modelId: String/* = "2462404307064980477"*/) {
//        RxScheduleUtil.rxSchedulerHelper(aprApi.getModelInfo(modelId))
//                .compose(ErrorTransformer.getInstance()).subscribe(object : BaseSubscriber<ModelDetail>() {
//                    override fun onError(ex: ApiException) {
//                        errorResult.value = ex
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(modelDetail: ModelDetail?) {
//                        modelDetailResult.value = modelDetail
//                    }
//
//                })
//    }

    /**----------------审批选择框数据选项---------------**/
    val widgetDataError = MutableLiveData<ApiException>()
    val widgetDataResult = MutableLiveData<List<SelectFrameData>>()

//    fun getWidgetData(widgetId: String) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.getWidgetData(widgetId)
//        )
//                .compose(ErrorTransformer.getInstance()).subscribe(object : BaseSubscriber<List<SelectFrameData>>() {
//                    override fun onError(ex: ApiException) {
//                        widgetDataError.value = ex
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(detail: List<SelectFrameData>?) {
//                        LogUtil.showLog("请求到列表数据 widgetId = $widgetId")
//                        LogUtil.showLog(detail ?: "null")
//                        widgetDataResult.value = detail
//                    }
//
//                })
//    }

    /**----------------个人团队信息---------------**/
    val userOrgInfoError = MutableLiveData<ApiException>()
    val userOrgInfoResult = MutableLiveData<UserOrgInfo>()

//    fun getUserOrgInfo(companyId: String, userId: String) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.getOrgInfo(
//                        userId = userId,
//                        companyId = companyId)
//        )
//                .compose(ErrorTransformer.getInstance()).subscribe(object : BaseSubscriber<UserOrgInfo>() {
//                    override fun onError(ex: ApiException) {
//                        userOrgInfoError.value = ex
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(detail: UserOrgInfo?) {
//                        userOrgInfoResult.value = detail
//                    }
//
//                })
//    }

    /**----------------审批ID获取---------------**/
    val getAprIdResult = MutableLiveData<String>()
    val getAprIdError = MutableLiveData<ApiException>()

//    fun getAprId() {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.getAprId())
//                .compose(ErrorTransformer.getInstance()).subscribe(object : BaseSubscriber<String>() {
//                    override fun onError(ex: ApiException) {
//                        getAprIdError.value = ex
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(approveId: String?) {
//                        getAprIdResult.value = approveId
//                    }
//
//                })
//    }

    val getAprIdResult3 = MutableLiveData<List<String>>()
    val getAprIdError3 = MutableLiveData<ApiException>()

//    fun getAprId3(count: Int) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.getAprId3(count))
//                .compose(ErrorTransformer.getInstance()).subscribe(object : BaseSubscriber<List<String>>() {
//                    override fun onError(ex: ApiException) {
//                        getAprIdError3.value = ex
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(approveIds: List<String>?) {
//                        getAprIdResult3.value = approveIds
//                    }
//                })
//    }

//    val checkUserIdsResult = MutableLiveData<String>()
//    val checkUserIdsError = MutableLiveData<ApiException>()
//
//    fun checkUserIds(data: Any) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.checkAprUser(data))
//                .compose(ErrorTransformer.getInstance()).subscribe(object : BaseSubscriber<String>() {
//                    override fun onError(ex: ApiException) {
//                        checkUserIdsError.value = ex
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(approveId: String?) {
//                        checkUserIdsResult.value = approveId
//                    }
//
//                })
//    }

    /**----------------创建审批---------------**/
//    val createAprResult = MutableLiveData<Any>()
//    val createAprError = MutableLiveData<ApiException>()
//
//    fun createApr(data: CreateAprData) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.createApprove(data))
//                .compose(ErrorTransformer.getInstance()).subscribe(object : BaseSubscriber<Any>() {
//                    override fun onError(ex: ApiException) {
//                        createAprError.value = ex
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(detail: Any?) {
//                        createAprResult.value = detail
//                    }
//                })
//    }

    /**----------------创建审批后操作---------------**/

    /**----------------判断自选审批人员中是否存在离职人员---------------**/
//    val checkLeaveResult = MutableLiveData<AprCheckLeaveLaunchBean>()
//    val checkLeaveError = MutableLiveData<String>()
//    fun checkLeave(token: String, organizationId: String, userIds: List<String>,
//                   life: LifecycleTransformer<Result<AprCheckLeaveLaunchBean>>) {
//        val map = hashMapOf<String, Any>()
//        map["organizationId"] = organizationId
//        map["userIds"] = userIds
//        ApprovalService.checkLeave(token, map)
//                .compose(life)
//                .compose(ErrorTransformer.getInstance())
//                .subscribe(object : BaseSubscriber<AprCheckLeaveLaunchBean>() {
//                    override fun onError(ex: ApiException) {
//                        checkLeaveError.value = ex.message
//                    }
//
//                    override fun onComplete() {
//                    }
//
//                    override fun onNext(t: AprCheckLeaveLaunchBean?) {
//                        if (t != null) {
//                            checkLeaveResult.value = t
//                        }
//                    }
//
//                })
//    }

    /**----------------获取审批列表---------------**/
//    val getMyAprListResult = MutableLiveData<GetMyAprListResult>()
//    val getMyAprListError = MutableLiveData<ApiException>()
//    fun getMyAprList(getAprListData: Map<String, Any>) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.getMyAprList(getAprListData)
//        ).compose(ErrorTransformer.getInstance()).subscribe(object : BaseSubscriber<GetMyAprListResult>() {
//            override fun onError(ex: ApiException) {
//                getMyAprListError.value = ex
//            }
//
//            override fun onComplete() {
//
//            }
//
//            override fun onNext(detail: GetMyAprListResult?) {
//                getMyAprListResult.value = detail
//            }
//
//        })
//    }

    /**----------------获取审批筛选条件---------------**/
    val getFilterResult = MutableLiveData<List<ModelFilterData>>()
    val getFilterError = MutableLiveData<ApiException>()
//    fun getApprovalFilter(companyId: String) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.getAprFilter(companyId)
//        ).compose(ErrorTransformer.getInstance())
//                .subscribe(object : BaseSubscriber<List<ModelFilterData>>() {
//                    override fun onError(ex: ApiException) {
//                        getFilterError.value = ex
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(detail: List<ModelFilterData>?) {
//                        getFilterResult.value = detail
//                    }
//
//                })
//    }

    /**----------------获取审批详情---------------**/
    val getAprDetailResult = MutableLiveData<ApprovalDetailData>()
    val getAprDetailError = MutableLiveData<ApiException>()
//    fun getAprDetailByType(approveId: String, type: Int) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.getAprDetailByType(approveId, type)
//        ).compose(ErrorTransformer.getInstance()).subscribe(object : BaseSubscriber<ApprovalDetailData>() {
//            override fun onError(ex: ApiException) {
//                getAprDetailError.value = ex
//            }
//
//            override fun onComplete() {
//            }
//
//            override fun onNext(detail: ApprovalDetailData?) {
//                getAprDetailResult.value = detail
//            }
//
//        })
//    }

    /**----------------审批撤回---------------**/
    val recallAprResult = MutableLiveData<Any>()
    val recallAprError = MutableLiveData<ApiException>()
//    fun recallApr(approveId: String) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.recallApr(approveId)
//        ).compose(NoDataTransformer).subscribe(object : BaseSubscriber<Any>() {
//            override fun onError(ex: ApiException) {
//                recallAprError.value = ex
//            }
//
//            override fun onComplete() {
//
//            }
//
//            override fun onNext(detail: Any?) {
//                recallAprResult.value = detail
//            }
//
//        })
//    }

    /**----------------审批催办---------------**/
    val remindAprResult = MutableLiveData<Boolean>()
    val remindAprError = MutableLiveData<ApiException>()
//    fun remindApr(approveId: String) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.remindApr(approveId,"")
//        ).compose(ErrorTransformer.getInstance()).subscribe(object : BaseSubscriber<Boolean>() {
//            override fun onError(ex: ApiException) {
//                remindAprError.value = ex
//            }
//
//            override fun onComplete() {
//
//            }
//
//            override fun onNext(detail: Boolean?) {
//                remindAprResult.value = detail
//            }
//
//        })
//
//    }

    /**----------------审批加审---------------**/
//    private var _conferenceListResult = MutableLiveData<CommonResult<ConfData>>()
//    val conferenceListResult: LiveData<CommonResult<ConfData>> = _conferenceListResult
    val addAprNodeResult = MutableLiveData<Any>()
    val addAprNodeError = MutableLiveData<ApiException>()
//    fun addAprNode2(approveAssignee: AprAddNodeData2) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.addAprNode2(approveAssignee)
//        ).compose(NoDataTransformer).subscribe(object : BaseSubscriber<Any>() {
//            override fun onError(ex: ApiException) {
//                addAprNodeError.value = ex
//            }
//
//            override fun onComplete() {
//
//            }
//
//            override fun onNext(detail: Any?) {
//                addAprNodeResult.value = detail
//            }
//
//        })
//
//    }

//    fun addAprNode3(approveAssignee: AprAddNodeData3) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.addAprNode3(approveAssignee)
//        ).compose(NoDataTransformer).subscribe(object : BaseSubscriber<Any>() {
//            override fun onError(ex: ApiException) {
//                addAprNodeError.value = ex
//            }
//
//            override fun onComplete() {
//
//            }
//
//            override fun onNext(detail: Any?) {
//                addAprNodeResult.value = detail
//            }
//
//        })
//
//    }
    /**----------------审批加审---------------**/

    /**----------------审批处理---------------**/
    val dealAprResult = MutableLiveData<Any>()
    val dealAprError = MutableLiveData<ApiException>()
//    fun dealApr(dealApproval: DealApprovalData) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.dealApprove(dealApproval)
//        ).compose(NoDataTransformer).subscribe(object : BaseSubscriber<Any>() {
//            override fun onError(ex: ApiException) {
//                dealAprError.value = ex
//            }
//
//            override fun onComplete() {
//
//            }
//
//            override fun onNext(detail: Any?) {
//                dealAprResult.value = detail
//            }
//
//        })
//
//    }

    /**----------------审批处理---------------**/
    val reCommitCheckResult = MutableLiveData<Boolean>()
    val reCommitCheckError = MutableLiveData<ApiException>()
//    fun reCommitCheck(approveId: String) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.reCreateApproveCheck(approveId)
//        ).compose(ErrorTransformer.getInstance()).subscribe(object : BaseSubscriber<Boolean>() {
//            override fun onError(ex: ApiException) {
//                reCommitCheckError.value = ex
//            }
//
//            override fun onComplete() {
//
//            }
//
//            override fun onNext(detail: Boolean?) {
//                reCommitCheckResult.value = detail
//            }
//
//        })
//
//    }


    /**----------------发审批评论---------------**/
    val aprCommentResult = MutableLiveData<Any>()
    val aprCommentError = MutableLiveData<ApiException>()
//    fun createAprComment(comment: AprCommentData) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.createAprComment(comment)
//        ).compose(NoDataTransformer).subscribe(object : BaseSubscriber<Any>() {
//            override fun onError(ex: ApiException) {
//                aprCommentError.value = ex
//            }
//
//            override fun onComplete() {
//
//            }
//
//            override fun onNext(detail: Any?) {
//                aprCommentResult.value = detail
//            }
//        })
//    }

    /**
     * 查询团队剩余容量
     */
    var searchCompanyCapacitySuccess = MutableLiveData<CapacityBean>()
    var searchCompanyCapacityError = MutableLiveData<String>()
//    fun searchCompanyCapacity(life: LifecycleTransformer<Result<CapacityBean>>,
//                              token: String, companyId: String) {
//        TaskService.searchCompanyCapacity(token, companyId)
//                .compose(life)
//                .compose(ErrorTransformer.getInstance<CapacityBean>())
//                .subscribe(object : BaseSubscriber<CapacityBean>() {
//                    override fun onError(ex: ApiException) {
//                        searchCompanyCapacityError.value = ex.message
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(t: CapacityBean?) {
//                        if (t != null) {
//                            searchCompanyCapacitySuccess.value = t
//                        } else searchCompanyCapacityError.value = "请求失败"
//                    }
//
//                })
//    }

    /**-----------------获取审批评论列表-----------------*/
    val aprCommentListResult = MutableLiveData<List<CommentListData>>()
    val aprCommentListError = MutableLiveData<ApiException>()
//    fun getAprCommentList(approveId: String, page: Int, length: Int) {
//        RxScheduleUtil.rxSchedulerHelper(
//                aprApi.getAprCommentList(approveId, page, length)
//        ).compose(ErrorTransformer.getInstance()).subscribe(object : BaseSubscriber<List<CommentListData>>() {
//            override fun onError(ex: ApiException) {
//                aprCommentListError.value = ex
//            }
//
//            override fun onComplete() {
//
//            }
//
//            override fun onNext(detail: List<CommentListData>?) {
//                aprCommentListResult.value = detail
//            }
//        })
//
//    }

    /**-----------------获取团队待审批数量-----------------*/
    val unCompleteApprovalCountResult = MutableLiveData<Long>()
    val unCompleteApprovalCountError = MutableLiveData<String>()
//    fun waitDealApproveCount(organizationId: String) {
//        RxScheduleUtil.rxSchedulerHelper(aprApi.waitDealApproveCount(organizationId))
//                .compose(ErrorTransformer.getInstance())
//                .subscribe(object : BaseSubscriber<Long>() {
//                    override fun onError(ex: ApiException) {
//                        unCompleteApprovalCountError.value = ex.message
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(t: Long?) {
//                        if (t != null) unCompleteApprovalCountResult.value = t
//                    }
//                })
//
//    }

    /**-----------------获取团队待审批数量-----------------*/
//    fun checkFixProcessPersonLeave(life: LifecycleTransformer<Result<Boolean>>,
//                                   modelId: String, organizationId: String,
//                                   onSuccess: (Boolean) -> Unit, onError: (ApiException) -> Unit) {
//        RxScheduleUtil.rxSchedulerHelper(aprApi.checkFixProcessPersonLeave(modelId, organizationId))
//                .compose(life)
//                .compose(ErrorTransformer.getInstance())
//                .subscribe(object : BaseSubscriber<Boolean>() {
//                    override fun onError(ex: ApiException) {
//                        onError.invoke(ex)
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(t: Boolean?) {
//                        if (t != null) onSuccess.invoke(t)
//                    }
//                })
//
//    }

}