package com.joinutech.approval.request

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.joinutech.approval.api.ApprovalApi
import com.joinutech.approval.data.*
import com.joinutech.common.base.isApproval
import com.joinutech.common.base.isFuture
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.bean.AprCheckLeaveLaunchBean
import com.joinutech.ddbeslibrary.bean.CapacityBean
import com.joinutech.ddbeslibrary.request.*
import com.trello.rxlifecycle3.LifecycleTransformer

/**
 * @className: ApprovalModel
 * @desc: 审批网络请求
 * @author: zyy
 * @date: 2019/8/16 10:44
 * @company: joinUTech
 * @leader: ke
 */
class ApprovalModel2 : ViewModel() {

    private val aprApi =
        RetrofitClient.single_intance.getRxRetrofit().create(ApprovalApi::class.java)

    /**----------------模板设置相关---------------**/
    private val _modelListResult = MutableLiveData<CommonResult<List<ModelGroup>>>()
    val modelListResult: LiveData<CommonResult<List<ModelGroup>>> = _modelListResult

    /**审批设置模板列表*/
    fun getModelList(orgId: String, type: Int = 1) {
        RequestHelper.onRequest(
            if (isFuture && isApproval) {
                aprApi.getModelListV2(orgId, type)
            } else {
                aprApi.getModelList(orgId, type)
            },
            _modelListResult, "获取审批设置模板列表"
        )
    }

    private val _changeModelResult = MutableLiveData<CommonResult<Any>>()
    val changeModelResult: LiveData<CommonResult<Any>> = _changeModelResult

    /**启用停用*/
    fun changeModel(modelId: String, type: Int) {
        RequestHelper.onRequestAny(
            aprApi.changeModel(modelId, type),
            _changeModelResult,
            "审批模板启用停用"
        )
    }

    /**通知全员*/
    private val _modelNotifyResult = MutableLiveData<CommonResult<Any>>()
    val modelNotifyResult: LiveData<CommonResult<Any>> = _modelNotifyResult

    fun modelNotify(modelId: String) {
        RequestHelper.onRequestAny(aprApi.modelNotify(modelId), _modelNotifyResult, "审批模板 通知全员")
    }
    /**----------------模板设置相关---------------**/

    /**----------------审批首页列表---------------**/
    private val _aprListResult = MutableLiveData<CommonResult<AprList>>()
    val aprListResult: LiveData<CommonResult<AprList>> = _aprListResult

    fun getAprList(companyId: String) {
        RequestHelper.onRequest(
            aprApi.getAprList(UserHolder.getAccessToken(), companyId),
            _aprListResult, "审批首页列表"
        )
    }

    fun getAprListV2(companyId: String) {
        RequestHelper.onRequest(aprApi.getAprListV2(companyId), _aprListResult, "审批首页列表")
    }

    /**审批推送状态获取*/
    private val _getAprPushStatusResult = MutableLiveData<CommonResult<Int>>()
    val getAprPushStatusResult: LiveData<CommonResult<Int>> = _getAprPushStatusResult

    fun getAprPushStatus(companyId: String) {
        RequestHelper.onRequest(
            aprApi.getPushStatus(companyId),
            _getAprPushStatusResult,
            "审批推送状态获取"
        )
    }

    /**审批推送状态获取*/
    private val _setAprPushStatusResult = MutableLiveData<CommonResult<Boolean>>()
    val setAprPushStatusResult: LiveData<CommonResult<Boolean>> = _setAprPushStatusResult

    fun setAprPushStatus(companyId: String, type: Int) {
        RequestHelper.onRequest(
            aprApi.putPushStatus(companyId, type),
            _setAprPushStatusResult,
            "审批推送状态设置"
        )
    }

    /**审批功能提醒状态获取*/
    private val _getAprFuncSettingResult = MutableLiveData<CommonResult<ApproveAlertSetting>>()
    val getAprFuncSettingResult: LiveData<CommonResult<ApproveAlertSetting>> =
        _getAprFuncSettingResult

    fun getAprFuncSetting(userId: String, companyId: String) {
        RequestHelper.onRequest(
            aprApi.getAprSettings(userId, companyId),
            _getAprFuncSettingResult,
            "审批功能提醒状态获取"
        )
    }

    /**审批功能提醒状态设置*/
    private val _setAprFuncSettingResult = MutableLiveData<CommonResult<Any>>()
    val setAprFuncSettingResult: LiveData<CommonResult<Any>> = _setAprFuncSettingResult

    fun setAprFuncSetting(data: ApproveAlertSetting) {
        RequestHelper.onRequestAny(
            aprApi.setAprSettings(data),
            _setAprFuncSettingResult,
            "审批功能提醒状态设置"
        )
    }

    /**----------------审批模板详情获取结果---------------**/
    private val _modelDetailResult = MutableLiveData<CommonResult<ModelDetail>>()
    val modelDetailResult: LiveData<CommonResult<ModelDetail>> = _modelDetailResult

    fun getAprModelInfo(modelId: String) {
        RequestHelper.onRequest(aprApi.getModelInfo(modelId), _modelDetailResult, "审批模板详情")
    }

    fun getAprModelInfoV2(modelId: String) {
        RequestHelper.onRequest(aprApi.getModelInfoV2(modelId), _modelDetailResult, "审批模板详情")
    }
// private val _modelDetailResult = MutableLiveData<CommonResult<ModelDetail>>()
//    val modelDetailResult: LiveData<CommonResult<ModelDetail>> = _modelDetailResult
//
//    fun getAprModelInfo(modelId: String) {
//        RequestHelper.onRequest(aprApi.getModelInfo(modelId), _modelDetailResult, "审批模板详情")
//    }

    /**----------------审批选择框数据选项---------------**/
    private val _widgetDataResult = MutableLiveData<CommonResult<List<SelectFrameData>>>()
    var widgetDataResult: LiveData<CommonResult<List<SelectFrameData>>> = _widgetDataResult

    fun getWidgetData(widgetId: String) {
        RequestHelper.onRequest(aprApi.getWidgetData(widgetId), _widgetDataResult, "审批选择框数据选项")
    }

    /**----------------个人团队信息---------------**/
    private val _userOrgInfoResult = MutableLiveData<CommonResult<UserOrgInfo>>()
    val userOrgInfoResult: LiveData<CommonResult<UserOrgInfo>> = _userOrgInfoResult

    fun getUserOrgInfo(companyId: String, userId: String) {
        RequestHelper.onRequest(
            aprApi.getOrgInfo(userId = userId, companyId = companyId),
            _userOrgInfoResult, "个人团队信息"
        )
    }

    /**----------------审批ID获取---------------**/
    private val _getAprIdResult = MutableLiveData<CommonResult<String>>()
    val getAprIdResult: LiveData<CommonResult<String>> = _getAprIdResult

    fun getAprId() {
        RequestHelper.onRequest(aprApi.getAprId(), _getAprIdResult, "审批ID获取")
    }

    private val _getAprIdResult3 = MutableLiveData<CommonResult<List<String>>>()
    val getAprIdResult3: LiveData<CommonResult<List<String>>> = _getAprIdResult3

    fun getAprId3(count: Int) {
        RequestHelper.onRequest(aprApi.getAprId3(count), _getAprIdResult3, "审批ID获取")
    }

//    private val _checkUserIdsResult = MutableLiveData<CommonResult<String>>()
//    val checkUserIdsResult: LiveData<CommonResult<String>> = _checkUserIdsResult
//
//    fun checkUserIds(data: Any) {
//        RequestHelper.onRequest(aprApi.checkAprUser(data), _checkUserIdsResult, "检查用户是否有效")
//    }

    /**----------------创建审批---------------**/

    private val _createAprResult = MutableLiveData<CommonResult<Any>>()
    val createAprResult: LiveData<CommonResult<Any>> = _createAprResult

    fun createAprV5(data: CreateAprData) {
        RequestHelper.onRequest(aprApi.createApproveV5(data), _createAprResult, "创建审批")
    }

    fun createAprV6(data: CreateAprData) {
        RequestHelper.onRequest(aprApi.createApproveV6(data), _createAprResult, "创建审批")
    }

    //限时提交校验
    private val _checkIsLimitResult = MutableLiveData<CommonResult<Any>>()
    val checkIsLimitResult: LiveData<CommonResult<Any>> = _checkIsLimitResult
    fun checkIsLimit(data: LimitData) {
        RequestHelper.onRequest(aprApi.checkIsLimit(data), _checkIsLimitResult, "限时校验")
    }

    /**----------------创建审批后操作---------------**/

    /**----------------判断自选审批人员中是否存在离职人员---------------**/
    private val _checkLeaveResult = MutableLiveData<CommonResult<AprCheckLeaveLaunchBean>>()
    val checkLeaveResult: LiveData<CommonResult<AprCheckLeaveLaunchBean>> = _checkLeaveResult

    fun checkLeave(
        life: LifecycleTransformer<Result<AprCheckLeaveLaunchBean>>,
        organizationId: String,
        userIds: List<String>
    ) {
        val map = hashMapOf<String, Any>()
        map["organizationId"] = organizationId
        map["userIds"] = userIds
        RequestHelper.onRequest(life, aprApi.checkLeave(map), _checkLeaveResult, "检查离职人员")
    }

    /**----------------获取审批列表---------------**/
//    val getMyAprListResult = MutableLiveData<GetMyAprListResult>()
//    val getMyAprListError = MutableLiveData<ApiException>()
    private val _getMyAprListResult = MutableLiveData<CommonResult<GetMyAprListResult>>()
    val getMyAprListResult: LiveData<CommonResult<GetMyAprListResult>> = _getMyAprListResult

    fun getMyAprList(getAprListData: Map<String, Any>) {
        RequestHelper.onRequest(aprApi.getMyAprList(getAprListData), _getMyAprListResult, "获取审批列表")
    }

    /**----------------获取审批筛选条件---------------**/
    private val _getFilterResult = MutableLiveData<CommonResult<List<ModelFilterData>>>()
    val getFilterResult: LiveData<CommonResult<List<ModelFilterData>>> = _getFilterResult

    fun getApprovalFilter(companyId: String) {
        RequestHelper.onRequest(aprApi.getAprFilter(companyId), _getFilterResult, "获取审批筛选条件")
    }

    /**----------------获取审批详情---------------**/
    private val _getAprDetailResult = MutableLiveData<CommonResult<ApprovalDetailData>>()
    val getAprDetailResult: LiveData<CommonResult<ApprovalDetailData>> = _getAprDetailResult

    fun getAprDetailByType(approveId: String, type: Int) {
        RequestHelper.onRequest(
            aprApi.getAprDetailByType(approveId, type),
            _getAprDetailResult,
            "获取审批详情"
        )
    }

    /**----------------审批撤回---------------**/
    private val _recallAprResult = MutableLiveData<CommonResult<Any>>()
    val recallAprResult: LiveData<CommonResult<Any>> = _recallAprResult

    fun recallApr(approveId: String) {
        RequestHelper.onRequestAny(aprApi.recallApr(approveId), _recallAprResult, "审批撤回")
    }

    /**----------------审批催办---------------**/
    private val _remindAprResult = MutableLiveData<CommonResult<Boolean>>()
    val remindAprResult: LiveData<CommonResult<Boolean>> = _remindAprResult

    fun remindApr(approveId: String, approveAssigneeId: String) {
        RequestHelper.onRequest(
            aprApi.remindApr(approveId, approveAssigneeId),
            _remindAprResult,
            "审批催办"
        )
    }

    /**----------------审批加审---------------**/
//    private var _conferenceListResult = MutableLiveData<CommonResult<ConfData>>()
//    val conferenceListResult: LiveData<CommonResult<ConfData>> = _conferenceListResult
    private val _addAprNodeResult = MutableLiveData<CommonResult<Any>>()
    val addAprNodeResult: LiveData<CommonResult<Any>> = _addAprNodeResult

    fun addAprNode2(approveAssignee: AprAddNodeData2) {
        RequestHelper.onRequestAny(aprApi.addAprNode2(approveAssignee), _addAprNodeResult, "审批加审")
    }

    fun addAprNode3(approveAssignee: AprAddNodeData3) {
        RequestHelper.onRequestAny(aprApi.addAprNode3(approveAssignee), _addAprNodeResult, "审批加审")
    }

    /**----------------审批加审---------------**/

    /**----------------审批处理---------------**/
    private val _dealAprResult = MutableLiveData<CommonResult<Any>>()
    val dealAprResult: LiveData<CommonResult<Any>> = _dealAprResult

    fun dealApr(dealApproval: DealApprovalData) {
        RequestHelper.onRequestAny(aprApi.dealApprove(dealApproval), _dealAprResult, "审批处理")
    }

    private val _dealAprFastResult = MutableLiveData<CommonResult<Any>>()
    val dealAprFastResult: LiveData<CommonResult<Any>> = _dealAprFastResult

    fun dealApproveFast(dealApproval: FastProcessApprovalData) {
        RequestHelper.onRequestAny(
            aprApi.dealApproveFast(dealApproval),
            _dealAprFastResult,
            "审批列表处理审批"
        )
    }

    /**----------------审批重新发起检测---------------**/
    private val _reCommitCheckResult = MutableLiveData<CommonResult<Boolean>>()
    val reCommitCheckResult: LiveData<CommonResult<Boolean>> = _reCommitCheckResult

    fun reCommitCheck(approveId: String) {
        RequestHelper.onRequest(
            aprApi.reCreateApproveCheck(approveId),
            _reCommitCheckResult,
            "审批重新发起检测"
        )
    }

    /**----------------发审批评论---------------**/
    private val _aprCommentResult = MutableLiveData<CommonResult<Any>>()
    val aprCommentResult: LiveData<CommonResult<Any>> = _aprCommentResult

    fun createAprComment(comment: AprCommentData) {
        RequestHelper.onRequestAny(aprApi.createAprCommentV4(comment), _aprCommentResult, "发审批评论")
    }

    /**
     * 查询团队剩余容量
     */
    private val _searchCompanyCapacityResult = MutableLiveData<CommonResult<CapacityBean>>()
    val searchCompanyCapacityResult: LiveData<CommonResult<CapacityBean>> =
        _searchCompanyCapacityResult

    fun searchCompanyCapacity(
        life: LifecycleTransformer<Result<CapacityBean>>,
        token: String,
        companyId: String
    ) {
        RequestHelper.onRequest(
            life, DdbesApiUtil.getTaskService().searchCompanyCapacity(token, companyId),
            _searchCompanyCapacityResult, "查询团队剩余容量"
        )
    }

    /**-----------------获取审批评论列表-----------------*/
    private val _aprCommentListResult = MutableLiveData<CommonResult<List<CommentListData>>>()
    val aprCommentListResult: LiveData<CommonResult<List<CommentListData>>> = _aprCommentListResult

    fun getAprCommentList(approveId: String, page: Int, length: Int) {
        RequestHelper.onRequest(
            aprApi.getAprCommentList(approveId, page, length),
            _aprCommentListResult,
            "获取审批评论列表"
        )
    }

    /**-----------------获取团队待审批数量-----------------*/
    private val _unCompleteApprovalCountResult = MutableLiveData<CommonResult<Long>>()
    val unCompleteApprovalCountResult: LiveData<CommonResult<Long>> = _unCompleteApprovalCountResult

    fun waitDealApproveCount(organizationId: String) {
        RequestHelper.onRequest(
            aprApi.waitDealApproveCount(organizationId),
            _unCompleteApprovalCountResult,
            "获取团队待审批数量"
        )
    }

    /**-----------------校验固定流程是否存在审批人离职情况-----------------*/
    private val _checkProcessMemberLeaveResult = MutableLiveData<CommonResult<Boolean>>()
    val checkProcessMemberLeaveResult: LiveData<CommonResult<Boolean>> =
        _checkProcessMemberLeaveResult

    fun checkFixProcessPersonLeave(
        life: LifecycleTransformer<Result<Boolean>>,
        modelId: String, organizationId: String
    ) {
        RequestHelper.onRequest(
            life,
            aprApi.checkFixProcessPersonLeave(modelId, organizationId),
            _checkProcessMemberLeaveResult,
            "校验固定流程是否存在审批人离职情况"
        )
    }

}