package com.joinutech.approval.data

import com.joinutech.ddbeslibrary.bean.CreateTaskCommentUploadFileBean
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.joinutech.ddbeslibrary.bean.UploadFileBean
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.marktoo.lib.cachedweb.LogUtil
import java.io.Serializable

/**
 * @className: ApprovalData
 * @desc: 审批数据相关对象定义
 * @author: zyy
 * @date: 2019/8/15 10:29
 * @company: joinUTech
 * @leader: ke
 */
/**模板设置列表数据项 模板分组数据*/
data class ModelGroup(
        val groupId: String,
        val groupName: String,
        val models: List<ModelData>?,
        val organizationId: String,
        /**0.删除；1.正常;2.新增未保存；3.删除未保存*/
        /** TODO: 2019/9/11 审批分组的状态：0.删除；1.正常;2.其他分组，3.已停用分组*/
        val status: Int
)

/**模板显示数据*/
data class ModelData(
        val createTime: Long,
        val deployId: String,
        val modelDesc: String,
        val modelId: String,
        val modelLogo: String,
        val modelName: String,
        val modelType: Int,
        /**1 已启用，2 未启用*/
        val status: Int,
        //0.全部可见；1.部分可见
        val visibleAble: Int
)

/**审批功能列表*/
data class AprList(
        val approveCount: Int,
        val approveModel: List<AprGroup>,
        val assigneeCount: Int,
        val crbonCopyCount: Int
)

/**审批功能列表分组*/
data class AprGroup(
        val items: List<AprItem>?,
        val title: String
)

/**审批功能设置获取和提交*/
data class ApproveAlertSetting(
        /**审批提醒开关 1.开启 0.关闭*/
        val approvalSwitch: Int,
        val approvalRemindHour: Int,
        val approvalRemindMinute: Int,
        val companyId: String,
        val userId: String
)

/**审批功能列表项*/
data class AprItem(
        val modelId: String,
        val modelName: String,
        val modelGroup: String,
        val modelLogo: String,
        val modelDesc: String,
        //0.全部可见；1.部分可见
        val visibleAble: Int,
        /**1 已启用，2 未启用*/
        val modelStatus: Int
) : Serializable

/**审批模板详情*/
data class ModelDetail(
        val modelId: String,
        val modelName: String,
        val modelDesc: String,
        /**1.低级流程 固定；2.高级流程；3.自定义模式（自选人员)*/
        val processType: Int,
        val processId: String,
        val deployId: String,
        val version: Int,
        val carbonCopy: MutableList<SearchMemberBean>,
//        val widget: List<HashMap<String, Any>>,
        val widget: List<WidgetInfo>,
        //加审的时候是否可以加审外部联系人 0 不允许 1允许
        val outApprove: Int
)

/** * 模板中属性数据默认按hashmap接收，需要使用哪些，在这个对象中声明属性即可*/
/**提交审批后返回属性详情*/
/**审批属性详情*/
class WidgetInfo(
        var modelId: String? = null,//接口字段
        var widgetId: String? = null,//接口字段
        /**控件类型：
         * 1.文本类型；2.长文本类型；
         * 3.时间点类型；4.时间段类型；
         * 5.时间长度类型；
         * 6.单选框类型；7.多选框类型；
         * 8.人员选择类型；
         * 9.关键字选择类型；
         * 10.图片类型；
         * 11.地点类型
         * 12.文件类型 移动端空数组即可
         * 13.时间段自动计算时长属性，计算规则需要服务端给返回
         * 20 审批人选择
         * 30 抄送人选择
         * else 自动获取：人员姓名，职位，部门，当前时间
         * */
        val type: Int,//接口字段
        val title: String = "",//接口字段
        val prompt: String = "",//接口字段
        /**必填内容:0:否，1：是*/
        val required: String = "0",//接口字段
        /**副标题*/
        val secTitle: String = "",//接口字段
        /**副提示语*/
        val secPrompt: String = "",//接口字段
        /**单位后缀*/
        val suffix: String = "",//接口字段
        /**文本长度限制*/
        val wordLimit: Int = 10,//接口字段
        /**键盘类型1：普通，2：数字*/
        val keyBoard: Int = 1,//接口字段
        /**人员选择：0，单人选择，1：多人选择*/
        val personType: Int = 0,//接口字段
        /**文字类型:0.姓名类；1.职位类；2.部门类*/
        val wordType: Int = 0,//接口字段

        /**自动获取部门:0：否，1：是，自动获取部门*/
        val autoDept: Int = 0,//接口字段
        /**手动自动选择：0，手动，1：自动*/
        val nowChoose: Int = 0,//接口字段
        /**日期选择：0：年月日时分，1：年月日，2：时分*/
        val dateChoose: Int = 0,//接口字段
        /**是否允许手动定位选择：0：否 自动定位，1：是 手动定位*/
        val addressChoose: Int = 0,//接口字段

        /**带选择列表的内容数据？*/
        val chooseSort: String = "",//接口字段
        /**选择数量*/
        val chooseCount: Int = 1,//接口字段

        /**状态：0：删除，1：正常，2,：修改*/
        val status: Int = 1,//接口字段
        /**--------------自增字段，提交内容时使用---------------**/
        /**--------------获取详情时，提交的内容通过这个字段返回---------------**/
        var content: String? = "",
        /**标记是否属于套件?*/
        val isWidgetmodel: Int? = 0,//接口字段
        /**套件属性服务端保存key提前设置*/
        val modelKey: String? = "",//接口字段
        val rule: Int? = 0,//接口字段
        val maxNum: Int? = 0,//接口字段
        val thirdTitle: String? = "",//接口字段
        //0代表不限制，1代表限制当月提交，2限制当日提交，3限制补交，4限制提前提交，这个字段只在type为3、4、13时用到
        val isLimit:Int? =0,
        //代表限制的天数
        val dayNumber:Int? =0,
        val limitRemind:String? ="",
        /**--------------自增字段---------------**/
        /**审批id，用于存储图片时，标识审批相关的图片，用于清理未提交审批资源占用*/
        var aprId: String = "",
        /**日期时间选择结果缓存*/
        var dateResult: DateSegResult? = null,
//        /**对应type13，多个时间点*/
//        var dateArray: HashMap<Int, Long> = hashMapOf(),
        /**定位地址选择结果缓存*/
        var addressResult: AddressResult? = null,
        /**人员，部门选择结果缓存*/
        var personResult: List<SelectData>? = null,
        /**图片选择结果缓存*/
        var selectFileResult: MutableList<UploadFileBean>? = null,
//        /**列表项选择结果缓存*/
//        var selectFrameResult: MutableList<SelectFrameData>? = null,
        /**节点选择结果缓存*/
        var selectNodeResult: MutableList<SearchMemberBean>? = null,
        /**抄送人选择结果缓存*/
        var selectCopyResult: MutableList<SearchMemberBean>? = null,
        /**评论列表*/
        var commentList: ArrayList<CommentListData>? = null,
        var isEdit: Boolean = true,//接口字段
        var showing: Boolean = false//接口字段
)

fun WidgetInfo.turnSubmit(): SubmitWidgetInfo {
    return SubmitWidgetInfo(modelId, widgetId, type, title, prompt, required, secTitle, secPrompt,
            suffix, wordLimit, keyBoard, personType, wordType, autoDept, nowChoose, dateChoose,
            addressChoose, chooseSort, chooseCount, status, content)
}

fun WidgetInfo.turnSubmitV2(): SubmitWidgetInfo {
    return SubmitWidgetInfo(modelId, widgetId, type, title, prompt, required, secTitle, secPrompt,
            suffix, wordLimit, keyBoard, personType, wordType, autoDept, nowChoose, dateChoose,
            addressChoose, chooseSort, chooseCount, status, content,
            isWidgetmodel, modelKey, rule, maxNum, thirdTitle)
}

class SubmitWidgetInfo(
        val modelId: String? = null,
        val widgetId: String? = null,
        /**控件类型：
         * 1.文本类型；2.长文本类型；
         * 3.时间点类型；4.时间段类型；
         * 5.时间长度类型；
         * 6.单选框类型；7.多选框类型；
         * 8.人员选择类型；
         * 9.关键字选择类型；
         * 10.图片类型；
         * 11.地点类型
         * 12.文件类型 移动端空数组即可
         * 13.时间段自动计算时长属性，计算规则需要服务端给返回
         * 20 审批人选择
         * 30 抄送人选择
         * else 自动获取：人员姓名，职位，部门，当前时间
         * */
        val type: Int,
        val title: String = "",
        val prompt: String = "",
        /**必填内容:0:否，1：是*/
        val required: String = "0",
        /**副标题*/
        val secTitle: String = "",
        /**副提示语*/
        val secPrompt: String = "",
        /**单位后缀*/
        val suffix: String = "",
        /**文本长度限制*/
        val wordLimit: Int = 10,
        /**键盘类型1：普通，2：数字*/
        val keyBoard: Int = 1,
        /**人员选择：0，单人选择，1：多人选择*/
        val personType: Int = 0,
        /**文字类型:0.姓名类；1.职位类；2.部门类*/
        val wordType: Int = 0,

        /**自动获取部门:0：否，1：是，自动获取部门*/
        val autoDept: Int = 0,
        /**手动自动选择：0，手动，1：自动*/
        val nowChoose: Int = 0,
        /**日期选择：0：年月日时分，1：年月日，2：时分*/
        val dateChoose: Int = 0,
        /**是否允许手动定位选择：0：否 自动定位，1：是 手动定位*/
        val addressChoose: Int = 0,

        /**带选择列表的内容数据？*/
        val chooseSort: String = "",
        /**选择数量*/
        val chooseCount: Int = 1,

        /**状态：0：删除，1：正常，2,：修改*/
        val status: Int = 1,
        /**--------------自增字段，提交内容时使用---------------**/
        /**--------------获取详情时，提交的内容通过这个字段返回---------------**/
        val content: String? = "",
        /**标记是否属于套件?*/
        val isWidgetmodel: Int? = 0,
        /**套件属性服务端保存key提前设置*/
        val modelKey: String? = "",
        val rule: Int? = 0,
        val maxNum: Int? = 0,
        val thirdTitle: String? = ""
)

data class SelectData(var headimg: String, var name: String, var userId: String)

data class DateSegResult(var startTime: String = 0L.toString(), var endTime: String = 0L.toString())

data class AddressResult(var address: String = "", var positioning: String = "", var longitude: Double = 0.0, var latitude: Double = 0.0)

/**审批人自动获取信息*/
data class UserOrgInfo(
        val deptId: String,
        val deptName: String,
        val managerId: String,
        val managerName: String
)

/**单选多选框数据源*/
data class SelectFrameData(
        val frameId: String,
        val status: Int,
        val title: String,
        val widgetId: String
)

/**创建审批提交数据*/
data class CreateAprData(
        var approveId: String,
        val approveName: String,
        val approveType: Int,
        /**审批人*/
        val assignee: List<Map<String, Any>>,
        /**选择的抄送人*/
        val carbonCopys: List<String>,
        /**增加内部抄送人*/
        val extraInCarbonCopys: List<String>,
        /**增加外部抄送人*/
        val extraOutCarbonCopys: List<String>,
        /**内容数组*/
        val content: List<SubmitWidgetInfo>,
//        val content: List<Map<String, Any>>,
        /**列表中显示审批简略数据 第一行*/
        val oneApproveContent: String,
        /**列表中显示审批简略数据 第二行*/
        val twoApproveContent: String,
        /**列表中显示审批简略数据 第三行*/
        val threeApproveContent: String,
        val deployId: String,
        val deptId: String,
        val modelId: String,
        val organizationId: String,
        val processId: String = "",
        val version: Int,
        /**1.申请审批；2.重新提交*/
        val type: Int = 1
)

data class LimitData(
        val dayNumber: Int?=0,
        val limitType:Int?=0,
        val time:ArrayList<Long>?= arrayListOf()
)

/**获取个人审批列表请求数据*/
data class GetAprList(
        /**团队id*/
        val organizationId: String,
        /**
         * 1.我发起的；
         * 2.我审批的，
         * 3.抄送我的
         * */
        val type: Int,

        /**
         * 我审批的：
         * 1.待审批；
         * 2.已审批的；
         * 3.已完成的
         * */
        val myApproveType: Int = 0,

        /**审批状态：
         * 0.删除，
         *
         * 3.进行中；
         * 1完成；
         * 5.终止；
         *
         * 4.未提交；
         * */
        val status: Int = 0,

        /**抄送状态：
         * 0.删除；
         * 1.正常；
         * 2未查看
         * */
        var carbonStatus: Int = 0,

        /**分页*/
        var start: Int = 1,
        /**分页长度*/
        val length: Int = 20,
        /**关键词*/
        var keyword: String = "",
        /**模板id*/
        var modelId: String = ""
) {
    fun getMap(requestType: Int): Map<String, Any> {
        val map = HashMap<String, Any>()
        when (requestType) {
            // 快速审批
            0 -> {
                map["organizationId"] = organizationId
                map["type"] = type
                map["myApproveType"] = myApproveType
                map["start"] = start
                map["length"] = length
                LogUtil.showLog("快速审批 获取数据 请求参数：${GsonUtil.toJson(map)}")
            }
            // 我发起的
            1 -> {
                map["organizationId"] = organizationId
                map["type"] = type
                map["status"] = status
                map["start"] = start
                map["length"] = length
                if (StringUtils.isNotBlankAndEmpty(keyword)) {
                    map["keyword"] = keyword
                }
                if (StringUtils.isNotBlankAndEmpty(modelId)) {
                    map["modelId"] = modelId
                }
                LogUtil.showLog("我发起的 获取数据 请求参数：${GsonUtil.toJson(map)}")
            }
            // 我审批的
            2 -> {
                map["organizationId"] = organizationId
                map["type"] = type
                map["myApproveType"] = myApproveType
                map["start"] = start
                map["length"] = length
                if (StringUtils.isNotBlankAndEmpty(keyword)) {
                    map["keyword"] = keyword
                }
                if (StringUtils.isNotBlankAndEmpty(modelId)) {
                    map["modelId"] = modelId
                }
                LogUtil.showLog("我审批的 获取数据 请求参数：${GsonUtil.toJson(map)}")
            }
            // 抄送我的
            else -> {
                map["organizationId"] = organizationId
                map["type"] = type
                map["carbonStatus"] = carbonStatus
                map["start"] = start
                map["length"] = length
                if (StringUtils.isNotBlankAndEmpty(keyword)) {
                    map["keyword"] = keyword
                }
                if (StringUtils.isNotBlankAndEmpty(modelId)) {
                    map["modelId"] = modelId
                }
                LogUtil.showLog("抄送我的 获取数据 请求参数：${GsonUtil.toJson(map)}")
            }
        }
        return map
    }
}

//data class GetLaunchRequest(
//        /**团队id*/
//        val organizationId: String,
//        /**
//         * 1.我发起的；
//         * */
//        val type: Int = 1,
//
//        /**审批状态：
//         * 0.删除，
//         *
//         * 3.进行中；
//         * 1完成；
//         * 5.终止；
//         *
//         * 4.未提交；
//         * */
//        val status: Int = 0,
//
//        /**分页*/
//        val start: Int = 1,
//        /**分页长度*/
//        val length: Int = 20,
//        /**关键词*/
//        val keyword: String = "",
//        /**筛选ID*/
//        val modelId: String = ""
//)
//
//data class GetHandleRequest(
//        /**团队id*/
//        val organizationId: String,
//        /**
//         * 2.我审批的，
//         * */
//        val type: Int = 2,
//
//        /**
//         * 我审批的：
//         * 1.待审批；
//         * 2.已审批的；
//         * 3.已完成的
//         * */
//        val myApproveType: Int = 0,
//        /**分页*/
//        val start: Int = 1,
//        /**分页长度*/
//        val length: Int = 20,
//        /**关键词*/
//        val keyword: String = "",
//        /**筛选ID*/
//        val modelId: String = ""
//)
//
//data class GetCopyRequest(
//        /**团队id*/
//        val organizationId: String,
//        /**
//         * 3.抄送我的
//         * */
//        val type: Int = 3,
//
//        /**抄送状态：
//         * 0.删除；
//         * 1.正常；
//         * 2未查看
//         * */
//        val carbonStatus: Int = 0,
//
//        /**分页*/
//        val start: Int = 1,
//        /**分页长度*/
//        val length: Int = 20,
//        /**关键词*/
//        val keyword: String = "",
//        /**筛选ID*/
//        val modelId: String = ""
//)

/**模板删选数据*/
data class ModelFilterData(
        val modelId: String = "",
        val modelName: String = "",
        val modelGroup: String = "",
        var isSelect: Boolean = false
)

/**审批上部 审批列表数据*/
data class GetMyAprListResult(
        val approveList: List<MyAprListData>,
        /**只返回了0*/
        val count: Int = 0,
        /**所有审批数量*/
        val total: Int = 0
)

/**审批列表数据*/
data class MyAprListData(
        val approveId: String,
        val approveName: String,
        /**当前审批节点人名*/
        val approver: String,
        /**创建者名字*/
        val creator: String,
        /**内容*/
        val content: String? = "",
        /**第一条*/
        val oneApproveContent: String? = "",
        /**第二条*/
        val twoApproveContent: String? = "",
        /**第三条*/
        val threeApproveContent: String? = "",
        val createTime: Long,
        val deployId: String,
        /**1 已读*/
        var isRead: Int,
        val modelLogo: String,
        /**0.删除，1通过；2.拒绝；3.进行中；4.未提交；5.撤回；6.退回*/
        val status: Int,
        val taskId: String = ""
) : Serializable

/**
 * {
"code": 1,
"msg": "请求成功",
"data": {
"approveProcess": {
"carbonCopy": [
{
"headimg": "http://ddbes.cdn.joinutech.com/FvGLIo5yNZUkxteQV7MSFdabkz8x",
"name": "刘立雪",
"userId": "4073025347994994"
}
],
"assignee": [
{
"approveType": 1,
"assigneeUser": [
{
"approveAssigneeId": "2463520317809624061",
"headimg": "http://ddbes-repository-1257239906.cos.ap-beijing.myqcloud.com/HEAD/4102554220119040B7ryE6RPf01x62KBAu1566463429013.png",
"createTime": 0,
"name": "151分部",
"userId": "4102554220119040",
"content": "",
"opinion": 3
}
]
}
],
"approveCreate": {
"headimg": "https://ddbes-repository-1257239906.cos.ap-beijing.myqcloud.com/HEAD/4073025347995417_avatar_1566529977179_1.png",
"createTime": 1566890036273,
"name": "上官海棠好看",
"userId": "4073025347995417",
"opinion": 999
}
},
"approveInfo": {
"approveId": "2463520303850980349",
"approveName": "上官海棠好看 的 自定义",
"createTime": 1566890036273,
"status": 3,
"deployId": "process12460548253054141437:1:3",
"modelId": "2463506759671612413",
"approveType": 1,
"version": 5,
"nowVersion": 5,
"taskId": "",
"isRead": 0,
"retrial": 1,
"duplicate": 1,
"isPush": 1,
"stteId": 1
}
}
}
 * */
/**审批详情数据*/
data class ApprovalDetailData(
        /**审批信息*/
        val approveInfo: ApproveInfo,
        /**审批流程数据*/
        val approveProcess: ApproveProcess,
        /**审批提交内容列表*/
        val content: List<WidgetInfo>,
        /**截止时间*/
        val endTime: Long
)

/**审批流程相关数据*/
data class ApproveProcess(
        /**审批创建者*/
        val approveCreate: SearchMemberBean,
        /**审批节点*/
        val assignee: ArrayList<Assignee>,
        /**审批抄送人*/
        val carbonCopy: ArrayList<SearchMemberBean>
)

///**审批流程中 发起人数据*/

/**审批流程中 审批节点数据*/
data class Assignee(
        /**审批类型 1：普通类型：2：会审；3：或审*/
        val approveType: Int,
        val assigneeUser: List<SearchMemberBean>
)

/**审批人*/
//data class AssigneeUser(
//        /**创建者没有content*/
//        val content: String,
//        val createTime: Long,
//        val opinion: Int,
//        /**以下三个发起人，审批人，抄送人共用*/
//        val headimg: String,
//        val name: String,
//        val userId: String
//)

///**审批抄送数据*/

/**审批详情信息*/
data class ApproveInfo(
        val approveId: String,
        val approveName: String,
        /**模板logo*/
        val modelLogo: String,
        /**1.简易流程审批；2.复杂审批；3自由流程审批*/
        val approveType: Int,
        val deployId: String,
        /**是否去重：0.否；1是 （加审人不可以在审批列表出现多次）*/
        val duplicate: Int,
        /**是否允许推送提醒 催办*/
        val isPush: Int,
        val isRead: Int,
        val modelId: String,
        val taskId: String,
        /**是否允许加审：0.否，1是（允许加审时显示加审按钮）*/
        val retrial: Int,
        /**审批状态：0.删除，1通过；2.拒绝；3.进行中；4.未提交；5.撤回；6.退回*/
        val status: Int,
        /**小分类状态，区分 未完成已完成已终止 待审批已审批已完成 删除正常未读 stteId*/
        val stteId: Int,
        val createTime: Long,
        val nowVersion: Int,
        val version: Int,
        //加审的时候是否可以加审外部联系人 0 不允许 1允许
        val outApprove: Int
)

/**加审提交数据*/
data class AprAddNodeData2(
        /**审批id*/
        val approveId: String,
        /**被加审审批人节点Id*/
        val approveAssigneeId: String,
        /**当前审批人节点Id*/
        val approveOldAssigneeId: String,
        /**被加审审批人的userId*/
        val assigneeId: String,
        /**加审增加审批人节点id后的节点id数组 */
        val assignees: ArrayList<String>,
        /**被加审人的部门id*/
        val deptId: String,
        /**团队id*/
        val organizationId: String,
        /**判断加入人员的类型*/
        val isOuter: Int
)

/**加审提交数据*/
data class AprAddNodeData3(
        /**审批id*/
        val approveId: String,
        /**被加审审批人节点Id*/
        val approveAssigneeId: List<String>,
        /**当前审批人节点Id*/
        val approveOldAssigneeId: String,
        /**被加审审批人的userId*/
        val assigneeId: List<String>,
        /**加审增加审批人节点id后的节点id数组 */
        val assignees: ArrayList<String>,
        /**被加审人的部门id*/
        val deptId: String,
        /**团队id*/
        val organizationId: String,
        /**判断加入人员的类型 0 否 1 是*/
        val isOuter: Int,
        /**加审位置 0 之前 1 之后*/
        val location: Int
)

/**处理审批提交数据*/
data class DealApprovalData(
        /**审批人节点id*/
        val approveAssigneeId: String,
        val organizationId: String,
        /**审批id*/
        val approveId: String,
        /**审批流程id*/
        val deployId: String,
        val deptId: String,
        val opinion: Boolean,
        /**工作流 任务id*/
        val taskId: String,
        val content: String
)

/**审批列表快速处理审批提交数据*/
data class FastProcessApprovalData(
        /**审批id*/
        val approveId: String,
        /**0 同意 1 拒绝*/
        val opinion: Int,
        /**团队id*/
        val organizationId: String)

/**审批评论提交数据*/
open class AprCommentData(
        val approveId: String = "",
        val content: String = "",
        val organizationId: String = "",
        val pics: ArrayList<CreateTaskCommentUploadFileBean> = arrayListOf()
)

/**评论返回数据对象*/
data class CommentListData(
        val commentId: String,
        val userId: String,
        val name: String,
        val avatar: String,
        val createTime: Long,
        val approveId: String,
        val content: String?,
        val file: List<ApprovalDetailFileBean>?
)

