package com.joinutech.approval.utils

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.database.Cursor
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import com.joinutech.common.storage.FileUtil
import java.io.File

/**
 * @className: Utils
 * @desc: 图片选择工具类
 * @author: zyy
 * @date: 2019/7/29 15:54
 * @company: joinUTech
 * @leader: ke
 */

@Deprecated("no used")
class PictureUtil(private val activity: AppCompatActivity) {
    val takePhotoCode: Int = 1001
    val takeCropCode: Int = 1002
    val takeSelectPictureCode: Int = 1003

    val permissions = arrayOf(
            Manifest.permission.CAMERA,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE
    )

    fun checkPermissions(activity: AppCompatActivity, array: Array<String>): Boolean {
        for (permission in array) {
            if (ActivityCompat.checkSelfPermission(activity, permission) == PackageManager.PERMISSION_DENIED) {
                return false
            }
        }
        return true
    }

    /**
     * 系统拍照
     * @param requestCode  拍照requestCode
     * @param targetFile 指定定拍照结果存储文件
     * The caller may pass an extra EXTRA_OUTPUT to control where this image will be written. If the EXTRA_OUTPUT is not present,
     * then a small sized image is returned as a Bitmap object in the extra field.
     * This is useful for applications that only need a small image.
     * If the EXTRA_OUTPUT is present, then the full-sized image will be written to the Uri value of EXTRA_OUTPUT.
     * 如果不指定输出文件，会返回拍照结果最小尺寸的bitmap，如果你只需要这种图片，可以不传这个参数，通过data.getExtras().get("data")获取bitmap
     * 如果指定输出文件，需要记住这个文件，拍照回调时用这个file读取拍照结果，例如imageView.setImageURI(Uri.fromFile(file))
     * */
    fun takePhoto(targetFile: File? = null, requestCode: Int = takePhotoCode) {
        val cameraIntent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
        cameraIntent.addCategory(Intent.CATEGORY_DEFAULT)
        cameraIntent.putExtra("outputFormat", Bitmap.CompressFormat.JPEG.toString())

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            cameraIntent.flags = Intent.FLAG_GRANT_READ_URI_PERMISSION
        }

        if (targetFile != null) {
            if (targetFile.exists()) {
                targetFile.delete()
            }
            cameraIntent.putExtra(
                    MediaStore.EXTRA_OUTPUT,
                    FileUtil.getContentUri(
                            activity,
                            targetFile
                    )
            )
        }
        activity.startActivityForResult(cameraIntent, requestCode)
    }

    /**
     * 拍照结果读取
     * @param intent 拍照结果intent
     * @return Uri 拍照结果Uri，可能会空，使用时判断一下
     * */
    fun onResult(context: Context, intent: Intent?, targetFile: File? = null): Any? {
        return if (targetFile != null) {
            FileUtil.getContentUri(context, targetFile)
        } else {
            intent?.extras?.get("data")
        }
    }

    /**
     * 图片裁剪
     * @param activity 当前页面
     * @param target 需要执行截图的图片Uri(7.0及以上：在裁剪的时，设置图片源的时候必须使用ContentUri，设置输出位置的时候继续使用 file://URI)
     * @param aspectX 宽度比例，低版本设置这两个属性后，截取框会变成圆形
     * @param aspectY 高度比例
     * @param width 截图结果中宽度
     * @param height 截图结果中高度
     * @param scale 是否可以缩放
     * @param needBitmap 需要返回bitmap对象时，设置为true，result中intent的data为bitmap对象；
     * 其它情况设置为false，通过指定结果存储位置Uri
     * @param resultUri 需要保存到指定位置时，设置此参数
     * */
    fun takeCrop(
            activity: AppCompatActivity,
            target: Uri,
            desFile: File? = null,
            code: Int = takeCropCode,
            aspectX: Int = 1,
            aspectY: Int = 1,
            width: Int = 320,
            height: Int = 320,
            scale: Boolean = true,
            needBitmap: Boolean = false
    ) {
        val cropIntent = Intent("com.android.camera.action.CROP")
        cropIntent.setDataAndType(target, "image/*")
        cropIntent.putExtra("crop", "true")
        cropIntent.putExtra("outputX", width)
        cropIntent.putExtra("outputY", height)
        cropIntent.putExtra("scale", scale)
        /**
         * return-data
         * 这个属性决定我们在 onActivityResult 中接收到的是什么数据，
         * 如果设置为true 那么data将会返回一个bitmap[data.Extras().getParcelable("data")]
         * 如果设置为false，则会将图片保存到本地并将对应的uri返回，当然这个uri得有我们自己设定。
         * 系统裁剪完成后将会将裁剪完成的图片保存在我们所这设定这个uri地址上。我们只需要在裁剪完成后直接调用该uri来设置图片，就可以了。
         */
        cropIntent.putExtra("return-data", needBitmap)
        if (!needBitmap && desFile != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                cropIntent.putExtra("aspectX", aspectX)
                cropIntent.putExtra("aspectY", aspectY)
//                临时访问权限
                cropIntent.flags = Intent.FLAG_GRANT_READ_URI_PERMISSION
            }
//            裁剪图片时，必须采用fileUri,不能使用ContentUri，所以使用Uri.fromFile()
            cropIntent.putExtra(MediaStore.EXTRA_OUTPUT, Uri.fromFile(desFile))
        }
//        输出格式设置
        cropIntent.putExtra("outputFormat", Bitmap.CompressFormat.JPEG.toString())
//        检测认脸
        cropIntent.putExtra("noFaceDetection", true)

        activity.startActivityForResult(cropIntent, code)
    }

    /**打开相册选择图片*/
    fun openGallery() {
        val photoPickerIntent: Intent
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            photoPickerIntent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
            photoPickerIntent.flags = Intent.FLAG_GRANT_READ_URI_PERMISSION
        } else {
            photoPickerIntent = Intent(Intent.ACTION_GET_CONTENT)
            photoPickerIntent.type = "image/*"
        }
        activity.startActivityForResult(photoPickerIntent, takeSelectPictureCode)
    }

    /**相册选择结果获取*/
    fun onGalleryResult(context: Context, uri: Uri, needBitmap: Boolean = false): Any? {

        if (needBitmap) {
            val contentResolver = context.contentResolver
            return BitmapFactory.decodeStream(contentResolver.openInputStream(uri))
        } else {
//            contentUri实例
//            content://com.android.providers.media.documents/document/image%3A100883

//            val pathHead = "file:///"

            var realPath: String? = null
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT && DocumentsContract.isDocumentUri(context, uri)) {
//                val docId = DocumentsContract.getDocumentId(uri)
//                when {
//                    isExternalStorageDocument(uri) -> {
//                        val split = docId.split(":")
//                        val type = split[0]
//                        if ("primary".equals(type, true)) {
//                            realPath = pathHead + Environment.getExternalStorageDirectory() + "/" + split[1]
//                        }
//                    }
//                    isDownloadsDocument(uri) -> {
//                        val contentUri = ContentUris.withAppendedId(
//                            Uri.parse("content://downloads/public_downloads"),
//                            docId.toLong()
//                        )
//                        return pathHead + getDataColumn(context, contentUri, null, null)
//                    }
//                    isMediaDocument(uri) -> {
//                        val split = docId.split(":".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
//                        val type = split[0]
//
//                        var contentUri: Uri? = null
//                        when (type) {
//                            "image" -> contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
//                            "video" -> contentUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
//                            "audio" -> contentUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
//                        }
//
//                        if (contentUri != null) {
//                            val selection = "_id=?"
//                            val selectionArgs = arrayOf(split[1])
//                            realPath = pathHead + getDataColumn(context, contentUri, selection, selectionArgs)
//                        }
//                    }
//                }
//            } else

//            if ("content".equals(uri.scheme, true)) {
//                realPath = pathHead + getDataColumn(context, uri, null, null)
//            } else if ("file".equals(uri.scheme, true)) {
//                realPath = pathHead + uri.path
//            }
            if ("content".equals(uri.scheme, true)) {
                realPath = getDataColumn(context, uri, null, null)
            } else if ("file".equals(uri.scheme, true)) {
                realPath = uri.path
            }
            return realPath
        }
    }

    /**查新真实存储路径*/
    private fun getDataColumn(context: Context, uri: Uri, selection: String?, selectionArgs: Array<String>?): String? {
        var cursor: Cursor? = null
        val column = MediaStore.Images.Media.DATA
        val projection = arrayOf(column)
        try {
            cursor = context.contentResolver.query(uri, projection, selection, selectionArgs, null)
            if (cursor != null && cursor.moveToFirst()) {
                val columnIndex = cursor.getColumnIndexOrThrow(column)
                return cursor.getString(columnIndex)
            }
        } finally {
            cursor?.close()
        }
        return null

    }

//    private fun isExternalStorageDocument(uri: Uri): Boolean {
//        return "com.android.externalstorage.documents" == uri.authority
//    }
//
//    private fun isDownloadsDocument(uri: Uri): Boolean {
//        return "com.android.providers.downloads.documents" == uri.authority
//    }
//
//    private fun isMediaDocument(uri: Uri): Boolean {
//        return "com.android.providers.media.documents" == uri.authority
//    }
}

//object FileUtil {
//
//    private var authority: String? = null
//
//    /**
//     * <provider
//    android:name="android.support.v4.content.FileProvider"
//    android:authorities="com.marktoo.test.fileprovider"
//    android:exported="false"
//    android:grantUriPermissions="true">
//    <meta-data
//    android:name="android.support.FILE_PROVIDER_PATHS"
//    android:resource="@xml/file_path" />
//    </provider>
//     * */
//    fun init(authority: String = "com.marktoo.test.fileprovider") {
//        this.authority = authority
//    }
//
//    fun getContentUri(context: Context, file: File?): Uri? {
//        return if (file == null || authority.isNullOrEmpty()) {
//            null
//        } else
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//                FileProvider.getUriForFile(context, authority!!, file)
//            } else {
//                Uri.fromFile(file)
//            }
//    }
//
//    fun getFilePath(context: Context, pathName: String = "temp") :String{
//        val rootDir = if (Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED) {
//            context.getExternalFilesDir(null)
//        } else {
//            context.filesDir
//        }
//        val parentDir = File(rootDir, pathName)
//        if (!parentDir.exists()) {
//            parentDir.mkdirs()
//        }
//        return parentDir.absolutePath
//    }
//
//    fun buildFile(context: Context, parent: String, fileName: String): File {
//        val rootDir = if (Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED) {
//            context.getExternalFilesDir(null)
//        } else {
//            context.filesDir
//        }
//        val parentDir = File(rootDir, parent)
//        if (!parentDir.exists()) {
//            parentDir.mkdirs()
//        }
//        return File(parentDir, fileName)
//    }
//
//}