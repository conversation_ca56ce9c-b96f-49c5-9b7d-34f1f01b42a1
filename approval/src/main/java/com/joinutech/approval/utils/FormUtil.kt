package com.joinutech.approval.utils

import android.view.View
import android.widget.EditText
import kotlin.random.Random

/**
 * @className: EditUtil
 * @desc: 编辑框工具类
 * @author: zyy
 * @date: 2019/8/1 15:02
 * @company: joinUTech
 * @leader: ke
 */

@Deprecated("no used")
object FormUtil {

    fun checkEmpty(vararg edits: EditText): Boolean {
        if (edits.isNotEmpty()) {
            for (edit in edits) {
                val text = edit.text.toString()
                return text.isNullOrEmpty()
            }
        }
        return false
    }

    fun changeState(vararg texts: View, isEnable: Boolean): Boolean {
        if (texts.isNotEmpty()) {
            for (text in texts) {
                text.isSelected = isEnable
                text.isEnabled = isEnable
                text.isClickable = isEnable
            }
        }
        return false
    }

    fun random(data: MutableList<String>): List<String> {
        val count = data.size
        val random = Random
        println("source data is $data")
        for (index in count - 1 downTo 0) {
            var rIndex = random.nextInt(index + 1)
            var temp = data[rIndex]
            data[rIndex] = data[index]
            data[index] = temp
        }
        println("result data is $data")
        return data
    }
}