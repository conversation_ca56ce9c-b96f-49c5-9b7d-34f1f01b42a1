package com.joinutech.approval.utils

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import com.joinutech.approval.AprLaunchActivity
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.service.LocationCallback
import com.joinutech.ddbeslibrary.service.LocationResult
import com.joinutech.ddbeslibrary.service.LocationService
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.PermissionUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.toast
import com.joinutech.ddbeslibrary.utils.toastShort
import com.marktoo.lib.cachedweb.LogUtil.showLog

/**
 * @Des：
 * @author: moon
 * @date: 9/21/23
 */
object AutoLocationUtil {

    private var locationService: LocationService? = null

    private var currentLocation: LocationResult? = null

    private var locationListener: MyLocationListener? = null

    interface LocationListener {
        fun location(currentLocation : LocationResult?)
    }

    @SuppressLint("CheckResult")
    fun autoLocation(context: Activity , listener: LocationListener) {
        locationListener = MyLocationListener(listener)

        if (locationService == null) {
            locationService = (context.application as BaseApplication).locationService
        }
        //百度地图定位示例，会在locationListener监听中获得位置信息===========开始=====================
        locationService?.apply {
            initLocation()
            registerListener(locationListener)
            setLocationOption(getOption())
        }
        PermissionUtils.requestPermissionActivity(
            context,
            arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),
            "当前审批需要获取你的位置信息",
            onSuccess = {
                //开启地图定位图层
                locationService?.start()
            },
            onError = {
                toast(context, "获取当前位置需要使用位置权限")
            }
        )
        //百度地图定位示例============================================结束===========================
    }

    class MyLocationListener(listener: LocationListener?) : LocationCallback {

        var mListener: LocationListener? = null

        init {
            this.mListener = listener
        }


        override fun getTag(): String = "apr"

        override fun onLocationResult(result: LocationResult) {
        }

        override fun onPoiLocationResult(result: LocationResult) {
            if (!result.locationSuccess) {
//                toast("无法获取位置信息，请检查定位是否开启")
                return
            }
            showLog("获取到审批定位信息 ${GsonUtil.toJson(result)}")
            mListener?.location(result)
            if (locationService != null) {
                // 定位成功后注销定位服务
                locationService?.apply {
                    unregisterListener(locationListener)
                    stop()
                }
            }
        }
    }

}