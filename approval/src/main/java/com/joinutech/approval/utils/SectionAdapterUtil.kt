package com.joinutech.approval.utils

import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.approval.data.ModelData
import com.joinutech.approval.data.ModelGroup
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection

/**
 * @className: SectionAdapterUtil
 * @desc: 分组适配器 section 相关
 * @author: zyy
 * @date: 2019/7/31 19:46
 * @company: joinUTech
 * @leader: ke
 */
//class GroupBean(val title: String, val position: Int)
//
//class ChildBean(val title: String, val icon: Int = 0, val desc: String = "", var isCustom: Boolean = false, var toggle: Boolean = false, val position: Int)
//
//class BigData(val group: GroupBean, val children: MutableList<ChildBean>)

interface OnSectionListener {
    /**resId：0 标识itemClick，非0 标识view点击事件*/
    fun onGroupClick(resId: Int, group: ModelGroup)

    /**resId：0 标识itemClick，非0 标识view点击事件*/
    fun onChildClick(resId: Int, child: ModelData)

    fun initHeader(itemView: View): RecyclerView.ViewHolder

    fun initChild(itemView: View): RecyclerView.ViewHolder

    fun bindHeader(holder: RecyclerView.ViewHolder, group: ModelGroup)
    fun bindChild(holder: RecyclerView.ViewHolder, childBean: ModelData)
}

class MySection(private val bigData: ModelGroup, parameters: SectionParameters,
                private val clickListener: OnSectionListener
) : StatelessSection(parameters) {

    override fun getContentItemsTotal(): Int {
        if (bigData.models != null) {
            return bigData.models.size
        }
        return 0
    }

    override fun onBindHeaderViewHolder(holder: RecyclerView.ViewHolder?) {
        clickListener.bindHeader(holder!!, bigData)
    }

    override fun onBindItemViewHolder(holder: RecyclerView.ViewHolder?, position: Int) {
        clickListener.bindChild(holder!!, bigData.models!![position])
    }

    override fun getHeaderViewHolder(view: View?): RecyclerView.ViewHolder {
        return clickListener.initHeader(view!!)
    }

    override fun getItemViewHolder(view: View?): RecyclerView.ViewHolder {
        return clickListener.initChild(view!!)
    }

}
