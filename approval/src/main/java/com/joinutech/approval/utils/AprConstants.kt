package com.joinutech.approval.utils

/**
 * @className: Constants
 * @desc: 审批全局变量
 * @author: zyy
 * @date: 2019/7/31 12:39
 * @company: joinUTech
 * @leader: ke
 */

object RouteApr {
    /**审批模板设置：管理和个性化设置*/
    const val APR_MODEL = "/approval/model"
    const val APR_MODEL_MANAGE = "/approval/model_manage"
    const val APR_MODEL_SET_PAGE = "/approval/model_set"
    /**审批模板设置：管理和个性化设置*/

    /**审批列表*/
    const val APR_FUNC_PAGE = "/approval/func"

    /**审批列表web页面*/
    const val APR_FUNC_WEB_PAGE = "/approval/func/web"

    /**审批个性化设置*/
    const val APR_FUNC_SET_PAGE = "/approval/func_set"

    /**发起审批*/
    const val APR_LAUNCH_CREATE = "/approval/create"

    /**选择人员*/
    const val APR_SELECT_PERSON = "/approval/select_person"

    /**加审页面*/
    const val PAGE_APR_ADD_PERSON = "/approval/add_approval_person"

    /**选择加审人员和抄送人员,自选审批人员*/
    const val APR_WITH_EXTERNAL_SELECT_PERSON = "/approval/select_person_with_external"

    /**部门选择*/
    const val APR_DEPT_SELECT = "/approval/deptSelect"

//    /**我发起的审批*/
//    const val APR_LAUNCH_PAGE = "/approval/launch"
//    /**待我审批*/
//    const val APR_HANDLE_PAGE = "/approval/handle"
//    /**抄送我的*/
//    const val APR_COPY_PAGE = "/approval/copy"

    /**快速审批*/
    const val APR_QUICK_PAGE = "/approval/quick"

    /**快速审批结果*/
    const val APR_QUICK_RESULT_PAGE = "/approval/quick_result"

    /**审批详情*/
    const val APR_DETAIL_PAGE = "/approval/detail"

    /**审批评论*/
    const val APR_COMMENT_PAGE = "/approval/comment"

    /**协作团队审批*/
    const val companyCooperationCompanyDetailActivity = "/approval/CooperationCompanyDetailActivity"
}