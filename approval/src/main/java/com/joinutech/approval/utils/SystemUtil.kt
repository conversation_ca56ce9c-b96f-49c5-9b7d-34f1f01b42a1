package com.joinutech.approval.utils

import android.annotation.SuppressLint
import android.app.Service
import android.content.Context
import android.os.Build
import android.telephony.TelephonyManager

/**
 * @Description: TODO
 * @Author: zhaoyy
 * @Time:  -
 * @packageName:
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */

object SystemUtil {
    @SuppressLint("MissingPermission")
    fun getImei(context: Context): String {
        val tm: TelephonyManager = context.getSystemService(Service.TELEPHONY_SERVICE) as TelephonyManager
//        Build.VERSION_CODES.M 6.0 需要开始动态申请权限
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            tm.imei
        }else{
            tm.deviceId
        }

    }
}