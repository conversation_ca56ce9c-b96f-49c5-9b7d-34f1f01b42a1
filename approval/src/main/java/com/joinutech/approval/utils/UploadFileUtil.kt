package com.joinutech.approval.utils

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.view.View
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import com.joinutech.approval.R
import com.joinutech.approval.api.ApprovalApi
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.base.MyBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.bean.CapacityBean
import com.joinutech.ddbeslibrary.bean.PanFileBean
import com.joinutech.ddbeslibrary.bean.UploadFileBean
import com.joinutech.ddbeslibrary.imbean.ImTokenBean
import com.joinutech.ddbeslibrary.request.*
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.UploadFileService
import com.joinutech.ddbeslibrary.utils.*
import com.marktoo.lib.cachedweb.LogUtil
import com.trello.rxlifecycle3.LifecycleTransformer
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import io.reactivex.FlowableOnSubscribe
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import okhttp3.MediaType
import okhttp3.RequestBody
import java.io.File

//审批详情上传图片和上传附件文件的上传流程梳理，上传图片时继续使用原代码，上传附件文件时使用该梳理的上传工具；lj
object UploadFileUtil {
    /**上传文件*/
    private var fileList = arrayListOf<UploadFileBean>()
    private var upSuccessRecord:HashMap<String,String>? = hashMapOf<String, String>()
    private var progressHash:HashMap<String,Int>? = hashMapOf<String, Int>()
    private var onProgress:(HashMap<String,Int>?)->Unit ={}
    private var onRefresh:(HashMap<String,String>?)->Unit ={}
    private var onFinish:(HashMap<String,String>?)->Unit ={}
    private var onError:()->Unit ={}

    @SuppressLint("SetTextI18n")
    fun uploadFileList(
        activity: FragmentActivity,
        fileListData: ArrayList<UploadFileBean>,
        upSuccessRecordMap: HashMap<String, String>,
        progressHashMap: HashMap<String, Int>,
        companyId: String,
        onProgressBack:(HashMap<String,Int>?)->Unit={},
        onRefreshBack:(HashMap<String,String>?)->Unit={},
        onFinishBack:(HashMap<String,String>?)->Unit={},
        onErrorBack:()->Unit={}
        ) {
        onProgress=onProgressBack
        onRefresh=onRefreshBack
        onFinish=onFinishBack
        onError=onErrorBack
        fileList = fileListData
        upSuccessRecord = upSuccessRecordMap
        progressHash = progressHashMap

        if (fileList.isEmpty()) {
            return
        }
        val currentUploadFiles = arrayListOf<UploadFileBean>()
        for (file in fileList) {
            /**已完成或者正在上传的文件不再加入上传队列*/
            /*上传已完成和上传进行中都不包含时，才加入上传队列*/
            if ((upSuccessRecord!!.isNotEmpty() && upSuccessRecord!!.containsKey(file.fileUrl))
                || (progressHash!!.isNotEmpty() && progressHash!!.containsKey(file.fileUrl))
            ) {
                continue
            } else {
                // 添加到上传记录中，进度为0
                currentUploadFiles.add(file)
                progressHash!![file.fileUrl] = 0
            }
        }
        Loggerr.i("文件上传tag", "===所有文件中剩余文件上传数量=${currentUploadFiles.size}===")
        if (currentUploadFiles.isNotEmpty()) {
            checkCapacity(activity, currentUploadFiles, companyId)
        }
    }

    /**检查文件上传空间是否满足*/
    private fun checkCapacity(
        activity: FragmentActivity,
        currentUploadFiles: ArrayList<UploadFileBean>,
        companyId: String
    ) {
        val tag = "检查存储空间"
        RxScheduleUtil.rxSchedulerHelper(
            DdbesApiUtil.getTaskService().searchCompanyCapacity(
                UserHolder.getAccessToken(), companyId
            )
        ).compose(ErrorTransformer.getInstance())
            .subscribe(object : BaseSubscriber<CapacityBean>() {
                override fun onError(ex: ApiException) {
                    RequestHelper.showLogLine(msg = ">>>$tag 返回错误<<<")
                    try {
//                        toast("上传文件失败")
                        haveCapacity(activity, currentUploadFiles)
                    } catch (e: Exception) {
                    }
                }

                override fun onComplete() {
                    RequestHelper.showLogLine(msg = ">>>$tag 获取结束<<<")
                }

                override fun onNext(result: CapacityBean?) {
                    if (result != null) {
                        RequestHelper.showLogLine(msg = ">>>$tag 返回数据<<<")
                        onCapacityResponse(activity, currentUploadFiles, result)
                    } else {
                        RequestHelper.showLogLine(msg = ">>>$tag 未返回数据<<<")
                    }
                }
            })
    }

  private  fun onCapacityResponse(
        activity: FragmentActivity,
        currentUploadFiles: ArrayList<UploadFileBean>,
        capacity: CapacityBean
    ) {
        LogUtil.showLog("查看容量结果回调 ----", "file_up__")
        val canUsed = capacity.capacity - capacity.used
        var computerFileTotalSize = 0L
        currentUploadFiles.forEach { bean ->
            computerFileTotalSize += File(bean.fileUrl).length()
        }
        //判断是否容量可用
        if (computerFileTotalSize <= canUsed) {
            Loggerr.i("文件上传tag", "===云空间可以继续使用===")
            haveCapacity(activity, currentUploadFiles)
        } else {
            val dialog = BaseCenterDialogHelper(
                activity = activity,
                onBindView = { view ->
                    run {
                        val cancel = view.findViewById<TextView>(R.id.cancel)
                        val lineV = view.findViewById<View>(R.id.line_v)
                        val tvContent = view.findViewById<TextView>(R.id.tv_content)
                        tvContent.text = "团队云盘存储空间已满，无法上传更多文件/附件。" +
                                "请登录pan.ddbes.com了解更多存储详情"
                        val tvTitle = view.findViewById<TextView>(R.id.tv_title)
                        val tvHint = view.findViewById<TextView>(R.id.tv_hint)
                        tvTitle.visibility = View.GONE
                        tvHint.visibility = View.GONE
                        cancel.visibility = View.GONE
                        lineV.visibility = View.GONE
                    }
                },
                onConfirm = {}, onCancel = {})
            dialog.initView()
            dialog.show(true)
        }
    }


    /**云空间可用时处理文件上传*/
    private fun haveCapacity(
        activity: FragmentActivity,
        currentUploadFiles: ArrayList<UploadFileBean>
    ) {
        LogUtil.showLog("容量够用准备上传 获取md5 获取文件id 上传文件 ----", "file_up__")
//        val perms = arrayOf(
//            Manifest.permission.READ_EXTERNAL_STORAGE,
//            Manifest.permission.WRITE_EXTERNAL_STORAGE
//        )
//        val tips = "选取文件需要你授权读写权限"
//        val perTips = tips
//        PermissionUtils.requestPermissionActivity(activity, perms, tips,
//            onSuccess = {
//                //先获取文件的md5，再通过后台获取上传文件的id值，再去上传文件
//                Flowable.create(FlowableOnSubscribe<HashMap<String, String>> { emitter ->
//                    try {
//                        // 去重需要上传的文件，hashMap中key为要上传文件的路径，value为文件对应的hash
//                        val hashMap = hashMapOf<String, String>()
//                        currentUploadFiles.forEach { bean ->
//                            if (hashMap.isEmpty() || !hashMap.containsKey(bean.fileUrl)) {
//                                val hashValue = StringUtils.getFileMD5(File(bean.fileUrl))
//                                if (StringUtils.isNotBlankAndEmpty(hashValue)) {
//                                    hashMap[bean.fileUrl] = hashValue
//                                }
//                            }
//                        }
//                        // 上传文件转换为map记录即将上传的数据，文件路径:对应文件的hash
//                        if (hashMap.isNotEmpty()) {
//                            emitter.onNext(hashMap)
//                        } else {
//                            emitter.onNext(hashMapOf())
//                        }
//                    } catch (e: Exception) {
//                        emitter.onNext(hashMapOf())
//                    }
//                }, BackpressureStrategy.BUFFER)
//                    .subscribeOn(Schedulers.io())
//                    .observeOn(AndroidSchedulers.mainThread())
//                    .subscribe { hashValues ->
//                        LogUtil.showLog(
//                            "获取hash完成，开始请求文件id ${GsonUtil.toJson(hashValues)}",
//                            "file_up__"
//                        )
//                        if (!hashValues.isNullOrEmpty()) {
//                            // 取出需要上传文件的 hash 值集合，请求fileId信息，判断文件在云盘是否存在
//                            val hashList = hashValues.values.toList()
//                            val map = hashMapOf<String, Any>()
//                            map["hash"] = hashList
//                            Loggerr.i("文件上传tag", "===待上传文件的哈希值===")
//                            //遍历带角标
//                           for((index,value) in hashList.withIndex()){
//                               Loggerr.i("文件上传tag", "=待上传文件=角标=${index}===哈希值==${value}=")
//                           }
//                            UploadFileService.getPanTencentId(UserHolder.getAccessToken(), map)
//                                .compose((activity as MyUseBaseActivity).bindToLifecycle())
//                                .compose(ErrorTransformer.getInstance())
//                                .subscribe(object : BaseSubscriber<List<PanFileBean>>() {
//                                    override fun onError(ex: ApiException) {
//                                        onError.invoke()
//                                        ToastUtil.show(BaseApplication.joinuTechContext, ex.message)
//                                    }
//
//                                    override fun onComplete() {
//
//                                    }
//
//                                    override fun onNext(cloudFileInfoList: List<PanFileBean>?) {
//                                        LogUtil.showLog(
//                                            "获取文件id 结果回调 ${GsonUtil.toJson(cloudFileInfoList)} ----",
//                                            "file_up__"
//                                        )
//                                        if (!cloudFileInfoList.isNullOrEmpty()) {
//                                            val emptyFileIdList =
//                                                cloudFileInfoList.filter { it.key.isNullOrBlank() || it.id.isNullOrBlank() }
//                                            if (emptyFileIdList.isNotEmpty()) {
//                                                activity.toastShort("当前返回fileId存在空异常")
//                                                activity.toastShort(
//                                                    GsonUtil.toJson(
//                                                        cloudFileInfoList
//                                                    )
//                                                )
//                                                return
//                                            }
//
//                                            for((index,value) in cloudFileInfoList.withIndex()){
//                                                Loggerr.i("文件上传tag", "=云文件对象=角标=${index}===哈希值==${value.hash}=")
//                                                Loggerr.i("文件上传tag", "=云文件对象=角标=${index}==是否存在==${value.exist}=")
//                                            }
//
//                                            // 更新已选择文件的云端信息
//                                            val temp = hashMapOf<String, UploadFileBean>()
//                                            fileList?.forEach { file ->
//                                                val hash = hashValues[file.fileUrl]
//                                                if (!hash.isNullOrBlank()) {
//                                                    val data =
//                                                        cloudFileInfoList[hashList.indexOf(hash)]
//                                                    file.fileId = data.key
//                                                    file.isUploadFlag = data.exist
//                                                    file.hash = hash
//                                                    // 要上传的文件在云端已经存在时，设置上传记录中进度为100，已上传文件中设置文件路径对应云端id即fileId
//                                                    if (data.exist) {
//                                                        Loggerr.i("文件上传tag", "===云文件已经存在=fileUrl=${file.fileUrl}==")
//                                                        progressHash!![file.fileUrl] = 100
//                                                        upSuccessRecord!![file.fileUrl] = data.id
//                                                    } else {
//                                                        // 云端不存在的文件加入需要上传文件的集合中
//                                                        Loggerr.i("文件上传tag", "===云文件不存在=fileUrl=${file.fileUrl}=需要上传=")
//                                                        temp[file.fileUrl] = file
//                                                    }
//                                                }
//                                            }
//                                            // 过滤出需要上传文件信息，云端不存在且hash不为空
////                                                        val needUploadFile = currentUploadFiles.filter { !it.isUploadFlag && !it.hash.isNullOrBlank() }.toMutableList()
//                                            //有需要上传的文件上传，没有需要的话刷新列表
//                                            if (!temp.isNullOrEmpty()){
//                                                Loggerr.i("文件上传tag", "===开始上传文件=")
//                                                dealUploadEvent(activity,temp.values.toMutableList() as ArrayList<UploadFileBean>)
//                                            }else {
//                                                Loggerr.i("文件上传tag", "===云文件都在===不需要上传===直接刷新=")
//                                               onFinish.invoke(upSuccessRecord)
//                                            }
//                                        }
//                                    }
//                                })
//                        }
//                    }
//            },
//            onError = {
//                ToastUtil.show(BaseApplication.joinuTechContext, tips)
//            } , preTips = perTips)


        Flowable.create(FlowableOnSubscribe<HashMap<String, String>> { emitter ->
            try {
                // 去重需要上传的文件，hashMap中key为要上传文件的路径，value为文件对应的hash
                val hashMap = hashMapOf<String, String>()
                currentUploadFiles.forEach { bean ->
                    if (hashMap.isEmpty() || !hashMap.containsKey(bean.fileUrl)) {
                        val hashValue = StringUtils.getFileMD5(File(bean.fileUrl))
                        if (StringUtils.isNotBlankAndEmpty(hashValue)) {
                            hashMap[bean.fileUrl] = hashValue
                        }
                    }
                }
                // 上传文件转换为map记录即将上传的数据，文件路径:对应文件的hash
                if (hashMap.isNotEmpty()) {
                    emitter.onNext(hashMap)
                } else {
                    emitter.onNext(hashMapOf())
                }
            } catch (e: Exception) {
                emitter.onNext(hashMapOf())
            }
        }, BackpressureStrategy.BUFFER)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { hashValues ->
                LogUtil.showLog(
                    "获取hash完成，开始请求文件id ${GsonUtil.toJson(hashValues)}",
                    "file_up__"
                )
                if (!hashValues.isNullOrEmpty()) {
                    // 取出需要上传文件的 hash 值集合，请求fileId信息，判断文件在云盘是否存在
                    val hashList = hashValues.values.toList()
                    val map = hashMapOf<String, Any>()
                    map["hash"] = hashList
                    Loggerr.i("文件上传tag", "===待上传文件的哈希值===")
                    //遍历带角标
                    for((index,value) in hashList.withIndex()){
                        Loggerr.i("文件上传tag", "=待上传文件=角标=${index}===哈希值==${value}=")
                    }
                    UploadFileService.getPanTencentId(UserHolder.getAccessToken(), map)
                        .compose((activity as MyUseBaseActivity).bindToLifecycle())
                        .compose(ErrorTransformer.getInstance())
                        .subscribe(object : BaseSubscriber<List<PanFileBean>>() {
                            override fun onError(ex: ApiException) {
                                onError.invoke()
                                ToastUtil.show(BaseApplication.joinuTechContext, ex.message)
                            }

                            override fun onComplete() {

                            }

                            override fun onNext(cloudFileInfoList: List<PanFileBean>?) {
                                LogUtil.showLog(
                                    "获取文件id 结果回调 ${GsonUtil.toJson(cloudFileInfoList)} ----",
                                    "file_up__"
                                )
                                if (!cloudFileInfoList.isNullOrEmpty()) {
                                    val emptyFileIdList =
                                        cloudFileInfoList.filter { it.key.isNullOrBlank() || it.id.isNullOrBlank() }
                                    if (emptyFileIdList.isNotEmpty()) {
                                        activity.toastShort("当前返回fileId存在空异常")
                                        activity.toastShort(
                                            GsonUtil.toJson(
                                                cloudFileInfoList
                                            )
                                        )
                                        return
                                    }

                                    for((index,value) in cloudFileInfoList.withIndex()){
                                        Loggerr.i("文件上传tag", "=云文件对象=角标=${index}===哈希值==${value.hash}=")
                                        Loggerr.i("文件上传tag", "=云文件对象=角标=${index}==是否存在==${value.exist}=")
                                    }

                                    // 更新已选择文件的云端信息
                                    val temp = hashMapOf<String, UploadFileBean>()
                                    fileList?.forEach { file ->
                                        val hash = hashValues[file.fileUrl]
                                        if (!hash.isNullOrBlank()) {
                                            val data =
                                                cloudFileInfoList[hashList.indexOf(hash)]
                                            file.fileId = data.key
                                            file.isUploadFlag = data.exist
                                            file.hash = hash
                                            // 要上传的文件在云端已经存在时，设置上传记录中进度为100，已上传文件中设置文件路径对应云端id即fileId
                                            if (data.exist) {
                                                Loggerr.i("文件上传tag", "===云文件已经存在=fileUrl=${file.fileUrl}==")
                                                progressHash!![file.fileUrl] = 100
                                                upSuccessRecord!![file.fileUrl] = data.id
                                            } else {
                                                // 云端不存在的文件加入需要上传文件的集合中
                                                Loggerr.i("文件上传tag", "===云文件不存在=fileUrl=${file.fileUrl}=需要上传=")
                                                temp[file.fileUrl] = file
                                            }
                                        }
                                    }
                                    // 过滤出需要上传文件信息，云端不存在且hash不为空
//                                                        val needUploadFile = currentUploadFiles.filter { !it.isUploadFlag && !it.hash.isNullOrBlank() }.toMutableList()
                                    //有需要上传的文件上传，没有需要的话刷新列表
                                    if (!temp.isNullOrEmpty()){
                                        Loggerr.i("文件上传tag", "===开始上传文件=")
                                        dealUploadEvent(activity,temp.values.toMutableList() as ArrayList<UploadFileBean>)
                                    }else {
                                        Loggerr.i("文件上传tag", "===云文件都在===不需要上传===直接刷新=")
                                        onFinish.invoke(upSuccessRecord)
                                    }
                                }
                            }
                        })
                }
            }

    }

    private fun dealUploadEvent(activity: MyUseBaseActivity, files: ArrayList<UploadFileBean>) {
        FileUploadUtil.uploadMultiFileWithProgress(
            hashList = files,
            onProgress = { filePath, _, _, percent ->
                activity.runOnUiThread {
                    // 更新进度时是按文件路径更新，所有列表中根据元素文件路径获取进度即可，多个相同文件时也不需要多个上传任务
                    if (progressHash!![filePath] == null || progressHash!![filePath]!! < 100) {
                        progressHash!![filePath] = percent
                        Loggerr.i("文件上传tag", "===刷新一次上传进度==filePath=${filePath}===")
                        onProgress.invoke(progressHash)
                    }
                }
            },
            onSuccess = { uploadResult ->
                Loggerr.i("文件上传tag", "===上传文件成功一次uploadResult.size==${uploadResult.size}===")
                if (!uploadResult.isNullOrEmpty()) {
                    // 文件上传结果转型为map，key为本地文件路径
                    val map = uploadResult.map { file -> file.filePath to file }.toMap()
                    if (!map.isNullOrEmpty()) {
                        // 上传文件结果返回
                        files.forEach { file ->
                            if (map.containsKey(file.fileUrl)) {
                                // 更新全局上传记录
//                                    upSuccessRecord[file.fileUrl] = map[file.fileUrl]?.remoteUrl
//                                            ?: ""
                                // 此处为上一步操作中请求的文件存储fileId，来自过滤后仅需要上传的文件中数据files
                                upSuccessRecord!![file.fileUrl] = file.fileId
                                // 更新上传进度记录
                                progressHash!![file.fileUrl] = 100
                                file.isUploadFlag = true
                            }
                        }

//                            buildLocalLinkUrl(files)

                        // 上传文件结果更新到选中文件记录缓存中
                        fileList.forEach { bean ->
                            if (map.containsKey(bean.fileUrl)) {
                                // 标记文件上传完成
                                bean.isUploadFlag = true
                            }
                        }

                    }
                    if (files.size == uploadResult.size) {
                        LogUtil.showLog("全部文件上传成功")
                    } else {
                        LogUtil.showLog("有文件未上传成功")
                    }

                    onFinish.invoke(upSuccessRecord)
                } else {
                    LogUtil.showLog("无文件上传成功")
                }
            },
            onError = {
                /*文件上传失败处理*/
                LogUtil.showLog("文件上传失败")
                LogUtil.showLog(it)
                onError.invoke()
            },
            tosFileType = TosFileType.PAN
        )
    }


    //=================================以上是上传文件=================================结束==============

    //格式化文件大小
    fun formatFileSize(length: Long): String? {
        var result: String? = null
        var sub_string = 0
        if (length >= 1073741824) {
            sub_string = (length.toFloat() / 1073741824).toString().indexOf(
                ".")
            result = ((length.toFloat() / 1073741824).toString() + "000").substring(0,
                sub_string + 3) + "GB"
        } else if (length >= 1048576) {
            sub_string = (length.toFloat() / 1048576).toString().indexOf(".")
            result = ((length.toFloat() / 1048576).toString() + "000").substring(0,
                sub_string + 3) + "MB"
        } else if (length >= 1024) {
            sub_string = (length.toFloat() / 1024).toString().indexOf(".")
            result = ((length.toFloat() / 1024).toString() + "000").substring(0,
                sub_string + 3) + "KB"
        } else if (length < 1024) result = java.lang.Long.toString(length) + "B"

        return result
    }


    //扫码签名后通知服务器
    fun sendSignatureId( json:String,onSuccess: (String) -> Unit, onFailer: (String) -> Unit) {
       val approveService= RetrofitClient.single_intance.getRxRetrofit().create(ApprovalApi::class.java)
        val parse = MediaType.parse("application/json; charset=utf-8")
        val requestBody = RequestBody.create(parse, json)//生成requestBody
        RxScheduleUtil.rxSchedulerHelper(approveService.sendSignatureId(requestBody))
            .compose(ErrorTransformer.getInstance())
            .subscribe(object : BaseSubscriber<Any>() {
                override fun onNext(t: Any?) {
                    onSuccess.invoke("请求成功")
                }

                override fun onError(ex: ApiException) {

                    onFailer.invoke("请求失败")
                }

                override fun onComplete() {

                }
            })

    }


}