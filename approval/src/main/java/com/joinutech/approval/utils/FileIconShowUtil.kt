package com.joinutech.approval.utils

import android.content.Context
import android.widget.ImageView
import com.joinutech.approval.R
import com.joinutech.ddbeslibrary.utils.CommonUtils.checkSuffix
import java.lang.ref.WeakReference

/**
 * @Description: 文件图标显示
 * @Author: hjr
 * @Time: 2020/1/9 15:31
 * @packageName: com.jounutech.task.utils
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
object FileIconShowUtil {

    /**
     * 显示下载文件的页面
     */
    fun showDownLoadFileIcon(name: String, imageView: ImageView, mContext: Context) {
        val context: WeakReference<Context> = WeakReference(mContext)
        when {
            checkSuffix(name,
                    context.get()!!.resources.getStringArray(com.joinutech.ddbeslibrary.R.array.rc_word_file_suffix)) -> {
                imageView.setImageResource(R.drawable.icon_approval_word_file)
                return
            }
            checkSuffix(name,
                    context.get()!!.resources.getStringArray(com.joinutech.ddbeslibrary.R.array.rc_excel_file_suffix)) -> {
                imageView.setImageResource(R.drawable.icon_approval_excel_file)
                return
            }
            checkSuffix(name,
                    context.get()!!.resources.getStringArray(com.joinutech.ddbeslibrary.R.array.rc_pdf_file_suffix)) -> {
                imageView.setImageResource(R.drawable.icon_approval_pdf_file)
                return
            }
            checkSuffix(name,
//                    context.get()!!.resources.getStringArray(R.array.rc_ppt_file_suffix)) -> {
                    context.get()!!.resources.getStringArray(com.joinutech.ddbeslibrary.R.array.rc_ppt_file_suffix)) -> {
                imageView.setImageResource(R.drawable.icon_approval_ppt_file)
                return
            }
            else ->{
                imageView.setImageResource(R.drawable.icon_approval_unknown_file)
                return
            }
        }
    }
}