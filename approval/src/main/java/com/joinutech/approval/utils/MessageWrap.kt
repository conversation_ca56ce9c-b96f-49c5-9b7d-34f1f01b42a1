package com.joinutech.approval.utils

/**
 * @className: MessageWrap
 * @desc: 通知触发消息
 * @author: zyy
 * @date: 2019/8/28 16:49
 * @company: joinUTech
 * @leader: ke
 */
@Deprecated("deprecated object")
data class MessageWrap(val type: MsgType, val content: String = "", val data: Any? = null)

enum class MsgType {
    /**评论成功*/
    COMMENT_SUCCESS,
//    /**添加人员返回结果*/
//    APR_ADD_PERSON,

    /**审批详情已读*/
    APR_DETAIL_READED,
    /**审批撤回成功*/
    APR_RECALL_SUCCESS,
    /**加审操作完成*/
    APR_ADD_NODE_SUCCESS,
    /**处理审批完成*/
    APR_PROCESS_OVER,
    /**重新提交完成，刷新我发起已终止列表，我发起列表
     * 因为从列表进入，所以需要刷新下列表*/
    APR_RE_COMMIT_SUCCESS,

    /*----------------更新小红点------------*/
    /**我发起的已完成但是未读数量更新*/
    APR_FINISH_NO_READ_REFRESH,
    /**待审批列表刷新*/
    APR_NO_PROCESS_REFRESH,
//    /**抄送我的未读数量刷新*/
//    APR_COPY_NO_READ_REFRESH
    /**合作团队的待审批列表刷新*/
    COORPERATION_APR_NO_PROCESS_REFRESH,
    /**合作团队的抄送列表刷新*/
    COORPERATION_APR_COPY_REFRESH
}
