package com.joinutech.approval

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.EditText
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.approval.adapter.CooperationCompanyApprovalAdapter
import com.joinutech.approval.data.GetAprList
import com.joinutech.approval.data.MyAprListData
import com.joinutech.approval.utils.MsgType
import com.joinutech.approval.utils.RouteApr
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.utils.*
import com.scwang.smart.refresh.layout.SmartRefreshLayout

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/3 14:50
 * @packageName: com.joinutech.approval
 * @Company: JoinuTech
 */
class CooperationCompanyCopyFragment : AprBaseFragment() {

    override val layoutRes: Int = R.layout.fragment_cooperation_company_copy

    override fun openEventBus(): Boolean {
        return false
    }

    private lateinit var searchEdit: EditText
    private lateinit var rvTempList: RecyclerView
    private lateinit var srlRefresh: SmartRefreshLayout
    private lateinit var noDataViewPage: PageEmptyView
    private lateinit var adapter: CooperationCompanyApprovalAdapter
    private var approvalList = arrayListOf<MyAprListData>()
    private var approvalType = 0
    private var companyId = ""
    private var keyWord = ""
    private val startPage = 1
    private val pageSize = 20
    private var page: Int = startPage
    private var pageLength = pageSize

    override fun initView(rootView: View) {
        companyId = arguments?.getString(COMPANY_ID)!!
        rvTempList = rootView.findViewById(R.id.rv_list)
        srlRefresh = rootView.findViewById(R.id.srl_refresh)
        noDataViewPage = rootView.findViewById(R.id.layout_empty_layout)
        noDataViewPage.hide()
        searchEdit = rootView.findViewById(R.id.searchEdit)
        rvTempList.layoutManager = LinearLayoutManager(mActivity)
        srlRefresh.setEnableRefresh(false)
        srlRefresh.setEnableLoadMore(false)
        srlRefresh.setEnableAutoLoadMore(false)
    }

    companion object {
        private const val COMPANY_ID = "company_id"
        fun newInstance(companyId: String): CooperationCompanyCopyFragment {
            return CooperationCompanyCopyFragment().apply {
                arguments = Bundle().apply {
                    putString(COMPANY_ID, companyId)
                }
            }
        }
    }

    override fun initLogic() {
        super.initLogic()
        adapter = CooperationCompanyApprovalAdapter(mActivity, approvalList)
        adapter.setModelType(3)
        adapter.setSubModelType(0)
        adapter.setItemClickListener(object : ItemClickListener {
            override fun onItemClick(position: Int) {
                adapter.mData[position].isRead = 1
                adapter.notifyItemChanged(position)
                val bundle = Bundle()
                bundle.putString("companyId", companyId)
                bundle.putString("approveId", adapter.mData[position].approveId)
                bundle.putInt("modelIndex", 3)
                bundle.putBoolean("isCooperationCompany", true)
                if (approvalType == 0) {
                    bundle.putInt("listIndex", 2)
                    jump(RouteApr.APR_DETAIL_PAGE, bundle, 1010)
                } else {
                    jump(RouteApr.APR_DETAIL_PAGE, bundle)
                }
            }

        })
        rvTempList.adapter = adapter
        srlRefresh.setOnLoadMoreListener {
            if (approvalList.size > 0 && approvalList.size % pageLength > 0) {
                srlRefresh.setEnableLoadMore(false)
                srlRefresh.finishLoadMore(200)
            } else {
                page++
                srlRefresh.finishLoadMore(1200)
                getData()
            }
        }
        getObserver()
        searchEdit.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (s != null) {
                    keyWord = s.toString()
                    getData()
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        })
        getData()
    }

    private fun getObserver() {
        viewModel?.getMyAprListResult?.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                if (page == startPage) {
                    approvalList.clear()
                }
                if (!it.approveList.isNullOrEmpty()) {
                    approvalList.addAll(it.approveList)
                }
                if (!approvalList.isNullOrEmpty()) {
                    noDataViewPage.hide()
                    srlRefresh.visibility = View.VISIBLE
                } else {
                    noDataViewPage.show()
                    srlRefresh.visibility = View.GONE
                }
                /**抄送审批的红点数量更新*/
                if (approvalType == 0 && StringUtils.isEmpty(keyWord)) {
                    //只有type =0和关键词为空时全部的数量记录才准确
                    showLog("更新抄送审批数量")
                    EventBusUtils.sendEvent(EventBusEvent(
                            MsgType.COORPERATION_APR_COPY_REFRESH.name,
                            data = it.count))
                }
                adapter.notifyDataSetChanged()
            }, onError = { code, msg ->
                if (page == startPage) {
                    noDataViewPage.show()
                    srlRefresh.visibility = View.GONE
                }
                if (page > startPage) {
                    page--
                }
                showToast(msg)
            }, onDefault = { msg ->
                if (page == startPage) {
                    noDataViewPage.show()
                    srlRefresh.visibility = View.GONE
                }
                if (page > startPage) {
                    page--
                }
                showToast(msg)
            })

        })
    }

    fun setApprovalTypeRefreshData(approvalType: Int) {
        page = startPage
        this.approvalType = approvalType
        getData()
    }

    private fun getData() {
        showLoading()
        val getMyAprList = GetAprList(companyId, type = 3, carbonStatus = approvalType + 1,
                start = page, length = pageLength)
        viewModel?.getMyAprList(getMyAprList.getMap(3))
    }
}