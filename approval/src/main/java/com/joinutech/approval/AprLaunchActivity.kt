package com.joinutech.approval

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.google.gson.reflect.TypeToken
import com.joinutech.approval.adapter.ApprovalTypeSelectDialogAdapter
import com.joinutech.approval.data.*
import com.joinutech.approval.temp.ApprovalTypeDialogBean
import com.joinutech.approval.utils.MsgType
import com.joinutech.approval.utils.RouteApr
import com.joinutech.common.adapter.MultiTypeAdapterNoReuse
import com.joinutech.common.base.isApproval
import com.joinutech.common.base.isFuture
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.TimePickerUtil
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.base.BaseApplication.Companion.approCacheMap
import com.joinutech.ddbeslibrary.bean.*
import com.joinutech.ddbeslibrary.databinding.CommonListWithEditLayoutBinding
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.service.LocationCallback
import com.joinutech.ddbeslibrary.service.LocationResult
import com.joinutech.ddbeslibrary.service.LocationService
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.widget.activity.TaskImagePreviewActivity
import com.joinutech.ddbeslibrary.widget.contrarywind.view.CustomWheelView
import com.joinutech.ddbeslibrary.widget.wheel.builder.OptionsPickerBuilder
import com.joinutech.ddbeslibrary.widget.wheel.listener.OnOptionsSelectListener
import com.joinutech.ddbeslibrary.widget.wheel.view.OptionsPickerView
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.*
import kotlin.collections.ArrayList

/**
 * @className: AprLaunchActivity
 * @desc: 发起审批页面
 * @author: zyy
 * @date: 2019/8/2 9:12
 * @company: joinUTech
 * @leader: ke
 */
//审批详情tcp，比如点击加班进入的页面
@Route(path = RouteApr.APR_LAUNCH_CREATE)
class AprLaunchActivity(
//    override val contentViewResId: Int = R.layout.common_list_with_edit_layout
    override val contentViewResId: Int = com.joinutech.ddbeslibrary.R.layout.common_list_with_edit_layout
) : AprBaseActivity<CommonListWithEditLayoutBinding>() {

    override fun initImmersion() {
        mImmersionBar?.apply {
            keyboardEnable(true).keyboardMode(
                WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN
            )
        }
    }

    //路由的使用（在approval模块使用其他模块的选择城市功能,因为不能直接打开对应页面，需要使用路由）
    //路由的使用，，，获取provider对象
    @JvmField
    @Autowired(name = "/Personal/provider")
    var personProvider: RouteServiceProvider? = null

    private var modelId: String? = null
    private var companyId = ""

    private var reCommitData: HashMap<String, String>? = null
    private var dialogIsShow = false

    override fun initView() {
        mActivity = this
        if (intent != null && intent!!.extras != null) {
            if (intent!!.extras!!.containsKey(ConsKeys.COMPANY_ID)) {
                companyId = intent!!.extras!!.getString(ConsKeys.COMPANY_ID, "")
                if (companyId.isNullOrBlank()) {
                    companyId = CompanyHolder.getCurrentOrg()?.companyId ?: ""
                }
            }
            if (intent!!.extras!!.containsKey(ConsKeys.MODEL_ID)) {
                modelId = intent?.extras?.getString(ConsKeys.MODEL_ID, "")
            }
            if (intent!!.extras!!.containsKey(ConsKeys.TARGET_ID)) {
                aprId = intent?.extras?.getString(ConsKeys.TARGET_ID, "")
            }
            if (intent!!.extras!!.containsKey("widgets")) {
                val json = intent!!.extras!!.getString("widgets")
                if (json != null && json.isNotBlank()) {
                    reCommitData = GsonUtil.fromJson(json)
                }
            }
        }
        initListView()
    }

    private lateinit var mActivity: AppCompatActivity
    private lateinit var adapter: MultiTypeAdapterNoReuse<WidgetInfo>
    private val limit = 50 // TODO: 2019/9/16 选择图片限制数量
//    private val selector = PicSelector(this, limit)

    /**缓存所有的属性组件，访问组件索引，获取组件内容*/
    private lateinit var properties: HashMap<Int, AprProperty>

    /**当前编辑组件切换缓存*/
    private var targetProperty: AprProperty? = null

    /**上传文件记录信息*/
    private val uploadResult = hashMapOf<String, String>()

    /**审批属性列表数据*/
    private val propertyDataList = arrayListOf<WidgetInfo>()

    private var rvList: RecyclerView? = null
    private var sendBtn: TextView? = null

    private fun initListView() {
        sendBtn = findViewById(com.joinutech.ddbeslibrary.R.id.send_btn_tv)
        sendBtn?.setOnClickListener {
            createApprovalData()
        }

        findViewById<View>(R.id.layout_empty_layout).visibility = View.GONE
        properties = hashMapOf()

        rvList = findViewById(R.id.rv_list)
        rvList!!.layoutManager = LinearLayoutManager(this)
        adapter = MultiTypeAdapterNoReuse(//审批布局，显示布局tcp，梳理步骤3
            activity = this, data = propertyDataList,
            generateViewType = { _: Int, data: WidgetInfo ->
                data.type
            },
            generateLayoutId = { type ->
                when (type) {
                    0 -> {
                        R.layout.property_model_desc
                    }
                    1 -> {
                        /**短文本输入*/
                        /**短文本输入*/
                        /**单行多行输入 发起和预览不一致，发起：短文本右对齐，预览都左对齐*/
                        /**单行多行输入 发起和预览不一致，发起：短文本右对齐，预览都左对齐*/
                        R.layout.property_text_input
                    }
                    2 -> {
                        /**长文本输入*/
                        /**长文本输入*/
                        R.layout.property_text_input_multi
                    }
                    3, 4 -> {//有限时提交
                        /**时间点和时间段 发起右对齐，预览左对齐*/
                        /**时间点和时间段 发起右对齐，预览左对齐*/
                        R.layout.property_time_selector
                    }
                    13 -> {//有限时提交
                        /**时间点，可以动态增加时间点*/
                        /**时间点，可以动态增加时间点*/
                        R.layout.property_time_selector_dynamic
                    }
                    14 -> {//选择城市
                        R.layout.property_city_selector
                    }
//                        5 -> {
//                            /**选择持续时间 发起右对齐，预览左对齐*/
//                            R.layout.property_text_selector
//                        }
                    5, 6, 7, 8, 9 -> {
                        /**单选框多选框 发起右对齐，预览左对齐*/
                        /**单选框多选框 发起右对齐，预览左对齐*/
                        R.layout.property_text_selector
                    }
//                        8 -> {
//                            /**
//                             * 人员选择，跳转页面选择人员，personType：单选0 多选1
//                             * 发起是只显示名字，右对齐
//                             * 预览时显示头像（圆形）和名字，左对齐，需要用另一个布局
//                             * */
//                            R.layout.property_text_selector
//                        }
//                        9 -> {
//                            /**
//                             * 选择关键字类型，人员，职位，部门自动获取，手动选择部门
//                             * 发起右对齐，预览左对齐
//                             * */
//                            R.layout.property_text_selector
//                        }
                    10 -> {
                        /**
                         * 图片选择布局
                         * 发起时：显示提示文本
                         * 预览时：不显示提示文本*/
                        /**
                         * 图片选择布局
                         * 发起时：显示提示文本
                         * 预览时：不显示提示文本*/
                        R.layout.property_select_picture
                    }
                    11 -> {
                        /**
                         * 定位类型
                         * 发起右对齐，自动显示定位logo，
                         * 预览左对齐，显示定位logo
                         * */
                        /**
                         * 定位类型
                         * 发起右对齐，自动显示定位logo，
                         * 预览左对齐，显示定位logo
                         * */
                        R.layout.property_location_text
                    }
                    /**
                     * 发起时：自由流程时，自己增加两项
                     * 预览时：另外一套布局，不能拖动
                     * */
                    /**
                     * 发起时：自由流程时，自己增加两项
                     * 预览时：另外一套布局，不能拖动
                     * */
                    20 -> {//添加自选审批人布局
                        /**
                         * 发起时：自由流程时，自己增加两项
                         * 预览时：另外一套布局，不能拖动
                         * */
                        /**
                         * 发起时：自由流程时，自己增加两项
                         * 预览时：另外一套布局，不能拖动
                         * */
                        R.layout.property_select_node
                    }
                    12 -> {
                        /**
                         * 附件类型
                         */
                        /**
                         * 附件类型
                         */
                        R.layout.property_approval_file
                    }
                    30 -> {//添加抄送人布局
                        /**
                         * 抄送人员选择
                         * 发起时：显示头像和名字，上传数据只存储id数组
                         * 预览时：显示头像和名字，预览时解析数据，包含头像、userId和名字
                         * */
                        /**
                         * 抄送人员选择
                         * 发起时：显示头像和名字，上传数据只存储id数组
                         * 预览时：显示头像和名字，预览时解析数据，包含头像、userId和名字
                         * */
                        R.layout.property_select_workmate
                    }
                    31 -> {
                        /**
                         * 默认抄送人员
                         * 发起时：显示头像和名字，上传数据只存储id数组
                         * 预览时：显示头像和名字，预览时解析数据，包含头像、userId和名字
                         * */
                        /**
                         * 默认抄送人员
                         * 发起时：显示头像和名字，上传数据只存储id数组
                         * 预览时：显示头像和名字，预览时解析数据，包含头像、userId和名字
                         * */
                        R.layout.property_select_workmate
                    }
//                        40 -> {
//                            /**
//                             * 只有预览时显示
//                             * 评论内容显示布局
//                             * */
//                            R.layout.property_comment
//                        }
                    else -> {
//                            自动获取（当前人，团队职位，团队部门），
                        R.layout.property_text_selector
                    }
                }
            },
            onBindItem = { position: Int, data: WidgetInfo, view: View ->
                showLog(data)
                data.isEdit = true
                val property = when (data.type) {
                    // 0 审批模板hint显示
                    0 -> {
                        val hintProperty = ApprovalHintProperty(data, view)
                        hintProperty.initView()
                        hintProperty
                    }
                    // 1和2：单行多行输入 发起和预览不一致，
                    // 发起时：短文本右对齐，
                    // 预览时：都左对齐
                    1 -> {
                        // 短文本
                        val editProperty = EditProperty(data, view)
                        editProperty.initView()
                        if (reCommitData != null && reCommitData!!.isNotEmpty()
                            && data.widgetId in reCommitData!!.keys
                        ) {
                            editProperty.setContent(reCommitData!![data.widgetId]!!)
                        }
                        editProperty
                    }//已完成缓存
                    2 -> {
                        // 长文本
                        val editProperty = EditProperty(data, view)
                        editProperty.initView()
                        if (reCommitData != null &&
                            reCommitData!!.isNotEmpty() &&
                            data.widgetId in reCommitData!!.keys
                        ) {
                            editProperty.setContent(reCommitData!![data.widgetId]!!)
                        }
                        editProperty
                    }//已完成缓存
                    3 -> {
                        // 时间点 long.toString()
                        val timeProperty = TimeProperty(data, view, true)
                        timeProperty.initView()
                        timeProperty.addTimeSelectListener(
                            begin = {
                                //tcp限时提交，点击界面的开始时间，触发下面的代码
                                var startTime = 0L
                                var endTime = 0L
                                if (data.isLimit == 1) {
                                    timePickerUtil = null
                                    //注释的代码可以把时间选择限制在当月
//                                    startTime=TimePickerUtil.getStartTime()
//                                    endTime=TimePickerUtil.getEndTime()
//                                    Loggerr.i("限制时间", "===starttime=${XUtil.turnToTimeStr(startTime)}===")
//                                    Loggerr.i("限制时间", "===endtime=${XUtil.turnToTimeStr(endTime)}===")
                                }
                                showCustomPicker(
                                    data.dateChoose,
                                    startTime = startTime,
                                    endTime = endTime,
                                    result = {
                                        timeProperty.setBeginTime(it)
                                    })
                            },
                            end = {

                            })
                        timeProperty
                    }//已完成
                    4 -> {
                        // 时间段 startTime endTime: long.toString()
                        val timeProperty = TimeProperty(data, view, false)
                        timeProperty.initView()
                        timeProperty.addTimeSelectListener(
                            begin = {//tcp限时提交，点击界面的开始时间，触发下面的代码
                                var startTime = 0L
                                var endTime = 0L
                                if (data.isLimit == 1) {
                                    timePickerUtil = null
//                                    startTime=TimePickerUtil.getStartTime()
//                                    endTime=TimePickerUtil.getEndTime()
                                }
                                showCustomPicker(
                                    data.dateChoose,
                                    startTime = startTime,
                                    endTime = endTime,
                                    result = { beginDate ->
                                        timeProperty.setBeginTime(beginDate)
                                        if (data.dateResult != null
                                            && data.dateResult!!.endTime.toLong() > 0L &&
                                            beginDate.time >= data.dateResult!!.endTime.toLong()
                                        ) {
                                            timeProperty.setEndTime(null)
                                        }
                                    })
                            },
                            end = {//tcp限时提交，点击界面的结束时间，触发下面的代码
                                var startTime = 0L
                                var endTime = 0L
                                if (data.isLimit == 1) {
                                    timePickerUtil = null
//                                    startTime=TimePickerUtil.getStartTime()
//                                    endTime=TimePickerUtil.getEndTime()
                                }
                                showCustomPicker(
                                    data.dateChoose,
                                    startTime = startTime,
                                    endTime = endTime,
                                    result = { endDate ->
                                        if (data.dateResult != null && data.dateResult!!.startTime.toLong() > 0L &&
                                            // 只选择年月日时可以是同一天
                                            ((data.dateChoose == 1 && endDate.time >= data.dateResult!!.startTime.toLong())
                                                    ||
                                                    // 带时分秒时，不能是同一天
                                                    (data.dateChoose != 1 && endDate.time > data.dateResult!!.startTime.toLong()))
                                        ) {
                                            timeProperty.setEndTime(endDate)
                                        } else {
                                            timeProperty.setEndTime(null)
                                            toastShort("开始时间应早于结束时间")
                                        }
                                    })
                            })
                        timeProperty
                    }//已完成
                    13 -> {
                        // 时间点，可以动态增加时间点
                        val timeProperty = TimeDynamicCellProperty(data, view)
                        timeProperty.initView()
                        timeProperty.listener = object : OnTimeChooseListener {
                            override fun onChooseTime(index: Int) {
                                var startTime = 0L
                                var endTime = 0L
                                if (data.isLimit == 1) {
                                    timePickerUtil = null
//                                    startTime=TimePickerUtil.getStartTime()
//                                    endTime=TimePickerUtil.getEndTime()
                                }
                                showCustomPicker(
                                    data.dateChoose,
                                    startTime = startTime,
                                    endTime = endTime,
                                    result = { date ->
                                        timeProperty.setTime(index, date)
                                    })
                            }
                        }
                        timeProperty
                    }
                    5 -> {
                        // 持续时间选择 String
                        val textProperty = TextProperty(data, view)
                        textProperty.initView()
                        textProperty.addListener {
                            showCustomRangePicker(it) { s1: String, s2: String, s3: String ->
                                textProperty.setContent("$s1 $s2 时$s3 分")
                            }
                        }
                        textProperty
                    }//已完成
                    6, 7 -> {
                        // 单选多选选项  存储选项内容
                        val textProperty = TextProperty(data, view)
                        textProperty.initView()
                        textProperty.addListener {
                            targetProperty = textProperty
                            showListSelectData()
                        }
                        textProperty
                    }//已完成
                    8 -> {
                        // 存储选择人员id或id数组
                        val textProperty = TextProperty(data, view)
                        textProperty.initView()
                        textProperty.addListener {
                            targetProperty = textProperty
                            when (data.personType) {
                                0 -> {
                                    // 单选人员
                                    if (!companyId.isNullOrBlank()) {
                                        val bundle = Bundle()
                                        bundle.putString("title", "选择人员")
                                        bundle.putString("companyId", companyId)
                                        bundle.putInt("maxSelect", 1)

                                        jump(RouteApr.APR_SELECT_PERSON, bundle, 801)//选择人员，单选
                                    }
                                }
                                else -> {
                                    // 多选人员
                                    if (!companyId.isNullOrBlank()) {
                                        val bundle = Bundle()
                                        bundle.putString("title", "选择人员")
                                        bundle.putString("companyId", companyId)
                                        if (!data.personResult.isNullOrEmpty()) {
                                            bundle.putString(
                                                "selectUserIdListString",
                                                GsonUtil.toJson(data.personResult?.map { it.userId })
                                            )
                                        }
                                        bundle.putInt("maxSelect", -1)//加班人员，离职交接人员，用印人
                                        jump(RouteApr.APR_SELECT_PERSON, bundle, 802)//选择人员，多选
                                    }
                                }
                            }
                        }
                        textProperty
                    }//已完成
                    9 -> {
                        // 选择关键字类型，人员，职位，部门自动获取，手动选择部门
                        // 存储自动获取信息内容：姓名，职位，部门名称
                        val textProperty = TextProperty(data, view)
                        textProperty.initView()
                        textProperty.addListener {
                            //手动获取部门
                            if (data.wordType == 2 && data.autoDept == 0) {
                                if (!companyId.isNullOrBlank()) {
                                    targetProperty = textProperty
                                    jump(RouteApr.APR_DEPT_SELECT, requestCode = 803)
                                }
                            }
                        }
                        if (userOrgInfo != null) {
                            when (data.wordType) {
                                0 -> {
                                    // 姓名
                                    data.personResult = arrayListOf(SelectData("", "", userId!!))
                                    textProperty.setContent(userName ?: "未获取到")
                                }
                                1 -> {
                                    // 职位
                                    textProperty.setContent(
                                        userOrgInfo?.managerName
                                            ?: "未获取到"
                                    )
                                }
                                2 -> {
                                    // 部门
                                    if (data.autoDept == 1) {
                                        textProperty.setContent(
                                            userOrgInfo?.deptName
                                                ?: "未获取到"
                                        )
                                    }
                                }
                            }
                        }
                        textProperty
                    }//已完成
                    10 -> {
                        // 选择图片 上传图片结果的字符串数组
                        showLog("重建图片选择属性控件")
                        val selectPicProperty =
                            SelectPicProperty(data, view, uploadResult, limit = limit)
                        selectPicProperty.initView()
                        selectPicProperty.setViewModel(this as AprBaseActivity<ViewBinding>, companyId)
                        selectPicProperty.listener = object : OnSelectPicListener {
                            override fun onResult(list: List<String>) {
                            }

                            override fun onSelect(property: AprProperty, selectedCount: Int) {
                                hindSoftMethod()
                                selectPicProperty.setAprId(aprId)
                                targetProperty = property
                                dealPicSelect(selectedCount)
                            }

                            override fun onPreview(position: Int, picList: ArrayList<String>) {
                                val intent = Intent(mContext, TaskImagePreviewActivity::class.java)
                                val previewDataBean =
                                    TaskImagePreviewActivity.PreviewDataBean(
                                        position, picList, true
                                    )
                                val bundle = Bundle()
                                bundle.putSerializable("previewData", previewDataBean)
                                intent.putExtras(bundle)
                                mContext!!.startActivity(intent)
                            }
                        }
                        selectPicProperty
                    }//已完成
                    11 -> {
                        // 定位信息选择 定位对象json
                        val locationProperty = LocationProperty(data, view)
                        locationProperty.initView()
                        if (data.addressChoose == 1) {//手动定位
                            locationProperty.addListener {
                                targetProperty = it
                                ARouter.getInstance()
                                    .build(RouteIm.locationActivity)
                                    .withString("type", "selectLocation")
                                    .withString("title", "请选择地点")
                                    .withString("rightTitle", "完成")
                                    .navigation(this, ATTENDANCE_LOCATION)
                            }
                        } else {
                            if (currentLocation != null) {
                                data.addressResult = AddressResult(
                                    address = currentLocation!!.name,
                                    positioning = currentLocation!!.address ?: "未获取到",
                                    longitude = currentLocation!!.lng,
                                    latitude = currentLocation!!.lat
                                )
                                locationProperty.setContent(
                                    name = currentLocation?.name ?: "未获取到",
                                    detail = currentLocation?.address ?: "未获取到"
                                )
                            }
                        }
                        locationProperty
                    }//已完成
                    12 -> {
                        //附件类型，纯展示，暂无业务逻辑
                        val selectFileProperty=  ApprovalFileProperty(data, view)
                        selectFileProperty.initView()
                        selectFileProperty.setViewModel(this as AprBaseActivity<ViewBinding> ,companyId)
                        selectFileProperty.listener=object:OnSelectFileListener{
                            override fun onSelect(property: AprProperty, selectedCount: Int) {
                              //选择附件
                                targetProperty=property
                                (ARouter.getInstance().build(RouteProvider.IM_PROVIDER).navigation() as RouteServiceProvider)
                                    .openPageWithResult(this@AprLaunchActivity,"selectFile",Bundle(),{})
                            }

                            override fun onPreview(uploadFileBean: UploadFileBean) {
                                val bundle=Bundle()
                                bundle.putString("fileLocalPath",uploadFileBean.fileUrl)
                                bundle.putString("fileTrueName",uploadFileBean.fileName)
                                bundle.putString("fileShowName",uploadFileBean.fileName)
                                (ARouter.getInstance().build(RouteProvider.IM_PROVIDER).navigation() as RouteServiceProvider)
                                    .openPageWithResult(this@AprLaunchActivity,"previewFile",
                                    bundle,{})
                            }

                            override fun onResult(list: List<String>) {

                            }
                        }
                        selectFileProperty
                    }
                    14 -> {//选择城市
                        val cityProperty = CityProperty(data, view)
                        cityProperty.initView()
                        cityProperty.addListener {
                            targetProperty = cityProperty
                            //选择城市
                            Loggerr.i("选择城市", "====开始执行====")
                            val bundle = Bundle()
                            bundle.putString("cityLevel", "市")//选择到市即可
                            bundle.putString("pageTitle", "选择城市")
                            personProvider?.openPageWithResult(this, "city_selector", bundle) {}
                        }
                        cityProperty
                    }
                    20 -> {//添加自选审批人
                        // 选择节点 用户id数组
                        val selectNodeProperty = SelectNodeProperty(data, view)
                        selectNodeProperty.initView()
                        selectNodeProperty.listener = object : OnSelectNodeListener {
                            override fun onSelect(property: AprProperty) {
                                hindSoftMethod()
                                targetProperty = property
                                if (!companyId.isNullOrBlank()) {// 选择审批人员，单选
                                    val unSelectPerson = arrayListOf<String>()
                                    if (!data.selectNodeResult.isNullOrEmpty()) {
                                        val selectedIds = data.selectNodeResult!!
                                            .filter { !it.userId.isNullOrBlank() }
                                            .map { it.userId }.toSet()
                                        if (selectedIds.isNotEmpty()) {
                                            unSelectPerson.addAll(selectedIds)
                                        }
                                    }
                                    selectApprovalPerson(
                                        companyId = companyId,
                                        title = "选择审批人员", unSelectPerson = unSelectPerson,
                                        singleSelect = true, requestCode = 2001
                                    )
                                }
                            }

                            override fun onPreview(
                                position: Int,
                                picList: ArrayList<ContactModel>
                            ) {

                            }

                            override fun onResult(list: List<ContactModel>) {

                            }

                        }
                        selectNodeProperty
                    }//已完成
                    30 -> {
                        // 抄送人员 人员id数组
                        val selectMates = SelectMateProperty(data, view)
                        selectMates.initView()
                        selectMates.listener = object : OnSelectMateListener {

                            override fun onSelect(property: AprProperty) {
                                hindSoftMethod()
                                targetProperty = property
                                val unSelectPerson = arrayListOf<String>()
                                if (modelDetail != null && !modelDetail!!.carbonCopy.isNullOrEmpty()) {
                                    val defaultIds =
                                        modelDetail!!.carbonCopy.map { it.userId }.toSet()
                                    if (defaultIds.isNotEmpty()) {
                                        unSelectPerson.addAll(defaultIds)
                                    }
                                }
                                if (!data.selectCopyResult.isNullOrEmpty()) {
                                    val selectedIds = data.selectCopyResult!!
                                        .filterNot { it.userId.isNullOrBlank() }
                                        .map { it.userId }
                                        .toSet()
                                    if (selectedIds.isNotEmpty()) {
                                        unSelectPerson.addAll(selectedIds)
                                    }
                                }
//                                  // TODO: 2019/9/30 增加过滤默认抄送人数据
                                if (!companyId.isNullOrBlank()) {//选择抄送人员，多选，过滤已选择人员
                                    selectApprovalPerson(
                                        companyId = companyId,
                                        title = "选择抄送人员", unSelectPerson = unSelectPerson,
                                        singleSelect = false, requestCode = 3001
                                    )
                                }
                            }

                            override fun onPreview(
                                position: Int,
                                picList: ArrayList<SearchMemberBean>
                            ) {

                            }

                            override fun onResult(list: List<SearchMemberBean>) {

                            }

                        }
                        selectMates
                    }//已完成
                    31 -> {
                        // 默认抄送人员 人员id数组
                        val selectPerson = SelectPersonProperty(data, view) {
                            if (it.headimg == "--" && !data.selectCopyResult.isNullOrEmpty()) {
                                val memberList =
                                    arrayListOf<TaskDetailMemberBean>()
                                for (member in data.selectCopyResult!!) {
                                    memberList.add(
                                        TaskDetailMemberBean(
                                            member.headimg, 0,
                                            0, 0,
                                            member.userId, member.name
                                        )
                                    )
                                }
                                val bundle = Bundle()
                                bundle.putSerializable("gridViewList", memberList)
                                bundle.putString("title", "抄送人")
                                jump(RouteTask.taskAllJoiner, bundle)
                            }
                        }
                        selectPerson.initView()
                        // TODO: 2019/9/30 新增默认抄送人数据
                        selectPerson
                    }
//                        40 -> {
//                            /**评论数据*/
//                            val commentProperty = CommentProperty(data, view)
//                            commentProperty.initView()
//                            commentProperty
//                        }
                    else -> {
                        val textProperty = TextProperty(data, view)
                        textProperty.initView()
                        textProperty
                    }
                }
                //将所有属性被填写的内容都放在这个集合中properties
                properties[position] = property
            },
            onItemClick = { _: Int, _: WidgetInfo, _: View ->
            }
        )
//        rv_list.addItemDecoration(ListItemDecoration(DeviceUtil.dip2px(this, 5f)))
        rvList!!.adapter = adapter
    }

    //选择人员弹框
    private fun selectApprovalPerson(
        companyId: String, title: String,
        unSelectPerson: ArrayList<String> = arrayListOf(),
        singleSelect: Boolean = true, requestCode: Int
    ) {

        fun onSelectPerson(position: Int = 0) {
            val bundle = Bundle()
            bundle.putString("title", title)
            bundle.putString("companyId", companyId)
            bundle.putInt("maxSelect", if (singleSelect) 1 else -1)
            bundle.putStringArrayList("unSelectPerson", unSelectPerson)
            bundle.putInt("personType", position)
            jump(RouteApr.APR_SELECT_PERSON, bundle, requestCode)
        }

        if (0 == modelDetail?.outApprove) {// 不能选择外部联系人
            onSelectPerson()
        } else {// 选择外部联系人
            val dialog = object : DialogHolder(
                this,
//                R.layout.dialog_white_top_corner, Gravity.BOTTOM
                com.joinutech.ddbeslibrary.R.layout.dialog_white_top_corner, Gravity.BOTTOM
            ) {
                override fun bindView(dialogView: View) {
                    val cancel = dialogView.findViewById<TextView>(R.id.cancel)
                    cancel.setOnClickListener {
                        dialog?.dismiss()
                    }
                    val typeListRv = dialogView.findViewById<RecyclerView>(R.id.rv_list)
                    typeListRv.layoutManager = LinearLayoutManager(mContext)
                    val titleList = arrayListOf<ApprovalTypeDialogBean>()
                    titleList.add(ApprovalTypeDialogBean("团队内部人员"))
                    titleList.add(ApprovalTypeDialogBean("外部协作人员"))
                    val adapter = ApprovalTypeSelectDialogAdapter(mContext!!, titleList)
                    typeListRv.adapter = adapter
                    adapter.setItemClickListener(object : ItemClickListener {
                        override fun onItemClick(position: Int) {
                            onSelectPerson(position)
                            dialog?.dismiss()
                        }
                    })
                }
            }
            dialog.initView()
            dialog.show(true)
        }
    }

    fun hindSoftMethod() {
        showLog("尝试关闭输入法。。。")
        val inputMethodManager = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        if (inputMethodManager.isActive &&
            currentFocus != null && currentFocus?.windowToken != null
        ) {
            showLog("关闭输入法中。。。")
            inputMethodManager.hideSoftInputFromWindow(
                currentFocus!!.windowToken,
                InputMethodManager.HIDE_NOT_ALWAYS
            )
        }
    }

    private fun showCheckIsLimitError(msg:String?) {
        //对话框使用示例
        val dialog = MyDialog(mContext, 309, 171,
            "", needBtnConfirm = true, needBtnCancel = false, bgResourceId = 0)
        dialog.setContentText(msg?:"")
        dialog.show()
    }

    /**--------------网络请求部分----------------**/
    private var modelDetail: ModelDetail? = null

    override fun initLogic() {
        super.initLogic()
        //校验限时提交，校验结果
        viewModel?.checkIsLimitResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
              trueAprCreate()
            }, onError = { _, msg ->
                hideLoading()
//                toastShort(msg)
                showCheckIsLimitError(msg)
            }, onDefault = { msg ->
                hideLoading()
                toastShort(msg)
            })
        })

        /**创建审批*/
        viewModel?.createAprResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                toastShort("成功发布审批")
                if (reCommitData != null && reCommitData!!.isNotEmpty()) {
                    EventBusUtils.sendEvent(EventBusEvent(MsgType.APR_RE_COMMIT_SUCCESS.name, null))
                }
                //清除缓存
                approCacheMap.clear()
                setResult(Activity.RESULT_OK)
                finish()
            }, onError = { _, msg ->
                toastShort(msg)
            }, onDefault = { msg ->
                toastShort(msg)
            })
        })

        /**获取列表属性数据源*/
        viewModel?.widgetDataResult?.observe(this, Observer { data ->

            RequestHelper.onResponse(data, onSuccess = { result ->
                showLog("监听到列表结果：")
                showLog(result)
                updateWidgetInfo(result)//网络请求后回调数据
            }, onError = { _, msg ->
                toastShort(msg)
//                updateWidgetInfo(currentWidgetId, arrayListOf())
            }, onDefault = { msg -> })
        })

        /**审批创建时，检查审批人状态*/
        viewModel?.checkLeaveResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                if (it.type == 1) {
                    //没有人员离职
//                    trueAprCreate() //(旧版调用位置)自定义流程审批创建时，检查审批人状态
                    checkIsLimitToCreatApr()
                } else if (it.type == 0) {
                    //有人离职了
                    val name = dealLeavePersonNameShow(
                        it.userIds,
                        aprNodeWidget!!.selectNodeResult!!
                    )
                    if (StringUtils.isNotBlankAndEmpty(name) && !dialogIsShow) {
                        dialogIsShow = true
//                        val view1 = View.inflate(mContext, R.layout.dialog_only_ok_base, null)
                        val view1 = View.inflate(mContext, com.joinutech.ddbeslibrary.R.layout.dialog_only_ok_base, null)
                        val leaveHintDialog = BottomDialogUtil.showBottomDialog(
                            mContext!!, view1, Gravity.CENTER, false
                        )
                        view1.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.tv2).text = "${name}等人员已离职，" +
                                "请从审批人列表中删除！"
                        view1.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.confirm_base).setOnClickListener {
                            dialogIsShow = false
                            leaveHintDialog.dismiss()
                        }
                        leaveHintDialog.show()
                    }
                }
            }, onError = { _, msg ->
                toastShort(msg)
            }, onDefault = { msg ->
                toastShort(msg)
            })
        })

        /**获取用户团队信息*/
        viewModel?.userOrgInfoResult?.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                userOrgInfo = it
                adapter.notifyDataSetChanged()
            }, onError = { _, msg ->
                toastShort(msg)
            }, onDefault = { msg ->
                toastShort(msg)
            })

        })

        /**获取审批模板详情*/
        //获取模板详情结果tcp，梳理步骤1
        viewModel?.modelDetailResult!!.observe(this, Observer { result ->
            RequestHelper.onResponse(result, onSuccess = {
                Loggerr.i("审批模板详情", "====json==${GsonUtil.toJson(it)}")
                fun chargeCache() {
                    //在缓存中获取之前的编辑数据
                    val approCache = UserHolder.getApproCache()
                    if (approCache != null) {
                        val cacheModelId = approCache[userId + "_modelId"]
                        if (cacheModelId.isNullOrBlank() || cacheModelId != it.modelId.plus("_")
                                .plus(it.version)
                        ) {
                            approCacheMap.clear()
                        } else {
                            val oldTimeTag = approCache[userId + "timeTag"]?.toLong() ?: 0L
                            if ((System.currentTimeMillis() - oldTimeTag) < 1000 * 60 * 60 * 24) {// 未超过一天则保留缓存
                                approCacheMap.putAll(approCache)
                            } else {
                                approCacheMap.clear()
                            }
                        }
                    }
                    // 缓存模板版本信息
                    approCacheMap[userId + "_modelId"] = it.modelId.plus("_").plus(it.version)
                }
                chargeCache()
                modelDetail = it
                if (modelDetail != null) {
                    updateList()
                } else {
                    toastShort("获取审批模板失败")
                }
            }, onError = { _, msg ->
                toastShort(msg)
            }, onDefault = { msg ->
                toastShort(msg)
            })
        })

        if (modelId != null && modelId!!.isNotBlank()) {
            loadModelDetail()
        }
    }

    private fun loadModelDetail() {//获取模板详情tcp
        if (isFuture && isApproval) {
            Loggerr.i("获取模板详情", "===开始获取模板详情=modelId=${modelId}==")
            viewModel?.getAprModelInfoV2(modelId!!)
        } else {
            viewModel?.getAprModelInfo(modelId!!)
        }
    }

    var aprNodeWidget: WidgetInfo? = null
    var aprCopyWidget: WidgetInfo? = null

    /**显示审批模板详情信息*/
//    var clearCacheTag = true

    //处理数据显示列表，梳理步骤2
    private fun updateList() {
        //清空之前model（比如加班或者请假）的缓存====开始====
//        val cacheKeys = approCacheMap.keys
//        for (key in cacheKeys) {
//            if (key.contains(modelDetail?.modelId ?: "为空则不清空")) {
//                clearCacheTag = false
//                break
//            }
//        }
//        if (clearCacheTag) {
//            approCacheMap.clear()
//        }
        //清空之前model（比如加班或者请假）的缓存====结束====
        propertyDataList.clear()
        /**添加审批模板描述项*/
        if (StringUtils.isNotBlankAndEmpty(modelDetail!!.modelDesc)) {
            propertyDataList.add(WidgetInfo(type = 0, title = modelDetail!!.modelDesc))
        }
        propertyDataList.addAll(modelDetail!!.widget)
//        propertyDataList.addAll(modelDetail!!.getWidgetList())

        // TODO: 2019/9/17 抄送人员都显示
        aprCopyWidget = WidgetInfo(modelId = modelDetail?.modelId, type = 30)//添加抄送人布局
        propertyDataList.add(aprCopyWidget!!)

        if (modelDetail?.processType == 3) {
            // TODO: 2019/8/21 自选人员：自选审批人员和抄送人员
            aprNodeWidget = WidgetInfo(modelId = modelDetail?.modelId, type = 20)//添加自选审批人布局
            propertyDataList.add(aprNodeWidget!!)
        }

        // TODO: 2019/9/30 默认抄送人员
        if (modelDetail?.carbonCopy != null && modelDetail!!.carbonCopy.isNotEmpty()) {
            val aprDefaultCopyWidget = WidgetInfo(
                type = 31, title = "此审批默认抄送至",
                prompt = "", selectCopyResult = modelDetail!!.carbonCopy, isEdit = false
            )//显示默认抄送人
            propertyDataList.add(aprDefaultCopyWidget)
        }

        //将缓存中的数据加进去tcp
        propertyDataList.forEach { widgetInfo ->
            if (widgetInfo.widgetId in reCommitData?.keys ?: arrayListOf<String?>()) {
                when (widgetInfo.type) {
                    3 -> {
                        widgetInfo.dateResult =
                            reCommitData?.get(widgetInfo.widgetId) as DateSegResult
                    }
                }
            }
        }
        adapter.notifyDataSetChanged()
        checkService()
    }

    private fun checkService() {
        if (!(reCommitData != null && reCommitData!!.isNotEmpty())) {
            //没有重新审批的数据
            loadAprId()
        }
        setPageTitle(modelDetail!!.modelName)
        /*  setRightTitle("发布", View.OnClickListener {
              createApprovalData()
          })*/

        for (pro in propertyDataList) {
            if (pro.type == 3 || pro.type == 4 || pro.type == 13) {
//                initCustomTimePicker()
            }

            if (pro.type == 5) {
                initCustomTimeRangePicker()
            }

            if (pro.type == 11 && pro.addressChoose == 0) {
                autoLocation()
            }

            if (pro.type == 6 || pro.type == 7) {
                // TODO: 2019/8/21 动态获取选择框数据列表
            }

            if (pro.type == 9) {
//                if (companyBean != null) {
//                    loadUserOrgInfo()
//                }
            }
        }
        loadUserOrgInfo()
    }

    private var aprId: String? = null
    private fun loadAprId() {
//        viewModel?.getAprIdError?.observe(this, Observer {
//            toastShort(it.message)
//            finish()
//        })

        viewModel?.getAprIdResult?.observe(this, Observer { result ->
            RequestHelper.onResponse(result, onSuccess = {
                aprId = it
            }, onError = { _, msg ->
                toastShort(msg)
                finish()
            }, onDefault = { msg ->
                toastShort(msg)
                finish()
            })

        })
        viewModel?.getAprId()
    }

    private val assignee = arrayListOf<Map<String, Any>>()

    //点击发布
    @SuppressLint("SetTextI18n")
    private fun createApprovalData() {
        if (aprId == null || userName == null || modelDetail == null || userOrgInfo == null || companyId.isNullOrBlank()) {
            toastShort("审批前置条件不满足")
            return
        }

        val positionContent = hashMapOf<Int, String>()

        if (properties.isNotEmpty()) {
            var checkResult = true

            //检查是否有填写未完成的属性
            fun checkData(index: Int, result: String): String {
               /* if (propertyDataList[index].type == 12) {//附件类型
                    return GsonUtil.toJson(arrayListOf<CreateTaskCommentUploadFileBean>())
                }*/
                if (propertyDataList[index].type == 10||
                        propertyDataList[index].type==12
                        ) {//图片选择,和附件选择
                    val hasUnUploadFile =
                        propertyDataList[index].selectFileResult?.find { !it.isUploadFlag }
                    if (hasUnUploadFile != null) {
                        toastShort("${propertyDataList[index].title} 还未上传完成，请稍等！")
                        checkResult = false
                        return ""
                    }
                }
                if (propertyDataList[index].type == 13||propertyDataList[index].type==8) {//动态增加时间点
                    if (result.isBlank()) {
                        toastShort("${propertyDataList[index].title}未设置")
                        checkResult = false
                    }
                    return result
                }
                if (propertyDataList[index].required == "1") {//文本输入
                    if (StringUtils.isEmpty(result)) {
                        toastShort("${propertyDataList[index].title}不能为空！")
                        checkResult = false
                        return ""
                    }
                    if (propertyDataList[index].type == 10) {//图片选择
                        if (propertyDataList[index].content.isNullOrBlank() || propertyDataList[index].content == "[]") {
                            toastShort("${propertyDataList[index].title}不能为空！")
                            checkResult = false
                            ""
                        }
                    }
                }
                return result
            }

            /**过滤添加的审批模板描述项*/
            if (properties[0] is ApprovalHintProperty) {
                for (index in 1 until properties.size) {
                    positionContent[index - 1] = checkData(index, properties[index]!!.getResult())
                    if (!checkResult) break
                }
            } else {
                for (index in 0 until properties.size) {
                    positionContent[index] = checkData(index, properties[index]!!.getResult())
                    if (!checkResult) break
                }
            }

            if (!checkResult) {
                return
            }

            // 仅修改原数据内容，用于提交包含套件内容相关属性
            modelDetail!!.widget.forEachIndexed { index, widgetInfo ->
                widgetInfo.content = positionContent[index] ?: ""
            }

        } else {
            return
        }

        assignee.clear()
        val assigneeIds = arrayListOf<String>()

        if (modelDetail!!.processType == 3) {
            if (!aprNodeWidget!!.selectNodeResult.isNullOrEmpty()) {
                aprNodeWidget!!.selectNodeResult!!.filter { !it.userId.isNullOrBlank() }
                    .forEach {
                        val data = hashMapOf<String, Any>()
                        data["userId"] = it.userId
                        data["isOuter"] = it.isOuter
                        assignee.add(data)
                        assigneeIds.add(it.userId)
                    }
            }
            if (assignee.isEmpty()) {
                toastShort("请添加审批人")
                return
            }

            //判断是否包含离职人员，如果有弹出提示
            showLoading()
            viewModel?.checkLeave(bindToLifecycle(), companyId, assigneeIds)
        } else {
//            trueAprCreate() //(旧版调用位置) 固定流程时，提交审批数据
            checkIsLimitToCreatApr()
        }
    }

    private var userOrgInfo: UserOrgInfo? = null

    /**加载用户团队信息*/
    private fun loadUserOrgInfo() {
//        viewModel?.userOrgInfoError?.observe(this, Observer {
//            hideLoading()
//            toastShort(it.message)
//        })

        if (companyId.isNullOrBlank() || userId.isNullOrBlank()) return
        viewModel?.getUserOrgInfo(companyId, userId!!)
    }

    private val widgetListData = hashMapOf<String, List<SelectFrameData>>()

    private fun showListSelectData() {
        if (targetProperty is TextProperty) {
            val property = targetProperty as TextProperty
            val widget = property.data
            val widgetId = widget.widgetId
            if (StringUtils.isNotBlankAndEmpty(widgetId)) {
                // 1、加载类型数据
                // 2、缓存类型数据
                fun loadSelectSourceData(onResult: (List<SelectFrameData>) -> Unit) {
                    if (widgetListData.isNotEmpty() && widgetListData.containsKey(widgetId)
                        && widgetListData[widgetId] != null && widgetListData[widgetId]!!.isNotEmpty()
                    ) {
                        onResult.invoke(widgetListData[widgetId]!!)
                    } else {
                        showLog("执行网络请求")
                        viewModel?.getWidgetData(widgetId!!)
                    }
                }
                // 3、返回类型数据后，根据属性类型弹窗单选或多选
                loadSelectSourceData { result ->
                    updateWidgetInfo(result)// 直接取到数据，则直接回调
                }
            }
        }
    }

    private fun updateWidgetInfo(data: List<SelectFrameData>) {
        if (data.isNotEmpty()) {
            if (targetProperty != null && targetProperty is TextProperty
                && targetProperty?.getType() in arrayOf(6, 7)
            ) {
                val property = targetProperty as TextProperty
                val widget = property.data
                widgetListData[widget.widgetId!!] = data

                fun onConfirm(selectData: List<SelectFrameData>?) {
                    if (selectData == null) {
                        property.setContent("")
                    } else {
                        val result = mutableListOf<SelectFrameData>()
                        if (selectData.isNotEmpty()) {
                            result.addAll(selectData)
                        }
                        if (result.isNotEmpty()) {
                            when (widget.type) {
                                6 -> {
                                    /**单选底部弹窗，需要单独获取选择项数据*/
                                    property.setContent(result[0].title)
                                }
                                else -> {
                                    /**多选中心弹窗，需要单独获取选择项数据*/
                                    if (result.size > 1) {
                                        val sb = StringBuilder()
                                        for (frame in result) {
                                            sb.append(frame.title).append(",")
                                        }
                                        val temp = sb.toString()
                                        val content = temp.substring(0, temp.length - 1)
                                        property.setContent(content)
                                    } else {
                                        property.setContent(result[0].title)
                                    }
                                }
                            }
                        }
                    }
                    widget.showing = false
                    targetProperty = null
                }

                fun onCancel() {
                    widget.showing = false
                    targetProperty = null
                }

                if (widget.type == 6) {
                    singleSelectDialog(widget.title, data,
                        onConfirm = { i: Int, selectFrameData: SelectFrameData? ->
                            if (selectFrameData == null) {
                                onConfirm(null)
                            } else {
                                // 4、选择结果返回给属性内容
                                onConfirm(arrayListOf(selectFrameData))
                            }
                        },
                        onCancel = { onCancel() })
                } else {
                    showListSelector(widget.title, data, false,
                        onConfirm = {
                            // 4、选择结果返回给属性内容
                            onConfirm(it)
                        },
                        onCancel = { onCancel() })
                }
            }
        }
    }

    private fun dealLeavePersonNameShow(
        userIds: List<String>,
        selectNodeResult: MutableList<SearchMemberBean>
    ): String {
        if (userIds.isNotEmpty() && selectNodeResult.isNotEmpty()) {
            var personName = ""
            selectNodeResult.forEach { allPeron ->
                userIds.forEach { leavePerson ->
                    run {
                        if (leavePerson == allPeron.userId) {
                            personName += "${allPeron.name},"
                        }
                    }
                }
            }

            return personName.substring(0, personName.length - 1)
        }
        return ""
    }

    //检查是否有时间限制，然后创建审批，此时widgetInfo的content字段已经有了getResule的值；
    private fun checkIsLimitToCreatApr() {
        showLoading()
        val widgetInfo_3 = modelDetail?.widget?.find { it.type == 3 && it.isLimit != 0 }
        val widgetInfo_4 = modelDetail?.widget?.find { it.type == 4 && it.isLimit != 0 }
        val widgetInfo_13 = modelDetail?.widget?.find { it.type == 13 && it.isLimit != 0 }

        val timeList= arrayListOf<Long>()
        val limitWidgetInfo = if (widgetInfo_3 != null) {
            timeList.add(widgetInfo_3.content?.toLong()?:0L)
            widgetInfo_3
        } else if (widgetInfo_4 != null) {
            val dateSegResult=GsonUtil.fromJson<DateSegResult>(widgetInfo_4.content,DateSegResult::class.java)
            timeList.add(dateSegResult?.startTime?.toLong()?:0L)
            timeList.add(dateSegResult?.endTime?.toLong()?:0L)
            widgetInfo_4
        } else if (widgetInfo_13 != null) {
            val stringList=GsonUtil.fromJson2<ArrayList<String>>(widgetInfo_13.content,object:TypeToken<ArrayList<String>>(){}.type)
            stringList?.forEach {
                timeList.add(it.toLong())
            }
            widgetInfo_13
        }else{
            null
        }

        if (limitWidgetInfo == null) {
            trueAprCreate()
        }else{
            //当type是3时content就是startTime.toString;是4时content就是GsonUtil.toJson(DateSegResult)
            // 是13时content就是GsonUtil.toJson(ArrayList<String>)，
            //调用校验接口
            val limitData = LimitData(
                dayNumber = limitWidgetInfo.dayNumber,
                limitType = limitWidgetInfo.isLimit,
                time = timeList
            )

            viewModel?.checkIsLimit(limitData)//看结果，搜索“校验结果”
        }

    }

    /**创建审批操作*/
    //真正创建审批tcp
    //用户填写的数据都赋值到了modelDetail!!.widget集合中WidgetInfo对象的content字段
    private fun trueAprCreate() {
        val extraInCarbonCopys = arrayListOf<String>()
        val extraOutCarbonCopys = arrayListOf<String>()
        if (aprCopyWidget != null && aprCopyWidget!!.selectCopyResult != null
            && aprCopyWidget!!.selectCopyResult!!.isNotEmpty()
        ) {
            aprCopyWidget!!.selectCopyResult!!
                .filter { !it.userId.isNullOrBlank() }
                .forEach {
                    if (it.isOuter == 0) {
                        extraInCarbonCopys.add(it.userId)
                    } else {
                        extraOutCarbonCopys.add(it.userId)
                    }
                }
        }

        /**截取属性前两条信息做列表展示*/
        var oneInfo: String? = null
        var twoInfo: String? = null
        var threeInfo: String? = null

        val onlyProp = properties.filterValues { it.getType() in 1..19 }.toSortedMap()

        if (onlyProp.isNotEmpty()) {
            when {
                onlyProp.size > 2 -> {
                    val keys = onlyProp.keys.toList()
                    oneInfo = onlyProp[keys[0]]!!.getShortContent()
                    twoInfo = onlyProp[keys[1]]!!.getShortContent()
                    threeInfo = onlyProp[keys[2]]!!.getShortContent()
                }
                onlyProp.size > 1 -> {
                    val keys = onlyProp.keys.toList()
                    oneInfo = onlyProp[keys[0]]!!.getShortContent()
                    twoInfo = onlyProp[keys[1]]!!.getShortContent()
                }
                else -> {
                    oneInfo = onlyProp[onlyProp.firstKey()]!!.getShortContent()
                }
            }
        }

        //提交的有效数据，WidgetInfo的type字段是3、4、13时有限时提交
        val submitWidgetList = modelDetail!!.widget.map {
            if (isFuture && isApproval) {
                it.turnSubmitV2()
            } else {
                it.turnSubmit()
            }
        }

        // TODO: 2019/9/30 默认审批人数据需要上传吗？
        val createAprData = CreateAprData(
            approveId = aprId!!,
            approveName = "${userName}-${modelDetail!!.modelName}申请",
            approveType = modelDetail!!.processType,
            /**固定流程时不需要传，默认空数组*/
            assignee = assignee,
            /**抄送人可以不填写，默认空数组 // TODO: 2019/9/30 3时传这个字段 */
            carbonCopys = arrayListOf(),
            /**增加抄送人 在3状态只传空数组，1,2时用于增加抄送人 */
            extraInCarbonCopys = extraInCarbonCopys,
            extraOutCarbonCopys = extraOutCarbonCopys,
            content = submitWidgetList,
            oneApproveContent = oneInfo ?: "",
            twoApproveContent = twoInfo ?: "",
            threeApproveContent = threeInfo ?: "",
            deployId = modelDetail!!.deployId,
            deptId = userOrgInfo!!.deptId,
            modelId = modelDetail!!.modelId,
            organizationId = companyId,
            processId = modelDetail!!.processId,
            version = modelDetail!!.version,
            type = if (reCommitData != null && reCommitData!!.isNotEmpty()) 2 else 1
        )
        val json = GsonUtil.toJson(createAprData)
        showLog("提交审批数据 ===> $json <===")
//        showLoading()
//         CHANGE_HISTORY: 2021/3/18 11:06 如果审批套件版本未发布，则保持原有功能不变
        if (isFuture && isApproval) {
            viewModel?.createAprV6(createAprData)
        } else {
            viewModel?.createAprV5(createAprData)
        }
    }

    /**----------------功能代码部分----------------**/
    /**底部弹窗dialog*/
    private fun singleSelectDialog(
        title: String, data: List<SelectFrameData>,
        onConfirm: (position: Int, data: SelectFrameData?) -> Unit,
        onCancel: () -> Unit
    ) {
        val list = mutableListOf<SelectFrameData>()
        list.addAll(data)
        val helper: BottomListDialogHelper<SelectFrameData> = BottomListDialogHelper(
//            mActivity, itemLayoutId = R.layout.item_title_with_select, title = "", data = list,
            mActivity, itemLayoutId = com.joinutech.ddbeslibrary.R.layout.item_title_with_select, title = "", data = list,
            onBindItem = { i: Int, item: SelectFrameData, view: View ->
                val textView = view.findViewById<TextView>(R.id.tv_name)
                textView.text = item.title
                textView.gravity = Gravity.CENTER
                view.findViewById<View>(R.id.iv_select).visibility = View.GONE
                view.findViewById<View>(R.id.line).visibility =
                    if (i == list.lastIndex) View.GONE else View.VISIBLE
            },
            onConfirm = onConfirm,
            onCancel = {
                onCancel.invoke()
            }
        )
        helper.initView()
        helper.show(outSizeEnable = false,
            onDismissListener = DialogInterface.OnDismissListener {
                showLog("弹窗消失回调了------>")
                it.dismiss()
                onCancel.invoke()
            })
    }

    /**居中弹窗dialog*/
    private fun showListSelector(
        title: String, data: List<SelectFrameData>, singleSelect: Boolean,
        onConfirm: (result: List<SelectFrameData>) -> Unit,
        onCancel: () -> Unit
    ) {
        val list = mutableListOf<SelectFrameData>()
        list.addAll(data)
        val dialog = SelectListDialog(this, title, list, singleSelect, onConfirm, onCancel)
        dialog.initView()
        dialog.show(outSizeEnable = true, onDismissListener = DialogInterface.OnDismissListener {
            onCancel.invoke()
        })
    }

    //选择图片
    private fun dealPicSelect(selectedCount: Int) {
//        val view = View.inflate(mContext, R.layout.dialog_personicon_bottom_layout, null)
        val view = View.inflate(mContext, com.joinutech.ddbeslibrary.R.layout.dialog_personicon_bottom_layout, null)
        val dialog = BottomDialogUtil.showBottomDialog(mContext!!, view, Gravity.BOTTOM)
        val mTakePicture = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.takePicture)
        val mSelectPicture = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.selectPicture)
        val mDismiss = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.dismiss)
        mTakePicture.setOnClickListener {
            PictureNewHelper.beforeSelectPhoto(
                this,
                type = ConsKeys.TAKE_PHOTO,
                maxSelectNum = limit - selectedCount
            )
//            selector.onSelect("takePhoto", selectedCount)
            dialog.dismiss()
        }
        mSelectPicture.setOnClickListener {
            PictureNewHelper.beforeSelectPhoto(this, maxSelectNum = limit - selectedCount)
//            selector.onSelect("", selectedCount)
            dialog.dismiss()
        }
        mDismiss.setOnClickListener {
            dialog.dismiss()
        }
    }

    private var timePickerUtil: TimePickerUtil? = null

    private fun initCustomTimePicker(startTime: Long? = null, endTime: Long? = null) {
        timePickerUtil = TimePickerUtil(this)
        timePickerUtil!!.initCustomTimePicker(
            mode = 0,
            calendars = timePickerUtil!!.getCalendars(startTime, endTime)
        )
        timePickerUtil!!.setPickerTypes(arrayOf(0, 2))
    }

    private fun showCustomPicker(
        dateType: Int = 0,
        startTime: Long? = null,
        endTime: Long? = null,
        result: (date: Date) -> Unit
    ) {
//        0：年月日时分，1：年月日，2：时分
        if (timePickerUtil == null) {
            initCustomTimePicker(startTime, endTime)
        }
        timePickerUtil?.showCustomPicker(dateType) { type, date ->
            result.invoke(date)
        }
    }

    private var optionsPickerView: OptionsPickerView<String>? = null
    private val dayRange = arrayListOf<String>()
    private val hourRange = arrayListOf<String>()
    private val minuteRange = arrayListOf<String>()

    private fun initCustomTimeRangePicker() {

        for (i in 0 until 366) {
            if (i < 10) {
                dayRange.add("0$i 天")
            } else {
                dayRange.add("$i 天")
            }
        }

        for (i in 0 until 24) {
            if (i < 10) {
                hourRange.add("0$i")
            } else {
                hourRange.add("$i")
            }
        }

        for (i in 0 until 60) {
            if (i < 10) {
                minuteRange.add("0$i")
            } else {
                minuteRange.add("$i")
            }
        }

        val centerColor: Int = 0xff1E87F0.toInt()
        optionsPickerView = OptionsPickerBuilder(this,
            OnOptionsSelectListener { pk1, pk2, pk3, view ->
                // 关闭弹窗时选择结果回调
            })
            .setOptionsSelectChangeListener { selectOne, selectTwo, selectThree ->
                // 弹窗中选择改变回调
            }
            .setSelectOptions(0, 0, 0)
            .setLayoutRes(R.layout.dialog_time_selector) {
                XUtil.hideView(
                    it.findViewById(R.id.tv_title),
                    it.findViewById(R.id.tv_time_result),
                    it.findViewById(R.id.date_indicator),
                    it.findViewById(R.id.time_indicator)
                )
                it.findViewById<View>(R.id.btn_submit).setOnClickListener {
                    optionsPickerView?.returnData()
                    optionsPickerView?.dismiss()
                }
                it.findViewById<View>(R.id.btn_cancel)
                    .setOnClickListener { optionsPickerView?.dismiss() }
            }.setCyclic(false, false, false)
            .setTextColorCenter(centerColor)
            .setTextXOffset(30, 0, -30)
            .setDividerType(CustomWheelView.DividerType.NONE)
            .build()
        optionsPickerView?.setNPicker(dayRange, hourRange, minuteRange)
        optionsPickerView?.setSelectOptions(0, 0, 0)
    }

    private fun showCustomRangePicker(
        view: View,
        result: (result1: String, result2: String, result3: String) -> Unit
    ) {
        optionsPickerView?.listener =
            OnOptionsSelectListener { pk1: Int, pk2: Int, pk3: Int, view: View ->
                result.invoke(dayRange[pk1], hourRange[pk2], minuteRange[pk3])
            }
        optionsPickerView?.show(view)
    }

    private var locationService: LocationService? = null

    private var currentLocation: LocationResult? = null

    inner class MyLocationListener : LocationCallback {

        override fun getTag(): String = "apr"

        override fun onLocationResult(result: LocationResult) {
        }

        override fun onPoiLocationResult(result: LocationResult) {
            if (!result.locationSuccess) {
                toastShort("无法获取位置信息，请检查定位是否开启")
                return
            }
            showLog("获取到审批定位信息 ${GsonUtil.toJson(result)}")
            currentLocation = result
            adapter.notifyDataSetChanged()
            if (locationService != null) {
                // 定位成功后注销定位服务
//                locationService?.apply {
//                    unregisterListener(locationListener)
//                    stop()
//                }
            }
        }
    }

    private var locationListener: MyLocationListener? = null

    @SuppressLint("CheckResult")
    private fun autoLocation() {
        if (locationService == null) {
            locationListener = MyLocationListener()
            locationService = (mActivity.application as BaseApplication).locationService

            //百度地图定位示例，会在locationListener监听中获得位置信息===========开始=====================
//            locationService?.apply {
//                initLocation()
//                registerListener(locationListener)
//                setLocationOption(getOption())
//            }
            PermissionUtils.requestPermissionActivity(
                this,
                arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),
                "当前审批需要获取你的位置信息",
                onSuccess = {
                    //开启地图定位图层
//                    locationService?.start()
                },
                onError = {
                    ToastUtil.show(mActivity, "获取当前位置需要使用位置权限")
                }
            )
            //百度地图定位示例============================================结束===========================

//            val rxPermissions = RxPermissions(mActivity)
//            rxPermissions.request(Manifest.permission.ACCESS_FINE_LOCATION).subscribe {
//                if (it) {
//                    //开启地图定位图层
//                    locationService?.start()
//                } else {
//                    ToastUtil.show(mActivity, "获取当前位置需要使用位置权限")
////                mListener?.refreshLocation("无法获取位置信息", false)
//                }
//            }
        }
    }

    override fun onStop() {
        if (locationService != null) {
//            locationService?.apply {
//                unregisterListener(locationListener)
//                stop()
//            }
        }
        super.onStop()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == RESULT_OK && requestCode == 20002) {
            (ARouter.getInstance().build(RouteProvider.IM_PROVIDER)
                .navigation() as RouteServiceProvider)
                .openPageWithResult(this@AprLaunchActivity, "selectFile", Bundle(), {})
            return
        }
        if (resultCode == Activity.RESULT_OK) {
            targetProperty?.checkResult(requestCode, resultCode, data)
            targetProperty = null
        } else {
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        UserHolder.saveApproCache()
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): CommonListWithEditLayoutBinding {
        return CommonListWithEditLayoutBinding.inflate(layoutInflater)
    }

    //接收选择城市的结果
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun receiveCityResult(event: EventBusEvent<String>) {
        if (event.data != null && event.data is String) {
            when (event.code) {
                "tcp_city_selector" -> {
                    val result = event.data as String
                    Loggerr.i("选择城市", "==接收到回调==result=${result}")
                    if (!StringUtils.isEmpty(result)) {
                        val intent = Intent()
                        intent.putExtra("result", result)
                        targetProperty?.checkResult(909, Activity.RESULT_OK, intent)
                        targetProperty = null
                    }
                }
            }
        }
    }


}
