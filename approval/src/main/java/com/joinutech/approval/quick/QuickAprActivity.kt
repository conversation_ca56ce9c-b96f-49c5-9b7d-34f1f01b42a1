package com.joinutech.approval.quick

import android.annotation.SuppressLint
import android.graphics.Paint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.viewpager.widget.ViewPager
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.approval.AprBaseActivity
import com.joinutech.approval.R
import com.joinutech.approval.data.DealApprovalData
import com.joinutech.approval.data.GetAprList
import com.joinutech.approval.data.MyAprListData
import com.joinutech.approval.data.UserOrgInfo
import com.joinutech.approval.databinding.ActivityQuickApprovalBinding
import com.joinutech.approval.utils.RouteApr
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.utils.*

/**
 * @className: QuickAprActivity
 * @desc: 审批快速审批页面
 * @author: zyy
 * @date: 2019/8/2 9:12
 * @company: joinUTech
 * @leader: ke
 */
@Route(path = RouteApr.APR_QUICK_PAGE)
class QuickAprActivity(override val contentViewResId: Int = R.layout.activity_quick_approval) :
        AprBaseActivity<ActivityQuickApprovalBinding>(), ViewPager.OnPageChangeListener {

    override fun showToolBar(): Boolean = false

    private var list: MutableList<MyAprListData> = mutableListOf()

    lateinit var vpContainer: ViewPager
    lateinit var adapter: QuickFragmentAdapter
    lateinit var tvTitle: TextView
    lateinit var tvJump: TextView
    private var cooperationCompanyApproval = false

    var position: Int = 0
    var companyId: String = ""
    override fun initView() {
//        ARouter.getInstance().inject(this)
        mImmersionBar?.titleBar(R.id.cl_top_bar)?.statusBarDarkFont(true)?.init()
        if (intent != null && intent.extras != null) {
            if (intent.extras!!.containsKey("companyId")) {
                companyId = intent.extras!!.getString("companyId", "")
            }
            cooperationCompanyApproval = intent.extras!!.getBoolean(
                    "cooperationCompanyApproval", false)
        }
        vpContainer = findViewById(R.id.vp_pages)
        adapter = QuickFragmentAdapter(supportFragmentManager, list)
        vpContainer.adapter = adapter
        vpContainer.addOnPageChangeListener(this)
        vpContainer.offscreenPageLimit = 1

        tvTitle = findViewById(R.id.tv_title)
        tvJump = findViewById(R.id.tv_jump)

        tvJump.paint.flags = Paint.UNDERLINE_TEXT_FLAG
        tvJump.paint.isAntiAlias = true

        findViewById<View>(R.id.tv_back).setOnClickListener(this)
        findViewById<View>(R.id.tv_jump).setOnClickListener(this)
        findViewById<View>(R.id.tv_agree).setOnClickListener(this)
        findViewById<View>(R.id.tv_refuse).setOnClickListener(this)
        updateTitle()
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityQuickApprovalBinding {
        return ActivityQuickApprovalBinding.inflate(layoutInflater)
    }

    override fun onPageScrollStateChanged(state: Int) {

    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {

    }

    override fun onPageSelected(position: Int) {

    }

    @SuppressLint("SetTextI18n")
    private fun updateTitle() {
        if (list.isNotEmpty()) {
            tvTitle.text = "快速审批(${position + 1}/${list.size})"
        }
    }

    override fun onNoDoubleClick(v: View) {
        binding.tvBack.isEnabled = false
        binding.tvJump.isEnabled = false
        binding.tvAgree.isEnabled = false
        binding.tvRefuse.isEnabled = false

        when (v.id) {
            R.id.tv_back -> {
                closeActivity()
            }
            R.id.tv_jump -> {
                onProcess(0)
            }
            R.id.tv_agree -> {
                process++
                onProcess(1)
            }
            R.id.tv_refuse -> {
                process++
                onProcess(-1)
            }
        }
    }

    private fun closeActivity() {
        val bundle = Bundle()
        bundle.putInt("done", process)
        bundle.putInt("no_done", list.size - process)
        ARouter.getInstance().build(RouteApr.APR_QUICK_RESULT_PAGE).with(bundle).navigation()
        finish()
    }

    var process: Int = 0
    var currentResult = 0
    private fun onProcess(result: Int) {
        currentResult = result
        binding.tvBack.isEnabled = false
        binding.tvJump.isEnabled = false
        binding.tvAgree.isEnabled = false
        binding.tvRefuse.isEnabled = false
        if (result == 0) {
            onOpt(result)
        } else {
            val userAssignId = (adapter.getItem(position) as QuickAprContentFragment).getUserAssignId()
            if (!cooperationCompanyApproval) {
                if (StringUtils.isNotBlankAndEmpty(userAssignId) && userOrgInfo != null) {
                    processApr(userAssignId!!, userOrgInfo!!.deptId, result == 1)
                }
            } else {
                if (StringUtils.isNotBlankAndEmpty(userAssignId)) {
                    processApr(userAssignId!!, "0", result == 1)
                }
            }
        }
    }

    private fun onOpt(result: Int) {
        (adapter.getItem(position) as QuickAprContentFragment).aprResult(result)
        BaseApplication.mMainThreadHandler.postDelayed({
            position += 1
            if (position >= list.size) {
                closeActivity()
            } else {
                vpContainer.setCurrentItem(position, true)
                updateTitle()
            }
        }, 600)
        BaseApplication.mMainThreadHandler.postDelayed({
            binding.tvBack.isEnabled = true
            binding.tvJump.isEnabled = true
            binding.tvAgree.isEnabled = true
            binding.tvRefuse.isEnabled = true
        }, 1000)
    }

    /**记录审批操作 -1 同意 -2 拒绝*/
    private var currentAprProcessType = 0

    private fun processApr(userAssignId: String, deptId: String, opinion: Boolean) {
        if (StringUtils.isNotBlankAndEmpty(companyId)) {
            val data = DealApprovalData(
                    approveAssigneeId = userAssignId,
                    organizationId = companyId,
                    approveId = list[position].approveId,
                    deployId = list[position].deployId,
                    deptId = deptId,
                    opinion = opinion,
                    taskId = list[position].taskId,
                    content = "")
            showLoading()
            currentAprProcessType = if (opinion) -1 else -2
            viewModel?.dealApr(data)
        }
    }

    /**获取用户团队信息，处理审批和添加审批人都需要自己查询接口，获得deptId*/
    var userOrgInfo: UserOrgInfo? = null

    private fun loadUserOrgInfo() {
//        viewModel?.userOrgInfoError!!.observe(this, Observer {
//            toastShort(it.message)
//        })
        viewModel?.userOrgInfoResult?.observe(this, Observer { result ->
            RequestHelper.onResponse(result, onSuccess = {
                userOrgInfo = it
            }, onError = { code, msg ->
                toastShort(msg)
            }, onDefault = { msg ->
                toastShort(msg)
            })
        })
    }

    override fun initLogic() {
        super.initLogic()
        if (StringUtils.isNotBlankAndEmpty(companyId)) {
//            viewModel?.getMyAprListError!!.observe(this, Observer {
//                hideLoading()
//                XUtil.hideView(
//                        findViewById<View>(R.id.tv_jump),
//                        findViewById<View>(R.id.tv_agree),
//                        findViewById<View>(R.id.tv_refuse)
//                )
//            })

            viewModel?.getMyAprListResult!!.observe(this, Observer { result ->
                hideLoading()
                RequestHelper.onResponse(result, onSuccess = {
                    list.clear()
                    if (it.approveList.isNotEmpty()) {
                        list.addAll(it.approveList)
                    }
                    updateView()
                }, onError = { code, msg ->
                    toastShort(msg)
                    XUtil.hideView(
                            findViewById<View>(R.id.tv_jump),
                            findViewById<View>(R.id.tv_agree),
                            findViewById<View>(R.id.tv_refuse)
                    )
                }, onDefault = { msg ->
                    toastShort(msg)
                    XUtil.hideView(
                            findViewById<View>(R.id.tv_jump),
                            findViewById<View>(R.id.tv_agree),
                            findViewById<View>(R.id.tv_refuse)
                    )
                })

            })
            val quickApr = GetAprList(companyId, type = 2, myApproveType = 1, status = 0,
                    start = 1, length = 999)
            showLoading()
            viewModel?.getMyAprList(quickApr.getMap(0))
            if (!cooperationCompanyApproval) {
                loadUserOrgInfo()
                viewModel?.getUserOrgInfo(companyId, userId!!)
            }
        }

//        viewModel?.dealAprError!!.observe(this, Observer {
//            hideLoading()
//            toastShort(it.message)
//            Handler().postDelayed({
//                tv_back.isEnabled = true
//                tv_jump.isEnabled = true
//                tv_agree.isEnabled = true
//                tv_refuse.isEnabled = true
//            }, 1000)
//        })
        viewModel?.dealAprResult!!.observe(this, Observer { result ->
            hideLoading()
            if (currentAprProcessType == -1 || currentAprProcessType == -2) {
//                ApprovalUtil().updateAprData(this, list[position].approveId, currentAprProcessType) {}
            }
            RequestHelper.onResponse(result, onSuccess = {
                EventBusUtils.sendEvent(EventBusEvent(EventBusAction.refresh_all_appro_undo, 0))
                onOpt(currentResult)
            }, onError = { code, msg ->
                toastShort(msg)
                BaseApplication.mMainThreadHandler.postDelayed({
                    binding.tvBack.isEnabled = true
                    binding.tvJump.isEnabled = true
                    binding.tvAgree.isEnabled = true
                    binding.tvRefuse.isEnabled = true
                }, 1000)
            }, onDefault = { msg ->
                toastShort(msg)
                BaseApplication.mMainThreadHandler.postDelayed({
                    binding.tvBack.isEnabled = true
                    binding.tvJump.isEnabled = true
                    binding.tvAgree.isEnabled = true
                    binding.tvRefuse.isEnabled = true
                }, 1000)
            })

        })
    }

    private fun updateView() {
        position = 0
        updateTitle()
        adapter.notifyDataSetChanged()
    }
}
