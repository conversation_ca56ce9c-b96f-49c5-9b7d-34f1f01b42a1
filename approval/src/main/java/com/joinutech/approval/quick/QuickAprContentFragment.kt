package com.joinutech.approval.quick

import android.os.Bundle
import android.view.View
import android.view.animation.AnimationUtils
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.approval.*
import com.joinutech.approval.data.ApprovalDetailData
import com.joinutech.approval.data.ApproveInfo
import com.joinutech.approval.data.WidgetInfo
import com.joinutech.common.adapter.MultiTypeAdapter
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.bean.TaskDetailMemberBean
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.utils.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * @className: QuickAprContentFragment
 * @desc: 快速审批内容
 * @author: zyy
 * @date: 2019/8/5 16:14
 * @company: joinUTech
 * @leader: ke
 */
class QuickAprContentFragment(
        override val layoutRes: Int = R.layout.fragment_quick_approval
) : AprBaseFragment() {

    override fun openEventBus() = false

    val list = mutableListOf<String>()
    private lateinit var iv_apr_icon: ImageView
    private lateinit var tv_apr_title: TextView
    private lateinit var tv_apr_time: TextView
    private lateinit var iv_apr_result: ImageView
    private lateinit var tv_apr_state: TextView

    private var rvList: RecyclerView? = null
    private val propertyList = arrayListOf<WidgetInfo>()
    private var propertyAdapter: MultiTypeAdapter<WidgetInfo>? = null

    override fun initView(root: View) {
        if (arguments != null) {
            if (arguments!!.containsKey(APPROVE_ID)) {
                approveId = arguments!!.getString(APPROVE_ID, "")
            }
        }
        iv_apr_icon = root.findViewById(R.id.iv_apr_icon)
        tv_apr_title = root.findViewById(R.id.tv_apr_title)
        tv_apr_time = root.findViewById(R.id.tv_apr_time)

        iv_apr_result = root.findViewById(R.id.iv_apr_result)
        tv_apr_state = root.findViewById(R.id.tv_apr_state)

        iv_apr_result.visibility = View.INVISIBLE
        tv_apr_state.visibility = View.GONE

        rvList = root.findViewById<RecyclerView>(R.id.rv_list)
    }

    override fun initLogic() {
        super.initLogic()
        loadData()
    }

    var approveId: String = ""
    var modelType: Int = 2
//    var modelSubType: Int = 1

    var aprDetailData: ApprovalDetailData? = null

    private fun loadData() {
        if (approveId.isBlank()) {
            return
        }
        viewModel?.getAprDetailResult!!.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result, onSuccess = {
                aprDetailData = it
                iniViewWithData()
            }, onError = { code, msg ->
                showToast("未获取到审批数据")
            }, onDefault = { msg -> showToast(msg) })

        })
        showLoading()
        viewModel?.getAprDetailByType(approveId, modelType)
    }

    private fun iniViewWithData() {
        aprDetailData?.apply {
            iniTopView(aprDetailData!!.approveInfo)
            if (aprDetailData!!.content.isNotEmpty()) {
                /**全部属性和提交的内容*/
                propertyList.addAll(aprDetailData!!.content)
            }
        }

        rvList!!.layoutManager = LinearLayoutManager(mActivity)
        propertyAdapter = MultiTypeAdapter(
                mActivity,
                propertyList,
                generateViewType = { position: Int, data: WidgetInfo ->
                    if (data.type == 9 && data.wordType == 0) {
                        8
                    } else {
                        data.type
                    }
                },
                generateLayoutId = { type ->
                    when (type) {
                        1 -> {
                            /**单行多行输入*/
                            /**单行多行输入*/
                            /**单行多行输入*/
                            /**单行多行输入*/
                            R.layout.property_text_input_detail
                        }
                        2 -> {
                            /**单行多行输入*/
                            /**单行多行输入*/
                            /**单行多行输入*/
                            /**单行多行输入*/
                            R.layout.property_text_input_detail_multi
                        }
                        3, 4 -> {
                            /**时间点和时间段*/
                            /**时间点和时间段*/
                            /**时间点和时间段*/
                            /**时间点和时间段*/
                            R.layout.property_time_selector_detail
                        }
                        5 -> {
                            /**选择持续时间*/
                            /**选择持续时间*/
                            /**选择持续时间*/
                            /**选择持续时间*/
                            R.layout.property_text_selector_detail
                        }
                        6, 7 -> {
                            /**单选框多选框*/
                            /**单选框多选框*/
                            /**单选框多选框*/
                            /**单选框多选框*/
                            R.layout.property_text_selector_detail
                        }
                        8 -> {
                            /**人员选择，跳转页面选择人员，personType：单选0 多选1*/
                            /**人员选择，跳转页面选择人员，personType：单选0 多选1*/
                            /**人员选择，跳转页面选择人员，personType：单选0 多选1*/
                            /**人员选择，跳转页面选择人员，personType：单选0 多选1*/
                            R.layout.property_select_workmate_detail
                        }
                        9 -> {
                            /**选择关键字类型，人员，职位，部门自动获取，手动选择部门*/
                            /**选择关键字类型，人员，职位，部门自动获取，手动选择部门*/
                            /**选择关键字类型，人员，职位，部门自动获取，手动选择部门*/
                            /**选择关键字类型，人员，职位，部门自动获取，手动选择部门*/
                            R.layout.property_text_selector_detail
                        }
                        10 -> {
                            /**图片选择布局*/
                            /**图片选择布局*/
                            /**图片选择布局*/
                            /**图片选择布局*/
                            R.layout.property_select_picture_detail
                        }
                        11 -> {
                            /**定位类型*/
                            /**定位类型*/
                            /**定位类型*/
                            /**定位类型*/
                            R.layout.property_location_text_detail
                        }
                        else -> {
//                            自动获取（当前人，团队职位，团队部门），
                            R.layout.property_text_selector_detail
                        }
                    }
                },
                onBindItem = { position: Int, data: WidgetInfo, view: View ->
                    data.isEdit = false
                    showLog(data)
                    when (data.type) {
                        1 -> {
                            //单行多行输入 发起和预览不一致，发起：短文本右对齐，预览都左对齐
                            //短文本
                            //长文本
                            val editProperty = EditProperty(data, view)
                            editProperty.initView()
                        }
                        2 -> {
                            //多行输入 发起和预览不一致，发起：短文本右对齐，预览都左对齐
                            //长文本
                            val editProperty = EditPropertyDetail(data, view)
                            editProperty.initView()
                        }
                        //时间点3和时间段4 发起右对齐，预览左对齐
                        3 -> {
                            //时间点 long.toString()
                            val timeProperty = TimeProperty(data, view, true)
                            timeProperty.initView()
                        }
                        4 -> {
                            //时间段
                            val timeProperty = TimeProperty(data, view, false)
                            timeProperty.initView()
                        }
                        8 -> {
                            // 人员选择，跳转页面选择人员，personType：单选0 多选1
                            // 发起是只显示名字，右对齐
                            // 预览时显示头像（圆形）和名字，左对齐，需要用另一个布局
                            val selectPerson = SelectPersonProperty(data, view) {
                                if (it.headimg == "--" && !data.selectCopyResult.isNullOrEmpty()) {
                                    val memberList = arrayListOf<TaskDetailMemberBean>()
                                    for (member in data.selectCopyResult!!) {
                                        memberList.add(TaskDetailMemberBean(member.headimg, 0, 0, 0, member.userId, member.name))
                                    }
                                    val bundle = Bundle()
                                    bundle.putSerializable("gridViewList", memberList)
                                    bundle.putString("title", data.title)
                                    jump(RouteTask.taskAllJoiner, bundle)
                                }
                            }
                            selectPerson.initView()
                        }
                        5, 6, 7, 9 -> {
                            // 选择持续时间(5) 发起右对齐，预览左对齐
                            // 单选框(6)多选框(7) 发起右对齐，预览左对齐
                            // 选择关键字类型(9)，人员，职位，部门自动获取，手动选择部门
                            // 发起右对齐，预览左对齐
                            // 持续时间选择
                            // 选择关键字类型，人员，职位，部门自动获取，手动选择部门
                            if (data.type == 9 && data.wordType == 0) {
                                val selectPerson = SelectPersonProperty(data, view) {
                                }
                                selectPerson.initView()
                            } else {
                                val textProperty = TextProperty(data, view)
                                textProperty.initView()
                            }
                        }
                        10 -> {
                            // 图片选择布局
                            // 发起时：显示提示文本
                            // 预览时：不显示提示文本
                            val selectPicProperty = SelectPicProperty(data, view, hashMapOf())
                            selectPicProperty.initView()
                            selectPicProperty.listener = object : OnSelectPicListener {
                                override fun onResult(list: List<String>) {
                                }

                                override fun onSelect(property: AprProperty, selectedCount: Int) {
                                }

                                override fun onPreview(position: Int, picList: ArrayList<String>) {
                                }
                            }
                        }
                        11 -> {
                            // 定位信息选择
                            // 定位类型
                            // 发起右对齐，自动显示定位logo，
                            // 预览左对齐，显示定位logo
                            val locationProperty = LocationProperty(data, view)
                            locationProperty.initView()
                        }
                        else -> {
                            val textProperty = TextProperty(data, view)
                            textProperty.initView()
                        }
                    }
                },
                onItemClick = { position: Int, data: WidgetInfo, view: View ->
                }
        )
        rvList!!.adapter = propertyAdapter

    }

    private fun iniTopView(approveInfo: ApproveInfo) {
        if (!UserHolder.getCurrentUser()?.avatar.isNullOrEmpty()) {
            XUtil.loadImage(mActivity, iv_apr_icon, UserHolder.getCurrentUser()?.avatar)
        }
        tv_apr_title.text = approveInfo.approveName
        tv_apr_time.text = "发布时间：" + DateUtil.time2Date(approveInfo.createTime)
    }

    fun getUserAssignId(): String? {
        if (aprDetailData != null && aprDetailData!!.approveProcess.assignee.isNotEmpty()) {
            for (assignee in aprDetailData!!.approveProcess.assignee) {
                if (assignee.approveType == 1) {
                    val member = assignee.assigneeUser[0]
                    if (member.userId == userId!!) {
                        return member.approveAssigneeId
                    }
                } else {
                    //TODO 会审或审处理
                }
            }
        }
        return null
    }

    /**
     * @param result -1 refuse,1 agree,0 jump
     * */
    fun aprResult(result: Int) {

        when (result) {
            -1 -> {
                iv_apr_result.setImageResource(R.drawable.icon_no_pass)
                iv_apr_result.visibility = View.VISIBLE
                val anim = AnimationUtils.loadAnimation(mActivity, R.anim.apr_result_scale)
                iv_apr_result.startAnimation(anim)
            }
            1 -> {
                iv_apr_result.setImageResource(R.drawable.icon_passed)
                iv_apr_result.visibility = View.VISIBLE
                val anim = AnimationUtils.loadAnimation(mActivity, R.anim.apr_result_scale)
                iv_apr_result.startAnimation(anim)
            }
            0 -> {

            }
        }
    }

    companion object {
        /**
         * The fragment argument representing the section number for this
         * fragment.
         */
        private const val APPROVE_ID = "approveId"

        /**
         * Returns a new instance of this fragment for the given section
         * number.
         */
        @JvmStatic
        fun newInstance(approveId: String): QuickAprContentFragment {
            return QuickAprContentFragment().apply {
                arguments = Bundle().apply {
                    putString(APPROVE_ID, approveId)
                }
            }
        }
    }

}

object DateUtil {
    fun time2Date(time: Long): String {
        return SimpleDateFormat(TIME_FORMAT_PATTERN1).format(Date(time))
    }
}