package com.joinutech.approval.quick

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import com.joinutech.approval.data.MyAprListData

/**
 * @className: QuickFragmentAdapter
 * @desc: 快速审批viewpager 适配器
 * @author: zyy
 * @date: 2019/8/2 9:18
 * @company: joinUTech
 * @leader: ke
 */
class QuickFragmentAdapter(fm: FragmentManager, private val list: MutableList<MyAprListData>) : FragmentPagerAdapter(fm, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT) {

    private val fragments = hashMapOf<String, QuickAprContentFragment>()

    override fun getItem(position: Int): Fragment {
        val approveId = list[position].approveId
        if (!fragments.containsKey(approveId) || fragments[approveId] == null) {
            fragments[approveId] = QuickAprContentFragment.newInstance(approveId)
        }
        return fragments[approveId]!!
    }

    override fun getCount(): Int {
        return list.size
    }


}