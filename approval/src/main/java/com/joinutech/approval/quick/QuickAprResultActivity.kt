package com.joinutech.approval.quick

import android.view.LayoutInflater
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.approval.AprBaseActivity
import com.joinutech.approval.R
import com.joinutech.approval.databinding.ActivityQuickAprResultBinding
import com.joinutech.approval.utils.RouteApr
import com.joinutech.ddbeslibrary.utils.*

@Route(path = RouteApr.APR_QUICK_RESULT_PAGE)
class QuickAprResultActivity(override val contentViewResId: Int = R.layout.activity_quick_apr_result)
    : AprBaseActivity<ActivityQuickAprResultBinding>() {
    override fun showToolBar(): <PERSON><PERSON>an {
        return false
    }

    override fun onBackPressed() {
        super.onBackPressed()
        EventBusUtils.sendEvent(EventBusEvent(
                EventBusAction.Event_REFRESH_COOPERATION_COMPANY_DETAIL,""))
    }

    override fun initView() {
        val bundle = intent.extras
        if (bundle != null) {
            val done = bundle.getInt("done", 0)
            val noDone = bundle.getInt("no_done", 0)
            val isBack = bundle.getBoolean("isBack", false)

            binding.tvResultDone.text = "$done 个"
            binding.tvResultNoDone.text = "$noDone 个"
            if (isBack) {
                binding.tvResult.text = "审批累了，休息一下吧!"
            } else {
                when {
                    noDone > 0 -> {
                        binding.tvResult.text = "执行了这么多审批，辛苦了！"
                    }
                    noDone == 0 -> {
                        binding.tvResult.text = "恭喜你已完成所有审批！"
                    }
                }
            }
        }
        binding.tvResultOver.setOnClickListener {
            finish()
        }
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityQuickAprResultBinding {
        return ActivityQuickAprResultBinding.inflate(layoutInflater)
    }
}
