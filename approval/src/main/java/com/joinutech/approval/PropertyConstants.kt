package com.joinutech.approval

import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.InputType
import android.text.TextWatcher
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatEditText
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.alibaba.android.arouter.facade.Postcard
import com.alibaba.android.arouter.facade.callback.NavigationCallback
import com.google.gson.reflect.TypeToken
import com.joinutech.approval.adapter.ApprovalFileAdapter
import com.joinutech.approval.adapter.AprDetailCopyPersonAdapter
import com.joinutech.approval.custom.ExpandTextView
import com.joinutech.approval.data.*
import com.joinutech.approval.utils.FilesUtils
import com.joinutech.approval.utils.UploadFileUtil
import com.joinutech.common.adapter.MyAdapter
import com.joinutech.common.provider.FilePreviewProvider
import com.joinutech.common.storage.FileUtil
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.base.BaseApplication.Companion.approCacheMap
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.bean.*
import com.joinutech.ddbeslibrary.request.DdbesApiUtil
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.request.RxScheduleUtil
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.UploadFileService
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.CircleImageView
import com.joinutech.ddbeslibrary.widget.roundimage.RoundedImageView
import com.joinutech.ddbeslibrary.widget.activity.TaskImagePreviewActivity
import com.marktoo.lib.cachedweb.LogUtil.showLog
import com.tea.fileselectlibrary.FileSelector
import com.trello.rxlifecycle3.components.support.RxAppCompatActivity
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import io.reactivex.FlowableOnSubscribe
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import java.io.File
import java.util.*
import kotlin.collections.HashMap

/**
 * @className: PropertyConstants
 * @desc: 属性相关
 * @author: zyy
 * @date: 2019/8/28 16:35
 * @company: joinUTech
 * @leader: ke
 */

interface AprProperty {
    companion object {
        const val charTag = ":"
    }

    fun initView()
    fun getType(): Int

    //用于发布审批时提取数据
    //说明：该方法返回的值最终赋值到了接口获取的审批模板详情对象modelDetail的widget集合中WidgetInfo对象的content字段上了，
    // 创建审批时就是在这里取的数据。
    fun getResult(): String

    /**
     *  控件类型：1.文本类型；2.长文本类型；3.时间点类型；4.时间段类型；5.时间长度类型；6.单选框类型；
     *  7.多选框类型；8.人员选择类型；9.关键字选择类型；10图片类型；11.地点类型;12.文件类
     *
     *
     *  8位成员(只有一人时显示人员姓名) 10张图片  12个文件
     *
     */
    fun getShortContent(): String

    fun checkResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean
}

//属性控件
//选择城市属性
class CityProperty(val data: WidgetInfo, val rootView: View) : AprProperty {

    override fun getType(): Int = data.type

    //返回的数据用于创建审批
    override fun getResult(): String {
        if (StringUtils.isEmpty(data.content)) {
            return ""
        }
        return data.content!!
    }

    //返回的数据用于简略显示审批
    override fun getShortContent(): String {
        return data.title + AprProperty.charTag + contentTv.text
    }

    private var resultData: String? = null

    override fun checkResult(requestCode: Int, resultCode: Int, intent: Intent?): Boolean {
        return when (requestCode) {
            909 -> {
                //选择城市
                resultData = intent?.getStringExtra("result")
                Loggerr.i("选择城市", "===checkResult==执行==resultData=${resultData}")
                try {
                    if (resultData != null && resultData!!.isNotEmpty()) {
                        setContent(resultData ?: "")
                        //写缓存----选择人员
                        approCacheMap.put(
                            data.modelId + data.widgetId + "cityResult",
                            resultData ?: ""
                        )
                    }

                } catch (e: Exception) {
                }
                true
            }
            else -> {
                false
            }
        }
    }

    private lateinit var requiredTv: View
    private lateinit var titleTv: TextView
    private lateinit var contentTv: TextView
    private lateinit var arrowIv: ImageView

    @SuppressLint("RtlHardcoded")
    override fun initView() {
        requiredTv = rootView.findViewById(R.id.tv_required)
        titleTv = rootView.findViewById(R.id.tv_title)
        contentTv = rootView.findViewById(R.id.tv_content)
        arrowIv = rootView.findViewById(R.id.iv_arrow)

        AprUtil.initTitleStatus(titleTv, data.isEdit)

        initData()
        when {
            data.isEdit -> contentTv.gravity = Gravity.END or Gravity.RIGHT
            else -> contentTv.gravity = Gravity.START or Gravity.LEFT
        }
        contentTv.hint = data.prompt
        if (data.content != null && data.content!!.isNotEmpty()) {
            contentTv.text = data.content!!
        }
        titleTv.text = data.title
        if (data.required == "1") {
            requiredTv.visibility = View.VISIBLE
        } else {
            requiredTv.visibility = View.INVISIBLE
        }

        arrowIv.visibility = View.VISIBLE
    }

    private fun initData() {
        //读缓存
        if (data.modelId + data.widgetId + "cityResult" in approCacheMap.keys) {
            val cacheJson = approCacheMap.get(data.modelId + data.widgetId + "cityResult")
            if (cacheJson != null) {
                when (data.type) {
                    14 -> {//选择城市
                        data.content = cacheJson
                    }
                }
            }
        }
    }

    fun addListener(onClick: (view: View) -> Unit) {
        rootView.setOnClickListener {
            val inputMethodManager =
                rootView.context.getSystemService(RxAppCompatActivity.INPUT_METHOD_SERVICE) as InputMethodManager
            if (inputMethodManager.isActive) {
                inputMethodManager.hideSoftInputFromWindow(
                    rootView.windowToken!!, InputMethodManager.HIDE_NOT_ALWAYS
                )
            }
            onClick.invoke(it)
        }
    }

    fun setContent(info: String, default: Boolean = true) {
        contentTv.text = info
        if (default) {
            data.content = info
        }
    }

}//已缓存

//单行文本，支持是否必选，自动获取回调，设置内容等
class TextProperty(val data: WidgetInfo, val rootView: View, var isRelated: Boolean = false) :
    AprProperty {

    override fun getType(): Int = data.type

    override fun getResult(): String {
        when (data.type) {
            8->{
                if (data.personResult != null && data.personResult?.isNotEmpty()?:false) {
                    val result = if (data.personType == 0) {
                        arrayListOf(data.personResult!![0].userId)
                    } else {
                        val content = arrayListOf<String>()
                        for (result in data.personResult!!) {
                            content.add(result.userId)
                        }
                        content
                    }
                    return GsonUtil.toJson(result)
                }else{
                    return ""
                }
            }
            9->{
                if (data.wordType == 0) {
                    return GsonUtil.toJson(arrayListOf(data.personResult!![0].userId))
                }else{
                    return data.content?:""
                }
            }
            else->{
                return data.content?:""
            }
        }
    }

    override fun getShortContent(): String {
        if (data.type == 8) {
            val result = if (!data.personResult.isNullOrEmpty()) {
                if (data.personType == 0) {
                    data.personResult!![0].name
                } else {
                    "${data.personResult?.size ?: 0}位成员"
                }
            } else {
                "0位成员"
            }

            return data.title + AprProperty.charTag + result
        }
        return data.title + AprProperty.charTag + content.text
    }

    private var resultData: String? = null

    override fun checkResult(requestCode: Int, resultCode: Int, intent: Intent?): Boolean {
        return when (requestCode) {
            801 -> {
                /**选择人员*/
                resultData = intent?.getStringExtra("members")
                if (resultData != null && resultData!!.isNotEmpty()) {
                    val list = GsonUtil.fromJson(resultData, Array<SearchMemberBean>::class.java)
                    if (list != null && list.isNotEmpty()) {
                        val result = list[0]
                        data.personResult = arrayListOf(
                            SelectData(
                                result.headimg, result.name, result.userId
                            )
                        )
                        setContent(result.name)
                        //写缓存---选择人员
                        approCacheMap.put(
                            data.modelId + data.widgetId + "personResult",
                            GsonUtil.toJson(data.personResult)
                        )

                    }
                }
                true
            }
            802 -> {
                /**选择多个人员*/
                resultData = intent?.getStringExtra("members")
                try {
                    if (resultData != null && resultData!!.isNotEmpty()) {
                        val list =
                            GsonUtil.fromJson(resultData, Array<SearchMemberBean>::class.java)
                        if (list.isNullOrEmpty()) {
                            data.personResult = arrayListOf<SelectData>()
                            setContent("")
                        } else {
                            val results = arrayListOf<SelectData>()
                            val sb = StringBuilder()
                            for (result in list) {
                                results.add(SelectData(result.headimg, result.name, result.userId))
                                sb.append(result.name).append(",")
                            }
                            val result = sb.toString()
                            data.personResult = results
                            setContent(result.substring(0, result.length - 1))
                        }
                        //写缓存----选择人员
                        approCacheMap.put(
                            data.modelId + data.widgetId + "personResult",
                            GsonUtil.toJson(data.personResult)
                        )
                    }

                } catch (e: Exception) {
                }
                true
            }
            803 -> {
                /**选择部门*/
                resultData = intent?.getStringExtra("selectDept")
                if (StringUtils.isNotBlankAndEmpty(resultData)) {
                    setContent(resultData!!)
                }
                true
            }
            else -> {
                false
            }
        }
    }

    private lateinit var required: View
    private lateinit var title: TextView
    private lateinit var content: TextView
    private lateinit var arrow: ImageView

    @SuppressLint("RtlHardcoded")
    override fun initView() {
        required = rootView.findViewById(R.id.tv_required)
        title = rootView.findViewById(R.id.tv_title)
        content = rootView.findViewById(R.id.tv_content)
        arrow = rootView.findViewById(R.id.iv_arrow)

        AprUtil.initTitleStatus(title, data.isEdit)

        initData()
        content.text = data.content
        when {
            data.isEdit -> content.gravity = Gravity.END or Gravity.RIGHT
            else -> content.gravity = Gravity.START or Gravity.LEFT
        }
        if (isRelated) {
            content.hint = data.secPrompt
            title.text = data.secTitle
        } else {
            content.hint = data.prompt
            if (data.content != null && data.content!!.isNotEmpty()) {
                content.text = data.content!!
            }
            title.text = data.title
        }
        if (data.isEdit) {
            if (data.required == "1") {
                required.visibility = View.VISIBLE
            } else {
                required.visibility = View.INVISIBLE
            }
        } else {
            required.visibility = View.GONE
        }

        if (!data.isEdit || data.type == 9) {
            arrow.visibility = View.GONE
        } else {
            arrow.visibility = View.VISIBLE
        }
    }

    private fun initData() {
        //读缓存
        if ((data.modelId + data.widgetId + "personResult") in approCacheMap.keys) {
            val cacheJson = approCacheMap.get(data.modelId + data.widgetId + "personResult")
            if (cacheJson != null) {
                when (data.type) {
                    8 -> {
                        data.personResult = GsonUtil.fromJson2<List<SelectData>>(cacheJson,
                            object : TypeToken<List<SelectData>>() {}.type
                        )
                    }
                }
            }
        }
        //读缓存---用于回显的
        if ((data.modelId + data.widgetId + "content"+data.type) in approCacheMap.keys) {
            data.content = approCacheMap.get(data.modelId + data.widgetId + "content"+data.type)

            if (data.type == 8&&data.personResult.isNullOrEmpty()) {
                data.content=""
            }
        }
        if (data.content == null) {
            data.content = ""
        }
    }

    fun addListener(onClick: (view: View) -> Unit) {
        if (data.autoDept == 1 && data.wordType == 2) {
            rootView.setOnClickListener(null)
        } else {
            rootView.setOnClickListener {
                val inputMethodManager =
                    rootView.context.getSystemService(RxAppCompatActivity.INPUT_METHOD_SERVICE) as InputMethodManager
                if (inputMethodManager.isActive) {
                    inputMethodManager.hideSoftInputFromWindow(
                        rootView.windowToken!!, InputMethodManager.HIDE_NOT_ALWAYS
                    )
                }
                onClick.invoke(it)
            }
        }
    }

    fun setContent(info: String, default: Boolean = true) {
        content.text = info
        if (default) {
            data.content = info
            //选择人员---写缓存
            if (data.type != 3) {
                approCacheMap[data.modelId + data.widgetId + "content"+data.type] = data.content ?: ""
            }
        }
    }
}//已缓存

/**短文本、长文本 编辑控件*/
class EditProperty(val data: WidgetInfo, private val rootView: View) : AprProperty {
    override fun getType(): Int = data.type

    override fun getResult(): String {
        if (data.content == null) {
            data.content = ""
        }
        approCacheMap.put(data.modelId + data.widgetId ?: "", data.content ?: "")
        return data.content!!
    }

    override fun getShortContent(): String {
        return data.title + AprProperty.charTag + content.text
    }

    override fun checkResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        return false
    }

    private lateinit var required: View
    private lateinit var title: TextView
    private lateinit var content: AppCompatEditText

    override fun initView() {

        //读缓存
        if (data.modelId + data.widgetId in approCacheMap.keys) {
            data.content = approCacheMap.get(data.modelId + data.widgetId)
        }

        required = rootView.findViewById(R.id.tv_required)
        title = rootView.findViewById(R.id.tv_title)
        content = rootView.findViewById(R.id.et_input)

        AprUtil.initTitleStatus(title, data.isEdit)

        title.text = if (StringUtils.isNotBlankAndEmpty(data.suffix)) {
            data.title + "(" + data.suffix + ")"
        } else {
            data.title
        }
        content.hint = data.prompt

        if (data.isEdit) {
            var isInit = true
            var oldValue = ""
//            content.setOnTouchListener { v, event ->
//                when (event.action) {
//                    MotionEvent.ACTION_DOWN -> {
//                        v.parent.requestDisallowInterceptTouchEvent(true)
//                    }
//                    MotionEvent.ACTION_UP -> {
//                        v.parent.requestDisallowInterceptTouchEvent(false)
//                    }
//                    MotionEvent.ACTION_CANCEL -> {
//                        v.parent.requestDisallowInterceptTouchEvent(false)
//                    }
//                }
//                false
//            }
            content.addTextChangedListener(object : TextWatcher {
                override fun afterTextChanged(s: Editable?) {
                    showLog("property+++  afterTextChanged ${s.toString().trim()}")
                    if (data.type == 2) {
                        if (s != null && s.length > 2000) {
                            ToastUtil.show(
                                BaseApplication.joinuTechContext,
                                "输入的文本过长,请检查"
                            )
                            s.delete(2000, s.length)
                        }
                    }
                    if ((isInit && StringUtils.isEmpty(oldValue)
                                && StringUtils.isNotBlankAndEmpty(s.toString()))
                        || !isInit
                        || (StringUtils.isNotBlankAndEmpty(oldValue)
                                && StringUtils.isEmpty(s.toString()))
                    ) {
                        isInit = false
                        data.content = s.toString().trim()
                    } else {
                        isInit = true
                    }
                    getResult()//写缓存
                }

                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                    oldValue = s.toString()
                    showLog("property+++  beforeTextChanged ${s.toString().trim()}")
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    showLog("property+++  onTextChanged ${s.toString().trim()}")

                }

            })
            if (data.type == 1) {
                content.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(data.wordLimit))
//                content.gravity = Gravity.END
//                content.setLines(1)
//                content.setSingleLine(true)
//                content.ellipsize = TextUtils.TruncateAt.END
                when (data.keyBoard) {
                    1 -> {
                        XUtil.setEditType(content, InputType.TYPE_CLASS_TEXT)
                    }
                    2 -> {
                        XUtil.setEditNumberType(content, InputType.TYPE_NUMBER_FLAG_DECIMAL)
                    }
                    3 -> {
                        XUtil.setEditNumberType(content, InputType.TYPE_CLASS_NUMBER)
                    }
                    4 -> {
                        XUtil.setEditType(content, InputType.TYPE_TEXT_VARIATION_PASSWORD)
                    }
                }
            } else {
//                content.height = DeviceUtil.dip2px(rootView.context, 45f)
//                content.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(500))
            }
        } else {
            content.isEnabled = false
        }
        content.setText(data.content)

        if (data.isEdit) {
            if (data.required == "1") {
                required.visibility = View.VISIBLE
            } else {
                required.visibility = View.INVISIBLE
            }
        } else {
            required.visibility = View.GONE
        }

    }

    fun setContent(info: String) {
        content.setText(info)
        data.content = info
    }

}//已缓存

/**输入文本 详情时使用 可展开文本控件*/
class EditPropertyDetail(val data: WidgetInfo, private val rootView: View) : AprProperty {
    override fun checkResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        return false
    }

    override fun getType(): Int = data.type

    override fun getResult(): String {
        if (data.content == null) {
            data.content = ""
        }
        return data.content!!
    }

    override fun getShortContent(): String {
        return ""
    }

    private lateinit var required: View
    private lateinit var title: TextView
    private lateinit var content: ExpandTextView

    override fun initView() {
        required = rootView.findViewById(R.id.tv_required)
        title = rootView.findViewById(R.id.tv_title)
        content = rootView.findViewById(R.id.et_input)

        AprUtil.initTitleStatus(title, data.isEdit)

        title.text = if (StringUtils.isNotBlankAndEmpty(data.suffix)) {
            data.title + "(" + data.suffix + ")"
        } else {
            data.title
        }

        content.isEnabled = false
        content.setDesc(data.content, TextView.BufferType.NORMAL)

        required.visibility = View.GONE

    }
}

/**定位属性控件*/
class LocationProperty(private val data: WidgetInfo, val rootView: View) : AprProperty {
    private lateinit var required: View
    private lateinit var title: TextView
    private lateinit var content: TextView
    private lateinit var arrow: ImageView
    private lateinit var subContent: TextView

    @SuppressLint("RtlHardcoded")
    override fun initView() {
        required = rootView.findViewById(R.id.tv_required)
        title = rootView.findViewById(R.id.tv_title)
        content = rootView.findViewById(R.id.tv_content)
        subContent = rootView.findViewById(R.id.tv_sub_content)
        arrow = rootView.findViewById(R.id.iv_arrow)

        AprUtil.initTitleStatus(title, data.isEdit)

        title.text = data.title

        if (data.isEdit) {
            if (data.required == "1") {
                required.visibility = View.VISIBLE
            } else {
                required.visibility = View.INVISIBLE
            }
            if (data.addressChoose == 0) {
//                subContent.setTextColor(rootView.context.resources.getColor(R.color.c_ff9a9a9a))
                subContent.setTextColor(rootView.context.resources.getColor(com.joinutech.ddbeslibrary.R.color.c_ff9a9a9a))
                content.visibility = View.VISIBLE
                arrow.setImageResource(R.drawable.icon_location)
            } else {
                /**手动获取定位*/

                //手动定位  读缓存
                if (data.modelId + data.widgetId + "addressResult" in approCacheMap.keys) {
                    val addressResultString =
                        approCacheMap.get(data.modelId + data.widgetId + "addressResult")
                    if (addressResultString != null && addressResultString.isNotBlank()) {
                        data.addressResult =
                            GsonUtil.fromJson(addressResultString, AddressResult::class.java)
                    }
                }

//                arrow.setImageResource(R.drawable.icon_right_arrow)
                arrow.setImageResource(com.joinutech.ddbeslibrary.R.drawable.icon_right_arrow)
                content.visibility = View.GONE
                subContent.hint = data.secPrompt
            }
            if (data.addressResult != null) {
                content.text = data.addressResult?.address
                subContent.text = data.addressResult?.positioning
            }
        } else {
            required.visibility = View.GONE
            content.gravity = Gravity.START or Gravity.LEFT
            subContent.gravity = Gravity.START or Gravity.LEFT
            if (data.addressResult == null) {
                if (data.content != null && data.content!!.isNotEmpty()) {
                    data.addressResult = GsonUtil.fromJson(data.content!!)
                }
            }
            data.addressResult?.let {
                content.text = it.address
                subContent.text = it.positioning
            }
        }
    }

    fun addListener(onClick: (property: LocationProperty) -> Unit) {
        if (data.addressChoose == 0) {
            rootView.setOnClickListener(null)
        } else {
            rootView.setOnClickListener {
                val inputMethodManager =
                    rootView.context.getSystemService(RxAppCompatActivity.INPUT_METHOD_SERVICE) as InputMethodManager
                if (inputMethodManager.isActive) {
                    inputMethodManager.hideSoftInputFromWindow(
                        rootView.windowToken, InputMethodManager.HIDE_NOT_ALWAYS
                    )
                }
                onClick.invoke(this)
            }
        }
    }

    fun setContent(name: String, detail: String) {
        content.text = name
        subContent.text = detail
    }

    override fun checkResult(requestCode: Int, resultCode: Int, intent: Intent?): Boolean {
        if (requestCode == ATTENDANCE_LOCATION) {
            intent?.let {
                val lat = it.getDoubleExtra("latitude", 0.0)
                val lng = it.getDoubleExtra("longitude", 0.0)
                var locationTitle = it.getStringExtra("title") ?: ""
                var locationAddress = it.getStringExtra("address") ?: ""
                if (StringUtils.isEmpty(locationTitle)) {
                    locationTitle = ""
                }
                if (StringUtils.isEmpty(locationAddress)) {
                    locationAddress = ""
                }
                setContent(locationTitle, locationAddress)
                if (data.addressResult == null) {
                    data.addressResult = AddressResult(locationTitle, locationAddress, lng, lat)
                } else {
                    data.addressResult?.address = locationTitle
                    data.addressResult?.positioning = locationAddress
                    data.addressResult?.longitude = lng
                    data.addressResult?.latitude = lat
                }
                //手动定位   写缓存
                getResult()
            }
            return true
        }
        return false
    }

    override fun getType(): Int = data.type

    override fun getResult(): String {
        val result = if (data.addressResult == null) {
            AddressResult()
        } else {
            data.addressResult!!
        }
        data.content = GsonUtil.toJson(result)
        //手动定位写缓存
        approCacheMap.put(data.modelId + data.widgetId + "addressResult", data.content ?: "")
        return data.content!!
    }

    override fun getShortContent(): String {
        return data.title + AprProperty.charTag + content.text
    }
}

/**时间设置属性 包含单行文本控件*/
class TimeProperty(
    val data: WidgetInfo,
    private val rootView: View,
    private val isSingle: Boolean = true
) : AprProperty {
    override fun checkResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        return false
    }

    override fun getType(): Int = data.type

    override fun getResult(): String {
        if (isSingle) {
            data.content =
                if (data.dateResult != null && data.dateResult!!.startTime.isNotEmpty() && data.dateResult!!.startTime.toLong() > 0L) {
                    data.dateResult!!.startTime
                } else {
                    ""
                }
        } else {
            val result = if (data.dateResult == null) {
                DateSegResult()
            } else {
                data.dateResult!!
            }
            data.content = GsonUtil.toJson(result)
        }
        approCacheMap.put(data.modelId + data.widgetId + "dataResult", data.content ?: "")
        return data.content!!
    }

    override fun getShortContent(): String {
        var info = ""
        if (beginText != null) {
            info = beginText!!.getShortContent()
        }
        if (endText != null) {
            info.plus("-").plus(endText!!.getShortContent())
        }
        return info
    }

    private lateinit var beginTime: View

    //    private lateinit var line: View
    private lateinit var endTime: View

    private fun getDateStr(time: String): String {
        val pattern = when (data.dateChoose) {
            1 -> {
                TIME_FORMAT_PATTERN4
            }
            2 -> {
                "HH:mm"
            }
            else -> {
                TIME_FORMAT_PATTERN1
            }
        }
        return XUtil.getTime(Date(time.toLong()), pattern)
    }

    override fun initView() {
        beginTime = rootView.findViewById(R.id.begin_time)
      val  tipLayout:ConstraintLayout = rootView.findViewById(R.id.tip_view)
      val  tipTwoTv:TextView = rootView.findViewById(R.id.tip_two_tv)
        if (data.isLimit == 0) {
            tipLayout.visibility=View.GONE
        }else{
            tipLayout.visibility=View.VISIBLE
            tipTwoTv.text=data.limitRemind
        }
        endTime = rootView.findViewById(R.id.end_time)
        beginText = TextProperty(data, beginTime)
        beginText!!.initView()
        iniData()
        try {
            if (data.dateResult != null && data.dateResult!!.startTime.isNotEmpty() && data.dateResult!!.startTime.toLong() > 0L) {
                beginText!!.setContent(getDateStr(data.dateResult!!.startTime), false)
            } else {
                data.dateResult!!.startTime = 0L.toString()
                beginText!!.setContent("", false)
            }
        } catch (e: Exception) {
            data.dateResult!!.startTime = 0L.toString()
            beginText!!.setContent("", false)
        }
        if (isSingle) {
//            line.visibility = View.GONE
            endTime.visibility = View.GONE
        } else {
//            line.visibility = View.VISIBLE
            endTime.visibility = View.VISIBLE
            endText = TextProperty(data, endTime, true)
            endText!!.initView()
            try {
                if (data.dateResult != null && data.dateResult!!.endTime.isNotEmpty() && data.dateResult!!.endTime.toLong() > 0L) {
                    endText!!.setContent(getDateStr(data.dateResult!!.endTime), false)
                } else {//因为在endText!!.initView()中，widgetId相同，所以把开始时间的缓存取出来显示了
                    data.dateResult!!.endTime = 0L.toString()
                    endText!!.setContent("", false)
                }
            } catch (e: Exception) {
                data.dateResult!!.endTime = 0L.toString()
                endText!!.setContent("", false)
            }
        }
    }

    private fun iniData() {
        //读缓存
        if (data.modelId + data.widgetId + "dataResult" in approCacheMap.keys) {
            data.content = approCacheMap.get(data.modelId + data.widgetId + "dataResult")
        }
        if (data.dateResult == null) {
            data.dateResult = if (data.content != null && data.content!!.isNotEmpty()) {
                if (isSingle) {
                    val seg = DateSegResult()
                    seg.startTime = data.content!!
                    seg
                } else {
                    GsonUtil.fromJson<DateSegResult>(data.content!!)
                }
            } else {
                DateSegResult()
            }
        }
    }

    private var beginText: TextProperty? = null
    private var endText: TextProperty? = null

    fun addTimeSelectListener(begin: () -> Unit, end: () -> Unit) {
        beginText?.addListener {
            begin.invoke()
        }
        if (!isSingle) {
            endText?.addListener { end.invoke() }
        }
    }

    fun setBeginTime(start: Date? = null) {

        if (data.dateResult == null) {
            data.dateResult = DateSegResult()
        }
        if (start != null) {
            data.dateResult?.startTime = start.time.toString()
            beginText?.setContent(getDateStr(data.dateResult!!.startTime), false)
            getResult()//写缓存
        } else {
            beginText?.setContent("", false)
            data.dateResult?.startTime = 0L.toString()
        }
    }

    fun setEndTime(end: Date? = null) {

        if (data.dateResult == null) {
            data.dateResult = DateSegResult()
        }
        if (end != null) {
            data.dateResult?.endTime = end.time.toString()
            endText?.setContent(getDateStr(data.dateResult!!.endTime), false)
            getResult()//写缓存
        } else {
            endText?.setContent("", false)
            data.dateResult?.endTime = 0L.toString()
        }
    }
}//已缓存

class TimeCalcProperty(val data: WidgetInfo, private val rootView: View) : AprProperty {
    override fun checkResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        return false
    }

    override fun getType(): Int = data.type

    override fun getResult(): String {
        val result = if (data.dateResult == null) {
            DateSegResult()
        } else {
            data.dateResult!!
        }
        data.content = GsonUtil.toJson(result)
        return data.content!!
    }

    override fun getShortContent(): String {
        var info = ""
        if (beginText != null) {
            info = beginText!!.getShortContent()
        }
        if (endText != null) {
            info.plus("-").plus(endText!!.getShortContent())
        }
        return info
    }

    private lateinit var beginTime: View
    private lateinit var endTime: View

    private lateinit var calcText: View

    private fun getDateStr(time: String): String {
        val pattern = when (data.dateChoose) {
            1 -> {
                TIME_FORMAT_PATTERN4
            }
            2 -> {
                "HH:mm"
            }
            else -> {
                TIME_FORMAT_PATTERN1
            }
        }
        return XUtil.getTime(Date(time.toLong()), pattern)
    }

    override fun initView() {
        beginTime = rootView.findViewById(R.id.begin_time)
        endTime = rootView.findViewById(R.id.end_time)
        calcText = rootView.findViewById(R.id.auto_calc_time)
        beginText = TextProperty(data, beginTime)
        beginText!!.initView()
        endTime.visibility = View.VISIBLE
        endText = TextProperty(data, endTime, true)
        endText!!.initView()
        iniData()
    }

    private fun iniData() {
        if (data.dateResult == null) {
            data.dateResult = if (data.content != null && data.content!!.isNotEmpty()) {
                GsonUtil.fromJson<DateSegResult>(data.content!!)
            } else {
                DateSegResult()
            }
        }
        try {
            if (data.dateResult != null && data.dateResult!!.startTime.isNotEmpty() && data.dateResult!!.startTime.toLong() > 0L) {
                beginText!!.setContent(getDateStr(data.dateResult!!.startTime), false)
            }
        } catch (e: Exception) {
            data.dateResult!!.startTime = 0L.toString()
            beginText!!.setContent("", false)
        }

        try {
            if (data.dateResult != null && data.dateResult!!.endTime.isNotEmpty() && data.dateResult!!.endTime.toLong() > 0L) {
                endText!!.setContent(getDateStr(data.dateResult!!.endTime), false)
            }
        } catch (e: Exception) {
            data.dateResult!!.endTime = 0L.toString()
            endText!!.setContent("", false)
        }
    }

    private var beginText: TextProperty? = null
    private var endText: TextProperty? = null

    fun addTimeSelectListener(begin: () -> Unit, end: () -> Unit) {
        beginText?.addListener {
            begin.invoke()
        }
        endText?.addListener { end.invoke() }
    }

    fun setBeginTime(start: Date? = null) {
        if (data.dateResult == null) {
            data.dateResult = DateSegResult()
        }
        if (start != null) {
            data.dateResult?.startTime = start.time.toString()
            beginText?.setContent(getDateStr(data.dateResult!!.startTime), false)
        } else {
            beginText?.setContent("", false)
            data.dateResult?.startTime = 0L.toString()
        }
    }

    fun setEndTime(end: Date? = null) {
        if (data.dateResult == null) {
            data.dateResult = DateSegResult()
        }
        if (end != null) {
            data.dateResult?.endTime = end.time.toString()
            endText?.setContent(getDateStr(data.dateResult!!.endTime), false)
        } else {
            endText?.setContent("", false)
            data.dateResult?.endTime = 0L.toString()
        }
    }
}

interface OnTimeChooseListener {
    fun onChooseTime(index: Int)
}

class TimeDynamicCellProperty(val data: WidgetInfo, private val rootView: View) : AprProperty {
    private val cacheTag = data.modelId + data.widgetId + "dynamic"
    override fun checkResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        return false
    }

    override fun getType(): Int = data.type

    override fun getResult(): String {
        val noSelectTime = cacheTimes.find { it.isNullOrBlank() }
        if (noSelectTime != null) {
            return ""
        }
        return GsonUtil.toJson(cacheTimes)
    }

    override fun getShortContent(): String {
//        return tvTimeSelectors[0].getShortContent()
        val temp = tvTimeSelectors.joinToString(",") { it.getShortContent() }
        return if (temp.length > 30) temp.substring(0, 30) else temp
    }

    private val tvTimeSelectors = arrayListOf<TextProperty>()

    private fun getDateStr(time: String): String {
        val pattern = when (data.dateChoose) {
            1 -> {
                TIME_FORMAT_PATTERN4
            }
            2 -> {
                "HH:mm"
            }
            else -> {
                TIME_FORMAT_PATTERN1
            }
        }
        return XUtil.getTime(Date(time.toLong()), pattern)
    }

    private fun getTimeValue(dateInfo: String): String {
        val pattern = when (data.dateChoose) {
            1 -> {
                TIME_FORMAT_PATTERN4
            }
            2 -> {
                "HH:mm"
            }
            else -> {
                TIME_FORMAT_PATTERN1
            }
        }
        return XUtil.getTimeValue(dateInfo, pattern)
    }

    private lateinit var container: LinearLayout

    // 每行选择时间 缓存数据
    private val cacheTimes = arrayListOf<String>()

    override fun initView() {
        cacheTimes.clear()
        val  tipLayout:ConstraintLayout = rootView.findViewById(R.id.tip_view)
        val  tipTwoTv:TextView = rootView.findViewById(R.id.tip_two_tv)
        if (data.isLimit == 0) {
            tipLayout.visibility=View.GONE
        }else{
            tipLayout.visibility=View.VISIBLE
            tipTwoTv.text=data.limitRemind
        }
        container = rootView.findViewById(R.id.ll_time_container)
        val timeOne = rootView.findViewById<View>(R.id.time_one)
        tvTimeSelectors.add(TextProperty(data, timeOne).apply { initView() })
        cacheTimes.add("")
        //读缓存---用于回显的
        if (cacheTag in approCacheMap.keys) {
            approCacheMap[cacheTag]?.let { cache ->
                cacheTimes.clear()
                val cacheList = GsonUtil.fromJson<ArrayList<String>>(cache)
                if (!cacheList.isNullOrEmpty()) {
                    cacheTimes.addAll(cacheList)
                }
            }
        }
        if (cacheTimes.isNotEmpty()) {
            if (tvTimeSelectors.size < cacheTimes.size) {
                for (i in 0 until (cacheTimes.size - tvTimeSelectors.size)) {
                    addCell(true) // 初始化添加时间选择行
                }
            }
        }
        iniData()
    }

    private fun iniData() {
        val maxNum = if (data.maxNum != null && data.maxNum > 0) {
            data.maxNum
        } else {
            1
        }
        for (i in tvTimeSelectors.indices) {
            tvTimeSelectors[i].also { property ->
                if (cacheTimes[i].isBlank() || cacheTimes[i] == "0") {
                    property.setContent("")
                } else {
                    property.setContent(getDateStr(cacheTimes[i]))
                }
                property.rootView.tag = i
                property.rootView.setOnClickListener { cellView ->
                    showLog("点击选择时间")
                    listener?.onChooseTime(cellView.tag as Int)
                }
                property.rootView.findViewById<ImageView>(R.id.iv_add_cell).also { view ->
                    if (tvTimeSelectors.size < maxNum) {
                        view.isSelected = true
                        view.setOnClickListener {
                            if (tvTimeSelectors.size < maxNum) {
                                addCell()// 点击添加时间选择行
                            }
                        }
                    } else {
                        view.isSelected = false
                        view.setOnClickListener(null)
                    }
                    view.visibility = if (maxNum > 1) View.VISIBLE else View.GONE
                }

                property.rootView.findViewById<ImageView>(R.id.iv_del_cell).also { view ->
                    if (tvTimeSelectors.size == 1) {
                        view.isSelected = false
                        view.setOnClickListener(null)
                    } else {
                        view.isSelected = true
                        view.setOnClickListener {
                            if (tvTimeSelectors.size > 1) {
                                delCell(property.rootView.tag as Int)
                            }
                        }
                    }
                    view.visibility = if (maxNum > 1) View.VISIBLE else View.GONE
                }
            }
        }
    }

    private fun delCell(index: Int) {
        showLog("删除一行，清理时间选择器")
        container.removeViewAt(index)
        tvTimeSelectors.removeAt(index)
        cacheTimes.removeAt(index)
        updateCache()
        iniData()
    }

    private fun addCell(isInit: Boolean = false) {
        showLog("添加一行")
        val cellView =
            LayoutInflater.from(rootView.context).inflate(R.layout.property_text_selector, null)
        val params = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )
        container.addView(cellView, params)
        tvTimeSelectors.add(TextProperty(data, cellView).apply {
            initView()
            setContent("", false)
        })
        if (!isInit) {
            cacheTimes.add("")
            updateCache()
            iniData()
        }
    }

    var listener: OnTimeChooseListener? = null

    fun setTime(targetIndex: Int, date: Date? = null) {
        showLog("设置第${targetIndex + 1}行数据")
        val result = (date?.time ?: 0L).toString()
        if (!result.isNullOrBlank() && result != "0" && result in cacheTimes) {
            rootView.context.toastShort("当前${data.title}已存在")
        } else {
            if (cacheTimes.size <= targetIndex) return
            cacheTimes[targetIndex] = result
            updateCache()
            if (date != null) {
                tvTimeSelectors[targetIndex].setContent(getDateStr(date.time.toString()))
            } else {
                tvTimeSelectors[targetIndex].setContent("")
            }
            showLog("选中时间后，结果数据为：${getResult()}")
        }
    }

    private fun updateCache() {
        approCacheMap[cacheTag] = GsonUtil.toJson(cacheTimes)
    }

}

/**选择审批人*/
class SelectNodeProperty(val data: WidgetInfo, private val rootView: View) : AprProperty {
    override fun checkResult(requestCode: Int, resultCode: Int, intent: Intent?): Boolean {
        if (requestCode == 2001) {
            val resultData = intent?.getStringExtra("members")
            try {
                if (resultData != null && resultData.isNotEmpty()) {
                    val list = GsonUtil.getList2(resultData, SearchMemberBean::class.java)
//                            GsonUtil.fromJson(resultData, Array<SearchMemberBean>::class.java)?.toMutableList()
                    if (list != null && list.isNotEmpty()) {
                        updateData(list)
                    }
                }
            } catch (e: Exception) {
            }
            return true
        }
        return false
    }

    private fun updateData(list: MutableList<SearchMemberBean>) {
        showLog(selectNodeList)
//        selectNodeList.removeAt(selectNodeList.size - 1)
        selectNodeList.addAll(list)
//        selectNodeList.add(placeHolderData)
        updateView()
    }

    private lateinit var title: TextView
    private lateinit var tvHint: TextView
    private lateinit var topLine: View
    private lateinit var middleLine: View
    private lateinit var rvList: RecyclerView
    private lateinit var addPic: View
    private var itemTouchHelper: ItemTouchHelper? = null
    private val placeHolderData = SearchMemberBean()
    private val selectNodeList = arrayListOf<SearchMemberBean>()
//    lateinit var adapter: MyDragAdapter<SearchMemberBean>
    lateinit var adapter: MyAdapter<SearchMemberBean>

    override fun initView() {
        title = rootView.findViewById(R.id.tv_title)
        tvHint = rootView.findViewById(R.id.tv_hint)
        topLine = rootView.findViewById(R.id.line_top)
        middleLine = rootView.findViewById(R.id.line_middle)
        rvList = rootView.findViewById(R.id.rv_list)
        addPic = rootView.findViewById(R.id.cl_add_node)

//        AprUtil.initTitleStatus(title, data.isEdit)

        initData()

        if (!data.isEdit) {
            XUtil.hideView(title, topLine, tvHint, middleLine)
        } else {
            XUtil.showView(title, topLine, tvHint, middleLine)
        }

      /*  adapter = MyDragAdapter(rootView.context,
            layoutId = if (data.isEdit) {
                R.layout.item_apr_node_drag_list
            } else {
                R.layout.item_apr_node_list
            },
            dragViewId = R.id.iv_drag,
            data = selectNodeList,
            onBindItem = { _: MyHolder<SearchMemberBean>,
                           position: Int,
                           member: SearchMemberBean,
                           itemView: View ->
                if (data.isEdit) {
                    val picture = itemView.findViewById<ImageView>(R.id.iv_workmate_icon)
                    val name = itemView.findViewById<TextView>(R.id.tv_name)
                    val del = itemView.findViewById<ImageView>(R.id.iv_del)
                    val drag = itemView.findViewById<ImageView>(R.id.iv_drag)
//                        itemView.findViewById<View>(R.id.iv_drag).setOnTouchListener { v, event ->
//                            if (event.action == MotionEvent.ACTION_DOWN) {
//                                onStartDrag(holder)
//                            }
//                            true
//                        }
                    if (position == selectNodeList.size - 1) {
                        del.visibility = View.GONE
                        drag.visibility = View.GONE
                        name.text = "添加自选审批人"
                        picture.setImageResource(R.drawable.ic_add_copy)
                    } else {
                        del.visibility = View.VISIBLE
                        drag.visibility = View.VISIBLE
                        name.text = member.name
                        XUtil.loadRoundImage(
                            rootView.context, member.headimg, picture,
                            DeviceUtil.dip2px(rootView.context, 4f)
                        )
                        del.setOnClickListener {
                            onDel(position, member)
                        }
                    }
                } else {
                    XUtil.hideView(itemView.findViewById(R.id.tv_node_state_special))
                    XUtil.setText(itemView, R.id.tv_node_name, member.name)
                    if (member.isOuter == 1) {
                        XUtil.showView(itemView.findViewById(R.id.externalTag))
                    } else XUtil.hideView(itemView.findViewById(R.id.externalTag))
                    XUtil.loadRoundImage(
                        itemView.context, member.headimg,
                        itemView.findViewById(R.id.iv_node_avatar),
                        DeviceUtil.dip2px(itemView.context, 4f)
                    )
                    if (position == 0) {
                        XUtil.hideView(itemView.findViewById(R.id.line_top))
                    } else {
                        XUtil.showView(itemView.findViewById(R.id.line_top))
                    }
                    if (position == selectNodeList.size - 1) {
                        XUtil.hideView(itemView.findViewById(R.id.line_bottom))
                    } else {
                        XUtil.showView(itemView.findViewById(R.id.line_bottom))
                    }
                    if (member.content.isNotEmpty()) {
                        XUtil.showView(itemView.findViewById(R.id.tv_node_opinion))
                        XUtil.setText(itemView, R.id.tv_node_opinion, "审批意见:${member.content}")
                    } else {
                        XUtil.hideView(itemView.findViewById(R.id.tv_node_opinion))
                    }
                    if (member.retrialCreator != null && member.retrialCreator!!.isNotEmpty()) {
                        XUtil.showView(itemView.findViewById(R.id.tv_node_retrial_creator))
                        XUtil.setText(
                            itemView,
                            R.id.tv_node_retrial_creator,
                            "由${member.retrialCreator!!}邀请加审"
                        )
                    } else {
                        XUtil.hideView(itemView.findViewById(R.id.tv_node_retrial_creator))
                    }
                    if (member.createTime > 0L) {
                        XUtil.showView(itemView.findViewById(R.id.tv_node_time))
                        XUtil.setText(
                            itemView,
                            R.id.tv_node_time,
                            XUtil.turnToTimeStr(member.createTime, TIME_FORMAT_PATTERN1)
                        )
                    } else {
                        XUtil.hideView(itemView.findViewById(R.id.tv_node_time))
                    }
                    AprUtil.getOpinion(member.opinion) { title: String, color: Int, icon: Int ->
                        XUtil.setText(itemView, R.id.tv_node_state, title)
                        itemView.findViewById<TextView>(R.id.tv_node_state)
                            .setTextColor(CommonUtils.getColor(rootView.context, color))
                        itemView.findViewById<ImageView>(R.id.iv_node_icon).setImageResource(icon)
                    }
                    if (member.opinion == 4 && member.isOuter == 1) {
                        //外部联系人已被删除，审批被退回
                        //一种特殊情况
                        XUtil.hideView(itemView.findViewById(R.id.externalTag))
                        XUtil.hideView(itemView.findViewById(R.id.tv_node_state))
                        XUtil.showView(itemView.findViewById(R.id.tv_node_state_special))
                        itemView.findViewById<ImageView>(R.id.iv_node_icon)
                            .setImageResource(R.drawable.icon_node_stop)
                    }
                }
            },
            onItemClick = { position: Int, _: SearchMemberBean, _: View ->
                if (data.isEdit && position == selectNodeList.size - 1) {
                    listener?.onSelect(this)
                }
            }
        )*/

        //新版使用的adapter,,tcp
        adapter = MyAdapter(rootView.context,
            R.layout.item_approval_list_2,
            selectNodeList,
            onBindItem = { position: Int, path: SearchMemberBean, itemView: View ->
                val del = itemView.findViewById<ImageView>(R.id.iv_del)
                val picture = itemView.findViewById<CircleImageView>(R.id.iv_pic)
                val name = itemView.findViewById<TextView>(R.id.tv_name)
                XUtil.showView(name)
                name.text = path.name

                if (data.isEdit) {
                    if (position == 0) {
                        del.visibility = View.GONE
                        itemView.findViewById<ImageView>(R.id.arrow_tag_iv).visibility = View.GONE
                        picture.setImageResource(R.drawable.ic_add_2)
                    } else {
                        del.visibility = View.VISIBLE
                        if (position==selectNodeList.lastIndex) {
                            itemView.findViewById<ImageView>(R.id.arrow_tag_iv).visibility = View.GONE
                        } else {
                            itemView.findViewById<ImageView>(R.id.arrow_tag_iv).visibility = View.VISIBLE
                        }
                        XUtil.loadRoundImage(
                            rootView.context,
                            path.headimg,
                            picture,
                            DeviceUtil.dip2px(rootView.context, 4f)
                        )
                        del.setOnClickListener {
                            onDel(position,path)
                        }
                    }
                }

            },
            onItemClick = { position: Int, s: SearchMemberBean, view: View ->
                if (data.isEdit && position == 0) {
                    listener?.onSelect(this)
                }
            }
        )

        if (data.isEdit) {
            addPic.setOnClickListener { listener?.onSelect(this) }
            if (selectNodeList.size > 1) {
                addPic.visibility = View.GONE
                rvList.visibility = View.VISIBLE
            } else {
                addPic.visibility = View.VISIBLE
                rvList.visibility = View.GONE
            }

//            adapter.attachDragEvent(rvList)
        } else {
            addPic.visibility = View.GONE
            rvList.visibility = View.VISIBLE
        }

        rvList.layoutManager = GridLayoutManager(rootView.context, 6)
        rvList.adapter = adapter
    }

    private fun initData() {
        //选择审批人  读缓存
        if (data.modelId + data.widgetId + "nodeResult" in approCacheMap.keys) {
            val cacheJson = approCacheMap.get(data.modelId + data.widgetId + "nodeResult")
            if (cacheJson != null) {
                data.selectNodeResult =
                    GsonUtil.fromJson(cacheJson, Array<SearchMemberBean>::class.java)
                        ?.toMutableList()
            }
        }


        selectNodeList.clear()
        if (data.selectNodeResult == null || data.selectNodeResult!!.isEmpty()) {
            data.selectNodeResult = arrayListOf()
            if (data.isEdit) {
                data.selectNodeResult!!.add(placeHolderData)
            }
            if (!data.isEdit && data.content != null && data.content!!.isNotEmpty()) {
                try {
                    val list = GsonUtil.fromJson(data.content, Array<SearchMemberBean>::class.java)
                        ?.toMutableList()
                    if (list != null) {
                        data.selectNodeResult!!.addAll(list)
                    }
                } catch (e: Exception) {
                }
            }

        }
        selectNodeList.addAll(data.selectNodeResult!!)
    }

    var listener: OnSelectNodeListener? = null

    private fun onDel(position: Int, member: SearchMemberBean) {
        val realIndex = selectNodeList.indexOf(member)
        showLog("position = $position, realIndex = $realIndex")
        selectNodeList.remove(member)
        updateView()
    }

    private fun updateView() {
        adapter.notifyDataSetChanged()
        data.selectNodeResult!!.clear()
        data.selectNodeResult!!.addAll(selectNodeList)

        //选择审批人  写缓存
        approCacheMap.put(
            data.modelId + data.widgetId + "nodeResult",
            GsonUtil.toJson(data.selectNodeResult)
        )

        if (selectNodeList.size > 1) {
            addPic.visibility = View.GONE
            rvList.visibility = View.VISIBLE
        } else {
            addPic.visibility = View.VISIBLE
            rvList.visibility = View.GONE
        }
    }

    override fun getType(): Int = data.type

    override fun getResult(): String {
        val result = if (data.selectNodeResult == null || data.selectNodeResult!!.isEmpty()) {
            arrayListOf()
        } else {
            val temp = arrayListOf<String>()
            for (i in 0 until data.selectNodeResult!!.size - 1) {
                temp.add(data.selectNodeResult!![i].userId)
            }
            temp
        }
        data.content = GsonUtil.toJson(result)

        return data.content!!
    }

    override fun getShortContent(): String {
        val count = if (data.selectNodeResult == null || data.selectNodeResult!!.isEmpty()) {
            "0位成员"
        } else if (data.selectNodeResult!!.size == 2) {
            data.selectNodeResult!![0].name
        } else {
            "${data.selectNodeResult!!.size - 1}位成员"
        }
        return data.title + AprProperty.charTag + count + "位成员"
    }
}

/**
 * 发起时：选择抄送人
 * */
class SelectMateProperty(val data: WidgetInfo, private val rootView: View) : AprProperty {

    lateinit var tv_content: TextView
    lateinit var tv_title: TextView
    lateinit var rvList: RecyclerView
    private val selectMateList = arrayListOf<SearchMemberBean>()
    private val placeHolderData = SearchMemberBean()
    lateinit var adapter: MyAdapter<SearchMemberBean>

    var listener: OnSelectMateListener? = null

    lateinit var addPic: ImageView
    lateinit var addTitle: TextView

    override fun initView() {
        tv_content = rootView.findViewById(R.id.tv_content)
        tv_title = rootView.findViewById(R.id.tv_title)
        rvList = rootView.findViewById(R.id.rv_list)
        initData()
        adapter = MyAdapter(rootView.context,
            if (data.isEdit) R.layout.item_mate_list_2 else R.layout.item_mate_list_detail,
            selectMateList,
            onBindItem = { position: Int, path: SearchMemberBean, itemView: View ->
                val del = itemView.findViewById<ImageView>(R.id.iv_del)
                val picture = itemView.findViewById<CircleImageView>(R.id.iv_pic)
                val name = itemView.findViewById<TextView>(R.id.tv_name)
                XUtil.showView(name)
                name.text = path.name

                if (data.isEdit) {
                    if (position == 0) {
                        del.visibility = View.GONE
                        itemView.findViewById<ImageView>(R.id.add_tag_iv).visibility = View.GONE
                        picture.setImageResource(R.drawable.ic_add_2)
                    } else {
                        del.visibility = View.VISIBLE
                        if (position==selectMateList.lastIndex) {
                            itemView.findViewById<ImageView>(R.id.add_tag_iv).visibility = View.GONE
                        } else {
                            itemView.findViewById<ImageView>(R.id.add_tag_iv).visibility = View.VISIBLE
                        }
                        XUtil.loadRoundImage(
                            rootView.context,
                            path.headimg,
                            picture,
                            DeviceUtil.dip2px(rootView.context, 4f)
                        )
                        del.setOnClickListener {
                            onDel(position)
                        }
                    }
                } else {
                    DeviceUtil.getTextWidth(PROPERTY_TITLE_MODEL, name.textSize)
                    del.visibility = View.GONE
                    XUtil.loadRoundImage(
                        rootView.context,
                        path.headimg,
                        picture,
                        DeviceUtil.dip2px(rootView.context, 4f)
                    )
                }

            },
            onItemClick = { position: Int, s: SearchMemberBean, view: View ->
                if (data.isEdit && position == 0) {
                    listener?.onSelect(this)
                }
            }
        )
        addPic = rootView.findViewById(R.id.iv_add_pic)
        addTitle = rootView.findViewById(R.id.tv_copy_title)
        addPic.setOnClickListener { listener?.onSelect(this) }
        addTitle.setOnClickListener { listener?.onSelect(this) }
//        if (data.isEdit) {
        if (selectMateList.size > 1) {
            addPic.visibility = View.GONE
            addTitle.visibility = View.GONE
            rvList.visibility = View.VISIBLE
        } else {
            addPic.visibility = View.VISIBLE
            addTitle.visibility = View.VISIBLE
            rvList.visibility = View.GONE
        }

        rvList.layoutManager = GridLayoutManager(rootView.context, 6)
        rvList.adapter = adapter
    }

    private fun initData() {

        //选择抄送人  读缓存
        if (data.modelId + data.widgetId + "copyResult" in approCacheMap.keys) {
            val cacheJson = approCacheMap.get(data.modelId + data.widgetId + "copyResult")
            if (cacheJson != null) {
                data.selectCopyResult =
                    GsonUtil.fromJson(cacheJson, Array<SearchMemberBean>::class.java)
                        ?.toMutableList()
            }
        }

        selectMateList.clear()
        if (data.selectCopyResult.isNullOrEmpty()) {
            data.selectCopyResult = arrayListOf()
            if (data.isEdit) {
                data.selectCopyResult!!.add(placeHolderData)
            }
            if (!data.isEdit && data.content != null && data.content!!.isNotEmpty()) {
                try {
                    val list = GsonUtil.fromJson(data.content, Array<SearchMemberBean>::class.java)
                        ?.toMutableList()
                    if (list != null) {
                        data.selectCopyResult!!.addAll(list)
                    }
                } catch (e: Exception) {
                }
            }

        }
        selectMateList.addAll(data.selectCopyResult!!)
    }

    private fun onDel(position: Int) {
        selectMateList.removeAt(position)
        updateView()
    }

    override fun checkResult(requestCode: Int, resultCode: Int, intent: Intent?): Boolean {
        if (requestCode == 3001) {
            val resultData = intent?.getStringExtra("members")
            try {
                if (resultData != null && resultData.isNotEmpty()) {
                    val list = GsonUtil.fromJson(resultData, Array<SearchMemberBean>::class.java)
                        ?.toMutableList()
//                    selectMateList.clear()
                    if (list != null && list.isNotEmpty()) {
                        updateData(list)
                    }
                }
            } catch (e: Exception) {
            }
            return true
        }
        return false
    }

    private fun updateData(list: List<SearchMemberBean>) {
//        selectMateList.removeAt(selectMateList.size - 1)
        selectMateList.addAll(list)
//        selectMateList.add(placeHolderData)
        updateView()
    }

    private fun updateView() {
        adapter.notifyDataSetChanged()
        data.selectCopyResult!!.clear()
        data.selectCopyResult!!.addAll(selectMateList)

        //选择抄送人  写缓存
        approCacheMap.put(
            data.modelId + data.widgetId + "copyResult",
            GsonUtil.toJson(data.selectCopyResult)
        )

        if (selectMateList.size > 1) {
            addPic.visibility = View.GONE
            addTitle.visibility = View.GONE
            rvList.visibility = View.VISIBLE
        } else {
            addPic.visibility = View.VISIBLE
            addTitle.visibility = View.VISIBLE
            rvList.visibility = View.GONE
        }
    }

    override fun getType(): Int = data.type

    override fun getResult(): String {
        val result = if (data.selectCopyResult == null) {
            arrayListOf()
        } else {
            val temp = arrayListOf<String>()
            for (i in 0 until data.selectCopyResult!!.size - 1) {
                temp.add(data.selectCopyResult!![i].userId)
            }
            temp
        }
        data.content = GsonUtil.toJson(result)
        return data.content!!
    }

    override fun getShortContent(): String {
        val count = if (data.selectCopyResult == null || data.selectCopyResult!!.isEmpty()) {
            "0位成员"
        } else if (data.selectCopyResult!!.size == 2) {
            data.selectCopyResult!![1].name
        } else {
            "${data.selectCopyResult!!.size - 1}位成员"
        }
        return data.title + AprProperty.charTag + count
    }
}

/**
 * 预览时：抄送人,选择人员预览
 *
 */
class SelectPersonProperty(
    val data: WidgetInfo, private val rootView: View,
    private val onShowMore: (member: SearchMemberBean) -> Unit
) : AprProperty {
    override fun checkResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        return false
    }

    lateinit var tvTitle: TextView
    private lateinit var rvList: RecyclerView
    private val showPersonList = arrayListOf<SearchMemberBean>()
    private lateinit var adapter: AprDetailCopyPersonAdapter

    var listener: OnSelectMateListener? = null

    private val spanCount = 5

    override fun initView() {
        tvTitle = rootView.findViewById(R.id.tv_title)
        if (data.type !in arrayOf(30, 31)) {
            AprUtil.initTitleStatus(tvTitle, data.isEdit)
        }
        rvList = rootView.findViewById(R.id.rv_list)
        initData()
        adapter = AprDetailCopyPersonAdapter(rootView.context, showPersonList, onShowMore)
        tvTitle.text = data.title
        rvList.layoutManager = GridLayoutManager(rootView.context, spanCount)
        rvList.adapter = adapter

        val tvContent = rootView.findViewById<TextView>(R.id.tv_content)
        if (data.type in arrayOf(30, 31)) {
            if (data.type == 31) {
                tvContent.visibility = View.GONE
            } else {
                tvContent.text = if (data.prompt.isNotBlank() && "0" != data.prompt) {
                    try {
                        XUtil.turnToTimeStr(data.prompt.toLong(), TIME_FORMAT_PATTERN1)
                    } catch (e: Exception) {
                        ""
                    }
                } else {
                    ""
                }
                tvContent.visibility = View.VISIBLE
            }
            if (showPersonList.size > 0) {
                rvList.visibility = View.VISIBLE
            }
        }
    }

    private fun initData() {
        showPersonList.clear()
        if (data.selectCopyResult == null) {
            data.selectCopyResult = arrayListOf()
            if (!data.isEdit && data.content != null && data.content!!.isNotEmpty()) {
                try {
                    val list = GsonUtil.fromJson(data.content, Array<SearchMemberBean>::class.java)
                        ?.toMutableList()
                    if (list != null) {
                        data.selectCopyResult!!.addAll(list)
                    }
                } catch (e: Exception) {
                }
            }
        }

        if (data.selectCopyResult!!.isNotEmpty()) {
            val limit = spanCount * 2
            if (data.selectCopyResult!!.size > limit) {
                for (i in 0 until data.selectCopyResult!!.size) {
                    if (i < limit - 1) {
                        showPersonList.add(data.selectCopyResult!![i])
                    } else {
                        showPersonList.add(SearchMemberBean(headimg = "--"))
                        break
                    }
                }
            } else {
                showPersonList.addAll(data.selectCopyResult!!)
            }
        }
    }

    override fun getType(): Int = data.type

    override fun getResult(): String {
        val result = if (data.selectCopyResult == null) {
            arrayListOf()
        } else {
            val temp = arrayListOf<String>()
            for (i in 0 until data.selectCopyResult!!.size - 1) {
                temp.add(data.selectCopyResult!![i].userId)
            }
            temp
        }
        data.content = GsonUtil.toJson(result)
        return data.content!!
    }

    override fun getShortContent(): String {
        return ""
    }
}

/**选择图片属性控件（上传）*/
class SelectPicProperty(
    val data: WidgetInfo, private val rootView: View,
    /**上传成功的文件记录*/
    private val upSuccessRecord: HashMap<String, String>,
    private val limit: Int = 50
) : AprProperty {

    private val cacheTag = data.modelId + data.widgetId + "selectPicList"
    override fun checkResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        if (requestCode == IMAGE_CUT_CODE) {
            if (data != null) {
                val selectList = PictureNewHelper.afterSelectPhotoInfo(data)
                if (selectList.isNotEmpty()) {
                   /* val hadCount = if (selectPicList.last() == placeHolderData) {
                        selectPicList.size - 1
                    } else {
                        selectPicList.size
                    }*/
                    val hadCount= selectPicList.size
                    if ((hadCount + selectList.size) > limit) {
                        Toast.makeText(rootView.context, "超过图片上限 $limit 张", Toast.LENGTH_SHORT)
                            .show()
                        return false
                    }

                    if (!selectPicList.isNullOrEmpty() && selectPicList.last() == placeHolderData) {
                        selectPicList.removeAt(selectPicList.lastIndex)
                    }

                    for (media in selectList) {
                        selectPicList.add(
                            UploadFileBean(
                                fileId = "", hash = "",
                                fileName = media["fileName"] as String,
                                fileUrl = media["compressPath"] as String
                            )
                        )
                    }
                    //新版修改位置
                  /*  if (selectPicList.size < limit) {
                        selectPicList.add(placeHolderData)
                    }*/
                    updateData()
                }
            }
            return true
        }
        return false
    }

    lateinit var required: TextView
    lateinit var title: TextView
    lateinit var content: TextView
    private lateinit var addPic: ImageView
    private lateinit var rvList: RecyclerView
    lateinit var adapter: MyAdapter<UploadFileBean>

    /**图片占位*/
    private val placeHolderData = UploadFileBean(isUploadFlag = true)

    /**选中的图片 末尾包含占位符，用来显示添加图片按钮*/
    private val selectPicList = arrayListOf<UploadFileBean>()
    private val progressHash = HashMap<String, Int>()

    var listener: OnSelectPicListener? = null

    @SuppressLint("SetTextI18n")
    override fun initView() {
        required = rootView.findViewById(R.id.tv_required)
        title = rootView.findViewById(R.id.tv_title)
        content = rootView.findViewById(R.id.tv_content)
        addPic = rootView.findViewById(R.id.iv_add_pic)
        rvList = rootView.findViewById(R.id.rv_list)

        title.text = data.title
        content.text = data.prompt

        selectPicList.clear()
        // 初始化 selectItemResult 图片选中缓存

        if (data.selectFileResult == null || data.selectFileResult!!.isEmpty()) {
            data.selectFileResult = arrayListOf()
        }
        // 初始化 selectPicList 图片适配器数据
        selectPicList.addAll(data.selectFileResult!!)
        //新版修改位置
       /* if (selectPicList.size < limit) {
            if (!selectPicList.isNullOrEmpty()) {
                if (selectPicList.last() != placeHolderData) {
                    selectPicList.add(placeHolderData)
                }
            } else {
                selectPicList.add(placeHolderData)
            }
        }*/
        adapter = MyAdapter(rootView.context, R.layout.item_pic_list, selectPicList,
            onBindItem = { position: Int, path: UploadFileBean, itemView: View ->
                val del = itemView.findViewById<ImageView>(R.id.iv_del)
                val picture = itemView.findViewById<ImageView>(R.id.iv_pic)
                val ivProgress = itemView.findViewById<ImageView>(R.id.iv_progress)
                val tvProgress = itemView.findViewById<TextView>(R.id.tv_progress)
                ivProgress.visibility = View.GONE
                tvProgress.visibility = View.GONE
                if (position == selectPicList.lastIndex && selectPicList[position] == placeHolderData) {
                    //新版不会执行
                    del.visibility = View.GONE
                    picture.setImageResource(R.drawable.ic_add_pic)
                    picture.setOnClickListener {
                        listener?.onSelect(property = this, selectedCount = selectPicList.size - 1)
                    }
                } else {
                    // 上传成功或者上传进度为100 隐藏进度条遮罩
                    if (upSuccessRecord.containsKey(path.fileUrl) ||
                        (progressHash.containsKey(path.fileUrl) && progressHash[path.fileUrl] == 100)
                    ) {
                        ivProgress.visibility = View.GONE
                        tvProgress.visibility = View.GONE
                        del.visibility = View.VISIBLE
                    } else {
                        del.visibility = View.GONE
                        ivProgress.visibility = View.VISIBLE
                        tvProgress.visibility = View.VISIBLE
                        val progress =
                            if (progressHash.isNotEmpty() && progressHash.containsKey(path.fileUrl)) {
                                progressHash[path.fileUrl]!!
                            } else {
                                0
                            }
                        tvProgress.text = "$progress%"
                    }

                    XUtil.loadImage(rootView.context, picture, path.fileUrl)
                    del.setOnClickListener {
                        onDel(position)
                    }
                    picture.setOnClickListener {
                        val previewPicList: ArrayList<String> = arrayListOf()
                        previewPicList.addAll(
                            selectPicList.filter { it.fileUrl.isNotBlank() }
                                .map { it.fileUrl }
                                .toList())
                        listener?.onPreview(position, previewPicList)
                    }
                }
            },
            onItemClick = { _: Int, _: UploadFileBean, _: View ->

            })
        rvList.layoutManager = GridLayoutManager(rootView.context, 5)
        rvList.adapter = adapter

        if (data.required == "1") {
            required.visibility = View.VISIBLE
        } else {
            required.visibility = View.INVISIBLE
        }
        if (selectPicList.size >= 1) {
//            addPic.visibility = View.GONE
            rvList.visibility = View.VISIBLE
        } else {
            addPic.visibility = View.VISIBLE
            rvList.visibility = View.GONE
        }
        addPic.setOnClickListener {
            listener?.onSelect(
                this, selectPicList.size//新版修改位置，旧版的时候多个-1
            )
        }


    }

    private lateinit var activity: AprBaseActivity<ViewBinding>
    private var companyId: String = ""

    fun setViewModel(activity: AprBaseActivity<ViewBinding>, companyId: String) {
        this.activity = activity
        this.companyId = companyId
        if (companyId.isNullOrBlank()) {
            this.companyId = CompanyHolder.getCurrentOrg()?.companyId ?: ""
        }

        //读缓存--选择图片
        if (cacheTag in approCacheMap.keys) {
            val cacheJson = approCacheMap[cacheTag]
            if (!cacheJson.isNullOrBlank()) {
                if (data.type == 10) {
                    val list = GsonUtil.fromJson2<ArrayList<UploadFileBean>>(cacheJson,
                        object : TypeToken<ArrayList<UploadFileBean>>() {}.type
                    )
                    if (list != null) {
                        selectPicList.clear()
                        selectPicList.addAll(list)
                        updateView()// 选择图片后调
                        uploadFile()// 上传图片
                    }
                }
            }
        }

    }

    /**编辑状态下接收新选择的图片*/
    private fun updateData() {
        //选择图片  写缓存
        updateCache()
        updateView()// 选择图片后调
        uploadFile()// 上传图片
    }

    //选择图片  写缓存
    private fun updateCache() {
        approCacheMap[cacheTag] = GsonUtil.toJson(selectPicList)
    }

    private fun onDel(position: Int) {
        selectPicList.removeAt(position)
        //新版修改位置
        /*if (selectPicList.size < limit && selectPicList.last() != placeHolderData) {
            selectPicList.add(placeHolderData)
        }*/
        updateCache()
        updateView()// 删除图片后调
    }

    /**选择图片后
     *      适配器刷新
     *      更新用户选择图片缓存 selectItemResult
     *      判断图片列表和图片添加图标显示
     * */
    private fun updateView() {
        adapter.notifyDataSetChanged()
        data.selectFileResult!!.clear()
        data.selectFileResult!!.addAll(selectPicList)

        if (selectPicList.size >= 1) {
//            addPic.visibility = View.GONE
            rvList.visibility = View.VISIBLE
        } else {
            addPic.visibility = View.VISIBLE
            rvList.visibility = View.GONE
        }
    }

    /**上传图片*/
    @SuppressLint("SetTextI18n")
    private fun uploadFile() {
        val temp = mutableListOf<UploadFileBean>()
        if (!data.selectFileResult.isNullOrEmpty()) {
            temp.addAll(data.selectFileResult!!)
            if (temp.last() == placeHolderData) {
                temp.removeAt(temp.size - 1)
            }
        }
        if (temp.isEmpty()) {
            return
        }
        val currentUploadFiles = arrayListOf<UploadFileBean>()
        for (file in temp) {
            /**已完成或者正在上传的文件不再加入上传队列*/
            /*上传已完成和上传进行中都不包含时，才加入上传队列*/
            if ((upSuccessRecord.isNotEmpty() && upSuccessRecord.containsKey(file.fileUrl))
                || (progressHash.isNotEmpty() && progressHash.containsKey(file.fileUrl))
            ) {
                continue
            } else {
                // 添加到上传记录中，进度为0
                currentUploadFiles.add(file)
                progressHash[file.fileUrl] = 0
            }
        }
        if (currentUploadFiles.isNotEmpty()) {
            checkCapacity(currentUploadFiles)
        }
    }

    /**检查文件上传空间是否满足*/
    private fun checkCapacity(currentUploadFiles: ArrayList<UploadFileBean>) {
        val tag = "检查存储空间"
        RxScheduleUtil.rxSchedulerHelper(
            DdbesApiUtil.getTaskService().searchCompanyCapacity(
                UserHolder.getAccessToken(), companyId
            )
        ).compose(ErrorTransformer.getInstance())
            .subscribe(object : BaseSubscriber<CapacityBean>() {
                override fun onError(ex: ApiException) {
                    RequestHelper.showLogLine(msg = ">>>$tag 返回错误<<<")
                    try {
                        ToastUtil.show(BaseApplication.joinuTechContext, "上传图片失败")
                    } catch (e: Exception) {
                    }
                }

                override fun onComplete() {
                    RequestHelper.showLogLine(msg = ">>>$tag 获取结束<<<")
                }

                override fun onNext(result: CapacityBean?) {
                    if (result != null) {
                        RequestHelper.showLogLine(msg = ">>>$tag 返回数据<<<")
                        onCapacityResponse(currentUploadFiles, result)
                    } else {
                        RequestHelper.showLogLine(msg = ">>>$tag 未返回数据<<<")
                    }
                }
            })
    }

    fun onCapacityResponse(currentUploadFiles: ArrayList<UploadFileBean>, capacity: CapacityBean) {
        showLog("查看容量结果回调 ----", "file_up__")
        val canUsed = capacity.capacity - capacity.used
        var computerFileTotalSize = 0L
        currentUploadFiles.forEach { bean ->
            computerFileTotalSize += File(bean.fileUrl).length()
        }
        //判断是否容量可用
        if (computerFileTotalSize <= canUsed) {
            haveCapacity(activity, currentUploadFiles)
        } else {
            val dialog = BaseCenterDialogHelper(
                activity = activity,
                onBindView = { view ->
                    run {
                        val cancel = view.findViewById<TextView>(R.id.cancel)
                        val lineV = view.findViewById<View>(R.id.line_v)
                        val tvContent = view.findViewById<TextView>(R.id.tv_content)
                        tvContent.text = "团队云盘存储空间已满，无法上传更多图片/附件。" +
                                "请登录pan.ddbes.com了解更多存储详情"
                        val tvTitle = view.findViewById<TextView>(R.id.tv_title)
                        val tvHint = view.findViewById<TextView>(R.id.tv_hint)
                        tvTitle.visibility = View.GONE
                        tvHint.visibility = View.GONE
                        cancel.visibility = View.GONE
                        lineV.visibility = View.GONE
                    }
                },
                onConfirm = {}, onCancel = {})
            dialog.initView()
            dialog.show(true)
        }
    }

    /**云空间可用时处理文件上传*/
    private fun haveCapacity(
        activity: AprBaseActivity<ViewBinding>,
        currentUploadFiles: ArrayList<UploadFileBean>
    ) {
        showLog("容量够用准备上传 获取md5 获取文件id 上传文件 ----", "file_up__")
        val perms = arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )
        val tips = "选取图片需要你授权读写"
        PermissionUtils.requestPermissionActivity(activity, perms, tips,
            onSuccess = {
                //先获取文件的md5，再通过后台获取上传文件的id值，再去上传文件
                Flowable.create(FlowableOnSubscribe<HashMap<String, String>> { emitter ->
                    try {
                        // 去重需要上传的文件，hashMap中key为要上传文件的路径，value为文件对应的hash
                        val hashMap = hashMapOf<String, String>()
                        currentUploadFiles.forEach { bean ->
                            if (hashMap.isEmpty() || !hashMap.containsKey(bean.fileUrl)) {
                                val hashValue = StringUtils.getFileMD5(File(bean.fileUrl))
                                if (StringUtils.isNotBlankAndEmpty(hashValue)) {
                                    hashMap[bean.fileUrl] = hashValue
                                }
                            }
                        }
                        // 上传文件转换为map记录即将上传的数据，文件路径:对应文件的hash
                        if (hashMap.isNotEmpty()) {
                            emitter.onNext(hashMap)
                        } else {
                            emitter.onNext(hashMapOf())
                        }
                    } catch (e: Exception) {
                        emitter.onNext(hashMapOf())
                    }
                }, BackpressureStrategy.BUFFER)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe { hashValues ->
                        showLog("获取hash完成，开始请求文件id ${GsonUtil.toJson(hashValues)}", "file_up__")
                        if (!hashValues.isNullOrEmpty()) {
                            // 取出需要上传文件的 hash 值集合，请求fileId信息，判断文件在云盘是否存在
                            val hashList = hashValues.values.toList()
                            val map = hashMapOf<String, Any>()
                            map["hash"] = hashList
                            UploadFileService.getPanTencentId(UserHolder.getAccessToken(), map)
                                .compose(activity.bindToLifecycle())
                                .compose(ErrorTransformer.getInstance())
                                .subscribe(object : BaseSubscriber<List<PanFileBean>>() {
                                    override fun onError(ex: ApiException) {
                                        activity.dismissDialog()
                                        ToastUtil.show(BaseApplication.joinuTechContext, ex.message)
                                    }

                                    override fun onComplete() {

                                    }

                                    override fun onNext(cloudFileInfoList: List<PanFileBean>?) {
                                        showLog(
                                            "获取文件id 结果回调 ${GsonUtil.toJson(cloudFileInfoList)} ----",
                                            "file_up__"
                                        )
                                        if (!cloudFileInfoList.isNullOrEmpty()) {
                                            val emptyFileIdList =
                                                cloudFileInfoList.filter { it.key.isNullOrBlank() || it.id.isNullOrBlank() }
                                            if (emptyFileIdList.isNotEmpty()) {
                                                rootView.context.toastShort("当前返回fileId存在空异常")
                                                rootView.context.toastShort(
                                                    GsonUtil.toJson(
                                                        cloudFileInfoList
                                                    )
                                                )
                                                return
                                            }
//                                                        // 更新需要上传文件的云端信息
//                                                        currentUploadFiles.forEach { file ->
//                                                            // 返回云盘文件信息，根据上传hash顺序返回，所以要
//                                                            // 先找到文件hash，确定文件在结果中顺序，再取到当前文件的云端存储信息
//                                                            val hash = hashValues[file.fileUrl]
//                                                            if (!hash.isNullOrBlank()) {
//                                                                val data = cloudFileInfoList[hashList.indexOf(hash)]
//                                                                file.fileId = data.id
//                                                                file.isUploadFlag = data.exist
//                                                                file.hash = hash
//                                                                // 要上传的文件在云端已经存在时，设置上传记录中进度为100，已上传文件中设置文件路径对应云端id即fileId
//                                                                if (data.exist) {
//                                                                    progressHash[file.fileUrl] = 100
//                                                                    upSuccessRecord[file.fileUrl] = data.id
//                                                                }
//                                                            }
//                                                        }
                                            // 更新已选择文件的云端信息
                                            val temp = hashMapOf<String, UploadFileBean>()
                                            data.selectFileResult?.forEach { file ->
                                                val hash = hashValues[file.fileUrl]
                                                if (!hash.isNullOrBlank()) {
                                                    val data =
                                                        cloudFileInfoList[hashList.indexOf(hash)]
                                                    file.fileId = data.key
                                                    file.isUploadFlag = data.exist
                                                    file.hash = hash
                                                    // 要上传的文件在云端已经存在时，设置上传记录中进度为100，已上传文件中设置文件路径对应云端id即fileId
                                                    if (data.exist) {
                                                        progressHash[file.fileUrl] = 100
                                                        upSuccessRecord[file.fileUrl] = data.id
                                                    } else {
                                                        // 云端不存在的文件加入需要上传文件的集合中
                                                        temp[file.fileUrl] = file
                                                    }
                                                }
                                            }
                                            // 过滤出需要上传文件信息，云端不存在且hash不为空
//                                                        val needUploadFile = currentUploadFiles.filter { !it.isUploadFlag && !it.hash.isNullOrBlank() }.toMutableList()
                                            //有需要上传的文件上传，没有需要的话刷新列表
                                            if (!temp.isNullOrEmpty())
                                                dealUploadEvent(temp.values.toMutableList() as ArrayList<UploadFileBean>)
                                            else {
                                                adapter.notifyDataSetChanged()
                                            }
                                        }
                                    }
                                })
                        }
                    }
            },
            onError = {
                ToastUtil.show(BaseApplication.joinuTechContext, tips)
            })
    }

    private fun dealUploadEvent(files: ArrayList<UploadFileBean>) {
        FileUploadUtil.uploadMultiFileWithProgress(
            hashList = files,
            onProgress = { filePath, _, _, percent ->
                activity.runOnUiThread {
                    // 更新进度时是按文件路径更新，所有列表中根据元素文件路径获取进度即可，多个相同文件时也不需要多个上传任务
                    if (progressHash[filePath] == null || progressHash[filePath]!! < 100) {
                        progressHash[filePath] = percent
                        adapter.notifyDataSetChanged()
                    }
                }
            },
            onSuccess = { uploadResult ->
                if (!uploadResult.isNullOrEmpty()) {
                    // 文件上传结果转型为map，key为本地文件路径
                    val map = uploadResult.map { file -> file.filePath to file }.toMap()
                    if (!map.isNullOrEmpty()) {
                        // 上传文件结果返回
                        files.forEach { file ->
                            if (map.containsKey(file.fileUrl)) {
                                // 更新全局上传记录
//                                    upSuccessRecord[file.fileUrl] = map[file.fileUrl]?.remoteUrl
//                                            ?: ""
                                // 此处为上一步操作中请求的文件存储fileId，来自过滤后仅需要上传的文件中数据files
                                upSuccessRecord[file.fileUrl] = file.fileId
                                // 更新上传进度记录
                                progressHash[file.fileUrl] = 100
                                file.isUploadFlag = true
                            }
                        }

//                            buildLocalLinkUrl(files)

                        // 上传文件结果更新到选中文件记录缓存中
                        data.selectFileResult?.forEach { bean ->
                            if (map.containsKey(bean.fileUrl)) {
                                // 标记文件上传完成
                                bean.isUploadFlag = true
                            }
                        }

                    }
                    if (files.size == uploadResult.size) {
                        showLog("全部图片上传成功")
                    } else {
                        showLog("有图片未上传成功")
                    }
                    adapter.notifyDataSetChanged()
                } else {
                    showLog("无图片上传成功")
                }
            },
            onError = {
                /*图片上传失败处理*/
                showLog("图片上传失败")
                showLog(it)
            },
            tosFileType = TosFileType.PAN
        )
    }

    fun setAprId(aprId: String?) {
        data.aprId = aprId ?: ""
    }

    override fun getType(): Int = data.type

    override fun getResult(): String {
        val result =
            // 未选择文件或者清空了
            if (data.selectFileResult == null || data.selectFileResult!!.isEmpty()) {
                arrayListOf()
            } else {
                // 选择到最大限度时，末尾不是占位，需要判断末尾是否为占位（添加图片logo）
                if (data.selectFileResult!!.last() == placeHolderData) {
                    data.selectFileResult!!.removeAt(data.selectFileResult!!.size - 1)
                }
                if (data.selectFileResult!!.isNotEmpty()) {
                    val taskPicUrls =
                        arrayListOf<CreateTaskCommentUploadFileBean>()
                    for (file in data.selectFileResult!!) {
                        // 判断上传结果中记录信息，
                        // 如果上传成功记录中包含已选择文件，
                        // 或者上传进度中包含已选择文件且进度为100，则添加到图片结果数据中
                        if (upSuccessRecord.containsKey(file.fileUrl) ||
                            (progressHash.containsKey(file.fileUrl) && progressHash[file.fileUrl] == 100)
                        ) {
                            taskPicUrls.add(
                                CreateTaskCommentUploadFileBean(
                                    file.fileId,
                                    file.hash,
                                    file.fileName,
                                    0
                                )
                            )
                        }
                    }
                    taskPicUrls
                } else {
                    arrayListOf()
                }
            }
        if (data.required == "1" && result.isEmpty()) {
            data.content = ""
        } else {
            data.content = GsonUtil.toJson(result)
        }
        return data.content!!
    }

    override fun getShortContent(): String {
        return if (!data.selectFileResult.isNullOrEmpty()) {
            val picCount = if (data.selectFileResult!!.last() == placeHolderData) {
                data.selectFileResult!!.size - 1
            } else {
                data.selectFileResult!!.size
            }
            data.title + AprProperty.charTag + "${picCount}张图片"
        } else {
            data.title + AprProperty.charTag + "0张图片"
        }
    }
}//已缓存

/**图片预览*/
class SelectPicPreviewProperty(val data: WidgetInfo, private val rootView: View) : AprProperty {

    override fun checkResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        return false
    }

    lateinit var required: TextView
    lateinit var title: TextView
    lateinit var content: TextView
    private lateinit var addPic: ImageView
    private lateinit var rvList: RecyclerView
    lateinit var adapter: MyAdapter<UploadFileBean>
    lateinit var detailAdapter: MyAdapter<ApprovalDetailFileBean>
    private val detailPicList = arrayListOf<ApprovalDetailFileBean>()

    var listener: OnSelectPicListener? = null

    @SuppressLint("SetTextI18n")
    override fun initView() {
        required = rootView.findViewById(R.id.tv_required)
        title = rootView.findViewById(R.id.tv_title)
        content = rootView.findViewById(R.id.tv_content)
        addPic = rootView.findViewById(R.id.iv_add_pic)
        rvList = rootView.findViewById(R.id.rv_list)

        detailPicList.clear()
        if (data.content != null && data.content!!.isNotEmpty()) {
            try {
                val list: List<ApprovalDetailFileBean>? =
                    GsonUtil.getList2(data.content, ApprovalDetailFileBean::class.java)
                if (!list.isNullOrEmpty()) {
                    detailPicList.addAll(list)
                }
            } catch (e: Exception) {
            }
        }

        title.text = if (detailPicList.isEmpty()) {
            data.title.plus("：未添加")
        } else {
            data.title
        }
        content.visibility = View.GONE
        addPic.visibility = View.GONE
        if (detailPicList.size > 0) {
            rvList.visibility = View.VISIBLE
        }

        detailAdapter = MyAdapter(rootView.context, R.layout.item_pic_list_detail, detailPicList,
            onBindItem = { position: Int, path: ApprovalDetailFileBean,
                           itemView: View ->

                val del = itemView.findViewById<ImageView>(R.id.iv_del)
                val picture = itemView.findViewById<ImageView>(R.id.iv_pic)
                val ivProgress = itemView.findViewById<ImageView>(R.id.iv_progress)
                val tvProgress = itemView.findViewById<TextView>(R.id.tv_progress)
                ivProgress.visibility = View.GONE
                tvProgress.visibility = View.GONE
                del.visibility = View.GONE
                XUtil.loadImage(rootView.context, picture, path.url)
                picture.setOnClickListener {
                    val previewPicList: ArrayList<String> = arrayListOf()
                    previewPicList.addAll(detailPicList.map { it.url }
                        .toList())
                    listener?.onPreview(position, previewPicList)
                }
            },
            onItemClick = { _: Int, _: ApprovalDetailFileBean,
                            _: View ->

            })

        rvList.layoutManager = GridLayoutManager(rootView.context, 5)
        required.visibility = View.GONE
        rvList.adapter = detailAdapter
    }

    override fun getType(): Int = data.type

    override fun getResult(): String {
        return data.content!!
    }

    override fun getShortContent(): String {
        return if (!data.selectFileResult.isNullOrEmpty()) {
            data.title + AprProperty.charTag + "${data.selectFileResult!!.size}张图片"
        } else {
            data.title + AprProperty.charTag + "0张图片"
        }
    }

}

/**审批评论控件*/
class CommentProperty(
    val appCompatActivity: AppCompatActivity,
    val data: WidgetInfo,
    private val rootView: View
) : AprProperty {
    private lateinit var title: TextView
    private lateinit var rvList: RecyclerView

    override fun initView() {
        title = rootView.findViewById(R.id.tv_title)
        rvList = rootView.findViewById(R.id.rv_list)
        val list = data.commentList!!
        val adapter = MyAdapter(
            rootView.context,
            R.layout.item_apr_comment_layout,
            list,
            { i: Int, comment: CommentListData, view: View ->
                XUtil.loadImage(
                    view.context, view.findViewById(
                        R.id.iv_comment_user_icon
                    ), comment.avatar
                )
                XUtil.setText(view, R.id.tv_comment_user_name, comment.name)
                XUtil.setText(
                    view, R.id.tv_comment_time, XUtil.turnToTimeStr(
                        comment.createTime, TIME_FORMAT_PATTERN2
                    )
                )
                if (StringUtils.isNotBlankAndEmpty(comment.content)) {
                    XUtil.showView(view.findViewById(R.id.tv_comment_content))
                    XUtil.setText(view, R.id.tv_comment_content, comment.content!!)
                } else {
                    XUtil.hideView(view.findViewById(R.id.tv_comment_content))
                }
                if (!comment.file.isNullOrEmpty()) {
                    XUtil.showView(view.findViewById(R.id.rv_list))
                    val picList = view.findViewById<RecyclerView>(R.id.rv_list)
                    val picAdapter = MyAdapter(
                        view.context,
                        R.layout.item_approval_file,
                        comment.file as ArrayList<ApprovalDetailFileBean>,
                        { _: Int, data: ApprovalDetailFileBean, itemView: View ->
                            val icon = itemView.findViewById<ImageView>(R.id.fileIcon)
                            if (CommonUtils.checkSuffix(
                                    data.fileName,
                                    rootView.context.resources.getStringArray(
//                                        R.array.rc_image_file_suffix
                                        com.joinutech.ddbeslibrary.R.array.rc_image_file_suffix
                                    )
                                )
                            ) {
                                ImageLoaderUtils.loadImage(
                                    rootView.context,
                                    icon, data.url
                                )
                            } else {
                                icon.setImageResource(FileUtil.getFileTypeIcon(data.fileName))
//                                        showDownLoadFileIcon(data.fileName,
//                                                itemView.findViewById(R.id.fileIcon), rootView.context)
                            }
                            itemView.findViewById<TextView>(R.id.fileName).text =
                                data.fileName
                        },
                        { _: Int, data: ApprovalDetailFileBean, _: View ->
                            if (data.fileType == 0) {
                                val intent = Intent(
                                    rootView.context,
                                    TaskImagePreviewActivity::class.java
                                )
                                val previewDataBean =
                                    TaskImagePreviewActivity.PreviewDataBean(
                                        0, arrayListOf(data.url),
                                        true
                                    )
                                val bundle = Bundle()
                                bundle.putSerializable("previewData", previewDataBean)
                                intent.putExtras(bundle)
                                rootView.context.startActivity(intent)
                            } else {
                                if (StringUtils.isNotBlankAndEmpty(data.url)) {
                                    FilePreviewProvider.getPreviewPost(
                                        data.fileName,
                                        data.fileSize,
                                        data.url
                                    )
                                        .navigation(rootView.context, object : NavigationCallback {

                                            override fun onLost(postcard: Postcard?) {
                                            }

                                            override fun onFound(postcard: Postcard?) {
                                            }

                                            override fun onInterrupt(postcard: Postcard?) {
                                                appCompatActivity.runOnUiThread {
                                                    ToastUtil.show(
                                                        rootView.context,
                                                        "此类型不支持在线预览，请到WEB端查看"
                                                    )
                                                }
                                            }

                                            override fun onArrival(postcard: Postcard?) {
                                            }
                                        })
                                } else {
                                    ToastUtil.show(rootView.context, "暂不支持此文件的打开")
                                }
                            }
                        }
                    )
                    picList.layoutManager = LinearLayoutManager(rootView.context)
                    picList.adapter = picAdapter
                } else {
                    XUtil.hideView(view.findViewById(R.id.rv_list))
                }
            },
            { i: Int, s: CommentListData, view: View ->

            }
        )

        rvList.layoutManager = LinearLayoutManager(rootView.context)
        rvList.adapter = adapter
    }

    fun setAprId(aprId: String?) {
        data.aprId = aprId ?: ""
    }

    override fun checkResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        return false
    }

    override fun getType(): Int = data.type

    override fun getResult(): String {
        return ""
    }

    override fun getShortContent(): String {
        return ""
    }

}

/**审批详情附件控件*/
class ApprovalDetailFileProperty(
    val data: WidgetInfo, private val rootView: View,
    val listener: ItemClickListener
) : AprProperty {
    override fun initView() {
        val fileList: List<ApprovalDetailFileBean>? =
            GsonUtil.fromJson(
                data.content,
                object : TypeToken<List<ApprovalDetailFileBean>>() {}.type
            )
        if (!fileList.isNullOrEmpty()) {
            val adapter = ApprovalFileAdapter(
                rootView.context,
                fileList as ArrayList<ApprovalDetailFileBean>, listener
            )
            val rvList = rootView.findViewById<RecyclerView>(R.id.rv_list)
            rvList.layoutManager = LinearLayoutManager(rootView.context)
            rvList.adapter = adapter
        } else {
            val noFileTv = rootView.findViewById<TextView>(R.id.noFileTv)
            noFileTv.visibility = View.VISIBLE
        }
    }

    override fun checkResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        return false
    }

    override fun getType(): Int = data.type

    override fun getResult(): String {
        return ""
    }

    override fun getShortContent(): String {
        return ""
    }

}

/**审批模板Hint显示组件*/
class ApprovalHintProperty(val data: WidgetInfo, private val rootView: View) : AprProperty {
    override fun initView() {
        rootView.findViewById<TextView>(R.id.tv_hint).text = data.title
    }

    override fun checkResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        return false
    }

    override fun getType(): Int = data.type

    override fun getResult(): String {
        return ""
    }

    override fun getShortContent(): String {
        return ""
    }

}

/**审批附件属性控件（上传）*/
class ApprovalFileProperty(val data: WidgetInfo, private val rootView: View) : AprProperty {

    private lateinit var fileAddIv: ImageView
    private lateinit var fileLineView: View
    private lateinit var fileListRecycler: RecyclerView
    lateinit var adapter: MyAdapter<UploadFileBean>

    private val selectFileList = arrayListOf<UploadFileBean>()
    private val progressMap = hashMapOf<String,Int>()
    private val upSuccessRecordMap = hashMapOf<String,String>()

    private val cacheTag = data.modelId + data.widgetId + "selectFileList"

    var listener: OnSelectFileListener? = null
    override fun initView() {

        fileAddIv = rootView.findViewById(R.id.file_add_image_iv)
        fileLineView = rootView.findViewById(R.id.file_line_view)
        fileListRecycler = rootView.findViewById(R.id.file_list_recycler)

        selectFileList.clear()
        if (data.selectFileResult == null || data.selectFileResult!!.isEmpty()) {
            data.selectFileResult = arrayListOf()
        }
        selectFileList.addAll(data.selectFileResult!!)

        adapter = MyAdapter(rootView.context, R.layout.item_file_layout, selectFileList,
            onBindItem = { position: Int, path: UploadFileBean, itemView: View ->
                val fileDeleteIv = itemView.findViewById<ImageView>(R.id.file_delete_iv)
                val fileHeaderRiv = itemView.findViewById<RoundedImageView>(R.id.file_header_riv)
                val fileProgressRiv = itemView.findViewById<RoundedImageView>(R.id.file_progress_riv)
                val fileProgressTv = itemView.findViewById<TextView>(R.id.file_progress_tv)
                val fileNameTv = itemView.findViewById<TextView>(R.id.file_name_tv)
                val fileLengthTv = itemView.findViewById<TextView>(R.id.file_length_tv)

                fileNameTv.text=path.fileName
                fileLengthTv.text=UploadFileUtil.formatFileSize(path.fileLength)
                fileProgressRiv.visibility = View.GONE
                fileProgressTv.visibility = View.GONE
                // 上传成功或者上传进度为100 隐藏进度条遮罩
                if (upSuccessRecordMap.containsKey(path.fileUrl) ||
                    (progressMap.containsKey(path.fileUrl) && progressMap[path.fileUrl] == 100)
                ) {
                    fileProgressRiv.visibility = View.GONE
                    fileProgressTv.visibility = View.GONE
                    fileDeleteIv.visibility = View.VISIBLE
                } else {
                    fileDeleteIv.visibility = View.GONE
                    fileProgressRiv.visibility = View.VISIBLE
                    fileProgressTv.visibility = View.VISIBLE
                    val progress =
                        if (progressMap.isNotEmpty() && progressMap.containsKey(path.fileUrl)) {
                            progressMap[path.fileUrl]!!
                        } else {
                            0
                        }
                    fileProgressTv.text = "$progress%"
                }

              fileHeaderRiv.setImageResource(FileUtil.getFileTypeIcon(path.fileName))

                fileDeleteIv.setOnClickListener {
                    //删除附件
                    deleteFile(position)
                }
                fileHeaderRiv.setOnClickListener {
                   //附件中预览文件
                    listener?.onPreview(path)
                }
            },
            onItemClick = { _: Int, _: UploadFileBean, _: View ->

            })

        fileListRecycler.layoutManager = LinearLayoutManager(rootView.context)
        fileListRecycler.adapter = adapter

        if (selectFileList.size >= 1) {
            fileListRecycler.visibility = View.VISIBLE
            fileLineView.visibility = View.VISIBLE
        } else {
            fileListRecycler.visibility = View.GONE
            fileLineView.visibility = View.GONE
        }
        //添加附件
        fileAddIv.setOnClickListener {
            listener?.onSelect(
                this, selectFileList.size
            )
        }

    }

    private lateinit var activity: AprBaseActivity<ViewBinding>
    private var companyId: String = ""

    fun setViewModel(activity: AprBaseActivity<ViewBinding>, companyId: String) {
        Loggerr.i("文件上传tag", "===setViewModel方法执行一次===")
        this.activity = activity
        this.companyId = companyId
        if (companyId.isNullOrBlank()) {
            this.companyId = CompanyHolder.getCurrentOrg()?.companyId ?: ""
        }

        //读缓存--选择附件
        if (cacheTag in approCacheMap.keys) {
            val cacheJson = approCacheMap[cacheTag]
            if (!cacheJson.isNullOrBlank()) {
                if (data.type == 12) {
                    val list = GsonUtil.fromJson2<ArrayList<UploadFileBean>>(cacheJson,
                        object : TypeToken<ArrayList<UploadFileBean>>() {}.type
                    )
                    if (list != null) {
                        selectFileList.clear()
                        selectFileList.addAll(list)
                        updateView()// 选择附件后调
                        uploadFile()// 上传附件
                    }
                }
            }
        }

    }

    private fun deleteFile(position: Int) {
        selectFileList.removeAt(position)

        updateCache()
        updateView()
    }

    private fun updateCache() {
        approCacheMap[cacheTag] = GsonUtil.toJson(selectFileList)
    }

    private fun updateView() {
        adapter.notifyDataSetChanged()
        data.selectFileResult!!.clear()
        data.selectFileResult!!.addAll(selectFileList)

        if (selectFileList.size >= 1) {
            fileListRecycler.visibility = View.VISIBLE
            fileLineView.visibility = View.VISIBLE
        } else {
            fileListRecycler.visibility = View.GONE
            fileLineView.visibility = View.GONE
        }
    }

    private fun uploadFile(){
        Loggerr.i("文件上传tag", "===uploadFile方法执行一次===")
        UploadFileUtil.uploadFileList(
            activity =activity,
            fileListData = selectFileList,
            upSuccessRecordMap = upSuccessRecordMap,
            progressHashMap = progressMap,
            companyId = companyId,
            onProgressBack = {
                             adapter.notifyDataSetChanged()
            },
            onRefreshBack = {adapter.notifyDataSetChanged()},
            onFinishBack = {adapter.notifyDataSetChanged()},
            onErrorBack = {activity.dismissDialog()}
        )
    }

    override fun checkResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        if (requestCode == 9001) {
            if (data != null) {
             val files= FileSelector.obtainSelectorList(data)
                if (files.isNotEmpty()) {
                    files.forEach {
                        selectFileList.add(
                            UploadFileBean(
                                fileId = "",
                                hash="",
                                fileName = it.name,
                                fileUrl = it.absolutePath,
                                fileLength = it.length()
                            )
                        )
                    }
                    updateData()
                }
            }
        }else if (requestCode == 9002) {
            //调用的是系统的文件选择器，返回结果的处理
            if (data != null) {
             val resultUri=data.data
                Loggerr.i("系统文件选择器", "==uri路径=结果=${resultUri}===")
                //下面这个方法不能对Excel等普通文件进行转换
                val path= FilesUtils.getPath(activity, resultUri)
                Loggerr.i("系统文件选择器", "==绝对路径=结果=${path}===")
                if (StringUtils.isNotBlankAndEmpty(path)) {
                    val targetFile=File(path)
                    selectFileList.add(
                        UploadFileBean(
                            fileId = "",
                            hash="",
                            fileName = targetFile.name,
                            fileUrl = targetFile.absolutePath,
                            fileLength = targetFile.length()
                        )
                    )
                    updateData()
                }
            }
        }

        return false
    }

    private fun updateData() {
        updateCache()
        updateView()
        uploadFile()
    }

    override fun getType(): Int = data.type

    override fun getResult(): String {
        val result =
            // 未选择文件或者清空了
            if (data.selectFileResult == null || data.selectFileResult!!.isEmpty()) {
                arrayListOf()
            } else {
                if (data.selectFileResult!!.isNotEmpty()) {
                    val taskFileUrls =
                        arrayListOf<CreateTaskCommentUploadFileBean>()
                    for (file in data.selectFileResult!!) {
                        // 判断上传结果中记录信息，
                        // 如果上传成功记录中包含已选择文件，
                        // 或者上传进度中包含已选择文件且进度为100，则添加到图片结果数据中
                        if (upSuccessRecordMap.containsKey(file.fileUrl) ||
                            (progressMap.containsKey(file.fileUrl) && progressMap[file.fileUrl] == 100)
                        ) {
                            taskFileUrls.add(
                                CreateTaskCommentUploadFileBean(
                                    file.fileId,
                                    file.hash,
                                    file.fileName,
                                    1
                                )
                            )
                        }
                    }
                    taskFileUrls
                } else {
                    arrayListOf()
                }
            }
        if (data.required == "1" && result.isEmpty()) {
            data.content = ""
        } else {
            data.content = GsonUtil.toJson(result)
        }
        return data.content!!
    }

    override fun getShortContent(): String {
        return if (!data.selectFileResult.isNullOrEmpty()) {
            val fileCount =  data.selectFileResult!!.size
            data.title + AprProperty.charTag + "${fileCount}个文件"
        } else {
            data.title + AprProperty.charTag + "0个文件"
        }
    }



}

interface OnSelectPicListener {
    fun onSelect(property: AprProperty, selectedCount: Int)
    fun onPreview(position: Int, picList: ArrayList<String>)
    fun onResult(list: List<String>)
}

interface OnSelectFileListener {
    fun onSelect(property: AprProperty, selectedCount: Int)
    fun onPreview(uploadFileBean: UploadFileBean)
    fun onResult(list: List<String>)
}

interface OnSelectNodeListener {
    fun onSelect(property: AprProperty)
    fun onPreview(position: Int, picList: ArrayList<ContactModel>)
    fun onResult(list: List<ContactModel>)
}

interface OnSelectMateListener {
    fun onSelect(property: AprProperty)
    fun onPreview(position: Int, picList: ArrayList<SearchMemberBean>)
    fun onResult(list: List<SearchMemberBean>)
}

class SelectListDialog(
    activity: AppCompatActivity,
    val title: String,
    val data: MutableList<SelectFrameData>,
    private val singleSelect: Boolean = false,
    private val onConfirm: (result: List<SelectFrameData>) -> Unit,
    private val onCancel: () -> Unit
) : DialogHolder(activity, R.layout.dialog_center_list_layout) {

    lateinit var tvTitle: TextView
    lateinit var rvList: RecyclerView
    lateinit var cancel: TextView
    lateinit var confirm: TextView
    lateinit var lineTop: View
    lateinit var lineH: View
    lateinit var lineV: View

    override fun bindView(dialogView: View) {
        tvTitle = dialogView.findViewById(R.id.tv_title)
        rvList = dialogView.findViewById(R.id.rv_list)
        cancel = dialogView.findViewById(R.id.cancel)
        confirm = dialogView.findViewById(R.id.confirm)
        lineH = dialogView.findViewById(R.id.line_ho)
        lineTop = dialogView.findViewById(R.id.line_top)
        lineV = dialogView.findViewById(R.id.line_v)
        tvTitle.text = title
        initListView()
        cancel.setOnClickListener {
            dialog?.dismiss()
            onCancel.invoke()
        }
        confirm.setOnClickListener {
            dialog?.dismiss()
            onConfirm.invoke(selectData)
        }
    }

    var adapter: MyAdapter<SelectFrameData>? = null

    private val selectData = arrayListOf<SelectFrameData>()

    private fun initListView() {
        adapter = MyAdapter(activity,
//            R.layout.item_title_with_select,
            com.joinutech.ddbeslibrary.R.layout.item_title_with_select,
            data,
            onBindItem = { i: Int, s: SelectFrameData, view: View ->
                view.findViewById<TextView>(R.id.tv_name).text = s.title
                if (singleSelect) {
                    view.findViewById<View>(R.id.iv_select).visibility = View.GONE
                } else {
                    view.findViewById<View>(R.id.iv_select).visibility = View.VISIBLE
                }
                view.findViewById<View>(R.id.line).visibility = View.GONE
            },
            onItemClick = { i: Int, s: SelectFrameData, view: View ->
                if (singleSelect) {
                    selectData.clear()
                    selectData.add(s)
                } else {
                    if (s in selectData) {
                        view.findViewById<View>(R.id.iv_select).isSelected = false
                        selectData.remove(s)
                    } else {
                        selectData.add(s)
                        view.findViewById<View>(R.id.iv_select).isSelected = true
                    }
                }
            }
        )

        rvList.layoutManager = LinearLayoutManager(activity)
        rvList.adapter = adapter
    }
}

object AprUtil {

    /**审批列表和详情头部审批状态获取*/
    fun getAprDetailState(
        aprType: Int,
        subAprType: Int,
        aprState: Int,
        isProcessor: Boolean,
        result: (title: String, color: Int) -> Unit
    ) {
        /**审批状态：0.删除，1通过；2.拒绝；3.进行中；4.未提交；5.撤回；6.退回*/
        val title = when (aprType) {
            1 -> {
                /**我发起的*/
                when (subAprType) {
                    3 -> {
                        /**未完成*/
                        "审批中"
                    }
                    1 -> {
                        /**已完成*/
                        when (aprState) {
                            1 -> "已通过"
                            2 -> "未通过"
                            else -> ""
                        }
                    }
                    5 -> {
                        /**已终止*/
                        when (aprState) {
                            5 -> "已撤回"
                            6 -> "已退回"
                            else -> ""
                        }
                    }
                    else -> ""
                }
            }
            2 -> {
                /**我审批的*/
                when (subAprType) {
                    1 -> {
                        /**待我审批*/
                        if (isProcessor) {
                            "待我审批"
                        } else {
                            "待审批"
                        }
                    }
                    2 -> {
                        /**已审批*/
//                        if (processUserId == BaseApplication.CacheDataHolder.getCurrentUserId()) {
                        "已审批"
//                        } else {
//                            "其他人已审批"
//                        }
                    }
                    3 -> {
                        /**已完成*/
                        when (aprState) {
                            1 -> "已通过"
                            2 -> "未通过"
                            5 -> "已撤回"
                            6 -> "已退回"
                            else -> ""
                        }
                    }
                    else -> ""
                }
            }
            3 -> {
                /**抄送我的*/
                when (aprState) {
                    1 -> "已通过"
                    2 -> "未通过"
                    5 -> "已撤回"
                    6 -> "已退回"
                    else -> "未完成"
                }
            }
            else -> ""
        }

        val color = when (aprState) {
            1 -> R.color.c_5290ED
            2 -> R.color.c_F3695B
            3 -> R.color.c_8BC039
            else -> com.joinutech.ddbeslibrary.R.color.color999999
        }

        result.invoke(title, color)
    }

    /**审批列表和详情头部审批状态获取*/
    fun getAprState(
        aprType: Int,
        subAprType: Int,
        aprState: Int,
        result: (title: String, color: Int) -> Unit
    ) {
        /**审批状态：0.删除，1通过；2.拒绝；3.进行中；4.未提交；5.撤回；6.退回*/
        when (aprType) {
            1 -> {
                /**我发起的*/
                when (subAprType) {
                    3 -> {
                        /**未完成*/
//                        result.invoke("审批中", R.color.c_apr_process)
                        result.invoke("审批中", com.joinutech.ddbeslibrary.R.color.c_apr_process)
                    }
                    1 -> {
                        /**已完成*/
                        when (aprState) {
                            1 -> result.invoke("已通过", com.joinutech.ddbeslibrary.R.color.c_apr_success)

                            2 -> result.invoke("未通过", com.joinutech.ddbeslibrary.R.color.c_apr_finish)
                            else -> ""
                        }
                    }
                    5 -> {
                        /**已终止*/
                        val title = when (aprState) {
                            5 -> "已撤回"
                            6 -> "已退回"
                            else -> ""
                        }
                        result.invoke(title, com.joinutech.ddbeslibrary.R.color.c_apr_finish)
                    }
                    else -> ""
                }
            }
            2 -> {
                /**我审批的*/
                when (subAprType) {
                    1 -> {
                        /**待我审批*/
                        result.invoke("待我审批", com.joinutech.ddbeslibrary.R.color.c_apr_process)
                    }
                    2 -> {
                        /**已审批*/
//                        if (processUserId == BaseApplication.CacheDataHolder.getCurrentUserId()) {
                        result.invoke("已审批", com.joinutech.ddbeslibrary.R.color.c_apr_over)

//                        } else {
//                        或审分组审批时使用
//                            "其他人已审批"
//                        }
                    }
                    3 -> {
                        /**已完成*/
                        when (aprState) {
                            1 -> result.invoke("已通过", com.joinutech.ddbeslibrary.R.color.c_apr_success)
                            2 -> result.invoke("未通过", com.joinutech.ddbeslibrary.R.color.c_apr_finish)
                            5 -> result.invoke("已撤回", com.joinutech.ddbeslibrary.R.color.c_apr_finish)
                            6 -> result.invoke("已退回", com.joinutech.ddbeslibrary.R.color.c_apr_finish)
                            else -> ""
                        }
                    }
                    else -> ""
                }
            }
            3 -> {
                /**抄送我的*/
                when (aprState) {
                    1 -> result.invoke("已通过", com.joinutech.ddbeslibrary.R.color.c_apr_success)
                    2 -> result.invoke("未通过", com.joinutech.ddbeslibrary.R.color.c_apr_finish)
                    5 -> result.invoke("已撤回", com.joinutech.ddbeslibrary.R.color.c_apr_finish)
                    6 -> result.invoke("已退回", com.joinutech.ddbeslibrary.R.color.c_apr_finish)
                    else -> ""
                }
            }
            else -> ""
        }
    }

    /**审批列表和详情头部审批状态获取*/
    fun getStatus(status: Int) {
        val title = when (status) {
            /**审批状态：0.删除，1通过；2.拒绝；3.进行中；4.未提交；5.撤回；6.退回*/
            0 -> ""
            1 -> "已同意"
            2 -> "已拒绝"
            3 -> "审批中"
            4 -> ""
            5 -> "已撤回"
            6 -> "已退回"
            else -> "审批中"
        }

        val color = when (status) {
            0 -> R.color.c_EA6858
            1 -> R.color.c_3F8BEE
            3 -> R.color.c_8BC039
            else -> com.joinutech.ddbeslibrary.R.color.color999999

//            0 -> 0xFFEA6858
//            1 -> 0xFFF8BEE
//            3 -> 0xFF8BC039
//            4 -> 0xFF999999
//            else -> 0xFF808080
        }
    }

    /**审批节点状态显示*/
    fun getOpinion(opinion: Int, result: (title: String, color: Int, icon: Int) -> Unit) {
        val title = when (opinion) {
            0 -> "已拒绝"
            1 -> "已同意"
            2 -> "待审批"
            3 -> "审批中"
            4 -> "已退回"
            999 -> "发起审批"
            else -> "审批中"
        }

        val color = when (opinion) {
            0 -> R.color.c_EA6858
            1 -> R.color.c_3F8BEE
            3 -> R.color.c_8BC039
            4 -> com.joinutech.ddbeslibrary.R.color.color999999
            else -> com.joinutech.ddbeslibrary.R.color.color808080

//            0 -> 0xFFEA6858
//            1 -> 0xFFF8BEE
//            3 -> 0xFF8BC039
//            4 -> 0xFF999999
//            else -> 0xFF808080
        }

        val icon = when (opinion) {
            0 -> R.drawable.icon_node_stop
            1 -> R.drawable.icon_node_agree
            3 -> R.drawable.icon_node_running
            999 -> R.drawable.icon_node_begin
            else -> R.drawable.icon_node_un_start
        }
        result.invoke(title, color, icon)
    }

    fun getAprStateColor(state: Int): Int {
        return when (state) {
            1 -> R.color.c_3F8BEE
            2 -> R.color.c_EA6858
            3 -> R.color.c_8BC039
            else -> com.joinutech.ddbeslibrary.R.color.color808080
        }
    }

    fun initTitleStatus(title: TextView, isEdit: Boolean) {
       /* val params = title.layoutParams
        if (!isEdit) {
            params.width = DeviceUtil.getTextWidth(PROPERTY_TITLE_MODEL, title.textSize)
        } else {
            params.width = DeviceUtil.getTextWidth(PROPERTY_TITLE_MODEL, title.textSize)
        }
        title.layoutParams = params*/
    }
}

/**
 * 控件分类
 * 当前页面底部弹窗选择》
 * 时间类：
 * 时间段选择》两个单行文本 R.layout.property_time_selector
 * 持续时间选择》单行文本 R.layout.property_time_selector
 *
 * 单行跳转选择类》
 *
 * 跳转页面选择后返回结果》》
 * 定位信息类：》》跳转地址搜索页面定位，选择结果后返回，定位不到时错误页面 单行文本
 * 自动定位位置   实际位置显示两行信息 单行文本 R.layout.property_location_text
 * 选择定位位置   只显示一行 单行文本 R.layout.property_text_selector
 *
 * 人员选择 跳转团队成员列表，选择后返回（单选和多选） 单行文本ok R.layout.property_text_selector
 * 抄送人员选择（可以手动设置抄送项时显示） 跳转团队成员列表，选择后返回（多选） 组件和图片选择相似（grid展示）R.layout.property_select_workmate
 * 自定义审批流程 审批节点选择 跳转到团队列表多选人员后，返回 列表显示  拖动排序知识点（list展示，可排序）R.layout.property_select_node
 * 部门选择 跳转团队部门列表，选择后返回（手动，自动不需要跳转,无右箭头） 单行文本 R.layout.property_text_selector
 *
 * 自动获取类》》》
 *
 * 当前人，团队职位，团队部门（自动）    单行文本 R.layout.property_text_selector
 *
 *
 * 文件选择：》》单行文本 R.layout.property_text_selector
 * 弹窗提示选择文件类型，跳转文件管理，对选中结果过滤？
 *
 *文本输入类：
 * 单行文本 是不是带单位 数字 文字都可以
 * 多行文本
 * R.layout.property_text_input 设置edit text lines
 *
 * 图片选择布局
 * R.layout.property_select_picture
 * 人员选择布局
 * R.layout.property_select_workmate
 *
 * */
/**
 * 文本类属性
 * 11 单行文本
 * 111
 * 12 多行文本
 *
 * */