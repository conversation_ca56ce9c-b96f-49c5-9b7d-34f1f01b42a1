package com.joinutech.approval.aprhistory

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.EditText
import android.widget.PopupWindow
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.approval.AprUtil
import com.joinutech.approval.R
import com.joinutech.approval.data.ModelFilterData
import com.joinutech.approval.data.MyAprListData
import com.joinutech.approval.launch.MyAprListFragment
import com.joinutech.approval.utils.GridItemDecoration
import com.joinutech.common.adapter.MyAdapter
import com.joinutech.ddbeslibrary.utils.*

/**
 * @Description: 审批页面辅助类
 * @Author: zhaoyy
 * @packageName:
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
/**审批列表适配器*/
class AprListAdapter(activity: AppCompatActivity, data: MutableList<MyAprListData>,
                     modelType: Int, subModelType: Int,
                     onItemClick: (position: Int, data: MyAprListData, view: View) -> Unit)
    : MyAdapter<MyAprListData>(activity, R.layout.item_apr_list_layout, data,
        onBindItem = { position: Int, data: MyAprListData, itemView: View ->
            if (position == 0) {
                itemView.findViewById<View>(R.id.line_top).visibility = View.GONE
            } else {
                itemView.findViewById<View>(R.id.line_top).visibility = View.VISIBLE
            }
            XUtil.setText(itemView, R.id.tv_apr_title, data.approveName)
            XUtil.loadImage(activity, itemView.findViewById(R.id.iv_apr_icon), XUtil.getAprIcon(data.modelLogo))
            XUtil.setText(itemView, R.id.tv_apr_time, XUtil.getShowTime(data.createTime))

            if (!data.oneApproveContent.isNullOrBlank()) {
                XUtil.showView(itemView.findViewById(R.id.tv_pro_one))
                XUtil.setText(itemView, R.id.tv_pro_one, data.oneApproveContent)
            } else {
                XUtil.hideView(itemView.findViewById(R.id.tv_pro_one))
            }
            if (!data.twoApproveContent.isNullOrBlank()) {
                XUtil.showView(itemView.findViewById(R.id.tv_pro_two))
                XUtil.setText(itemView, R.id.tv_pro_two, data.twoApproveContent)
            } else {
                XUtil.hideView(itemView.findViewById(R.id.tv_pro_two))
            }

            if (!data.threeApproveContent.isNullOrBlank()) {
                XUtil.showView(itemView.findViewById(R.id.tv_pro_three))
                XUtil.setText(itemView, R.id.tv_pro_three, data.threeApproveContent)
            } else {
                XUtil.hideView(itemView.findViewById(R.id.tv_pro_three))
            }

//            if (StringUtils.isEmpty(data.approver)) {
//                XUtil.hideView(itemView.findViewById(R.id.tv_apr_node))
//            } else {
//                if (modelType == 1 && subModelType == 3) {
//                    XUtil.showView(itemView.findViewById(R.id.tv_apr_node))
//                    XUtil.setText(itemView, R.id.tv_apr_node, data.approver)
//                }
//            }

            when {
                // TODO 需要验证
//                抄送我的（加载成功后更新红点，刷不刷新都可以）
//                我发起的审批（撤回成功后刷新列表）
//                待我审批的未审批（modelType=1 && subModelType=1）
//                （加审或审批成功后需要刷新列表）
                modelType == 4 -> {
                    if (data.isRead == 0) {
                        XUtil.showView(itemView.findViewById(R.id.iv_red_dot))
                    } else {
                        XUtil.hideView(itemView.findViewById(R.id.iv_red_dot))
                    }
                }
//                抄送我的，我审批的待审批，我发起的已完成
                modelType == 3 || (modelType == 1 && subModelType == 1) -> {
                    if (data.isRead == 0) {
                        XUtil.showView(itemView.findViewById(R.id.iv_red_dot))
                    } else {
                        XUtil.hideView(itemView.findViewById(R.id.iv_red_dot))
                    }
                }
                modelType == 2 && subModelType == 1 -> {
                    XUtil.showView(itemView.findViewById(R.id.iv_red_dot))
                }
                else -> {
                    XUtil.hideView(itemView.findViewById(R.id.iv_red_dot))
                }
            }

            AprUtil.getAprState(modelType, subModelType, data.status) { title: String, color: Int ->
                XUtil.showView(itemView.findViewById(R.id.tv_apr_node))
                if (modelType == 1 && subModelType == 3) {
                    XUtil.setText(itemView, R.id.tv_apr_node, data.approver + title)
                } else {
                    XUtil.setText(itemView, R.id.tv_apr_node, title)
                }
//                XUtil.setText(itemView, R.id.tv_apr_state, title)
                itemView.findViewById<TextView>(R.id.tv_apr_node).setTextColor(
                        itemView.resources.getColor(color))
            }
        },
        onItemClick = onItemClick) {
//    fun setAprState(state: Int) {
////        normal 发起审批，待审批，已退回，已撤回
//        R.color.color808080
//
////        processing 审批中，未完成
//        R.color.c_8BC039
//
////        refuse 已拒绝
//        R.color.c_EA6858
////        passed 已同意，审批已通过
//        R.color.c_3F8BEE
//
//    }
}

/**审批筛选弹窗*/
class MyFilterPop(val context: Context, val list: ArrayList<ModelFilterData>) {
    private var pop: PopupWindow? = null
    private lateinit var popAdapter: MyAdapter<ModelFilterData>
    private val filterData = arrayListOf(ModelFilterData(modelName = "全部", modelId = "", isSelect = true))

    init {
        filterData.addAll(list)
    }

    fun showPop(targetView: View, result: (modelId: String) -> Unit) {
        popAdapter = MyAdapter(context, R.layout.item_filter_layout,
                filterData,
                onBindItem = { i: Int, s: ModelFilterData, view: View ->
                    val textView = view.findViewById<TextView>(R.id.tv_name)
                    textView.text = s.modelName
                    textView.isSelected = s.isSelect
                },
                onItemClick = { i: Int, s: ModelFilterData, view: View ->
                    for (filter in filterData) {
                        filter.isSelect = false
                    }
                    filterData[i].isSelect = true
                    pop!!.dismiss()
                    result.invoke(s.modelId)
                }
        )
        val mLayoutInflater = LayoutInflater.from(context)
        val contentView = mLayoutInflater.inflate(R.layout.layout_search_filter, null)
        val rvList = contentView.findViewById<RecyclerView>(R.id.rv_list)
        rvList.layoutManager = GridLayoutManager(context, 4)
        rvList.addItemDecoration(GridItemDecoration(4, DeviceUtil.dip2px(context, 8.0f)))
        rvList.adapter = popAdapter
        pop = PopUtil.buildPop(contentView)
        PopUtil.showAtDown(pop!!, targetView)
    }

    fun hidePop() {
        pop!!.dismiss()
    }

    fun finish() {
        if (pop != null && pop!!.isShowing) {
            pop!!.dismiss()
        }
        pop = null
    }

}

/**审批列表viewpager适配器*/
abstract class MyHandleAdapter(fm: FragmentManager, val titles: Array<String>) : FragmentPagerAdapter(fm) {

    val fragments = arrayOfNulls<MyAprListFragment>(3)

    override fun getPageTitle(position: Int): CharSequence? {
        return titles[position]
    }

    override fun getCount(): Int {
        return fragments.size
    }

    fun onRefresh(position: Int, listIndex: Int, needRefresh: Boolean = false) {
        fragments[position]?.onRefresh(listIndex, needRefresh)
    }

    fun onChangePage(modelId: String, keyWord: String, position: Int) {
        fragments[position]?.onChangePage(modelId, keyWord)
    }

    fun setFilter(modelId: String, position: Int) {
        fragments[position]?.setFilter(modelId)
    }

    fun setKeyWord(keyWord: String, position: Int) {
        fragments[position]?.setKeyWord(keyWord)
    }
}

/**输入框弹窗*/
class AprInputDialog(activity: AppCompatActivity, private val titleInfo: String,
                     private val onCancel: () -> Unit,
                     private val onConfirm: (result: String) -> Unit,
                     gravity: Int = Gravity.CENTER, val confirmText: String) : DialogHolder(
        activity = activity,
        layoutId = R.layout.dialog_bottom_edit_confirm_blue,
//        layoutId = R.layout.layout_apr_advice_dialog,
        tag = "input_dialog",
        gravity = gravity
) {
    private lateinit var input: EditText
    private lateinit var title: TextView
    private lateinit var tip: TextView
    private lateinit var rvList: RecyclerView
    private lateinit var cancel: TextView
    private lateinit var confirm: TextView
    private lateinit var lineTop: View
    private lateinit var lineH: View
    private lateinit var lineV: View

    override fun bindView(dialogView: View) {
        title = dialogView.findViewById(R.id.tv_title)
        tip = dialogView.findViewById(R.id.tv_advice_tip)
        input = dialogView.findViewById(R.id.et_content)
        rvList = dialogView.findViewById(R.id.rv_list)
        cancel = dialogView.findViewById(R.id.cancel)
        confirm = dialogView.findViewById(R.id.confirm)
        lineH = dialogView.findViewById(R.id.line_ho)
        lineTop = dialogView.findViewById(R.id.line_top)
        lineV = dialogView.findViewById(R.id.line_v)
        if (StringUtils.isNotBlankAndEmpty(confirmText)) {
            confirm.text = confirmText
        }
        title.text = titleInfo
        XUtil.showView(tip, input)
        XUtil.hideView(lineV, lineTop, rvList)
        cancel.setOnClickListener {
            dialog?.dismiss()
            onCancel.invoke()
        }
        confirm.setOnClickListener {
            dialog?.dismiss()
            onConfirm.invoke(input.text.toString())
        }
    }
}

object Codes {
    val REQUEST_APRDETAIL_ADD_NODE = 10001
    val REQUEST_APRDETAIL_ADD_COMMENT = 10002
    val SIGN_REQUEST = 10013
}
