<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".quick.QuickAprActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_top_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_back"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_42"
            android:gravity="center"
            android:paddingStart="@dimen/dp_15"
            android:paddingEnd="@dimen/dp_15"
            android:text="退出"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_15"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_42"
            android:gravity="center"
            android:paddingStart="@dimen/dp_15"
            android:paddingEnd="@dimen/dp_15"
            android:text="快速审批"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_17"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/line_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_grey"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_top_bar" />

    <com.joinutech.common.widget.NoScrollPager
        android:id="@+id/vp_pages"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/dp_15"
        app:layout_constraintBottom_toTopOf="@id/tv_refuse"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line_top" />

    <TextView
        android:id="@+id/tv_jump"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="@dimen/dp_9"
        android:paddingBottom="@dimen/dp_24"
        android:text="跳过此审批"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_15"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_refuse"
        android:layout_width="144dp"
        android:layout_height="48dp"
        android:layout_marginBottom="@dimen/dp_9"
        android:background="@drawable/bg_refuse"
        android:gravity="center"
        android:text="拒绝"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_15"
        app:layout_constraintBottom_toTopOf="@id/tv_jump"
        app:layout_constraintEnd_toStartOf="@id/tv_agree"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_agree"
        android:layout_width="144dp"
        android:layout_height="48dp"
        android:layout_marginBottom="@dimen/dp_9"
        android:background="@drawable/bg_agree"
        android:gravity="center"
        android:text="同意"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_15"
        app:layout_constraintBottom_toTopOf="@id/tv_jump"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_refuse" />

</androidx.constraintlayout.widget.ConstraintLayout>