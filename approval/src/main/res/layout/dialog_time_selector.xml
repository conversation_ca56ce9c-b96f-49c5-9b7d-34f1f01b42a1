<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="225dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white">

    <TextView
        android:id="@+id/btn_cancel"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_38"
        android:gravity="end|center_vertical"
        android:paddingStart="@dimen/dp_25"
        android:text="取消"
        android:textColor="@color/color1E87F0"
        android:textSize="@dimen/sp_12"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_38"
        android:layout_marginEnd="@dimen/dp_20"
        android:gravity="center"
        tools:text="1993年5月20日"
        android:textColor="@color/color_date_selector_text"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="@id/btn_cancel"
        app:layout_constraintEnd_toStartOf="@id/tv_time_result"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/btn_cancel"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_time_result"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_38"
        android:gravity="center"
        tools:text="11:45"
        android:textColor="@color/color_date_selector_text"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="@id/btn_cancel"
        app:layout_constraintEnd_toStartOf="@id/btn_submit"
        app:layout_constraintStart_toEndOf="@id/tv_title"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/date_indicator"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_1"
        android:background="@color/color1E87F0"
        app:layout_constraintBottom_toBottomOf="@id/btn_cancel"
        app:layout_constraintEnd_toEndOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="@id/tv_title" />

    <View
        android:id="@+id/time_indicator"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_1"
        android:background="@color/color1E87F0"
        app:layout_constraintBottom_toBottomOf="@id/btn_submit"
        app:layout_constraintEnd_toEndOf="@id/tv_time_result"
        app:layout_constraintStart_toStartOf="@id/tv_time_result" />

    <TextView
        android:id="@+id/btn_submit"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_38"
        android:gravity="start|center_vertical"
        android:paddingEnd="@dimen/dp_25"
        android:text="确认"
        android:textColor="@color/color1E87F0"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/line_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_grey"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_cancel" />

    <LinearLayout
        android:id="@+id/optionspicker"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@android:color/white"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line_top">

        <com.joinutech.ddbeslibrary.widget.contrarywind.view.CustomWheelView
            android:id="@+id/options1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            app:cwv_textColorCenter="#ff0082f2"
            app:cwv_textColorOut="#ffa5a5a5" />

        <com.joinutech.ddbeslibrary.widget.contrarywind.view.CustomWheelView
            android:id="@+id/options2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            app:cwv_textColorCenter="#ff0082f2"
            app:cwv_textColorOut="#ffa5a5a5" />

        <TextView
            android:id="@+id/tv_separator"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text=":"
            android:textColor="@color/c_0082F2"
            android:textSize="@dimen/sp_17" />

        <com.joinutech.ddbeslibrary.widget.contrarywind.view.CustomWheelView
            android:id="@+id/options3"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            app:cwv_textColorCenter="#ff0082f2"
            app:cwv_textColorOut="#ffa5a5a5" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>