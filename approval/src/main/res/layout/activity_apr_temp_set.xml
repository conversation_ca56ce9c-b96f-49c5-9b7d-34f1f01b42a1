<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <TextView
        android:id="@+id/tv_apr_temp_set_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp_14"
        android:text="关闭后，用户将无法使用提醒审批的功能"
        android:textColor="@color/text_gren"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        layout="@layout/item_set_toggle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_apr_temp_set_tip" />
</androidx.constraintlayout.widget.ConstraintLayout>