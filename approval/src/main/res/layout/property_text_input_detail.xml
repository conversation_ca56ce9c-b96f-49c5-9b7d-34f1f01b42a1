<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:padding="@dimen/dp_15"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <TextView
        android:id="@+id/tv_required"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:text="*"
        android:textColor="@color/red"
        android:textSize="@dimen/sp_14"
        android:visibility="visible"
        app:layout_constraintEnd_toStartOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/property_title_detail"
        android:layout_marginStart="@dimen/dp_4"
        app:layout_constraintEnd_toStartOf="@id/et_input"
        app:layout_constraintStart_toEndOf="@id/tv_required"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginStart="@dimen/dp_0" />

    <!--todo 输入框 多行文本时左对齐，其他输入右对齐,数字类型要控制好输入键盘-->

    <EditText
        android:id="@+id/et_input"
        style="@style/property_content_detail"
        android:layout_marginStart="@dimen/dp_15"
        android:background="@color/white"
        android:gravity="top|start"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

</androidx.constraintlayout.widget.ConstraintLayout>