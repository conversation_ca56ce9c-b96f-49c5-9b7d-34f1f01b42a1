<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:minHeight="64dp">

    <ImageView
        android:id="@+id/iv_node_icon"
        android:layout_width="@dimen/dp_16"
        android:layout_height="@dimen/dp_16"
        android:layout_marginStart="@dimen/dp_32"
        android:src="@drawable/icon_node_begin"
        app:layout_constraintBottom_toBottomOf="@id/iv_node_avatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_node_avatar" />

    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/iv_node_avatar"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_32"
        android:layout_marginTop="@dimen/dp_14"
        android:src="@drawable/icon_node_begin"
        app:layout_constraintStart_toEndOf="@id/iv_node_icon"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_node_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_7"
        android:layout_marginEnd="@dimen/dp_8"
        android:ellipsize="end"
        android:maxLength="10"
        android:maxLines="1"
        android:textColor="@color/text_black"
        android:textSize="@dimen/sp_13"
        app:layout_constraintBottom_toBottomOf="@id/iv_node_avatar"
        app:layout_constraintStart_toEndOf="@id/iv_node_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_node_avatar"
        tools:text="teststtes" />

    <TextView
        android:id="@+id/externalTag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:background="@drawable/shape_d2e6ae_button_rounded"
        android:paddingStart="8dp"
        android:paddingTop="3dp"
        android:paddingEnd="7dp"
        android:paddingBottom="3dp"
        android:text="外部协作人员"
        android:textColor="#ff8fc02e"
        android:textSize="@dimen/textsize_12"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/tv_node_name"
        app:layout_constraintStart_toEndOf="@id/tv_node_name"
        app:layout_constraintTop_toTopOf="@id/tv_node_name" />

    <TextView
        android:id="@+id/tv_node_retrial_creator"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginEnd="@dimen/dp_8"
        android:ellipsize="end"
        android:maxLines="1"
        tools:text="aaaaaaaaaaa"
        android:singleLine="true"
        android:textColor="@color/c_b3b3b3"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toStartOf="@id/tv_node_state"
        app:layout_constraintStart_toStartOf="@id/iv_node_avatar"
        app:layout_constraintTop_toBottomOf="@id/iv_node_avatar" />

    <!--android:text="一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十"-->

    <TextView
        android:id="@+id/tv_node_opinion"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginEnd="@dimen/dp_8"
        android:ellipsize="end"
        android:maxLength="100"
        tools:text="bbbbbbbbbbb"
        android:textColor="@color/c_b3b3b3"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toStartOf="@id/tv_node_state"
        app:layout_constraintStart_toStartOf="@id/iv_node_avatar"
        app:layout_constraintTop_toBottomOf="@id/tv_node_retrial_creator" />

    <TextView
        android:id="@+id/tv_node_time"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:layout_marginEnd="@dimen/dp_8"
        android:ellipsize="end"
        tools:text="12:00"
        android:maxLines="1"
        android:paddingBottom="@dimen/dp_5"
        android:textColor="@color/c_b3b3b3"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toStartOf="@id/tv_node_state"
        app:layout_constraintStart_toStartOf="@id/iv_node_avatar"
        app:layout_constraintTop_toBottomOf="@id/tv_node_opinion" />

    <TextView
        android:id="@+id/tv_node_state"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_14"
        android:textColor="@color/color808080"
        android:textSize="@dimen/sp_12"
        tools:text="nnn"
        app:layout_constraintBottom_toBottomOf="@id/iv_node_avatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_node_avatar"
        app:layout_goneMarginEnd="@dimen/dp_8" />

    <TextView
        android:id="@+id/tv_node_state_special"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="@dimen/dp_14"
        android:text="外部联系人已被删除，审批被退回"
        android:textColor="@color/colorF54e00"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintStart_toStartOf="@id/iv_node_avatar"
        app:layout_constraintTop_toBottomOf="@id/iv_node_avatar" />

    <View
        android:id="@+id/line_bottom"
        android:layout_width="@dimen/dp_1"
        android:layout_height="0dp"
        android:background="@drawable/dash_vertical"
        android:layerType="software"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/iv_node_icon"
        app:layout_constraintStart_toStartOf="@id/iv_node_icon"
        app:layout_constraintTop_toBottomOf="@id/iv_node_icon" />


    <View
        android:id="@+id/line_top"
        android:layout_width="@dimen/dp_1"
        android:layout_height="0dp"
        android:background="@drawable/dash_vertical"
        android:layerType="software"
        app:layout_constraintBottom_toTopOf="@id/iv_node_icon"
        app:layout_constraintEnd_toEndOf="@id/iv_node_icon"
        app:layout_constraintStart_toStartOf="@id/iv_node_icon"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>