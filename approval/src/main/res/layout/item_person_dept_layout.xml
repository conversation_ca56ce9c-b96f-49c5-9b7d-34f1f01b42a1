<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layout_height="wrap_content"
    android:minHeight="78dp">

    <TextView
        android:id="@+id/tv_index"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:background="@color/background"
        android:gravity="center_vertical"
        android:paddingStart="14dp"
        android:textColor="#BDBDBD"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_select"
        android:layout_width="@dimen/dp_17"
        android:layout_height="@dimen/dp_17"
        android:layout_marginStart="@dimen/dp_15"
        android:src="@drawable/selector_circle_select"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_index" />


    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_34"
        android:layout_marginStart="@dimen/dp_15"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_select"
        app:layout_constraintTop_toBottomOf="@id/tv_index" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_15"
        android:layout_marginEnd="@dimen/dp_20"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="研发部 (12)"
        android:textColor="@color/colorFF323232"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toTopOf="@id/tv_info"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toBottomOf="@id/tv_index" />

    <TextView
        android:id="@+id/tv_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:layout_marginEnd="@dimen/dp_20"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="研发部-普通员工"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_name"
        app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#E6E6E6"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>