<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    >

    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/iv_pic"
        android:layout_width="@dimen/dp_39"
        android:layout_height="@dimen/dp_39"
        android:layout_marginTop="@dimen/dp_5"
        android:src="@drawable/ic_placeholder"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="@dimen/dp_5"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLength="5"
        android:singleLine="true"
        android:text="安然-123"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toEndOf="@id/iv_pic"
        app:layout_constraintStart_toStartOf="@id/iv_pic"
        app:layout_constraintTop_toBottomOf="@id/iv_pic" />

    <TextView
        android:id="@+id/externalTag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="10dp"
        android:background="@drawable/shape_d2e6ae_button_rounded"
        android:paddingStart="8dp"
        android:paddingEnd="7dp"
        android:visibility="gone"
        android:paddingTop="3dp"
        android:paddingBottom="3dp"
        android:singleLine="true"
        android:text="外部协作"
        android:textColor="#ff8fc02e"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:textSize="@dimen/textsize_12"
        app:layout_constraintTop_toBottomOf="@id/tv_name" />
</androidx.constraintlayout.widget.ConstraintLayout>