<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="@dimen/dp_2">

    <!--<com.joinutech.approval.custom.lib.RoundedImageView-->
    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/iv_pic"
        android:layout_width="@dimen/dp_29"
        android:layout_height="@dimen/dp_29"
        android:src="@drawable/ic_add_copy"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_del"
        android:layout_width="@dimen/dp_27"
        android:layout_height="@dimen/dp_27"
        android:paddingStart="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        android:src="@drawable/iv_del"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--todo 图片选择 人员选择可复用-->
    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:textColor="@color/colorb2b2b2"
        android:textSize="@dimen/sp_11"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/iv_pic"
        app:layout_constraintStart_toStartOf="@id/iv_pic"
        app:layout_constraintTop_toBottomOf="@id/iv_pic"
        tools:text="担当办公设置" />

</androidx.constraintlayout.widget.ConstraintLayout>