<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:padding="@dimen/dp_15"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <TextView
        android:id="@+id/tv_required"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="*"
        android:textColor="@color/red"
        android:textSize="@dimen/sp_14"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_title"
        style="@style/property_title_detail"
        android:layout_marginStart="@dimen/dp_4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_content"
        app:layout_constraintStart_toEndOf="@id/tv_required"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginStart="@dimen/dp_0" />

    <TextView
        android:id="@+id/tv_content"
        style="@style/property_content_detail"
        android:layout_marginStart="@dimen/dp_15"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_arrow"
        app:layout_constraintStart_toEndOf="@id/tv_title"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_arrow"
        android:layout_width="@dimen/dp_6"
        android:layout_height="@dimen/dp_12"
        android:layout_marginStart="@dimen/dp_5"
        android:src="@drawable/icon_right_arrow"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_content"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>