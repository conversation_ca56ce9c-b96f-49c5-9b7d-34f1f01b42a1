<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@color/white"
        android:paddingStart="@dimen/dp_15"
        android:paddingBottom="@dimen/dp_15">

        <!--显示抄送人的标题-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ll_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_required"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:text="*"
                android:textColor="@color/red"
                android:textSize="@dimen/sp_14"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tv_title"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible"

                />

            <TextView
                android:id="@+id/tv_title"
                style="@style/property_title"
                android:layout_marginStart="@dimen/dp_5"
                android:paddingTop="@dimen/dp_12"
                android:paddingBottom="@dimen/dp_12"
                android:text="抄送人"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/tv_required"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_5"
                android:layout_marginEnd="@dimen/dp_15"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="(审批通过后，将抄送至你选择的人员)"
                android:textColor="@color/color999999"
                android:textSize="@dimen/sp_12"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/tv_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_required"
                app:layout_constraintTop_toTopOf="@id/tv_title"
                tools:visibility="visible" />

            <View
                android:id="@+id/line_middle"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_0_2"
                android:background="@color/line_grey"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
        <!--添加按钮-->
        <ImageView
            android:id="@+id/iv_add_pic"
            android:layout_width="@dimen/dp_43"
            android:layout_height="@dimen/dp_43"
            android:layout_marginStart="@dimen/dp_5"
            android:layout_marginTop="@dimen/dp_10"
            android:src="@drawable/ic_add_2"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_title"
            tools:visibility="visible" />
        <!--添加抄送人-->
        <TextView
            android:id="@+id/tv_copy_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/dp_15"
            android:text="添加抄送人"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/iv_add_pic"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_add_pic"
            app:layout_constraintTop_toTopOf="@id/iv_add_pic"
            tools:visibility="visible" />
        <!--列表-->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_5"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_2"
            android:minHeight="@dimen/dp_38"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_title" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>