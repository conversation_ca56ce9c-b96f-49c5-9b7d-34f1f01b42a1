<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <include
        android:id="@+id/begin_time"
        layout="@layout/property_text_selector"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <View
        android:id="@+id/line_time_segment"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:layout_marginStart="@dimen/dp_15"
        android:background="@color/line_grey"
        android:visibility="gone"
        tools:visibility="visible" />

    <include
        android:id="@+id/end_time"
        layout="@layout/property_text_selector"
        android:layout_width="match_parent"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_height="wrap_content" />

    <View
        android:id="@+id/line_calc_segment"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:layout_marginStart="@dimen/dp_15"
        android:background="@color/line_grey"
        android:visibility="gone"
        tools:visibility="visible" />

    <include
        android:id="@+id/auto_calc_time"
        layout="@layout/property_text_input"
        android:visibility="gone"
        tools:visibility="visible" />
</LinearLayout>