<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.joinutech.ddbeslibrary.widget.roundimage.RoundedImageView
        android:id="@+id/iv_pic"
        android:layout_width="@dimen/dp_35"
        android:layout_height="@dimen/dp_35"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:riv_corner_radius="@dimen/dp_2" />

</androidx.constraintlayout.widget.ConstraintLayout>