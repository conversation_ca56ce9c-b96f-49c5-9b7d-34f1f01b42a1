<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <TextView
        android:id="@+id/tv_apr_temp_set_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp_14"
        android:text="开启此项后，担当办公将给你发送处理提醒"
        android:textColor="@color/text_gren"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_toggle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:minHeight="@dimen/dp_46"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/tv_apr_temp_set_tip">

        <TextView
            android:id="@+id/set_toggle_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_15"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingTop="@dimen/dp_16"
            android:paddingBottom="@dimen/dp_16"
            android:text="未处理审批每日提醒"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toTopOf="@id/set_time_name"
            app:layout_constraintEnd_toStartOf="@id/btn_toggle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kyleduo.switchbutton.SwitchButton
            android:id="@+id/btn_toggle"
            android:layout_width="@dimen/dp_50"
            android:layout_height="@dimen/dp_34"
            android:layout_marginEnd="@dimen/dp_12"
            app:layout_constraintBottom_toBottomOf="@id/set_toggle_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/set_toggle_name"
            android:checked="true"
            app:kswAnimationDuration="300"
            app:kswBackDrawable="@drawable/ios_back_drawable"
            app:kswThumbDrawable="@drawable/ios_thumb_selector"
            app:kswThumbMarginBottom="-8dp"
            app:kswThumbMarginLeft="-5dp"
            app:kswThumbMarginRight="-5dp"
            app:kswThumbMarginTop="-2.5dp"
            app:kswThumbRangeRatio="1.4"
            />

        <View
            android:id="@+id/line_middle"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_2"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_12"
            android:background="@color/line_grey"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/set_time_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_15"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingTop="@dimen/dp_16"
            android:paddingBottom="@dimen/dp_16"
            android:text="提醒时间"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_time"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/set_toggle_name" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_8"
            android:paddingTop="@dimen/dp_16"
            android:paddingBottom="@dimen/dp_16"
            android:text="09：00"
            android:textColor="@color/colorFF323232"
            android:textSize="@dimen/sp_13"
            app:layout_constraintBottom_toBottomOf="@id/set_time_name"
            app:layout_constraintEnd_toStartOf="@id/iv_arrow"
            app:layout_constraintTop_toTopOf="@id/set_time_name"
            app:layout_goneMarginEnd="@dimen/dp_12" />

        <ImageView
            android:id="@+id/iv_arrow"
            android:layout_width="@dimen/dp_6"
            android:layout_height="@dimen/dp_14"
            android:layout_marginEnd="@dimen/dp_12"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_time"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_time" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>