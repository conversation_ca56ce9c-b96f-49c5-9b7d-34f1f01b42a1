<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_property_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/dp_15"
        android:paddingTop="@dimen/dp_10"
        android:paddingEnd="@dimen/dp_15"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_required"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text="*"
            android:textColor="@color/red"
            android:textSize="@dimen/sp_14"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_content"
            app:layout_constraintStart_toEndOf="@id/tv_title"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/tv_title"
            style="@style/property_title"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_5"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_required"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginStart="@dimen/dp_0"
            tools:text="用印文件名称" />

        <TextView
            android:id="@+id/tv_content"
            style="@style/property_content"
            android:layout_marginStart="@dimen/dp_10"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_arrow"
            app:layout_constraintStart_toEndOf="@id/tv_required"
            app:layout_constraintTop_toTopOf="parent"
            tools:hint="请填写用印标题，不超过10个字" />

        <ImageView
            android:id="@+id/iv_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_5"
            android:layout_marginEnd="@dimen/dp_10"
            android:src="@drawable/icon_right_arrow"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_add_cell"
            app:layout_constraintStart_toEndOf="@id/tv_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginEnd="@dimen/dp_0" />

        <ImageView
            android:id="@+id/iv_add_cell"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_5"
            android:src="@drawable/select_ic_apr_plus"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_del_cell"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginEnd="@dimen/dp_0"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/iv_del_cell"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_30"
            android:src="@drawable/select_ic_apr_mu"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/line_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:layout_marginStart="@dimen/dp_15"
        android:background="@color/line_grey"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_property_content" />

</androidx.constraintlayout.widget.ConstraintLayout>