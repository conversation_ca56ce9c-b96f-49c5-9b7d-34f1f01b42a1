<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dp_10"
    android:background="@color/white">

<!--文件头像容器-->
    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.joinutech.ddbeslibrary.widget.roundimage.RoundedImageView
            android:id="@+id/file_header_riv"
            android:layout_width="@dimen/dp_46"
            android:layout_height="@dimen/dp_46"
            android:layout_marginEnd="@dimen/dp_7"
            android:paddingStart="@dimen/dp_1"
            android:paddingBottom="@dimen/dp_1"
            android:layout_marginStart="4dp"
            android:paddingEnd="0dp"
            android:src="@drawable/bg_image_default"
            app:riv_corner_radius="@dimen/dp_4" />

<!--进度遮罩-->
        <com.joinutech.ddbeslibrary.widget.roundimage.RoundedImageView
            android:id="@+id/file_progress_riv"
            android:layout_width="@dimen/dp_46"
            android:layout_height="@dimen/dp_46"
            android:layout_marginEnd="@dimen/dp_7"
            android:paddingStart="@dimen/dp_1"
            android:layout_marginStart="4dp"
            android:visibility="gone"
            tools:visibility="visible"
            android:paddingBottom="@dimen/dp_1"
            android:background="#590082F2"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:riv_corner_radius="@dimen/dp_4" />


<!--进度数字-->
        <TextView
            android:id="@+id/file_progress_tv"
            android:text="15%"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_centerInParent="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </RelativeLayout>


<!---->

<!--文件名称-->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/file_name_tv"
            style="@style/property_title_detail"
            android:ellipsize="end"
            tools:text="这是文件名文件名文件名"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/file_length_tv"
            style="@style/property_content_detail"
            android:layout_marginTop="6dp"
            tools:text="999Mb"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </LinearLayout>

<!--删除按钮-->
    <ImageView
        android:id="@+id/file_delete_iv"
        android:src="@drawable/del_img"
        android:layout_marginEnd="16dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:paddingStart="6dp"
        android:paddingEnd="6dp"
        android:layout_width="28dp"
        android:layout_height="28dp"/>


</LinearLayout>