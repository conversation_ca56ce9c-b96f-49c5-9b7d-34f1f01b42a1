<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:padding="@dimen/dp_15"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <TextView
        android:id="@+id/tv_required"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="*"
        android:textColor="@color/red"
        android:textSize="@dimen/sp_14"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/property_title_detail"
        android:layout_marginStart="@dimen/dp_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_required"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginStart="@dimen/dp_0" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_45"
        android:layout_marginStart="@dimen/dp_15"
        android:background="@drawable/bg_gray_corner_frame"
        android:paddingStart="@dimen/dp_10"
        android:paddingTop="@dimen/dp_5"
        android:paddingEnd="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_content"
            style="@style/property_content"
            android:layout_marginEnd="@dimen/dp_5"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/c_0082F2"
            android:visibility="visible"
            app:layout_constraintBottom_toTopOf="@id/tv_sub_content"
            app:layout_constraintEnd_toStartOf="@id/iv_arrow"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginBottom="@dimen/dp_0"
            app:layout_goneMarginEnd="@dimen/dp_0" />

        <TextView
            android:id="@+id/tv_sub_content"
            style="@style/property_content"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginEnd="@dimen/dp_5"
            android:ellipsize="end"
            android:maxLines="1"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_arrow"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_content" />

        <ImageView
            android:id="@+id/iv_arrow"
            android:layout_width="@dimen/dp_22"
            android:layout_height="@dimen/dp_22"
            android:src="@drawable/icon_location_in_detail"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>