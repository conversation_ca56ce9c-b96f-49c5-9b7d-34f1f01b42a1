<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:minHeight="@dimen/dp_46"
    android:orientation="vertical">

    <TextView
        android:id="@+id/set_toggle_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_15"
        android:layout_marginBottom="@dimen/dp_16"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="允许发起人使用提醒审批功能"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btn_toggle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.kyleduo.switchbutton.SwitchButton
        android:id="@+id/btn_toggle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:kswAnimationDuration="300"
        app:kswBackDrawable="@drawable/ios_back_drawable"
        app:kswThumbDrawable="@drawable/ios_thumb_selector"
        app:kswThumbMarginBottom="-8dp"
        app:kswThumbMarginLeft="-5dp"
        app:kswThumbMarginRight="-5dp"
        app:kswThumbMarginTop="-2.5dp"
        app:kswThumbRangeRatio="1.4"
        />
</androidx.constraintlayout.widget.ConstraintLayout>