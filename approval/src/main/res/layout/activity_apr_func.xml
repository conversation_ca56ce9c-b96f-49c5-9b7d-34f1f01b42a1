<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".func.AprFuncActivity">
    <!--头部四个按钮的容器-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_entrance"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingBottom="@dimen/dp_15"
        app:layout_constraintTop_toTopOf="parent">
        <!--全部审批按钮-->
        <FrameLayout
            android:id="@+id/ll_total_approval"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:orientation="vertical"
            android:visibility="visible"
            app:layout_constraintEnd_toStartOf="@id/ll_launch"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_total_approval"
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_36"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp_10"
                android:src="@drawable/icon_total_launch_2" />

            <TextView
                android:id="@+id/tv_total_approval"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp_57"
                android:text="全部审批"
                android:textColor="@color/text_gren"
                android:textSize="@dimen/textsize_13" />

            <TextView
                android:id="@+id/tv_total_approval_count"
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:layout_gravity="top|end"
                android:background="@drawable/bg_red_circle"
                android:gravity="center"
                android:text="88"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_12"
                android:visibility="gone" />
        </FrameLayout>
        <!--我发起的按钮-->
        <FrameLayout
            android:id="@+id/ll_launch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:orientation="vertical"
            app:layout_constraintEnd_toStartOf="@id/ll_handle"
            app:layout_constraintStart_toEndOf="@+id/ll_total_approval"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_apr_launch_port"
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_36"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp_10"
                android:src="@drawable/icon_launch_2" />

            <TextView
                android:id="@+id/tv_apr_launch_port"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp_57"
                android:text="我发起的"
                android:textColor="@color/text_gren"
                android:textSize="@dimen/textsize_13" />

            <TextView
                android:id="@+id/tv_launch_count"
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:layout_gravity="top|end"
                android:background="@drawable/bg_red_circle"
                android:gravity="center"
                android:text="88"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_12" />
        </FrameLayout>
        <!--我审批的-->
        <FrameLayout
            android:id="@+id/ll_handle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:orientation="vertical"
            app:layout_constraintEnd_toStartOf="@id/ll_copy"
            app:layout_constraintStart_toEndOf="@id/ll_launch"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_36"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp_10"
                android:src="@drawable/icon_handle_2" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp_57"
                android:text="我审批的"
                android:textColor="@color/text_gren"
                android:textSize="@dimen/textsize_13" />

            <TextView
                android:id="@+id/tv_handle_count"
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:layout_gravity="top|end"
                android:background="@drawable/bg_red_circle"
                android:gravity="center"
                android:text="88"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_12" />
        </FrameLayout>
        <!--抄送我的-->
        <FrameLayout
            android:id="@+id/ll_copy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/ll_handle"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_36"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp_10"
                android:src="@drawable/icon_copy_2" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp_57"
                android:text="抄送我的"
                android:textColor="@color/text_gren"
                android:textSize="@dimen/sp_12" />

            <TextView
                android:id="@+id/tv_copy_count"
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:layout_gravity="top|end"
                android:background="@drawable/bg_red_circle"
                android:gravity="center"
                android:text="88"
                android:textColor="@color/white"
                android:textSize="@dimen/textsize_13" />
        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
    <!--列表和空白页的容器-->
    <include
        layout="@layout/common_list_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_entrance" />

</androidx.constraintlayout.widget.ConstraintLayout>
