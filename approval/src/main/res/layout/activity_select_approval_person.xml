<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/topLayout"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="0.2dp"
            android:background="@color/line_grey"
            app:layout_constraintBottom_toBottomOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/orgMemberLayout"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingStart="14dp"
            android:paddingEnd="14dp"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                android:id="@+id/orgMemberTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="团队成员"
                android:textColor="@color/color_tab_text"
                android:textSize="@dimen/textsize_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/orgMemberLine"
                android:layout_width="24dp"
                android:layout_height="2dp"
                android:layout_marginTop="9dp"
                android:background="@color/main_blue"
                app:layout_constraintEnd_toEndOf="@id/orgMemberTv"
                app:layout_constraintStart_toStartOf="@id/orgMemberTv"
                app:layout_constraintTop_toBottomOf="@id/orgMemberTv" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/externalLayout"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingStart="14dp"
            android:paddingEnd="14dp"
            app:layout_constraintStart_toEndOf="@id/orgMemberLayout">

            <TextView
                android:id="@+id/externalMemberTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="外部协作人员"
                android:textColor="@color/color_tab_text"
                android:textSize="@dimen/textsize_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/externalMemberLine"
                android:layout_width="24dp"
                android:layout_height="2dp"
                android:layout_marginTop="9dp"
                android:background="@color/main_blue"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/externalMemberTv"
                app:layout_constraintStart_toStartOf="@id/externalMemberTv"
                app:layout_constraintTop_toBottomOf="@id/externalMemberTv" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/searchTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="13dp"
            android:background="@drawable/bg_ecedef_corner_14"
            android:drawableStart="@drawable/icon_search"
            android:drawablePadding="3dp"
            android:gravity="center"
            android:paddingStart="9dp"
            android:paddingTop="4dp"
            android:paddingEnd="18dp"
            android:paddingBottom="4dp"
            android:text="搜索"
            android:textColor="#ffa6a6a6"
            android:textSize="@dimen/textsize_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/contentViewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topLayout"
        app:layout_constraintVertical_weight="1" />
</androidx.constraintlayout.widget.ConstraintLayout>