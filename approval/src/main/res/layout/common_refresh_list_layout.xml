<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/srl_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <!--公共无数据页面显示-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:ev_content="暂无审批"
        app:ev_icon="@drawable/ic_empty_approval" />
</FrameLayout>