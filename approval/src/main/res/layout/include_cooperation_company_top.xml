<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/background"
    android:paddingTop="70dp"
    android:visibility="visible">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/top_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="25dp"
        android:layout_marginTop="20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.joinutech.ddbeslibrary.widget.CircleImageView
            android:id="@+id/logo"
            android:layout_width="45dp"
            android:layout_height="45dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="81dp"
            android:layout_marginEnd="10dp"
            android:textColor="#ff323232"
            android:textSize="@dimen/textsize_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/logo" />

        <TextView
            android:id="@+id/tv_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:background="@drawable/shape_cooperation_dfeaf7"
            android:paddingStart="9dp"
            android:paddingTop="2dp"
            android:paddingEnd="9dp"
            android:paddingBottom="2dp"
            android:textColor="#ff2479ed"
            android:textSize="@dimen/textsize_11"
            app:layout_constraintStart_toStartOf="@id/name"
            app:layout_constraintTop_toBottomOf="@id/name" />

        <TextView
            android:id="@+id/tv_kind"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="9dp"
            android:layout_marginTop="2dp"
            android:background="@drawable/shape_cooperation_dfeaf7"
            android:paddingStart="9dp"
            android:paddingTop="2dp"
            android:paddingEnd="9dp"
            android:paddingBottom="2dp"
            android:textColor="#ff2479ed"
            android:textSize="@dimen/textsize_11"
            app:layout_constraintStart_toEndOf="@id/tv_type"
            app:layout_constraintTop_toBottomOf="@id/name" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_detail_layout"
        android:layout_width="322dp"
        android:layout_height="154dp"
        android:layout_marginTop="19dp"
        android:background="@drawable/icon_cooperation_company_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/top_layout">

        <TextView
            android:id="@+id/tv_person_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="31dp"
            android:layout_marginTop="26dp"
            tools:text="郭向阳"
            android:textColor="#ffffffff"
            android:textSize="@dimen/textsize_19"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_person_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:background="@drawable/shape_cooperation_0049ab"
            android:paddingStart="10dp"
            android:paddingTop="3dp"
            android:paddingEnd="10dp"
            android:paddingBottom="3dp"
            tools:text="客户"
            android:textColor="#ffffffff"
            android:textSize="@dimen/textsize_12"
            app:layout_constraintBottom_toBottomOf="@id/tv_person_name"
            app:layout_constraintStart_toEndOf="@id/tv_person_name"
            app:layout_constraintTop_toTopOf="@id/tv_person_name" />

        <ImageView
            android:id="@+id/iv_person_phone_icon"
            android:layout_width="19dp"
            android:layout_height="19dp"
            android:layout_marginTop="17dp"
            android:src="@drawable/icon_person_info_phone"
            app:layout_constraintStart_toStartOf="@id/tv_person_name"
            app:layout_constraintTop_toBottomOf="@id/tv_person_name" />

        <TextView
            android:id="@+id/tv_person_phone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            tools:text="13825632587"
            android:textColor="#ffffffff"
            android:textSize="@dimen/textsize_12"
            app:layout_constraintBottom_toBottomOf="@id/iv_person_phone_icon"
            app:layout_constraintStart_toEndOf="@id/iv_person_phone_icon"
            app:layout_constraintTop_toTopOf="@id/iv_person_phone_icon" />

        <ImageView
            android:id="@+id/iv_email_icon"
            android:layout_width="19dp"
            android:layout_height="19dp"
            android:layout_marginTop="5dp"
            android:src="@drawable/icon_person_info_email"
            app:layout_constraintStart_toStartOf="@id/iv_person_phone_icon"
            app:layout_constraintTop_toBottomOf="@id/iv_person_phone_icon" />

        <TextView
            android:id="@+id/tv_email"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            tools:text="<EMAIL>"
            android:textColor="#ffffffff"
            android:textSize="@dimen/textsize_12"
            app:layout_constraintBottom_toBottomOf="@id/iv_email_icon"
            app:layout_constraintStart_toEndOf="@id/iv_email_icon"
            app:layout_constraintTop_toTopOf="@id/iv_email_icon" />

        <TextView
            android:id="@+id/tv_email_edit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="编辑"
            android:textColor="#ffffffff"
            android:textSize="@dimen/textsize_12"
            app:layout_constraintBottom_toBottomOf="@id/iv_email_icon"
            app:layout_constraintStart_toEndOf="@id/tv_email"
            app:layout_constraintTop_toTopOf="@id/iv_email_icon" />

        <ImageView
            android:id="@+id/iv_remark_icon"
            android:layout_width="19dp"
            android:layout_height="19dp"
            android:layout_marginTop="5dp"
            android:src="@drawable/icon_person_info_remark"
            app:layout_constraintStart_toStartOf="@id/iv_email_icon"
            app:layout_constraintTop_toBottomOf="@id/iv_email_icon" />

        <TextView
            android:id="@+id/tv_remark_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:text="合作项目对接人"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            app:layout_constraintBottom_toBottomOf="@id/iv_remark_icon"
            app:layout_constraintStart_toEndOf="@id/iv_remark_icon"
            app:layout_constraintTop_toTopOf="@id/iv_remark_icon" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="176dp"
        android:layout_height="31dp"
        android:layout_marginTop="123dp"
        android:background="@drawable/icon_white_left"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/cl_detail_layout" />

    <View
        android:layout_width="176dp"
        android:layout_height="31dp"
        android:layout_marginTop="123dp"
        android:background="@drawable/icon_white_right"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/cl_detail_layout" />
</androidx.constraintlayout.widget.ConstraintLayout>