<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/desc_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:textColor="@color/colorFF323232"
        android:textSize="@dimen/sp_13" />

    <!--可以灵活的将收藏的按钮至右 至左 居中  -->
    <TextView
        android:id="@+id/desc_op_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4.0dip"
        android:gravity="start|left"
        android:singleLine="true"
        android:textColor="@color/c_1E80F7"
        android:textSize="@dimen/sp_13"
        android:visibility="gone" />

</LinearLayout>