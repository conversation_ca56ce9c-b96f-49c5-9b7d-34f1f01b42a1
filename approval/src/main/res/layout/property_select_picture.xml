<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:paddingStart="@dimen/dp_15"
    android:paddingEnd="@dimen/dp_15"
    android:paddingTop="@dimen/dp_12"
    android:paddingBottom="@dimen/dp_12"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <TextView
        android:id="@+id/tv_required"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="*"
        android:includeFontPadding="false"
        android:textColor="@color/red"
        android:textSize="@dimen/sp_14"
        android:visibility="invisible"
        tools:visibility="visible"
        app:layout_constraintEnd_toStartOf="@id/tv_content"
        app:layout_constraintStart_toEndOf="@id/tv_title"
        app:layout_constraintTop_toTopOf="parent"
        />

    <TextView
        android:id="@+id/tv_title"
        style="@style/property_title"
        android:layout_marginStart="@dimen/dp_4"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="证明文件"
        app:layout_constraintEnd_toStartOf="@id/tv_required"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginStart="@dimen/dp_0" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="wrap_content"
        android:layout_height="16dp"
        android:layout_marginStart="@dimen/dp_5"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="(请选择图片)"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintStart_toEndOf="@id/tv_required"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

    <ImageView
        android:id="@+id/iv_add_pic"
        android:layout_width="@dimen/dp_22"
        android:layout_height="@dimen/dp_22"
        android:src="@drawable/ic_add_pic_2"
        app:layout_constraintTop_toTopOf="@+id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_14"
        android:minHeight="@dimen/dp_120"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />


</androidx.constraintlayout.widget.ConstraintLayout>