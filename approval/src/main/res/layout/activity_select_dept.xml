<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:focusable="true"
    android:focusableInTouchMode="true"
    tools:context=".org.SelectDeptActivity">

    <!--搜索框-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/search_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:background="@color/white"
        android:orientation="vertical"
        android:paddingStart="@dimen/dp_11"
        android:paddingTop="@dimen/dp_10"
        android:paddingEnd="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintTop_toTopOf="parent">

        <EditText
            android:id="@+id/et_search_input"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_28"
            android:layout_marginEnd="8dp"
            android:background="@drawable/bg_gray_corner_14"
            android:hint="搜索"
            android:imeOptions="actionSearch"
            android:maxLines="1"
            android:paddingStart="@dimen/dp_26"
            android:paddingEnd="@dimen/dp_26"
            android:singleLine="true"
            android:textColor="@color/text_black"
            android:textColorHint="@color/colorFFAAAAAA"
            android:textSize="@dimen/sp_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_cancel"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginEnd="@dimen/dp_0" />

        <ImageView
            android:layout_width="@dimen/dp_17"
            android:layout_height="@dimen/dp_17"
            android:layout_marginStart="@dimen/dp_8"
            android:src="@drawable/icon_search"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_input_clear"
            android:layout_width="@dimen/dp_12"
            android:layout_height="@dimen/dp_12"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_5"
            android:src="@drawable/del_img"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/et_search_input"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="取消"
            android:textSize="@dimen/sp_15"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ScrollView
        android:id="@+id/sv_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/btn_select_dept"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search_bar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!--层级列表-->
            <include
                android:id="@+id/level_layout"
                layout="@layout/include_level_list_layout" />

            <!--当前部门 上级部门信息-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_company_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:paddingBottom="@dimen/dp_14"
                app:layout_constraintTop_toBottomOf="@id/level_layout">

                <TextView
                    android:id="@+id/tv_company_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_12"
                    android:layout_marginTop="@dimen/dp_14"
                    android:textColor="@color/colorFF333333"
                    android:textSize="@dimen/sp_14"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="加优科技有限公司" />

                <TextView
                    android:id="@+id/tv_dept_level"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_12"
                    android:layout_marginTop="@dimen/dp_8"
                    android:text="部门层级：1级部门"
                    android:textColor="@color/color999999"
                    android:textSize="@dimen/sp_12"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_company_name" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_sub_dept"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_24"
                android:background="@color/background"
                android:gravity="center"
                android:text="子部门"
                android:textColor="@color/color999999"
                android:textSize="@dimen/sp_12"
                app:layout_constraintTop_toBottomOf="@id/cl_company_layout" />

            <!--子部门列表-->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_sub_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/tv_sub_dept" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <TextView
        android:id="@+id/btn_select_dept"
        android:layout_width="match_parent"
        android:layout_height="47dp"
        android:background="@color/white"
        android:gravity="center"
        android:text="选择此部门"
        android:textColor="#ff1e87f0"
        android:textSize="15sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sv_list" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/dept_search_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_1"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search_bar" />
</androidx.constraintlayout.widget.ConstraintLayout>