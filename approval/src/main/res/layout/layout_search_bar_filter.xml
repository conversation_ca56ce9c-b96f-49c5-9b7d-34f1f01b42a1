<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_42"
    android:background="@color/white"
    android:orientation="vertical"
    android:padding="@dimen/dp_10">

    <EditText
        android:id="@+id/et_search_input"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_27"
        android:layout_marginEnd="@dimen/dp_10"
        android:layout_marginStart="@dimen/dp_5"
        android:background="@drawable/bg_gray_corner_5"
        android:hint="搜索"
        android:imeOptions="actionSearch"
        android:maxLines="1"
        android:paddingStart="@dimen/dp_26"
        android:paddingEnd="@dimen/dp_26"
        android:singleLine="true"
        android:textColor="@color/text_black"
        android:textColorHint="#8E8E93"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_filter"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:layout_marginStart="@dimen/dp_8"
        android:src="@drawable/icon_search"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/et_search_input"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_input_clear"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:paddingEnd="@dimen/dp_4"
        android:paddingStart="@dimen/dp_4"
        android:src="@drawable/del_img"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/et_search_input"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_filter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="筛选"
        android:paddingEnd="@dimen/dp_8"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>