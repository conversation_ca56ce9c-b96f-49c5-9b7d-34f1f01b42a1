<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <include
        android:id="@+id/begin_time"
        layout="@layout/property_text_selector_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/line_time_segment"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/line_time_segment"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        app:layout_constraintBottom_toTopOf="@id/end_time"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@color/line_grey"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/begin_time" />

    <include
        android:id="@+id/end_time"
        layout="@layout/property_text_selector_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line_time_segment" />
</androidx.constraintlayout.widget.ConstraintLayout>