<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".AprSignatureActivity">

    <!--状态栏-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/sign_title_contain_cl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/dp_2"
        android:paddingTop="@dimen/dp_2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/sign_back_iv"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginStart="2dp"
            android:paddingStart="3dp"
            android:paddingTop="3dp"
            android:paddingEnd="3dp"
            android:paddingBottom="3dp"
            android:src="@drawable/back_grey"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/sign_title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="这是标题" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--手写控件-->
    <com.joinutech.approval.custom.HandwritingBoardView
        android:id="@+id/signature_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="@dimen/dp_1"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/shape_gray_tag_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/sign_btns_contain_cl"
        app:layout_constraintHorizontal_weight="6"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sign_title_contain_cl" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/sign_btns_contain_cl"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/signature_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/signature_view"
        app:layout_constraintTop_toTopOf="@+id/signature_view">

        <!--取消-->
        <TextView
            android:id="@+id/signature_cancel_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"
            android:text="取消"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/signature_confirm_tv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
        <!--确认-->
        <TextView
            android:id="@+id/signature_confirm_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_marginEnd="@dimen/dp_4"
            android:background="@drawable/shape_blue_btn_bg"
            android:gravity="center"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"
            android:text="确认"
            android:textColor="@color/white"
            android:textSize="12sp"
            app:layout_constraintBottom_toTopOf="@+id/signature_undo_tv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/signature_cancel_tv" />

        <!--撤销-->
        <TextView
            android:id="@+id/signature_undo_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"
            android:text="撤销"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/signature_retry_tv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/signature_confirm_tv" />

        <!--重写-->
        <TextView
            android:id="@+id/signature_retry_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_marginEnd="@dimen/dp_4"
            android:background="@drawable/shape_blue_btn_bg"
            android:gravity="center"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"
            android:text="重写"
            android:textColor="@color/white"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/signature_undo_tv" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>