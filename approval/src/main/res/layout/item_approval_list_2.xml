<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <!--箭头-->
    <ImageView
        android:id="@+id/arrow_tag_iv"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:layout_marginEnd="1dp"
        android:src="@drawable/ic_approval_arrow"
        app:layout_constraintBottom_toBottomOf="@+id/iv_pic"
        app:layout_constraintStart_toEndOf="@+id/iv_pic"
        android:layout_marginStart="4dp"
        app:layout_constraintTop_toTopOf="@+id/iv_pic" />


    <!--头像-->
    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/iv_pic"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:layout_marginEnd="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@color/text_hint_color_2" />

<!--用于实现类似负值的layout_margin...属性效果-->
    <androidx.legacy.widget.Space
        android:id="@+id/space_view"
        android:layout_width="0dp"
        app:layout_constraintTop_toTopOf="@+id/iv_pic"
        app:layout_constraintEnd_toEndOf="@+id/iv_pic"
        android:layout_marginEnd="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_height="0dp"/>

    <!--预约会议删除人员按钮-->
    <ImageView
        android:id="@+id/iv_del"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:src="@drawable/icon_black_fork_2"
        android:visibility="gone"
       app:layout_constraintStart_toEndOf="@+id/space_view"
        app:layout_constraintBottom_toTopOf="@+id/space_view"
        tools:visibility="visible" />

    <!-- 图片选择 人员选择可复用-->
     <TextView
         android:id="@+id/tv_name"
         android:layout_width="@dimen/dp_56"
         android:layout_height="wrap_content"
         android:ellipsize="end"
         android:maxLines="1"
         android:gravity="center"
         android:text="安然-123"
         android:textColor="@color/colorFF333333"
         android:textSize="@dimen/sp_12"
         android:visibility="gone"
         app:layout_constraintEnd_toEndOf="@id/iv_pic"
         app:layout_constraintStart_toStartOf="@id/iv_pic"
         app:layout_constraintTop_toBottomOf="@id/iv_pic"
         tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>