<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <ImageView
        android:id="@+id/temp_icon"
        android:layout_width="@dimen/dp_46"
        android:layout_height="@dimen/dp_46"
        android:layout_marginStart="@dimen/dp_14"
        android:layout_marginTop="@dimen/dp_15"
        android:layout_marginBottom="@dimen/dp_15"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/temp_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_12"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="请假"
        android:textColor="@color/text_black"
        android:textSize="@dimen/sp_15"
        app:layout_constraintStart_toEndOf="@id/temp_icon"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/temp_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_15"
        android:layout_marginBottom="@dimen/dp_12"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/color808080"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/temp_notify"
        app:layout_constraintStart_toStartOf="@id/temp_name"
        app:layout_constraintTop_toBottomOf="@id/temp_name" />

    <TextView
        android:id="@+id/temp_notify"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_10"
        android:background="@drawable/bg_blue_circle"
        android:paddingStart="@dimen/dp_4"
        android:paddingTop="@dimen/dp_7"
        android:paddingEnd="@dimen/dp_4"
        android:paddingBottom="@dimen/dp_7"
        android:visibility="gone"
        android:text="通知全员"
        android:textColor="@color/color1E87F0"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/temp_toggle"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/temp_toggle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_14"
        android:background="@drawable/bg_blue_circle"
        android:paddingStart="@dimen/dp_6"
        android:paddingTop="@dimen/dp_7"
        android:paddingEnd="@dimen/dp_6"
        android:paddingBottom="@dimen/dp_7"
        android:text="停用"
        android:textColor="@color/color1E87F0"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>