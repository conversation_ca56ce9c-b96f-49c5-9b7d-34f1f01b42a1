<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingStart="20dp"
    android:paddingEnd="20dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_rounded_dialog">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            android:text="提示"
            android:textColor="@color/text_black"
            android:textSize="@dimen/sp_16"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_left"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="每日"
            android:textColor="@color/colorFF333333"
            app:layout_constraintBottom_toBottomOf="@id/wv_hour"
            app:layout_constraintEnd_toStartOf="@id/wv_hour"
            app:layout_constraintTop_toTopOf="@id/wv_hour" />

        <com.joinutech.ddbeslibrary.widget.contrarywind.view.CustomWheelView
            android:id="@+id/wv_hour"
            android:layout_width="@dimen/dp_55"
            android:layout_height="@dimen/dp_100"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <TextView
            android:id="@+id/tv_right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="提醒"
            android:textColor="@color/colorFF333333"
            app:layout_constraintBottom_toBottomOf="@id/wv_hour"
            app:layout_constraintStart_toEndOf="@id/wv_hour"
            app:layout_constraintTop_toTopOf="@id/wv_hour" />

        <View
            android:id="@+id/line_ho"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_2"
            android:layout_marginTop="@dimen/dp_30"
            android:background="@color/line_grey"
            app:layout_constraintTop_toBottomOf="@id/wv_hour" />

        <View
            android:id="@+id/line_v"
            android:layout_width="@dimen/dp_0_2"
            android:layout_height="0dp"
            android:background="@color/line_grey"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line_ho" />

        <TextView
            android:id="@+id/cancel"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginTop="@dimen/dp_30"
            android:layout_weight="1"
            android:gravity="center"
            android:text="取消"
            android:textColor="@color/color666666"
            app:layout_constraintEnd_toStartOf="@id/confirm"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/wv_hour"
            app:layout_goneMarginTop="@dimen/dp_10" />

        <TextView
            android:id="@+id/confirm"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginStart="20dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="确定"
            android:textColor="@color/main_blue"
            app:layout_constraintBottom_toBottomOf="@id/cancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@id/cancel"
            app:layout_constraintTop_toTopOf="@id/cancel" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>