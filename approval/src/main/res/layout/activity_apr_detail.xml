<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    tools:context=".AprDetailActivity">

    <TextView
        android:id="@+id/topEndTime"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:paddingTop="10dp"
        android:paddingBottom="8dp"
        android:textColor="#ff666666"
        android:textSize="@dimen/textsize_11"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/detail_top_bar"
        layout="@layout/layout_apr_detail_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topEndTime" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/srl_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/detail_bottom_bar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/detail_top_bar"
        app:layout_constraintVertical_bias="0.0"
        app:layout_constraintVertical_weight="1">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingTop="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_10"
            android:visibility="visible" />
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/detail_bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible">

        <LinearLayout
            android:id="@+id/ll_remind"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:minHeight="@dimen/dp_47"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ll_retry"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="gone">

            <ImageView
                style="@style/apr_bottom_bar_icon"
                android:src="@drawable/icon_remind" />

            <TextView
                style="@style/default_text"
                android:layout_marginTop="@dimen/dp_5"
                android:text="催办" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_retry"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:minHeight="@dimen/dp_47"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ll_comment"
            app:layout_constraintStart_toEndOf="@id/ll_remind"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="gone">

            <ImageView
                style="@style/apr_bottom_bar_icon"
                android:src="@drawable/icon_node_retry" />

            <TextView
                style="@style/default_text"
                android:layout_marginTop="@dimen/dp_5"
                android:text="重新提交" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_comment"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:minHeight="@dimen/dp_47"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ll_add_apr"
            app:layout_constraintStart_toEndOf="@id/ll_retry"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <ImageView
                style="@style/apr_bottom_bar_icon"
                android:src="@drawable/icon_comment" />

            <TextView
                style="@style/default_text"
                android:layout_marginTop="@dimen/dp_5"
                android:text="评论" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_add_apr"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:minHeight="@dimen/dp_47"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ll_refuse"
            app:layout_constraintStart_toEndOf="@id/ll_comment"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="gone">

            <ImageView
                style="@style/apr_bottom_bar_icon"
                android:src="@drawable/icon_add_apr" />

            <TextView
                style="@style/default_text"
                android:layout_marginTop="@dimen/dp_5"
                android:text="加审" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_refuse"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:minHeight="@dimen/dp_47"
            android:orientation="vertical"
            android:paddingStart="@dimen/dp_10"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ll_agree"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <TextView
                android:layout_width="104dp"
                android:layout_height="@dimen/dp_36"
                android:background="@drawable/bg_gray_left_corner"
                android:gravity="center"
                android:text="拒绝"
                android:textColor="#386DFF"
                android:textSize="@dimen/sp_14"
                android:visibility="visible" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_agree"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:minHeight="@dimen/dp_47"
            android:orientation="vertical"
            android:paddingEnd="@dimen/dp_10"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <TextView
                android:layout_width="104dp"
                android:layout_height="@dimen/dp_36"
                android:background="@drawable/bg_blue_right_corner"
                android:gravity="center"
                android:text="同意"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14"
                android:visibility="visible" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/bottomApprovalToDoHint"
        android:layout_width="wrap_content"
        android:layout_height="34dp"
        android:layout_marginBottom="64dp"
        android:visibility="gone"
        app:cardCornerRadius="17dp"
        app:layout_constraintBottom_toTopOf="@id/detail_bottom_bar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/approvalNum"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:textColor="#ff333333"
                android:textSize="@dimen/textsize_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/line"
                android:layout_width="0.2dp"
                android:layout_height="match_parent"
                android:layout_marginStart="10dp"
                android:background="@color/line_grey"
                app:layout_constraintStart_toEndOf="@id/approvalNum" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="10dp"
                android:paddingEnd="13dp"
                android:text="现在处理  >"
                android:textColor="#ff0d4df0"
                android:textSize="@dimen/textsize_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/line"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>