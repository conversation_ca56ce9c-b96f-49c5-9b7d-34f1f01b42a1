<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="43dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/launch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="19dp"
            android:paddingEnd="10dp"
            android:text="审批"
            android:textColor="#ff323232"
            android:textSize="@dimen/textsize_15"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/copy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="18dp"
            android:paddingEnd="10dp"
            android:text="抄送"
            android:textColor="#ff323232"
            android:textSize="@dimen/textsize_15"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/launch"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/line_launch"
            android:layout_width="19dp"
            android:layout_height="2dp"
            android:layout_marginStart="24dp"
            android:background="@color/main_blue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <View
            android:id="@+id/line_copy"
            android:layout_width="19dp"
            android:layout_height="2dp"
            android:layout_marginStart="40dp"
            android:background="@color/main_blue"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/line_launch" />

        <View
            android:id="@+id/dot_launch"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:background="@drawable/shape_round_red_dot"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@id/launch"
            app:layout_constraintTop_toTopOf="@id/launch" />

        <View
            android:id="@+id/dot_copy"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:background="@drawable/shape_round_red_dot"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@id/copy"
            app:layout_constraintTop_toTopOf="@id/copy" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="43dp"
        android:background="@color/line_grey"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_approval_type_layout"
        android:layout_width="wrap_content"
        android:layout_height="43dp"
        android:layout_marginEnd="15dp"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_approval_type"
            android:layout_width="19dp"
            android:layout_height="19dp"
            android:src="@drawable/icon_arrow_down_black"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_approval_handle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            android:text="待处理"
            android:textColor="#ff323232"
            android:textSize="@dimen/textsize_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_approval_type"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_undo_layout"
        android:layout_width="wrap_content"
        android:layout_height="43dp"
        android:layout_marginEnd="15dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_undo_icon"
            android:layout_width="17dp"
            android:layout_height="17dp"
            android:src="@drawable/selector_special_date"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            android:text="只看未读"
            android:textColor="#ff323232"
            android:textSize="@dimen/textsize_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_undo_icon"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/vp_viewpager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line"
        app:layout_constraintVertical_weight="1" />
</androidx.constraintlayout.widget.ConstraintLayout>