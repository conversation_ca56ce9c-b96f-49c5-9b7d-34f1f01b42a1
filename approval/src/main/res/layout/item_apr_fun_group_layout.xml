<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dp_14"
    android:background="@color/white"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_group_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_15"
        android:layout_marginTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        android:text="出勤休假"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/textsize_14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_group_toggle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_14"
        android:paddingBottom="@dimen/dp_10"
        android:text="收起"
        android:textColor="@color/color808080"
        android:textSize="@dimen/sp_13"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_group_name"
        app:layout_constraintBottom_toBottomOf="@id/tv_group_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_group_name" />

    <View
        android:id="@+id/line_apr_func_group_name"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:layout_marginStart="@dimen/dp_15"
        android:background="@color/colorffeeeeee"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_group_name" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/gv_apr_grid_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dp_88"
        android:paddingStart="@dimen/dp_23"
        android:paddingEnd="@dimen/dp_23"
        android:paddingBottom="@dimen/dp_10"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line_apr_func_group_name" />
</androidx.constraintlayout.widget.ConstraintLayout>