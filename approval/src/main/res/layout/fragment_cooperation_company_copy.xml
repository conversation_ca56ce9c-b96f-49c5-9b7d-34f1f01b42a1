<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <EditText
        android:id="@+id/searchEdit"
        android:layout_width="match_parent"
        android:layout_height="29dp"
        android:layout_marginStart="14dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="14dp"
        android:background="@drawable/rounded_membersearch_edit_bg"
        android:drawableStart="@drawable/iconsmallsearch"
        android:drawablePadding="5dp"
        android:hint="搜索"
        android:paddingStart="14dp"
        android:paddingEnd="14dp"
        android:textColor="#000000"
        android:textColorHighlight="#999999"
        android:textSize="@dimen/textsize_12"
        app:layout_constraintTop_toTopOf="parent" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/srl_refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingTop="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/searchEdit"
        app:layout_constraintVertical_weight="1">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white" />
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <!--公共无数据页面显示-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:ev_content="暂无审批抄送"
        app:ev_icon="@drawable/ic_empty_approval" />
</androidx.constraintlayout.widget.ConstraintLayout>