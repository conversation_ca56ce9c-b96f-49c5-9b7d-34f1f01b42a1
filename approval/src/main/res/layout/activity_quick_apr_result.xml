<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".quick.QuickAprResultActivity">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_42"
        android:gravity="center"
        android:paddingStart="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_15"
        android:text="快速审批"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_17"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/line_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_grey"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <TextView
        android:id="@+id/tv_result"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="76dp"
        android:text="恭喜你已完成所有审批！"
        android:textColor="@color/colorFF333333"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line_top" />

    <TextView
        android:id="@+id/tv_result_done"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="67dp"
        android:text="45个"
        android:textColor="@color/main_blue"
        android:textSize="18sp"
        app:layout_constraintEnd_toStartOf="@id/tv_result_no_done"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_result" />

    <TextView
        android:id="@+id/tv_result_no_done"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="67dp"
        android:text="45个"
        android:textColor="@color/colorFF333333"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_result_done"
        app:layout_constraintTop_toBottomOf="@id/tv_result" />

    <TextView
        android:id="@+id/tv_result_done_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:text="本次共执行审批"
        android:textColor="#7E7E7E"
        android:textSize="12sp"
        app:layout_constraintEnd_toStartOf="@id/tv_result_no_done"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_result_done" />

    <TextView
        android:id="@+id/tv_result_no_done_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:text="剩余未执行审批"
        android:textColor="#7E7E7E"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_result_done"
        app:layout_constraintTop_toBottomOf="@id/tv_result_no_done" />

    <TextView
        android:id="@+id/tv_result_over"
        android:layout_width="match_parent"
        android:gravity="center"
        android:layout_height="@dimen/dp_40"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginTop="34dp"
        android:layout_marginEnd="@dimen/dp_48"
        android:background="@drawable/bg_blue_corner"
        android:text="关闭"
        android:textColor="@color/white"
        android:textSize="15sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_result_done_title" />

</androidx.constraintlayout.widget.ConstraintLayout>