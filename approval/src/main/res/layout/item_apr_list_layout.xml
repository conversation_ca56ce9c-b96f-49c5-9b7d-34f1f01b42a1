<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_15"
    android:paddingBottom="@dimen/dp_8">

    <View
        android:id="@+id/line_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_grey"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_apr_icon"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_34"
        android:layout_marginTop="@dimen/dp_14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line_top" />

    <View
        android:id="@+id/iv_red_dot"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:background="@drawable/shape_round_red_dot"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/iv_apr_icon"
        app:layout_constraintEnd_toEndOf="@id/iv_apr_icon"
        app:layout_constraintStart_toEndOf="@id/iv_apr_icon"
        app:layout_constraintTop_toTopOf="@id/iv_apr_icon"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_apr_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_12"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_14"
        app:layout_constraintEnd_toStartOf="@id/tv_apr_time"
        app:layout_constraintStart_toEndOf="@id/iv_apr_icon"
        app:layout_constraintTop_toBottomOf="@id/line_top"
        app:layout_goneMarginEnd="@dimen/dp_15"
        tools:text="高小花的用印申请" />

    <TextView
        android:id="@+id/tv_apr_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_15"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="@id/tv_apr_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_apr_title"
        app:layout_constraintTop_toTopOf="@id/tv_apr_title"
        tools:text="2019/07/06  12:00" />

    <TextView
        android:id="@+id/tv_pro_one"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:layout_marginEnd="@dimen/dp_15"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_apr_title"
        app:layout_constraintTop_toBottomOf="@id/tv_apr_title"
        tools:text="用印事由： 掌握临沂合同章\n用印部门： 财务部"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_pro_two"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:layout_marginEnd="@dimen/dp_15"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_apr_title"
        app:layout_constraintTop_toBottomOf="@id/tv_pro_one"
        tools:text="用印事由： 掌握临沂合同章\n用印部门： 财务部"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_pro_three"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:layout_marginEnd="@dimen/dp_15"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_apr_title"
        app:layout_constraintTop_toBottomOf="@id/tv_pro_two"
        tools:text="用印事由： 掌握临沂合同章\n用印部门： 财务部"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_apr_node"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:layout_marginEnd="@dimen/dp_15"
        android:textSize="@dimen/textsize_13"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_apr_title"
        app:layout_constraintTop_toBottomOf="@id/tv_pro_three"
        app:layout_goneMarginTop="@dimen/dp_10"
        tools:text="当前审批人+审批状态，颜色也在此上"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_apr_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_15"
        android:layout_marginBottom="@dimen/dp_6"
        android:textColor="#85C230"
        android:textSize="@dimen/textsize_13"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_apr_time"
        app:layout_constraintTop_toTopOf="@id/tv_apr_time"
        tools:visibility="gone" />


</androidx.constraintlayout.widget.ConstraintLayout>