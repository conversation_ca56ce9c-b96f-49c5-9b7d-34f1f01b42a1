<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <!--android:background="#80000000"-->

    <LinearLayout
        android:id="@+id/one_select"
        android:layout_width="@dimen/dp_144"
        android:layout_height="@dimen/dp_76"
        android:layout_gravity="end|top"
        android:background="@drawable/bg_pop_apr_more"
        android:orientation="vertical"
        android:paddingStart="@dimen/dp_10"
        android:paddingTop="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_15">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_export"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_10">

            <ImageView
                android:id="@+id/iv_recall_one"
                android:layout_width="@dimen/dp_21"
                android:layout_height="@dimen/dp_21"
                android:src="@drawable/icon_recall"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/apr_recall_one"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/apr_recall_one"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="撤回"
                android:textColor="@color/color808080"
                android:textSize="@dimen/sp_14"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_recall_one" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/two_select"
        android:layout_width="@dimen/dp_144"
        android:layout_height="wrap_content"
        android:layout_gravity="end|top"
        android:background="@drawable/bg_pop_apr_more"
        android:minHeight="@dimen/dp_50"
        android:orientation="vertical"
        android:paddingStart="@dimen/dp_10"
        android:paddingTop="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_15"
        android:visibility="gone">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_export_two"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_10">

            <ImageView
                android:id="@+id/iv_export_two"
                android:layout_width="@dimen/dp_21"
                android:layout_height="@dimen/dp_21"
                android:src="@drawable/icon_export"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/apr_export_two"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintHorizontal_chainStyle="spread"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/apr_export_two"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="导出"
                android:textColor="@color/color808080"
                android:textSize="@dimen/sp_14"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@id/iv_export_two"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/line_middle"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_2"
            android:layout_marginStart="@dimen/dp_5"
            android:layout_marginEnd="@dimen/dp_5"
            android:background="@color/line_DCDCDC"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_recall"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <ImageView
                android:id="@+id/iv_recall"
                android:layout_width="@dimen/dp_21"
                android:layout_height="@dimen/dp_21"
                android:src="@drawable/icon_recall"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/apr_recall"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/apr_recall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="撤回"
                android:textColor="@color/color808080"
                android:textSize="@dimen/sp_14"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_recall" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>
</LinearLayout>
