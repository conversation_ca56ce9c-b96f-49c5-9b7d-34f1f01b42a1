<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blue"
    tools:context=".AprHelpActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">


        <ImageView
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_350"
            app:layout_constraintEnd_toEndOf="parent"
            android:src="@drawable/iv_help_top"
            android:adjustViewBounds="true"
            android:scaleType="fitXY"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_330"
            android:background="@drawable/bg_corner_white_20"
            android:paddingTop="@dimen/dp_18"
            android:paddingBottom="@dimen/dp_38"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="流程示意"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/line_one"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_0_5"
                android:layout_marginTop="@dimen/dp_18"
                android:background="@color/c_E5E5E5"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_title" />

            <TextView
                android:id="@+id/tv_apr_flow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_14"
                android:layout_marginTop="@dimen/dp_24"
                android:text="审批流程"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/sp_12"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/line_one" />

            <ImageView
                android:id="@+id/iv_apr_flow"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_100"
                android:layout_marginStart="@dimen/dp_30"
                android:layout_marginTop="@dimen/dp_24"
                android:layout_marginEnd="@dimen/dp_30"
                app:layout_constraintEnd_toEndOf="parent"
                android:src="@drawable/iv_apr_flow"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_apr_flow" />

            <TextView
                android:id="@+id/tv_apr_invite"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_14"
                android:layout_marginTop="@dimen/dp_36"
                android:text="审批中需邀请"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/sp_12"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_apr_flow" />

            <ImageView
                android:id="@+id/iv_apr_invite"
                android:layout_width="@dimen/dp_192"
                android:layout_height="@dimen/dp_20"
                android:layout_marginStart="@dimen/dp_30"
                android:layout_marginTop="@dimen/dp_24"
                android:src="@drawable/iv_apr_invite"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_apr_invite" />

            <View
                android:id="@+id/line_two"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_0_5"
                android:layout_marginTop="@dimen/dp_22"
                android:background="@color/c_E5E5E5"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_apr_invite" />


            <TextView
                android:id="@+id/tv_apr_flow_change"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_14"
                android:layout_marginTop="@dimen/dp_24"
                android:text="当C选择E、F两人并设为“在我之前审批”时，审批流程变更为"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/sp_12"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/line_two" />

            <ImageView
                android:id="@+id/iv_apr_flow_change"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_100"
                android:layout_marginStart="@dimen/dp_15"
                android:src="@drawable/iv_apr_flow_change"
                android:layout_marginTop="@dimen/dp_24"
                android:layout_marginEnd="@dimen/dp_30"
                app:layout_constraintTop_toBottomOf="@id/tv_apr_flow_change" />

            <View
                android:id="@+id/line_three"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_0_5"
                android:layout_marginTop="@dimen/dp_22"
                android:background="@color/c_E5E5E5"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_apr_flow_change" />


            <TextView
                android:id="@+id/tv_apr_flow_change_result"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_14"
                android:layout_marginTop="@dimen/dp_24"
                android:text="当C选择E、F两人并设为“在我之后审批”时，审批流程变更为"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/sp_12"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/line_three" />

            <ImageView
                android:id="@+id/iv_apr_flow_change_result"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_100"
                android:layout_marginStart="@dimen/dp_15"
                android:src="@drawable/iv_apr_flow_change_result"
                android:layout_marginTop="@dimen/dp_24"
                android:layout_marginEnd="@dimen/dp_30"
                app:layout_constraintTop_toBottomOf="@id/tv_apr_flow_change_result" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>