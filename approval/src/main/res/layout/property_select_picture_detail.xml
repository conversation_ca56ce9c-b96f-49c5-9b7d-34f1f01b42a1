<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:padding="@dimen/dp_15"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <TextView
        android:id="@+id/tv_required"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="*"
        android:textColor="@color/red"
        android:textSize="@dimen/sp_14"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/property_title_detail"
        android:layout_marginStart="@dimen/dp_4"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="图片描述"
        app:layout_constraintEnd_toStartOf="@id/tv_content"
        app:layout_constraintStart_toEndOf="@id/tv_required"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginStart="@dimen/dp_0" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="wrap_content"
        android:layout_height="16dp"
        android:layout_marginStart="@dimen/dp_5"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="(请选择图片)"
        android:textColor="@color/color808080"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintStart_toEndOf="@id/tv_title"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

    <ImageView
        android:id="@+id/iv_add_pic"
        android:layout_width="@dimen/dp_46"
        android:layout_height="@dimen/dp_46"
        android:layout_marginTop="@dimen/dp_14"
        android:src="@drawable/icon_add_pic"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_14"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />
</androidx.constraintlayout.widget.ConstraintLayout>