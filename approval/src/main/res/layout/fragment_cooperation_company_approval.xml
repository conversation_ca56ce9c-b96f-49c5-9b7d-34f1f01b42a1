<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/background">

    <TextView
        android:id="@+id/fastApproval"
        android:layout_width="match_parent"
        android:layout_height="47dp"
        android:background="@color/white"
        android:gravity="center"
        android:text="快速审批"
        android:textColor="#ff2479ed"
        android:textSize="@dimen/textsize_15"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/srl_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white" />
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <!--默认空白页面-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:visibility="gone"
        app:ev_content="暂无待处理的审批"
        app:ev_icon="@drawable/ic_empty_approval" />
</androidx.constraintlayout.widget.ConstraintLayout>