<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".AprApproveAddActivity">

    <TextView
        android:id="@+id/tv_apr_add_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:padding="@dimen/dp_15"
        android:text="选择要加审的方式"
        android:textColor="@color/c_ff888888"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/line_one"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_5"
        android:background="@color/c_E5E5E5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_apr_add_title" />

    <TextView
        android:id="@+id/tv_add_before"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:includeFontPadding="false"
        android:padding="@dimen/dp_14"
        android:text="在我之前"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_14"
        app:layout_constraintTop_toBottomOf="@id/line_one" />

    <ImageView
        android:id="@+id/iv_add_before"
        android:layout_width="@dimen/dp_14"
        android:layout_height="@dimen/dp_14"
        android:layout_marginEnd="@dimen/dp_14"
        android:visibility="gone"
        android:src="@drawable/iv_select_right"
        app:layout_constraintBottom_toBottomOf="@id/tv_add_before"
        app:layout_constraintEnd_toEndOf="@id/tv_add_before"
        app:layout_constraintTop_toTopOf="@id/tv_add_before" />

    <View
        android:id="@+id/line_two"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_5"
        android:background="@color/c_E5E5E5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_add_before" />

    <TextView
        android:id="@+id/tv_add_after"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:includeFontPadding="false"
        android:padding="@dimen/dp_14"
        android:text="在我之后"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_14"
        app:layout_constraintTop_toBottomOf="@id/line_two" />

    <ImageView
        android:id="@+id/iv_add_after"
        android:layout_width="@dimen/dp_14"
        android:layout_height="@dimen/dp_14"
        android:layout_marginEnd="@dimen/dp_14"
        android:src="@drawable/iv_select_right"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tv_add_after"
        app:layout_constraintEnd_toEndOf="@id/tv_add_after"
        app:layout_constraintTop_toTopOf="@id/tv_add_after" />

    <View
        android:id="@+id/line_three"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_5"
        android:background="@color/c_E5E5E5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_add_after" />

    <TextView
        android:id="@+id/tv_apr_add_help"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:padding="@dimen/dp_15"
        android:text="点击查看加审方式区别>>"
        android:textColor="#1885FF"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line_three" />

    <View
        android:id="@+id/line_four"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_5"
        android:background="@color/c_E5E5E5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_apr_add_help" />

    <LinearLayout
        android:id="@+id/ll_apr_add_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_37"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_15"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line_four">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:text="加审人员"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_7"
            android:includeFontPadding="false"
            android:text="(点击选择加审的人员并调整顺序)"
            android:textColor="@color/color999999"
            android:textSize="@dimen/sp_12" />
    </LinearLayout>

    <include
        android:id="@+id/v_add_member"
        layout="@layout/item_apr_add_member_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_apr_add_title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_apr_add_title" />

</androidx.constraintlayout.widget.ConstraintLayout>