<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_property_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingStart="@dimen/dp_15"
        android:paddingTop="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_15"
        android:paddingBottom="@dimen/dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_required"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:text="*"
            android:textColor="@color/red"
            android:textSize="@dimen/sp_14"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tv_title"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/property_title"
            android:layout_marginStart="@dimen/dp_4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_required"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginStart="@dimen/dp_0"
            android:maxEms="7"
            tools:text="用印事由" />

        <!--todo 输入框 多行文本时左对齐，其他输入右对齐,数字类型要控制好输入键盘-->

        <EditText
            android:id="@+id/et_input"
            style="@style/property_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:background="@color/white"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLength="50"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_required"
            app:layout_constraintTop_toTopOf="parent"
            tools:hint="请填写用印标题，不超过10个字" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/line_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:layout_marginStart="@dimen/dp_15"
        android:background="@color/line_grey"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_property_content" />
</androidx.constraintlayout.widget.ConstraintLayout>