<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

<!--    <com.joinutech.approval.custom.lib.RoundedImageView-->
    <com.joinutech.ddbeslibrary.widget.roundimage.RoundedImageView
        android:id="@+id/iv_pic"
        android:layout_width="@dimen/dp_46"
        android:layout_height="@dimen/dp_46"
        android:layout_marginTop="@dimen/dp_7"
        android:layout_marginEnd="@dimen/dp_7"
        android:paddingStart="@dimen/dp_1"
        android:paddingBottom="@dimen/dp_1"
        android:paddingEnd="0dp"
        android:src="@drawable/icon_add_pic"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:riv_corner_radius="@dimen/dp_4" />

    <com.joinutech.ddbeslibrary.widget.roundimage.RoundedImageView
        android:id="@+id/iv_progress"
        android:layout_width="@dimen/dp_46"
        android:layout_height="@dimen/dp_46"
        android:layout_marginTop="@dimen/dp_7"
        android:layout_marginEnd="@dimen/dp_7"
        android:paddingStart="@dimen/dp_1"
        android:visibility="gone"
        android:paddingBottom="@dimen/dp_1"
        android:background="#590082F2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:riv_corner_radius="@dimen/dp_4" />

    <TextView
        android:id="@+id/tv_progress"
        app:layout_constraintTop_toTopOf="@id/iv_progress"
        app:layout_constraintStart_toStartOf="@id/iv_progress"
        app:layout_constraintEnd_toEndOf="@id/iv_progress"
        app:layout_constraintBottom_toBottomOf="@id/iv_progress"
        android:text="15%"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <ImageView
        android:id="@+id/iv_del"
        android:layout_width="@dimen/dp_27"
        android:layout_height="@dimen/dp_27"
        android:paddingStart="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        android:src="@drawable/icon_join_cancel"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--todo 图片选择 人员选择可复用-->
    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="安然-123"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/iv_pic"
        app:layout_constraintStart_toStartOf="@id/iv_pic"
        app:layout_constraintTop_toBottomOf="@id/iv_pic" />

</androidx.constraintlayout.widget.ConstraintLayout>