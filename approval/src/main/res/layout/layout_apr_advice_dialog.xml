<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingLeft="@dimen/dp_36"
    android:paddingRight="@dimen/dp_36">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_310"
        android:background="@drawable/shape_white_rounded_dialog">

        <EditText
            android:id="@+id/et_content"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_158"
            android:layout_marginStart="@dimen/dp_17"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_17"
            android:background="@color/input_background"
            android:maxLength="100"
            android:orientation="vertical"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_goneMarginTop="@dimen/dp_30" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp_28"
            android:gravity="center"
            android:text="审批意见"
            android:textColor="@color/text_black"
            android:textSize="@dimen/sp_17"
            android:textStyle="bold"
            android:visibility="visible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/tv_advice_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_12"
            android:gravity="center"
            android:text="（0-100字，选填）"
            android:textColor="@color/color999999"
            android:textSize="@dimen/sp_14"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/et_content" />

        <TextView
            android:id="@+id/cancel"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_40"
            android:layout_marginTop="@dimen/dp_17"
            android:layout_weight="1"
            android:gravity="center"
            android:text="取消"
            android:textColor="@color/color999999"
            android:textSize="@dimen/sp_14"
            app:layout_constraintEnd_toStartOf="@id/confirm"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_advice_tip"
            app:layout_goneMarginTop="@dimen/dp_10" />

        <TextView
            android:id="@+id/confirm"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_40"
            android:layout_marginStart="20dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="完成"
            android:textColor="@color/c_006BFC"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="@id/cancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@id/cancel"
            app:layout_constraintTop_toTopOf="@id/cancel" />

        <View
            android:id="@+id/line_ho"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_2"
            android:layout_marginTop="@dimen/dp_17"
            android:background="@color/line_grey"
            app:layout_constraintTop_toBottomOf="@id/tv_advice_tip" />

        <View
            android:id="@+id/line_v"
            android:layout_width="@dimen/dp_0_2"
            android:layout_height="0dp"
            android:background="@color/line_grey"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line_ho" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>