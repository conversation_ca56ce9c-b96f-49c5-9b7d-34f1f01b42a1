<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_property_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp_15"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_required"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="*"
            android:textColor="@color/red"
            android:textSize="@dimen/sp_14"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_title"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/property_title"
            android:layout_marginStart="@dimen/dp_4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_required"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginStart="@dimen/dp_0" />


        <TextView
            android:id="@+id/tv_content"
            style="@style/property_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_5"
            android:ellipsize="end"
            android:gravity="end"
            android:hint="未获取到位置信息"
            android:maxLines="1"
            android:textColor="@color/c_0082F2"
            android:visibility="visible"
            app:layout_constraintBottom_toTopOf="@id/tv_sub_content"
            app:layout_constraintEnd_toStartOf="@id/iv_arrow"
            app:layout_constraintStart_toEndOf="@id/tv_required"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginBottom="@dimen/dp_0"
            app:layout_goneMarginEnd="@dimen/dp_0" />

        <TextView
            android:id="@+id/tv_sub_content"
            style="@style/property_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginEnd="@dimen/dp_5"
            android:ellipsize="end"
            android:gravity="end"
            android:hint="未获取到位置地址"
            android:maxLines="1"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_arrow"
            app:layout_constraintStart_toEndOf="@id/tv_title"
            app:layout_constraintTop_toBottomOf="@id/tv_content"
            app:layout_goneMarginEnd="@dimen/dp_0"
            app:layout_goneMarginTop="@dimen/dp_0" />

        <ImageView
            android:id="@+id/iv_arrow"
            android:layout_width="@dimen/dp_10"
            android:layout_height="@dimen/dp_12"
            android:src="@drawable/icon_location"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/line_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_grey"
        android:layout_marginStart="@dimen/dp_15"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_property_content" />
</androidx.constraintlayout.widget.ConstraintLayout>