<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_48"
    android:background="@color/white"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_11"
    android:paddingTop="@dimen/dp_10"
    android:paddingEnd="@dimen/dp_12"
    android:paddingBottom="@dimen/dp_10">

    <EditText
        android:id="@+id/et_search_input"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_28"
        android:layout_marginEnd="8dp"
        android:background="@drawable/bg_gray_corner_14"
        android:hint="搜索"
        android:imeOptions="actionSearch"
        android:maxLines="1"
        android:paddingStart="@dimen/dp_26"
        android:paddingEnd="@dimen/dp_26"
        android:singleLine="true"
        android:textColor="@color/text_black"
        android:textColorHint="@color/colorFFAAAAAA"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_cancel"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="@dimen/dp_0" />

    <ImageView
        android:layout_width="@dimen/dp_17"
        android:layout_height="@dimen/dp_17"
        android:layout_marginStart="@dimen/dp_8"
        android:src="@drawable/icon_search"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_input_clear"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_5"
        android:src="@drawable/del_img"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/et_search_input"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:text="取消"
        android:textSize="@dimen/sp_15"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>