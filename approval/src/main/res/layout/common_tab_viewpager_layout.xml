<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/tab_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_35"
        android:background="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tab_one"
            style="@style/apr_tab_text"
            android:layout_height="match_parent"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tab_two"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="自定义模板" />

        <TextView
            android:id="@+id/tv_one_count"
            style="@style/apr_tab_dot"
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_1"
            android:text="88"
            app:layout_constraintStart_toEndOf="@id/tab_one"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tab_two"
            style="@style/apr_tab_text"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="自定义模板"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tab_three"
            app:layout_constraintStart_toEndOf="@id/tab_one"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_two_count"
            style="@style/apr_tab_dot"
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_1"
            android:text="88"
            app:layout_constraintStart_toEndOf="@id/tab_two"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tab_three"
            style="@style/apr_tab_text"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="自定义模板"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tab_two"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_three_count"
            style="@style/apr_tab_dot"
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_1"
            android:text="88"
            app:layout_constraintStart_toEndOf="@id/tab_three"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/indicator_one"
            style="@style/apr_tab_indicator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/tab_one"
            app:layout_constraintStart_toStartOf="@id/tab_one" />

        <View
            android:id="@+id/indicator_two"
            style="@style/apr_tab_indicator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/tab_two"
            app:layout_constraintStart_toStartOf="@id/tab_two" />

        <View
            android:id="@+id/indicator_three"
            style="@style/apr_tab_indicator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/tab_three"
            app:layout_constraintStart_toStartOf="@id/tab_three" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/line_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_grey"
        app:layout_constraintTop_toBottomOf="@id/tab_title" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/vp_page_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line_top" />

</androidx.constraintlayout.widget.ConstraintLayout>
