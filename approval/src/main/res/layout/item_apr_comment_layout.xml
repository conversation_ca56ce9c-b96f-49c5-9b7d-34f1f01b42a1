<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/dp_14"
    android:paddingBottom="@dimen/dp_5">

    <TextView
        android:id="@+id/tv_comment_user_name"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_12"
        android:ellipsize="end"
        android:maxLines="1"
        android:layout_marginEnd="@dimen/dp_5"
        android:textColor="@color/c_3F8BEE"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="@id/iv_comment_user_icon"
        app:layout_constraintEnd_toStartOf="@id/tv_comment_time"
        app:layout_constraintStart_toEndOf="@id/iv_comment_user_icon"
        app:layout_constraintTop_toTopOf="@id/iv_comment_user_icon" />

    <TextView
        android:id="@+id/tv_comment_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_5"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        android:visibility="visible"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_comment_user_name"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tv_comment_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_15"
        android:textColor="@color/colorFF323232"
        android:textSize="@dimen/sp_14"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/iv_comment_user_icon"
        app:layout_constraintTop_toBottomOf="@id/iv_comment_user_icon" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_list"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_13"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/iv_comment_user_icon"
        app:layout_constraintTop_toBottomOf="@id/tv_comment_content"
        app:layout_goneMarginTop="@dimen/dp_15" />

    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/iv_comment_user_icon"
        android:layout_width="@dimen/dp_19"
        android:layout_height="@dimen/dp_19"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>