<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_15"
    android:paddingTop="@dimen/dp_16"
    android:paddingEnd="@dimen/dp_15"
    android:paddingBottom="@dimen/dp_17">


    <ImageView
        android:id="@+id/iv_apr_icon"
        android:layout_width="@dimen/dp_46"
        android:layout_height="@dimen/dp_46"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_apr_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_65"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/colorFF323232"
        android:textSize="@dimen/sp_15"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_apr_icon"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_apr_time"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_65"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color808080"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_apr_title"
        app:layout_constraintTop_toBottomOf="@id/tv_apr_title" />


    <ImageView
        android:id="@+id/iv_apr_result"
        android:layout_width="@dimen/dp_57"
        android:layout_height="@dimen/dp_57"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_apr_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#85C230"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>