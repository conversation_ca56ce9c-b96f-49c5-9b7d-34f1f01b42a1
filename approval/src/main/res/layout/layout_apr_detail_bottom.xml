<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <LinearLayout
        android:id="@+id/ll_comment"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_47"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/ll_add_apr"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:layout_width="@dimen/dp_22"
            android:layout_height="@dimen/dp_22"
            android:src="@drawable/icon_comment" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_1"
            android:text="评论"
            android:textColor="@color/c_b3b3b3" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_add_apr"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_47"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@id/ll_comment"
        app:layout_constraintEnd_toStartOf="@id/ll_refuse"
        app:layout_constraintStart_toEndOf="@id/ll_comment"
        app:layout_constraintTop_toTopOf="@id/ll_comment">

        <ImageView
            android:layout_width="@dimen/dp_22"
            android:layout_height="@dimen/dp_22"
            android:src="@drawable/icon_add_apr" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_1"
            android:text="评论"
            android:textColor="@color/c_b3b3b3" />
    </LinearLayout>

    <TextView
        android:id="@+id/ll_refuse"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_47"
        android:background="#ECECEC"
        android:gravity="center"
        android:text="拒绝"
        android:textColor="#666666"
        app:layout_constraintBottom_toBottomOf="@id/ll_comment"
        app:layout_constraintEnd_toStartOf="@id/ll_agree"
        app:layout_constraintStart_toEndOf="@id/ll_add_apr"
        app:layout_constraintTop_toTopOf="@id/ll_comment" />

    <TextView
        android:id="@+id/ll_agree"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_47"
        android:background="@color/c_3F8BEE"
        android:gravity="center"
        android:text="同意"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/ll_comment"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ll_refuse"
        app:layout_constraintTop_toTopOf="@id/ll_comment" />

</merge>