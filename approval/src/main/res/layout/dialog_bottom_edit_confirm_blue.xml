<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_rounded_dialog">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            android:text="提示"
            android:textColor="@color/color393939"
            android:textSize="@dimen/textsize_14"
            android:visibility="visible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/cancel"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="取消"
            android:textColor="@color/color999999"
            android:textSize="@dimen/textsize_15"
            app:layout_constraintEnd_toStartOf="@id/confirm"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line_ho"
            app:layout_goneMarginTop="@dimen/dp_10" />

        <TextView
            android:id="@+id/confirm"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:background="@color/main_blue"
            android:gravity="center"
            android:text="确定"
            android:textColor="@color/white"
            android:textSize="@dimen/textsize_15"
            app:layout_constraintBottom_toBottomOf="@id/cancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@id/cancel"
            app:layout_constraintTop_toTopOf="@id/cancel" />

        <View
            android:id="@+id/line_v"
            android:layout_width="@dimen/dp_0_2"
            android:layout_height="0dp"
            android:background="@color/line_grey"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line_ho" />

        <View
            android:id="@+id/line_top"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_2"
            android:background="@color/line_grey"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <View
            android:id="@+id/line_ho"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_2"
            android:layout_marginTop="@dimen/dp_20"
            android:background="@color/line_grey"
            app:layout_constraintTop_toBottomOf="@id/dialog_content"
            app:layout_goneMarginTop="@dimen/dp_10" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/dialog_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintTop_toBottomOf="@id/line_top">

            <EditText
                android:id="@+id/et_content"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_158"
                android:layout_marginStart="@dimen/dp_17"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginEnd="@dimen/dp_17"
                android:background="@color/input_background"
                android:gravity="top|start"
                android:hint="在此填写审批意见..."
                android:maxLength="100"
                android:orientation="vertical"
                android:padding="@dimen/dp_8"
                android:textColor="@color/colorFF333333"
                android:textColorHint="@color/color666666"
                android:textSize="@dimen/sp_14"
                android:visibility="gone"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginTop="@dimen/dp_30"
                tools:visibility="visible" />


            <TextView
                android:id="@+id/tv_advice_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_12"
                android:gravity="center"
                android:text="（0-100字，选填）"
                android:textColor="@color/color999999"
                android:textSize="@dimen/sp_14"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/et_content"
                tools:visibility="visible" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_list"
                android:layout_width="match_parent"
                android:layout_height="300dp"
                android:layout_marginTop="@dimen/dp_10"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="gone" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>