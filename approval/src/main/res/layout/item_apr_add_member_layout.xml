<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_67"
    android:background="@color/white"
    android:orientation="vertical">

    <View
        android:id="@+id/line_five"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_5"
        android:background="@color/c_E5E5E5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_38"
        android:layout_height="@dimen/dp_38"
        android:layout_marginStart="@dimen/dp_14"
        android:src="@drawable/iv_add_apr"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line_five" />

    <ImageView
        android:id="@+id/iv_drag"
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_30"
        android:layout_marginEnd="@dimen/dp_8"
        android:padding="@dimen/dp_6"
        android:src="@drawable/icon_drag"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line_five" />

    <ImageView
        android:id="@+id/iv_del"
        android:layout_width="@dimen/dp_25"
        android:layout_height="@dimen/dp_25"
        android:layout_marginEnd="@dimen/dp_8"
        android:padding="@dimen/dp_4"
        android:src="@drawable/iv_del"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_drag"
        app:layout_constraintTop_toBottomOf="@id/line_five" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_10"
        android:background="@color/white"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:singleLine="true"
        android:text="添加加审人员"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_del"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toBottomOf="@id/line_five" />

</androidx.constraintlayout.widget.ConstraintLayout>