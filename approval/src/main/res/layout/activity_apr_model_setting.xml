<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".temp.AprModelSettingActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_model_manage"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_46"
        android:layout_marginTop="@dimen/dp_15"
        android:background="@color/white"
        android:paddingStart="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_15"
        app:layout_constraintTop_toTopOf="parent">


        <ImageView
            android:id="@+id/iv_arrow1"
            android:layout_width="@dimen/dp_6"
            android:layout_height="@dimen/dp_12"
            android:src="@drawable/icon_right_arrow"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_title1"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="审批模板管理"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_arrow1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_model_set"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_46"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@color/white"
        android:paddingStart="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_15"
        app:layout_constraintTop_toBottomOf="@id/cl_model_manage">


        <ImageView
            android:id="@+id/iv_arrow2"
            android:layout_width="@dimen/dp_6"
            android:layout_height="@dimen/dp_12"
            android:src="@drawable/icon_right_arrow"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_title2"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="个性化设置"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_arrow2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>