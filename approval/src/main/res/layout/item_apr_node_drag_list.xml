<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_property_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="@dimen/dp_15"
        android:paddingStart="@dimen/dp_20"
        android:paddingEnd="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_15"
        app:layout_constraintBottom_toTopOf="@id/line_bottom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!--<ImageView-->
        <com.joinutech.ddbeslibrary.widget.CircleImageView
            android:id="@+id/iv_workmate_icon"
            android:layout_width="@dimen/dp_43"
            android:layout_height="@dimen/dp_43"
            android:src="@drawable/ic_add_copy"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_10"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="添加自选审批人"
            android:textColor="@color/colorFF323232"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_del"
            app:layout_constraintStart_toEndOf="@id/iv_workmate_icon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_del"
            android:layout_width="@dimen/dp_25"
            android:layout_height="@dimen/dp_25"
            android:layout_marginEnd="@dimen/dp_8"
            android:padding="@dimen/dp_4"
            android:src="@drawable/iv_del"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_drag"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_drag"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_30"
            android:padding="@dimen/dp_6"
            android:src="@drawable/icon_drag"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/line_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:layout_marginStart="@dimen/dp_15"
        android:background="@color/line_grey"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_property_content" />
</androidx.constraintlayout.widget.ConstraintLayout>