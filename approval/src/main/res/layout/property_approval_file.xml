<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dp_10"
    android:background="@color/white"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <!--不支持文件附件时的显示提示-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/fileUnSupportLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="15dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/fileTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginTop="16dp"
            android:paddingBottom="17dp"
            android:text="文件"
            android:textColor="@color/color999999"
            android:textSize="@dimen/textsize_15"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp"
            android:layout_marginEnd="15dp"
            android:text="暂不支持"
            android:textColor="@color/color999999"
            android:textSize="@dimen/textsize_13"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="29dp"
            android:background="#FAFAFC"
            android:gravity="center_vertical"
            android:paddingStart="14dp"
            android:paddingEnd="0dp"
            android:text="如有上传文件类的需要，请登录oa.ddbes.com使用网页版"
            android:textColor="@color/color999999"
            android:textSize="@dimen/textsize_12"
            app:layout_constraintTop_toBottomOf="@id/fileTitle" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--支持文件附件-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/file_support_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/dp_15"
        android:paddingTop="10dp"
        android:visibility="visible"
        android:paddingEnd="@dimen/dp_15"
        android:paddingBottom="10dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/file_top_title"
            style="@style/property_title"
            android:maxLines="1"
            android:layout_marginStart="@dimen/dp_4"
            android:text="附件"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginStart="@dimen/dp_0" />

        <ImageView
            android:id="@+id/file_add_image_iv"
            android:layout_width="28dp"
            android:paddingStart="@dimen/dp_4"
            android:paddingEnd="@dimen/dp_1"
            android:paddingTop="@dimen/dp_4"
            android:paddingBottom="@dimen/dp_4"
            android:layout_height="28dp"
            android:src="@mipmap/ic_file_image"
            app:layout_constraintBottom_toBottomOf="@+id/file_top_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/file_top_title" />

        <View
            android:id="@+id/file_line_view"
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:background="@color/line_gray_one"
            app:layout_constraintEnd_toEndOf="@+id/file_add_image_iv"
            app:layout_constraintStart_toEndOf="@+id/file_top_title"
            app:layout_constraintTop_toBottomOf="@+id/file_top_title" />

        <!--附件列表-->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/file_list_recycler"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:minHeight="@dimen/dp_120"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/file_top_title"
            app:layout_constraintTop_toBottomOf="@id/file_line_view"
            tools:visibility="visible" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>