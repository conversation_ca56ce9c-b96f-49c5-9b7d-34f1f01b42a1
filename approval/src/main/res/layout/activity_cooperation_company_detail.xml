<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/abl_top_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <include
                android:id="@+id/include_cooperation_top"
                layout="@layout/include_cooperation_company_top" />

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/tb_top_bar"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:background="@color/white"
                app:contentInsetEnd="0dp"
                app:contentInsetLeft="0dp"
                app:contentInsetRight="0dp"
                app:contentInsetStart="0dp"
                app:layout_collapseMode="pin">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ImageView
                        android:id="@+id/iv_back_icon"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="10dp"
                        android:scaleType="center"
                        android:src="@drawable/back_grey"
                        tools:ignore="ContentDescription" />

                    <TextView
                        android:id="@+id/tv_cooper_title"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:gravity="center"
                        android:text="标题"
                        android:textColor="@android:color/black"
                        android:textSize="17sp"
                        tools:ignore="HardcodedText" />

                    <ImageView
                        android:id="@+id/iv_more_icon"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="10dp"
                        android:scaleType="center"
                        android:src="@drawable/icon_cooperation_right_more"
                        tools:ignore="ContentDescription" />
                </RelativeLayout>
            </androidx.appcompat.widget.Toolbar>
        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:id="@+id/nsv_bottom_layout"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">
        <include
            android:id="@+id/include_cooperation_bottom"
            layout="@layout/include_cooperation_bottom"/>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>