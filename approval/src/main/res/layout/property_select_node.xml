<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_hint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp_15"
        android:paddingTop="@dimen/dp_6"
        android:paddingEnd="@dimen/dp_15"
        android:paddingBottom="@dimen/dp_6"
        android:text="此审批模板为自由流程模式，请自选审批人"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_10"
        app:layout_constraintBottom_toTopOf="@id/line_top"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/line_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_grey"
        app:layout_constraintBottom_toTopOf="@id/ll_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_hint" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line_top"
        app:layout_goneMarginTop="@dimen/dp_10">

        <TextView
            android:id="@+id/tv_title"
            style="@style/property_title"
            android:layout_width="0dp"
            android:paddingStart="@dimen/dp_20"
            android:paddingTop="@dimen/dp_12"
            android:paddingBottom="@dimen/dp_12"
            android:text="审批流程"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/line_middle"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_2"
            android:layout_marginStart="@dimen/dp_15"
            android:background="@color/line_grey"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_add_node"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_20"
        android:paddingTop="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_15"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_title">

        <ImageView
            android:id="@+id/iv_workmate_icon"
            android:layout_width="@dimen/dp_43"
            android:layout_height="@dimen/dp_43"
            android:src="@drawable/ic_add_2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_del"
            android:layout_width="@dimen/dp_25"
            android:layout_height="@dimen/dp_25"
            android:layout_marginEnd="@dimen/dp_8"
            android:padding="@dimen/dp_4"
            android:src="@drawable/iv_del"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_drag"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_drag"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_30"
            android:padding="@dimen/dp_6"
            android:src="@drawable/icon_drag"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_10"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="添加自选审批人"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_del"
            app:layout_constraintStart_toEndOf="@id/iv_workmate_icon"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
       android:paddingStart="@dimen/dp_20"
        android:paddingEnd="@dimen/dp_2"
        android:paddingBottom="@dimen/dp_10"
        android:visibility="gone"
        android:minHeight="@dimen/dp_38"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_title"
        app:layout_goneMarginTop="@dimen/dp_10"
        tools:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>