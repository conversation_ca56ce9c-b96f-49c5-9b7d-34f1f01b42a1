<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginTop="10dp"
    android:background="@color/colorF6F6F6">
    <ImageView
        android:layout_width="33dp"
        android:layout_height="33dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="6dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:id="@+id/fileIcon"
        />
    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@id/fileIcon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="10dp"
        android:textColor="@color/colorFF323232"
        android:textSize="@dimen/textsize_15"
        android:id="@+id/fileName"
        />
</androidx.constraintlayout.widget.ConstraintLayout>