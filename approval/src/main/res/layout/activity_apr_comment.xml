<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".AprCommentActivity">

    <EditText
        android:id="@+id/et_comment_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="top|start"
        android:hint="请输入评论内容，最多200字"
        android:minHeight="220dp"
        android:paddingStart="@dimen/dp_12"
        android:paddingTop="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_15"
        android:textColor="@color/text_black"
        android:textColorHint="@color/c_b3b3b3"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:layout_marginTop="9dp"
        android:minHeight="154dp"
        android:paddingStart="@dimen/dp_10"
        android:paddingTop="@dimen/dp_17"
        android:paddingEnd="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_17"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_comment_input">

        <ImageView
            android:id="@+id/iv_add_pic"
            android:layout_width="@dimen/dp_46"
            android:layout_height="@dimen/dp_46"
            android:layout_marginTop="@dimen/dp_14"
            android:src="@drawable/icon_add_pic"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_pic_title" />

        <TextView
            android:id="@+id/tv_pic_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="添加照片"
            android:textColor="@color/c_ff888888"
            android:textSize="@dimen/sp_13"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_14"
            android:minHeight="@dimen/dp_120"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_pic_title"
            app:layout_constraintTop_toBottomOf="@id/tv_pic_title" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>