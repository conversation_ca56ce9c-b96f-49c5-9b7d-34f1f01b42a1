<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_apr_launch_port"
        android:layout_width="@dimen/dp_46"
        android:layout_height="@dimen/dp_46"
        android:layout_marginTop="@dimen/dp_14"
        android:src="@drawable/icon_custom_apr_default"
        app:layout_constraintBottom_toTopOf="@id/tv_apr_launch_port"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_apr_launch_port"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_7"
        android:ellipsize="end"
        android:lines="1"
        android:ems="6"
        android:maxLength="6"
        android:maxLines="1"
        android:gravity="center_horizontal"
        android:text="自定义审批123"
        android:textColor="@color/color808080"
        android:textSize="@dimen/textsize_13"
        app:layout_constraintEnd_toEndOf="@id/iv_apr_launch_port"
        app:layout_constraintStart_toStartOf="@id/iv_apr_launch_port"
        app:layout_constraintTop_toBottomOf="@id/iv_apr_launch_port" />
</androidx.constraintlayout.widget.ConstraintLayout>