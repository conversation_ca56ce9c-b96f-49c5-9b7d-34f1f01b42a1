<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <!--弹出dialog-->
    <!--<style name="my_dialog" parent="@android:style/Theme.Holo.DialogWhenLarge">-->
    <style name="my_dialog" parent="@style/Theme.AppCompat.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.6</item>
    </style>
    <!--dialog进入进出动画-->
    <style name="AnimBottom" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/push_bottom_in</item>
        <item name="android:windowExitAnimation">@anim/push_bottom_out</item>
    </style>

    <style name="BottomDialog" parent="@style/AppTheme">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <style name="default_text">
        <item name="android:includeFontPadding">false</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/colorFF333333</item>
        <item name="android:textColorHint">@color/color999999</item>
        <item name="android:textSize">@dimen/sp_12</item>
    </style>

    <style name="property_title" parent="default_text">
        <item name="android:textColor">@color/c_151515</item>
        <item name="android:textSize">@dimen/sp_15</item>
    </style>

    <style name="property_content" parent="default_text">
        <item name="android:layout_width">0dp</item>
    </style>

    <style name="property_title_detail" parent="default_text">
        <item name="android:textColor">@color/c_ff888888</item>
    </style>

    <style name="property_content_detail" parent="default_text">
        <item name="android:layout_width">0dp</item>
    </style>

    <style name="apr_tab_text" parent="default_text">
        <item name="android:textColor">@color/color_tab_text</item>
        <item name="android:paddingStart">@dimen/dp_10</item>
        <item name="android:paddingEnd">@dimen/dp_10</item>
    </style>

    <style name="apr_tab_dot">
        <item name="android:background">@drawable/bg_red_circle</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/sp_12</item>
    </style>

    <style name="apr_tab_indicator">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">@dimen/dp_2</item>
        <item name="android:background">@color/c_apr_over</item>
    </style>

    <style name="apr_bottom_bar_icon">
        <item name="android:layout_width">@dimen/dp_28</item>
        <item name="android:layout_height">@dimen/dp_28</item>
    </style>

</resources>