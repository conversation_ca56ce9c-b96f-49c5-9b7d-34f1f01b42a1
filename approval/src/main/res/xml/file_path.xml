<?xml version="1.0" encoding="utf-8"?>
<paths xmlns:android="http://schemas.android.com/apk/res/android">
    <!--Represents files in the files/ subdirectory of your app's internal storage area.
    This subdirectory is the same as the value returned by Context.getFilesDir().-->
    <!--app内部存储范围内的files/目录下的文件路径，通过Context.getFilesDir()获取该路径-->
    <files-path name="files" path="files/"/>

    <!--Represents files in the cache subdirectory of your app's internal storage area.
    The root path of this subdirectory is the same as the value returned by getCacheDir().-->
    <!--提供app内部存储空间内缓存子目录中路径，通过Context.getCacheDir()获取该路径-->
    <cache-path name="cache" path="cache/"/>

    <!--Represents files in the root of the external storage area.
    The root path of this subdirectory is the same as the value returned by Environment.getExternalStorageDirectory().-->
    <!--提供app外部存储空间内根路径，通过Environment.getExternalStorageDirectory()获取该路径-->
    <external-path name="ex_root" path="/"/>

    <!--Represents files in the root of your app's external storage area.
    The root path of this subdirectory is the same as the value
    returned by Context#getExternalFilesDir(String) Context.getExternalFilesDir(null).-->
    <!--提供app外部存储空间内文件子目录中路径，path为方法的参数，
    通过Context#getExternalFilesDir(String) Context.getExternalFilesDir(null)获取该路径-->
    <external-files-path name="ex_files" path="ex_files/"/>

    <!--Represents files in the root of your app's external cache area.
     The root path of this subdirectory is the same as the value returned by Context.getExternalCacheDir().-->
    <!--提供app外部存储空间内缓存子目录中路径，path为相对app外部存储空间中的路径，通过Context.getExternalCacheDir()获取该路径-->
    <external-cache-path name="ex_cache" path="ex_cache/"/>

    <!--Represents files in the root of your app's external media area.
    The root path of this subdirectory is the same as the value returned by the first result of Context.getExternalMediaDirs().-->
    <!--Note: this directory is only available on API 21+ devices.-->
    <!--提供app外部存储空间内媒体子目录路径，path为相对app外部存储空间中的路径，
    通过Context.getExternalMediaDirs()获取该路径，此目录为API21以上可见-->
    <external-media-path name="ex_media" path="ex_media/"/>

    <external-path name="external_files" path="." />

    <!--These child elements all use the same attributes:-->
    <!--name:contentUri 中路径名，value为文件创建时目录名，
    例如：创建文件时，先找到其父目录，如：File(context.getExternalCacheDir(),"ex_cache")，
    再根据父目录，创建文件File(parentPath,"fileName.jpg"),
    最终生成的Uri路径为：content://com.marktoo.test.fileProvider/ex_cache/fileName.jpg，这样以达到掩盖原始真实路径的安全策略-->

    <!--name="name"-->
    <!--A URI path segment. To enforce security, this value hides the name of the subdirectory you're sharing.
    The subdirectory name for this value is contained in the path attribute.-->
    <!--path="path"-->
    <!--The subdirectory you're sharing. While the name attribute is a URI path segment,
    the path value is an actual subdirectory name. Notice that the value refers to a subdirectory,
    not an individual file or files. You can't share a single file by its file name,
    nor can you specify a subset of files using wildcards.-->

    <!--You must specify a child element of <paths> for each directory that contains files for which you want content URIs.
    For example, these XML elements specify two directories:-->
    <!--<files-path name="my_images" path="images/"/>-->
    <!--<files-path name="my_docs" path="docs/"/>-->
</paths>