<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_mine_top_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="95dp"
            android:layout_height="97dp"
            android:src="@drawable/icon_fragment_mine_top_circle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <include layout="@layout/layout_common_toolbar_left" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/view_mine_top_margin"
        android:layout_width="match_parent"
        android:layout_height="103dp"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@id/cl_mine_top_layout" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_mine_top_margin">

        <RelativeLayout
            android:id="@+id/mine_set_rv"
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:layout_marginStart="14dp"
            android:layout_marginTop="86dp"
            android:layout_marginEnd="14dp"
            android:background="@drawable/rounded_white_29"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_mine_set"
                android:layout_width="39dp"
                android:layout_height="39dp"
                android:layout_centerVertical="true"
                android:layout_marginStart="9dp"
                android:src="@drawable/icon_mine_set" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="14dp"
                android:layout_toEndOf="@id/iv_mine_set"
                android:text="设置"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/textsize_16" />

            <ImageView
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="16dp"
                android:src="@drawable/icon_right_arrow_blue" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/about_rv"
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:layout_marginStart="14dp"
            android:layout_marginTop="14dp"
            android:layout_marginEnd="14dp"
            android:background="@drawable/rounded_white_29"
            app:layout_constraintTop_toBottomOf="@id/mine_set_rv">

            <ImageView
                android:id="@+id/about_set"
                android:layout_width="39dp"
                android:layout_height="39dp"
                android:layout_centerVertical="true"
                android:layout_marginStart="9dp"
                android:src="@drawable/icon_mine_fragment_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="14dp"
                android:layout_toEndOf="@id/about_set"
                android:text="关于担当"
                android:textColor="#333333"
                android:textSize="@dimen/textsize_16" />

            <ImageView
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="16dp"
                android:src="@drawable/icon_right_arrow_blue" />
        </RelativeLayout>

        <TextView
            android:id="@+id/tv_service_phone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="19dp"
            android:text="客服中心：************"
            android:textColor="@color/color666666"
            android:textSize="@dimen/textsize_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_mine_layout"
        android:layout_width="match_parent"
        android:layout_height="173dp"
        android:layout_marginStart="14dp"
        android:layout_marginTop="76dp"
        android:layout_marginEnd="14dp"
        android:background="@drawable/rounded_main_blue_14"
        app:layout_constraintTop_toTopOf="parent">

        <com.joinutech.ddbeslibrary.widget.CircleImageView
            android:id="@+id/iv_mine_icon"
            android:layout_width="62dp"
            android:layout_height="62dp"
            android:layout_centerInParent="true"
            android:layout_marginStart="24dp"
            android:layout_marginTop="24dp"
            app:civ_border_color="@color/white"
            app:civ_border_width="2dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@drawable/icon_fragment_mine_water_size"
            app:layout_constraintBottom_toBottomOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_detail_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="9dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_mine_icon"
            app:layout_constraintTop_toTopOf="@id/iv_mine_icon">

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxEms="6"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="@dimen/textsize_19"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_sex_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginStart="9dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_name"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_age"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            android:layout_marginEnd="68dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="#ffffffff"
            android:textSize="@dimen/textsize_13"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/cl_detail_layout"
            app:layout_constraintTop_toBottomOf="@id/cl_detail_layout" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_org_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_age"
            app:layout_constraintTop_toBottomOf="@id/tv_age">

            <ImageView
                android:id="@+id/iv_org_icon"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:src="@drawable/icon_fragment_mine_org"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_org_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="#ffffffff"
                android:textSize="@dimen/textsize_13"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_org_icon"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_address_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_age"
            app:layout_constraintTop_toBottomOf="@id/cl_org_layout"
            app:layout_goneMarginTop="30dp">

            <ImageView
                android:id="@+id/iv_address_icon"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:src="@drawable/icon_fragment_mine_address"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_address_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="#ffffffff"
                android:textSize="@dimen/textsize_13"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_address_icon"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/iv_mine_qr"
        android:layout_width="62dp"
        android:layout_height="35dp"
        android:layout_marginTop="36dp"
        android:layout_marginEnd="11dp"
        android:src="@drawable/icon_fragment_mine_code"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/cl_mine_layout" />
</androidx.constraintlayout.widget.ConstraintLayout>