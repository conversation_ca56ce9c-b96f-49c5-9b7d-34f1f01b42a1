<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="300dp"
    android:layout_height="63dp"
    android:background="@drawable/rounded_white_dialog_version">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="恭喜，您正在使用担当最新版本！"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>