<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#F4F4F4"
    android:orientation="vertical"
    android:paddingBottom="10dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:gravity="center_vertical"
        android:background="@color/white"
        android:orientation="horizontal">

        <com.joinutech.ddbeslibrary.widget.CircleImageView
            android:id="@+id/heading"
            android:layout_width="41dp"
            android:layout_height="41dp"
            android:layout_marginStart="14dp"
            android:src="@drawable/default_heading" />

        <TextView
            android:id="@+id/name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="2"
            android:paddingStart="@dimen/dp_14"
            android:paddingEnd="@dimen/dp_10"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_14"
            tools:text="加优科技有限公司加优科技有限公司加优科技有限公司加优科技有限公司加优科技有限公司加优科技有限公司加优科技有限公司加优科技有限公司加优科技有限公司加优科技有限公司" />

        <TextView
            android:id="@+id/selected"
            android:layout_width="77dp"
            android:layout_height="24dp"
            android:layout_marginEnd="19dp"
            android:background="@drawable/round_corner_blue_white_bg"
            android:gravity="center"
            android:text="设为主要团队"
            android:textColor="@color/color_blue_gray"
            android:textSize="@dimen/sp_10" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/dp_14"
        android:paddingTop="@dimen/dp_10"
        android:paddingEnd="@dimen/dp_20"
        android:text="在您的通讯录中，主要团队将会突出显示；您对外的个人资料中的【所在团队】也会显示您设置的主要团队。"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"/>

</LinearLayout>