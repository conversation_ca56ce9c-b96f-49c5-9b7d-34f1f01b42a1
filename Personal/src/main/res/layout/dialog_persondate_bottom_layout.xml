<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="226dp"
    android:background="@color/white"
    android:orientation="vertical">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp"
            android:layout_marginBottom="13dp"
            android:layout_marginLeft="25dp"
            android:text="取消"
            android:textColor="#2EA9FF"
            android:textSize="@dimen/textsize_14"
            />
        <TextView
            android:id="@+id/confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp"
            android:layout_marginBottom="13dp"
            android:layout_marginRight="25dp"
            android:layout_alignParentRight="true"
            android:text="保存"
            android:textColor="#2EA9FF"
            android:textSize="@dimen/textsize_14"
            />
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_below="@id/cancel"
            android:background="@color/line_grey"
            />
    </RelativeLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="10dip"
        android:paddingBottom="10dp"
        android:paddingLeft="70dp"
        android:paddingRight="70dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="年份"
            android:textColor="@color/black_87alpha"
            android:textSize="@dimen/textsize_16" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="月份"
            android:textColor="@color/black_87alpha"
            android:textSize="@dimen/textsize_16" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="日期"
            android:textColor="@color/black_87alpha"
            android:textSize="@dimen/textsize_16" />
    </LinearLayout>

    <DatePicker
        android:id="@+id/datePicker"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:calendarViewShown="false"
        android:theme="@android:style/Theme.Holo.Light.NoActionBar" />
</LinearLayout>