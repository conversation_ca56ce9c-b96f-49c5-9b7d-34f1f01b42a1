<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/bg_person">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/rl_icon_layout"
            android:layout_width="match_parent"
            android:layout_height="66dp"
            android:background="@color/white">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="14dp"
                android:text="头像"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/textsize_16" />

            <ImageView
                android:id="@+id/iv_person_arrow"
                android:layout_width="9dp"
                android:layout_height="13dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="14dp"
                android:src="@drawable/ic_person_arrow" />

            <RelativeLayout
                android:layout_width="52dp"
                android:layout_height="52dp"
                android:layout_centerVertical="true"
                android:layout_marginRight="12dp"
                android:layout_toLeftOf="@id/iv_person_arrow">

                <com.joinutech.ddbeslibrary.widget.CircleImageView
                    android:id="@+id/iv_header"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="visible" />

                <!--                <TextView-->
                <!--                    android:id="@+id/tv_person_header_name"-->
                <!--                    android:layout_width="wrap_content"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:layout_centerInParent="true"-->
                <!--                    android:text=""-->
                <!--                    android:textColor="@color/white"-->
                <!--                    android:textSize="@dimen/textsize_28"-->
                <!--                    android:visibility="gone" />-->
            </RelativeLayout>

            <View
                android:layout_width="wrap_content"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/line_grey" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_name_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_46"
            android:background="@color/white">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="14dp"
                android:text="姓名"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/textsize_16" />

            <ImageView
                android:id="@+id/iv_name_arrow"
                android:layout_width="9dp"
                android:layout_height="13dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="14dp"
                android:src="@drawable/ic_person_arrow" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="12dp"
                android:layout_toLeftOf="@id/iv_name_arrow"
                android:textColor="#666666"
                android:textSize="@dimen/textsize_16" />

            <View
                android:layout_width="wrap_content"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/line_grey" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_birthday_layout"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:background="@color/white">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="14dp"
                android:text="生日"
                android:textColor="#333333"
                android:textSize="@dimen/textsize_16" />

            <ImageView
                android:id="@+id/iv_birthday_arrow"
                android:layout_width="9dp"
                android:layout_height="13dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="14dp"
                android:src="@drawable/ic_person_arrow" />

            <TextView
                android:id="@+id/tv_birthday"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="12dp"
                android:layout_toLeftOf="@id/iv_birthday_arrow"
                android:textColor="#666666"
                android:textSize="@dimen/textsize_16" />

            <View
                android:layout_width="wrap_content"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/line_grey" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_sex_layout"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:layout_marginBottom="9dp"
            android:background="@color/white">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="14dp"
                android:text="性别"
                android:textColor="#333333"
                android:textSize="@dimen/textsize_16" />

            <ImageView
                android:id="@+id/iv_sex_arrow"
                android:layout_width="9dp"
                android:layout_height="13dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="14dp"
                android:src="@drawable/ic_person_arrow" />

            <TextView
                android:id="@+id/tv_sex"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="12dp"
                android:layout_toLeftOf="@id/iv_sex_arrow"
                android:textColor="#666666"
                android:textSize="@dimen/textsize_16" />

            <ImageView
                android:id="@+id/iv_sex_icon"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_centerVertical="true"
                android:layout_marginRight="9dp"
                android:layout_toLeftOf="@id/tv_sex" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_work_layout"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:layout_marginBottom="9dp"
            android:background="@color/white">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="14dp"
                android:text="职业"
                android:textColor="#333333"
                android:textSize="@dimen/textsize_16" />

            <ImageView
                android:id="@+id/iv_wok_arrow"
                android:layout_width="9dp"
                android:layout_height="13dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="14dp"
                android:src="@drawable/ic_person_arrow" />

            <TextView
                android:id="@+id/tv_work"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="12dp"
                android:layout_toLeftOf="@id/iv_wok_arrow"
                android:textColor="#666666"
                android:textSize="@dimen/textsize_16" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_email_layout"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:background="@color/white">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="14dp"
                android:text="邮箱"
                android:textColor="#333333"
                android:textSize="@dimen/textsize_16" />

            <ImageView
                android:id="@+id/iv_email_arrow"
                android:layout_width="9dp"
                android:layout_height="13dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="14dp"
                android:src="@drawable/ic_person_arrow" />

            <TextView
                android:id="@+id/tv_email"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="12dp"
                android:layout_toLeftOf="@id/iv_email_arrow"
                android:textColor="#666666"
                android:textSize="@dimen/textsize_16" />

            <View
                android:layout_width="wrap_content"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/line_grey" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/rl_area_layout"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:gravity="center_vertical"
            android:background="@color/white">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="14dp"
                android:text="地区"
                android:textColor="#333333"
                android:textSize="@dimen/textsize_16" />

            <TextView
                android:id="@+id/tv_area"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:paddingLeft="10dp"
                android:gravity="right"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="12dp"
                android:layout_toLeftOf="@id/iv_area_arrow"
                android:textColor="#666666"
                tools:text="222222222222222222222222222222222222222222222222222222"
                android:textSize="@dimen/textsize_16" />

            <ImageView
                android:id="@+id/iv_area_arrow"
                android:layout_width="9dp"
                android:layout_height="13dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="14dp"
                android:src="@drawable/ic_person_arrow" />


        </LinearLayout>

        <RelativeLayout
            android:id="@+id/rl_qr_layout"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:background="@color/white"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="14dp"
                android:text="我的二维码"
                android:textColor="#333333"
                android:textSize="@dimen/textsize_16" />

            <ImageView
                android:id="@+id/iv_qr_arrow"
                android:layout_width="9dp"
                android:layout_height="13dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="14dp"
                android:src="@drawable/ic_person_arrow" />

            <ImageView
                android:id="@+id/iv_qr"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerVertical="true"
                android:layout_marginEnd="12dp"
                android:layout_toStartOf="@id/iv_qr_arrow" />
        </RelativeLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/rl_company_layout"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:layout_marginTop="9dp"
            android:background="@color/white">

            <TextView
                android:id="@+id/tv_org_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="14dp"
                android:text="主要团队"
                android:textColor="#333333"
                android:textSize="@dimen/textsize_16"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_company_logo"
                android:layout_width="9dp"
                android:layout_height="13dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="14dp"
                android:src="@drawable/ic_person_arrow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_company_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_20"
                android:layout_marginEnd="@dimen/dp_12"
                android:ellipsize="end"
                android:gravity="end|right"
                android:maxLines="1"
                android:textColor="@color/color666666"
                android:textSize="@dimen/textsize_16"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/iv_company_logo"
                app:layout_constraintStart_toEndOf="@id/tv_org_title"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</ScrollView>