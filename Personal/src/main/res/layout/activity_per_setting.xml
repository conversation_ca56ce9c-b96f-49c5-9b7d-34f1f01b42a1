<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/background"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/layout1_per_setting"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingLeft="14dp"
        android:paddingRight="14dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="账号与安全"
            android:textColor="@color/text_black"
            android:textSize="@dimen/textsize_14" />

        <ImageView
            android:id="@+id/arr1_seting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:src="@drawable/arraw_right" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="14dp"
            android:layout_toLeftOf="@id/arr1_seting"
            android:src="@drawable/lock" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/layout2_per_setting"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginTop="10dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingLeft="14dp"
        android:paddingRight="14dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="新消息通知"
            android:textColor="@color/text_black"
            android:textSize="@dimen/textsize_14" />

        <ImageView

            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:src="@drawable/arraw_right" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_grey"
        android:visibility="visible" />

    <RelativeLayout
        android:id="@+id/layout3_per_setting"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginBottom="10dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingLeft="14dp"
        android:paddingRight="14dp"
        android:visibility="visible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="隐私"
            android:textColor="@color/text_black"
            android:textSize="@dimen/textsize_14" />

        <ImageView

            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:src="@drawable/arraw_right" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/layout4_per_setting"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingLeft="14dp"
        android:paddingRight="14dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="清除缓存"
            android:textColor="@color/text_black"
            android:textSize="@dimen/textsize_14" />

        <ImageView
            android:id="@+id/icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:src="@drawable/arraw_right" />

        <TextView
            android:id="@+id/cacheText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="10dp"
            android:layout_toLeftOf="@id/icon"
            android:textColor="#999999"
            android:textSize="11sp" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/login_device_ount"
        android:layout_width="match_parent"
        android:visibility="gone"
        android:layout_height="48dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingLeft="14dp"
        android:paddingRight="14dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="登录设备"
            android:textColor="@color/text_black"
            android:textSize="@dimen/textsize_14" />

        <ImageView
            android:id="@+id/login_device_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:src="@drawable/arraw_right" />

        <TextView
            android:id="@+id/login_device_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="10dp"
            tools:text="2"
            android:text="0"
            android:layout_toLeftOf="@+id/login_device_icon"
            android:textColor="#999999"
            android:textSize="13sp" />
    </RelativeLayout>

    <TextView
        android:id="@+id/logout_per_setting"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginTop="10dp"
        android:background="@color/white"
        android:gravity="center"
        android:text="退出登录"
        android:textColor="#FF3000"
        android:textSize="@dimen/textsize_16" />
</LinearLayout>