<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="255dp"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="19dp"
        android:text="请设置您的性别"
        android:textColor="#333333"
        android:textSize="@dimen/textsize_16" />

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="46dp">

        <ImageView
            android:id="@+id/iv_girl_icon"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginRight="60dp"
            android:src="@drawable/selector_person_girl" />

        <ImageView
            android:id="@+id/iv_man_icon"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_toRightOf="@id/iv_girl_icon"
            android:src="@drawable/selector_person_man" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/iv_girl_icon"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="9dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_girl"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="女"
                android:textColor="#CDCDCD"
                android:textSize="@dimen/textsize_16" />

            <TextView
                android:id="@+id/tv_man"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="100dp"
                android:text="男"
                android:textColor="#CDCDCD"
                android:textSize="@dimen/textsize_16" />
        </LinearLayout>
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="27dp"
        android:background="@color/line_grey" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:paddingTop="14dp"
            android:paddingBottom="14dp"
            android:text="取消"
            android:textColor="#999999"
            android:textSize="@dimen/textsize_16" />

        <TextView
            android:id="@+id/confirm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:paddingTop="14dp"
            android:paddingBottom="14dp"
            android:text="保存"
            android:textColor="#0068FF"
            android:textSize="@dimen/textsize_16" />
    </LinearLayout>
</LinearLayout>