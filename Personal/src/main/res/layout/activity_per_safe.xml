<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/layout1_per_safe"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingLeft="14dp"
        android:paddingRight="14dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="更换手机号"
            android:textColor="@color/text_black"
            android:textSize="@dimen/textsize_14" />

        <ImageView
            android:id="@+id/arr1_seting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:src="@drawable/arraw_right" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_grey" />

    <RelativeLayout
        android:id="@+id/layout2_per_safe"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingLeft="14dp"
        android:paddingRight="14dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="修改密码"
            android:textColor="@color/text_black"
            android:textSize="@dimen/textsize_14" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:src="@drawable/arraw_right" />
    </RelativeLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:layout_marginStart="14dp"
        android:gravity="center_vertical"
        android:text="社交账号关联"
        android:textColor="@color/color999999"
        android:textSize="12sp" />

    <RelativeLayout
        android:id="@+id/layout3_per_safe"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/white"
        android:paddingLeft="14dp"
        android:paddingRight="14dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="微信"
            android:textColor="@color/text_black"
            android:textSize="@dimen/textsize_14" />

        <ImageView
            android:id="@+id/iv_wx_bind_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@drawable/arraw_right" />

        <TextView
            android:id="@+id/tv_wx_bind_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:layout_toLeftOf="@id/iv_wx_bind_arrow"
            android:text="未关联"
            android:textColor="@color/color999999"
            android:textSize="@dimen/textsize_14" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/ll_un_register"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@color/white"
        android:paddingLeft="14dp"
        android:paddingRight="14dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="注销担当账号"
            android:textColor="@color/text_black"
            android:textSize="@dimen/sp_14" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="visible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="20dp"
            android:text="关联【担当办公】微信公众号，及时获取工作通知"
            android:textColor="#ff333333"
            android:textSize="@dimen/textsize_14" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="17dp"
            android:gravity="center"
            android:text="可在微信中搜索【担当办公】名称，或直接保存下方二维码\n进入微信识别关联【担当办公】微信公众号，及时获取\n工作通知"
            android:textColor="#ff999999"
            android:textSize="@dimen/textsize_12" />

        <ImageView
            android:id="@+id/iv_wx_official_account"
            android:layout_width="98dp"
            android:layout_height="98dp"
            android:layout_gravity="center"
            android:layout_marginTop="25dp"
            android:src="@drawable/icon_wx_office_account" />

        <TextView
            android:id="@+id/tv_save"
            android:layout_width="228dp"
            android:layout_height="37dp"
            android:layout_gravity="center"
            android:layout_marginTop="31dp"
            android:background="@drawable/rounded_blue_savephone_bg_3"
            android:gravity="center"
            android:text="保存到手机"
            android:textColor="#ffffffff"
            android:textSize="@dimen/textsize_12" />
    </LinearLayout>
</LinearLayout>