<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical"
        android:paddingLeft="@dimen/dp_40"
        android:paddingRight="@dimen/dp_40"
        android:paddingBottom="@dimen/dp_40">

        <com.joinutech.ddbeslibrary.widget.CircleImageView
            android:id="@+id/civ_avatar"
            android:layout_width="86dp"
            android:layout_height="86dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_20"
            android:src="@drawable/default_heading" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="14dp"
            android:layout_marginBottom="33dp"
            android:textColor="@color/text_black"
            android:textSize="@dimen/sp_16" />

        <TextView
            android:id="@+id/tv_unregister_tip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lineSpacingExtra="@dimen/dp_1"
            android:text="很遗憾，担当无法继续为您提供服务。请完成以下操作方可完成注销。"
            android:textColor="@color/text_black" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_15"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <View
                android:layout_width="@dimen/dp_4"
                android:layout_height="@dimen/dp_4"
                android:background="@drawable/circle_black" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/dp_10"
                android:text="若为项目创建者，请移交项目负责人"
                android:textColor="@color/text_black"
                android:textSize="@dimen/sp_14" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <View
                android:layout_width="@dimen/dp_4"
                android:layout_height="@dimen/dp_4"
                android:background="@drawable/circle_black" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/dp_10"
                android:text="退出或解散所有团队、群组"
                android:textColor="@color/text_black"
                android:textSize="@dimen/sp_14" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <View
                android:layout_width="@dimen/dp_4"
                android:layout_height="@dimen/dp_4"
                android:background="@drawable/circle_black" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/dp_10"
                android:text="解绑关联的第三方账号"
                android:textColor="@color/text_black"
                android:textSize="@dimen/sp_14" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:orientation="horizontal">

            <View
                android:layout_width="@dimen/dp_4"
                android:layout_height="@dimen/dp_4"
                android:layout_marginTop="@dimen/dp_7"
                android:background="@drawable/circle_black" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/dp_10"
                android:text="已知晓担当云文档内的个人文件、企业文件、部门文件、协作文件注销之后不可找回"
                android:textColor="@color/text_black"
                android:textSize="@dimen/sp_14" />
        </LinearLayout>


    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_40"
        android:layout_marginEnd="@dimen/dp_40"
        android:gravity="start"
        android:text="注销后所有用户数据将被抹除不可恢复，请谨慎操作"
        android:textColor="@color/text_black" />

    <TextView
        android:id="@+id/btn_confirm"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginStart="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_marginEnd="@dimen/dp_40"
        android:background="@drawable/shape_tv_corner4"
        android:gravity="center"
        android:text="注销担当账号"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14" />
</LinearLayout>