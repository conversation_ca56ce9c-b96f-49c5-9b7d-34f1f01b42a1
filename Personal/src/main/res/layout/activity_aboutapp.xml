<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/rl_top_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="36dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/logo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_about_top" />

            <TextView
                android:id="@+id/tv_version_name"
                tools:text="3.2.1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/logo"
                android:layout_centerHorizontal="true"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="12dp"
                android:textColor="#999999"
                android:textSize="13sp" />
        </RelativeLayout>

        <TextView
            android:id="@+id/tv_version_update"
            android:layout_width="78dp"
            android:layout_height="24dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="30dp"
            android:background="@drawable/shape_tv_corner2"
            android:gravity="center"
            android:text="检查更新"
            android:textColor="#ff1e87f0"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/rl_top_layout"
            tools:visibility="visible" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="28dp"
            android:overScrollMode="never"
            app:layout_constraintTop_toBottomOf="@id/tv_version_update">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_content_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:minHeight="210dp"
                android:paddingStart="31dp"
                android:paddingTop="19dp"
                android:paddingEnd="31dp"
                android:paddingBottom="19dp">

                <TextView
                    android:id="@+id/tv_version_code"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#666666"
                    android:textSize="14sp"
                    tools:text="更新更新更新更新更更新更新更新"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_version_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:lineSpacingExtra="10dp"
                    android:textColor="#666666"
                    android:textSize="12sp"
                    app:layout_constraintTop_toBottomOf="@id/tv_version_code" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </ScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#F4F4F4"
        android:gravity="center_horizontal"
        android:orientation="vertical">


<!--隐私政策入口-->
        <TextView
            android:id="@+id/tv_pricacy_entrance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="《隐私政策》"
            android:textColor="#2479ed"
            android:textSize="13sp" />
        <TextView
            android:id="@+id/tv_service_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/aboutapp_text"
            android:textColor="@color/color999999"
            android:textSize="13sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="10dp"
            android:text="联系我们：<EMAIL>"
            android:textColor="@color/color999999"
            android:textSize="13sp" />

    </LinearLayout>
</LinearLayout>