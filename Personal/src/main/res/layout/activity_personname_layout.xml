<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="43dp"
        android:layout_marginRight="35dp">

        <TextView
            android:id="@+id/tv_email_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="26dp"
            android:layout_marginBottom="31dp"
            android:text="请设置可用的邮箱，以方便您方便接收各类工作邮件"
            android:textSize="@dimen/textsize_15"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <EditText
            android:id="@+id/et_email"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_email_tip"
            android:layout_marginStart="26dp"
            android:background="@color/white"
            android:hint="请输入姓名"
            android:textColor="#ff323232"
            android:textSize="@dimen/textsize_17"
            app:layout_constraintEnd_toStartOf="@id/tv_email_count"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_email_tip" />

        <TextView
            android:id="@+id/tv_email_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginEnd="35dp"
            android:text="2/20"
            android:textColor="#999999"
            android:textSize="@dimen/textsize_10"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginStart="26dp"
            android:layout_marginTop="12dp"
            android:background="@color/line_grey"
            app:layout_constraintEnd_toStartOf="@id/tv_email_count"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/et_email" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_group_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="14dp"
        android:gravity="end"
        android:text="30"
        android:textColor="#ffaaaaaa"
        android:textSize="12sp"
        android:visibility="visible" />

    <TextView
        android:id="@+id/tv_name_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="26dp"
        android:layout_marginTop="8dp"
        android:text="1-10个字符。建议您使用真实姓名，方便您在团队中的工作"
        android:textColor="#0A6DFF"
        android:textSize="@dimen/textsize_14"
        android:visibility="visible" />

    <TextView
        android:id="@+id/tv_email_rule"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="26dp"
        android:layout_marginTop="19dp"
        android:layout_marginRight="46dp"
        android:text="\t\t邮箱应由字母a~z(不区分大小写)、数字0~9、点、减号或下划线组成；只能以数字或字母开头和结尾，例如：beijing.2008，用户名长度为4~18个字符。"
        android:textColor="#999999"
        android:textSize="@dimen/textsize_14"
        android:visibility="gone" />
</LinearLayout>