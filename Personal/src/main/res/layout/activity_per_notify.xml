<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/white"
        android:paddingLeft="14dp"
        android:paddingRight="14dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="接收新消息通知"
            android:textColor="@color/text_black"
            android:layout_centerVertical="true"
            android:textSize="@dimen/textsize_14" />

        <com.kyleduo.switchbutton.SwitchButton
            android:id="@+id/switch1_notify"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            app:kswAnimationDuration="300"
            app:kswBackDrawable="@drawable/ios_back_drawable"
            app:kswThumbDrawable="@drawable/ios_thumb_selector"
            app:kswThumbMarginBottom="-8dp"
            app:kswThumbMarginLeft="-5dp"
            app:kswThumbMarginRight="-5dp"
            app:kswThumbMarginTop="-2.5dp"
            app:kswThumbRangeRatio="1.4"
             />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/switch2_notifyLayout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:background="@color/white"
        android:paddingLeft="14dp"
        android:paddingRight="14dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="声音"
            android:textColor="@color/text_black"
            android:layout_centerVertical="true"
            android:textSize="@dimen/textsize_14" />

        <com.kyleduo.switchbutton.SwitchButton
            android:id="@+id/switch2_notify"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            app:kswAnimationDuration="300"
            app:kswBackDrawable="@drawable/ios_back_drawable"
            app:kswThumbDrawable="@drawable/ios_thumb_selector"
            app:kswThumbMarginBottom="-8dp"
            app:kswThumbMarginLeft="-5dp"
            app:kswThumbMarginRight="-5dp"
            app:kswThumbMarginTop="-2.5dp"
            app:kswThumbRangeRatio="1.4"/>

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/line_grey" />

    <RelativeLayout
        android:id="@+id/switch3_notifyLayout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/white"
        android:paddingLeft="14dp"
        android:paddingRight="14dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="震动"
            android:textColor="@color/text_black"
            android:layout_centerVertical="true"
            android:textSize="@dimen/textsize_14" />

        <com.kyleduo.switchbutton.SwitchButton
            android:id="@+id/switch3_notify"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            app:kswAnimationDuration="300"
            app:kswBackDrawable="@drawable/ios_back_drawable"
            app:kswThumbDrawable="@drawable/ios_thumb_selector"
            app:kswThumbMarginBottom="-8dp"
            app:kswThumbMarginLeft="-5dp"
            app:kswThumbMarginRight="-5dp"
            app:kswThumbMarginTop="-2.5dp"
            app:kswThumbRangeRatio="1.4" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/line_grey" />

    <RelativeLayout
        android:id="@+id/switch_pc_wehter_receive_msg_notifyLayout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:visibility="gone"
        android:background="@color/white"
        android:paddingLeft="14dp"
        android:paddingRight="14dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="接收手机通知"
            android:textColor="@color/text_black"
            android:layout_centerVertical="true"
            android:textSize="@dimen/textsize_14" />

        <com.kyleduo.switchbutton.SwitchButton
            android:id="@+id/switch_pc_notify"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            app:kswAnimationDuration="300"
            app:kswBackDrawable="@drawable/ios_back_drawable"
            app:kswThumbDrawable="@drawable/ios_thumb_selector"
            app:kswThumbMarginBottom="-8dp"
            app:kswThumbMarginLeft="-5dp"
            app:kswThumbMarginRight="-5dp"
            app:kswThumbMarginTop="-2.5dp"
            app:kswThumbRangeRatio="1.4" />

    </RelativeLayout>
    <TextView
        android:visibility="gone"
        android:text="当担当桌面端在线时，本设备继续接收新消息通知"
        android:layout_marginTop="5dp"
        android:layout_marginRight="14dp"
        android:layout_marginLeft="14dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

</LinearLayout>