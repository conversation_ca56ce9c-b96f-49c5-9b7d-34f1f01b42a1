<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    android:scrollbars="none">

    <LinearLayout
        android:id="@+id/ll_save_pic_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_20">

        <com.joinutech.ddbeslibrary.widget.CircleImageView
            android:id="@+id/iv_header"
            android:layout_width="@dimen/dp_76"
            android:layout_height="@dimen/dp_76"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_25" />

        <TextView
            android:id="@+id/tv_user_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_14"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_18" />

        <ImageView
            android:id="@+id/iv_qr_code"
            android:layout_width="@dimen/dp_230"
            android:layout_height="@dimen/dp_230"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_20" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:src="@drawable/icon_logo_small" />

            <TextView
                android:id="@+id/tv_hint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="@dimen/dp_4"
                android:textColor="@color/color999999"
                android:textSize="@dimen/sp_13" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_save"
            android:layout_width="242dp"
            android:layout_height="48dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_30"
            android:background="@drawable/rounded_blue_savephone_bg"
            android:gravity="center"
            android:text="保存到手机"
            android:textColor="@color/white"
            android:textSize="@dimen/textsize_16" />
    </LinearLayout>


</ScrollView>