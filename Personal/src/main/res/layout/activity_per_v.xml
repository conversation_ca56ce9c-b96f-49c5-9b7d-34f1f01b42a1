<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <View
        android:id="@+id/line_per_v"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginBottom="50dp"
        android:background="@color/line_grey" />

    <ImageView
        android:id="@+id/per_top_img"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/line_per_v"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="40dp"
        android:src="@drawable/lock_verify" />

    <LinearLayout
        android:id="@+id/per_layout1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:visibility="visible"
        android:layout_below="@id/per_top_img"
        android:orientation="vertical">

        <TextView
            android:id="@+id/per_tv_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="25dp"
            android:text="更换关联手机号，需要先进行身份验证"
            android:textColor="@color/text_black"
            android:textSize="@dimen/textsize_16" />

        <EditText
            android:id="@+id/per_et_1"
            android:layout_width="match_parent"
            android:layout_height="32dp"
            android:layout_marginLeft="35dp"
            android:layout_marginRight="35dp"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="请输入登录密码"
            android:inputType="textPassword"
            android:maxLength="16"
            android:singleLine="true"
            android:textColor="@color/text_black"
            android:textColorHint="@color/text_tipgren"
            android:textSize="@dimen/textsize_14" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="35dp"
            android:layout_marginRight="35dp"
            android:background="@color/line_grey" />

        <LinearLayout
            android:id="@+id/per_layout2"
            android:layout_width="match_parent"
            android:layout_height="32dp"
            android:layout_marginLeft="35dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="35dp"
            tools:visibility="visible"
            android:orientation="horizontal"
            android:visibility="gone">

            <EditText
                android:id="@+id/per_et_2"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="请输入验证码"
                android:inputType="number"
                android:maxEms="6"
                android:singleLine="true"
                android:textColor="@color/text_black"
                android:textColorHint="@color/text_tipgren"
                android:textSize="@dimen/textsize_14" />

            <TextView
                android:id="@+id/per_v_tv1"
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:layout_gravity="right"
                android:layout_marginBottom="4dp"
                android:background="@drawable/shape_tv_corner"
                android:gravity="center"
                android:minWidth="50dp"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:text="获取验证码"
                android:textColor="@color/main_blue"
                android:textSize="@dimen/textsize_12" />
        </LinearLayout>

        <View
            android:id="@+id/per_line"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="35dp"
            android:layout_marginRight="35dp"
            android:background="@color/line_grey"
            android:visibility="gone" />
        <TextView
            android:id="@+id/per_tv_finish"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginLeft="41dp"
            android:layout_marginRight="41dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/shape_button_corner"
            android:gravity="center"
            android:text="下一步"
            android:textColor="@color/white"
            android:textSize="14sp" />
    </LinearLayout>
</RelativeLayout>