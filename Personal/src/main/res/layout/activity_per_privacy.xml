<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:scrollbars="none">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="26dp"
            android:layout_marginLeft="14dp"
            android:gravity="center_vertical"
            android:text="谁可以看我的资料"
            android:textColor="@color/text_gren" />

        <RelativeLayout
            android:id="@+id/allWatch"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/white"
            android:gravity="center_vertical"
            android:paddingLeft="14dp"
            android:paddingRight="14dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="好友及有权限的同事"
                android:textColor="@color/text_black"
                android:textSize="@dimen/textsize_14" />

            <ImageView
                android:id="@+id/check1_privacy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:src="@drawable/checkmark" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/line_grey" />

        <RelativeLayout
            android:id="@+id/someWatch"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/white"
            android:gravity="center_vertical"
            android:paddingLeft="14dp"
            android:paddingRight="14dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="仅有权限的同事"
                android:textColor="@color/text_black"
                android:textSize="@dimen/textsize_14" />

            <ImageView
                android:id="@+id/check2_privacy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:src="@drawable/checkmark"
                android:visibility="gone" />
        </RelativeLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="26dp"
            android:layout_marginStart="14dp"
            android:gravity="center_vertical"
            android:text="可以通过什么方式添加我"
            android:textColor="@color/text_gren" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/white"
            android:paddingLeft="14dp"
            android:paddingRight="14dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="手机号搜索"
                android:textColor="@color/text_black"
                android:textSize="@dimen/textsize_14" />

            <com.kyleduo.switchbutton.SwitchButton
                android:id="@+id/switch2_privacy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                app:kswAnimationDuration="300"
                app:kswBackDrawable="@drawable/ios_back_drawable"
                app:kswThumbDrawable="@drawable/ios_thumb_selector"
                app:kswThumbMarginBottom="-8dp"
                app:kswThumbMarginLeft="-5dp"
                app:kswThumbMarginRight="-5dp"
                app:kswThumbMarginTop="-2.5dp"
                app:kswThumbRangeRatio="1.4" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/line_grey" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/white"
            android:paddingLeft="14dp"
            android:paddingRight="14dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="群组"
                android:textColor="@color/text_black"
                android:textSize="@dimen/textsize_14" />

            <com.kyleduo.switchbutton.SwitchButton
                android:id="@+id/switch3_privacy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                app:kswAnimationDuration="300"
                app:kswBackDrawable="@drawable/ios_back_drawable"
                app:kswThumbDrawable="@drawable/ios_thumb_selector"
                app:kswThumbMarginBottom="-8dp"
                app:kswThumbMarginLeft="-5dp"
                app:kswThumbMarginRight="-5dp"
                app:kswThumbMarginTop="-2.5dp"
                app:kswThumbRangeRatio="1.4" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/line_grey" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/white"
            android:paddingLeft="14dp"
            android:paddingRight="14dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="团队架构"
                android:textColor="@color/text_black"
                android:textSize="@dimen/textsize_14" />

            <com.kyleduo.switchbutton.SwitchButton
                android:id="@+id/switch4_privacy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                app:kswAnimationDuration="300"
                app:kswBackDrawable="@drawable/ios_back_drawable"
                app:kswThumbDrawable="@drawable/ios_thumb_selector"
                app:kswThumbMarginBottom="-8dp"
                app:kswThumbMarginLeft="-5dp"
                app:kswThumbMarginRight="-5dp"
                app:kswThumbMarginTop="-2.5dp"
                app:kswThumbRangeRatio="1.4" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/line_grey" />
        <!--团队合作开关-->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/white"
            android:paddingLeft="14dp"
            android:paddingRight="14dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="合作团队"
                android:textColor="@color/text_black"
                android:textSize="@dimen/textsize_14" />

            <com.kyleduo.switchbutton.SwitchButton
                android:id="@+id/switch5_privacy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                app:kswAnimationDuration="300"
                app:kswBackDrawable="@drawable/ios_back_drawable"
                app:kswThumbDrawable="@drawable/ios_thumb_selector"
                app:kswThumbMarginBottom="-8dp"
                app:kswThumbMarginLeft="-5dp"
                app:kswThumbMarginRight="-5dp"
                app:kswThumbMarginTop="-2.5dp"
                app:kswThumbRangeRatio="1.4" />

        </RelativeLayout>
        <!--陌生人发消息开关-->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="26dp"
            android:background="@color/white"
            android:paddingLeft="14dp"
            android:visibility="gone"
            android:paddingRight="14dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="不允许陌生人发送消息"
                android:textColor="@color/text_black"
                android:textSize="@dimen/textsize_14" />

            <com.kyleduo.switchbutton.SwitchButton
                android:id="@+id/switch6_stranger"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                app:kswAnimationDuration="300"
                app:kswBackDrawable="@drawable/ios_back_drawable"
                app:kswThumbDrawable="@drawable/ios_thumb_selector"
                app:kswThumbMarginBottom="-8dp"
                app:kswThumbMarginLeft="-5dp"
                app:kswThumbMarginRight="-5dp"
                app:kswThumbMarginTop="-2.5dp"
                app:kswThumbRangeRatio="1.4" />

        </RelativeLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="26dp"
            android:layout_marginStart="14dp"
            android:background="@color/white"
            android:gravity="center_vertical"
            android:visibility="gone"
            android:text="开启后，仅好友可与您联系"
            android:textColor="@color/text_gren" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/line_grey" />
        <!--通讯录黑名单-->
        <RelativeLayout
            android:id="@+id/rl_black_layout"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:visibility="gone"
            android:background="@color/white"
            android:paddingLeft="14dp"
            android:paddingRight="14dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="通讯录黑名单"
                android:textColor="@color/text_black"
                android:textSize="@dimen/textsize_14" />
            <!--箭头-->
            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="5dp"
                android:src="@drawable/arraw_right" />
        </RelativeLayout>

    </LinearLayout>
</ScrollView>