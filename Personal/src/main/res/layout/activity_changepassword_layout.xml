<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/layout1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="40dp"
            android:layout_marginTop="34dp"
            android:layout_marginRight="40dp"
            android:text="6-16位字母和数字的组合"
            android:textColor="#0A6DFF"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/per_et_3"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_marginLeft="40dp"
            android:layout_marginTop="34dp"
            android:layout_marginRight="40dp"
            android:background="@null"
            android:hint="请输入旧密码"
            android:inputType="textPassword"
            android:maxLength="16"
            android:singleLine="true"
            android:textColor="@color/text_black"
            android:textColorHint="#ffababab"
            android:textSize="15sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="40dp"
            android:layout_marginRight="40dp"
            android:background="@color/line_grey" />

        <EditText
            android:id="@+id/per_et_4"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_marginLeft="40dp"
            android:layout_marginTop="34dp"
            android:layout_marginRight="40dp"
            android:background="@null"
            android:hint="请输入新密码"
            android:inputType="textPassword"
            android:maxLength="16"
            android:singleLine="true"
            android:textColor="@color/text_black"
            android:textColorHint="#ffababab"
            android:textSize="15sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="40dp"
            android:layout_marginRight="40dp"
            android:background="@color/line_grey" />

        <EditText
            android:id="@+id/per_et_5"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_marginLeft="40dp"
            android:layout_marginTop="34dp"
            android:layout_marginRight="40dp"
            android:background="@null"
            android:hint="请再次输入新密码"
            android:inputType="textPassword"
            android:maxLength="16"
            android:singleLine="true"
            android:textColor="@color/text_black"
            android:textColorHint="#ffababab"
            android:textSize="15sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="40dp"
            android:layout_marginRight="40dp"
            android:background="@color/line_grey" />

        <LinearLayout
            android:id="@+id/per_layout_showPassword"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginLeft="40dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="40dp"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/per_img_showword"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/selector_password" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="6dp"
                android:text="显示密码"
                android:textColor="@color/text_lowgray"
                android:textSize="@dimen/textsize_12" />
        </LinearLayout>

        <TextView
            android:id="@+id/finish"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginLeft="41dp"
            android:layout_marginTop="14dp"
            android:layout_marginRight="41dp"
            android:background="@drawable/shape_button_corner"
            android:gravity="center"
            android:text="完成修改"
            android:textColor="@color/white"
            android:textSize="@dimen/textsize_16" />

    </LinearLayout>
</LinearLayout>