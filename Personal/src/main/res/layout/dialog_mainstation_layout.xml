<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_white_mainstation_bg">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ll_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp_20"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:paddingStart="@dimen/dp_10"
                android:paddingEnd="@dimen/dp_10"
                android:textColor="@color/color393939"
                android:textSize="@dimen/sp_14"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="您确认要将 xxx 设置为您的主要团队吗？" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_2"
            android:layout_marginTop="@dimen/dp_20"
            android:background="@color/line_grey"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintTop_toBottomOf="@id/ll_text" />

        <View
            android:id="@+id/line1"
            android:layout_width="@dimen/dp_0_2"
            android:layout_height="@dimen/dp_46"
            android:background="@color/line_grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line" />

        <TextView
            android:id="@+id/cancel"
            android:layout_width="0dp"
            android:layout_height="46dp"
            android:gravity="center"
            android:text="取消"
            android:textColor="@color/color999999"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/confirm"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line" />

        <TextView
            android:id="@+id/confirm"
            android:layout_width="0dp"
            android:layout_height="46dp"
            android:layout_marginStart="@dimen/dp_10"
            android:gravity="center"
            android:text="确认"
            android:textColor="@color/color1E87F0"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/cancel"
            app:layout_constraintTop_toBottomOf="@id/line" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>