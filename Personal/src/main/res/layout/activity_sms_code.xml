<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingLeft="40dp"
    android:paddingRight="40dp">

    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/civ_avatar"
        android:layout_width="86dp"
        android:layout_height="86dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_20"
        android:src="@drawable/default_heading" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="14dp"
        android:layout_marginBottom="33dp"
        android:textColor="@color/text_black"
        android:textSize="@dimen/sp_16" />

    <TextView
        android:id="@+id/tv_phone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_14" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="20dp">

        <EditText
            android:id="@+id/et_code_input"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:background="@null"
            android:hint="请输入验证码"
            android:inputType="number"
            android:maxEms="6"
            android:minWidth="200dp"
            android:singleLine="true"
            android:textColor="@color/text_black"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_send_code"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:text="获取验证码"
            android:textColor="@color/main_blue" />

        <View
            android:layout_width="@dimen/dp_0_5"
            android:layout_height="@dimen/dp_20"
            android:layout_centerVertical="true"
            android:layout_marginRight="40dp"
            android:layout_toLeftOf="@id/tv_send_code"
            android:background="@color/line_grey" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_5"
            android:layout_alignParentBottom="true"
            android:background="@color/line_grey" />
    </RelativeLayout>

    <TextView
        android:id="@+id/btn_confirm"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginTop="48dp"
        android:background="@drawable/shape_tv_corner3"
        android:gravity="center"
        android:text="下一步"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14" />
</LinearLayout>