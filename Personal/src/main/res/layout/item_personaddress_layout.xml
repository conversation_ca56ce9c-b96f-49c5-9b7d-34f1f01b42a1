<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white">
    <TextView
        android:id="@+id/tv_address_name"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:textColor="#222222"
        android:textSize="@dimen/textsize_14"
        android:text="安次区"
        android:layout_centerVertical="true"
        android:layout_marginLeft="14dp"
        />
    <ImageView
        android:id="@+id/item_confirm_iv"
        android:layout_width="17dp"
        android:layout_height="17dp"
        android:src="@drawable/icon_personaddress_confirm"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="14dp"
        android:visibility="gone"
        tools:visibility="visible"
        />
</RelativeLayout>