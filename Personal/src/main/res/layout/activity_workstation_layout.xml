<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="#F4F4F4">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_data_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="invisible"
        tools:visibility="visible"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_org_count"
            android:layout_width="match_parent"
            android:layout_height="19dp"
            android:gravity="center_vertical"
            android:paddingStart="14dp"
            android:textColor="@color/color999999"
            android:textSize="@dimen/sp_12"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_org_count" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--默认空白页面-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:ev_content="还未创建或加入团队"
        tools:visibility="gone"
        app:ev_icon="@drawable/ic_empty_friend"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>