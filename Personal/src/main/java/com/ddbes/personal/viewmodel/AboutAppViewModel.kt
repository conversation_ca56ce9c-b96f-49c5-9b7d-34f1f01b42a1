package com.ddbes.personal.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.ddbes.personal.contract.PersonSettingContract
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.joinutech.ddbeslibrary.VersionConfig
import com.joinutech.ddbeslibrary.bean.AppVersionBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

/**
 * <AUTHOR>
 * @date   2019/5/8 14:22
 * @className: AboutAppViewModel
 *@Description: 类作用描述
 */
class AboutAppViewModel : ViewModel() {

    @Inject
    @field:Named(PersonConstUtil.PERSONSETTING_MODULE)
    lateinit var module: PersonSettingContract.PersonSettingModule
    var validateVersionObservable = MutableLiveData<AppVersionBean>()
    var validateVersionErrorObservable: MutableLiveData<String> = MutableLiveData()
    var versionConfig = VersionConfig()

    init {
        DaggerPersonComponent.builder().build().inject(this)
    }

    fun validateVersion(life: LifecycleTransformer<Result<AppVersionBean>>,
                        client: String, version: String, versionCode: Int) {
        module.validateVersion(life, client, version, versionCode, {
            validateVersionObservable.value = it
        }, {
            validateVersionErrorObservable.value = it
        })
    }

    fun getVersionInfo(): VersionConfig {
        return versionConfig
    }
}