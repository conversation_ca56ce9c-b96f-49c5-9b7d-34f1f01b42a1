package com.ddbes.personal.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.module.ChangePasswordRepository
import com.ddbes.personal.util.PersonConstUtil
import com.joinutech.ddbeslibrary.bean.VerifyImageBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/11/27 14:39
 * @packageName: com.ddbes.personal.viewmodel
 * @Company: JoinuTech
 */
class ChangePasswordViewModel : ViewModel() {

    @Inject
    @field:Named(PersonConstUtil.CHANGE_PASSWORD_REPOSITORY)
    lateinit var module: ChangePasswordRepository
    var getVerifyImageSuccessObservable = MutableLiveData<VerifyImageBean>()
    var getVerifyImageErrorObservable: MutableLiveData<String> = MutableLiveData()
    var verifyImageSuccessObservable = MutableLiveData<Any>()
    var verifyImageErrorObservable: MutableLiveData<String> = MutableLiveData()

    init {
        DaggerPersonComponent.builder().build().inject(this)
    }


    fun getVerifyImage(life: LifecycleTransformer<Result<VerifyImageBean>>, phone: String){
        module.getVerifyImage(life,phone,{
            getVerifyImageSuccessObservable.value =it
        },{
            getVerifyImageErrorObservable.value =it
        })
    }

    fun verifyImage(life: LifecycleTransformer<Result<Any>>, dataMap: Any){
        module.verifyImage(life,dataMap,{
            verifyImageSuccessObservable.value =it
        },{
            verifyImageErrorObservable.value =it
        })
    }

    fun verifyImageWithMsg(life: LifecycleTransformer<Result<Any>>, dataMap: Any){
        module.verifyImageWithMsg(life,dataMap,{
            verifyImageSuccessObservable.value =it
        },{
            verifyImageErrorObservable.value =it
        })
    }
}