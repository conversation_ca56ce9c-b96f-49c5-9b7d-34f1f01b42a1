package com.ddbes.personal.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.module.ModifyOrgInfoModule
import com.ddbes.personal.util.PersonConstUtil
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

/**
 * description ： 修改团队网站，团队联系电话，团队联系邮箱使用
 * author: 黄洁如
 * date : 2019/10/22
 */
class ModifyOrgInfoViewModel :ViewModel(){

    @Inject
    @field:Named(PersonConstUtil.MODIFY_ORG_INFO_MODULE)
    lateinit var module: ModifyOrgInfoModule
    var modifyOrgInfoSuccessObservable = MutableLiveData<Any>()
    var modifyOrgInfoErrorObservable: MutableLiveData<String> = MutableLiveData()

    init {
        DaggerPersonComponent.builder().build().inject(this)
    }

    fun modifyCompany(life: LifecycleTransformer<Result<Any>>,
                      token: String, data: Any){
        module.modifyCompany(life,token,data,{
            modifyOrgInfoSuccessObservable.value =it
        },{
            modifyOrgInfoErrorObservable.value = it
        })
    }

}