package com.ddbes.personal.viewmodel

import android.content.Context
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.ddbes.personal.contract.PersonSettingContract
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.utils.toast
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

/**
 * <AUTHOR>
 * @date   2019/5/5 14:22
 * @className: PersonSettingViewModel
 *@Description: 类作用描述
 */
class PersonSettingViewModel :ViewModel(){

    @Inject
    @field:Named(PersonConstUtil.PERSONSETTING_MODULE)
    lateinit var module: PersonSettingContract.PersonSettingModule
    var loginOutObservable = MutableLiveData<String>()
    var loginOutErrorObservable: MutableLiveData<String> = MutableLiveData()

    init {
        DaggerPersonComponent.builder().build().inject(this)
    }

    fun loginOut(context: Context, life: LifecycleTransformer<Result<String>>, token: String){
        module.loginOut(life,token, onSuccess = {
            loginOutObservable.value =it
        }, onError = {
            loginOutObservable.postValue(it)
        })
    }
}