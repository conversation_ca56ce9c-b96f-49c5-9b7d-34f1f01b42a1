package com.ddbes.personal.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.ddbes.personal.contract.PersonSettingContract
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

class PersonalSafeViewModel : ViewModel() {

    @Inject
    @field:Named(PersonConstUtil.PERSONSETTING_MODULE)
    lateinit var module: PersonSettingContract.PersonSettingModule
    var personalSafeWxBindSuccessObservable = MutableLiveData<Any>()
    var personalSafeWxBindErrorObservable: MutableLiveData<String> = MutableLiveData()
    var personalSafeWxUnBindSuccessObservable = MutableLiveData<Any>()
    var personalSafeWxUnBindErrorObservable: MutableLiveData<String> = MutableLiveData()

    init {
        DaggerPersonComponent.builder().build().inject(this)
    }

    fun wxBind(life: LifecycleTransformer<Result<Any>>, token: String, data: Any) {
        module.wxBind(life, token, data, {
            personalSafeWxBindSuccessObservable.value = it
        }, {
            personalSafeWxBindErrorObservable.value = it
        })
    }

    fun wxUnBind(life: LifecycleTransformer<Result<Any>>, token: String) {
        module.wxUnBind(life, token, {
            personalSafeWxUnBindSuccessObservable.value = it
        }, {
            personalSafeWxUnBindErrorObservable.value = it
        })
    }
}