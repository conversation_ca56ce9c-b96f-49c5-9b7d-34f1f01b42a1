package com.ddbes.personal.contract

import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/7/7.
 */
interface PersonalVerContract {
    interface PersonalVerPresenter {
        fun verifyPw(token:String,pw: String, life: LifecycleTransformer<Result<Any>>,
                     onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun upMobile(token:String,code: String,mobile: String,
                     life: LifecycleTransformer<Result<Any>>,
                     onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }

    interface PersonalVerModule {
        fun verifyPw(token:String,pw: String,life: LifecycleTransformer<Result<Any>>,
                     onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun upMobile(token:String,code: String,mobile: String,
                     life: LifecycleTransformer<Result<Any>>,
                     onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }


}