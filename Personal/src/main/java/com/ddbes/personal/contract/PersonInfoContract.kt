package com.ddbes.personal.contract

import android.content.Context
import android.widget.ImageView
import android.widget.TextView
import com.joinutech.ddbeslibrary.bean.PersonInfoBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import com.trello.rxlifecycle3.components.support.RxAppCompatActivity

/**
 * Description 个人信息Contract
 * Author HJR36
 * Date 2018/6/23 13:36
 */
interface PersonInfoContract {

    interface PersonInfoView {
        fun onSuccess(type: String, result: PersonInfoBean)
        fun onError(error: String)
        fun onDataSuccess(result: PersonInfoBean)
        fun onDataError(error: String)
        fun onUploadPicSuccess(result: Any)
        fun onUploadPicError(error: String)
    }

    interface PersonInfoPresenter {
        fun setView(view: PersonInfoView)
        fun destory()
        fun dealIcon(mContext: Context?)
        fun dealName(persoN_NAME: Int, personName: String)
        fun dealDate(lifeActivity: RxAppCompatActivity, personDateText: TextView)

        fun dealSex(lifeActivity: RxAppCompatActivity, personSexIcon: ImageView, sexName: String,
                    result: (info: String) -> Unit)

//        fun dealPermission()
        fun dealEmail(persoN_NAME: Int, personEmail: String)
        fun dealWork(industrY_REQUEST_CODE: Int, profession: String)
        fun dealAddress(addresS_REQUEST_CODE: Int)
        fun setContext(mContext: Context?)
        fun commitPersonInfo(lifeActivity: RxAppCompatActivity, name: String, type: String)
        fun getData(life: LifecycleTransformer<Result<PersonInfoBean>>)
        fun dealWorkStation(workStation_REQUEST_CODE: Int)
        fun uploadAvatar(life: LifecycleTransformer<Result<Any>>, picPath: String, token: String)
    }

    interface PersonInfoModule {
        fun getData(life: LifecycleTransformer<Result<PersonInfoBean>>,
                    onSuccess: (PersonInfoBean) -> Unit, onError: (String) -> Unit)

        fun commitPersonInfo(activity: RxAppCompatActivity,
                             name: String, type: String,
                             onSuccess: (type: String, PersonInfoBean) -> Unit, onError: (String) -> Unit)
    }
}