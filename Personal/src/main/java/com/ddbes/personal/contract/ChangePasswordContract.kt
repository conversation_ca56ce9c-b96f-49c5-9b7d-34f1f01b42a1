package com.ddbes.personal.contract

import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer

interface ChangePasswordContract {

    interface ChangePasswordPresenter {
        fun upPwd(oldPassword: String, password: String,
                  life: LifecycleTransformer<Result<Any>>,token:String,
                  onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }

    interface ChangePasswordModule {
        fun upPwd(oldPassword: String, password: String,
                  life: LifecycleTransformer<Result<Any>>, token:String,
                  onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }
}