package com.ddbes.personal.contract

import com.joinutech.ddbeslibrary.bean.AppVersionBean
import com.joinutech.ddbeslibrary.bean.PersonSettingBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer

interface PersonSettingContract {

    interface PersonSettingPresenter {
        fun watchSettingData(life: LifecycleTransformer<Result<PersonSettingBean>>,
                             token: String,
                             onSuccess: (PersonSettingBean) -> Unit, onError: (String) -> Unit)

        fun updateSettingData(life: LifecycleTransformer<Result<Any>>,
                              token: String, data: Map<String, Any>,
                              onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }

    interface PersonSettingModule {
        fun watchSettingData(life: LifecycleTransformer<Result<PersonSettingBean>>,
                             token: String,
                             onSuccess: (PersonSettingBean) -> Unit, onError: (String) -> Unit)

        fun updateSettingData(life: LifecycleTransformer<Result<Any>>,
                              token: String, data: Map<String, Any>,
                              onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun loginOut(life: LifecycleTransformer<Result<String>>, token: String,
                     onSuccess: (String) -> Unit, onError: (String) -> Unit)

        fun validateVersion(life: LifecycleTransformer<Result<AppVersionBean>>,
                            client: String, version: String, versionCode: Int,
                            onSuccess: (AppVersionBean) -> Unit, onError: (String) -> Unit)

        fun wxBind(life: LifecycleTransformer<Result<Any>>, token: String,
                   data: Any, onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun wxUnBind(life: LifecycleTransformer<Result<Any>>, token: String,
                     onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }
}