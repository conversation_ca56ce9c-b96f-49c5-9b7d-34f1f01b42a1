package com.ddbes.personal.contract

import com.joinutech.ddbeslibrary.bean.PersonInfoBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import com.trello.rxlifecycle3.components.support.RxAppCompatActivity

interface PersonCommitContract {

    interface PersonCommitPresenter {
        fun commitPersonInfo(activity: RxAppCompatActivity,
                             name: String, type: String,
                             onSuccess: (PersonInfoBean) -> Unit, onError: (String) -> Unit)

        fun commitCooperationOwnEmail(life: LifecycleTransformer<Result<Any>>,
                                      email: String, companyId: String, token: String,
                                      onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }

    interface PersonCommitModule {
        fun commitPersonInfo(activity: RxAppCompatActivity,
                             name: String, type: String,
                             onSuccess: (PersonInfoBean) -> Unit, onError: (String) -> Unit)

        fun commitCooperationOwnEmail(life: LifecycleTransformer<Result<Any>>,
                                      email: String, companyId: String, token: String,
                                      onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }

}