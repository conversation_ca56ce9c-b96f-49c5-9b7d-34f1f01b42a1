package com.ddbes.personal.contract

import com.joinutech.ddbeslibrary.bean.CompanyListBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer

@Deprecated("no used")
interface WorkStationContract {
    interface WorkStationPresenter {
        @Deprecated("no used")
        fun getWorkStationList(life: LifecycleTransformer<Result<CompanyListBean>>,
                               onSuccess: (CompanyListBean) -> Unit, onError: (String) -> Unit)
    }

    interface WorkStationMoudle {
        @Deprecated("no used")
        fun getWorkStationList(life: LifecycleTransformer<Result<CompanyListBean>>,
                               onSuccess: (CompanyListBean) -> Unit, onError: (String) -> Unit)
    }
}