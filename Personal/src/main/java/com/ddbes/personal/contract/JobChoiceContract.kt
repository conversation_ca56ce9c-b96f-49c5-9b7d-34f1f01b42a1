package com.ddbes.personal.contract

import android.content.Context
import com.joinutech.ddbeslibrary.bean.JobChoiceBean
import com.trello.rxlifecycle3.LifecycleTransformer
import com.joinutech.ddbeslibrary.request.Result
/**
 * Description TODO
 * Author HJR36
 * Date 2018/6/21 09:54
 */
interface JobChoiceContract {

    interface JobChoiceView{
        fun onSuccess(result:List<JobChoiceBean>)
        fun onError(error:String)
    }

    interface JobChoicePresenter{
        fun setView(view:JobChoiceView)
        fun destory()
        fun getData(life: LifecycleTransformer<Result<List<JobChoiceBean>>>, context: Context?,
                    accessToken: String?)
    }
    interface JobChoiceModule{
        fun getData(life: LifecycleTransformer<Result<List<JobChoiceBean>>>, context: Context?,
                    accessToken: String?
                    , onSuccess :(List<JobChoiceBean>)-> Unit, onError :(String) ->Unit)
    }
}