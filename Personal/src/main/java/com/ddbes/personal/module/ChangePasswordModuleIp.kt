package com.ddbes.personal.module

import com.ddbes.personal.contract.ChangePasswordContract
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.PersonService
import com.trello.rxlifecycle3.LifecycleTransformer

class ChangePasswordModuleIp :ChangePasswordContract.ChangePasswordModule {

    override fun upPwd(oldPassword: String, password: String,
                       life: LifecycleTransformer<Result<Any>>, token:String,
                       onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        val params = mapOf(Pair("oldPassword",oldPassword),
                Pair("password", password))
        PersonService.upupPwd(params,token)
                .compose(life).
                        compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>(){
                    override fun onError(ex: ApiException) {
                        if (ex.code == 1105){
                            onError.invoke("输入的旧密码错误")
                        }else {
                            onError.invoke(ex.message)
                        }
                    }

                    override fun onComplete() {}

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }
                })
    }

}