package com.ddbes.personal.module

import com.ddbes.personal.contract.WorkStationContract
import com.joinutech.ddbeslibrary.bean.CompanyListBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

class WorkStationModuleIp @Inject internal constructor() : WorkStationContract.WorkStationMoudle {

    override fun getWorkStationList(life: LifecycleTransformer<Result<CompanyListBean>>,
                                    onSuccess: (CompanyListBean) -> Unit, onError: (String) -> Unit) {
//        AddressbookService.getCompanies().compose(life)
//                .compose(ErrorTransformer.getInstance<CompanyListBean>())
//                .subscribe(object : BaseSubscriber<CompanyListBean>() {
//                    override fun onError(ex: ApiException) {
//                        onError.invoke(ex.message)
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(t: CompanyListBean?) {
//                        onSuccess.invoke(t!!)
//                    }
//
//                })
    }
}