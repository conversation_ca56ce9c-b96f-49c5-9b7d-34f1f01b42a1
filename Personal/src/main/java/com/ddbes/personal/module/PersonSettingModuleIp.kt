package com.ddbes.personal.module

import com.ddbes.library.im.ImService
import com.ddbes.personal.contract.PersonSettingContract
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.bean.AppVersionBean
import com.joinutech.ddbeslibrary.bean.PersonSettingBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.LoginService
import com.joinutech.ddbeslibrary.service.PersonService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

class PersonSettingModuleIp @Inject internal constructor() :
        PersonSettingContract.PersonSettingModule {

    override fun watchSettingData(life: LifecycleTransformer<Result<PersonSettingBean>>,
                                  token: String, onSuccess: (PersonSettingBean) -> Unit,
                                  onError: (String) -> Unit) {
        PersonService.getPersonSettingData(token)
                .compose(life)
                .compose(ErrorTransformer.getInstance<PersonSettingBean>())
                .subscribe(object : BaseSubscriber<PersonSettingBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {}

                    override fun onNext(t: PersonSettingBean?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun updateSettingData(life: LifecycleTransformer<Result<Any>>, token: String,
                                   data: Map<String, Any>,
                                   onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        PersonService.updatePersonSetting(token, data)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {}

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun loginOut(life: LifecycleTransformer<Result<String>>, token: String,
                          onSuccess: (String) -> Unit, onError: (String) -> Unit) {
        LoginService.loginOut(token)
                .compose(life)
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<String>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {}

                    override fun onNext(t: String?) {
                        //tcp退出登录APP成功，退出app成功，处理位置1
                        if (t != null) {
                            onSuccess.invoke(t)
                        } else {
                            onSuccess.invoke("退出成功")
                        }
                    }
                })
    }


    //检查更新tcp,调接口
    override fun validateVersion(life: LifecycleTransformer<Result<AppVersionBean>>,
                                 client: String, version: String, versionCode: Int,
                                 onSuccess: (AppVersionBean) -> Unit, onError: (String) -> Unit) {
        PersonService.validateVersion(client, version, versionCode)
                .compose(life)
                .compose(ErrorTransformer.getInstance<AppVersionBean>())
                .subscribe(object : BaseSubscriber<AppVersionBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {}

                    override fun onNext(t: AppVersionBean?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun wxBind(life: LifecycleTransformer<Result<Any>>, token: String,
                        data: Any, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        LoginService.wxBindSafe(token, data)
                .compose(life)
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        if (t != null) {
                            onSuccess.invoke(t)
                        }
                    }

                })
    }

    override fun wxUnBind(life: LifecycleTransformer<Result<Any>>, token: String,
                          onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        LoginService.wxUnBind(token)
                .compose(life)
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        if (t != null) {
                            onSuccess.invoke(t)
                        }
                    }

                })
    }
}