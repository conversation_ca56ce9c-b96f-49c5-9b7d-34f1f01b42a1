package com.ddbes.personal.module

import com.ddbes.personal.contract.PersonCommitContract
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.bean.PersonInfoBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.joinutech.ddbeslibrary.service.PersonService
import com.trello.rxlifecycle3.LifecycleTransformer
import com.trello.rxlifecycle3.components.support.RxAppCompatActivity

class PersonCommitModuleIp : PersonCommitContract.PersonCommitModule {

    override fun commitPersonInfo(activity: RxAppCompatActivity,
                                  name: String,
                                  type: String,
                                  onSuccess: (PersonInfoBean) -> Unit, onError: (String) -> Unit) {
        val data = HashMap<String, Any>()
        when (type) {
            "name" -> data["name"] = name
            "email" -> data["email"] = name
            "company" -> data["companyId"] = name
            "address" -> data["address"] = name
            "profession" -> data["profession"] = name
        }
        PersonService.commitPersonInfo(data, UserHolder.getAccessToken())// name email company address professino
                .compose(activity.bindToLifecycle())
                .compose(ErrorTransformer.getInstance<PersonInfoBean>())
                .subscribe(object : BaseSubscriber<PersonInfoBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {}

                    override fun onNext(t: PersonInfoBean?) {
                        if (t != null) {
                            onSuccess.invoke(t)
                        } else {
                            onError.invoke("修改失败")
                        }
                    }

                })
    }

    override fun commitCooperationOwnEmail(life: LifecycleTransformer<Result<Any>>,
                                           email: String, companyId: String, token: String,
                                           onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.commitCooperationOwnEmail(companyId, email, token)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {}

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

}