package com.ddbes.personal.module

import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

/**
 * description ： 修改团队信息Module
 * author: 黄洁如
 * date : 2019/10/22
 */

class ModifyOrgInfoModule @Inject internal constructor() {

    fun modifyCompany(life: LifecycleTransformer<Result<Any>>,
                               token: String, data: Any,
                      onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.modifyCompany(token, data)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}