package com.ddbes.personal.module

import android.annotation.SuppressLint
import android.content.Context
import com.joinutech.ddbeslibrary.request.Result
import com.ddbes.personal.contract.PersonalVerContract
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.request.exception.NoDataTransformer
import com.joinutech.ddbeslibrary.service.LoginService
import com.joinutech.ddbeslibrary.service.PersonService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

/**
 * Created by liuchuchu on 2018/7/7.
 */
class PersonalVerModuleIp @Inject internal constructor() : PersonalVerContract.PersonalVerModule {

    @SuppressLint("CheckResult")
    override fun verifyPw(token:String,pw: String,
                          life: LifecycleTransformer<Result<Any>>,
                          onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        val params = mapOf(Pair("password", pw))
        PersonService
                .verifyPersonal(params,token).compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
            override fun onComplete() {
            }

            override fun onError(ex: ApiException) {
             onError.invoke(ex.message)
            }

            override fun onNext(t: Any?) {
                onSuccess.invoke(t!!)
            }

        }
        )
    }

    //更换手机号
    @SuppressLint("CheckResult")
    override fun upMobile(token:String,code: String,mobile: String,
                          life: LifecycleTransformer<Result<Any>>,
                          onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        val params = mapOf(Pair("code",code),
               Pair("mobile",mobile),
        Pair("version","v2"))
        PersonService.upMobile(params,token)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object :BaseSubscriber<Any>(){
            override fun onError(ex: ApiException) {
                onError.invoke(ex.message)
            }

            override fun onComplete() {
            }

            override fun onNext(t: Any?) {
                onSuccess.invoke(t!!)
            }
        })
    }
}