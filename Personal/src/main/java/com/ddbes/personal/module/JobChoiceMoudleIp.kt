package com.ddbes.personal.module

import android.content.Context
import com.joinutech.ddbeslibrary.bean.JobChoiceBean
import com.joinutech.ddbeslibrary.request.Result
import com.ddbes.personal.contract.JobChoiceContract
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.PersonService
import com.joinutech.ddbeslibrary.utils.NetErrorDialog
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

/**
 * Description 职业选择Moudle
 * Author HJR36
 * Date 2018/6/21 10:27
 */
class JobChoiceMoudleIp @Inject internal constructor() : JobChoiceContract.JobChoiceModule {

    override fun getData(life: LifecycleTransformer<Result<List<JobChoiceBean>>>, context: Context?,
                         accessToken: String?,
                         onSuccess: (List<JobChoiceBean>) -> Unit, onError: (String) -> Unit) {
        PersonService.getJobChoice(accessToken!!)
                .compose(life)
                .compose(ErrorTransformer.getInstance<List<JobChoiceBean>>())
                .subscribe(object : BaseSubscriber<List<JobChoiceBean>>() {
                    override fun onComplete() {}

                    override fun onNext(t: List<JobChoiceBean>) {
                        onSuccess.invoke(t)
                    }

                    override fun onError(ex: ApiException) {
                        when (ex.message) {
                            "连接失败" -> NetErrorDialog.netSlowDialog(context!!,
                                    onComplete(), cancel())
                            "没有网络" -> NetErrorDialog.netIsNotAvailable(context!!)
                        }
                        onError.invoke(ex.message)
                    }
                })
    }
}