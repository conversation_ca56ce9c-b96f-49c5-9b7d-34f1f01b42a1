package com.ddbes.personal.module

import com.ddbes.personal.contract.PersonInfoContract
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.bean.PersonInfoBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.PersonService
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.trello.rxlifecycle3.LifecycleTransformer
import com.trello.rxlifecycle3.components.support.RxAppCompatActivity
import javax.inject.Inject

/**
 * Description 个人信息module
 * Author HJR36
 * Date 2018/6/23 13:42
 */
class PersonInfoModuleIp @Inject internal constructor() : PersonInfoContract.PersonInfoModule {

    override fun commitPersonInfo(lifeActivity: RxAppCompatActivity, name: String, type: String,
                                  onSuccess: (type: String, PersonInfoBean) -> Unit,
                                  onError: (String) -> Unit) {
        val data = HashMap<String, Any>()
        when (type) {
            "avatar" -> data["avatar"] = name
            "birthday" -> data["birthday"] = name
            "gender" -> {
                //gender (integer, optional): 性别 0.未设置 1.男 2.女 ,
                if (StringUtils.isNotBlankAndEmpty(name)) {
                    if (name == "男") data["gender"] = 1
                    else data["gender"] = 2
                } else {
                    data["gender"] = 0
                }
            }
        }
//        val token = mmkv.decodeString( USER.TOKEN,"")
        PersonService.commitPersonInfo(data, UserHolder.getAccessToken())// avatar birthday gender
                .compose(lifeActivity.bindToLifecycle())
                .compose(ErrorTransformer.getInstance<PersonInfoBean>())
                .subscribe(object : BaseSubscriber<PersonInfoBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {}

                    override fun onNext(t: PersonInfoBean?) {
                        if (t != null) {
                            onSuccess.invoke(type, t)
                        } else {
                            onError.invoke("修改失败")
                        }
                    }
                })
    }

    override fun getData(life: LifecycleTransformer<Result<PersonInfoBean>>,
                         onSuccess: (PersonInfoBean) -> Unit, onError: (String) -> Unit) {
        PersonService.getPersonInfo(UserHolder.getAccessToken())
                .compose(life)
                .compose(ErrorTransformer.getInstance<PersonInfoBean>())
                .subscribe(object : BaseSubscriber<PersonInfoBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {}

                    override fun onNext(t: PersonInfoBean?) {
                        onSuccess.invoke(t!!)
//                        RetrofitUrlManager.getInstance().setGlobalDomain(BASE_URL_2)
//                        Toast.makeText(BaseApplication.joinuTechContext, "全局替换baseUrl成功", Toast.LENGTH_SHORT).show()
                    }

                })
    }

}