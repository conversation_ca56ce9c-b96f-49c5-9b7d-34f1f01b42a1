package com.ddbes.personal.module

import com.joinutech.ddbeslibrary.bean.VerifyImageBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.LoginService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/11/27 14:41
 * @packageName: com.ddbes.personal.module
 * @Company: JoinuTech
 */
class ChangePasswordRepository @Inject internal constructor() {

    fun getVerifyImage(life: LifecycleTransformer<Result<VerifyImageBean>>, phone: String,
                                onSuccess: (VerifyImageBean) -> Unit, onError: (String) -> Unit) {
        //获取图片验证码
        LoginService.getVerifyImage(phone)
                .compose(life)
                .compose(ErrorTransformer.getInstance<VerifyImageBean>())
                .subscribe(object : BaseSubscriber<VerifyImageBean>() {
                    override fun onError(ex: ApiException) {
                        //该手机号未注册
                        onError.invoke(if (ex.code == 1104) {
                            "该手机号未注册"
                        } else ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: VerifyImageBean?) {
                        if (t!=null){
                            onSuccess.invoke(t)
                        }
                    }

                })
    }

    fun verifyImage(life: LifecycleTransformer<Result<Any>>, dataMap: Any,
                             onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        //校验图片验证码
        LoginService.verifyImage(dataMap)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        if (ex.code == 1500){
                            onError.invoke("匹配失败")
                        }else onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        if (t!=null){
                            onSuccess.invoke(t)
                        }
                    }

                })
    }

    fun verifyImageWithMsg(life: LifecycleTransformer<Result<Any>>, dataMap: Any,
                                    onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        //验证图片验证码带短信验证码
        LoginService.verifyImageWithMsg(dataMap)//修改密码验证 图片并发送验证码
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        if (t!=null){
                            onSuccess.invoke(t)
                        }
                    }

                })
    }
}