package com.ddbes.personal.view

import android.app.Activity
import android.content.Intent
import android.view.LayoutInflater
import androidx.core.content.ContextCompat
import android.view.View
import android.widget.AdapterView
import android.widget.ListView
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.ddbes.personal.R
import com.ddbes.personal.adapter.MainAdapter
import com.ddbes.personal.adapter.MoreAdapter
import com.joinutech.ddbeslibrary.bean.JobChoiceBean
import com.ddbes.personal.contract.JobChoiceContract
import com.ddbes.personal.contract.PersonCommitContract
import com.ddbes.personal.databinding.ActivityJobChoiceBinding
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.utils.ToastUtil
import javax.inject.Inject
import javax.inject.Named

/**
 * Description 职业选择
 * Author HJR36
 * Date 2018/6/21 09:36
 */
class JobChoiceActivity : MyUseBindingActivity<ActivityJobChoiceBinding>(), JobChoiceContract.JobChoiceView {
    override fun openArouterReceive(): Boolean {
        return false
    }

    private var moreAdapter: MoreAdapter? = null
    private var mainList: List<JobChoiceBean>? = null
    private var mainAdapter: MainAdapter? = null
    @Inject
    @field:Named(PersonConstUtil.PERSON_JOBCHOICE_PRESENTER)
    lateinit var presenter: JobChoiceContract.JobChoicePresenter
    @Inject
    @field:Named(PersonConstUtil.PERSON_COMMIT_PRESENTER)
    lateinit var commitPresenter: PersonCommitContract.PersonCommitPresenter

    override fun initImmersion() {
        setPageTitle("修改职业")
        setLeftTitle("取消", View.OnClickListener { finish() })
        setRightTitleColor(ContextCompat.getColor(mContext!!, R.color.color1A76F1),
                "保存", this)
        showToolBarLine()
    }

    override val contentViewResId: Int
        get() = R.layout.activity_job_choice

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityJobChoiceBinding {
        return ActivityJobChoiceBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean {
        return true
    }




    override fun initView() {
        whiteStatusBarBlackFont()
        DaggerPersonComponent.builder().build().inject(this)
        presenter.setView(this)
    }

        override fun initLogic() {
        super.initLogic()
        getLoadingDialog("请求职业信息", false)
        presenter.getData(bindToLifecycle<Result<List<JobChoiceBean>>>(), mContext, accessToken)
    }

    override fun onSuccess(result: List<JobChoiceBean>) {
        dismissDialog()
        binding.llAll.visibility = View.VISIBLE
        mainList = result
        mainAdapter = MainAdapter(mContext, mainList)
        mainAdapter!!.selectItem = 0
        binding.classifyMainlist.adapter = mainAdapter
        binding.classifyMainlist.onItemClickListener = AdapterView.OnItemClickListener { _, _, position, _ ->
            val lists = mainList!![position].son
            if (lists != null) {
                initAdapter(lists)
            }
            mainAdapter!!.selectItem = position
            mainAdapter!!.notifyDataSetChanged()
        }
        binding.classifyMainlist.choiceMode = ListView.CHOICE_MODE_SINGLE
        // 一定要设置这个属性，否则ListView不会刷新
        initAdapter(mainList!![0].son!!)

        binding.classifyMorelist.onItemClickListener = AdapterView.OnItemClickListener { _, _, position, _ ->
            moreAdapter!!.getItem(position) as JobChoiceBean.IndustrysBean
            moreAdapter!!.selectItem = position
            moreAdapter!!.notifyDataSetChanged()
        }
    }

    override fun onError(error: String) {
        dismissDialog()
        if (StringUtils.isNotBlankAndEmpty(error)) {
            setShowEmptyView(true)
        }
    }

    //刷新网络请求
    override fun onEmptyRefresh() {
        presenter.getData(bindToLifecycle<Result<List<JobChoiceBean>>>(), mContext, accessToken)
    }

    private fun initAdapter(lists: List<JobChoiceBean.IndustrysBean>) {
        moreAdapter = MoreAdapter(this, lists)
        binding.classifyMorelist.adapter = moreAdapter
        moreAdapter!!.notifyDataSetChanged()
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            com.joinutech.ddbeslibrary.R.id.tv_toolbar_right -> {
                val industry = mainList!![mainAdapter!!.selectItem]
                        .son!![moreAdapter!!.selectItem].name
                getLoadingDialog("提交职业选择", false)
                if (industry != null) {
                    commitPresenter.commitPersonInfo(this, industry, "profession", {
                        dismissDialog()
                        setResult(Activity.RESULT_OK, Intent().putExtra("industry", industry))
                        finish()
                    }, {
                        dismissDialog()
                        ToastUtil.show(mContext!!, it)
                    })
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        presenter.destory()
    }
}