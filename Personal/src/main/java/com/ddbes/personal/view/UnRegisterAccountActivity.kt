package com.ddbes.personal.view

import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import com.ddbes.library.im.netutil.ImNetUtil
import com.ddbes.personal.R
import com.ddbes.personal.databinding.ActivityUnregisterAccountBinding
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.VersionConfig
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.LoginService
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils

/**
 * @PackageName: com.ddbes.personal.view
 * @ClassName:UnRegisterAccountActivity
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/10/9 11:07
 * @Desc: 未注册账号页面
 */
class UnRegisterAccountActivity : MyUseBindingActivity<ActivityUnregisterAccountBinding>() {
    override fun initImmersion() {
        whiteStatusBarBlackFont()
    }

    private val title = "注销担当账号"
    private var code = ""
    override fun initView() {
        setPageTitle(title)
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        showToolBarLine()

        code = intent.getStringExtra("code") ?: ""
        binding.btnConfirm.setOnClickListener(this)
        if (UserHolder.isLogin()) {
            UserHolder.getCurrentUser()?.let {
                ImageLoaderUtils.loadImage(this, findViewById(R.id.civ_avatar), it.avatar)
                binding.tvName.text = it.name
            }
        }
    }

    override fun initLogic() {
        super.initLogic()
        checkGroup()
    }

    override fun onNoDoubleClick(v: View) {
        if (v.id == R.id.btn_confirm) {
            checkUnRegister()
        }
    }

    private var haveGroup = true

    private fun checkGroup() {
        ImNetUtil.getAppGroupListByType(bindToLifecycle(), 2,
                onSuccess = {
                    haveGroup = !it.isNullOrEmpty()
                },
                onFailer = {})
    }

    private fun checkUnRegister() {

        fun haveThirdAccount(): Boolean {
            return UserHolder.isLogin() && !UserHolder.getCurrentUser()!!.nickName.isNullOrEmpty()
        }

        fun haveOrg(): Boolean {
            return CompanyHolder.getTotalCompanies().isNotEmpty()
        }

//        if (haveOrg()) {
//            showLog("您还未退出或解散所有团队")
//        }
//        if (haveGroup) {
//            showLog("您还未退出或解散所有群组")
//        }
//        if (haveThirdAccount()) {
//            showLog("请解绑关联的第三方账号")
//        }
////                toastShort("调用注销接口")
//        if (code.isNullOrBlank() || code.length != 4) {
//            toastShort("注销失败")
//            return
//        }
//        unRegisterAccount()

        when {
            haveOrg() -> {
                showDialog("您还未退出或解散所有团队")
            }
            haveGroup -> {
                showDialog("您还未退出或解散所有群组")
            }
            haveThirdAccount() -> {
                showDialog("请解绑关联的第三方账号")
            }
            else -> {
//                toastShort("调用注销接口")
                if (code.isNullOrBlank() || code.length != 4) {
                    toastShort("注销失败")
                    return
                }
                unRegisterAccount()
            }
        }
    }

    private fun unRegisterAccount() {
        showLoading()
//        LoginService.unRegisterAccount(code, MMKVUtil.getString(MMKVKEY.VERSION_NAME))
        LoginService.unRegisterAccount(code, VersionConfig().versionName)
                .compose(bindToLifecycle())
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        hideLoading()
                        toastShort("注销失败 ${ex.message}")
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(t: Any?) {
                        hideLoading()
                        toastShort("注销成功")
                        onUserLogout()
                    }
                })
    }

    private fun showDialog(info: String) {
        val dialog = object : DialogHolder(this, com.joinutech.ddbeslibrary.R.layout.dialog_apr_base, Gravity.CENTER) {
            override fun bindView(dialogView: View) {
                XUtil.hideView(dialogView.findViewById(com.joinutech.ddbeslibrary.R.id.tv_title),
                        dialogView.findViewById(com.joinutech.ddbeslibrary.R.id.line_v),
                        dialogView.findViewById(R.id.confirm))
                val content = dialogView.findViewById<TextView>(R.id.tv_content)
                content.setTextColor(resources.getColor(com.joinutech.ddbeslibrary.R.color.text_black))
                content.text = "无法注销担当账号"
                val hint = dialogView.findViewById<TextView>(R.id.tv_hint)
                hint.setTextColor(resources.getColor(com.joinutech.ddbeslibrary.R.color.text_black))
                hint.text = info
                val cancel = dialogView.findViewById<TextView>(R.id.cancel)
                cancel.setTextColor(resources.getColor(com.joinutech.ddbeslibrary.R.color.text_black))
                cancel.text = "我知道了"
                cancel.setOnClickListener {
                    dialog?.dismiss()
                }
            }
        }
        dialog.initView()
        dialog.show(true)
    }

    override val contentViewResId: Int = R.layout.activity_unregister_account
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityUnregisterAccountBinding {
        return ActivityUnregisterAccountBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = false

}