package com.ddbes.personal.view

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.view.LayoutInflater
import android.view.View
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.personal.R
import com.ddbes.personal.contract.PersonInfoContract
import com.ddbes.personal.databinding.ActivityPersoninfoLayoutBinding
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.joinutech.common.util.CacheDataHolder
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.PersonInfoBean
import com.joinutech.ddbeslibrary.bean.QrCodeData
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.utils.PictureNewHelper
import com.joinutech.ddbeslibrary.utils.RoutePersonal
import com.joinutech.ddbeslibrary.utils.ScreenUtils
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.utils.isNotBlankAndEmpty
import com.joinutech.ddbeslibrary.utils.toast
import com.joinutech.flutter.EventUpdateUserInfo
import org.greenrobot.eventbus.EventBus
import javax.inject.Inject
import javax.inject.Named


/**
 * Description 个人资料
 * Author HJR36
 * Date 2018/6/20 15:04
 */
@Route(path = RoutePersonal.personalInfo)
class PersonInfoActivity(override val contentViewResId: Int = R.layout.activity_personinfo_layout)
    : MyUseBindingActivity<ActivityPersoninfoLayoutBinding>(), PersonInfoContract.PersonInfoView {

    private var userInfo: PersonInfoBean? = null

    override fun openArouterReceive(): Boolean {
        return false
    }

    // 图片裁剪
    private val IMAGE_CUT_CODE = 0x000003

    //名字
    private val PERSON_NAME = 0x000004

    //职业请求码
    private val INDUSTRY_REQUEST_CODE = 0x000005

    //区域请求码
    private val ADDRESS_REQUEST_CODE = 0x000006

    //主要企业请求码
    private val WORKSTATION_REQUEST_CODE = 0x000007

    @Inject
    @field:Named(PersonConstUtil.PERSON_PERSONINFO_PRESENTER)
    lateinit var presenter: PersonInfoContract.PersonInfoPresenter
    private var mainCompanyInfoChange = false

    override fun initImmersion() {
        setPageTitle("个人资料")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey, View.OnClickListener { onBackPressed() })
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        DaggerPersonComponent.builder().build().inject(this)
        presenter.setView(this)
        presenter.setContext(mContext)
    }

    override fun initLogic() {
        super.initLogic()
        //生成二维码
        binding.rlIconLayout.setOnClickListener(this)
        binding.rlNameLayout.setOnClickListener(this)
        binding.rlBirthdayLayout.setOnClickListener(this)
        binding.rlSexLayout.setOnClickListener(this)
        binding.rlWorkLayout.setOnClickListener(this)
        binding.rlEmailLayout.setOnClickListener(this)
        binding.rlAreaLayout.setOnClickListener(this)
        binding.rlCompanyLayout.setOnClickListener(this)
        binding.rlQrLayout.setOnClickListener(this)

        getLoadingDialog("获取个人资料", false)
        presenter.getData(bindToLifecycle<Result<PersonInfoBean>>())
    }

    override fun onDataSuccess(result: PersonInfoBean) {
        userInfo = result
        dismissDialog()
        binding.tvName.text = result.name
        if (StringUtils.isNotBlankAndEmpty(result.birthday) && result.birthday!!.length >= 10) {
            binding.tvBirthday.text = result.birthday!!.substring(0, 10)
        }
        when (result.gender) {
            "0" -> binding.tvSex.text = ""
            "1" -> {
                binding.tvSex.text = "男"
                binding.ivSexIcon.setImageResource(R.drawable.icon_personinfo_man_small)
            }
            "2" -> {
                binding.tvSex.text = "女"
                binding.ivSexIcon.setImageResource(R.drawable.icon_personinfo_girl_small)
            }
        }
        binding.tvWork.text = result.profession
        binding.tvEmail.text = result.email
        binding.tvArea.text = result.address
        CacheDataHolder.showLog("userInfo companyId = ${result.companyId}," +
                "current mainCompanyId = ${CompanyHolder.getMainOrg()?.companyId}")
        binding.tvCompanyName.text = CompanyHolder.getMainOrg()?.name
        if (isNotBlankAndEmpty(result.avatar)) {
//            tv_person_header_name.visibility = View.GONE
            ImageLoaderUtils.loadImage(mContext!!, binding.ivHeader, result.avatar)
        }
    }

    override fun onDataError(error: String) {
        dismissDialog()
        ToastUtil.show(mContext!!, "获取个人资料失败$error")
        finish()
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.rl_icon_layout -> {
                presenter.dealIcon(mContext)
            }
            R.id.rl_name_layout -> {
                if (StringUtils.isNotBlankAndEmpty(binding.tvName.text.toString())) {
                    presenter.dealName(PERSON_NAME, binding.tvName.text.toString())
                } else {
                    presenter.dealName(PERSON_NAME, "")
                }

            }
            R.id.rl_birthday_layout -> {
                presenter.dealDate(this, binding.tvBirthday)
            }
            R.id.rl_sex_layout -> {
                presenter.dealSex(this, binding.ivSexIcon, binding.tvSex.text.toString()) {
                    binding.tvSex.text = it
                }
            }
            R.id.rl_work_layout -> {
                presenter.dealWork(INDUSTRY_REQUEST_CODE, binding.tvWork.text.toString())
            }
            R.id.rl_email_layout -> {
                if (StringUtils.isNotBlankAndEmpty(binding.tvEmail.text.toString())) {
                    presenter.dealEmail(PERSON_NAME, binding.tvEmail.text.toString())
                } else {
                    presenter.dealEmail(PERSON_NAME, "")
                }
            }
            R.id.rl_area_layout -> {//地区
                // 进去选择地区
                presenter.dealAddress(ADDRESS_REQUEST_CODE)
            }
            R.id.rl_company_layout -> {
                //打开个人主要企业设置
                presenter.dealWorkStation(WORKSTATION_REQUEST_CODE)
            }
            R.id.rl_qr_layout -> {
                val member = QrCodeData(
                        headImg = userInfo?.avatar ?: "",
                        name = binding.tvName.text.toString(),
                        userId = userId!!,
                        content = "r.$userId")
                ARouter.getInstance()
                        .build(RoutePersonal.orgQrCode)
                        .withSerializable("qrCodeData", member)
                        .navigation()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                IMAGE_CUT_CODE -> {
                    if (data != null) {
                        // 图片选择结果回调
                        val selectList = PictureNewHelper.afterSelectPhotoPaths(data)
//                        val selectList = PictureSelector.obtainMultipleResult(data)
                        // 例如 LocalMedia 里面返回三种path
                        // 1.media.getPath(); 为原图path
                        // 2.media.getCutPath();为裁剪后path，需判断media.isCut();是否为true
                        // 3.media.getCompressPath();为压缩后path，需判断media.isCompressed();是否为true
                        // 如果裁剪并压缩了，已取压缩路径为准，因为是先裁剪后压缩的
                        if (selectList.isNotEmpty()) {
//                            tv_person_header_name.visibility = View.GONE
                            val picPath = selectList[0]
                            if (isNotBlankAndEmpty(picPath)) {
                                presenter.uploadAvatar(bindToLifecycle<Result<Any>>(), picPath, accessToken!!)
                            }
                        }
                    }
                }
                PERSON_NAME -> {
                    val type = data!!.getStringExtra("tag")
                    if (type == "name") {
                        binding.tvName.text = data.getStringExtra("name")
                    } else if (type == "email") {
                        binding.tvEmail.text = data.getStringExtra("name")
                    }
                }
                ADDRESS_REQUEST_CODE -> {
                    val address = data!!.getStringExtra("address")
                    binding.tvArea.text = address
                }
                INDUSTRY_REQUEST_CODE -> {
                    val industry = data!!.getStringExtra("industry")
                    binding.tvWork.text = industry
                }
                WORKSTATION_REQUEST_CODE -> {
                    mainCompanyInfoChange = true
                    val companyName = data!!.getStringExtra("companyName")
                    binding.tvCompanyName.text = companyName
                }

            }
        } else {
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

    override fun onUploadPicSuccess(result: Any) {
        //上传成功
        dismissDialog()
        val path = result as String
        if (StringUtils.isNotBlankAndEmpty(path)) {
            getLoadingDialog("上传头像", false)
            presenter.commitPersonInfo(this, path, "avatar")

        }
    }

    override fun onUploadPicError(error: String) {
        //上传图片失败
        dismissDialog()
        toast(error)
    }

    var avatarChanged = false

    override fun onSuccess(type: String, result: PersonInfoBean) {
        dismissDialog()
        userInfo = result
        if (type == "avatar") {
            avatarChanged = true
            if (isNotBlankAndEmpty(result.avatar)) {
                ImageLoaderUtils.loadImage(mContext!!, binding.ivHeader, result.avatar)
                EventBus.getDefault().post(EventUpdateUserInfo())
            }
        }
        UserHolder.onLogin(result, true)// change avatar
        ToastUtil.show(mContext!!, "设置成功")
    }

    override fun onError(error: String) {
        dismissDialog()
        ToastUtil.show(mContext!!, "提交失败")
    }

    override fun onDestroy() {
        super.onDestroy()
        presenter.destory()
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityPersoninfoLayoutBinding {
        return ActivityPersoninfoLayoutBinding.inflate(layoutInflater)
    }


}