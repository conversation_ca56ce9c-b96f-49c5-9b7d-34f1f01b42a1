package com.ddbes.personal.view

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.activity.viewModels
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bumptech.glide.Glide
import com.ddbes.library.im.ImService
import com.ddbes.library.im.imfile.tcpfileutil.DealFileUtil
import com.ddbes.library.im.imtcp.ConstantTcpUtil
import com.ddbes.library.im.imtcp.tcpcacheutil.OpenTopCacheHolder
import com.ddbes.library.im.netapi.ImApiUtil
import com.ddbes.library.im.util.LoginHelper
import com.ddbes.personal.R
import com.ddbes.personal.databinding.ActivityPerSettingBinding
import com.ddbes.personal.viewmodel.PersonSettingViewModel
//import com.joinu.jpush.DDJpushManager
//import com.joinu.jpush.PushUtil
import com.joinutech.common.util.NetUtil
import com.joinutech.common.util.NotifyUtil
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.RxScheduleUtil
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.DOWNLOAD_DIR
import com.joinutech.ddbeslibrary.utils.FileIOUtils
import com.joinutech.ddbeslibrary.utils.MyDialog
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.toast
import com.joinutech.flutter.EventClearFlutterCache
import com.joinutech.flutter.EventFinishPage
import com.joinutech.flutter.EventLoginOut
import com.trello.rxlifecycle3.LifecycleTransformer
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import java.io.File


/**
 * 个人设置
 * Created by liuchuchu on 2018/6/19.
 */
class PersonalSettingActivity : MyUseBindingActivity<ActivityPerSettingBinding>() {

    override val contentViewResId: Int = R.layout.activity_per_setting
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityPerSettingBinding {
        return ActivityPerSettingBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = false

    val vm : SettingPageViewModel by viewModels()

    private val MSG_CLEAR_CACHE_DATA_SUCC = 3201

    private lateinit var viewModel: PersonSettingViewModel

    override fun initImmersion() {
        setPageTitle("设置")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        showToolBarLine()
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        viewModel = getModel(PersonSettingViewModel::class.java)
    }

    override fun initLogic() {
        super.initLogic()
        //缓存设置
        getLoginOutObservable()
        binding.layout1PerSetting.setOnClickListener(this)
        binding.layout2PerSetting.setOnClickListener(this)
        binding.layout3PerSetting.setOnClickListener(this)
        binding.layout4PerSetting.setOnClickListener(this)
        binding.logoutPerSetting.setOnClickListener(this)

        vm.cacheSizeData.observe(this) {
            binding.cacheText.text = getString(R.string.total, it)
        }

//        vm.loginDeviceLiveData.observe(this) {
//            binding.loginDeviceTv.text = it.toString()
//        }

        vm.loadingData.observe(this) {
            if (it) {
                getLoadingDialog("正在清空缓存", false)
            }else{
                dismissDialog()
            }
        }

//        vm.loginDevices(this , bindToLifecycle() , imToken = UserHolder.getImToken() ?: "")

        vm.loadLocalCache(this)
    }

    @SuppressLint("WrongConstant")
    override fun onNoDoubleClick(v: View) {
        super.onNoDoubleClick(v)
        when (v.id) {
            R.id.layout1_per_setting -> {//账号与安全
                val intent1 = Intent(this@PersonalSettingActivity,
                        PersonalSafeActivity::class.java)
                startActivity(intent1)
            }
            R.id.layout2_per_setting -> {//新消息通知
                val intent1 = Intent(this@PersonalSettingActivity,
                        PersonalNotifyActivity::class.java)
                startActivity(intent1)
            }
            R.id.layout3_per_setting -> {//隐私
                val intent1 = Intent(this@PersonalSettingActivity,
                        PersonalPrivacyActivity::class.java)
                startActivity(intent1)
            }
            R.id.layout4_per_setting -> {//点击清除缓存tcp
                val dialog = MyDialog(mContext!!, 0, 0, "",
                        needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0)
                val view = View.inflate(mContext!!, R.layout.dialog_logout, null)
                val logoutDetailText = view.findViewById<TextView>(R.id.logoutDetail)
                val logoutText = view.findViewById<TextView>(R.id.logout)
                logoutText.text = "清除缓存"
                logoutDetailText.text = vm.cacheSizeData.value ?: ""
                dialog.setBtnRightText("确定")
                dialog.setBtnLeftText("取消")
                dialog.setCanceledOnTouchOutside(true)
                dialog.setView(view, Gravity.CENTER)
                dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
                    override fun clickLeftBtn() {
                        dialog.dismiss()
                    }

                })
                dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
                    override fun clickRightBtn() {
                        dialog.dismiss()
                        vm.clearCache(this@PersonalSettingActivity)
                    }
                })
                dialog.show()
            }

            R.id.logout_per_setting -> {//退出
                val dialog = MyDialog(mContext!!, 0, 0, "",
                        needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0)
                val view = View.inflate(mContext!!, R.layout.dialog_logout, null)
                dialog.setBtnRightText("退出")
                dialog.setBtnLeftText("取消")
                dialog.setCanceledOnTouchOutside(true)
                dialog.setView(view, Gravity.CENTER)
                dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
                    override fun clickLeftBtn() {
                        dialog.dismiss()
                    }

                })
                dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
                    override fun clickRightBtn() {
                        dialog.dismiss()
                        //tcp退出APP
                        if (!com.marktoo.lib.cachedweb.NetUtil.isNetworkConnected(this@PersonalSettingActivity)){
                            toast("暂无网络")
                            return
                        }
                        viewModel.loginOut(this@PersonalSettingActivity , bindToLifecycle(), accessToken)

                    }

                })
                dialog.show()
            }
        }
    }

    private fun getLoginOutObservable() {
        viewModel.loginOutObservable.observe(this, Observer {
            //接口 退出app成功，处理位置2
            onUserLogout()
//            todo

        })
        viewModel.loginOutErrorObservable.observe(this, Observer {
            onUserLogout()
        })
    }

    // 【flutter】覆盖它为了兼容flutter，退出登录后：此activity退出，清除相关数据， 然后调用 Flutter loginOut
    fun onUserLogout() {
        LoginHelper.sigOff(this)
    }

    @Subscribe
    fun closePage(b: EventFinishPage){
        finish()
    }

}

class SettingPageViewModel : ViewModel(){

    val loginDeviceLiveData = MutableLiveData<Int>(0)

    val loadingData = MutableLiveData<Boolean>()

    fun loginDevices(context: Context ,life: LifecycleTransformer<Result<List<Int>>>, imToken : String) {
        RxScheduleUtil.rxSchedulerHelper(ImApiUtil.getTcpImService().getLoginDevice(imToken))
            .compose(life)

            .compose(ErrorTransformer.getInstance<List<Int>>())
            .subscribe(object : BaseSubscriber<List<Int>>() {
                override fun onError(ex: ApiException) {}

                override fun onComplete() {}

                override fun onNext(t: List<Int>?) {
                    val count = (t ?: arrayListOf()).size
                    loginDeviceLiveData.postValue(count)
                }

            })
    }

    private fun getDiskCacheSize(context: Context): Long {
        if (context.externalCacheDir != null) {
            return if (context.externalCacheDir!!.length() > 0) {
                val diskpath = context.externalCacheDir!!.absolutePath + "/glide_cache"
                if (!StringUtils.isEmpty(diskpath)) {
                    var cacheSize = CommonUtils.getDirSize(File(diskpath))
                    cacheSize -= 240 * 1024
                    if (cacheSize < 0)
                        cacheSize = 0
                    cacheSize
                } else {
                    0
                }
            } else {
                0
            }
        }
        return 0
    }

    private fun getDiskCacheSize2(context: Context): Long {
        val cachePath1 = DealFileUtil.getFileCachePath(context, ConstantTcpUtil.VIDEO_COMPRESSED_FOLDER)
        val cachePath2 = DealFileUtil.getFileCachePath(context, ConstantTcpUtil.IMAGE_CACHE_FOLDER)
        val cachePath3 = DealFileUtil.getFileCachePath(context, ConstantTcpUtil.IMAGE_COMPRESSED_FOLDER)
        val cachePath4 = DealFileUtil.getFileCachePath(context, ConstantTcpUtil.VOICE_CACHE_FOLDER)
        val cachePath5 = DealFileUtil.getFileCachePath(context, ConstantTcpUtil.VIDEO_SHOT_SCREEN_FOLDER)
        val cachePath6 = DealFileUtil.getFileCachePath(context, ConstantTcpUtil.FILE_CACHE_FOLDER)
        var cacheSize1 = CommonUtils.getDirSize(File(cachePath1))
        cacheSize1 -= 240 * 1024
        var cacheSize2 = CommonUtils.getDirSize(File(cachePath2))
        cacheSize2 -= 240 * 1024
        var cacheSize3 = CommonUtils.getDirSize(File(cachePath3))
        cacheSize3 -= 240 * 1024
        var cacheSize4 = CommonUtils.getDirSize(File(cachePath4))
        cacheSize4 -= 240 * 1024
        var cacheSize5 = CommonUtils.getDirSize(File(cachePath5))
        cacheSize5 -= 240 * 1024
        var cacheSize6 = CommonUtils.getDirSize(File(cachePath6))
        cacheSize6 -= 240 * 1024
        if (cacheSize1 < 0) {
            cacheSize1 = 0L
        }
        if (cacheSize2 < 0) {
            cacheSize2 = 0L
        }
        if (cacheSize3 < 0) {
            cacheSize3 = 0L
        }
        if (cacheSize4 < 0) {
            cacheSize4 = 0L
        }
        if (cacheSize5 < 0) {
            cacheSize5 = 0L
        }
        if (cacheSize6 < 0) {
            cacheSize6 = 0L
        }
        return cacheSize1 + cacheSize2 + cacheSize3 + cacheSize4 + cacheSize5 + cacheSize6

    }

    val cacheSizeData = MutableLiveData<String>()

    fun loadLocalCache(context: Context) {
        viewModelScope.launch {
            withContext(Dispatchers.IO){
                val size1 = getDiskCacheSize(context)
                val size2 = getDiskCacheSize2(context)
                val totalSize = size1 + size2
                val r = CommonUtils.convertFileSize(totalSize)
                cacheSizeData.postValue(r)
            }
        }
    }

    fun clearCache(context: Context) {
        viewModelScope.launch {
            withContext(Dispatchers.IO){
                loadingData.postValue(true)
                Glide.get(context).clearDiskCache()
                FileIOUtils.delFolder(DealFileUtil.getFileCachePath(context, ConstantTcpUtil.IMAGE_COMPRESSED_FOLDER))
                FileIOUtils.delFolder(DealFileUtil.getFileCachePath(context, ConstantTcpUtil.IMAGE_CACHE_FOLDER))
                FileIOUtils.delFolder(DealFileUtil.getFileCachePath(context, ConstantTcpUtil.VOICE_CACHE_FOLDER))
                FileIOUtils.delFolder(DealFileUtil.getFileCachePath(context, ConstantTcpUtil.VIDEO_SHOT_SCREEN_FOLDER))
                FileIOUtils.delFolder(DealFileUtil.getFileCachePath(context, ConstantTcpUtil.VIDEO_COMPRESSED_FOLDER))
                FileIOUtils.delFolder(DealFileUtil.getFileCachePath(context, ConstantTcpUtil.FILE_CACHE_FOLDER))
                val file = File(DOWNLOAD_DIR, "/ddbes.apk")
                if (file.exists()) file.delete()
                withContext(Dispatchers.Main){
                    Glide.get(context).clearMemory()
                }
                toast("清除成功")

                EventBus.getDefault().post(EventClearFlutterCache())

                loadingData.postValue(false)

                loadLocalCache(context)
            }
        }
    }

}