package com.ddbes.personal.view

import android.app.Activity
import android.view.LayoutInflater
import android.view.View
import com.alibaba.android.arouter.facade.annotation.Route
import com.ddbes.library.im.imtcp.tcpcacheutil.PersonSettingCacheHolder
import com.ddbes.personal.R
import com.ddbes.personal.contract.PersonSettingContract
import com.ddbes.personal.databinding.ActivityPerNotifyBinding
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.joinutech.common.util.CacheDataHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.PersonSettingBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.utils.RoutePersonal
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.toast
import javax.inject.Inject
import javax.inject.Named


/**
 * 消息通知设置页面
 * Created by liuchuchu on 2018/6/20.
 */
@Route(path = RoutePersonal.personNotifyConfigActivity)
class PersonalNotifyActivity(override val contentViewResId: Int = R.layout.activity_per_notify)
        : MyUseBindingActivity<ActivityPerNotifyBinding>() {

    @Inject
    @field:Named(PersonConstUtil.PERSONSETTING_PRESENTER)
    lateinit var presenter: PersonSettingContract.PersonSettingPresenter
    private var noticeSwitch: Int = 1//消息通知 1.开启 2.关闭,
    private var shockSwitch: Int = 1//震动 1.开启 2.关闭,
    private var voiceSwitch: Int = 1//声音 1.开启 2.关闭

    private var pcReceiveSwitch: Int = 0 // 0 开启； 1 关闭
    var isInit = true

    override fun initImmersion() {
        setPageTitle("新消息通知")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey, View.OnClickListener { onBackPressed() })
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        DaggerPersonComponent.builder().build().inject(this)
    }

    override fun initLogic() {
        super.initLogic()
        loadSetting()
        //初始化消息配置
        initSetting()
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityPerNotifyBinding {
        return ActivityPerNotifyBinding.inflate(layoutInflater)
    }

    private fun loadSetting() {
        getLoadingDialog("加载隐私设置。。。", false)
        presenter.watchSettingData(bindToLifecycle<Result<PersonSettingBean>>(), accessToken!!, {
            dismissDialog()
            noticeSwitch = it.noticeSwitch
            shockSwitch = it.shockSwitch
            voiceSwitch = it.voiceSwitch

            pcReceiveSwitch = it.noticeType

            if (noticeSwitch > 0) {
                binding.switch1Notify.setCheckedNoEvent(true)
                binding.switch2NotifyLayout.visibility = View.VISIBLE
                binding.switch3NotifyLayout.visibility = View.VISIBLE
                binding.switch2Notify.setCheckedNoEvent(voiceSwitch == 1)
                binding.switch3Notify.setCheckedNoEvent(shockSwitch == 1)
                PersonSettingCacheHolder.saveSetting(true,shockSwitch == 1,voiceSwitch == 1)
            } else {
                binding.switch1Notify.setCheckedNoEvent(false)
                binding.switch2NotifyLayout.visibility = View.GONE
                binding.switch3NotifyLayout.visibility = View.GONE
                PersonSettingCacheHolder.saveSetting(false,false,false)
            }

            binding.switchPcNotify.setCheckedNoEvent(pcReceiveSwitch == 0)

            isInit = false
        }, {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun initSetting() {
        binding.switch1Notify.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) { //是否接受新消息通知
                val map = hashMapOf<String, Any>()
                noticeSwitch = 1
                map["noticeSwitch"] = noticeSwitch
                presenter.updateSettingData(bindToLifecycle(), accessToken!!, map,
                        onSuccess = {
                            showLog("binding.switch1Notify - 修改成功")
                            binding.switch2NotifyLayout.visibility = View.VISIBLE
                            binding.switch3NotifyLayout.visibility = View.VISIBLE
                            binding.switch2Notify.setCheckedNoEvent(true)
                            binding.switch3Notify.setCheckedNoEvent(true)
                            CacheDataHolder.saveMsgPushEnable(noticeSwitch)
                            PersonSettingCacheHolder.saveSetting(true,true,true)
                        },
                        onError = {
                            noticeSwitch = 2
                            binding.switch1Notify.setCheckedNoEvent(false)
                            ToastUtil.show(mContext!!, it)
                        })
            } else {
                val map = hashMapOf<String, Any>()
                noticeSwitch = 0
                map["noticeSwitch"] = noticeSwitch
                presenter.updateSettingData(bindToLifecycle(), accessToken!!, map, {
                    showLog("binding.switch1Notify - 修改成功")
                    CacheDataHolder.saveMsgPushEnable(noticeSwitch)
                    binding.switch2NotifyLayout.visibility = View.GONE
                    binding.switch3NotifyLayout.visibility = View.GONE
                    PersonSettingCacheHolder.saveSetting(false,false,false)
                }, {
                    noticeSwitch = 1
                    binding.switch1Notify.setCheckedNoEvent(true)
                    ToastUtil.show(mContext!!, it)
                })
            }
        }

        binding.switch2Notify.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {//是否静音
                val map = hashMapOf<String, Any>()
                voiceSwitch = 1
                map["voiceSwitch"] = voiceSwitch
                presenter.updateSettingData(bindToLifecycle(), accessToken!!, map, {
                    showLog("switch2_notify - 修改成功")
                    CacheDataHolder.saveMsgPushSoundEnable(voiceSwitch)
                    PersonSettingCacheHolder.saveSetting(null,null,true)
                }, {
                    voiceSwitch = 0
                    binding.switch2Notify.setCheckedNoEvent(false)
                    ToastUtil.show(mContext!!, it)
                })
            } else {
                val map = hashMapOf<String, Any>()
                voiceSwitch = 0
                map["voiceSwitch"] = voiceSwitch
                presenter.updateSettingData(bindToLifecycle(), accessToken!!, map, {
                    showLog("switch2_notify - 修改成功")
                    CacheDataHolder.saveMsgPushSoundEnable(voiceSwitch)
                    PersonSettingCacheHolder.saveSetting(null,null,false)
                }, {
                    voiceSwitch = 1
                    binding.switch1Notify.setCheckedNoEvent(false)
                    ToastUtil.show(mContext!!, it)
                })
            }
        }
        binding.switch3Notify.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                val map = hashMapOf<String, Any>()
                shockSwitch = 1
                map["shockSwitch"] = shockSwitch
                presenter.updateSettingData(bindToLifecycle(), accessToken!!, map, {
                    showLog("switch3_notify - 修改成功")
                    CacheDataHolder.saveMsgPushVibEnable(shockSwitch)
                    PersonSettingCacheHolder.saveSetting(null,true,null)
                }, {
                    shockSwitch = 0
                    binding.switch3Notify.setCheckedNoEvent(false)
                    ToastUtil.show(mContext!!, it)
                })
            } else {
                val map = hashMapOf<String, Any>()
                shockSwitch = 0
                map["shockSwitch"] = shockSwitch
                presenter.updateSettingData(bindToLifecycle(), accessToken!!, map, {
                    showLog("switch3_notify - 修改成功")
                    CacheDataHolder.saveMsgPushVibEnable(shockSwitch)
                    PersonSettingCacheHolder.saveSetting(null,false,null)
                }, {
                    shockSwitch = 1
                    binding.switch3Notify.setCheckedNoEvent(false)
                    ToastUtil.show(mContext!!, it)
                })
            }
        }

        binding.switchPcNotify.setOnCheckedChangeListener { _, isChecked ->
            val map = hashMapOf<String,Any>()
            map["noticeType"] = if (!isChecked) 1 else 0
            presenter.updateSettingData(bindToLifecycle() ,accessToken , map , {

            }, {
                binding.switchPcNotify.setCheckedNoEvent(!isChecked)
                toast(this@PersonalNotifyActivity , it)
            })
        }
    }

    override fun onBackPressed() {
//        super.onBackPressed()
        setResult(Activity.RESULT_OK)
        finish()
    }

    override fun showToolBar(): Boolean {
        return true
    }


    override fun openArouterReceive(): Boolean {
        return false
    }
}