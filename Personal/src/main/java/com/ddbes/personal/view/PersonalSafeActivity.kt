package com.ddbes.personal.view

import android.Manifest
import android.annotation.SuppressLint
import android.app.AlertDialog
import android.content.DialogInterface
import android.content.Intent
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.lifecycle.Observer
import com.ddbes.personal.R
import com.ddbes.personal.databinding.ActivityPerSafeBinding
import com.ddbes.personal.viewmodel.PersonalSafeViewModel
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.flutter.EventFinishPage
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import io.reactivex.Observable
import io.reactivex.ObservableSource
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * 账号与安全
 * Created by liuchuchu on 2018/6/20.
 */
class PersonalSafeActivity : MyUseBindingActivity<ActivityPerSafeBinding>() {

    override val contentViewResId: Int = R.layout.activity_per_safe
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityPerSafeBinding {
        return ActivityPerSafeBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true
    override fun openArouterReceive(): Boolean = false

    private var wxName = ""
    private lateinit var api: IWXAPI
    private lateinit var viewModel: PersonalSafeViewModel
    private var disposable: Disposable? = null

    override fun initImmersion() {
        setPageTitle("账号与安全")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        showToolBarLine()
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        viewModel = getModel(PersonalSafeViewModel::class.java)
    }

    override fun initLogic() {
        super.initLogic()
        binding.layout1PerSafe.setOnClickListener(this)
        binding.layout2PerSafe.setOnClickListener(this)
        binding.layout3PerSafe.setOnClickListener(this)
        binding.llUnRegister.setOnClickListener(this)
        binding.tvSave.setOnClickListener(this)
        //通过WXAPIFactory工厂获取IWXApI的示例
        api = WXAPIFactory.createWXAPI(this, APP_ID_WX, true)
        //将应用的appid注册到微信
        api.registerApp(APP_ID_WX)
        if (StringUtils.isNotBlankAndEmpty(UserHolder.getCurrentUser()?.nickName)) {
            wxName = UserHolder.getCurrentUser()?.nickName!!
            binding.tvWxBindInfo.text = wxName
            binding.tvWxBindInfo.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.text_black))
        }
        getObservable()
    }

    private fun getObservable() {
        viewModel.personalSafeWxBindSuccessObservable.observe(this, Observer {
            dismissDialog()
            toast("绑定微信成功")
//            MMKVUtil.saveString(USER.WXNICKNAME, wxName)
            UserHolder.getCurrentUser()?.nickName = wxName
            binding.tvWxBindInfo.text = wxName
            binding.tvWxBindInfo.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.text_black))
        })
        viewModel.personalSafeWxBindErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
            wxName = ""
            binding.tvWxBindInfo.text = "未关联"
            binding.tvWxBindInfo.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color999999))
        })
        viewModel.personalSafeWxUnBindSuccessObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, "解绑微信成功")
            wxName = ""
            val userInfo = UserHolder.getCurrentUser()
            if (userInfo != null) {
                userInfo.nickName = ""
                userInfo.openId = ""
                UserHolder.onLogin(userInfo, true)// clear weixin name openid
            }
//            MMKVUtil.saveString(USER.WXNICKNAME, wxName)
            binding.tvWxBindInfo.text = "未关联"
            binding.tvWxBindInfo.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color999999))
        })
        viewModel.personalSafeWxUnBindErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.layout1_per_safe -> {//点击更换手机号
                val intent1 = Intent(this@PersonalSafeActivity,
                        PersonalVerifyActivity::class.java)
                startActivity(intent1)
            }

            R.id.layout2_per_safe -> {
                val intent1 = Intent(this@PersonalSafeActivity,
                        ChangePasswordActivity::class.java)
                startActivity(intent1)
            }
            R.id.layout3_per_safe -> {
                // 点击微信，关联按钮
                dealWxBindEvent()
            }
            R.id.ll_un_register -> {
                dealUnRegisterAccount()
            }
            R.id.tv_save -> {
                saveViewToFile(binding.ivWxOfficialAccount)
            }
        }
    }

    private fun dealUnRegisterAccount() {
        startActivity(Intent(this, SMSCodeActivity::class.java))
    }

    @SuppressLint("CheckResult")
    private fun requestPhonePermission(onSuccess: () -> Unit) {
        val perms = arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE)
        PermissionUtils.requestPermissionActivity(this, perms, "存储权限", {
            onSuccess.invoke()
        }, {
            ToastUtil.show(mContext!!, "保存图片需要您授权读写权限")
        })
    }

    private fun doThings(view: View) {
        getLoadingDialog("", false)
        disposable = Observable.defer {
            ObservableSource<Boolean> { observer ->
                val b = SaveViewToFileUtil.getBitmapFromView(view)
                SaveViewToFileUtil.save2Gallery(this@PersonalSafeActivity , bmp = b)
                observer.onNext(true)
            }
        }.subscribeOn(Schedulers.computation())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe {
                    toast(this@PersonalSafeActivity ,"保存到相册")
                    dismissDialog()
                }
    }

    //保存View到文件
    private fun saveViewToFile(view: View) {
        val perms = arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE)

        val has = PermissionUtils.checkPermissions(this, perms)
        if (!has){
            AlertDialog.Builder(this)
                .setMessage("需要您同意使用存储权限，才能保存图片到手机").setPositiveButton("同意" ,
                    DialogInterface.OnClickListener { dialog, which ->
                        dialog?.dismiss()

                        requestPhonePermission {
                            doThings(view)
                        }

                    }).setNegativeButton("拒绝") { _, _ -> }
                .setCancelable(true)
                .show()
        }else{
            requestPhonePermission {
                doThings(view)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (disposable != null && disposable!!.isDisposed) {
            disposable!!.dispose()
        }
    }

    private fun dealWxBindEvent() {
        if (StringUtils.isEmpty(wxName)) {
//            val hasWechat = packageManager?.getPackageInfo("com.tencent.mm", GET_SIGNING_CERTIFICATES)
//            if (hasWechat != null) {
            if (CommonUtils.isHaveWeChat(mContext!!)) {
                //有微信
                val req = SendAuth.Req()
                req.scope = "snsapi_userinfo"
                req.state = "wechat_sdk_微信登录"
                MMKVUtil.saveString("wxInfo", "wxInfo")

                // 保存当前Activity状态，防止被系统回收
                MMKVUtil.saveString("wx_login_from_activity", "PersonalSafeActivity")

                api.sendReq(req)
            } else {
                ToastUtil.show(mContext!!, "请先安装微信")
            }
        } else {
            val view1 = View.inflate(mContext, com.joinutech.ddbeslibrary.R.layout.dialog_base, null)
            val unBindDialog = BottomDialogUtil.showBottomDialog(
                    mContext!!, view1, Gravity.CENTER)
            val confirm = view1.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.confirm_base)
            val cancel = view1.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.cancel_base)
            val tv2 = view1.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.tv2)
            tv2.text = "您确认要解除与此微信账号的关联吗？"
            confirm.text = "解除"
            confirm.setOnClickListener {
                unBindDialog.dismiss()
                getLoadingDialog("", true)
                viewModel.wxUnBind(bindToLifecycle(), accessToken!!)
            }
            cancel.setOnClickListener {
                unBindDialog.dismiss()
            }
            unBindDialog.show()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun wechatLogin(event: EventBusEvent<String>) {
        if (event.code == EventBusAction.BIND_BY_WECHAT) {
            val data: String = event.data as String
            val token = UserHolder.getAccessToken()
            if (StringUtils.isEmpty(token)) {
                ToastUtil.show(mContext!!, "微信关联失败,请稍后重试")
                return
            }
            if (StringUtils.isNotBlankAndEmpty(data) && data.contains(":")) {
                val infoList = data.split(":")
                if (infoList.size < 3) return
                val openId = infoList[0]
                val unionid = infoList[1]
                wxName = infoList[2]

//                val unionid: String = data.substring(0, index)
//                val secondIndex = data.lastIndexOf(":")
//                val openId: String = data.substring(index + 1, secondIndex)
//                wxName = data.substring(secondIndex + 1)
                if (StringUtils.isNotBlankAndEmpty(unionid) && StringUtils.isNotBlankAndEmpty(openId)
                        && StringUtils.isNotBlankAndEmpty(wxName)) {
                    getLoadingDialog("", true)
                    val map = hashMapOf<String, Any>()
                    map["nickName"] = wxName
                    map["wxAuthId"] = unionid
                    map["wxOpenId"] = openId
                    //type:1担当 2云资产
                    map["type"] = 1
                    viewModel.wxBind(bindToLifecycle(), token, map)
                } else {
                    ToastUtil.show(mContext!!, "微信获取信息失败")
                }
            } else {
                ToastUtil.show(mContext!!, "微信获取信息失败")
            }
        } else {
            showLog("WECHATINFO 微信获取信息失败")
        }
    }


    @Subscribe
    fun closePage(b: EventFinishPage){
        finish()
    }
}