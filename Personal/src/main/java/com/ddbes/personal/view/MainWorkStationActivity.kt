package com.ddbes.personal.view

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.style.ForegroundColorSpan
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.ddbes.personal.R
import com.ddbes.personal.adapter.WorkStationAdapter
import com.ddbes.personal.contract.PersonCommitContract
import com.ddbes.personal.databinding.ActivityWorkstationLayoutBinding
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.joinutech.common.adapter.BaseAdapter
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.common.util.CompanyHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.request.exception.ErrorType
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.MyDialog
import com.joinutech.ddbeslibrary.utils.Spanny
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.flutter.EventRefreshOrg
import org.greenrobot.eventbus.EventBus
import javax.inject.Inject
import javax.inject.Named


/**
 * @Desc: 主要团队设置页面
 * @PackageName: com.ddbes.personal.view
 * @ClassName: MainWorkStationActivity
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/6/10 9:13
 */
class MainWorkStationActivity : MyUseBindingActivity<ActivityWorkstationLayoutBinding>() {

    override val contentViewResId: Int = R.layout.activity_workstation_layout
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityWorkstationLayoutBinding {
        return ActivityWorkstationLayoutBinding.inflate(layoutInflater)
    }

    lateinit var adapter: WorkStationAdapter

    @Inject
    @field:Named(PersonConstUtil.PERSON_COMMIT_PRESENTER)
    lateinit var presenter: PersonCommitContract.PersonCommitPresenter

//    @Inject
//    @field:Named(PersonConstUtil.WORKSTATION_PRESENTER)
//    lateinit var presenterWork: WorkStationContract.WorkStationPresenter
    private var dataList: List<WorkStationBean> = arrayListOf()
    private var mainOrgId: String? = ""
    private var newMainOrgId: String = ""

    override fun initImmersion() {
        whiteStatusBarBlackFont()
        setPageTitle("主要团队")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey, View.OnClickListener {
            onBackPressed()
        })
    }

    override fun initView() {
        DaggerPersonComponent.builder().build().inject(this)
//        setShowEmptyView(true)
        binding.layoutEmptyLayout.visibility = View.VISIBLE
        val layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this)
        binding.rvList.layoutManager = layoutManager
        adapter = WorkStationAdapter(mContext!!, dataList)
        binding.rvList.adapter = adapter
        adapter.setOnItemClickListener(object : BaseAdapter.OnItemClickListener {
            override fun onItemClick(view: View, position: Int) {
                //更改主要企业
                setMain(position)
            }
        })
    }

    @JvmField
    @Autowired(name = "/addressbook/service/org")
    var orgService: RouteServiceProvider? = null

    override fun initLogic() {
        super.initLogic()
        mainOrgId = CompanyHolder.getMainOrg()?.companyId
        newMainOrgId = mainOrgId ?: ""
        getLoadingDialog("请求团队列表数据", false)
        orgService?.service("getAllCompany", Bundle()) {
            hideLoading()
            if (it == ErrorType.SUCCESS.toString()) {
                // 更新页面显示
                if (!CompanyHolder.getTotalCompanies().isNullOrEmpty()) {
                    dataList = CompanyHolder.getTotalCompanies()
                    binding.clDataLayout.visibility = View.VISIBLE
                    binding.layoutEmptyLayout.visibility = View.GONE
                    binding.tvOrgCount.text = "您的账号共创建/加入了${dataList.size}个团队"
                    for (item in dataList) {
                        if (item.companyId == newMainOrgId) {
                            item.deptId = "1"
                        } else {
                            item.deptId = "0"
                        }
                    }
                    adapter.setDataSourceList(dataList)
                } else {
                    binding.clDataLayout.visibility = View.GONE
                    binding.layoutEmptyLayout.visibility = View.VISIBLE
//                    ToastUtil.show(mContext!!, it)
                }
            }
        }
    }

    private fun setMain(position: Int) {
        //弹出一个确认的对话框
        val name = dataList[position].name
        val regsiterDialog = MyDialog(mContext, 0, 0,
                "", needBtnConfirm = false, needBtnCancel = false, bgResourceId = 0)
        val view = View.inflate(mContext, R.layout.dialog_mainstation_layout, null)
        regsiterDialog.setView(view, Gravity.CENTER)
        regsiterDialog.show()
        view.findViewById<TextView>(R.id.tv_content).text = Spanny()
                .append("您确认要将 ")
                .append(name, ForegroundColorSpan(CommonUtils.getColor(this, com.joinutech.ddbeslibrary.R.color.color107EEB)))
                .append(" 设置为您的主要团队吗？")
//        view.findViewById<TextView>(R.id.text2).text = name
        regsiterDialog.setCanceledOnTouchOutside(true)
        view.findViewById<TextView>(R.id.cancel).setOnClickListener {
            regsiterDialog.dismiss()
        }
        view.findViewById<TextView>(R.id.confirm).setOnClickListener {
            regsiterDialog.dismiss()
            requestSetMain(position)
        }
    }

    private fun requestSetMain(position: Int) {
        val company = dataList[position]
        getLoadingDialog("设置主要团队", false)
        presenter.commitPersonInfo(this, company.companyId, "company",
                onSuccess = {
                    dismissDialog()
                    //选中了主要企业
                    setMainStationShow(position)
                    // 切换主要团队后，可以不用重新获取团队列表接口，手动修改即可
                    CompanyHolder.saveMainOrgId(newMainOrgId)

                    EventBus.getDefault().post(EventRefreshOrg(1))
                },
                onError = {
                    dismissDialog()
                    ToastUtil.show(mContext!!, it)
                })
    }

    private fun setMainStationShow(position: Int) {
        // 修改本地 主要团队
        var old = 0
        for (i in dataList.indices) {
            if (dataList[i].deptId == "1") {
                old = i
            }
            dataList[i].deptId = "0"
        }
        adapter.notifyItemChanged(old)
        dataList[position].deptId = "1"
        adapter.notifyItemChanged(position)
        newMainOrgId = dataList[position].companyId
        ToastUtil.showCustomToast(null, mContext!!, true, "已变更主要团队")
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun onBackPressed() {
        if (StringUtils.isNotBlankAndEmpty(mainOrgId) && StringUtils.isNotBlankAndEmpty(newMainOrgId)
                && mainOrgId != newMainOrgId) {
//            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST, ""))
            val intent = Intent()
            intent.putExtra("companyName", CompanyHolder.getMainOrg()?.name)
            setResult(Activity.RESULT_OK, intent)
        }
        finish()
    }

    override fun openArouterReceive(): Boolean {
        return true
    }
}