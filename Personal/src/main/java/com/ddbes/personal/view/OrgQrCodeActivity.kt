package com.ddbes.personal.view

import android.Manifest
import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import com.alibaba.android.arouter.facade.annotation.Route
import com.ddbes.personal.R
import com.ddbes.personal.databinding.ActivityQrcodeLayoutBinding
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.QrCodeData
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
//import com.xys.libzxing.zxing.encoding.EncodingUtils
import io.reactivex.Observable
import io.reactivex.ObservableSource
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

/**
 * Description 个人二维码
 * Author HJR36
 * Date 2018/6/19 16:40
 */
@Route(path = RoutePersonal.orgQrCode)
class OrgQrCodeActivity(override val contentViewResId: Int = R.layout.activity_qrcode_layout)
    : MyUseBindingActivity<ActivityQrcodeLayoutBinding>() {

    private var qrCodeData: QrCodeData? = null
    private var disposable: Disposable? = null

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        binding.tvSave.setOnClickListener(this)
    }

    override fun initLogic() {
        super.initLogic()
        if (intent.hasExtra("qrCodeData") && intent.getSerializableExtra("qrCodeData") != null) {
            qrCodeData = intent.getSerializableExtra("qrCodeData") as QrCodeData?
        }
        qrCodeData?.let {
            setPageTitle(it.title)
            binding.tvUserName.text = it.name
            if (StringUtils.isNotBlankAndEmpty(it.headImg)) {
                ImageLoaderUtils.loadImage(mContext!!, binding.ivHeader, it.headImg)
            } else {
                binding.ivHeader.setImageResource(com.joinutech.ddbeslibrary.R.drawable.icon_userdefault)
            }
            makeQrCode(it.content, binding.ivQrCode, null)
            binding.tvHint.text = it.hint
            if (it.savable) {
                binding.tvSave.visibility = View.VISIBLE
            } else {
                binding.tvSave.visibility = View.GONE
            }
        }

    }

    /**
     * bitmap 二维码中有logo时使用
     */
    private fun makeQrCode(data: String, iv: ImageView, bitmap: Bitmap?) {
        /**设置团队id到二维码中*/
//        val isSucceed = EncodingUtils.createQRCode(
//                data,
//                ScreenUtils.dip2px(mContext, 233f),
//                ScreenUtils.dip2px(mContext, 233f),
//                bitmap,
//                null
//        )
//        iv.setImageBitmap(isSucceed)
    }

    override fun showToolBar(): Boolean {
        return true
    }


    override fun openArouterReceive(): Boolean {
        return true
    }

    override fun onNoDoubleClick(v: View) {
        if (v.id == R.id.tv_save) {
            saveViewToFile(binding.llSavePicLayout)
        }
    }

    @SuppressLint("CheckResult")
    private fun requestPhonePermission(view: View) {
        val perms = arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE)

        val preTips = "需要您同意使用存储权限，才能使用保存图片功能"
        PermissionUtils.requestPermissionActivity(this, perms, "存储权限", {
            doThings(view)
        }, {
            ToastUtil.show(mContext!!, "保存图片需要您授权读写权限")
        } , preTips = preTips)
    }

    private fun doThings(view: View) {
        getLoadingDialog("", false)
        if (qrCodeData != null) {
            disposable = Observable.defer {
                ObservableSource<Bitmap> { observer ->
                    observer.onNext(
                            SaveViewToFileUtil.getBitmapFromView(view))
                }
            }.subscribeOn(Schedulers.computation())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe {
                        SaveViewToFileUtil.saveViewToFile(mContext!!,
                                "qr_${qrCodeData!!.name}_${qrCodeData!!.userId}",
                                bmp = it,
                                result = {
                                    dismissDialog()
                                })
                    }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (disposable != null && disposable!!.isDisposed) {
            disposable?.dispose()
        }
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityQrcodeLayoutBinding {
        return ActivityQrcodeLayoutBinding.inflate(layoutInflater)
    }

    //保存View到文件
    private fun saveViewToFile(view: View) {
        requestPhonePermission(view)
    }
}