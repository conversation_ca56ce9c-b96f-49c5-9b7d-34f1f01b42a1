package com.ddbes.personal.view

import android.animation.Animator
import android.graphics.Rect
import android.os.Build
import android.os.Handler
import android.os.Message
import android.text.InputType
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatSeekBar
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.Observer
import com.bumptech.glide.request.RequestOptions
import com.ddbes.library.im.util.LoginHelper
import com.ddbes.personal.R
import com.joinutech.ddbeslibrary.R as libR
import com.ddbes.personal.contract.ChangePasswordContract
import com.ddbes.personal.databinding.ActivityChangepasswordLayoutBinding
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.ddbes.personal.viewmodel.ChangePasswordViewModel
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.VerifyImageBean
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformerOnlyBean
import com.joinutech.ddbeslibrary.service.LoginService
import com.joinutech.ddbeslibrary.utils.AppManager
import com.joinutech.ddbeslibrary.utils.BottomDialogUtil
import com.joinutech.ddbeslibrary.utils.DeviceUtil
import com.joinutech.ddbeslibrary.utils.OnNoDoubleClickListener
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.StringUtils.Companion.setEtFilter
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.utils.toast
import com.joinutech.flutter.EventFinishPage
import me.jessyan.autosize.internal.CustomAdapt
import org.greenrobot.eventbus.EventBus
import java.lang.ref.WeakReference
import javax.inject.Inject
import javax.inject.Named

class ChangePasswordActivity : MyUseBindingActivity<ActivityChangepasswordLayoutBinding>(), CustomAdapt {

    private var handler = MyStaticHandler(WeakReference(this))

    override fun isBaseOnWidth(): Boolean {
        return false
    }

    override fun getSizeInDp(): Float {
        return 640f
    }

    private var transY = 0
    private var isAnim = false

    @Inject
    @field:Named(PersonConstUtil.CHANGE_PASSWORD_PRESENTER)
    lateinit var presenter: ChangePasswordContract.ChangePasswordPresenter
    private lateinit var viewModel: ChangePasswordViewModel
    private var topUrl = ""
    private var backUrl = ""
    private lateinit var bigIv: ImageView
    private lateinit var smallIv: ImageView
    private var refreshImage = false
    private var layoutParams: ConstraintLayout.LayoutParams? = null
    private lateinit var seekBar: AppCompatSeekBar
    private lateinit var maskIng: View
    private lateinit var verifySuccessIv: ImageView
    private lateinit var fileText: TextView
    private var dialog: AlertDialog? = null

    var phone: String = ""

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        setPageTitle("更换密码")
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        DaggerPersonComponent.builder().build().inject(this)
        setListenerToRootView()
        phone = UserHolder.getCurrentUser()?.mobile ?: ""
        transY = DeviceUtil.dip2px(this, 30f)
        viewModel = getModel(ChangePasswordViewModel::class.java)
    }

    override fun initLogic() {
        super.initLogic()
        binding.finish.setOnClickListener(this)
        binding.perLayoutShowPassword.setOnClickListener(this)
        setEtFilter(binding.perEt3)
        setEtFilter(binding.perEt4)
        setEtFilter(binding.perEt5)
        getObserver()
    }

    private fun getObserver() {
        viewModel.getVerifyImageSuccessObservable.observe(this, Observer {
            dismissDialog()
            if (refreshImage) {
                val pair = showImageVerifyIv(it, backUrl, bigIv, topUrl, smallIv)
                backUrl = pair.first
                topUrl = pair.second
                resetImageDistance(seekBar, smallIv, layoutParams)
            } else {
                showAndVerifyImage(it)
            }
        })
        viewModel.getVerifyImageErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
        viewModel.verifyImageSuccessObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, "图片匹配成功")
            maskIng.visibility = View.VISIBLE
            verifySuccessIv.visibility = View.VISIBLE
            maskIng.postDelayed({
                dialog?.dismiss()
                upPassWord()
            }, 1500)
        })
        viewModel.verifyImageErrorObservable.observe(this, Observer {
            dismissDialog()
            if (it == "匹配失败") {
                fileText.visibility = View.VISIBLE
                fileText.postDelayed({
                    fileText.visibility = View.GONE
                }, 1000)
            }
            ToastUtil.show(mContext!!, it)
            resetImageDistance(seekBar, smallIv, layoutParams)
        })
    }

    private fun showAndVerifyImage(it: VerifyImageBean) {
        val view = View.inflate(mContext!!, libR.layout.dialog_image_verify, null)
        dialog = BottomDialogUtil.showBottomDialog(
                mContext!!, view, Gravity.CENTER)
        dialog?.setCanceledOnTouchOutside(false)
        bigIv = view.findViewById(libR.id.bigIv)
        smallIv = view.findViewById(libR.id.smallIv)
        seekBar = view.findViewById(libR.id.seekBar)
        fileText = view.findViewById(libR.id.fileText)
        maskIng = view.findViewById(libR.id.maskIng)
        verifySuccessIv = view.findViewById(libR.id.verifySuccessIv)
        val refresh = view.findViewById<ImageView>(libR.id.refresh)
        refresh.setOnClickListener(object : OnNoDoubleClickListener {
            override fun onNoDoubleClick(v: View) {
                //点击刷新按钮，启动动画
                v.animate().rotationBy(360f).setDuration(500)
                        .setInterpolator(AccelerateDecelerateInterpolator())
                        .setListener(object : Animator.AnimatorListener {
                            override fun onAnimationStart(animation: Animator) {}
                            override fun onAnimationEnd(animation: Animator) {
                                viewModel.getVerifyImage(bindToLifecycle(), phone)
                                refreshImage = true
                            }

                            override fun onAnimationCancel(animation: Animator) {}
                            override fun onAnimationRepeat(animation: Animator) {}
                        })
            }

        })
        val pair = showImageVerifyIv(it, backUrl, bigIv, topUrl, smallIv)
        backUrl = pair.first
        topUrl = pair.second

        seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                layoutParams = smallIv.layoutParams as ConstraintLayout.LayoutParams
                val width: Int = bigIv.width
                val smallWidth: Int = smallIv.width
                val differenceValue = progress * 0.01 * width
                val value: Int = if (differenceValue > width - smallWidth) {
                    width - smallWidth
                } else {
                    differenceValue.toInt()
                }
                layoutParams?.leftMargin = value
                smallIv.layoutParams = layoutParams
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {

            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val progress = seekBar!!.progress
                Log.e("uploadProgressValue", progress.toString())
                getLoadingDialog("获取图片验证码", false)
                val width: Int = bigIv.width
                val smallWidth: Int = smallIv.width
                val differenceValue = progress * 0.01 * width
                val value: Double = if (differenceValue > width - smallWidth) {
                    (width - smallWidth) * 1.00 / width
                } else {
                    differenceValue / width
                }
                val hashMap = hashMapOf<String, Any>()
                val newId = if (StringUtils.isNotBlankAndEmpty(backUrl)) {
                    val dotIndex = backUrl.lastIndexOf(".")
                    val lineIndex = backUrl.lastIndexOf("/")
                    backUrl.substring(lineIndex + 1, dotIndex)
                } else ""
                val oriId = if (StringUtils.isNotBlankAndEmpty(topUrl)) {
                    val dotIndex = topUrl.lastIndexOf(".")
                    val lineIndex = topUrl.lastIndexOf("/")
                    topUrl.substring(lineIndex + 1, dotIndex)
                } else ""
                hashMap["newId"] = newId
                hashMap["oriId"] = oriId
                hashMap["phone"] = phone
                hashMap["type"] = 0
                hashMap["scale"] = value
                viewModel.verifyImage(bindToLifecycle(), hashMap)
            }
        })
    }

    private fun showImageVerifyIv(it: VerifyImageBean, backUrl: String, bigIv: ImageView,
                                  topUrl: String, smallIv: ImageView): Pair<String, String> {
        var backUrl1 = backUrl
        var topUrl1 = topUrl
        if (StringUtils.isNotBlankAndEmpty(it.backUrl)) {
            backUrl1 = it.backUrl
            val options = RequestOptions
                    .placeholderOf(libR.drawable.image_placeholder_im)
                    .error(libR.drawable.image_placeholder_im)
                    .centerCrop()
            ImageLoaderUtils.showImgWithOption(mContext!!, backUrl1, bigIv, options)
        }
        if (StringUtils.isNotBlankAndEmpty(it.topUrl)) {
            topUrl1 = it.topUrl
            ImageLoaderUtils.loadImage(this, smallIv, topUrl)
        }
        return Pair(backUrl1, topUrl1)
    }

    private fun resetImageDistance(seekBar: AppCompatSeekBar, smallIv: ImageView,
                                   layoutParams: ConstraintLayout.LayoutParams?) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            seekBar.setProgress(0, true)
        } else seekBar.progress = 0
        var layoutParams1 = layoutParams
        if (layoutParams1 == null)
            layoutParams1 = smallIv.layoutParams as ConstraintLayout.LayoutParams
        layoutParams1.leftMargin = 0
        smallIv.layoutParams = layoutParams1
    }

    override fun onNoDoubleClick(v: View) {
        if (v == binding.finish) {
            //完成更换密码
            changePassWord()
        } else if (v == binding.perLayoutShowPassword) {
            //是否显示密码
            if (!v.isSelected) {
                binding.perEt3.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
                binding.perEt4.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
                binding.perEt5.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
            } else {
                binding.perEt3.inputType =
                        InputType.TYPE_TEXT_VARIATION_PASSWORD or InputType.TYPE_CLASS_TEXT
                binding.perEt4.inputType =
                        InputType.TYPE_TEXT_VARIATION_PASSWORD or InputType.TYPE_CLASS_TEXT
                binding.perEt5.inputType =
                        InputType.TYPE_TEXT_VARIATION_PASSWORD or InputType.TYPE_CLASS_TEXT
            }
            v.isSelected = !v.isSelected
        }
    }

    private fun changePassWord() {
        if (StringUtils.isEmpty(binding.perEt3.editableText.toString())) {
            ToastUtil.show(mContext!!, "请输入旧密码")
            return
        }
        if (StringUtils.isEmpty(binding.perEt4.editableText.toString())) {
            ToastUtil.show(mContext!!, "请输入新密码")
            return
        }
        if (StringUtils.isEmpty(binding.perEt5.editableText.toString())) {
            ToastUtil.show(mContext!!, "请输入新密码")
            return
        }
        if (!StringUtils.isPassword(binding.perEt3.editableText.toString()) ||
                !StringUtils.isPassword(binding.perEt4.editableText.toString()) ||
                !StringUtils.isPassword(binding.perEt5.editableText.toString())) {
            ToastUtil.show(mContext!!, "请输入6-16位字母和数字(下划线)组合密码")
            return
        }
        if (binding.perEt4.editableText.toString() != binding.perEt5.editableText.toString()) {
            ToastUtil.show(mContext!!, "两次密码输入不一致")
            return
        }
        //需要先通过图片验证才可以修改密码
        if (StringUtils.isNotBlankAndEmpty(phone)) {
            getLoadingDialog("获取图片验证码", false)
            viewModel.getVerifyImage(bindToLifecycle(), phone)
        }
    }

    private fun upPassWord() {
        getLoadingDialog("正在修改", true)
        val oldPass = binding.perEt3.editableText.toString()
        val newPass = binding.perEt5.editableText.toString()
        presenter.upPwd(oldPass, newPass, bindToLifecycle(), accessToken!!,
                onSuccess = {
                    dismissDialog()
                    toast("密码已修改完成，请重新登录")
                    // TODO: 2021/8/31 修改手机号以后，调用退出登录接口，清理本地缓存，跳转到登录页面
                    LoginService.loginOut(accessToken!!)
                            .compose(bindToLifecycle())
                            .compose(ErrorTransformer.getInstance())
                            .subscribe(object : BaseSubscriber<Any>() {
                                override fun onError(ex: ApiException) {
                                    handler.postDelayed({
                                        handler.sendEmptyMessage(0)
                                    }, 300)
                                }

                                override fun onComplete() {
                                }

                                override fun onNext(t: Any?) {
                                    handler.postDelayed({
                                        handler.sendEmptyMessage(0)
                                    }, 300)
                                }

                            })
                },
                onError = {
                    dismissDialog()
                    toast(it)
                })
    }

    private fun setListenerToRootView() {
        val rootView = window.decorView.findViewById<View>(android.R.id.content)
        rootView.viewTreeObserver.addOnGlobalLayoutListener {
            val mKeyboardUp = isKeyboardShown(rootView)
            if (mKeyboardUp) {
                //键盘弹出
                binding.layout1.translationY = -(transY.toFloat())
                isAnim = true
            } else {
                //键盘收起
                if (isAnim) {
                    binding.layout1.translationY = 0f
                    isAnim = false
                }
            }
        }
    }

    /**
     * 静态内部类handler
     */
    class MyStaticHandler(ref: WeakReference<ChangePasswordActivity>) : Handler() {

        private var activity: ChangePasswordActivity? = ref.get()

        override fun handleMessage(msg: Message) {
            if (activity != null) {
                when (msg.what) {
                    0 -> {
                        activity?.let {
                            EventBus.getDefault().post(EventFinishPage())
                            LoginHelper.sigOff(it)

                        }
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        handler.removeCallbacksAndMessages(null)
        if (dialog != null) {
            dialog?.dismiss()
            dialog = null
        }
    }

    private fun isKeyboardShown(rootView: View): Boolean {
        val softKeyboardHeight = 100
        val r = Rect()
        rootView.getWindowVisibleDisplayFrame(r)
        val dm = rootView.resources.displayMetrics
        val heightDiff = rootView.bottom - r.bottom
        Log.e("diff", heightDiff.toString())
        return heightDiff > softKeyboardHeight * dm.density
    }

    override val contentViewResId: Int
        get() = R.layout.activity_changepassword_layout

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityChangepasswordLayoutBinding {
        return ActivityChangepasswordLayoutBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean {
        return true
    }


    override fun openArouterReceive(): Boolean {
        return false
    }

}