package com.ddbes.personal.view

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.ddbes.personal.R
import com.ddbes.personal.contract.PersonCommitContract
import com.ddbes.personal.databinding.ActivityPersonnameLayoutBinding
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.ddbes.personal.viewmodel.ModifyOrgInfoViewModel
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.CommonUtils.verifyEmailFormat
import com.joinutech.flutter.EventRefreshGroupInfo
import com.joinutech.flutter.EventUpdateUserInfo
import org.greenrobot.eventbus.EventBus
import javax.inject.Inject
import javax.inject.Named

/**
 * Description 修改姓名
 * Author HJR36
 * Date 2018/6/20 18:31
 */
@Route(path = RoutePersonal.personNameOrEmailActivity)
class PersonNameActivity : MyUseBindingActivity<ActivityPersonnameLayoutBinding>() {

    var maxNum = 10

    var type = ""

    @Autowired
    @JvmField
    var companyId = ""

    var personName = ""

    @Inject
    @field:Named(PersonConstUtil.PERSON_COMMIT_PRESENTER)
    lateinit var presenter: PersonCommitContract.PersonCommitPresenter
    private lateinit var modifyOrgInfoViewModel: ModifyOrgInfoViewModel

    @SuppressLint("ResourceAsColor")
    override fun initImmersion() {
        if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("type"))) {
            type = intent.getStringExtra("type") ?: ""
        }
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("name"))) {
                personName = intent.getStringExtra("name") ?: ""
            }
        }
        if (type.isNullOrBlank()) {
            finish()
        }

        when (type) {
            "name" -> {
                setPageTitle("修改姓名")
            }
            "email", "cooperationEmail" -> {
                setPageTitle("修改邮箱")
            }
            "companyWeb" -> {
                setPageTitle("官方网站")
            }
            "companyPhone" -> {
                setPageTitle("联系电话")
            }
            "companyEmail" -> {
                setPageTitle("邮箱")
            }
            "groupName" -> {
                setPageTitle("修改群组名称")
            }
        }
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        setRightTitleColor(ContextCompat.getColor(mContext!!, R.color.color1A76F1),
                "保存", this)
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        DaggerPersonComponent.builder().build().inject(this)
        when (type) {
            "groupName" -> {
                binding.tvNameTip.visibility = View.GONE
                binding.tvGroupCount.visibility = View.VISIBLE
            }
            "email", "companyEmail", "cooperationEmail" -> {
                binding.tvEmailTip.visibility = View.VISIBLE
                binding.tvEmailRule.visibility = View.GONE
                binding.tvNameTip.text = "电子邮件地址的标准格式：用户名@服务器域名"
            }
            "companyWeb" -> {
                binding.tvNameTip.visibility = View.GONE
                binding.tvEmailRule.visibility = View.GONE
                binding.tvEmailRule.visibility = View.VISIBLE
                binding.tvEmailRule.text = "国际域名格式如下:\n" +
                        "域名由各国文字的特定字符集、英文字母、数字及“-”(即连字符或减号)任意组合而成, 但\n" +
                        "开头及结尾均不能含有“-”。 域名中字母不分大小写。域名最长可达67个字节(包括后\n" +
                        "缀.com、.net、.org等)。例如: your-name.com 即是一个合格的域名, 而www.your\n" +
                        "-name.com是域名your-name.com下名为www的主机名。\n" +
                        "中文国内域名格式如下：\n" +
                        "各级域名长度限制在20个合法字符（汉字，英文a-z，A-Z，数字0-9和-等均算一个字\n" +
                        "符）；\n" +
                        "不能是纯英文或数字域名，应至少有一个汉字。-不能连续出现"
            }
            "companyPhone" -> {
                binding.tvEmailTip.visibility = View.GONE
                binding.tvEmailRule.visibility = View.GONE
                binding.tvNameTip.visibility = View.VISIBLE
                binding.tvNameTip.text = "请不要输入阿拉伯数字0-9,(,),-以外的字符"
            }
        }
        modifyOrgInfoViewModel = getModel(ModifyOrgInfoViewModel::class.java)
    }

    override fun initLogic() {
        super.initLogic()
        when (type) {
            "name" -> {
                binding.etEmail.hint = "请输入姓名"
                binding.etEmail.maxLines = 1
            }
            "email", "cooperationEmail" -> {
                binding.etEmail.hint = "请输入邮箱"
                binding.etEmail.maxLines = 1

            }
            "companyWeb" -> {
                binding.etEmail.hint = "请输入团队官方网站域名"
                binding.etEmail.maxLines = 3
            }
            "companyPhone" -> {
                binding.etEmail.hint = "请输入团队联系电话"
                binding.etEmail.maxLines = 1
            }
            "companyEmail" -> {
                binding.etEmail.hint = "请输入团队联系邮箱"
                binding.etEmail.maxLines = 1
            }
            "groupName" -> {
                binding.etEmail.hint = "请输入群组名称"
            }
        }
        if (StringUtils.isNotBlankAndEmpty(personName)) {
            binding.etEmail.setText(personName)
        }
        binding.etEmail.addTextChangedListener(object : TextWatcher {

            override fun afterTextChanged(s: Editable?) {
                //编辑框内容变化之后会调用该方法，s为编辑框内容变化后的内容
                if (!(type == "email" || type == "companyEmail" || type == "cooperationEmail")) {
                    if (s != null) {
                        when (type) {
                            "name" -> {
                                maxNum = 10
                            }
                            "companyWeb" -> {
                                maxNum = 67
                            }
                            "companyPhone" -> {
                                maxNum = 20
                            }
                            "groupName" -> {
                                maxNum = 30
                            }
                        }
                        if (s.length > maxNum) {
                            when (type) {
                                "name" -> {
                                    ToastUtil.showCustomToast(null, mContext!!,
                                            true, "姓名最多${maxNum}个字符")
                                }
                                "companyWeb" -> {
                                    ToastUtil.showCustomToast(null, mContext!!,
                                            true,
                                            "请将域名控制在${maxNum}个字符内")
                                }
                                "companyPhone" -> {
                                    ToastUtil.showCustomToast(null, mContext!!,
                                            true,
                                            "请将联系电话控制在${maxNum}个字符内")
                                }
                                "groupName" -> {
                                    ToastUtil.showCustomToast(null, mContext!!,
                                            true, "群组名称最多${maxNum}个字符")
                                }
                            }
                            if (type != "groupName") s.delete(maxNum, s.length)
                            else {
                                binding.tvGroupCount.text = "${30 - (s.length)}"
                                binding.tvGroupCount.setTextColor(
                                        CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.red))
                            }
                        }
                        if (type != "groupName") {
                            val num = s.length
                            val textNumber = "$num/$maxNum"
                            binding.tvEmailCount.visibility = View.VISIBLE
                            binding.tvEmailCount.text = textNumber
                        }
                    }
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        })
        modifyOrgInfoViewModel.modifyOrgInfoSuccessObservable.observe(this, Observer {
            dismissDialog()
            setResult(Activity.RESULT_OK, intent)
            finish()
        })
        modifyOrgInfoViewModel.modifyOrgInfoErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    override val contentViewResId: Int
        get() = R.layout.activity_personname_layout

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityPersonnameLayoutBinding {
        return ActivityPersonnameLayoutBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return true
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            com.joinutech.ddbeslibrary.R.id.tv_toolbar_right-> {
                onSave()
            }
        }
    }

    private fun onSave() {
        val text = binding.etEmail.text.toString()
        when (type) {
            "name" -> {
                if (StringUtils.isEmpty(text)) {
                    toastShort("请输入姓名")
                } else {
                    presenter.commitPersonInfo(this, text, type, {

                        updateFlutterInfo()

                        UserHolder.onLogin(it, true)// change name
                        val intent = Intent()
                        intent.putExtra("tag", type)
                        intent.putExtra("name", text)
                        setResult(Activity.RESULT_OK, intent)
                        finish()
                    }, {
                        ToastUtil.show(mContext!!, it)
                    })
                }
            }
            "email", "companyEmail", "cooperationEmail" -> {
                if (StringUtils.isNotBlankAndEmpty(text) && !verifyEmailFormat(text)) {
                    ToastUtil.showCustomToast(null, mContext!!,
                            true, "请遵循输入框下方提示内容输入正确格式的联系邮箱")
                    return
                }
                when (type) {
                    "email" -> {
                        presenter.commitPersonInfo(this, text, type,
                                onSuccess = {

                                    updateFlutterInfo()
                                    //邮箱立即保存，导出使用
//                            UserHolder.getCurrentUser()?.email = text
                                    UserHolder.onLogin(it, true)// change email
//                            MMKVUtil.saveString(USER.EMAIL, text)
                                    val intent = Intent()
                                    intent.putExtra("tag", type)
                                    intent.putExtra("name", text)
                                    setResult(Activity.RESULT_OK, intent)
                                    finish()
                                },
                                onError = {
                                    ToastUtil.show(mContext!!, it)
                                })
                    }
                    "cooperationEmail" -> {
                        getLoadingDialog("", false)
                        presenter.commitCooperationOwnEmail(bindToLifecycle(), text, companyId,
                                accessToken!!, {
                            dismissDialog()
                            ToastUtil.show(mContext!!, "修改成功")

                                updateFlutterInfo()

                            intent.putExtra("email", text)
                            setResult(Activity.RESULT_OK, intent)
                            finish()
                        }, {
                            dismissDialog()
                            ToastUtil.show(mContext!!, it)
                        })
                    }
                    else -> {
                        val data = hashMapOf<String, Any>()
                        data["companyId"] = companyId
                        data["linkManMail"] = text
                        getLoadingDialog("", false)
                        modifyOrgInfoViewModel.modifyCompany(bindToLifecycle(), accessToken!!, data)
                    }
                }
            }
            "companyWeb" -> {
                if (!StringUtils.isWebUrl(text)) {
                    //不是域名
                    ToastUtil.showCustomToast(null, mContext!!,
                            true, "请遵循输入框下方提示内容输入正确格式的域名")
                    return
                }
                val data = hashMapOf<String, Any>()
                data["companyId"] = companyId
                data["officialWebsite"] = text
                getLoadingDialog("", false)
                modifyOrgInfoViewModel.modifyCompany(bindToLifecycle(), accessToken!!, data)
            }
            "companyPhone" -> {
                if (!StringUtils.isTelPhone(text) && !StringUtils.isPhoneNumber(text)) {
                    //既不是手机号也不是电话号
                    ToastUtil.showCustomToast(null, mContext!!,
                            true, "请遵循输入框下方提示内容输入正确格式的联系电话")
                    return
                }
                val data = hashMapOf<String, Any>()
                data["companyId"] = companyId
                data["linkManPhone"] = text
                getLoadingDialog("", false)
                modifyOrgInfoViewModel.modifyCompany(bindToLifecycle(), accessToken!!, data)
            }
            "groupName" -> {
                if (text.length > 30) {
                    toastShort("群组名称不得超过30字")
                    return
                }
                if (StringUtils.isNotBlankAndEmpty(text)) {
                    val intent = Intent()
                    intent.putExtra("groupName", text)
                    setResult(Activity.RESULT_OK, intent)

                    EventBus.getDefault().post(EventRefreshGroupInfo())
                }
                finish()
            }
        }


    }


    private fun updateFlutterInfo() {
        EventBus.getDefault().post(EventUpdateUserInfo())
    }


}