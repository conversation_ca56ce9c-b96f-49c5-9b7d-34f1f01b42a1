package com.ddbes.personal.view

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Handler
import android.text.method.ScrollingMovementMethod
import android.text.style.ForegroundColorSpan
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.Observer
import com.alibaba.android.arouter.facade.annotation.Route
import com.ddbes.personal.databinding.ActivityAboutappBinding
import com.joinutech.ddbeslibrary.R
import com.ddbes.personal.viewmodel.AboutAppViewModel
import com.joinutech.common.base.LinkBuilder
import com.joinutech.ddbeslibrary.VersionConfig
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.base.NoticeWebActivity
import com.joinutech.ddbeslibrary.bean.AppVersionBean
import com.joinutech.ddbeslibrary.utils.AppVersionCheckUpdateUtil
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.MMKVUtil
import com.joinutech.ddbeslibrary.utils.MyDialog
import com.joinutech.ddbeslibrary.utils.RoutePersonal
import com.joinutech.ddbeslibrary.utils.ScreenUtils
import com.joinutech.ddbeslibrary.utils.Spanny
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil

/**
 * 关于担当
 * Created by liuchuchu on 2018/6/20.
 */
@Route(path = RoutePersonal.PERSON_ABOUT_APP)
class AboutAppActivity : MyUseBindingActivity<ActivityAboutappBinding>() {

    private lateinit var viewModel: AboutAppViewModel

//    private val versionName = MMKVUtil.getString(MMKVKEY.VERSION_NAME)
//    private val versionName = "2.4.4"
    private val versionConfig = VersionConfig()
    private val versionName = versionConfig.versionName

    override fun initImmersion() {
        setPageTitle("关于担当")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        showToolBarLine()
    }

//    private var attendCheck = 0

    @SuppressLint("SetTextI18n")
    override fun initView() {
        whiteStatusBarBlackFont()
        binding.tvVersionContent.maxHeight = ScreenUtils.dip2px(mContext, 260f)
        binding.tvVersionContent.movementMethod = ScrollingMovementMethod.getInstance()
        val spanny = Spanny().append("担当办公由").append("加优科技有限公司",
                ForegroundColorSpan(
                        CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color808080)))
                .append("提供技术服务", ForegroundColorSpan(
                        CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color999999)))
        binding.tvServiceName.text = spanny

//        val json = MMKVUtil.getString("appNewVersion")
//        if (json.isNotBlank() && json.startsWith("{") && json.endsWith("}")) {
//            val versionData = GsonUtil.fromJson<AppVersionBean>(json)
//            if (versionData != null && versionData.auditing == 1) {
//                tv_version_update.visibility = View.VISIBLE
//                tv_version_update.setOnClickListener(this)
//            } else {
//                tv_version_update.visibility = View.GONE
//            }
//        } else {
//            tv_version_update.visibility = View.GONE
//        }
    }

    override fun initLogic() {
        super.initLogic()
        viewModel = getModel(AboutAppViewModel::class.java)
        getObservable()

        binding.tvVersionName.text = "版本号：$versionName"
        binding.tvVersionCode.text = "$versionName 版本更新内容：\n${viewModel.getVersionInfo().versionDesc}"
        binding.tvPricacyEntrance.setOnClickListener {
            val intent = Intent(this, NoticeWebActivity::class.java)
            intent.putExtra("targetUrl", LinkBuilder.generate().getPrivacyUrl())
            intent.putExtra("title", "担当办公隐私政策条款")
            intent.putExtra("isShowConfirmButton", false)
            intent.putExtra("enablePermission" , 0)
            startActivity(intent)
        }

//        tv_version_update.setOnClickListener(this)
//        if (intent.getBooleanExtra("checkUpdate", false)) {
//            checkAppUpdate()
//        }
    }

    @SuppressLint("SetTextI18n")
    private fun getObservable() {
        viewModel.validateVersionObservable.observe(this, Observer {
            dismissDialog()
            val downUrl = it.downUrl
            binding.tvVersionCode.text = "${it.versionName} 版本更新内容："
            binding.tvVersionContent.text = it.desc
            //先判断有没有当前版本的版本描述，因为不变，所以只需存储一次即可
            if (StringUtils.isNotBlankAndEmpty(it.currentDesc)) {
                val versionContent =
                        MMKVUtil.getString("appVersionNum${versionName}")
                if (StringUtils.isEmpty(versionContent)) {
                    MMKVUtil.saveString("appVersionNum${versionName}", it.currentDesc)
                }
            }
            when (it.status) {
                0 -> {
                    val view = View.inflate(mContext!!, com.ddbes.personal.R.layout.dialog_version_success, null)
                    val myDialog = MyDialog(mContext!!, 300, 63, ""
                            , needBtnConfirm = false, needBtnCancel = false, bgResourceId = 0)
                    myDialog.setView(view, Gravity.CENTER)
                    myDialog.setCanceledOnTouchOutside(true)
                    myDialog.show()
                    val handler = Handler()
                    handler.postDelayed({
                        if (myDialog.isShowing) {
                            myDialog.dismiss()
                        }
                    }, 1500)
                }
                else -> {
                    //弹出检查更新对话框
                    AppVersionCheckUpdateUtil.updateNewVersionDialog(this, it)
                }
            }
        })
        viewModel.validateVersionErrorObservable.observe(this, Observer {
            dismissDialog()
            val json = MMKVUtil.getString("appMustUpdate")
            if (json.isNotBlank()) {
                val version = GsonUtil.fromJson(json, AppVersionBean::class.java)
                if (version != null) {
                    AppVersionCheckUpdateUtil.updateNewVersionDialog(this, version)
                } else {
                    ToastUtil.show(mContext!!, it)
                }
            } else {
                ToastUtil.show(mContext!!, it)
            }
        })
    }

    override fun onNoDoubleClick(v: View) {
        if (v.id == com.ddbes.personal.R.id.tv_version_update) {
            checkAppUpdate()
        }
    }

    /**App检查版本更新中*/
    //检查更新tcp，说明：versionConfig.versionCode2就是在config.gradle中发版时手动加1的versionCode值；
    private fun checkAppUpdate() {
        getLoadingDialog("检查版本更新中", true)
//        val versionCode = MMKVUtil.getInt(MMKVKEY.VERSION_CODE, 1)
        viewModel.validateVersion(bindToLifecycle(), "2", versionName, versionConfig.versionCode2)
    }

    override val contentViewResId: Int
        get() = com.ddbes.personal.R.layout.activity_aboutapp

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityAboutappBinding {
        return ActivityAboutappBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return false
    }
}