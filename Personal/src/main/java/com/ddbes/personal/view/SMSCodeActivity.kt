package com.ddbes.personal.view

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Color
import android.os.Handler
import android.os.Message
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.*
import com.ddbes.personal.R
import com.ddbes.personal.databinding.ActivitySmsCodeBinding
import com.joinutech.common.util.UserHolder
import com.joinutech.common.util.ImageVerifyDialog
import com.joinutech.common.util.ImageVerifyTouchListener
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.VerifyImageBean
import com.joinutech.ddbeslibrary.request.CommonResult
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.request.exception.NoDataTransformer
import com.joinutech.ddbeslibrary.service.LoginService
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.utils.toastShort
import com.trello.rxlifecycle3.LifecycleTransformer

/**
 * @PackageName: com.ddbes.personal.view
 * @ClassName:
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/10/9 11:07
 * @Desc: 手机验证码页面 验证微信绑定账号、私有群组、团队和协作团队是否都退出
 */
class SMSCodeActivity : MyUseBindingActivity<ActivitySmsCodeBinding>(), SMSCodeView {
    override fun initImmersion() {
        whiteStatusBarBlackFont()
    }

    private val title = "注销担当账号"
    override fun initView() {
        setPageTitle(title)
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        showToolBarLine()

        binding.tvSendCode.setOnClickListener(this)
        binding.btnConfirm.setOnClickListener(this)
        if (UserHolder.isLogin()) {
            UserHolder.getCurrentUser()?.let {
                ImageLoaderUtils.loadImage(this, findViewById(R.id.civ_avatar), it.avatar)
                phone = it.mobile
                binding.tvName.text = it.name
                binding.tvPhone.text = "请验证手机号：$phone"
            }
        }
    }

    private val smsCodeType = 16
    lateinit var viewModel: SMSCodeViewModel

    override fun initLogic() {
        super.initLogic()
        viewModel = getModel(SMSCodeViewModel::class.java)
        viewModel.initObserver(this, this)
    }

    private var isFirstSendMsg = true

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.tv_send_code -> {//获取验证码
                if (StringUtils.isNotBlankAndEmpty(phone) && StringUtils.isPhoneNumber(phone)) {
                    showLoading()
                    if (isFirstSendMsg) {
                        isFirstSendMsg = false
                        viewModel.onGetImageVerify(bindToLifecycle(), phone)
                    } else {
                        viewModel.onGetSMSCode(bindToLifecycle(), phone, smsCodeType)
                    }
                } else {
                    ToastUtil.show(mContext!!, "手机号错误，无法获取验证码")
                }
            }
            R.id.btn_confirm -> {
                checkCode()
            }
        }
    }

    private fun checkCode() {
        if (!binding.etCodeInput.text.isNullOrBlank() && StringUtils.isPhoneNumber(phone)) {
            viewModel.onCheckSMSCode(bindToLifecycle(), phone, smsCodeType, binding.etCodeInput.text.toString().trim())
        } else {
            toastShort("请输入验证码")
        }
    }

    private var phone = ""
    private var time = 60
    var msg1 = 0x111

    @SuppressLint("HandlerLeak")
    private var mHandler = object : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            if (msg.what == msg1) {
                time--
                binding.tvSendCode.isEnabled = false
                binding.tvSendCode.text = "重新获取".plus(time).plus("S")
                binding.tvSendCode.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.text_black))
                if (time > 0) {
                    sendEmptyMessageDelayed(msg1, 1000)
                } else {
                    time = 60
                    binding.tvSendCode.isEnabled = true
                    binding.tvSendCode.text = "重新获取"
                    binding.tvSendCode.setTextColor(Color.parseColor("#5786EE"))
                    removeCallbacksAndMessages(null)
                }
            }
        }
    }

    override fun onGetImageVerifyFailed(msg: String) {
        dismissDialog()
        toastShort(msg)
    }

    private var imageVerify: ImageVerifyDialog? = null
    fun init() {
        imageVerify = ImageVerifyDialog.Builder()
                .with(this)
                .addTouchListener(object : ImageVerifyTouchListener {
                    override fun onRefreshImage() {
                        onRefreshImageVerify()
                    }

                    override fun onCheckImage(backId: String, topId: String, scale: Double) {
                        onImageVerifyResult(backId, topId, scale)
                    }
                }).build()
    }

    override fun showImageVerify(data: VerifyImageBean) {
        dismissDialog()
        if (imageVerify == null) {
            init()
        }
        imageVerify?.show(data.backUrl, data.topUrl)
    }

    override fun onRefreshImageVerify() {
        showLoading()
        viewModel.onGetImageVerify(bindToLifecycle(), phone)
    }

    override fun onImageVerifyResult(newId: String, oriId: String, scale: Double) {
        showLoading()
        viewModel.onCheckImageVerify(bindToLifecycle(), phone, smsCodeType, newId, oriId, scale)
    }

    override fun onCheckImageVerifyResult(code: Int, msg: String) {
        dismissDialog()
        if (code == 1) {
            imageVerify?.onVerifySuccess {
                imageVerify?.hide()
                toastShort("发送成功")
                mHandler.sendEmptyMessage(msg1)
            }
        } else {
            imageVerify?.onVerifyFailed()
            ToastUtil.show(mContext!!, msg)
            imageVerify?.reset()
        }
    }

    override fun onGetSmsCodeResult(success: Boolean, msg: String) {
        dismissDialog()
        if (success) {
            toastShort("发送成功")
            mHandler.sendEmptyMessage(msg1)
        } else {
            toastShort(msg)
        }
    }

    override fun onCheckSMSCodeResult(success: Boolean, data: String) {
        if (success) {
            val intent = Intent(this, UnRegisterAccountActivity::class.java)
            intent.putExtra("code", data)// 校验验证码返回临时code
            startActivity(intent)
            finish()
        } else {
            toastShort(data)
        }
    }

    override fun onDestroy() {
        imageVerify?.hide()
        mHandler.removeCallbacksAndMessages(null)
        super.onDestroy()
    }

    override val contentViewResId: Int = R.layout.activity_sms_code
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivitySmsCodeBinding {
        return ActivitySmsCodeBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = false

}

interface SMSCodeView {
    /**获取图片验证码失败*/
    fun onGetImageVerifyFailed(msg: String)

    /**显示图片验证码*/
    fun showImageVerify(data: VerifyImageBean)

    /**刷新图片验证码*/
    fun onRefreshImageVerify()

    /**接收图片滑动停止后结果回调*/
    fun onImageVerifyResult(newId: String, oriId: String, scale: Double)

    /**图片验证码验证 结果*/
    fun onCheckImageVerifyResult(code: Int, msg: String)

    /**获取验证码结果回调*/
    fun onGetSmsCodeResult(success: Boolean, msg: String)

    /**校验验证码后结果处理*/
    fun onCheckSMSCodeResult(success: Boolean, data: String)

}

/**
 * 第一次发送验证码先获取图片验证
 * 再次获取时直接发送
 */
class SMSCodeViewModel : ViewModel() {

    fun initObserver(owner: LifecycleOwner, view: SMSCodeView) {
        getImageVerify.observe(owner, Observer { result ->
            RequestHelper.onResponse(result,
                    onSuccess = { data -> view.showImageVerify(data) },
                    onError = { _, msg -> view.onGetImageVerifyFailed(msg) },
                    onDefault = {
                        view.onGetImageVerifyFailed("获取验证码失败")
                    })
        })

        checkImageVerify.observe(owner, Observer { result ->
            RequestHelper.onResponse(result,
                    onSuccess = {
                        view.onCheckImageVerifyResult(1, "")
                    },
                    onError = { code, msg ->
                        view.onCheckImageVerifyResult(code, msg)
                    },
                    onDefault = {
                        view.onCheckImageVerifyResult(0, "验证失败")
                    })
        })

        getSmsCode.observe(owner, Observer { result ->
            RequestHelper.onResponse(result,
                    onSuccess = { data -> view.onGetSmsCodeResult(true, "") },
                    onError = { _, msg -> view.onGetSmsCodeResult(true, msg) },
                    onDefault = {
                        view.onGetSmsCodeResult(true, "获取验证码失败")
                    })
        })

        checkSmsCode.observe(owner, Observer { result ->
            RequestHelper.onResponse(result,
                    onSuccess = { data -> view.onCheckSMSCodeResult(true, data) },
                    onError = { _, msg -> view.onCheckSMSCodeResult(false, msg) },
                    onDefault = { view.onCheckSMSCodeResult(false, "验证码错误") })
        })
    }

    private val _getImageVerify = MutableLiveData<CommonResult<VerifyImageBean>>()
    private val getImageVerify: LiveData<CommonResult<VerifyImageBean>> = _getImageVerify

    /**获取图片验证码*/
    fun onGetImageVerify(life: LifecycleTransformer<Result<VerifyImageBean>>, phone: String) {
        //获取图片验证码
        LoginService.getVerifyImage(phone)
                .compose(life)
                .compose(ErrorTransformer.getInstance<VerifyImageBean>())
                .subscribe(object : BaseSubscriber<VerifyImageBean>() {
                    override fun onError(ex: ApiException) {
                        //该手机号未注册
                        _getImageVerify.value = CommonResult(errorCode = 0, extra = if (ex.code == 1104) {
                            "该手机号未注册"
                        } else {
                            ex.message
                        })
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: VerifyImageBean?) {
                        _getImageVerify.value = CommonResult(success = t)
                    }
                })
    }

    private val _checkImageVerify = MutableLiveData<CommonResult<Any>>()
    private val checkImageVerify: LiveData<CommonResult<Any>> = _checkImageVerify

    /**验证图片验证码，成功后会发送验证码 16*/
    fun onCheckImageVerify(life: LifecycleTransformer<Result<Any>>, phone: String, type: Int,
                           backId: String, topId: String, scale: Double) {
        val hashMap = hashMapOf<String, Any>()
        hashMap["newId"] = backId
        hashMap["oriId"] = topId
        hashMap["phone"] = phone
        hashMap["type"] = type //验证码类型
        hashMap["scale"] = scale

        //验证图片验证码带短信验证码
        LoginService.verifyImageWithMsg(hashMap)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        _checkImageVerify.value = CommonResult(errorCode = 0, extra = ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(data: Any?) {
                        _checkImageVerify.value = CommonResult(success = data)
                    }

                })
    }

    private val _getSmsCode = MutableLiveData<CommonResult<Any>>()
    private val getSmsCode: LiveData<CommonResult<Any>> = _getSmsCode

    /**手动获取验证码 手机号 验证码类型 注销 16*/
    fun onGetSMSCode(life: LifecycleTransformer<Result<Any>>, phone: String, type: Int) {
        LoginService.sendSms(phone, type)
                .compose(life)
                .compose(NoDataTransformer)
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        //该手机号未注册
                        _getSmsCode.value = CommonResult(errorCode = 0, extra = if (ex.code == 1104) {
                            "该手机号未注册"
                        } else {
                            ex.message
                        })
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(data: Any?) {
                        _getSmsCode.value = CommonResult(success = data)
                    }
                })
    }

    private val _checkSmsCode = MutableLiveData<CommonResult<String>>()
    private val checkSmsCode: LiveData<CommonResult<String>> = _checkSmsCode

    fun onCheckSMSCode(life: LifecycleTransformer<Result<String>>, phone: String, type: Int, code: String) {
        val hashMap = hashMapOf<String, Any>()
        hashMap["phone"] = phone
        hashMap["type"] = type //验证码类型
        hashMap["code"] = code
        LoginService.verifySMSCode(hashMap)
                .compose(life)
                .compose(ErrorTransformer.getInstance<String>())
                .subscribe(object : BaseSubscriber<String>() {
                    override fun onError(ex: ApiException) {
                        _checkSmsCode.value = CommonResult(errorCode = 0, extra = ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(data: String?) {
                        _checkSmsCode.value = CommonResult(success = data)
                    }

                })
    }
}