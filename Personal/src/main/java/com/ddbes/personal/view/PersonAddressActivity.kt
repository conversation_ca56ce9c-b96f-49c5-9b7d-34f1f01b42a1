package com.ddbes.personal.view

import android.app.Activity
import android.content.Intent
import android.view.LayoutInflater
import androidx.core.content.ContextCompat
import android.view.View
import com.ddbes.library.im.imtcp.ConstantEventCodeUtil
import com.ddbes.personal.R
import com.ddbes.personal.adapter.PersonAddressAdapter
import com.ddbes.personal.bean.PersonAddressBean
import com.ddbes.personal.contract.PersonCommitContract
import com.ddbes.personal.databinding.ActivityPersonadrressLayoutBinding
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.DistrictModel
import com.ddbes.personal.util.PersonConstUtil
import com.ddbes.personal.util.ProvinceModel
import com.ddbes.personal.util.XmlParserHandler
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.*
import java.util.HashMap
import javax.inject.Inject
import javax.inject.Named
import javax.xml.parsers.SAXParserFactory

/**
 * Description 修改地区
 * Author HJR36
 * Date 2018/6/21 14:09
 */
class PersonAddressActivity : MyUseBindingActivity<ActivityPersonadrressLayoutBinding>() {
    override fun openArouterReceive(): Boolean {
        return false
    }

    private val list = ArrayList<String>()
    private val dataList = ArrayList<PersonAddressBean>()

    /**
     * 所有省
     */
    private var mProvinceDatas: Array<String?> = arrayOf()

    /**
     * key - 省 value - 市
     */
    private var mCitisDatasMap: MutableMap<String?, Array<String?>> = HashMap()

    /**
     * key - 市 values - 区
     */
    private var mDistrictDatasMap: MutableMap<String?, Array<String?>> = HashMap()

    /**
     * key - 区 values - 邮编
     */
    private var mZipcodeDatasMap: MutableMap<String?, String> = HashMap()

    /**
     * 当前省的名称
     */
    private lateinit var mCurrentProviceName: String

    /**
     * 当前市的名称
     */
    private lateinit var mCurrentCityName: String

    /**
     * 当前区的名称
     */
    private var mCurrentDistrictName = ""

    /**
     * 当前区的邮政编码
     */
    private var mCurrentZipCode = ""

    private var type = "province"

    private lateinit var adapter: PersonAddressAdapter

    private lateinit var mRegionStr: String

    @Inject
    @field:Named(PersonConstUtil.PERSON_COMMIT_PRESENTER)
    lateinit var presenter: PersonCommitContract.PersonCommitPresenter

    var cityLevel: String? = null
    override fun initImmersion() {
        val pageTitle = intent.getStringExtra("pageTitle") ?: ""
        cityLevel = intent.getStringExtra("cityLevel") ?: ""
        if (StringUtils.isEmpty(pageTitle)) {
            setPageTitle("修改地区")
        } else {
            setPageTitle(pageTitle)
        }
        setLeftTitleTextSize(
            "取消", this, ContextCompat.getColor(mContext!!, R.color.color646464)
        )
    }

    override val contentViewResId: Int
        get() = R.layout.activity_personadrress_layout

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityPersonadrressLayoutBinding {
        return ActivityPersonadrressLayoutBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        DaggerPersonComponent.builder().build().inject(this)
        binding.tvSearchHint.text = "请选择省份"
        val layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this)
        binding.rvAddressList.layoutManager = layoutManager
        //添加自定义分割线
        val divider = androidx.recyclerview.widget.DividerItemDecoration(
            this,
            androidx.recyclerview.widget.DividerItemDecoration.VERTICAL
        )
        divider.setDrawable(ContextCompat.getDrawable(this, com.joinutech.ddbeslibrary.R.drawable.shape_bg_recylview_line)!!)
        binding.rvAddressList.addItemDecoration(divider)
    }

    override fun initLogic() {
        super.initLogic()
        initProvinceDatas()
        refreshAdapterData()
        adapter = PersonAddressAdapter(dataList, mContext!!, binding.rvAddressList, object
            : PersonAddressAdapter.MyItemClickListener {
            //点击事件
            override fun setOnItemClick(personAddressBean: PersonAddressBean) {
                dealItemSelectChange(personAddressBean)
            }
        })
        binding.rvAddressList.adapter = adapter
    }

    private fun refreshAdapterData() {
        when (type) {
            "province" -> {
                binding.tvSearchHint.text = "请选择省份"
            }
            "city" -> {
                binding.tvSearchHint.text = "请选择城市"
            }
            "distance" -> {
                binding.tvSearchHint.text = "请选择区县"
                setRightTitleColor(
                    ContextCompat.getColor(mContext!!, R.color.color1A76F1),
                    "完成", this
                )
            }
        }
        if (list.size > 0) {
            list.clear()
        }
        if (dataList.size > 0) {
            dataList.clear()
        }

        when (type) {
            "city" -> {
                mProvinceDatas = mCitisDatasMap[mCurrentProviceName]!!
            }
            "distance" -> {
                mProvinceDatas = mDistrictDatasMap[mCurrentCityName]!!
            }
        }
        for (mProvinceData in mProvinceDatas) {
            list.add(mProvinceData!!)
        }
        for (it in list) {
            val personAddressBean = PersonAddressBean()
            personAddressBean.address = it
            dataList.add(personAddressBean)
        }
        when (type) {
            "city", "distance" -> {
                adapter.setData(dataList)
            }
        }
    }

    private fun initProvinceDatas() {
        val provinceList: List<ProvinceModel>?
        val asset = assets
        try {
            val input = asset.open("province_data.xml")
            // 创建一个解析xml的工厂对象
            val spf = SAXParserFactory.newInstance()
            // 解析xml
            val parser = spf.newSAXParser()
            val handler = XmlParserHandler()
            parser.parse(input, handler)
            input.close()
            // 获取解析出来的数据
            provinceList = handler.dataList
            //*/ 初始化默认选中的省、市、区
            if (provinceList != null && !provinceList.isEmpty()) {
                mCurrentProviceName = provinceList[0].name
                val cityList = provinceList[0].cityList
                if (cityList != null && !cityList.isEmpty()) {
                    mCurrentCityName = cityList[0].name
                    val districtList = cityList[0].districtList
                    mCurrentDistrictName = districtList[0].name
                    mCurrentZipCode = districtList[0].zipcode
                }
            }
            //*/
            mProvinceDatas = arrayOfNulls(provinceList.size)
            for (i in provinceList.indices) {
                // 遍历所有省的数据
                mProvinceDatas[i] = provinceList[i].name
                val cityList = provinceList[i].cityList
                val cityNames = arrayOfNulls<String>(cityList.size)
                for (j in cityList.indices) {
                    // 遍历省下面的所有市的数据
                    cityNames[j] = cityList[j].name
                    val districtList = cityList[j].districtList
                    val distrinctNameArray = arrayOfNulls<String>(districtList.size)
                    val distrinctArray = arrayOfNulls<DistrictModel>(districtList.size)
                    for (k in districtList.indices) {
                        // 遍历市下面所有区/县的数据
                        val districtModel =
                            DistrictModel(districtList[k].name, districtList[k].zipcode)
                        // 区/县对于的邮编，保存到mZipcodeDatasMap
                        mZipcodeDatasMap[districtList[k].name] = districtList[k].zipcode
                        distrinctArray[k] = districtModel
                        distrinctNameArray[k] = districtModel.name
                    }
                    // 市-区/县的数据，保存到mDistrictDatasMap
                    mDistrictDatasMap[cityNames[j]] = distrinctNameArray
                }
                // 省-市的数据，保存到mCitisDatasMap
                mCitisDatasMap[provinceList[i].name] = cityNames
            }
        } catch (e: Throwable) {
            e.printStackTrace()
        } finally {

        }
    }

    //点击事件
    private fun dealItemSelectChange(personAddressBean: PersonAddressBean) {
        val address = personAddressBean.address
        when (type) {
            "province" -> {
                type = "city"
                mCurrentProviceName = address
                refreshAdapterData()
            }
            "city" -> {
                type = "distance"
                mCurrentCityName = address
                if (cityLevel == "市") {//选择城市，只选到市就行了
                    val result=if(mCurrentProviceName==mCurrentCityName){
                        mCurrentProviceName
                    }else{
                        "$mCurrentProviceName$mCurrentCityName"
                    }
                    finishBack(result)
                } else {
                    refreshAdapterData()
                }

            }
            "distance" -> {
                mCurrentDistrictName = address
                mRegionStr = "$mCurrentProviceName$mCurrentCityName$mCurrentDistrictName"
            }
        }


    }

    private fun finishBack(result: String) {
        Loggerr.i("选择城市","==回调==result=${result}")
        EventBusUtils.sendEvent(
            EventBusEvent(
                "tcp_city_selector",
                result
            )
        )
        finish()
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            com.joinutech.ddbeslibrary.R.id.tv_toolbar_right-> {//点击完成
                var address = ""
                for (it in dataList) {
                    if (it.isSelected) {
                        address = it.address
                    }
                }
                mCurrentDistrictName = address
                mRegionStr = "$mCurrentProviceName$mCurrentCityName$mCurrentDistrictName"
                getLoadingDialog("修改地区", false)
                presenter.commitPersonInfo(this, mRegionStr, "address", {
                    dismissDialog()
                    val intent = Intent()
                    intent.putExtra("address", mRegionStr)
                    setResult(Activity.RESULT_OK, intent)
                    finish()
                }, {
                    dismissDialog()
                    ToastUtil.show(mContext!!, it)
                })
            }
            com.joinutech.ddbeslibrary.R.id.toolbar_title_left -> {
                finish()
            }
        }
    }
}