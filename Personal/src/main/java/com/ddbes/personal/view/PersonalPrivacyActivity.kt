package com.ddbes.personal.view

import android.view.LayoutInflater
import android.view.View
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.personal.R
import com.ddbes.personal.contract.PersonSettingContract
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.bean.PersonSettingBean
import com.ddbes.library.im.imtcp.Logger
import com.ddbes.library.im.imtcp.imservice.strangerutil.StrangerSettingUtil
import com.ddbes.personal.databinding.ActivityPerPrivacyBinding
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.OnNoDoubleClickListener
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.ToastUtil
import javax.inject.Inject
import javax.inject.Named

/**
 * 隐私
 * Created by liuchuchu on 2018/6/20.
 */
class PersonalPrivacyActivity : MyUseBindingActivity<ActivityPerPrivacyBinding>() {

    @Inject
    @field:Named(PersonConstUtil.PERSONSETTING_PRESENTER)
    lateinit var presenter: PersonSettingContract.PersonSettingPresenter
    private var viewStatus: Int = 1//1.好友及同事可见 0.仅同事可见 ,
    private var mobileSwitch: Int = 1//手机号搜索添加1.开启 0.关闭 ,
    private var groupSwitch: Int = 1//群组搜索添加 1.开启 0.关闭
    private var organizationSwitch: Int = 1//团队架构搜索添加 1.开启 0.关闭
    private var cooperateSwitch: Int = 1//外部协作添加 1.开启 0.关闭
    private var strangerSwitch: Int = 0//是否允许陌生人发送消息 1.开启 0.关闭

    override fun openArouterReceive(): Boolean {
        return false
    }

    override fun initImmersion() {
        setPageTitle("隐私")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        DaggerPersonComponent.builder().build().inject(this)
    }

    override fun initLogic() {
        super.initLogic()
        getLoadingDialog("加载隐私设置。。。", false)
        presenter.watchSettingData(bindToLifecycle<Result<PersonSettingBean>>(), accessToken, {
            Logger.i("----验证---隐私设置的数据---", "----json---" + GsonUtil.toJson(it))
            dismissDialog()
            dealResult(it)
        }, {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    override val contentViewResId: Int
        get() = R.layout.activity_per_privacy

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityPerPrivacyBinding {
        return ActivityPerPrivacyBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean {
        return true
    }


    private fun updateSetting(map: HashMap<String, Any>) {
        presenter.updateSettingData(bindToLifecycle(), accessToken, map, {
        }, {
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun updateStrangSetting(map: HashMap<String, Any>) {
        StrangerSettingUtil.updateStrangerSetting(bindToLifecycle(), accessToken, map, onSuccess = {}, onError = {
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun dealResult(it: PersonSettingBean) {
        viewStatus = it.viewStatus
        if (viewStatus == 1) {
            binding.check1Privacy.visibility = View.VISIBLE
            binding.check2Privacy.visibility = View.GONE
        } else {
            binding.check1Privacy.visibility = View.GONE
            binding.check2Privacy.visibility = View.VISIBLE
        }
        mobileSwitch = it.mobileSwitch
        groupSwitch = it.groupSwitch
        organizationSwitch = it.organizationSwitch
        cooperateSwitch = it.cooperateSwitch
        strangerSwitch = it.strangerSwitch
        binding.switch2Privacy.setCheckedNoEvent(mobileSwitch == 1)
        binding.switch3Privacy.setCheckedNoEvent(groupSwitch == 1)
        binding.switch4Privacy.setCheckedNoEvent(organizationSwitch == 1)
        binding.switch5Privacy.setCheckedNoEvent(cooperateSwitch == 1)
        binding.switch6Stranger.setCheckedNoEvent(strangerSwitch == 1)
        updateStatus()
    }

    //更新的监听
    private fun updateStatus() {
        binding.switch2Privacy.setOnCheckedChangeListener { _, isChecked ->
            //加这一条，否则当我setChecked()时会触发此listener
            mobileSwitch = if (!isChecked) {
                //从选中到未选中
                0
            } else {
                1
            }
            val map = hashMapOf<String, Any>()
            map["mobileSwitch"] = mobileSwitch
            updateSetting(map)
        }
        binding.switch3Privacy.setOnCheckedChangeListener { _, isChecked ->
            groupSwitch = if (!isChecked) {
                //从选中到未选中
                0
            } else {
                1
            }
            val map = hashMapOf<String, Any>()
            map["groupSwitch"] = groupSwitch
            updateSetting(map)
        }
        binding.switch4Privacy.setOnCheckedChangeListener { _, isChecked ->
            organizationSwitch = if (!isChecked) {
                //从选中到未选中
                0
            } else {
                1
            }
            val map = hashMapOf<String, Any>()
            map["organizationSwitch"] = organizationSwitch
            updateSetting(map)
        }
        binding.allWatch.setOnClickListener(object : OnNoDoubleClickListener {
            override fun onNoDoubleClick(v: View) {
//                if (viewStatus == 0) {
                viewStatus = 1
                binding.check1Privacy.visibility = View.VISIBLE
                binding.check2Privacy.visibility = View.GONE
//                }
                val map = hashMapOf<String, Any>()
                map["viewStatus"] = viewStatus
                updateSetting(map)
            }
        })
        binding.someWatch.setOnClickListener(object : OnNoDoubleClickListener {
            override fun onNoDoubleClick(v: View) {
//                if (viewStatus == 1) {
                viewStatus = 0
                binding.check1Privacy.visibility = View.GONE
                binding.check2Privacy.visibility = View.VISIBLE
//                }
                val map = hashMapOf<String, Any>()
                map["viewStatus"] = viewStatus
                updateSetting(map)
            }
        })
        binding.switch5Privacy.setOnCheckedChangeListener { _, isChecked ->
            //                加这一条，否则当我setChecked()时会触发此listener
            cooperateSwitch = if (!isChecked) {
                //从选中到未选中
                0
            } else {
                1
            }
            val map = hashMapOf<String, Any>()
            map["cooperateSwitch"] = cooperateSwitch
            updateSetting(map)
        }

        binding.switch6Stranger.setOnCheckedChangeListener { _, isChecked ->
            //                加这一条，否则当我setChecked()时会触发此listener
            strangerSwitch = if (!isChecked) {
                //从选中到未选中
                0
            } else {
                1
            }
            val map = hashMapOf<String, Any>()
            map["strangerSwitch"] = strangerSwitch
            updateStrangSetting(map)
        }
        //点击黑名单
        binding.rlBlackLayout.setOnClickListener(object :OnNoDoubleClickListener{
            override fun onNoDoubleClick(v: View) {
                //跳转到黑名单列表页面
                ARouter.getInstance()
                        .build(RouteOrg.blackListActivity)//tcp这个是自己新添加的
                        .withString("type", "transMsg")
                        .navigation()
            }
        })
    }
}