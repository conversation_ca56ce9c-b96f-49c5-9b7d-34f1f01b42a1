package com.ddbes.personal.view

import android.animation.Animator
import android.annotation.SuppressLint
import android.graphics.Rect
import android.os.Build
import android.os.Handler
import android.os.Message
import android.text.Editable
import android.text.InputType
import android.text.TextWatcher
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatSeekBar
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.Observer
import com.bumptech.glide.request.RequestOptions
import com.ddbes.personal.R
import com.ddbes.personal.contract.PersonalVerContract
import com.ddbes.personal.databinding.ActivityPerVBinding
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.ddbes.personal.viewmodel.ChangePasswordViewModel
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.VerifyImageBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformerOnlyBean
import com.joinutech.ddbeslibrary.service.LoginService
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import me.jessyan.autosize.internal.CustomAdapt
import javax.inject.Inject
import javax.inject.Named

/**
 * 身份验证,更换手机
 * Created by liuchuchu on 2018/6/15.
 */

class PersonalVerifyActivity : MyUseBindingActivity<ActivityPerVBinding>(), CustomAdapt {

    override fun isBaseOnWidth(): Boolean {
        return false
    }

    override fun getSizeInDp(): Float {
        return 640f
    }

    private var transY = 0
    private var isAnim = false
    private lateinit var viewModel: ChangePasswordViewModel
    private var topUrl = ""
    private var backUrl = ""
    private lateinit var bigIv: ImageView
    private lateinit var smallIv: ImageView
    private var refreshImage = false
    private var layoutParams: ConstraintLayout.LayoutParams? = null
    private lateinit var seekBar: AppCompatSeekBar
    private lateinit var maskIng: View
    private lateinit var verifySuccessIv: ImageView
    private lateinit var fileText: TextView
    private var dialog: AlertDialog? = null

    private var targetPhone: String = ""//准备要更换成的新手机号
    private var oldPhone: String = ""//旧手机号

    override fun openArouterReceive(): Boolean {
        return false
    }

    @Inject
    @field:Named(PersonConstUtil.PERSONAL_VERIFY_PRESENTER)
    lateinit var presenter: PersonalVerContract.PersonalVerPresenter
    private var step = 0   //0：身份验证 2:更换手机
    private val msg1 = 0x211
    private var time = 60
    private val handler1 = @SuppressLint("HandlerLeak")
    object : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            if (msg.what == msg1) {
                removeCallbacksAndMessages(null)
                showLog(time.toString())
                if (time > 0) {
                    time--
                    binding.perVTv1.isEnabled = false
                    binding.perVTv1.text = time.toString().plus("S后可重新发送")
                    sendEmptyMessageDelayed(msg1, 1000)
                } else {
                    binding.perVTv1.isEnabled = true
                    binding.perVTv1.text = "获取验证码"
                    time = 60
                }
            }
        }

    }

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        setListenerToRootView()
        transY = DeviceUtil.dip2px(this, 35f)
        DaggerPersonComponent.builder().build().inject(this)
        setPageTitle("身份验证")
        viewModel = getModel(ChangePasswordViewModel::class.java)
    }

    private fun setListenerToRootView() {
        val rootView = window.decorView.findViewById<View>(android.R.id.content)
        rootView.viewTreeObserver.addOnGlobalLayoutListener {
            val mKeyboardUp = isKeyboardShown(rootView)
            if (mKeyboardUp) {
                //键盘弹出
                binding.perLayout1.translationY = -(transY.toFloat())
                isAnim = true
            } else {
                //键盘收起
                if (isAnim) {
                    binding.perLayout1.translationY = 0f
                    isAnim = false
                }
            }
        }
    }


    private fun isKeyboardShown(rootView: View): Boolean {
        val softKeyboardHeight = 100
        val r = Rect()
        rootView.getWindowVisibleDisplayFrame(r)
        val dm = rootView.resources.displayMetrics
        val heightDiff = rootView.bottom - r.bottom
        Log.e("diff", heightDiff.toString())
        return heightDiff > softKeyboardHeight * dm.density
    }

    override fun initLogic() {
        super.initLogic()
        binding.perTvFinish.setOnClickListener(this)
        binding.perVTv1.setOnClickListener(this)
        binding.perEt1.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val string = s.toString()
                if (step == 1) {
                    if (string.length >= 11) {
                        if (!StringUtils.isPhoneNumber(string)) {
                            ToastUtil.show(mContext!!, "请输入正确的手机号")
                            return
                        }
                        if (string == UserHolder.getCurrentUser()?.mobile) {
                            ToastUtil.show(mContext!!, "已经关联到这个手机号了")
                            return
                        }
                    }
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })
//        getObserver()
    }

    //弃用了，使用Xutil中整理的图片验证码
    private fun getObserver() {
        //验证码获取成功
        viewModel.getVerifyImageSuccessObservable.observe(this, Observer {
            dismissDialog()
            if (refreshImage) {
                val pair = showImageVerifyIv(it, backUrl, bigIv, topUrl, smallIv)
                backUrl = pair.first
                topUrl = pair.second
                resetImageDistance(seekBar, smallIv, layoutParams)
            } else {
                showAndVerifyImage(it)
            }
        })
        viewModel.getVerifyImageErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })

        viewModel.verifyImageSuccessObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, "图片匹配成功")
            maskIng.visibility = View.VISIBLE
            verifySuccessIv.visibility = View.VISIBLE
            maskIng.postDelayed({
                dialog?.dismiss()
                toastShort("发送成功")
                handler1.sendEmptyMessage(msg1)
            }, 1500)
        })
        viewModel.verifyImageErrorObservable.observe(this, Observer {
            dismissDialog()
            if (it == "匹配失败") {
                fileText.visibility = View.VISIBLE
                fileText.postDelayed({
                    fileText.visibility = View.GONE
                }, 1000)
            }
            ToastUtil.show(mContext!!, it)
            resetImageDistance(seekBar, smallIv, layoutParams)
        })
    }


    //处理图片验证码=====================开始=====================存在问题（图片对不齐）===================================================
    private fun showImageVerifyIv(
        it: VerifyImageBean, backUrl: String, bigIv: ImageView,
        topUrl: String, smallIv: ImageView
    ): Pair<String, String> {
        var backUrl1 = backUrl
        var topUrl1 = topUrl
        if (StringUtils.isNotBlankAndEmpty(it.backUrl)) {
            backUrl1 = it.backUrl
            val options = RequestOptions
                .placeholderOf(com.joinutech.ddbeslibrary.R.drawable.image_placeholder_im)
                .error(com.joinutech.ddbeslibrary.R.drawable.image_placeholder_im)
                .centerCrop()
            ImageLoaderUtils.showImgWithOption(mContext!!, backUrl1, bigIv, options)
        }
        if (StringUtils.isNotBlankAndEmpty(it.topUrl)) {
            topUrl1 = it.topUrl
            ImageLoaderUtils.loadImage(this, smallIv, topUrl)
        }
        return Pair(backUrl1, topUrl1)
    }

    private fun resetImageDistance(
        seekBar: AppCompatSeekBar, smallIv: ImageView,
        layoutParams: ConstraintLayout.LayoutParams?
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            seekBar.setProgress(0, true)
        } else seekBar.progress = 0
        var layoutParams1 = layoutParams
        if (layoutParams1 == null)
            layoutParams1 = smallIv.layoutParams as ConstraintLayout.LayoutParams
        layoutParams1.leftMargin = 0
        smallIv.layoutParams = layoutParams1
    }

    private fun showAndVerifyImage(it: VerifyImageBean) {
        val view = View.inflate(mContext!!, com.joinutech.ddbeslibrary.R.layout.dialog_image_verify, null)
        dialog = BottomDialogUtil.showBottomDialog(
            mContext!!, view, Gravity.CENTER
        )
        dialog?.setCanceledOnTouchOutside(false)
        bigIv = view.findViewById(com.joinutech.ddbeslibrary.R.id.bigIv)
        smallIv = view.findViewById(com.joinutech.ddbeslibrary.R.id.smallIv)
        seekBar = view.findViewById(com.joinutech.ddbeslibrary.R.id.seekBar)
        fileText = view.findViewById(com.joinutech.ddbeslibrary.R.id.fileText)
        maskIng = view.findViewById(com.joinutech.ddbeslibrary.R.id.maskIng)
        verifySuccessIv = view.findViewById(com.joinutech.ddbeslibrary.R.id.verifySuccessIv)
        val refresh = view.findViewById<ImageView>(com.joinutech.ddbeslibrary.R.id.refresh)
        oldPhone = UserHolder.getCurrentUser()?.mobile!!
        refresh.setOnClickListener(object : OnNoDoubleClickListener {
            override fun onNoDoubleClick(v: View) {
                //点击刷新按钮，启动动画
                v.animate().rotationBy(360f).setDuration(500)
                    .setInterpolator(AccelerateDecelerateInterpolator())
                    .setListener(object : Animator.AnimatorListener {
                        override fun onAnimationStart(animation: Animator) {}
                        override fun onAnimationEnd(animation: Animator) {
                            viewModel.getVerifyImage(bindToLifecycle(), targetPhone)
                            refreshImage = true
                        }

                        override fun onAnimationCancel(animation: Animator) {}
                        override fun onAnimationRepeat(animation: Animator) {}
                    })
            }

        })
        val pair = showImageVerifyIv(it, backUrl, bigIv, topUrl, smallIv)
        backUrl = pair.first
        topUrl = pair.second

        seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                layoutParams = smallIv.layoutParams as ConstraintLayout.LayoutParams
                val width: Int = bigIv.width
                val smallWidth: Int = smallIv.width
                val differenceValue = progress * 0.01 * width
                val value: Int = if (differenceValue > width - smallWidth) {
                    width - smallWidth
                } else {
                    differenceValue.toInt()
                }
                layoutParams?.leftMargin = value
                smallIv.layoutParams = layoutParams
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {

            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val progress = seekBar!!.progress
                Log.e("uploadProgressValue", progress.toString())
                getLoadingDialog("获取图片验证码", false)
                val width: Int = bigIv.width
                val smallWidth: Int = smallIv.width
                val differenceValue = progress * 0.01 * width
                val value: Double = if (differenceValue > width - smallWidth) {
                    (width - smallWidth) * 1.00 / width
                } else {
                    differenceValue / width
                }
                val hashMap = hashMapOf<String, Any>()
                val newId = if (StringUtils.isNotBlankAndEmpty(backUrl)) {
                    val dotIndex = backUrl.lastIndexOf(".")
                    val lineIndex = backUrl.lastIndexOf("/")
                    backUrl.substring(lineIndex + 1, dotIndex)
                } else ""
                val oriId = if (StringUtils.isNotBlankAndEmpty(topUrl)) {
                    val dotIndex = topUrl.lastIndexOf(".")
                    val lineIndex = topUrl.lastIndexOf("/")
                    topUrl.substring(lineIndex + 1, dotIndex)
                } else ""
                hashMap["newId"] = newId
                hashMap["oriId"] = oriId
                hashMap["phone"] = targetPhone
                hashMap["type"] = 9 //imageVerType 9更换手机号
                hashMap["scale"] = value
                viewModel.verifyImageWithMsg(bindToLifecycle(), hashMap)
            }
        })
    }
    //处理图片验证码=====================结束==========================存在问题（图片对不齐）==============================================


    override val contentViewResId: Int
        get() = R.layout.activity_per_v

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityPerVBinding {
        return ActivityPerVBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean {
        return true
    }


    override fun onNoDoubleClick(v: View) {
        super.onNoDoubleClick(v)
        when (v.id) {
            R.id.per_tv_finish -> {
                when (step) {
                    0 -> {
                        if (binding.perEt1.text == null || binding.perEt1.editableText.toString().length < 6) {
                            ToastUtil.show(mContext!!, "请输入正确的登录密码")
                            return
                        }
                        getLoadingDialog("正在验证", false)
                        presenter.verifyPw(accessToken!!, binding.perEt1.editableText.toString(),
                            bindToLifecycle<Result<Any>>(),
                            onSuccess = {
                                dismissDialog()
                                binding.perTopImg.setImageResource(R.drawable.connect_verify)
                                binding.perEt1.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
                                binding.perLayout2.visibility = View.VISIBLE
                                binding.perLine.visibility = View.VISIBLE
                                binding.perTv1.text = "请输入您的新手机号"
                                binding.perEt1.text.clear()
                                binding.perEt1.hint = "请输入新手机号"
                                binding.perTvFinish.text = "完成更换"
                                setPageTitle("更换手机号")
                                step = 1
                            },
                            onError = {
                                dismissDialog()
                                ToastUtil.show(mContext!!, "登录密码错误")
                            })

                    }
                    1 -> {//完成更换手机
                        if (binding.perEt1.text == null || StringUtils.isEmpty(binding.perEt1.editableText.toString())) {
                            ToastUtil.show(mContext!!, "请输入新手机号")
                            return
                        }
                        if (binding.perEt2.text == null || binding.perEt2.editableText.toString().isEmpty()) {
                            ToastUtil.show(mContext!!, "请输入验证码")
                            return
                        }
                        val phone = binding.perEt1.editableText.toString()
                        val msgCode = binding.perEt2.editableText.toString()
                        getLoadingDialog("更换手机号...", false)
                        presenter.upMobile(userToken, msgCode, phone, bindToLifecycle(),
                            onSuccess = {
                                dismissDialog()
                                // 更新手机号成功后修改本地缓存信息
                                UserHolder.getCurrentUser()?.let {
                                    it.mobile = phone
                                    UserHolder.onLogin(it, true)// change mobile
                                }
                                toast("手机号已更换完成，请重新登录")

                                LoginService.loginOut(userToken)
                                    .compose(bindToLifecycle())
                                    .compose(ErrorTransformerOnlyBean.getInstance())
                                    .subscribe(object : BaseSubscriber<Any>() {
                                        override fun onError(ex: ApiException) {
                                            onUserLogout()
                                            finish()
                                        }

                                        override fun onComplete() {}

                                        override fun onNext(t: Any?) {
                                            onUserLogout()
                                            finish()
                                        }
                                    })
                            },
                            onError = {
                                dismissDialog()
                                toast("验证码不正确")
                            })
                    }
                }
            }
            R.id.per_v_tv1 -> {//点击获取验证码
                if (binding.perEt1.text == null || StringUtils.isEmpty(binding.perEt1.editableText.toString())) {
                    ToastUtil.show(mContext!!, "请输入新手机号")
                    return
                }
                //应该给新手机号发验证
                targetPhone = binding.perEt1.editableText.toString()
                if (!StringUtils.isPhoneNumber(binding.perEt1.editableText.toString())) {
                    ToastUtil.show(mContext!!, "请输入正确的手机号")
                    return
                }
                if (targetPhone == UserHolder.getCurrentUser()?.mobile) {
                    ToastUtil.show(mContext!!, "已经关联到这个手机号了")
                    return
                }
//                getLoadingDialog("正在发送", false)
//                viewModel.getVerifyImage(bindToLifecycle(), targetPhone)
                XUtil.getImageCode(this, targetPhone, type = 9,
                    isNeedMsg = true,
                    onSuccess = {
                        toastShort("发送成功")
                        handler1.sendEmptyMessage(msg1)
                    },
                    onError = {

                    })
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        handler1.removeCallbacksAndMessages(null)
        if (dialog != null) {
            dialog?.dismiss()
            dialog = null
        }
    }
}