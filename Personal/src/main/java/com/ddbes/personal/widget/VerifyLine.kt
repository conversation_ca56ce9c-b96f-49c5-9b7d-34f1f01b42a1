package com.ddbes.personal.widget

import android.animation.Animator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View

/**
 * 身份验证线条绘制动画
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/6/15.
 */
class VerifyLine:View{
    private lateinit var mpaint:Paint
    private var isAnim=false
    private var dx=0f
    private lateinit var mLineListener: LineListener
    constructor(context: Context?) : super(context)
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs){
        initPaint()
    }
    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    fun setListener(lineListener: LineListener){
        mLineListener=lineListener
    }

    fun initPaint(){
        mpaint= Paint()
        mpaint.isAntiAlias=true
        mpaint.strokeWidth=10f
        mpaint.color=Color.parseColor("#E2E2E2")
    }

    override fun onDraw(canvas: Canvas) {
        if(isAnim){
            mpaint.color= Color.parseColor("#2479ed")
            canvas?.drawLine(0f,0f,dx,0f,mpaint)
        }else{
            canvas?.drawLine(0f,0f,width.toFloat(),0f,mpaint)
        }
    }

    fun startAnim(){
        isAnim=true
        var anim=ValueAnimator.ofFloat(0f,width.toFloat())
        anim.duration=300
        anim.start()
        anim.addUpdateListener {animation ->
                dx = animation.animatedValue as Float
                postInvalidate()
        }
        anim.addListener(object :Animator.AnimatorListener {
            override fun onAnimationRepeat(animation: Animator) {

            }

            override fun onAnimationEnd(animation: Animator) {
               mLineListener.endAnim()
            }

            override fun onAnimationCancel(animation: Animator) {

            }

            override fun onAnimationStart(animation: Animator) {

            }
        })
    }

    interface LineListener{
        fun endAnim()
    }

}