package com.ddbes.personal.widget

import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup

/**
 * 身份验证include
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/6/15.
 */
class VerifyStepLayout : ViewGroup {
    constructor(context: Context?) : super(context)
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        for (i in 0 until childCount) {
            measureChild(getChildAt(i), widthMeasureSpec, heightMeasureSpec)
        }
        var measure_height = getChildAt(0).measuredHeight + getChildAt(1).measuredHeight + 10
        setMeasuredDimension(measuredWidth, measure_height)


    }


    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        //图片摆放0-2
        val img_w=getChildAt(0).measuredWidth
        val img_h=getChildAt(0).measuredHeight
        val c0_L = getChildAt(5).measuredWidth / 2 -img_w/2
        getChildAt(0).layout(c0_L,0,c0_L+img_w,img_h)

        val c1_L=measuredWidth/2-img_w/2
        getChildAt(1).layout(c1_L,0,c1_L+img_w,img_h)

        val c2_L=measuredWidth-getChildAt(7).measuredWidth/2-img_w/2
        getChildAt(2).layout(c2_L,0,c2_L+img_w,img_h)
        //线条摆放3-4
        val c3_L=c0_L+img_w
        getChildAt(3).layout(c3_L,img_h/2,c1_L,img_h/2+getChildAt(3).measuredHeight)

        val c4_L=c1_L+img_w
        getChildAt(4).layout(c4_L,img_h/2,c2_L,img_h/2+getChildAt(3).measuredHeight)
        //文字摆放5-7
        getChildAt(5).layout(0,img_h+10,getChildAt(5).measuredWidth,measuredHeight)

        val c6_L=measuredWidth/2-getChildAt(6).measuredWidth/2
        getChildAt(6).layout(c6_L,img_h+10,c6_L+getChildAt(6).measuredWidth,measuredHeight)

        val c7_L=measuredWidth-getChildAt(7).measuredWidth
        getChildAt(7).layout(c7_L,img_h+10,measuredWidth,measuredHeight)
    }
}