package com.ddbes.personal.presenter

import android.content.Context
import com.joinutech.ddbeslibrary.request.Result
import com.ddbes.personal.contract.PersonalVerContract
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/7/7.
 */
class PersonalVerPresenterIp  @Inject internal constructor():PersonalVerContract.PersonalVerPresenter{

    @Inject
    @field:Named(PersonConstUtil.PERSONAL_VERIFY_MODULER)
    lateinit var module:PersonalVerContract.PersonalVerModule

    init {
        DaggerPersonComponent.builder().build().inject(this)
    }
    //身份验证
    override fun verifyPw(token:String,pw:String, life: LifecycleTransformer<Result<Any>>,
                          onSuccess: (Any) -> Unit, onError: (String) -> Unit) {//验证密码
        module.verifyPw(token,pw,life,onSuccess,onError)
    }



    //更换手机
    override fun upMobile(token:String,code: String,mobile: String,
                          life: LifecycleTransformer<Result<Any>>,
                          onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
            module.upMobile(token,code,mobile,life,onSuccess,onError)
    }

}