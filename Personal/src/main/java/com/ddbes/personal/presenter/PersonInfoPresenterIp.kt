package com.ddbes.personal.presenter

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.view.Gravity
import android.view.View
import android.widget.DatePicker
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.personal.R
import com.ddbes.personal.contract.PersonInfoContract
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.ddbes.personal.view.MainWorkStationActivity
import com.ddbes.personal.view.PersonAddressActivity
import com.ddbes.personal.view.PersonInfoActivity
import com.ddbes.personal.view.PersonNameActivity
import com.joinutech.ddbeslibrary.bean.PersonInfoBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.utils.*
import com.trello.rxlifecycle3.LifecycleTransformer
import com.trello.rxlifecycle3.components.support.RxAppCompatActivity
import java.util.*
import javax.inject.Inject
import javax.inject.Named

/**
 * Description 个人资料 Presenter
 * Author HJR36
 * Date 2018/6/23 13:38
 */
class PersonInfoPresenterIp @Inject internal constructor() : PersonInfoContract.PersonInfoPresenter {

    lateinit var mContext: Context
    private var activity: PersonInfoActivity? = null

    @Inject
    @field:Named(PersonConstUtil.PERSON_PERSONINFO_MOUDLE)
    lateinit var module: PersonInfoContract.PersonInfoModule

    //获取当前系统的年月日
    private var calendar = Calendar.getInstance()
    private var mYear = calendar.get(Calendar.YEAR)
    private var mMonth = calendar.get(Calendar.MONTH)
    private var mDay = calendar.get(Calendar.DAY_OF_MONTH)

    init {
        DaggerPersonComponent.builder().build().inject(this)
    }

    override fun setContext(mContext: Context?) {
        this.mContext = mContext!!
    }

    override fun setView(view: PersonInfoContract.PersonInfoView) {
        activity = view as PersonInfoActivity
    }

    override fun getData(life: LifecycleTransformer<Result<PersonInfoBean>>) {
        module.getData(life, {
            activity!!.onDataSuccess(it)
        }, {
            activity!!.onDataError(it)
        })
    }


    override fun dealAddress(addresS_REQUEST_CODE: Int) {
        val intent = Intent(activity!!, PersonAddressActivity::class.java)
        activity!!.startActivityForResult(intent, addresS_REQUEST_CODE)
    }

    override fun dealWork(industrY_REQUEST_CODE: Int, profession: String) {
        ARouter.getInstance().build(RouteOrg.industryActivity)
                .withString("type", "person")
                .withString("profession", profession)
                .navigation(activity!!, industrY_REQUEST_CODE)
    }

    override fun dealEmail(persoN_NAME: Int, personEmail: String) {
        val intent = Intent(activity!!, PersonNameActivity::class.java)
        intent.putExtra("type", "email")
        intent.putExtra("name", personEmail)
        activity!!.startActivityForResult(intent, persoN_NAME)
    }

    override fun dealSex(lifeActivity: RxAppCompatActivity, personSexIcon: ImageView, sexName: String,
                         result: (info: String) -> Unit) {
        val view = View.inflate(lifeActivity, R.layout.dialog_personsex_bottom_layout, null)
        val dialog = MyDialog(lifeActivity, 279, 255, ""
                , false, false, 0)
        dialog.setView(view, 0)
        dialog.show()
        val mGirlIcon = view.findViewById<ImageView>(R.id.iv_girl_icon)
        val mManIcon = view.findViewById<ImageView>(R.id.iv_man_icon)
        val mManText = view.findViewById<TextView>(R.id.tv_man)
        val mGirlText = view.findViewById<TextView>(R.id.tv_girl)
        //gender (integer, optional): 性别 0.未设置 1.男 2.女 ,
        if (StringUtils.isNotBlankAndEmpty(sexName)) {
            if (sexName == "男") {
                boySelected(mManIcon, mGirlIcon, mGirlText, lifeActivity, mManText)
            } else {
                girlSelected(mManIcon, mManText, lifeActivity, mGirlIcon, mGirlText)
            }
        }
        view.findViewById<TextView>(R.id.cancel).setOnClickListener {
            dialog.dismiss()
        }
        mGirlIcon.setOnClickListener {
            dealGirlSexIsSelected(lifeActivity, mGirlIcon, mGirlText, mManIcon, mManText)
        }
        mGirlText.setOnClickListener {
            dealGirlSexIsSelected(lifeActivity, mGirlIcon, mGirlText, mManIcon, mManText)
        }
        mManText.setOnClickListener {
            dealManSexIsSelected(lifeActivity, mGirlIcon, mGirlText, mManIcon, mManText)
        }
        mManIcon.setOnClickListener {
            dealManSexIsSelected(lifeActivity, mGirlIcon, mGirlText, mManIcon, mManText)
        }
        var sex = ""
        view.findViewById<TextView>(R.id.confirm).setOnClickListener {
            if (mGirlIcon.isSelected) {
                sex = "女"
            } else if (mManIcon.isSelected) {
                sex = "男"
            }
            if (sex == "男") {
                personSexIcon.setImageResource(R.drawable.icon_personinfo_man_small)
            } else {
                personSexIcon.setImageResource(R.drawable.icon_personinfo_girl_small)
            }
            if (sexName != sex) {
                result(sex)
                //只有有变化才去提交
                commitPersonInfo(lifeActivity, sex, "gender")
            }
            dialog.dismiss()
        }
    }

    private fun dealManSexIsSelected(mContext: Context?, mGirlIcon: ImageView,
                                     mGirlText: TextView, mManIcon: ImageView, mManText: TextView) {
        if (mManIcon.isSelected) {
            girlSelected(mManIcon, mManText, mContext, mGirlIcon, mGirlText)
        } else {
            boySelected(mManIcon, mGirlIcon, mGirlText, mContext, mManText)
        }
    }

    private fun boySelected(mManIcon: ImageView, mGirlIcon: ImageView,
                            mGirlText: TextView, mContext: Context?, mManText: TextView) {
        mManIcon.isSelected = true
        mGirlIcon.isSelected = false
        mGirlText.setTextColor(ContextCompat.getColor(mContext!!, R.color.sexTextColor))
        mManText.setTextColor(ContextCompat.getColor(mContext, R.color.sexSelectedManTextColor))
    }

    private fun girlSelected(mManIcon: ImageView, mManText: TextView,
                             mContext: Context?, mGirlIcon: ImageView, mGirlText: TextView) {
        mManIcon.isSelected = false
        mManText.setTextColor(ContextCompat.getColor(mContext!!, R.color.sexTextColor))
        mGirlIcon.isSelected = true
        mGirlText.setTextColor(ContextCompat.getColor(mContext, R.color.sexSelectedTextColor))
    }

    private fun dealGirlSexIsSelected(mContext: Context?, mGirlIcon: ImageView,
                                      mGirlText: TextView, mManIcon: ImageView, mManText: TextView) {
        if (mGirlIcon.isSelected) {
            boySelected(mManIcon, mGirlIcon, mGirlText, mContext, mManText)
        } else {
            girlSelected(mManIcon, mManText, mContext, mGirlIcon, mGirlText)
        }
    }

    override fun dealDate(lifeActivity: RxAppCompatActivity, personDateText: TextView) {
        val view = View.inflate(lifeActivity, R.layout.dialog_persondate_bottom_layout, null)
        val dialog = BottomDialogUtil.showBottomDialog(lifeActivity, view, Gravity.BOTTOM)
        val mCancel = view.findViewById<TextView>(R.id.cancel)
        val mConfirm = view.findViewById<TextView>(R.id.confirm)
        val mDatePicker = view.findViewById<DatePicker>(R.id.datePicker)
        val oldDate = personDateText.text.toString()
        var dateStr = personDateText.text.toString()
        mDatePicker.init(mYear, mMonth, mDay) { _, year, monthOfYear, dayOfMonth ->
            mYear = year
            mMonth = monthOfYear
            mDay = dayOfMonth
        }
        mCancel.setOnClickListener {
            dialog.dismiss()
        }
        mConfirm.setOnClickListener {
            var mMonthNew: String = (mMonth + 1).toString()
            if (mMonth + 1 < 10) {
                mMonthNew = "0$mMonthNew"
            }
            dateStr = "$mYear-$mMonthNew-$mDay"
            val currentYear = calendar.get(Calendar.YEAR)
            val currentMonth = calendar.get(Calendar.MONTH)
            val currentDay = calendar.get(Calendar.DAY_OF_MONTH)
            if ((mYear > currentYear) || (mYear == currentYear && mMonth > currentMonth) || (
                            mYear == currentYear && mMonth == currentMonth && mDay > currentDay
                            )) {
                ToastUtil.show(lifeActivity, "您选择的日期不符合要求，请重新选择")
            } else {
                dialog.dismiss()
                personDateText.text = dateStr
                if (oldDate != dateStr) {
                    commitPersonInfo(lifeActivity, dateStr, "birthday")
                }
            }
        }
    }

    override fun dealIcon(mContext: Context?) {
        val view = View.inflate(mContext, com.joinutech.ddbeslibrary.R.layout.dialog_personicon_bottom_layout, null)
        val dialog = BottomDialogUtil.showBottomDialog(mContext!!, view, Gravity.BOTTOM)
        val mTakePicture = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.takePicture)
        val mSelectPicture = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.selectPicture)
        val mDismiss = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.dismiss)
        mTakePicture.setOnClickListener {
            requestPhonePermission(ConsKeys.TAKE_PHOTO)
            dialog.dismiss()
        }
        mSelectPicture.setOnClickListener {
            requestPhonePermission()
            dialog.dismiss()
        }
        mDismiss.setOnClickListener {
            dialog.dismiss()
        }
    }

    override fun dealWorkStation(workStation_REQUEST_CODE: Int) {
        val intent = Intent(activity!!, MainWorkStationActivity::class.java)
        activity!!.startActivityForResult(intent, workStation_REQUEST_CODE)
    }

    @SuppressLint("CheckResult")
    private fun requestPhonePermission(type: String = "") {
        PictureNewHelper.beforeSelectPhoto(activity!!, type, true, maxSelectNum = 1)

//        val perms = arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE,
//                Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.CAMERA)
//        PermissionUtils.requestPermissionActivity(activity!!, perms, "照相机权限", {
//            doThings()
//        }, {
//            ToastUtil.show(mContext, "拍照/选取图片需要您授权读写及照相机权限")
//        })
    }

//    private fun doThings() {
//        if (type == ConsKeys.TAKE_PHOTO) {
//            takePhoto()
//        } else {
//            selectPhoto()
//        }
//    }
//
//    private fun selectPhoto() {
////        PhotoCuttingUtil.selectPhotoZoom2(activity!!, IMAGE_CUT_CODE)
//        PictureNewHelper.selectPhoto(activity!!, IMAGE_CUT_CODE, true, 1)
//    }
//
//    private fun takePhoto() {
////        PhotoCuttingUtil.takePhotoZoom2(activity!!, IMAGE_CUT_CODE)
//        PictureNewHelper.takePhoto(activity!!, IMAGE_CUT_CODE)
//    }

//    override fun dealPermission() {
//        if (type == ConsKeys.TAKE_PHOTO) {
//            takePhoto()
//        } else {
//            selectPhoto()
//        }
//    }

    override fun dealName(persoN_NAME: Int, personName: String) {
        val intent = Intent(activity!!, PersonNameActivity::class.java)
        intent.putExtra("type", "name")
        intent.putExtra("name", personName)
        activity?.startActivityForResult(intent, persoN_NAME)
    }

    override fun destory() {
        if (activity != null) {
            activity = null
        }
    }

    override fun uploadAvatar(life: LifecycleTransformer<Result<Any>>, picPath: String, token: String) {
        FileUploadUtil.uploadFileRequest(targetId = "avatar", filePath = picPath,
                onSuccess = {
                    if (StringUtils.isNotBlankAndEmpty(it)) {
                        activity?.onUploadPicSuccess(it)
                    }
                },
                onError = {
                    activity?.onUploadPicError("上传图片失败")
                },
                type = TosFileType.HEAD
        )
    }

    override fun commitPersonInfo(lifeActivity: RxAppCompatActivity,
                                  name: String, type: String) {
        module.commitPersonInfo(lifeActivity, name, type,
                onSuccess = { type, info ->
                    activity?.onSuccess(type, info)
                },
                onError = {
                    activity?.onError(it)
                })
    }

}