package com.ddbes.personal.presenter

import com.ddbes.personal.contract.PersonSettingContract
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.joinutech.ddbeslibrary.bean.PersonSettingBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

class PersonSettingPresenterIp @Inject internal constructor():PersonSettingContract.PersonSettingPresenter {
    @Inject
    @field:Named(PersonConstUtil.PERSONSETTING_MODULE)
    lateinit var module:PersonSettingContract.PersonSettingModule

    init {
        DaggerPersonComponent.builder().build().inject(this)
    }
    override fun watchSettingData(life: LifecycleTransformer<Result<PersonSettingBean>>,
                                  token: String,
                                  onSuccess: (PersonSettingBean) -> Unit, onError: (String) -> Unit) {
        module.watchSettingData(life, token, onSuccess, onError)
    }

    override fun updateSettingData(life: LifecycleTransformer<Result<Any>>,
                                   token: String, data:Map<String,Any>,
                                   onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.updateSettingData(life, token, data, onSuccess, onError)
    }
}