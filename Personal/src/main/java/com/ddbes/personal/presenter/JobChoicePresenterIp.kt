package com.ddbes.personal.presenter

import android.content.Context
import com.joinutech.ddbeslibrary.bean.JobChoiceBean
import com.joinutech.ddbeslibrary.request.Result
import com.ddbes.personal.contract.JobChoiceContract
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.ddbes.personal.view.JobChoiceActivity
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

/**
 * Description 职业选择Presenter
 * Author HJR36
 * Date 2018/6/21 10:05
 */
class JobChoicePresenterIp @Inject internal constructor() :JobChoiceContract.JobChoicePresenter {

    private var activity:JobChoiceActivity ? =null
    @Inject
    @field:Named(PersonConstUtil.PERSON_JOBCHOICE_MOUDLE)
    lateinit var moduel:JobChoiceContract.JobChoiceModule

    init {
        DaggerPersonComponent.builder().build().inject(this)
    }

    override fun setView(view: JobChoiceContract.JobChoiceView) {
        this.activity = view as JobChoiceActivity
    }

    override fun getData(life: LifecycleTransformer<Result<List<JobChoiceBean>>>,
                         context: Context?, accessToken: String?) {
        moduel.getData(life,context!!,accessToken,{
            activity!!.onSuccess(it)
        },{
            activity!!.onError(it)
        })
    }
    override fun destory() {
        if (activity!=null){
            activity = null
        }
    }

}