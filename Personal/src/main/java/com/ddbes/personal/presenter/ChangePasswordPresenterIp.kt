package com.ddbes.personal.presenter

import com.ddbes.personal.contract.ChangePasswordContract
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

class ChangePasswordPresenterIp :ChangePasswordContract.ChangePasswordPresenter {

    @Inject
    @field:Named(PersonConstUtil.CHANGE_PASSWORD_MODULER)
    lateinit var module:ChangePasswordContract.ChangePasswordModule

    init {
        DaggerPersonComponent.builder().build().inject(this)
    }

    override fun upPwd(oldPassword: String,
                       password: String,
                       life: LifecycleTransformer<Result<Any>>, token:String,
                       onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.upPwd(oldPassword,password,life,token,onSuccess,onError)
    }

}