package com.ddbes.personal.presenter

import com.ddbes.personal.contract.WorkStationContract
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.joinutech.ddbeslibrary.bean.CompanyListBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

class WorkStationPresenterIp @Inject internal constructor() : WorkStationContract.WorkStationPresenter {
    @Inject
    @field:Named(PersonConstUtil.WORKSTATION_MODULE)
    lateinit var module: WorkStationContract.WorkStationMoudle

    init {
        DaggerPersonComponent.builder().build().inject(this)
    }

    override fun getWorkStationList(life: LifecycleTransformer<Result<CompanyListBean>>,
                                    onSuccess: (CompanyListBean) -> Unit, onError: (String) -> Unit) {
        module.getWorkStationList(life, onSuccess, onError)
    }
}