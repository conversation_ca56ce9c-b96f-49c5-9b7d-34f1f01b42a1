package com.ddbes.personal.presenter

import com.ddbes.personal.contract.PersonCommitContract
import com.ddbes.personal.inject.DaggerPersonComponent
import com.ddbes.personal.util.PersonConstUtil
import com.joinutech.ddbeslibrary.bean.PersonInfoBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import com.trello.rxlifecycle3.components.support.RxAppCompatActivity
import javax.inject.Inject
import javax.inject.Named

class PersonCommitPresenterIp : PersonCommitContract.PersonCommitPresenter {

    @Inject
    @field:Named(PersonConstUtil.PERSON_COMMIT_MODULER)
    lateinit var module: PersonCommitContract.PersonCommitModule

    init {
        DaggerPersonComponent.builder().build().inject(this)
    }

    override fun commitPersonInfo(activity: RxAppCompatActivity,
                                  name: String, type: String,
                                  onSuccess: (PersonInfoBean) -> Unit, onError: (String) -> Unit) {
        module.commitPersonInfo(activity, name, type, onSuccess, onError)
    }

    override fun commitCooperationOwnEmail(life: LifecycleTransformer<Result<Any>>,
                                           email: String, companyId: String, token: String,
                                           onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.commitCooperationOwnEmail(life, email, companyId, token, onSuccess, onError)
    }

}