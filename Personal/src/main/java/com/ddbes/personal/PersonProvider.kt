package com.ddbes.personal

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.library.im.imtcp.Logger
import com.ddbes.personal.view.AboutAppActivity
import com.ddbes.personal.view.PersonAddressActivity
import com.ddbes.personal.view.PersonalSettingActivity
import com.joinutech.common.base.isDebug
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.bean.QrCodeData
import com.joinutech.ddbeslibrary.utils.RoutePersonal
import com.joinutech.ddbeslibrary.utils.ToastUtil

/**
 * @PackageName: com.ddbes.personal
 * @ClassName:
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/7/24 9:04
 * @Desc: 用户模块对外提供服务接口实现
 */
@Route(path = "/Personal/provider")
class PersonServiceImpl : PersonService {

    private var mContext: Context? = null
    override fun init(context: Context?) {
        this.mContext = context
    }

    override fun openPage(path: String, params: Bundle) {}

    override fun openPageWithResult(activity: FragmentActivity, path: String, params: Bundle, result: (data: String) -> Unit) {
        when (path) {
            "city_selector"->{
                val cityLevel = params.getString("cityLevel", "1")//选择到市即可
                val pageTitle = params.getString("pageTitle", "")
                val intent = Intent(activity, PersonAddressActivity::class.java)
                intent.putExtra("cityLevel",cityLevel)
                intent.putExtra("pageTitle",pageTitle)
                Logger.i("选择城市","====跳转到城市====")
                activity.startActivity(intent)
            }
            "user_detail" -> {
                ARouter.getInstance().build(RoutePersonal.personalInfo).navigation()
            }
            "user_qr" -> {
                val avatar = params.getString("avatar", "")
                val name = params.getString("name", "")
                val userId = params.getString("userId", "")
                if (!userId.isNullOrBlank()) {
                    val member = QrCodeData(headImg = avatar, name = name, userId = userId!!, content = "r.$userId")
                    ARouter.getInstance().build(RoutePersonal.orgQrCode)
                            .withSerializable("qrCodeData", member)
                            .navigation()
                } else {
                    if (isDebug) {
                        ToastUtil.show(mContext
                                ?: BaseApplication.joinuTechContext, "userId 为空")
                    }
                }
            }
            "user_setting" -> {
                val intent = Intent(activity, PersonalSettingActivity::class.java)
                activity.startActivity(intent)
            }
            "app_about" -> {
                val intent = Intent(activity, AboutAppActivity::class.java)
                activity.startActivity(intent)
            }
        }
    }

    override fun openPageWithResult1(
        activity: FragmentActivity,
        path: String,
        params: Bundle,
        result: (data: String) -> Unit
    ) {

    }

    override fun service(path: String, params: Bundle, result: (data: String) -> Unit) {
    }

}

interface PersonService : RouteServiceProvider