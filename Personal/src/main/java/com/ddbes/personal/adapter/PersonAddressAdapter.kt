package com.ddbes.personal.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ddbes.personal.R
import com.ddbes.personal.bean.PersonAddressBean


/**
 * Description 地址Adapter
 * Author HJR36
 * Date 2018/6/21 17:01
 */
class PersonAddressAdapter(list: List<PersonAddressBean>?, context: Context, rv: RecyclerView,
                           clickListener: MyItemClickListener) :
        RecyclerView.Adapter<PersonAddressAdapter.Holder>() {

    private val mContext = context

    private var dataList = list ?: arrayListOf()

    private var listener: MyItemClickListener = clickListener

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
        val holder = Holder(LayoutInflater.from(mContext).inflate(R.layout.item_personaddress_layout, parent, false))
        holder.itemView.tag = holder
        return holder
    }

    override fun getItemCount(): Int {
        return if (dataList == null) 0 else dataList!!.size
    }

    override fun onBindViewHolder(holder: Holder, position: Int) {
        val mAddressText = holder.itemView.findViewById<TextView>(R.id.tv_address_name)

        holder.mItem_confirm_iv?.visibility = if (dataList!![position].isSelected) View.VISIBLE else View.INVISIBLE
        mAddressText.text = dataList!![position].address

        holder.itemView.setOnClickListener {
            dataList.forEach {
                it.isSelected = false
            }

            dataList[position].isSelected = true

            notifyDataSetChanged()

            listener.setOnItemClick(dataList.get(position))
        }
    }

    class Holder(view: View) : RecyclerView.ViewHolder(view) {
        var mItem_confirm_iv: ImageView? = null

        init {
            mItem_confirm_iv = view.findViewById(R.id.item_confirm_iv)
        }
    }

    fun setData(list: List<PersonAddressBean>) {
        dataList = list
        notifyDataSetChanged()
    }


    interface MyItemClickListener {
        fun setOnItemClick(personAddressBean: PersonAddressBean)
    }

}