package com.ddbes.personal.adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;
import com.ddbes.personal.R;
import com.joinutech.ddbeslibrary.bean.JobChoiceBean;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;

/**
 * Created by Administrator on 2016/10/25 0025.
 */
public class MoreAdapter extends BaseAdapter{
    private Context context;
    private int position = 0;
    Holder hold;
    private List<JobChoiceBean.IndustrysBean> lists;

    public MoreAdapter(Context context, List<JobChoiceBean.IndustrysBean> lists) {
        this.context = context;
        this.lists = lists;
    }

    public int getCount() {
        return lists.size();
    }

    public Object getItem(int position) {
        return lists.get(position);
    }

    public long getItemId(int position) {
        return position;
    }

    public View getView(int position, View view, ViewGroup viewGroup) {

        if (view == null) {
            view = View.inflate(context, R.layout.item_morelist, null);
            hold = new Holder(view);
            view.setTag(hold);
        } else {
            hold = (Holder) view.getTag();
        }
        hold.txt.setText(lists.get(position).getName());
        hold.txt.setTextColor(0xFF666666);
        if (position == this.position) {
            hold.txt.setTextColor(0xFFFF8C00);
        }
        return view;
    }

    public void setSelectItem(int position) {
        this.position = position;
    }

    public int getSelectItem() {
        return position;
    }

    private static class Holder {
        TextView txt;
        public Holder(View view) {
            txt = (TextView) view.findViewById(R.id.moreitem_txt);
        }

        private ArrayList<Long> ids = new ArrayList<>();
        public void  study(){

        }
    }
}
