package com.ddbes.personal.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.ddbes.personal.R
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.common.adapter.BaseAdapter
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.CircleImageView

class WorkStationAdapter(var context: Context, dataList: List<WorkStationBean>) :
        BaseAdapter<WorkStationBean>(dataList, context) {

    override fun setDataSourceList(list: List<WorkStationBean>?) {
        mList = list!!
        notifyDataSetChanged()
    }

    override fun addMoreData(t: WorkStationBean) {
        (mList as ArrayList).add(t)
        notifyDataSetChanged()
    }

    override fun creatView(parent: ViewGroup): View? {
        return LayoutInflater.from(context).inflate(R.layout.item_workstation_layout, parent, false)
//        return View.inflate(context, R.layout.item_workstation_layout, null)
    }

    override fun updateView(holder: BaseViewHolder, position: Int) {
        val imageView = holder.itemView.findViewById<CircleImageView>(R.id.heading)
        val name = holder.itemView.findViewById<TextView>(R.id.name)
        ImageLoaderUtils.loadImage(mContext, imageView, mList!![position].logo)
        name.text = mList!![position].name
        val selected = holder.itemView.findViewById<TextView>(R.id.selected)
        if (mList!![position].deptId == "1") {
            selected.isSelected = true
            selected.text = "主要团队"
        } else {
            selected.isSelected = false
            selected.text = "设为主要团队"
        }

        if (position == mList?.lastIndex) {
            holder.itemView.findViewById<View>(R.id.tv_tip).visibility = View.VISIBLE
        }else{
            holder.itemView.findViewById<View>(R.id.tv_tip).visibility = View.GONE
        }
    }
}