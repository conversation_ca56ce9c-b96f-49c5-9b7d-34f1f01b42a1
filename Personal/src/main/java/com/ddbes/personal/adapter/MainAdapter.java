package com.ddbes.personal.adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ddbes.personal.R;
import com.joinutech.ddbeslibrary.bean.JobChoiceBean;

import java.util.List;

/**
 * Created by Administrator on 2016/10/25 0025.
 */
public class MainAdapter extends BaseAdapter {
    private Context context;
    private List<JobChoiceBean> list;
    private int position = 0;
    private Holder hold;

    public MainAdapter(Context context, List<JobChoiceBean> list) {
        this.context = context;
        this.list = list;
    }

    public int getCount() {
        return list.size();
    }

    public Object getItem(int position) {
        return list.get(position);
    }

    public long getItemId(int position) {
        return position;
    }

    public View getView(int position, View view, ViewGroup viewGroup) {

        if (view == null) {
            view = View.inflate(context, R.layout.item_mainlist, null);
            hold = new Holder(view);
            view.setTag(hold);
        } else {
            hold = (Holder) view.getTag();
        }
        hold.txt.setText(list.get(position).getName());
        hold.layout.setBackgroundColor(0xFFEBEBEB);
        if (position == this.position) {
            hold.layout.setBackgroundColor(0xFFFFFFFF);
        }
        return view;
    }

    public void setSelectItem(int position) {
        this.position = position;
    }

    public int getSelectItem() {
        return position;
    }

    private static class Holder {
        LinearLayout layout;
        TextView txt;

        public Holder(View view) {
            txt = (TextView) view.findViewById(R.id.mainitem_txt);
            layout = (LinearLayout) view.findViewById(R.id.mainitem_layout);
        }
    }
}
