package com.ddbes.personal.util

/**
 * Description 个人信息模块 全局dagger注解名称
 * Author HJR36
 * Date 2018/6/23 13:45
 */
object PersonConstUtil {
    //personInfo
    const val PERSON_PERSONINFO_MOUDLE = "personModule"
    const val PERSON_PERSONINFO_PRESENTER = "personPresenter"
    //JobChoice
    const val PERSON_JOBCHOICE_MOUDLE = "jobChoiceMoudle"
    const val PERSON_JOBCHOICE_PRESENTER = "jobChoicePresenter"
    //PersonalVerify
    const val PERSONAL_VERIFY_MODULER="personal_verify_module"
    const val PERSONAL_VERIFY_PRESENTER="personal_verify_presenter"
    //ChangePassword
    const val CHANGE_PASSWORD_MODULER="change_password_module"
    const val CHANGE_PASSWORD_PRESENTER="change_password_presenter"
    //CommitData
    const val PERSON_COMMIT_MODULER="person_commit_module"
    const val PERSON_COMMIT_PRESENTER="person_commit_presenter"
    //workstation
    const val WORKSTATION_PRESENTER="workstation_presenter"
    const val WORKSTATION_MODULE="workstation_module"
    //personSetting
    const val PERSONSETTING_PRESENTER="personsetting_presenter"
    const val PERSONSETTING_MODULE="personsetting_module"
    //修改团队网站等
    const val MODIFY_ORG_INFO_MODULE = "modify_org_info_module"
    //修改密码图片验证
    const val CHANGE_PASSWORD_REPOSITORY = "change_password_repository"
}