package com.ddbes.personal.inject

import com.ddbes.personal.contract.*
import com.ddbes.personal.module.*
import com.ddbes.personal.presenter.*
import com.ddbes.personal.util.PersonConstUtil.CHANGE_PASSWORD_MODULER
import com.ddbes.personal.util.PersonConstUtil.CHANGE_PASSWORD_PRESENTER
import com.ddbes.personal.util.PersonConstUtil.CHANGE_PASSWORD_REPOSITORY
import com.ddbes.personal.util.PersonConstUtil.MODIFY_ORG_INFO_MODULE
import com.ddbes.personal.util.PersonConstUtil.PERSONAL_VERIFY_MODULER
import com.ddbes.personal.util.PersonConstUtil.PERSONAL_VERIFY_PRESENTER
import com.ddbes.personal.util.PersonConstUtil.PERSONSETTING_MODULE
import com.ddbes.personal.util.PersonConstUtil.PERSONSETTING_PRESENTER
import com.ddbes.personal.util.PersonConstUtil.PERSON_COMMIT_MODULER
import com.ddbes.personal.util.PersonConstUtil.PERSON_COMMIT_PRESENTER
import com.ddbes.personal.util.PersonConstUtil.PERSON_JOBCHOICE_MOUDLE
import com.ddbes.personal.util.PersonConstUtil.PERSON_JOBCHOICE_PRESENTER
import com.ddbes.personal.util.PersonConstUtil.PERSON_PERSONINFO_MOUDLE
import com.ddbes.personal.util.PersonConstUtil.PERSON_PERSONINFO_PRESENTER
import com.ddbes.personal.util.PersonConstUtil.WORKSTATION_MODULE
import com.ddbes.personal.util.PersonConstUtil.WORKSTATION_PRESENTER
import dagger.Module
import dagger.Provides
import javax.inject.Named

/**
 * Description PersonModule
 * Author HJR36
 * Date 2018/6/21 10:53
 */

@Module
class PersonInjectModule  {

    @Provides
    @Named(PERSON_JOBCHOICE_PRESENTER)
    fun providerJobChoicePresenter():JobChoiceContract.JobChoicePresenter{
        return JobChoicePresenterIp()
    }

    @Provides
    @Named(PERSON_JOBCHOICE_MOUDLE)
    fun providerJobChoiceMoudle():JobChoiceContract.JobChoiceModule{
        return JobChoiceMoudleIp()
    }

    @Provides
    @Named(PERSON_PERSONINFO_MOUDLE)
    fun providerPersonInfoMoudle():PersonInfoContract.PersonInfoModule{
        return PersonInfoModuleIp()
    }

    @Provides
    @Named(PERSON_PERSONINFO_PRESENTER)
    fun providerPersonInfoPresenter():PersonInfoContract.PersonInfoPresenter{
        return PersonInfoPresenterIp()
    }

    @Provides
    @Named(PERSONAL_VERIFY_MODULER)
    fun providerPersonalVerModule():PersonalVerContract.PersonalVerModule{
        return PersonalVerModuleIp()
    }

    @Provides
    @Named(PERSONAL_VERIFY_PRESENTER)
    fun providerPersonalVerPresenter():PersonalVerContract.PersonalVerPresenter{
        return PersonalVerPresenterIp()
    }
    @Provides
    @Named(CHANGE_PASSWORD_PRESENTER)
    fun providerChangePasswordPresenter():ChangePasswordContract.ChangePasswordPresenter{
        return ChangePasswordPresenterIp()
    }
    @Provides
    @Named(CHANGE_PASSWORD_MODULER)
    fun providerChangePasswordModule():ChangePasswordContract.ChangePasswordModule{
        return ChangePasswordModuleIp()
    }
    @Provides
    @Named(PERSON_COMMIT_PRESENTER)
    fun providerPesonCommitPresenter():PersonCommitContract.PersonCommitPresenter{
        return PersonCommitPresenterIp()
    }
    @Provides
    @Named(PERSON_COMMIT_MODULER)
    fun providerPesonCommitModule():PersonCommitContract.PersonCommitModule{
        return PersonCommitModuleIp()
    }
    @Provides
    @Named(WORKSTATION_PRESENTER)
    fun providerWorkStationPresenter():WorkStationContract.WorkStationPresenter{
        return WorkStationPresenterIp()
    }
    @Provides
    @Named(WORKSTATION_MODULE)
    fun roviderWorkStationModule():WorkStationContract.WorkStationMoudle{
        return WorkStationModuleIp()
    }
    @Provides
    @Named(PERSONSETTING_PRESENTER)
    fun providerPersonSettingPresenter():PersonSettingContract.PersonSettingPresenter{
        return PersonSettingPresenterIp()
    }
    @Provides
    @Named(PERSONSETTING_MODULE)
    fun providerPersonSettingModule():PersonSettingContract.PersonSettingModule{
        return PersonSettingModuleIp()
    }

    @Provides
    @Named(MODIFY_ORG_INFO_MODULE)
    fun providerModifyOrgInfoModule():ModifyOrgInfoModule{
        return ModifyOrgInfoModule()
    }

    @Provides
    @Named(CHANGE_PASSWORD_REPOSITORY)
    fun providerChangePasswordRepository():ChangePasswordRepository{
        return ChangePasswordRepository()
    }
}