package com.ddbes.personal.inject

import com.ddbes.personal.presenter.*
import com.ddbes.personal.view.*
import com.ddbes.personal.viewmodel.*
import dagger.Component

/**
 * Description TODO
 * Author HJR36
 * Date 2018/6/21 10:52
 */
@Component(modules = [(PersonInjectModule::class)])
interface PersonComponent{
    //JobChoice
    fun inject(activity: JobChoiceActivity)
    fun inject(presenter:JobChoicePresenterIp)
    //PersonInfo
    fun inject(activity: PersonInfoActivity)
    fun inject(presenter:PersonInfoPresenterIp)
    //personalVerify
    fun inject(activity:PersonalVerifyActivity)
    fun inject(presenter:PersonalVerPresenterIp)
    //changePassword
    fun inject(activity:ChangePasswordActivity)
    fun inject(presenter:ChangePasswordPresenterIp)
    fun inject(viewModel: ChangePasswordViewModel)
    //personCommit
    fun inject(activity:PersonNameActivity)
    fun inject(presenter:PersonCommitPresenterIp)
    fun inject(activity:MainWorkStationActivity)
    fun inject(activity:PersonAddressActivity)
    //workstation
    fun inject(activity:WorkStationPresenterIp)
    //PersonSetting
    fun inject(activity:PersonalPrivacyActivity)
    fun inject(activity:PersonalNotifyActivity)
    fun inject(presenter:PersonSettingPresenterIp)
    fun inject(viewModel:PersonSettingViewModel)
    fun inject(viewModel:AboutAppViewModel)
    fun inject(viewModel:PersonalSafeViewModel)
    //modifyOrgInfo
    fun inject(viewModel: ModifyOrgInfoViewModel)
}