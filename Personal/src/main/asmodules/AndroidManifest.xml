<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.ddbes.personal">

    <application
        android:allowBackup="true"
        android:icon="@mipmap/icon"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.PersonalVerifyActivity"/>
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.PersonalPrivacyActivity"/>
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.PersonalSettingActivity"/>
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.PersonalNotifyActivity"/>
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.PersonalSafeActivity"/>
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.AboutAppActivity"/>
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.PersonAddressActivity" />
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.PersonInfoActivity" />
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.PersonNameActivity" />
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.JobChoiceActivity" />
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.MainWorkStationActivity" />
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.ChangePasswordActivity" />
    </application>

</manifest>