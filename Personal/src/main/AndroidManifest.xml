<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.ddbes.personal">

    <uses-permission android:name="android.permission.VIBRATE" />
    <application
        android:allowBackup="true"
        android:supportsRtl="true">
        <!--        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".MainActivity" />-->
        <activity
            android:name=".view.PersonalVerifyActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.PersonalPrivacyActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.PersonalSettingActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.PersonalNotifyActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.PersonalSafeActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.AboutAppActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.OrgQrCodeActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.PersonInfoActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.PersonNameActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.JobChoiceActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.PersonAddressActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.MainWorkStationActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.ChangePasswordActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.SMSCodeActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.UnRegisterAccountActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />

    </application>

</manifest>