if (rootProject.ext.isModule) {
    apply plugin: 'com.android.application'
} else {
    apply plugin: 'com.android.library'
}

apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply plugin: 'kotlin-kapt'
//apply plugin: 'kotlin-android'
android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    namespace = "com.joinutech.addressbook" // 使用模块的实际包名
    dexOptions {
        incremental true
        javaMaxHeapSize "4g"
    }
    defaultConfig {
        if (rootProject.ext.isModule) {
            applicationId "com.joinutech.addressbook"
        }
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
//        versionCode rootProject.ext.android.versionCode
//        versionName rootProject.ext.android.versionName
//        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
        kapt {
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }
    }

    repositories {
        flatDir {
//            dirs '../ddbeslibrary/libs'
            dirs 'libs'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    sourceSets {
        main {
            if (rootProject.ext.isModule) {
                manifest.srcFile 'src/main/asmodules/AndroidManifest.xml'
            } else {
                manifest.srcFile 'src/main/AndroidManifest.xml'
            }
            jniLibs.srcDirs = ['libs']
        }
    }

    repositories {
        flatDir {
            dirs 'libs'
        }
    }

    viewBinding{
        enable true
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }
}

dependencies {
    implementation supportLibs.stdlib_jdk7//代替
    api project(':lib_im')
    implementation supportLibs.appcompat
    implementation supportLibs.constraintLayout
//    implementation 'com.google.android.material:material:1.2.0'
    implementation supportLibs.material

    if (!rootProject.ext.isModule) {
        kapt supportLibs.arouter_compiler
    }
    kapt supportLibs.dagger_compiler
    implementation supportLibs.core_ktx
    implementation supportLibs.viewmodel_ktx
//    compile "androidx.core:core-ktx:+"
//    compile "androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0"
//    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation fileTree(dir: 'libs', include: ['*.jar'], exclude: [])
    //引用aar包的写法，使用注释掉的引用方式时，rebuild会报错，所以使用如今的方式引用aar包，tcp
    //除了修改这个方式之外，还要在其他使用到这个aar包的模块中添加‘../模块名/libs’，tcp
    //参考链接https://blog.csdn.net/qq_20872573/article/details/72818758
//    api files('libs/libmagicindicator_release_0_0_1.aar')
    api(name:'libmagicindicator_release_0_0_1',ext:'aar')
}
repositories {
    mavenCentral()
}
