<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.joinutech.addressbook">

    <application
        android:allowBackup="true"
        android:supportsRtl="true">
        <activity android:name=".view.tcpimpages.SearchOrgPersonActivity"></activity>
        <activity android:name=".view.tcpimpages.SearchFriendToTranslateActivity2" />
        <activity
            android:name=".view.tcpimpages.BlackListActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.tcpimpages.SearchFriendToShareActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.tcpimpages.SearchFriendToTranslateActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />

        <activity
            android:name=".view.secret.DeptSelectActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.secret.SecretOpenRangeActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.UndoActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.MineOrganizationsActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.CreateOrgActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.CreateOrganization2Activity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.FriendInfoActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.SearchResultActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.IndustryActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.OrganizationIntroActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.SelectVisitMethodActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.VerifyApplicationActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.ApplicationDetailsActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.OrganizationInfoActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.OrganRenameActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.OrganizationPerferenceActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.OrganizationMoreActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.ChangeCreatorActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.DisbandOrganActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.OrganizationChartActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.SelectedMemberActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.OrgDepartmentActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.DepartmentSettingActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.DepartmentSetActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.DepartmentSortActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.AddFriendActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.FriendRemarkNameActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.FriendListActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.SelectShareMemberActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.ApplicationListActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.PhoneContactActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.PersonSearchSelectList"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.AddOrgPermissionPersonActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.AddOrgPermissionPersonActivity2"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.OrgPermissionActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.PermissionNoticeDetailActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.UpdateDepHeadActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.CompanyIntroContentActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.BatchProcessUndoMsgActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.OrgImportPersonActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" /> <!-- <activity -->
        <!-- android:name=".view.OrgSelectMemberActivity" -->
        <!-- android:configChanges="orientation" -->
        <!-- android:screenOrientation="portrait" /> -->
        <activity
            android:name=".view.OrgExternalContactActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.AddOrgExternalContactActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.OrgExternalContactDetailActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.SearchExternalContactActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.CooperationCompanyApplicationDetailActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.GroupListActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.FriendSelectWithSearchListActivity2"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.GroupListSearchActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.SearchGlobalActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.WithDataListSelectActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.SelectWithSearchListWithBottomShowActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.SelectWithSearchListWithBottomShowActivity2"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
    </application>

</manifest>