package com.joinutech.addressbook.inject

import com.joinutech.addressbook.constract.*
import com.joinutech.addressbook.module.*
import com.joinutech.addressbook.presenter.*
import com.joinutech.addressbook.util.AddressbookUtil.ADDR_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.ADDR_PRESENTER
import com.joinutech.addressbook.util.AddressbookUtil.ADD_ORG_EXTERNAL_CONTACT_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.APPLICATIONDETAIL_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.APPLICATIONDETAIL_PRESENTER
import com.joinutech.addressbook.util.AddressbookUtil.APPLICATIONLIST_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.APPLICATIONLIST_PRESENTER
import com.joinutech.addressbook.util.AddressbookUtil.BATCH_PROCESS_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.CHANGE_PASSWORD_REPOSITORY
import com.joinutech.addressbook.util.AddressbookUtil.DEPSET_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.DEPSET_PRESENTER
import com.joinutech.addressbook.util.AddressbookUtil.FRIENDLIST_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.FRIENDLIST_PRESENTER
import com.joinutech.addressbook.util.AddressbookUtil.GET_COOPERATION_APPLICATION_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.GROUP_LIST_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.ORGCHARTDEP_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.ORGCHARTDEP_PRESENTER
import com.joinutech.addressbook.util.AddressbookUtil.ORGCHART_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.ORGCHART_PRESENTER
import com.joinutech.addressbook.util.AddressbookUtil.ORGINTRO_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.ORGINTRO_PRESENTER
import com.joinutech.addressbook.util.AddressbookUtil.ORGMEMBERINFO_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.ORGMEMBERINFO_PRESENTER
import com.joinutech.addressbook.util.AddressbookUtil.ORG_EXTERNAL_CONTACT_DETAIL_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.ORG_EXTERNAL_CONTACT_LIST_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.PHONECONTACTLIST_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.PHONECONTACTLIST_PRESENTER
import com.joinutech.addressbook.util.AddressbookUtil.REMARKNAME_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.REMARKNAME_PRESENTER
import com.joinutech.addressbook.util.AddressbookUtil.SEARCHLIST_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.SEARCHLIST_PRESENTER
import com.joinutech.addressbook.util.AddressbookUtil.SELECTEDMEMBER_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.SELECTEDMEMBER_PRESENTER
import com.joinutech.addressbook.util.AddressbookUtil.SELECT_VISIT_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.UNDOLIST_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.UNDOLIST_PRESENTER
import com.joinutech.addressbook.util.AddressbookUtil.VERAPPLICATION_MODULE
import com.joinutech.addressbook.util.AddressbookUtil.VERAPPLICATION_PRESENTER
import dagger.Module
import dagger.Provides
import javax.inject.Named

/**
 *<AUTHOR>
 *@date 2018/11/6
 */
@Module
class AddressbookInjectModule {
    @Provides
    @Named(ADDR_PRESENTER)
    fun provideAddrPresenter(): AddressbookConstract.AddressbookPresenter {
        return AddressbookPresenterIp()
    }

    @Provides
    @Named(ADDR_MODULE)
    fun provideAddrModule(): AddressbookConstract.AddressbookModule {
        return AddressbookModuleIp()
    }

    @Provides
    @Named(APPLICATIONDETAIL_PRESENTER)
    fun provideApplicationDetailPresenter(): ApplicationDetailConstract.ApplicationDetailPresenter {
        return ApplicationDetailPresenterIp()
    }

    @Provides
    @Named(APPLICATIONDETAIL_MODULE)
    fun provideApplicationDetailModule(): ApplicationDetailConstract.ApplicationDetailModule {
        return ApplicationDetailModuleIp()
    }

    @Provides
    @Named(ORGMEMBERINFO_PRESENTER)
    fun provideOrganizationMemberInfoPresenter(): FriendInfoConstract.
    FriendInfoPresenter {
        return FriendInfoPresenterIp()
    }

    @Provides
    @Named(ORGMEMBERINFO_MODULE)
    fun provideOrganizationMemberInfoModule(): FriendInfoConstract.
    FriendInfoModule {
        return FriendInfoModuleIp()
    }

    @Provides
    @Named(UNDOLIST_PRESENTER)
    fun provideUndoListPresenter(): UndoListConstract.UndoListPresenter {
        return UndoListPresenterIp()
    }

    @Provides
    @Named(UNDOLIST_MODULE)
    fun provideUndoListModule(): UndoListConstract.UndoListModule {
        return UndoListModuleIp()
    }

    @Provides
    @Named(ORGINTRO_PRESENTER)
    fun provideOrganizationIntroPresenter(): OrganizationIntroConstact.OrganizationIntroPresenter {
        return OrganizationIntroPresenterIp()
    }

    @Provides
    @Named(ORGINTRO_MODULE)
    fun provideOrganizationIntroModule(): OrganizationIntroConstact.OrganizationIntroMoudle {
        return OrganizationIntroModuleIp()
    }

    @Provides
    @Named(VERAPPLICATION_PRESENTER)
    fun provideVerifyApplicationPresenter(): VerifyApplicationConstact.VerifyApplicationPresenter {
        return VerifyApplicationPresenterIp()
    }

    @Provides
    @Named(VERAPPLICATION_MODULE)
    fun provideVerifyApplicationModule(): VerifyApplicationConstact.VerifyApplicationModule {
        return VerifyApplicationModuleIp()
    }

    @Provides
    @Named(SEARCHLIST_PRESENTER)
    fun provideSearchListPresenter(): SearchResultListContract.SearchResultListPresenter {
        return SearchListPresenterIp()
    }

    @Provides
    @Named(SEARCHLIST_MODULE)
    fun provideSearchListModule(): SearchResultListContract.SearchResultListModule {
        return SearchListModuleIp()
    }

    @Provides
    @Named(ORGCHART_PRESENTER)
    fun provideOrgChartPresenter(): OrgChartConstract.OrgChartPresenter {
        return OrgChartPresenterIp()
    }

    @Provides
    @Named(ORGCHART_MODULE)
    fun provideOrgChartModule(): OrgChartConstract.OrgChartModule {
        return OrgChartModuleIp()
    }

    @Provides
    @Named(ORGCHARTDEP_PRESENTER)
    fun provideOrgDepPresenter(): OrgDepConstract.OrgDepPresenter {
        return OrgDepPresenterIp()
    }

    @Provides
    @Named(ORGCHARTDEP_MODULE)
    fun provideOrgDepModule(): OrgDepConstract.OrgDepModule {
        return OrgDepModuleIp()
    }

    @Provides
    @Named(SELECTEDMEMBER_PRESENTER)
    fun provideSelectedMemberPresenter(): SelectedMemberConstract.SelectedMemberPresenter {
        return SelectedMemberPresenterIp()
    }

    @Provides
    @Named(SELECTEDMEMBER_MODULE)
    fun provideSelectedMemberModule(): SelectedMemberConstract.SelectedMemberModule {
        return SelectedMemberModuleIp()
    }

    @Provides
    @Named(DEPSET_PRESENTER)
    fun provideDepSetPresenter(): DepSetConstarct.DepSetPresenter {
        return DepSetPresenterIp()
    }

    @Provides
    @Named(DEPSET_MODULE)
    fun provideDepSetModule(): DepSetConstarct.DepSetModule {
        return DepSetModuleIp()
    }

    @Provides
    @Named(REMARKNAME_PRESENTER)
    fun provideRemarkNamePresenter(): FriendRemarkNameConstract.FriendRemarkNamePresenter {
        return FriendRemarkNamePresenterIp()
    }

    @Provides
    @Named(REMARKNAME_MODULE)
    fun provideRemarkNameModule(): FriendRemarkNameConstract.FriendRemarkNameModule {
        return FriendRemarkNameModuleIp()
    }

    @Provides
    @Named(FRIENDLIST_PRESENTER)
    fun provideFriendListPresenter(): FriendListConstract.FriendListPresenter {
        return FriendListPresenterIp()
    }

    @Provides
    @Named(FRIENDLIST_MODULE)
    fun provideFriendListModule(): FriendListConstract.FriendListModule {
        return FriendListModuleIp()
    }

    @Provides
    @Named(PHONECONTACTLIST_PRESENTER)
    fun providePhoneContactListPresenter(): PhoneContactConstract.PhoneContactPresenter {
        return PhoneContactPresenterIp()
    }

    @Provides
    @Named(PHONECONTACTLIST_MODULE)
    fun providePhoneContactListModule(): PhoneContactConstract.PhoneContactModule {
        return PhoneContactModuleIp()
    }

    @Provides
    @Named(APPLICATIONLIST_PRESENTER)
    fun provideApplicationListPresenter(): ApplicationListConstract.ApplicationListPresenter {
        return ApplicationListPresenterIp()
    }

    @Provides
    @Named(APPLICATIONLIST_MODULE)
    fun provideApplicationListModule(): ApplicationListConstract.ApplicationListModule {
        return ApplicationListModuleIp()
    }

    @Provides
    @Named(SELECT_VISIT_MODULE)
    fun provideSelectVisitModule(): SelectVisitModel {
        return SelectVisitModel()
    }

    @Provides
    @Named(BATCH_PROCESS_MODULE)
    fun provideBatchProcessModule(): BatchProcessUndoMsgModule {
        return BatchProcessUndoMsgModule()
    }

//    @Provides
//    @Named(WORK_NOTICE_SET_MODULE)
//    fun provideWorkNoticeSetModule():WorkNoticeSetModel{
//        return WorkNoticeSetModel()
//    }

    @Provides
    @Named(CHANGE_PASSWORD_REPOSITORY)
    fun provideChangeCreatorReposity(): ChangeCreatorReposity {
        return ChangeCreatorReposity()
    }

    @Provides
    @Named(ADD_ORG_EXTERNAL_CONTACT_MODULE)
    fun provideAddOrgExternalContactModule(): AddOrgExternalContactModule {
        return AddOrgExternalContactModule()
    }

    @Provides
    @Named(ORG_EXTERNAL_CONTACT_LIST_MODULE)
    fun provideOrgExternalContactListModuel(): OrgExternalContactListModuel {
        return OrgExternalContactListModuel()
    }

    @Provides
    @Named(ORG_EXTERNAL_CONTACT_DETAIL_MODULE)
    fun provideOrgExternalContactDetailModuel(): OrgExternalContactDetailModuel {
        return OrgExternalContactDetailModuel()
    }

    @Provides
    @Named(GET_COOPERATION_APPLICATION_MODULE)
    fun provideCooperationApplicationModule(): CooperationApplicationModule {
        return CooperationApplicationModule()
    }

    @Provides
    @Named(GROUP_LIST_MODULE)
    fun provideGroupListModule(): GroupListModule {
        return GroupListModule()
    }
}