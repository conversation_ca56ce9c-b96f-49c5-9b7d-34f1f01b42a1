package com.joinutech.addressbook.inject


import com.joinutech.addressbook.view.*
import com.joinutech.addressbook.presenter.*
import com.joinutech.addressbook.viewModel.*
import dagger.Component

/**
 *<AUTHOR>
 *@date 2018/11/6
 */
@Component(modules = [(AddressbookInjectModule::class)])
interface AddressbookComponent{
    fun inject(presenter:AddressbookPresenterIp)
    fun inject(activity:MineOrganizationsActivity)
    fun inject(activity: IndustryActivity)
    fun inject(activity: CreateOrganization2Activity)
    fun inject(activity: OrganizationInfoActivity)
    fun inject(activity: OrganRenameActivity)
    fun inject(activity: OrganizationPerferenceActivity)
    fun inject(activity:ApplicationDetailsActivity)
    fun inject(presenter:ApplicationDetailPresenterIp)
    fun inject(fragment:AddressbookFragment)
    fun inject(activity:FriendInfoActivity)
    fun inject(presenter:FriendInfoPresenterIp)
    fun inject(activity:UndoActivity)
    fun inject(presenter:UndoListPresenterIp)
    fun inject(activity:OrganizationIntroActivity)
    fun inject(presenter:OrganizationIntroPresenterIp)
    fun inject(activity:VerifyApplicationActivity)
    fun inject(presenter:VerifyApplicationPresenterIp)
    fun inject(activity:SearchResultActivity)
    fun inject(presenter:SearchListPresenterIp)
    fun inject(activity:ChangeCreatorActivity)
    fun inject(activity:DisbandOrganActivity)
    fun inject(activity:OrganizationMoreActivity)
    fun inject(activity:OrganizationChartActivity)
    fun inject(presenter:OrgChartPresenterIp)
    fun inject(activity:OrgDepartmentActivity)
    fun inject(presenter:OrgDepPresenterIp)
    fun inject(activity:SelectedMemberActivity)
    fun inject(presenter:SelectedMemberPresenterIp)
    fun inject(activity:DepartmentSetActivity)
    fun inject(presenter:DepSetPresenterIp)
    fun inject(activity:FriendRemarkNameActivity)
    fun inject(presenter:FriendRemarkNamePresenterIp)
    fun inject(activity:FriendListActivity)
    fun inject(presenter:FriendListPresenterIp)
    fun inject(activity:ApplicationListActivity)
    fun inject(presenter:ApplicationListPresenterIp)
    fun inject(presenter:PhoneContactPresenterIp)
    fun inject(activity:PhoneContactActivity)
    fun inject(activity:OrgPermissionActivity)
    fun inject(activity:AddOrgPermissionPersonActivity)
    fun inject(activity:AddOrgPermissionPersonActivity2)
    fun inject(viewModel:SelectVisitViewModel)
    fun inject(activity:UpdateDepHeadActivity)
    fun inject(activity:CompanyIntroContentActivity)
    fun inject(viewModel:BatchProcessUndoMsgViewModel)
//    fun inject(viewModel:WorkNoticeSetViewModel)
    fun inject(viewModel:OrgImportPersonViewModel)
    fun inject(activity:OrgImportPersonActivity)
    fun inject(viewModel:ChangeCreatorViewModel)
    fun inject(viewModel:AddOrgExternalContactViewModel)
    fun inject(viewModel:OrgExternalContactListViewModel)
    fun inject(viewModel:OrgExternalContactDetailViewModel)
    fun inject(viewModel:CooperationApplicationViewModel)
    fun inject(viewModel:FriendSelectViewModel)
    fun inject(viewModel:GroupListViewModel)
}