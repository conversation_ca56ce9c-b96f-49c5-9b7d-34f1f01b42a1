package com.joinutech.addressbook.view

import android.app.Activity
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.gyf.immersionbar.ImmersionBar
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.OrgExternalContactPersonAdapter
import com.joinutech.addressbook.databinding.ActivitySearchExternalContactBinding
import com.joinutech.addressbook.viewModel.OrgExternalContactListViewModel
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.ExternalContactListBean
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/11/29 13:58
 * @packageName: com.joinutech.addressbook.view
 * @Company: JoinuTech
 */
class SearchExternalContactActivity :MyUseBindingActivity<ActivitySearchExternalContactBinding>(){

    override val contentViewResId: Int
        get() = R.layout.activity_search_external_contact

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivitySearchExternalContactBinding {
        return ActivitySearchExternalContactBinding.inflate(layoutInflater)
    }

    private var personList = arrayListOf<ExternalContactListBean>()
    private lateinit var adapter:OrgExternalContactPersonAdapter
    private var keyWord = ""
    private var type:Int = 0
    private var level:String = ""
    private var companyId:String = ""
    private var isEdit = false
    private lateinit var viewModel: OrgExternalContactListViewModel

    override fun initImmersion() {
        if (intent != null) {
            type = intent.getIntExtra("type",0)
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("level"))) {
                level = intent.getStringExtra("level") ?: ""
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                companyId = intent.getStringExtra("companyId") ?: ""
            }
            isEdit = intent.getBooleanExtra("isEdit",false)
        }
        val immersionBar = ImmersionBar.with(this)
        immersionBar?.titleBar(R.id.tb_top_layout)?.init()
        immersionBar?.statusBarDarkFont(true, 0.2f)?.init()
    }

    override fun initView() {
         binding.rvList.layoutManager = LinearLayoutManager(this)
        viewModel = getModel(OrgExternalContactListViewModel::class.java)
    }

        override fun initLogic() {
        super.initLogic()
        adapter = OrgExternalContactPersonAdapter(mContext!!,personList)
        binding.rvList.adapter = adapter
         binding.etTopSearch.addTextChangedListener(object :TextWatcher{
            override fun afterTextChanged(s: Editable?) {
                if (s!=null){
                     binding.ivTopCancel.visibility = View.VISIBLE
                    keyWord = s.toString()
                    getData()
                }else {
                    binding.ivTopCancel.visibility = View.GONE
                    binding.layoutEmptyLayout.visibility = View.GONE
                    binding.rvList.visibility = View.GONE
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

        })
        binding.ivTopCancel.setOnClickListener(this)
         binding.tvTopCancel.setOnClickListener(this)
        binding.ivTopBack.setOnClickListener(this)
        getObserver()
        getData()
    }

    private fun getObserver() {
        viewModel.getExternalContactListSuccessObservable.observe(this, Observer {
            dismissDialog()
            if (!it.isNullOrEmpty()) {
                personList = it as ArrayList<ExternalContactListBean>
                binding.layoutEmptyLayout.visibility = View.GONE
                binding.rvList.visibility = View.VISIBLE
                adapter.setSourceList(personList)
                adapter.setCompanyId(companyId)
                adapter.setIsEdit(isEdit)
                adapter.setTypeIndex(type)
            } else {
                binding.layoutEmptyLayout.visibility = View.VISIBLE
                binding.rvList.visibility = View.GONE
            }
        })
        viewModel.getExternalContactListErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!,it)
        })
    }

    private fun getData() {
        getLoadingDialog("",true)
        viewModel.getExternalContactList(bindToLifecycle(),companyId,keyWord,level,
                type.toString(),accessToken!!)
    }

    override fun onNoDoubleClick(v: View) {
        when(v){
            binding.ivTopCancel,binding.tvTopCancel ->{
                binding.etTopSearch.setText("")
            }
             binding.ivTopBack ->{
                onBackPressed()
            }
        }
    }



    override fun showToolBar(): Boolean {
        return false
    }



    override fun openArouterReceive(): Boolean {
        return false
    }
}