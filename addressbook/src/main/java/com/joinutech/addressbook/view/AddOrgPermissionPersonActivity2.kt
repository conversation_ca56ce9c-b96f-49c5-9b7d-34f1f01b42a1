package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.app.Activity
import android.view.LayoutInflater
import android.view.View
import android.widget.CompoundButton
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.library.im.imtcp.Logger
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.AddressbookConstract
import com.joinutech.addressbook.databinding.ActivityAddorgperBinding
import com.joinutech.addressbook.databinding.ActivityAddorgperNewBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.common.adapter.MultiTypeAdapter
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.OrgPermissionListBean
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.MyDialog
import com.joinutech.ddbeslibrary.utils.ORG_PERMISS_TYPE
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.kyleduo.switchbutton.SwitchButton
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.*
import javax.inject.Inject
import javax.inject.Named


/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName: AddOrgPermissionPersonActivity2
 * @Desc: 管理员操作 页面优化
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/3/27 14:52
 */
//编辑管理员页面
class AddOrgPermissionPersonActivity2(
    override val contentViewResId:
    Int = R.layout.activity_addorgper_new
) : MyUseBindingActivity<ActivityAddorgperNewBinding>() {

    private var type: String = ""
    private var bean: OrgPermissionListBean = OrgPermissionListBean()
    private var userList: ArrayList<String> = arrayListOf()
    private var managerId: String = ""

    @Inject
    @field:Named(AddressbookUtil.ADDR_PRESENTER)
    lateinit var presenter: AddressbookConstract.AddressbookPresenter
    private var companyId: String = ""

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        setRightTitle("保存", this)
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("type"))) {
                type = intent.getStringExtra("type") ?: ""
            }
            when (type) {
                "add" -> {
                    //新增
                    setPageTitle("新增管理员")
                }
                "edit" -> {
                    //修改
                    setPageTitle("编辑管理员")
                }
            }
            if (intent.getSerializableExtra("bean") != null) {
                bean = intent.getSerializableExtra("bean") as OrgPermissionListBean
                managerId = bean.managerId
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                companyId = intent.getStringExtra("companyId") ?: ""
            }
        }
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        when (type) {
            "add" -> {
                //新增
                binding.deletePermission.visibility = View.GONE
            }
            "edit" -> {
                //修改
                binding.deletePermission.visibility = View.VISIBLE
            }
        }
        DaggerAddressbookComponent.builder().build().inject(this)
        initPermissionView()
    }

    private var adapter: MultiTypeAdapter<PermissionData>? = null
    private val permissions = mutableListOf<PermissionData>()

    //手动添加权限项
    private val permissionList = arrayListOf(
        PermissionData(
            icon = R.drawable.per_org,
            name = "团队管理权限",
            desc = "可对团队资料、成员及架构进行调整，可编辑公告",
            powerValue = "1"
        ),
        PermissionData(
            icon = R.drawable.per_attendace,
            name = "考勤管理权限",
            desc = "可查看全团队考勤数据、调整考勤规则等",
            powerValue = "2"
        ),
        PermissionData(
            icon = R.drawable.icon_task_manager,
            name = "项目创建权限",
            desc = "可在任务功能中创建新项目",
            powerValue = "3"
        ),
        PermissionData(
            icon = com.joinutech.ddbeslibrary.R.drawable.icon_approval_permission,
            name = "审批管理权限",
            desc = "可快速启/停用审批模板，可编辑审批流程",
            powerValue = "4"
        ),
        PermissionData(
            icon = R.drawable.icon_report_permission,
            name = "汇报管理权限",
            desc = "可设定工作汇报规则，查看工作汇报统计",
            powerValue = "5"
        ),
        PermissionData(
            icon = R.drawable.icon_number_report_permission,
            name = "企业数字报告预览权限",
            desc = "可在管理团队模块查看本团队的企业数字报告",
            powerValue = "6"
        ),
        PermissionData(
            icon = R.drawable.icon_total_approval,
            name = "查看全部审批权限",
            desc = "可查看团队全员发起的审批",
            powerValue = "7"
        ),
       /* PermissionData(
            icon = R.drawable.icon_health_statistics,
            name = "健康上报统计权限",
            desc = "可查看团队全员提交的健康上报，并导出表格",
            powerValue = "8"
        ),*/
        PermissionData(
            icon = R.drawable.icon_cloud_manage,
            name = "云文档管理权限",
            desc = "可对团队所有部门文件进行查看、删除等操作",
            powerValue = "9"
        ),
//        PermissionData(//访客系统,编辑管理员页面添加权限选项,完成
//            icon = R.drawable.icon_visitor_per,
//            name = "访客管理权限",
//            desc = "可对访客申请进行查看、发放通行证等操作",
//            powerValue = "a"
//        )
    )

    private val allToggleIndex = 0
    private fun initPermissionView() {
        adapter = MultiTypeAdapter(this, permissions,
            generateViewType = { _: Int, data: PermissionData ->
                data.icon
            },
            generateLayoutId = { type ->
                if (type == 0) {
                    R.layout.item_permission_simple_layout
                } else {
                    R.layout.item_permission_layout
                }
            },
            onBindItem = { position: Int, data: PermissionData, itemView: View ->
                val toggle = itemView.findViewById<SwitchButton>(R.id.sb_toggle)
                when (data.icon) {
                    0 -> {
                        itemView.findViewById<TextView>(R.id.tv_name).text = data.name
                    }
                    else -> {
                        itemView.findViewById<ImageView>(R.id.iv_icon).setImageResource(data.icon)
                        itemView.findViewById<TextView>(R.id.tv_name).text = data.name
                        itemView.findViewById<TextView>(R.id.tv_desc).text = data.desc
                    }
                }
                if (ORG_PERMISS_TYPE.checkSuperPermission(bean.managerPower)) {
                    toggle.visibility = View.GONE
                } else {
                    toggle.visibility = View.VISIBLE
//                        if (position != allToggleIndex) {
//                            data.isSelect = permissions[allToggleIndex].isSelect
//                        }
                    toggle.setCheckedNoEvent(data.isSelect)
                    toggle.setOnCheckedChangeListener { _: CompoundButton, select: Boolean ->
                        data.isSelect = select
                        if (position == allToggleIndex) {
                            permissions.forEach {
                                it.isSelect = select
                            }
                            adapter?.notifyDataSetChanged()
                        } else {
                            val newAllToggleState = if (select) {
                                showLog(permissions)
                                val temp = permissions.subList(1, permissions.size)
                                val size = temp.filter { !it.isSelect }.size
                                size == 0
                            } else {
                                false
                            }
                            val allToggle = permissions[allToggleIndex]
                            if ((!allToggle.isSelect && newAllToggleState)
                                || (allToggle.isSelect && !newAllToggleState)
                            ) {
                                allToggle.isSelect = newAllToggleState
                                adapter?.notifyItemChanged(allToggleIndex)
                            }
                        }
                    }
                }
            },
            onItemClick = { _: Int, _: PermissionData, _: View ->
            })

        binding.rvList.layoutManager = LinearLayoutManager(this)
        binding.rvList.adapter = adapter
    }

    override fun initLogic() {
        super.initLogic()
        initData()
        binding.selectedLayout.setOnClickListener(this)
        binding.deletePermission.setOnClickListener(this)
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityAddorgperNewBinding =
        ActivityAddorgperNewBinding.inflate(layoutInflater)


    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            com.joinutech.ddbeslibrary.R.id.tv_toolbar_right -> {
                //点击保存
                saveData()
            }
            R.id.selectedLayout -> {//点击选择人员，编辑管理员页
                // TODO: 2021/8/25 选择成员授予权限
                ARouter.getInstance()
                    .build(RouteOrg.friendListWithSearchActivity)
                    .withSerializable("selectedList", userList)
                    .withString("type", "givePermission")
                    .withString("companyId", companyId)
                    .navigation()
            }
            R.id.deletePermission -> {
                delOrgItem()
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun initData() {
        if (ORG_PERMISS_TYPE.checkSuperPermission(bean.managerPower)) {
            //超级管理员
            binding.ivPerIcon.visibility = View.VISIBLE
            binding.editName.isFocusable = false
            binding.editName.isFocusableInTouchMode = false
            binding.editName.hint = ""
            binding.deletePermission.visibility = View.GONE
        } else {
            binding.ivPerIcon.visibility = View.GONE
        }
        binding.editName.setText(bean.managerName)
        if (bean.userIds.isNotEmpty()) {
            userList.clear()
            userList.addAll(bean.userIds)
        }
        checkPermMembers()
        if (StringUtils.isNotBlankAndEmpty(bean.managerPower)) {
            permissions.clear()
            if (ORG_PERMISS_TYPE.checkSuperPermission(bean.managerPower)) {
                permissions.addAll(
                    arrayListOf(
                        PermissionData(name = "普通管理员设置权限"),
                        PermissionData(name = "团队偏好设置权限")
                    )
                )
                permissions.addAll(permissionList)
            } else {
                permissions.add(PermissionData(name = "全部权限", powerValue = "123456"))
                permissions.addAll(permissionList)
                for (per in permissions) {
                    per.isSelect = bean.managerPower.contains(per.powerValue)
                }
            }
            adapter?.notifyDataSetChanged()
        } else {
            permissions.clear()
            permissions.add(PermissionData(name = "全部权限"))
            permissions.addAll(permissionList)
            adapter?.notifyDataSetChanged()
        }
    }

    private fun delOrgItem() {
        val dialog = MyDialog(
            mContext!!, 260, 115,
            "是否确认删除此管理员？", needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0
        )
        dialog.setCanceledOnTouchOutside(true)
        dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
            override fun clickRightBtn() {
                getLoadingDialog("", false)
                presenter.deleteManager(bindToLifecycle(), accessToken!!, companyId,
                    bean.managerId, {
                        dismissDialog()
                        ToastUtil.show(mContext!!, "删除管理员成功")
                        setResult(Activity.RESULT_OK)
                        finish()
                    }, {
                        dismissDialog()
                        ToastUtil.show(mContext!!, it)
                    })
            }
        })
        dialog.show()
    }

    private fun getPermissions(): String {
        return if (ORG_PERMISS_TYPE.checkSuperPermission(bean.managerPower)) {
            ORG_PERMISS_TYPE.SUPER_PERMISSION
        } else {
            permissions.filter {
                it.isSelect
            }.map {
                it.powerValue
            }.let {
                val powers = StringBuilder()
                for (power in it) {
                    if (!powers.contains(power)) {
                        powers.append(power)
                    }
                }
                powers.toString()
            }
        }
    }


    private fun saveData() {
        bean.userIds = userList
        if (!ORG_PERMISS_TYPE.checkSuperPermission(bean.managerPower)) {
            val managerName = binding.editName.text.toString()
            if (StringUtils.isEmpty(managerName)) {
                ToastUtil.show(mContext!!, "请设置管理员名称")
                return
            }
            bean.managerName = managerName
            bean.managerId = managerId
        }
        bean.managerPower = getPermissions()

        if (type == "add") {
            //新增
            getLoadingDialog("", false)
            val map = hashMapOf<String, Any>()
            map["userIds"] = bean.userIds
            map["managerName"] = bean.managerName
            map["managerPower"] = bean.managerPower
            Logger.i("测试权限", "=add点击保存时，上传权限参数==managerPower=" + bean.managerPower)
            presenter.addManager(bindToLifecycle(), accessToken!!, companyId, map, {
                dismissDialog()
                ToastUtil.show(mContext!!, "新增管理员成功")
                setResult(Activity.RESULT_OK)
                finish()
            }, {
                dismissDialog()
                ToastUtil.show(mContext!!, it)
            })
        } else if (type == "edit") {
            //编辑
            getLoadingDialog("", false)
            val map = hashMapOf<String, Any>()
            map["managerId"] = bean.managerId
            map["userIds"] = bean.userIds
            map["managerName"] = bean.managerName
            map["managerPower"] = bean.managerPower
            Logger.i("测试权限", "=edit点击保存时，上传权限参数==managerPower=" + bean.managerPower)
            presenter.editManager(bindToLifecycle(), accessToken!!, companyId, map, {
                dismissDialog()
                ToastUtil.show(mContext!!, "修改管理员成功")
                setResult(Activity.RESULT_OK)
                finish()
            }, {
                dismissDialog()
                ToastUtil.show(mContext!!, it)
            })
        }
    }

    @SuppressLint("SetTextI18n")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun translateMsgSuccess(event: EventBusEvent<ArrayList<String>>) {
        if (event.code == EventBusAction.SELECTEDPER_USERLIST) {
            userList.clear()
            event.data?.let {
                userList.addAll(it)
            }
            checkPermMembers()
        }
    }

    private fun checkPermMembers() {
        if (userList.isNullOrEmpty()) {
            //没有选择人
            binding.selectedText.text = ""
        } else {
            //存在且不为空
            binding.selectedText.text = "已选择${userList.size}人"
        }
    }

    override fun showToolBar(): Boolean {
        return true
    }


    override fun openArouterReceive(): Boolean {
        return false
    }
}

data class PermissionData(
    val icon: Int = 0,
    val name: String,
    val desc: String = "",
    /**1 团队管理
     * 2 考勤管理
     * 3 任务管理
     * 4 审批管理
     * 5 汇报查看
     * 6 企业数字报告查看*/
    val powerValue: String = "",
    var isSelect: Boolean = false
)