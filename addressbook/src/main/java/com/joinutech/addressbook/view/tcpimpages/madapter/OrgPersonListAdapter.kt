package com.joinutech.addressbook.view.tcpimpages.madapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.addressbook.R
import com.joinutech.ddbeslibrary.bean.OrgPersonBean
import com.joinutech.ddbeslibrary.utils.CommonUtils.getColor
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.StringUtils.Companion.setSpanColorStr
import com.joinutech.ddbeslibrary.utils.XUtil
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.CircleImageView
import java.util.*
import java.util.regex.Pattern

class OrgPersonListAdapter(var context: Context, val dataList: ArrayList<OrgPersonBean>) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private lateinit var listener: OnClickOrgPersonListener
    private var keyWord = ""


    override fun getItemCount(): Int {
        return if (dataList.isNullOrEmpty()) 0 else dataList.size
    }

    fun setKeyWord(key: String) {
        keyWord = key
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val itemView = LayoutInflater.from(context)
            .inflate(R.layout.item_org_person_layout, parent, false)
        return OrgPersonViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val personHolder = holder as OrgPersonViewHolder
        if (dataList.isNotEmpty()) {
            val personBean = dataList[position]

            if (position == 0) {
                XUtil.showView(personHolder.indexTv)
                personHolder.indexTv.text = personBean.initial
            } else {
                val lastOneIndex = dataList[position - 1].initial
                if (lastOneIndex != personBean.initial) {
                    XUtil.showView(personHolder.indexTv)
                    personHolder.indexTv.text = personBean.initial
                } else {
                    XUtil.hideView(holder.indexTv)
                }
            }
            ImageLoaderUtils.loadImage(context, holder.headerIv, personBean.headimg)
            //富文本，关键字变色--------------开始---------
            if (!personBean.name.trim({ it <= ' ' }).isEmpty() && !keyWord.trim({ it <= ' ' })
                    .isEmpty() && personBean.name.contains(keyWord)
            ) {
                val allKeyWordList: MutableList<String> = ArrayList()
                val p1 = Pattern.compile(keyWord)
                val m1 = p1.matcher(personBean.name)
                while (m1.find()) {
                    allKeyWordList.add(m1.group())
                }
                personHolder.nameTv.setText(
                    setSpanColorStr(
                        personBean.name, allKeyWordList, getColor(context, com.joinutech.ddbeslibrary.R.color.colorFF5000)
                    )
                )
            }else{
                personHolder.nameTv.text = personBean.name
            }
            //富文本，关键字变色--------------结束---------

            if (StringUtils.isNotBlankAndEmpty(personBean.positionName)) {
                XUtil.showView(personHolder.positionTv)
                personHolder.positionTv.text = personBean.positionName
            } else {
                XUtil.hideView(personHolder.positionTv)
            }
            if (personBean.isSelect) {
                personHolder.chooseIv.isSelected = true
            } else {
                personHolder.chooseIv.isSelected = false
            }
            personHolder.itemView.setOnClickListener(object : View.OnClickListener {
                override fun onClick(v: View?) {
                    if (this@OrgPersonListAdapter::listener.isInitialized) {
                        listener.onClick(v, position)
                    }
                }
            })

        }
    }

    fun setItemClickListener(listener: OnClickOrgPersonListener) {
        this.listener = listener
    }



    interface OnClickOrgPersonListener {
        fun onClick(view: View?, position: Int)
    }

    class OrgPersonViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val headerIv = itemView.findViewById<CircleImageView>(R.id.header_civ)
        val nameTv = itemView.findViewById<TextView>(R.id.name_tv)
        val positionTv = itemView.findViewById<TextView>(R.id.position_tv)
        val statusTv = itemView.findViewById<TextView>(R.id.status_tv)
        val indexTv = itemView.findViewById<TextView>(R.id.index_tv)
        val chooseIv = itemView.findViewById<ImageView>(R.id.choose_tag_iv)

    }
}

