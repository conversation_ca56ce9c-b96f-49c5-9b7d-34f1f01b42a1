package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.bumptech.glide.Glide
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.AddressbookConstract
import com.joinutech.addressbook.databinding.ActivityCreat2Binding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.CompanyTypeBean
import com.joinutech.ddbeslibrary.utils.*
import javax.inject.Inject
import javax.inject.Named

/**
 *<AUTHOR>
 *@date 2018/11/20
 */
class CreateOrganization2Activity : MyUseBindingActivity<ActivityCreat2Binding>() {

    override val contentViewResId: Int = R.layout.activity_creat2
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityCreat2Binding {
        return ActivityCreat2Binding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true
    override fun openArouterReceive(): Boolean = false

    @Inject
    @field:Named(AddressbookUtil.ADDR_PRESENTER)
    lateinit var presenter: AddressbookConstract.AddressbookPresenter
    private var companyType = ""
    private var companyNums = ""
    private var companyName = ""
    private var logo = ""
    override fun initImmersion() {
        setPageTitle("创建团队")
        whiteStatusBarBlackFont()
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
    }

    override fun initView() {
        DaggerAddressbookComponent.builder().build().inject(this)
        binding.creat2Logo.setOnClickListener(this)
        binding.creat2R1.setOnClickListener(this)
        binding.creat2R2.setOnClickListener(this)
        binding.creat2R3.setOnClickListener(this)
        binding.creat2Next.setOnClickListener(this)
        companyName = intent.getStringExtra("name") ?: ""
        if (companyName.isNullOrBlank()) {

            finish()
        }
        binding.creat2Name.text = companyName
    }

    override fun initLogic() {
        super.initLogic()
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.creat2_logo -> {
                dealIcon()
            }
            R.id.creat2_r1 -> {//行业
                val intent1 = Intent(mContext!!, IndustryActivity::class.java)
                startActivityForResult(intent1, 0x11)
            }
            R.id.creat2_r2 -> {//团队类型
                presenter.getCompanyTypes(bindToLifecycle(), accessToken!!, {
                    selCompanyType(it)
                }, {
                    ToastUtil.show(mContext!!, it)
                })
            }
            R.id.creat2_r3 -> {
                selCompanyNums()
            }

            R.id.creat2_next -> {
                val map = hashMapOf<String, Any>()
                map["name"] = companyName
                map["type"] = companyType
                map["logo"] = logo
                map["scale"] = companyNums
                map["industry"] = industry
                getLoadingDialog("正在创建", true)
                presenter.creatCompany(bindToLifecycle(), accessToken!!, map, {
                    dismissDialog()
                    toastShort("创建成功")
                    setResult(0x22)
                    //创建团队后需要刷新团队列表和群组列表（因为创建成功团队群组后不会发消息
                    // 但是需要去刷新全部的团队列表）
                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST, "2"))
                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_GROUP_LIST, data = "4"))
                    //创建团队成功后刷新团队群组信息
                    finish()
                }, {
                    dismissDialog()
                    ToastUtil.show(mContext!!, it)
                })
            }
        }
    }

    private fun selCompanyType(list: List<CompanyTypeBean>) {
        val view = View.inflate(mContext, com.joinutech.ddbeslibrary.R.layout.dialog_bottom_layout, null)
        val layout = view.findViewById<LinearLayout>(com.joinutech.ddbeslibrary.R.id.dialog_bottom_layout)
        val dialog = BottomDialogUtil.showBottomDialog(mContext!!, view, Gravity.BOTTOM)
        for (i in list.indices) {
            val item = View.inflate(mContext, com.joinutech.ddbeslibrary.R.layout.item_dialog_bottom, null)
            val tv = item.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.dialog_bottom_tv)
            val line = item.findViewById<View>(com.joinutech.ddbeslibrary.R.id.dialog_bottom_line)
            tv.text = list[i].name
            if (i == list.size - 1) {
                line.visibility = View.GONE
            }
            item.tag = i
            item.setOnClickListener {
                companyType = list[item.tag as Int].name
                binding.creat2Tv2.text = companyType
                binding.creat2Next.text = "创建团队"
                dialog.dismiss()
            }
            layout.addView(item, LinearLayout.LayoutParams.MATCH_PARENT,
                    DeviceUtil.dip2px(mContext!!, 50f))
        }
        val mDismiss = view.findViewById<TextView>(R.id.dismiss)

        mDismiss.setOnClickListener {
            dialog.dismiss()
        }

    }

    private fun selCompanyNums() {
        val view = View.inflate(mContext, R.layout.dialog_companynums, null)
        val dialog = BottomDialogUtil.showBottomDialog(mContext!!, view, Gravity.BOTTOM)
        val layout = view.findViewById<LinearLayout>(R.id.layout_companynums)
        val mDismiss = view.findViewById<TextView>(R.id.dismiss)
        for (i in 0 until layout.childCount) {
            layout.getChildAt(i).setOnClickListener {
                if (it is TextView) { //子view不都是textView
                    companyNums = it.text.toString()
                    binding.creat2Tv3.text = companyNums
                    binding.creat2Next.text = "创建团队"
                    dialog.dismiss()
                }
            }
        }
        mDismiss.setOnClickListener {
            dialog.dismiss()
        }

    }

    private fun dealIcon() {
        val view = View.inflate(mContext, com.joinutech.ddbeslibrary.R.layout.dialog_personicon_bottom_layout, null)
        val dialog = BottomDialogUtil.showBottomDialog(mContext!!, view, Gravity.BOTTOM)
        val mTakePicture = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.takePicture)
        val mSelectPicture = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.selectPicture)
        val mDismiss = view.findViewById<TextView>(R.id.dismiss)
        mTakePicture.setOnClickListener {
            requestPhonePermission(ConsKeys.TAKE_PHOTO)
            dialog.dismiss()
        }
        mSelectPicture.setOnClickListener {
            requestPhonePermission()
            dialog.dismiss()
        }
        mDismiss.setOnClickListener {
            dialog.dismiss()
        }
    }

    @SuppressLint("CheckResult")
    private fun requestPhonePermission(type: String = "") {
        PictureNewHelper.beforeSelectPhoto(this, type, true, maxSelectNum = 1)

//        val perms = arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE,
//                Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.CAMERA)
//        PermissionUtils.requestPermissionActivity(this, perms, "照相机权限", {
//            doThings()
//        }, {
//            ToastUtil.show(mContext!!, "拍照/选取图片需要您授权读写及照相机权限")
//        })
    }

//    private fun doThings() {
//        if (type == ConsKeys.TAKE_PHOTO) {
//            PictureNewHelper.takePhoto(this, IMAGE_CUT_CODE)
////            PhotoCuttingUtil.takePhotoZoom2(this, IMAGE_CUT_CODE)
//        } else {
//            PictureNewHelper.selectPhoto(this, IMAGE_CUT_CODE, true)
////            PhotoCuttingUtil.selectPhotoZoom2(this, IMAGE_CUT_CODE)
//        }
//    }

    // 图片目录路径
    private var picPath = ""
    private var industry = ""

    @SuppressLint("MissingSuperCall")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                IMAGE_CUT_CODE -> {
                    if (data != null) {
                        // 图片选择结果回调
                        val selectList = PictureNewHelper.afterSelectPhotoPaths(data)
//                        val selectList = PictureSelector.obtainMultipleResult(data)
                        // 例如 LocalMedia 里面返回三种path
                        // 1.media.getPath(); 为原图path
                        // 2.media.getCutPath();为裁剪后path，需判断media.isCut();是否为true
                        // 3.media.getCompressPath();为压缩后path，需判断media.isCompressed();是否为true
                        // 如果裁剪并压缩了，已取压缩路径为准，因为是先裁剪后压缩的
                        if (selectList.isNotEmpty()) {
                            binding.creat2Logo.visibility = View.GONE
                            binding.ivLogo.visibility = View.VISIBLE
                            picPath = selectList[0]
                            if (isNotBlankAndEmpty(picPath)) {
                                Glide.with(mContext!!).load(picPath).into(binding.ivLogo)
                            }
                            FileUploadUtil.uploadFileRequest(targetId = "org", filePath = picPath,
                                    onSuccess = {
                                        logo = it
                                    },
                                    onError = {
                                        ToastUtil.show(mContext!!, "上传失败")
                                    },
                                    type = TosFileType.LOGO)
                            binding.creat2Next.text = "创建团队"
                        }
                    }
                }
            }
        } else if (requestCode == 0x11 && data != null) {
            industry = data.getStringExtra("industry") ?: ""
            binding.creat2Tv1.text = industry
            binding.creat2Next.text = "创建团队"
        }
    }


}