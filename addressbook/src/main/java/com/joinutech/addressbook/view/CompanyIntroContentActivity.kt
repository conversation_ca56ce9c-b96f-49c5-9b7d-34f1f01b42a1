package com.joinutech.addressbook.view

import android.app.Activity
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.AddressbookConstract
import com.joinutech.addressbook.databinding.ActivityCompanyIntroBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import javax.inject.Inject
import javax.inject.Named

/**
 * description ： 团队简介的修改
 * author: 黄洁如
 * date : 2019/10/21
 */
class CompanyIntroContentActivity : MyUseBindingActivity<ActivityCompanyIntroBinding>() {

    override val contentViewResId: Int = R.layout.activity_company_intro
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityCompanyIntroBinding {
        return ActivityCompanyIntroBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = false

    @Inject
    @field:Named(AddressbookUtil.ADDR_PRESENTER)
    lateinit var presenter: AddressbookConstract.AddressbookPresenter
    private var companyIntro = ""
    private var companyId = ""

    override fun initImmersion() {
        setPageTitle("团队简介")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyIntro"))) {
                companyIntro = intent.getStringExtra("companyIntro") ?: ""
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                companyId = intent.getStringExtra("companyId") ?: ""
            }
        }
    }

    override fun initView() {
        showToolBarLine()
        whiteStatusBarBlackFont()
        DaggerAddressbookComponent.builder().build().inject(this)
        if (StringUtils.isNotBlankAndEmpty(companyIntro)) {
            binding.companyIntroEdit.setText(companyIntro)
        }
    }

    override fun initLogic() {
        super.initLogic()
        binding.companyIntroSave.setOnClickListener(this)
        binding.companyIntroEdit.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (s != null) {
                    if (s.length > 200) {
                        s.delete(200, s.length)
                    }
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        })
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.companyIntroSave -> {
                if (StringUtils.isNotBlankAndEmpty(binding.companyIntroEdit.text.toString())) {
                    val map = hashMapOf<String, Any>()
                    map["profile"] = binding.companyIntroEdit.text.toString()
                    map["companyId"] = companyId
                    getLoadingDialog("", false)
                    presenter.modifyCompany(bindToLifecycle(), accessToken!!, map, {
                        dismissDialog()
                        setResult(Activity.RESULT_OK, intent)
                        finish()
                    }, {
                        dismissDialog()
                        ToastUtil.show(mContext!!, it)
                    })
                } else {
                    finish()
                }
            }
        }
    }

}