package com.joinutech.addressbook.view.tcpimpages.util;


import com.joinutech.ddbeslibrary.bean.OrgPersonBean;

import java.util.Comparator;

/**
 * 专用于按首字母排序
 *
 * <AUTHOR>
 * @fileName WaveSideBarView
 * @packageName com.nanchen.wavesidebarview
 * @date 2016/12/27  16:19
 * @github https://github.com/nanchen2251
 */

public class OrgPersonLetterComparator implements Comparator<OrgPersonBean>{

    @Override
    public int compare(OrgPersonBean personBean1, OrgPersonBean personBean2) {
        if (personBean1 == null || personBean2 == null){
            return 0;
        }
        String lhsSortLetters = personBean1.getInitial().substring(0, 1).toUpperCase();
        String rhsSortLetters = personBean2.getInitial().substring(0, 1).toUpperCase();
        return lhsSortLetters.compareTo(rhsSortLetters);
    }
}
