package com.joinutech.addressbook.view.tcpimpages.madapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.addressbook.R
import com.joinutech.ddbeslibrary.bean.PersonSearchBean
import com.joinutech.ddbeslibrary.utils.Loggerr
import com.joinutech.ddbeslibrary.utils.OnNoDoubleClickListener
import com.joinutech.ddbeslibrary.utils.XUtil
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.CircleImageView

class SearchFriendListAdapter(var context: Context, val dataList: ArrayList<PersonSearchBean>)
    : RecyclerView.Adapter<SearchFriendListAdapter.FriendViewHolder>() {
    private lateinit var listener: OnClickPersonListener
    override fun getItemCount(): Int {
        return if (dataList.isNullOrEmpty()) 0 else dataList.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FriendViewHolder {
        val itemView = LayoutInflater.from(context)
                .inflate(R.layout.item_search_person_layout, parent, false)
        return FriendViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: FriendViewHolder, position: Int) {
        holder.initView()
        if (dataList.isNotEmpty()) {
            val personBean = dataList[position]
            if (position == 0) {
                XUtil.showView(holder.personIndex)
                holder.personIndex.text = personBean.index
            } else {
                val lastOneIndex = dataList[position - 1].index
                if (lastOneIndex != personBean.index) {
                    XUtil.showView(holder.personIndex)
                    holder.personIndex.text = personBean.index
                } else {
                    XUtil.hideView(holder.personIndex)
                }
            }
            holder.personChoiceTag.isSelected = personBean.isChoosed
            holder.personName.text = personBean.name
            ImageLoaderUtils.loadImage(context, holder.personHeader, personBean.headerUrl)//显示头像
            holder.itemView.setOnClickListener(object : OnNoDoubleClickListener {
                override fun onNoDoubleClick(v: View) {
                    listener.onClick(v, position)
                }
            })
        }
    }

    fun setItemClickListener(listener: OnClickPersonListener) {
        this.listener = listener
    }

    interface OnClickPersonListener {
        fun onClick(view: View, position: Int)
    }

    class FriendViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        //        lateinit var time: TextView
        lateinit var personIndex: TextView
        lateinit var personChoiceTag: ImageView
        lateinit var personHeader: CircleImageView
        lateinit var personName: TextView


        fun initView() {
            personIndex = itemView.findViewById(R.id.tv_index)
            personChoiceTag = itemView.findViewById(R.id.iv_choice_tag)
            personHeader = itemView.findViewById(R.id.civ_header)
            personName = itemView.findViewById(R.id.tv_name)
        }

    }
}

