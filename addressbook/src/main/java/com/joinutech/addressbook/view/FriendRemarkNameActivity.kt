package com.joinutech.addressbook.view

import android.app.Activity
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.FriendRemarkNameConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.ddbes.library.im.util.FriendCacheHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.ddbes.library.im.imtcp.ConstantEventCodeUtil
import com.ddbes.library.im.imtcp.dbbean.FriendBean
import com.ddbes.library.im.imtcp.dbope.FriendDaoOpe
import com.ddbes.library.im.imtcp.dbope.SessionDaoOpe
import com.joinutech.addressbook.databinding.ActivityFriendRemarknameBinding
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.*
import io.reactivex.Observable
import io.reactivex.ObservableOnSubscribe
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import javax.inject.Inject
import javax.inject.Named

class FriendRemarkNameActivity : MyUseBindingActivity<ActivityFriendRemarknameBinding>() {

    private var nowUserId = ""

    override val contentViewResId: Int
        get() = R.layout.activity_friend_remarkname

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityFriendRemarknameBinding {
        return ActivityFriendRemarknameBinding.inflate(layoutInflater)
    }

    @Inject
    @field:Named(AddressbookUtil.REMARKNAME_PRESENTER)
    lateinit var presenter: FriendRemarkNameConstract.FriendRemarkNamePresenter
    private var remarkNameText = ""

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        setRightTitleColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color1E87F0),
                "完成", this)
        setPageTitle("修改备注")
        if (intent != null) {
            if (!intent.getStringExtra("userId").isNullOrBlank()) {
                nowUserId = intent.getStringExtra("userId") ?: ""
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("remarkName"))) {
                remarkNameText = intent.getStringExtra("remarkName") ?: ""
            }
        }
        if (nowUserId.isNullOrBlank()) {
            toastShort("用户信息缺失！")
            finish()
        }
    }

    override fun initView() {
        showToolBarLine()
        whiteStatusBarBlackFont()
        DaggerAddressbookComponent.builder().build().inject(this)
        if (StringUtils.isNotBlankAndEmpty(remarkNameText)) binding.etRemarkName.setText(remarkNameText)
    }

    override fun initLogic() {
        super.initLogic()

        binding.etRemarkName.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (s != null) {
                    val num = s.length
                    val textNumber = "$num/20"
                    if (s.length > 20) {
                        ToastUtil.showCustomToast(null, mContext!!,
                                true, "备注不得超过20字符")
                        binding.tvRemarkCount.visibility = View.VISIBLE
                        binding.tvRemarkCount.text = textNumber
                    } else {
                        if (s.isNotEmpty()) {
                            binding.tvRemarkCount.visibility = View.VISIBLE
                            binding.tvRemarkCount.text = textNumber
                        } else {
                            binding.tvRemarkCount.visibility = View.GONE
                        }
                    }
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

        })
    }

    

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            //点击完成
            com.joinutech.ddbeslibrary.R.id.tv_toolbar_right -> {
                val name = binding.etRemarkName.text?.toString()
                when {
                    name.isNullOrBlank() -> {
                        remarkFriendName("")
                    }
                    name.length <= 20 -> {
                        remarkFriendName(name)
                    }
                    else -> {
                        toastShort("昵称不能超过20个字符")
                    }
                }
            }
        }
    }

    /**
     * @param name 好友备注名称
     */
    private fun remarkFriendName(name: String) {
        //备注好友名称，tcp修改备注
        presenter.remarkFriend(bindToLifecycle(), accessToken, name, nowUserId, {
            //修改成功
            ToastUtil.show(mContext!!, "好友备注修改完成")
            val friend = FriendCacheHolder.getFriend(nowUserId)
            if (friend != null) {
                friend.userNick = name
                // 修改用户昵称后更新缓存和好友数据库信息，并通知页面刷新
                FriendCacheHolder.saveFriend(friend)
                //更新需要显示备注的地方
                    //更新会话
                val sessionBean = SessionDaoOpe.instance.querySessionByUid_AppChatId(this, userId, nowUserId)
                if (sessionBean != null) {
                    sessionBean.name = name
                    SessionDaoOpe.instance.updateSession(this, sessionBean)
                    EventBusUtils.sendEvent(EventBusEvent(ConstantEventCodeUtil.TCP_SESSION_CHANGE, sessionBean))
                }
                updateFriendInfo(name)//修改数据库中的信息
                EventBusUtils.sendEvent(EventBusEvent(ConstantEventCodeUtil.TCP_REMARK_CHANGE, name))
            }
            intent.putExtra("name", name)
            setResult(Activity.RESULT_OK, intent)
            finish()
        }, { msg ->
            ToastUtil.show(mContext!!, msg)
        })
    }

    private fun updateFriendInfo(remark: String) {
        Observable.create(ObservableOnSubscribe<FriendBean> {
            try {
                showLog("查询好友好友信息，并更新到数据库")
                val bean = FriendDaoOpe.instance.queryFriendByUserId(mContext!!, nowUserId)
                if (bean != null) {
                    bean.remark = remark
                    FriendDaoOpe.instance.updateFriend(mContext, bean)
                    it.onNext(bean)
                } else {
                    it.onError(Throwable("未找到该好友信息"))
                }
            } catch (e: Exception) {
            }
        })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe()
    }

    override fun showToolBar(): Boolean {
        return true
    }


    override fun openArouterReceive(): Boolean {
        return false
    }
}