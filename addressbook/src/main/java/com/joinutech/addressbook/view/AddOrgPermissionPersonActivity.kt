package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.app.Activity
import android.view.View
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.AddressbookConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.bean.OrgPermissionListBean
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.ORG_PERMISS_TYPE.APPROVAL_PERMISSION
import com.joinutech.ddbeslibrary.utils.ORG_PERMISS_TYPE.ATTENDANCE_PERMISSION
import com.joinutech.ddbeslibrary.utils.ORG_PERMISS_TYPE.ORG_PERMISSION
import com.joinutech.ddbeslibrary.utils.ORG_PERMISS_TYPE.TASK_PERMISSION
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import javax.inject.Inject
import javax.inject.Named


/**
 * <AUTHOR> by 黄洁如 on 2019/3/20 14:11
 * @fileName AddOrgPermissionPersonActivity
 * @describe 增加管理员
 * @org 加优科技
 */
@Deprecated("no used")
class AddOrgPermissionPersonActivity : MyUseBaseActivity() {
    override fun initImmersion() {
        TODO("Not yet implemented")
    }

    override fun initView() {
        TODO("Not yet implemented")
    }

    //    private var type: String = ""
//    private var bean: OrgPermissionListBean = OrgPermissionListBean(
//            "", "", arrayListOf(), "", "")
//    private var userList: ArrayList<String> = arrayListOf()
//    private var managerId: String = ""
//
//    @Inject
//    @field:Named(AddressbookUtil.ADDR_PRESENTER)
//    lateinit var presenter: AddressbookConstract.AddressbookPresenter
//    private var companyId: String = ""
//
//    override fun initImmersion() {
//        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
//        setRightTitle("保存", this)
//        if (intent != null) {
//            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("type"))) {
//                type = intent.getStringExtra("type") ?: ""
//            }
//            when (type) {
//                "add" -> {
//                    //新增
//                    setPageTitle("新增管理员")
//                }
//                "edit" -> {
//                    //修改
//                    setPageTitle("编辑管理员")
//                }
//            }
//            if (intent.getSerializableExtra("bean") != null) {
//                bean = intent.getSerializableExtra("bean") as OrgPermissionListBean
//                managerId = bean.managerId
//            }
//            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
//                companyId = intent.getStringExtra("companyId") ?: ""
//            }
//        }
//    }
//
//    override fun initView() {
//        whiteStatusBarBlackFont()
//        showToolBarLine()
//        when (type) {
//            "add" -> {
//                //新增
//                deletePermission.visibility = View.GONE
//            }
//            "edit" -> {
//                //修改
//                deletePermission.visibility = View.VISIBLE
//            }
//        }
//        DaggerAddressbookComponent.builder().build().inject(this)
//    }
//
//    override fun initLogic() {
//        super.initLogic()
//        if (type == "edit") {
//            //去解析上个页传过来的数据
//            initData()
//        }
//        selectedPerson.setOnClickListener(this)
//        deletePermission.setOnClickListener(this)
//        allPermissionSwitch.setOnCheckedChangeListener { _, isChecked ->
//            if (!isChecked) {
//                orgPerSwitch.setCheckedNoEvent(false)
//                attendancePerSwitch.setCheckedNoEvent(false)
//                taskPerSwitch.setCheckedNoEvent(false)
//                approvalPerSwitch.setCheckedNoEvent(false)
////                workReportPerSwitch.setCheckedNoEvent(false)
//            } else {
//                orgPerSwitch.setCheckedNoEvent(true)
//                attendancePerSwitch.setCheckedNoEvent(true)
//                taskPerSwitch.setCheckedNoEvent(true)
//                approvalPerSwitch.setCheckedNoEvent(true)
////                workReportPerSwitch.setCheckedNoEvent(true)
//            }
//        }
//        orgPerSwitch.setOnCheckedChangeListener { _, isChecked ->
//            dealSingleSwitch(isChecked)
//        }
//        taskPerSwitch.setOnCheckedChangeListener { _, isChecked ->
//            dealSingleSwitch(isChecked)
//        }
//        attendancePerSwitch.setOnCheckedChangeListener { _, isChecked ->
//            dealSingleSwitch(isChecked)
//        }
//        approvalPerSwitch.setOnCheckedChangeListener { _, isChecked ->
//            dealSingleSwitch(isChecked)
//        }
//    }
//
//    private fun dealSingleSwitch(isChecked: Boolean) {
//        if (!isChecked) {
//            allPermissionSwitch.setCheckedNoEvent(false)
//        } else {
//            if (orgPerSwitch.isChecked && taskPerSwitch.isChecked
//                    && attendancePerSwitch.isChecked && approvalPerSwitch.isChecked)
//                allPermissionSwitch.setCheckedNoEvent(true)
//        }
//    }
//
//    override fun onNoDoubleClick(v: View) {
//        when (v) {
//            tv_rightTitle -> {
//                //保存
//                saveData()
//            }
//            selectedPerson -> {
//                showLog("已经选中的人员信息为：${GsonUtil.toJson(userList)}")
//                ARouter.getInstance()
//                        .build(RouteOrg.friendListWithSearchActivity)
//                        .withSerializable("selectedList", userList)
//                        .withString("type", "givePermission")
//                        .withString("companyId", companyId)
//                        .navigation()
//            }
//            deletePermission -> {
//                delOrgItem()
//            }
//        }
//    }
//
//    @SuppressLint("SetTextI18n")
//    private fun initData() {
//        if (ORG_PERMISS_TYPE.checkSuperPermission(bean.managerPower)) {
//            //超级管理员
//            iv_per_icon.visibility = View.VISIBLE
//            editName.isFocusable = false
//            editName.isFocusableInTouchMode = false
//            editName.hint = ""
//            normalManagerSetPermissionLayout.visibility = View.VISIBLE
//            orgLikePermissionLayout.visibility = View.VISIBLE
//            allPermissionSwitch.visibility = View.GONE
//            orgPerSwitch.visibility = View.GONE
//            attendancePerSwitch.visibility = View.GONE
//            taskPerSwitch.visibility = View.GONE
//            approvalPerSwitch.visibility = View.GONE
////            workReportPerSwitch.visibility = View.GONE
//        } else {
//            iv_per_icon.visibility = View.GONE
//            normalManagerSetPermissionLayout.visibility = View.GONE
//            orgLikePermissionLayout.visibility = View.GONE
//            allPermissionSwitch.visibility = View.VISIBLE
//            orgPerSwitch.visibility = View.VISIBLE
//            attendancePerSwitch.visibility = View.VISIBLE
//            taskPerSwitch.visibility = View.VISIBLE
//            approvalPerSwitch.visibility = View.VISIBLE
////            workReportPerSwitch.visibility = View.VISIBLE
//        }
//        editName.setText(bean.managerName)
//        if (bean.userIds.isNotEmpty()) {
//            selectedText.text = "已选择${bean.userIds.size}人"
//            userList = bean.userIds as ArrayList<String>
//        } else {
//            selectedText.text = "未选择"
//        }
//        if (StringUtils.isNotBlankAndEmpty(bean.managerPower)) {
//            for (item in bean.managerPower.toCharArray()) {
//                when (item) {
//                    ORG_PERMISSION -> {
//                        orgPerSwitch.setCheckedNoEvent(true)
//                    }
//                    ATTENDANCE_PERMISSION -> {
//                        attendancePerSwitch.setCheckedNoEvent(true)
//                    }
//                    TASK_PERMISSION -> {
//                        taskPerSwitch.setCheckedNoEvent(true)
//                    }
//                    APPROVAL_PERMISSION -> {
//                        approvalPerSwitch.setCheckedNoEvent(true)
//                    }
////                    REPORT_PERMISSION -> {
////                        workReportPerSwitch.setCheckedNoEvent(true)
////                    }
//                    else -> {
//                        allPermissionSwitch.setCheckedNoEvent(false)
//                        orgPerSwitch.setCheckedNoEvent(false)
//                        attendancePerSwitch.setCheckedNoEvent(false)
//                        taskPerSwitch.setCheckedNoEvent(false)
//                        approvalPerSwitch.setCheckedNoEvent(false)
////                        workReportPerSwitch.setCheckedNoEvent(false)
//                    }
//                }
//            }
//            if (bean.managerPower.contains(ORG_PERMISSION)
//                    && bean.managerPower.contains(ATTENDANCE_PERMISSION)
//                    && bean.managerPower.contains(TASK_PERMISSION)
//                    && bean.managerPower.contains(APPROVAL_PERMISSION)
////                    && bean.managerPower.contains(REPORT_PERMISSION)
//            ) {
//                allPermissionSwitch.setCheckedNoEvent(true)
//            }
//        } else {
//            allPermissionSwitch.setCheckedNoEvent(false)
//            orgPerSwitch.setCheckedNoEvent(false)
//            attendancePerSwitch.setCheckedNoEvent(false)
//            taskPerSwitch.setCheckedNoEvent(false)
//            approvalPerSwitch.setCheckedNoEvent(false)
////            workReportPerSwitch.setCheckedNoEvent(false)
//        }
//    }
//
//    private fun delOrgItem() {
//        val dialog = MyDialog(mContext!!, 260, 115,
//                "是否确认删除此管理员？", needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0)
//        dialog.setCanceledOnTouchOutside(true)
//        dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
//            override fun clickRightBtn() {
//                getLoadingDialog("", false)
//                presenter.deleteManager(bindToLifecycle(), accessToken!!, companyId,
//                        bean.managerId, {
//                    dismissDialog()
//                    ToastUtil.show(mContext!!, "删除管理员成功")
//                    setResult(Activity.RESULT_OK)
//                    finish()
//                }, {
//                    dismissDialog()
//                    ToastUtil.show(mContext!!, it)
//                })
//            }
//        })
//        dialog.show()
//    }
//
//    private fun saveData() {
//        bean.userIds = userList
//        if (!ORG_PERMISS_TYPE.checkSuperPermission(bean.managerPower)) {
//            val managerName = editName.text.toString()
//            if (StringUtils.isEmpty(managerName)) {
//                ToastUtil.show(mContext!!, "请设置管理员名称")
//                return
//            }
//            bean.managerName = managerName
//            bean.managerPower = ""
//            if (!allPermissionSwitch.isChecked) {
//                if (orgPerSwitch.isChecked) bean.managerPower += ORG_PERMISSION
//                if (attendancePerSwitch.isChecked) bean.managerPower += ATTENDANCE_PERMISSION
//                if (taskPerSwitch.isChecked) bean.managerPower += TASK_PERMISSION
//                if (approvalPerSwitch.isChecked) bean.managerPower += APPROVAL_PERMISSION
////                if (workReportPerSwitch.isChecked) bean.managerPower += REPORT_PERMISSION
//            } else {
//                bean.managerPower += "$ORG_PERMISSION" +
//                        "$ATTENDANCE_PERMISSION" +
//                        "$TASK_PERMISSION" +
//                        "$APPROVAL_PERMISSION"
////                        +"$REPORT_PERMISSION"
//            }
//            bean.managerId = managerId
//        }
//        if (type == "add") {
//            //新增
//            getLoadingDialog("", false)
//            val map = hashMapOf<String, Any>()
//            map["userIds"] = bean.userIds
//            map["managerName"] = bean.managerName
//            map["managerPower"] = bean.managerPower
//            presenter.addManager(bindToLifecycle(), accessToken!!, companyId, map, {
//                dismissDialog()
//                ToastUtil.show(mContext!!, "新增管理员成功")
//                setResult(Activity.RESULT_OK)
//                finish()
//            }, {
//                dismissDialog()
//                ToastUtil.show(mContext!!, it)
//            })
//        } else if (type == "edit") {
//            //编辑
//            getLoadingDialog("", false)
//            val map = hashMapOf<String, Any>()
//            map["managerId"] = bean.managerId
//            map["userIds"] = bean.userIds
//            map["managerName"] = bean.managerName
//            map["managerPower"] = bean.managerPower
//            presenter.editManager(bindToLifecycle(), accessToken!!, companyId, map, {
//                dismissDialog()
//                ToastUtil.show(mContext!!, "修改管理员成功")
//                setResult(Activity.RESULT_OK)
//                finish()
//            }, {
//                dismissDialog()
//                ToastUtil.show(mContext!!, it)
//            })
//        }
//    }
//
//    @SuppressLint("SetTextI18n")
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    fun translateMsgSuccess(event: EventBusEvent<ArrayList<String>>) {
//        if (event.code == EventBusAction.SELECTEDPER_USERLIST) {
//            userList.clear()
//            if (event.data.isNullOrEmpty()) {
//                //没有选择人
//                selectedText.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.colorFFAAAAAA))
//                selectedText.text = "请选择人员"
//            } else {
//                //存在且不为空
//                selectedText.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.colorFF323232))
//                selectedText.text = "已选择${event.data!!.size}人"
//                userList = event.data!!
//            }
//        }
//    }
//
//    override val contentViewResId: Int get() = R.layout.activity_addorgper
//
//    override fun showToolBar(): Boolean {
//        return true
//    }
//
//    override fun openArouterReceive(): Boolean {
//        return false
//    }
    override val contentViewResId: Int
        get() = TODO("Not yet implemented")

    override fun showToolBar(): Boolean {
        TODO("Not yet implemented")
    }

    override fun openArouterReceive(): Boolean {
        TODO("Not yet implemented")
    }
}