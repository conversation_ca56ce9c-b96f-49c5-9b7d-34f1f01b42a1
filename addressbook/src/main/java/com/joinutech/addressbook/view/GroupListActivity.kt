package com.joinutech.addressbook.view

import android.app.Activity
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.addressbook.R
import com.joinutech.addressbook.view.fragment.GroupListFragment
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.bean.*
import com.ddbes.library.im.imtcp.dbope.FriendDaoOpe
import com.ddbes.library.im.imtcp.ConstantEventCodeUtil
import com.ddbes.library.im.imtcp.Logger
import com.ddbes.library.im.imtcp.dbbean.Message
import com.joinutech.ddbeslibrary.bean.AddOrgMsgBean
import com.joinutech.ddbeslibrary.bean.GroupCreateBean
import com.joinutech.ddbeslibrary.bean.GroupInviteUserInfoBean
import com.joinutech.ddbeslibrary.bean.UserInfo
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.ddbes.library.im.util.GroupService
import com.joinutech.addressbook.databinding.ActivityGroupListBinding
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.*
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * @Description: 群组列表
 * @Author: hjr
 * @Time: 2020/2/24 10:24
 * @packageName: com.joinutech.addressbook.view
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
@Route(path = RouteOrg.groupListActivity)
class GroupListActivity : MyUseBindingActivity<ActivityGroupListBinding>() {

    override val contentViewResId: Int = R.layout.activity_group_list
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityGroupListBinding {
        return ActivityGroupListBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true
    override fun openArouterReceive(): Boolean = true

    private var currentPosition = 0
    private lateinit var fragments: Array<GroupListFragment?>
    private lateinit var adapter: GroupListPageAdapter
    private var pageEntryType = 0
    var msgBean: Message? = null
    var companyBean: AddOrgMsgBean? = null
    var shareMap: HashMap<String, String>? = null

    override fun initImmersion() {
        setPageTitle("我的群组")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        setRightTitle("创建群组", this)
        if (intent != null) {
            pageEntryType = intent.getIntExtra("pageEntryType", 0)
            if (intent.getSerializableExtra("msgBean") != null) {
                msgBean = intent.getSerializableExtra("msgBean") as Message?
            }
            if (intent.getSerializableExtra("companyBean") != null) {
                companyBean = intent.getSerializableExtra("companyBean") as AddOrgMsgBean?
            }
            if (intent.getSerializableExtra("shareMap") != null) {
                shareMap = intent.getSerializableExtra("shareMap") as HashMap<String, String>?
            }
        }
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        fragments = arrayOfNulls(2)
        for (i in fragments.indices) {
            fragments[i] = GroupListFragment.newInstance(i, pageEntryType)
        }
        adapter = object : GroupListPageAdapter(this) {
            override fun createFragment(position: Int): Fragment {
                return fragments[position]!!
            }
        }
        
        binding.vpGroupPager.adapter = adapter
        binding.vpGroupPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                currentPosition = position
                showTab()
            }
        })
    }

    private fun showTab() {
        when (currentPosition) {
            0 -> {
                binding.tvJoinPrivate.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.main_blue))
                
                binding.lineJoinPrivate.visibility = View.VISIBLE
                binding.tvUnJoin.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color999999))
                binding.lineUnJoin.visibility = View.GONE
            }
            else -> {
                binding.tvUnJoin.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.main_blue))
                binding.lineUnJoin.visibility = View.VISIBLE
                binding.tvJoinPrivate.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color999999))
                binding.lineJoinPrivate.visibility = View.GONE
            }
        }
    }

    override fun initLogic() {
        super.initLogic()
        
        binding.clJoinLayout.setOnClickListener(this)
        binding.clUnJoinLayout.setOnClickListener(this)
        binding.search.setOnClickListener(this)
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.clJoinLayout -> {
                currentPosition = 0
                showTab()
                binding.vpGroupPager.currentItem = currentPosition
            }
            binding.clUnJoinLayout -> {
                currentPosition = 1
                showTab()
                binding.vpGroupPager.currentItem = currentPosition
            }
            tv_rightTitle -> {//tcp点击创建群组
                val allFriendList =
                        FriendDaoOpe.instance.getAllFriendList(mContext)
                if (!allFriendList.isNullOrEmpty()) {
                    val toList = allFriendList.map {
                        if (StringUtils.isNotBlankAndEmpty(it.remark)) {
                            FriendSelectBean(userId=it.userId, name=it.remark, avatar = it.avatar,logout = it.logout)
                        } else FriendSelectBean(userId=it.userId, name = it.name, avatar = it.avatar,logout = it.logout)
                    }.toMutableList() as ArrayList
                    ARouter.getInstance()
                            .build(RouteOrg.SelectSearchListWithBottomShowActivity)
                            .withString("title", "请选择成员")
                            .withInt("maxSelectNum", 20)
                            .withBoolean("isNeedPersonInfo", true)
                            .withSerializable("outPersonList", toList)
                            .navigation(this@GroupListActivity,
                                    IM_VC_SELECT_PERSON)
                }else{
                    ToastUtil.show(this,"你还没有好友，无法创建群组")
                }
            }
            binding.search -> {
                val intent = Intent(mContext!!, GroupListSearchActivity::class.java)
                startActivity(intent)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun updateGroupName(event: EventBusEvent<String>) {
        when (event.code) {
            ConsKeys.GROUP_INFO_UPDATE,//群信息刷新，刷新群组列表
            EventBusAction.REFRESH_GROUP_LIST -> {//群名称更改或则退出群组，刷新群组列表
                (fragments[0] as GroupListFragment).getListData(true)
            }
        }
    }

    @Suppress("UNCHECKED_CAST")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == IM_VC_SELECT_PERSON && resultCode == Activity.RESULT_OK
                && data != null) {
            val hashSet =
                    data.getSerializableExtra("selectUserIds") as HashSet<UserInfo>
            val selectUserIds: HashSet<String> = hashSet.map { it.userId }.toHashSet()
            val selectUserInfo = hashSet.map {
                GroupInviteUserInfoBean(it.userId, it.userName)
            }.toList()
            var selectUserName = ""
            if (StringUtils.isNotBlankAndEmpty(data.getStringExtra("selectUserName")))
                selectUserName = data.getStringExtra("selectUserName") ?: ""
            if (!selectUserIds.isNullOrEmpty()) {
                createGroupEvent(
                        selectUserIds.filter { it != userId }.toList(),
                        selectUserName, selectUserInfo)
            }
        }
    }

    private fun createGroupEvent(selectUserIds: List<String>, groupName: String,
                                 selectUserInfo: List<GroupInviteUserInfoBean>) {
        getLoadingDialog("", false)
        GroupService.createGroup(selectUserIds, groupName, accessToken!!)
                .compose(bindToLifecycle())
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<GroupCreateBean>() {
                    override fun onError(ex: ApiException) {
                        dismissDialog()
                        toastShort(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: GroupCreateBean?) {
                        dismissDialog()
                        if (t != null) {
                            //tcp群组创建成功,刷新群组列表
                            Logger.i("---执行---创建群组---", "---成功----")
                            EventBusUtils.sendEvent(EventBusEvent(ConstantEventCodeUtil.TCP_REFRESH_GROUP_LIST, "刷新群组列表"))

//                            MsgTranslator.createGroupSuccessMsg(//群组列表创建群组
//                                    mContext!!, userId!!, t.groupId, t.logo, selectUserName, selectUserInfo
//                            )
                            ARouter.getInstance()
                                    .build(RouteIm.groupChat)
                                    .withString("targetId", t.groupId)
                                    .withString("targetLogo", t.logo)
                                    .withString("targetName", "群聊(${selectUserIds.size + 1})")
                                    .navigation()
                            finish()
                        }
                    }

                })
    }


    abstract class GroupListPageAdapter(fragmentActivity: FragmentActivity)
        : FragmentStateAdapter(fragmentActivity) {
        override fun getItemCount(): Int {
            return 2
        }
    }
}