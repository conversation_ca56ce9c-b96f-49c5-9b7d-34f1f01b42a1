package com.joinutech.addressbook.view

import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.AsyncTask
import android.os.Handler
import android.os.Message
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.Observer

import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.addressbook.R
import com.joinutech.addressbook.databinding.ActivitySelectvisitmethodBinding
import com.joinutech.addressbook.viewModel.SelectVisitViewModel
import com.joinutech.common.base.WEB_URL_HOST
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.QrCodeData
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.WxBitmapUtil.buildTransaction
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage
import com.tencent.mm.opensdk.modelmsg.WXWebpageObject
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import java.io.BufferedOutputStream
import java.io.File
import java.io.FileOutputStream
import java.lang.ref.WeakReference
import java.net.URL

/**邀请人员页面
 *
 * 分享到单聊
 * 分享到群组
 * */
class SelectVisitMethodActivity : MyUseBindingActivity<ActivitySelectvisitmethodBinding>() {

    override val contentViewResId: Int = R.layout.activity_selectvisitmethod
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivitySelectvisitmethodBinding {
        return ActivitySelectvisitmethodBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true
    override fun openArouterReceive(): Boolean = false

    private var content: String = ""
    private var wxContent: String = ""

    //WX
    private lateinit var api: IWXAPI
    private var companyName: String = ""
    private var companyId: String = ""
    private var companyLogo = ""
    private var openUrl = ""
    var msg1 = 0x111
    private lateinit var viewModel: SelectVisitViewModel
    private val mHandler = MyStaticHandler(WeakReference(this))

    /**
     * 静态内部类handler
     */
    class MyStaticHandler(ref: WeakReference<SelectVisitMethodActivity>) : Handler() {

        private var activity: SelectVisitMethodActivity = ref.get()!!

        override fun handleMessage(msg: Message) {
            when (msg.what) {
                activity.msg1 -> {
                    if (activity.bitmap != null) {
                        activity.sendToWeiXin("担当办公", activity.openUrl, activity.wxContent,
                                activity.bitmap, 0)
                    }
                }
            }
        }
    }

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        setPageTitle("选择邀请方式")
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyName"))) {
                companyName = intent.getStringExtra("companyName") ?: ""
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                companyId = intent.getStringExtra("companyId") ?: ""
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyLogo"))) {
                companyLogo = intent.getStringExtra("companyLogo") ?: ""
            }
        }
    }

    override fun initView() {
        viewModel = getModel(SelectVisitViewModel::class.java)
        whiteStatusBarBlackFont()
        wxContent = "$userName" +
                "在担当办公中邀请您加入团队【$companyName】"
    }

    override fun initLogic() {
        super.initLogic()

        initWx()
        binding.qrCodeLayout.setOnClickListener(this)
        
        binding.clMsgLayout.setOnClickListener(this)
        binding.wxLayout.setOnClickListener(this)
        binding.addOrgLayout.setOnClickListener(this)
        getObservable()
        if (userId.isNullOrBlank() || accessToken.isNullOrBlank()) return
        getLoadingDialog("", true)
        val url = if (WEB_URL_HOST.contains("https://")) {
            WEB_URL_HOST.replace("https://", "")
        } else {
            WEB_URL_HOST.replace("http://", "")
        }
        viewModel.getMsgShortUrl(bindToLifecycle(), accessToken!!, companyId, userId!!, url)
    }

    private fun getObservable() {
        viewModel.selectVisitObservable.observe(this, Observer {
            dismissDialog()
            openUrl = it
            content = "$userName" +
                    "在担当办公中邀请您加入团队【$companyName】。$it"
        })
        viewModel.selectVisitErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, "发送邀请失败")
        })
    }

    private var bitmap: Bitmap? = null
    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.qrCodeLayout -> {
                val member = QrCodeData(
                        title = "二维码邀请",
                        headImg = companyLogo,
                        name = companyName,
                        userId = companyId,
                        content = "o.$companyId",
                        hint = "使用担当办公扫一扫，快速加入团队",
                        savable = true)
                ARouter.getInstance()
                        .build(RoutePersonal.orgQrCode)
                        .withSerializable("qrCodeData", member)
                        .navigation()
            }
            binding.clMsgLayout -> {//点击通过短信邀请
                if (StringUtils.isNotBlankAndEmpty(content)) {
                    sendMsg()
                }
            }
            binding.wxLayout -> {//点击邀请微信好友
                val imagePath = ""
                if (StringUtils.isNotBlankAndEmpty(openUrl)) {
                    if (StringUtils.isNotBlankAndEmpty(imagePath)) {
                        DownImage().execute(imagePath)
                    } else {
                        sendToWeiXin("担当办公", openUrl, wxContent,
                                WxBitmapUtil.getDefaultImage(mContext!!), 0)
                    }
                }
            }
            binding.addOrgLayout -> {//tcp点击通过聊天邀请
                addOrgMsg()
            }
        }
    }

    //转发群聊消息
   /* private fun addOrgMsg() {
        val dialog: AlertDialog?
        val view = View.inflate(mContext,
                R.layout.dialog_imagepreviewdeal_bottom_layout, null)
        dialog = BottomDialogUtil.showBottomDialog(mContext!!, view, Gravity.BOTTOM)
        val transformFriend = view.findViewById<TextView>(R.id.savePhoto)
        val transformGroup = view.findViewById<TextView>(R.id.takePicture)
        val selectPicture = view.findViewById<TextView>(R.id.selectPicture)
        val cancel = view.findViewById<TextView>(R.id.cancel)
        val line2 = view.findViewById<View>(R.id.line2)
//        transformGroup.text = "转发到群组"
//        transformFriend.text = "转发给担当好友"
        transformGroup.text = "邀请群组成员"
        transformFriend.text = "邀请担当好友"
        selectPicture.visibility = View.GONE
        line2.visibility = View.GONE
        //转发到好友
        transformFriend.setOnClickListener {
            dialog.dismiss()
            ARouter.getInstance()
                    .build(RouteOrg.friendListWithSearchActivity)
                    .withString("type", "addOrgMsg")
                    .withString("companyName", companyName)
                    .withString("companyId", companyId)
                    .withString("companyLogo", companyLogo)
                    .navigation()
        }
        transformGroup.setOnClickListener {
            dialog.dismiss()
            val bean = AddOrgMsgBean(companyId, companyLogo, companyName)
            ARouter.getInstance()
                    .build(RouteOrg.groupListActivity)//团队邀请到群组 3
                    .withInt("pageEntryType", 3)
                    .withSerializable("companyBean", bean)
                    .navigation()
        }
        cancel.setOnClickListener {
            dialog.dismiss()
        }
        dialog.show()
    }*/
    //tcp点击通过聊天邀请
    private fun addOrgMsg() {
            ARouter.getInstance()
                    .build(RouteOrg.searchFriendToTranslateActivity)
                    .withString("type", "addOrgMsg")
                    .withString("companyName", companyName)
                    .withString("companyId", companyId)
                    .withString("companyLogo", companyLogo)
                    .navigation()
    }

    @SuppressLint("StaticFieldLeak")
    internal inner class DownImage : AsyncTask<String, Void, String>() {
        private lateinit var shareImgfile: File

        override fun doInBackground(params: Array<String>): String? {
            val bitmap: Bitmap?
            try {
                //加载一个网络图片
                val stream = URL(params[0]).openStream()
                bitmap = BitmapFactory.decodeStream(stream)
                shareImgfile = File(mContext?.externalCacheDir!!.absolutePath,
                        System.currentTimeMillis().toString() + "icon_share.jpg")
                if (shareImgfile.exists()) {
                    shareImgfile.deleteOnExit()
                }
                val bos = BufferedOutputStream(FileOutputStream(shareImgfile))
                bitmap!!.compress(Bitmap.CompressFormat.JPEG, 10, bos)
                bos.flush()
                bos.close()
            } catch (e: Exception) {
                e.printStackTrace()
                return null
            }

            return shareImgfile.absolutePath
        }

        override fun onPostExecute(imageUrl: String) {
            if (StringUtils.isNotBlankAndEmpty(imageUrl)) {
                shareImage(imageUrl)
            } else {
                toastShort("分享有误")
            }

        }
    }

    private fun shareImage(imageUrl: String) {
        Thread {
            bitmap = JavaUtils.getimage(imageUrl)
            if (bitmap != null) {
                //压缩完成
                val msg = Message()
                msg.what = msg1
                mHandler.sendMessage(msg)
            }
        }.start()
    }

    private fun initWx() {
        api = WXAPIFactory.createWXAPI(this@SelectVisitMethodActivity, APP_ID_WX, true)
        // 将该app注册到微信
        api.registerApp(APP_ID_WX)
    }

    /**
     * @param title       分享的标题
     * @param openUrl     点击分享item打开的网页地址url
     * @param description 网页的描述
     * @param icon        分享item的图片
     * @param requestCode 0表示为分享到微信好友  1表示为分享到朋友圈 2表示微信收藏
     */
    fun sendToWeiXin(title: String, openUrl: String, description: String,
                     icon: Bitmap?, requestCode: Int) {
//        初始化一个WXWebpageObject对象，填写url
        val webpage = WXWebpageObject()
        webpage.webpageUrl = openUrl
        //Y用WXWebpageObject对象初始化一个WXMediaMessage对象，填写标题、描述
        val msg = WXMediaMessage(webpage)
        msg.title = title//网页标题
        msg.description = description//网页描述
        if (icon != null) {
            msg.thumbData = JavaUtils.bmpToByteArray(icon, true)
            icon.recycle()
        }
        //构建一个Req
        val req = SendMessageToWX.Req()
        req.transaction = buildTransaction("webpage")
        req.message = msg
        req.scene = requestCode
        api.sendReq(req)
//        toastShort("发送微信邀请成功")
    }

    private fun sendMsg() {
        val perms = arrayOf(Manifest.permission.SEND_SMS)
        PermissionUtils.requestPermissionActivity(this, perms, "发送短信", {
            sendMsgContent()
        }, {
            ToastUtil.show(mContext!!, "发送短信需要短信权限")
        })
    }

    private fun sendMsgContent() {
        val sendIntent = Intent(Intent.ACTION_SENDTO, Uri.parse("smsto:"))
        sendIntent.putExtra("sms_body", content)
        startActivity(sendIntent)
    }

    override fun onDestroy() {
        super.onDestroy()
        mHandler.removeCallbacksAndMessages(null)
    }

}