package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.AddressbookConstract
import com.joinutech.addressbook.databinding.ActivityOrangizationinfoBinding
import com.joinutech.addressbook.databinding.ActivityOrgannameBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.common.util.CompanyHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.CompanyTypeBean
import com.joinutech.ddbeslibrary.bean.UserInfo
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.activity.WithToolBarImagePreviewActivity
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import javax.inject.Inject
import javax.inject.Named

/**
 *<AUTHOR>
 *@date 2018/11/22
 * 团队资料
 */
class OrganizationInfoActivity : MyUseBindingActivity<ActivityOrangizationinfoBinding>() {

    override val contentViewResId: Int = R.layout.activity_orangizationinfo
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityOrangizationinfoBinding {
        return ActivityOrangizationinfoBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = false

    @Inject
    @field:Named(AddressbookUtil.ADDR_PRESENTER)
    lateinit var presenter: AddressbookConstract.AddressbookPresenter

    var companyId: String = "-2"//-2说明没有带过来id

    /**团队信息*/
    private var companyBean: WorkStationBean? = null

    private var isManager = 0
    private var orgPermission: Boolean = false
    private var isCreator: Boolean = false

    //    private var companyName: String = ""
    private var companyType: String = ""
    private var companyIndustry: String = ""
    private var companyNums: String = ""

    //    private var imgPath: String = ""
    private var profile = ""
    private var officialWebsite = ""
    private var linkManPhone = ""
    private var linkManMail = ""

    override fun initImmersion() {
        setPageTitle("团队资料")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        if (intent != null) {
            if (intent.getSerializableExtra("companyBean") != null) {
                companyBean = intent.getSerializableExtra("companyBean") as WorkStationBean
                companyId = companyBean!!.companyId
            }
            orgPermission = intent.getBooleanExtra("orgPermission", false)
            isCreator = intent.getBooleanExtra("isCreator", false)
        }
        setRightImage(com.joinutech.ddbeslibrary.R.drawable.icon_blackdotmore, View.OnClickListener {
            orgDealPop()
        })
    }

    @SuppressLint("InflateParams")
    private fun orgDealPop() {
        val mAddPopup: PopupWindow
        val mAddPopupLayout = LayoutInflater.from(mContext)
                .inflate(com.joinutech.ddbeslibrary.R.layout.pop_single_button_toolbar_down, null)
        mAddPopup = PopupWindow(mAddPopupLayout, ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT)
        mAddPopup.isOutsideTouchable = true
        mAddPopup.isFocusable = true
        mAddPopup.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        val dealTv = mAddPopupLayout.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.showTv)
        if (isCreator) {
            dealTv.text = "解散团队"
            dealTv.setOnClickListener(object : OnNoDoubleClickListener {
                override fun onNoDoubleClick(v: View) {
                    mAddPopup.dismiss()
                    val intent1 = Intent(mContext!!, DisbandOrganActivity::class.java)
                    intent1.putExtra("companyBean", companyBean)
                    startActivityForResult(intent1, 0x13)
                }

            })
        } else {
            dealTv.text = "退出团队"
            dealTv.setOnClickListener(object : OnNoDoubleClickListener {
                override fun onNoDoubleClick(v: View) {
                    mAddPopup.dismiss()
                    showDisband()
                }

            })
        }
        mAddPopup.showAtLocation(baseView!!, Gravity.NO_GRAVITY, 0, 0)
        mAddPopupLayout?.setOnClickListener {
            mAddPopup.dismiss()
        }
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        DaggerAddressbookComponent.builder().build().inject(this)
        if (orgPermission) {
            isManager = 1
        }

        binding.shortInfoEdit.setOnClickListener(this)
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.info_r1 -> {
                val intent1 = Intent(mContext!!, WithToolBarImagePreviewActivity::class.java)
                intent1.putExtra("isManager", isManager)
                intent1.putExtra("imgPath", companyBean?.logo)
                intent1.putExtra("type", "org")
                intent1.putExtra("companyId", companyId)
                startActivityForResult(intent1, UPDATECOMPANYLOGO)
            }
            R.id.info_r2 -> {  //名称
                val intent1 = Intent(mContext!!, OrganRenameActivity::class.java)
                intent1.putExtra("companyName", companyBean?.name)//默认能修改的都是管理员
                intent1.putExtra("companyId", companyId)
                startActivityForResult(intent1, 0x11)
            }
            R.id.info_r3 -> {//行业
                val intent1 = Intent(mContext!!, IndustryActivity::class.java)
                intent1.putExtra("isManager", 1)//默认能修改的都是管理员
                intent1.putExtra("companyId", companyId)
                startActivityForResult(intent1, 0x22)
            }
            R.id.info_r4 -> {//类型
                presenter.getCompanyTypes(bindToLifecycle(), accessToken!!, {
                    selCompanyType(it)
                }, { ToastUtil.show(mContext!!, it) })
            }
            R.id.info_r5 -> {//人数
                selCompanyNums()
            }
            R.id.companyIntroLayout -> {
                val intent = Intent(mContext!!, CompanyIntroContentActivity::class.java)
                intent.putExtra("companyIntro", profile)
                intent.putExtra("companyId", companyId)
                startActivityForResult(intent, COMPANY_INTRO_CHANGE)
            }
            R.id.companyWebLayout -> {
                ARouter.getInstance()
                        .build(RoutePersonal.personNameOrEmailActivity)
                        .withString("type", "companyWeb")
                        .withString("companyId", companyId)
                        .withString("personName", officialWebsite)
                        .navigation(this, COMPANY_INTRO_CHANGE)
            }
            R.id.companyPhoneLayout -> {
                ARouter.getInstance()
                        .build(RoutePersonal.personNameOrEmailActivity)
                        .withString("type", "companyPhone")
                        .withString("companyId", companyId)
                        .withString("personName", linkManPhone)
                        .navigation(this, COMPANY_INTRO_CHANGE)
            }
            R.id.companyEmailLayout -> {
                ARouter.getInstance()
                        .build(RoutePersonal.personNameOrEmailActivity)
                        .withString("type", "companyEmail")
                        .withString("companyId", companyId)
                        .withString("personName", linkManMail)
                        .navigation(this, COMPANY_INTRO_CHANGE)
            }
            R.id.shortInfoEdit->{
                val dialog = MyDialog(mContext, 309, 171,
                    "", needBtnConfirm = false, needBtnCancel = true, bgResourceId = 0)
                dialog.setContentText(profileString)
                dialog.show()
            }
        }
    }

    override fun initLogic() {
        super.initLogic()
        getOrgInfo()
        if (isCreator) binding.infoR2.setOnClickListener(this)
        if (isManager == 1) {

            binding.infoR1.setOnClickListener(this)
            binding.infoR3 .setOnClickListener(this)
            binding.infoR4 .setOnClickListener(this)
            binding.infoR5.setOnClickListener(this)
            binding.companyIntroLayout.setOnClickListener(this)
            binding.companyWebLayout.setOnClickListener(this)
            binding.companyPhoneLayout.setOnClickListener(this)
            binding.companyEmailLayout.setOnClickListener(this)
        }
    }

    @SuppressLint("MissingSuperCall")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == 0x11) {
                // 修改公司名后回调
                if (data != null) {
                    val companyName = data.getStringExtra("name") ?: ""
                    binding.infoName.text = companyName
                    // 团队名称更新后更新本地记录信息，可以不刷新团队列表，事件通知团队更新即可 companyId

                    // 更新变量
                    companyBean = companyBean?.copy(name = companyName)

                    // 更新缓存
                    var comInfo = CompanyHolder.getOrgInfo(companyId)
                    if (comInfo != null) {
                        comInfo.userName = companyName
                    } else {
                        comInfo = UserInfo(
                                userId = companyId,
                                userIcon = companyBean?.logo ?: "",
                                userName = companyName)
                    }
                    CompanyHolder.setOrgInfo(comInfo)

                    // 更新团队列表缓存
                    val allCompanies = CompanyHolder.getAllNormalOrg()
                    var target = allCompanies.find { it.companyId == companyId }
                    if (target != null) {
                        allCompanies.remove(target)
                        target = target.copy(name = companyName)
                        allCompanies.add(target)
                        CompanyHolder.saveAllNormalOrg(allCompanies)
                    } else {
                        val allCooper = CompanyHolder.getCooperationOrg()
                        target = allCooper.find { it.companyId == companyId }
                        if (target != null) {
                            allCooper.remove(target)
                            target = target.copy(logo = companyName)
                            allCooper.add(target)
                            CompanyHolder.saveCooperationOrg(allCooper)
                        }
                    }
                    EventBusUtils.sendEvent(EventBusEvent(ConsKeys.ORG_INFO_UPDATE, companyId))
                }
            } else if (requestCode == UPDATECOMPANYLOGO) {
                //修改了团队logo
                if (data != null) {
                    val orgLogo = data.getStringExtra("logo") ?: ""
                    showLog("接收到修改公司logo成功事件，更新缓存和UI $orgLogo")
                    ImageLoaderUtils.loadImage(this, binding.infoLogo, orgLogo)
                    // 更新变量
                    companyBean = companyBean?.copy(logo = orgLogo)
                    // 更新缓存
                    var comInfo = CompanyHolder.getOrgInfo(companyId)
                    if (comInfo != null) {
                        comInfo.userIcon = orgLogo
                    } else {
                        comInfo = UserInfo(companyId, orgLogo, companyBean?.name ?: "")
                    }
                    CompanyHolder.setOrgInfo(comInfo)
                    // 更新团队列表缓存
                    val allCompanies = CompanyHolder.getAllNormalOrg()
                    var target = allCompanies.find { it.companyId == companyId }
                    if (target != null) {
                        allCompanies.remove(target)
                        target = target.copy(logo = orgLogo)
                        allCompanies.add(target)
                        CompanyHolder.saveAllNormalOrg(allCompanies)
                    } else {
                        val allCooper = CompanyHolder.getCooperationOrg()
                        target = allCooper.find { it.companyId == companyId }
                        if (target != null) {
                            allCooper.remove(target)
                            target = target.copy(logo = orgLogo)
                            allCooper.add(target)
                            CompanyHolder.saveCooperationOrg(allCooper)
                        }
                    }
                    EventBusUtils.sendEvent(EventBusEvent(ConsKeys.ORG_INFO_UPDATE, companyId))
                }
            } else if (requestCode == 0x22) {
                // 修改公司所属行业后回调
                if (data != null) {
                    
                    binding.infoIndustry.text = data.getStringExtra("industry") ?: ""
                }
            } else if (requestCode == COMPANY_INTRO_CHANGE) {
                //修改了团队简介
                getOrgInfo()
            } else if (requestCode == 0x13) {
                // 解散团队回调
                finish()
            }
        }
    }

    /**获取团队详细设置信息*/
    var profileString=""
    private fun getOrgInfo() {
        getLoadingDialog("", false)
        presenter.queryCompanyById(bindToLifecycle(), accessToken!!, companyId, {
            dismissDialog()
//            if (StringUtils.isNotBlankAndEmpty(it.logo) && it.logo.startsWith("http")) {
            ImageLoaderUtils.loadImage(mContext!!, binding.infoLogo, it.logo ?:"")
//            } else {
//                if (StringUtils.isNotBlankAndEmpty(it.logo)) {
//                    ImageLoaderUtils.loadImage(mContext!!, info_logo, FileStorage.WEB_IMAGE_BASE_URL + it.logo)
//                }
//            }
             binding.infoName.text = it.name
            if (StringUtils.isNotBlankAndEmpty(it.industry)) {
                binding.infoIndustry.text = it.industry
            } else {
                binding.infoIndustry.text = "未设置"
            }
            if (StringUtils.isNotBlankAndEmpty(it.type)) {
                binding.infoType.text = it.type
            } else {
                binding.infoType.text = "未设置"
            }
            if (StringUtils.isNotBlankAndEmpty(it.scale)) {
                binding.infoNums.text = it.scale
            } else {
                binding.infoNums.text = "未设置"
            }
            if (StringUtils.isNotBlankAndEmpty(it.officialWebsite)) {
                binding.webEdit.text = it.officialWebsite
            } else {
                 binding.webEdit.text = "未填写"
            }
            if (StringUtils.isNotBlankAndEmpty(it.linkManPhone)) {
                 binding.phoneEdit.text = it.linkManPhone
            } else {
                binding.phoneEdit.text = "未填写"
            }
            if (StringUtils.isNotBlankAndEmpty(it.linkManMail)) {

                binding.emailEdit.text = it.linkManMail
            } else {
                binding.emailEdit.text = "未填写"
            }
            if (StringUtils.isNotBlankAndEmpty(it.profile)) {
                binding.shortInfoEdit.text = it.profile
                profileString=it.profile
            } else {
                binding.shortInfoEdit.text = "未填写"
            }
            companyIndustry = it.industry
            companyType = it.type
            companyNums = it.scale
            val logo = if (it.logo == null ) "" else it.logo
            companyBean = companyBean?.copy(logo = logo ?:"")
        }, {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun selCompanyType(list: List<CompanyTypeBean>) {
        val view = View.inflate(mContext, com.joinutech.ddbeslibrary.R.layout.dialog_bottom_layout, null)
        val layout = view.findViewById<LinearLayout>(com.joinutech.ddbeslibrary.R.id.dialog_bottom_layout)
        val dialog = BottomDialogUtil.showBottomDialog(mContext!!, view, Gravity.BOTTOM)
        for (i in list.indices) {
            val item = View.inflate(mContext, com.joinutech.ddbeslibrary.R.layout.item_dialog_bottom, null)
            val tv = item.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.dialog_bottom_tv)
            val line = item.findViewById<View>(com.joinutech.ddbeslibrary.R.id.dialog_bottom_line)
            tv.text = list[i].name
            if (i == list.size - 1) {
                line.visibility = View.GONE
            }
            item.tag = i
            item.setOnClickListener {
                val companyType1 = list[item.tag as Int].name
                if (companyType1 != companyType) {
                    val map = hashMapOf<String, Any>()
                    map["type"] = companyType1
                    map["companyId"] = companyId
                    presenter.modifyCompany(bindToLifecycle(), accessToken!!, map, {
                        binding.infoType.text = companyType1
                    }, {
                        ToastUtil.show(mContext!!, it)
                    })
                }
                dialog.dismiss()
            }
            layout.addView(item, LinearLayout.LayoutParams.MATCH_PARENT, DeviceUtil.dip2px(mContext!!, 50f))
        }
        val mDismiss = view.findViewById<TextView>(R.id.dismiss)

        mDismiss.setOnClickListener {
            dialog.dismiss()
        }

    }

    private fun selCompanyNums() {
        val view = View.inflate(mContext, R.layout.dialog_companynums, null)
        val dialog = BottomDialogUtil.showBottomDialog(mContext!!, view, Gravity.BOTTOM)
        val layout = view.findViewById<LinearLayout>(R.id.layout_companynums)
        val mDismiss = view.findViewById<TextView>(R.id.dismiss)
        for (i in 0 until layout.childCount) {
            layout.getChildAt(i).setOnClickListener {
                val companyNums1 = (it as TextView).text.toString()
                if (companyNums1 != companyNums) {
                    val map = hashMapOf<String, Any>()
                    map["scale"] = companyNums1
                    map["companyId"] = companyId
                    presenter.modifyCompany(bindToLifecycle(), accessToken!!, map, {
                        binding.infoNums.text = companyNums1
                    }, { error ->
                        ToastUtil.show(mContext!!, error)
                    })
                }
                dialog.dismiss()
            }
        }
        mDismiss.setOnClickListener {
            dialog.dismiss()
        }

    }

    private var disbandCompanyId = ""

    //团队已解散
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun orgDissolved(event: EventBusEvent<String>) {
        if (event.code == EventBusAction.Event_DISSOLVED_ORG || event.code == EventBusAction.Event_LEAVE_ORG) {
            if (StringUtils.isNotBlankAndEmpty(event.data) &&
                    StringUtils.isNotBlankAndEmpty(companyId) && event.data == companyId) {
                if (disbandCompanyId != event.data) {
                    disbandCompanyId = companyId
                    val dialog = if (event.code == EventBusAction.Event_DISSOLVED_ORG) {
                        MyDialog(mContext!!, 0, 0, "该团队已解散",
                                needBtnConfirm = true, needBtnCancel = false, bgResourceId = 0)
                    } else {
                        MyDialog(mContext!!, 0, 0, "你已被请离该团队",
                                needBtnConfirm = true, needBtnCancel = false, bgResourceId = 0)
                    }
                    dialog.setBtnRightText("好的")
                    dialog.setCanceledOnTouchOutside(false)
                    dialog.setCancelable(false)
                    dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
                        override fun clickRightBtn() {
                            finish()
                        }
                    })
                    dialog.show()
                }
            }
        }
    }

    private fun showDisband() {
        val view = View.inflate(mContext, R.layout.dialog_disband, null)
        val confirm = view.findViewById<TextView>(R.id.confirm_disband)
        val cancel = view.findViewById<TextView>(R.id.cancle_disband)
        val title1 = view.findViewById<TextView>(R.id.title_disband)
        val content = view.findViewById<TextView>(R.id.content_disband)
        val content2 = view.findViewById<TextView>(R.id.content2_disband)
        val content3 = view.findViewById<TextView>(R.id.content3_disband)
        content2.visibility = View.GONE
        content3.visibility = View.GONE
        title1.text = "您确认要退出团队吗？"
        content.text = "退出团队后，将无法使用和本团队相关的办公功能"
        val dialog = BottomDialogUtil.showBottomDialog(mContext!!, view, Gravity.CENTER)
        confirm.setOnClickListener {
            dialog.dismiss()
            quitCompany()
        }
        cancel.setOnClickListener {
            dialog.dismiss()
        }
    }

    private fun quitCompany() {
        showLoading()
        presenter.quitCompany(bindToLifecycle(), accessToken!!, companyId,
                onSuccess = {
                    hideLoading()
                    val allCompanies = CompanyHolder.getAllNormalOrg()
                    val org1 = allCompanies.find { it.companyId == companyId }
                    if (org1 != null) {
                        allCompanies.remove(org1)
                        CompanyHolder.saveAllNormalOrg(allCompanies)
                    } else {
                        val allCooper = CompanyHolder.getCooperationOrg()
                        val org2 = allCooper.find { it.companyId == companyId }
                        if (org2 != null) {
                            allCooper.remove(org2)
                            CompanyHolder.saveCooperationOrg(allCooper)
                        }
                    }
                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.Event_LEAVE_ORG, companyId))
//                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST, "-3"))
                    finish()
                },
                onError = {
                    hideLoading()
                    ToastUtil.show(mContext!!, it)
                })
    }
}