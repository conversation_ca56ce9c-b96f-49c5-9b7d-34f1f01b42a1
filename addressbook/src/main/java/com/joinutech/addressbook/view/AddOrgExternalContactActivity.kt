package com.joinutech.addressbook.view

import android.app.Activity
import android.content.Intent
import android.text.style.ForegroundColorSpan
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.lifecycle.Observer

import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.ExternalContactLevelSelectAdapter
import com.joinutech.addressbook.adapter.ExternalContactTypeSelectAdapter
import com.joinutech.addressbook.databinding.ActivityAddOrgExternalContactBinding
import com.joinutech.addressbook.viewModel.AddOrgExternalContactViewModel
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.ExternalContactDetailBean
import com.joinutech.ddbeslibrary.bean.FriendSimpleBean
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.CommonUtils.verifyEmailFormat

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/11/28 19:56
 * @packageName: com.joinutech.addressbook.view
 * @Company: JoinuTech
 */
class AddOrgExternalContactActivity : MyUseBindingActivity<ActivityAddOrgExternalContactBinding>() {

    override val contentViewResId: Int = R.layout.activity_add_org_external_contact
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityAddOrgExternalContactBinding {
        return ActivityAddOrgExternalContactBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = false

    private var externalContactId = ""
    private var externalContactType = ""
    private var externalContactLevel = ""
    private var externalContactName = ""
    private var externalContactPhone = ""
    private var externalContactEmail = ""
    private var externalContactRemark = ""
    private var currentCompanyId = ""
    private var typeIndex = 0
    private var bean: ExternalContactDetailBean? = null
    private lateinit var viewModel: AddOrgExternalContactViewModel

    override fun initImmersion() {
        setLeftTitle("取消")
        setRightTitleColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.main_blue), "保存",
                object : OnNoDoubleClickListener {
                    override fun onNoDoubleClick(v: View) {
                        saveExternalContact()
                    }

                })
        if (intent != null) {
            if (intent.getSerializableExtra("bean") != null) {
                bean = intent.getSerializableExtra("bean") as ExternalContactDetailBean?
                externalContactId = bean?.userId!!
                setPageTitle("修改外部协作人员")
            } else {
                setPageTitle("添加外部协作人员")
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("currentCompanyId"))) {
                currentCompanyId = intent.getStringExtra("currentCompanyId") ?: ""
            }
            typeIndex = intent.getIntExtra("typeIndex", 0)
        }
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        binding.tvNameTitle.text = Spanny()
                .append("姓名 ")
                .append("*", ForegroundColorSpan(
                        CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.red)))
        binding.tvPhoneTitle.text = Spanny()
                .append("手机号 ")
                .append("*", ForegroundColorSpan(
                        CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.red)))
        binding.kindLeftText.text = Spanny()
                .append("类型 ")
                .append("*", ForegroundColorSpan(
                        CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.red)))
        binding.levelLeftText.text = Spanny()
                .append("重要级别 ")
                .append("*", ForegroundColorSpan(
                        CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.red)))
        viewModel = getModel(AddOrgExternalContactViewModel::class.java)
    }

    override fun initLogic() {
        if (StringUtils.isNotBlankAndEmpty(externalContactId)) {
            binding.delete.visibility = View.VISIBLE
            externalContactType = bean?.type.toString()
            externalContactEmail = bean?.email.toString()
            externalContactPhone = bean?.mobile.toString()
            externalContactRemark = bean?.desc.toString()
            externalContactLevel = bean?.level.toString()
            externalContactName = bean?.externalContactsName.toString()
            binding.nameEdit.setText(externalContactName)
            binding.phoneEdit.setText(externalContactPhone)
            binding.remarkEdit.setText(externalContactRemark)
            binding.emailEdit.setText(externalContactEmail)
            if (bean?.active == 0) {
                //已激活的外部协作人员不能修改手机号了
                binding.phoneEdit.isFocusable = false
                binding.phoneEdit.isFocusableInTouchMode = false
                binding.phoneEdit.setTextColor(CommonUtils.getColor(mContext!!, R.color.color9a9a9a))
                binding.sendSmsLayout.visibility = View.GONE
            }
        } else {
            binding.delete.visibility = View.GONE
        }
        binding.nameRightIv.setOnClickListener(this)
        binding.kindRightIv.setOnClickListener(this)
        binding.kindRightTv.setOnClickListener(this)
        binding.levelRightIv.setOnClickListener(this)
        binding.levelRightTv.setOnClickListener(this)
        binding.levelRightIcon.setOnClickListener(this)
        binding.delete.setOnClickListener(this)
        setTypeShow()
        setLevelShow()
        getObserver()
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.nameRightIv -> {//点击通讯录图标
                selectAddType()
            }
            binding.kindRightIv, binding.kindRightTv -> {
                selectType()
            }
            binding.levelRightIv, binding.levelRightTv, binding.levelRightIcon -> {
                selectLevel()
            }
            binding.delete -> {
                delete()
            }
        }
    }

    private fun getObserver() {
        viewModel.updateOrgExternalContactSuccessObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, "修改外部协作人员成功")
            intent.putExtra("typeIndex", typeIndex)
            setResult(Activity.RESULT_OK, intent)
            EventBusUtils.sendEvent(EventBusEvent(
                    EventBusAction.Event_REFRESH_EXTERNAL_CONTACT_LIST, typeIndex))
            finish()
        })
        viewModel.addOrgExternalContactSuccessObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, "添加外部协作人员成功")
            intent.putExtra("typeIndex", typeIndex)
            setResult(Activity.RESULT_OK, intent)
            EventBusUtils.sendEvent(EventBusEvent(
                    EventBusAction.Event_REFRESH_EXTERNAL_CONTACT_LIST, typeIndex))
            finish()
        })
        viewModel.addOrgExternalContactErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
        viewModel.updateOrgExternalContactErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it.message)
            if (it.code == 4001) {
                //前一个页也需要关闭
                intent.putExtra("typeIndex", typeIndex)
                setResult(Activity.RESULT_OK, intent)
                finish()
            }
        })
        viewModel.deleteOrgExternalContactSuccessObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, "删除外部协作人员成功")
            val intent = Intent()
            intent.putExtra("delete", true)
            setResult(Activity.RESULT_OK, intent)
            EventBusUtils.sendEvent(EventBusEvent(
                    EventBusAction.Event_REFRESH_EXTERNAL_CONTACT_LIST, typeIndex))
            finish()
        })
        viewModel.deleteOrgExternalContactErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun selectAddType() {
        val dialog = object : DialogHolder(this,
            com.joinutech.ddbeslibrary.R.layout.dialog_white_top_corner, Gravity.BOTTOM) {
            override fun bindView(dialogView: View) {
                val cancel = dialogView.findViewById<TextView>(R.id.cancel)
                cancel.setOnClickListener {
                    dialog?.dismiss()
                }
                val typeListRv = dialogView.findViewById<RecyclerView>(R.id.rv_list)
                typeListRv.layoutManager = LinearLayoutManager(mContext)
                val titleList = arrayListOf<String>()
                titleList.add("从手机通讯录添加")
                titleList.add("从担当好友添加")
                val adapter = ExternalContactTypeSelectAdapter(mContext!!, titleList)
                typeListRv.adapter = adapter
                adapter.setItemClickListener(object : ItemClickListener {
                    override fun onItemClick(position: Int) {
                        // 添加外部协作人，通讯录[PhoneContactActivity]和好友列表[SearchResultActivity]添加
                        if (position == 0) {
                            val intent = Intent(mContext!!, PhoneContactActivity::class.java)
                            intent.putExtra("isAddExternalContact", true)
                            startActivityForResult(intent, ADD_EXTERNAL_CONTACT_FROM_PHONE)
                        } else {
                            ARouter.getInstance()
                                    .build(RouteOrg.searchResultActivity)
                                    .withString("type", "addExternalContact")
                                    .navigation(this@AddOrgExternalContactActivity,
                                            ADD_EXTERNAL_CONTACT_FROM_FRIEND_LIST)
                        }
                        dialog?.dismiss()
                    }

                })
            }

        }
        dialog.initView()
        dialog.show(true)
    }

    private fun delete() {
        val dialog = object : DialogHolder(this,
                R.layout.dialog_delete_external_contact, Gravity.CENTER) {
            override fun bindView(dialogView: View) {
                val cancel = dialogView.findViewById<TextView>(R.id.cancel)
                cancel.setOnClickListener {
                    dialog?.dismiss()
                }
                val delete = dialogView.findViewById<TextView>(R.id.delete)
                delete.setOnClickListener {
                    dialog?.dismiss()
                    getLoadingDialog("", true)
                    viewModel.deleteExternalContact(bindToLifecycle(), currentCompanyId,
                            externalContactId, accessToken!!)
                }
            }
        }
        dialog.initView()
        dialog.show(true)
    }

    private fun selectLevel() {
        val dialog = object : DialogHolder(this,
            com.joinutech.ddbeslibrary.R.layout.dialog_white_top_corner, Gravity.BOTTOM) {
            override fun bindView(dialogView: View) {
                val cancel = dialogView.findViewById<TextView>(R.id.cancel)
                cancel.setOnClickListener {
                    dialog?.dismiss()
                }
                val typeListRv = dialogView.findViewById<RecyclerView>(R.id.rv_list)
                typeListRv.layoutManager = LinearLayoutManager(mContext)
                val titleList = arrayListOf("1", "2", "3")
                val adapter = ExternalContactLevelSelectAdapter(mContext!!, titleList)
                typeListRv.adapter = adapter
                adapter.setItemClickListener(object : ItemClickListener {
                    override fun onItemClick(position: Int) {
                        externalContactLevel = adapter.mData[position]
                        setLevelShow()
                        dialog?.dismiss()
                    }

                })
            }

        }
        dialog.initView()
        dialog.show(true)
    }

    private fun setLevelShow() {
        binding.levelRightIcon.visibility = View.VISIBLE
        when (externalContactLevel) {
            "1" -> {
                binding.levelRightIcon.setImageResource(R.drawable.icon_external_contact_level_yellow)
                binding.levelRightTv.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.colorFAA62F))
                binding.levelRightTv.text = "非常重要"
            }
            "2" -> {
                binding.levelRightIcon.setImageResource(R.drawable.icon_external_contact_level_blue)
                binding.levelRightTv.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.main_blue))
                binding.levelRightTv.text = "重要"
            }
            "3" -> {
                binding.levelRightIcon.setImageResource(R.drawable.icon_external_contact_level_grey)
                binding.levelRightTv.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color7EB0D6))
                binding.levelRightTv.text = "一般"
            }
            else -> {
                binding.levelRightIcon.visibility = View.GONE
            }
        }
    }

    private fun selectType() {
        val dialog = object : DialogHolder(this,
            com.joinutech.ddbeslibrary.R.layout.dialog_white_top_corner, Gravity.BOTTOM) {
            override fun bindView(dialogView: View) {
                val cancel = dialogView.findViewById<TextView>(R.id.cancel)
                cancel.setOnClickListener {
                    dialog?.dismiss()
                }
                val typeListRv = dialogView.findViewById<RecyclerView>(R.id.rv_list)
                typeListRv.layoutManager = LinearLayoutManager(mContext)
                val titleList = arrayListOf<String>()
                titleList.add("客户")
                titleList.add("渠道商")
                titleList.add("供应商")
                titleList.add("合作伙伴")
                titleList.add("其他类型")
                val adapter = ExternalContactTypeSelectAdapter(mContext!!, titleList)
                typeListRv.adapter = adapter
                adapter.setItemClickListener(object : ItemClickListener {
                    override fun onItemClick(position: Int) {
                        val shoeTypeText = adapter.mData[position]
                        externalContactType = when (shoeTypeText) {
                            "客户" -> "1"
                            "渠道商" -> "2"
                            "供应商" -> "3"
                            "合作伙伴" -> "4"
                            "其他类型" -> "5"
                            else -> ""
                        }
                        binding.kindRightTv.text = shoeTypeText
                        dialog?.dismiss()
                    }

                })
            }

        }
        dialog.initView()
        dialog.show(true)
    }

    private fun setTypeShow() {
        val shoeTypeText = when (externalContactType) {
            "1" -> "客户"
            "2" -> "渠道商"
            "3" -> "供应商"
            "4" -> "合作伙伴"
            "5" -> "其他类型"
            else -> "未选择"
        }
        binding.kindRightTv.text = shoeTypeText
    }

    private fun saveExternalContact() {
        val data = hashMapOf<String, Any>()
        externalContactName = binding.nameEdit.text.toString()
        if (StringUtils.isEmpty(externalContactName)) {
            ToastUtil.show(mContext!!, "请输入团队备注姓名")
            return
        }
        data["name"] = externalContactName
        externalContactPhone = binding.phoneEdit.text.toString()
        if (StringUtils.isEmpty(externalContactPhone) ||
                !StringUtils.isPhoneNumber(externalContactPhone)) {
            ToastUtil.show(mContext!!, "请输入正确的手机号")
            return
        }
        data["phone"] = externalContactPhone
        externalContactEmail = binding.emailEdit.text.toString()
        if (StringUtils.isNotBlankAndEmpty(externalContactEmail) &&
                !verifyEmailFormat(externalContactEmail)) {
            ToastUtil.show(mContext!!, "请输入正确的邮箱")
            return
        }
        data["email"] = externalContactEmail
        if (StringUtils.isEmpty(externalContactType)) {
            ToastUtil.show(mContext!!, "请选择外部联系人类型")
            return
        }
        data["type"] = externalContactType
        if (StringUtils.isEmpty(externalContactLevel)) {
            ToastUtil.show(mContext!!, "请选择外部联系人重要级别")
            return
        }
        data["level"] = externalContactLevel
        externalContactRemark = binding.remarkEdit.text.toString()
        data["desc"] = externalContactRemark
        data["isSend"] = if (binding.sendMsgSwitch.isChecked) {
            //需要发激活邀请
            1
        } else {
            0
        }
        data["companyId"] = currentCompanyId
        getLoadingDialog("", true)
        if (StringUtils.isNotBlankAndEmpty(externalContactId)) {
            //编辑
            data["userId"] = externalContactId
            viewModel.updateExternalContact(bindToLifecycle(), data, accessToken!!)
        } else {
            //新增
            viewModel.addExternalContact(bindToLifecycle(), data, accessToken!!)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                ADD_EXTERNAL_CONTACT_FROM_PHONE -> {
                    if (data?.getSerializableExtra("bean") != null) {
                        val bean = data.getSerializableExtra("bean") as FriendSimpleBean
                        binding.nameEdit.setText(bean.name)
                        binding.phoneEdit.setText(bean.phone)
                    }
                }
                ADD_EXTERNAL_CONTACT_FROM_FRIEND_LIST -> {
                    if (data?.getSerializableExtra("bean") != null) {
                        val bean = data.getSerializableExtra("bean") as FriendSimpleBean
                        binding.nameEdit.setText(bean.name)
                        binding.phoneEdit.setText(bean.phone)
                        binding.emailEdit.setText(bean.email)
                    }
                }
            }
        }
    }

}