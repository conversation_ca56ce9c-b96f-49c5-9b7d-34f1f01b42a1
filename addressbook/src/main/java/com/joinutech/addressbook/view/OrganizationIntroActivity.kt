package com.joinutech.addressbook.view

import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.Observer
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.OrganizationIntroConstact
import com.joinutech.addressbook.databinding.ActivityOrgintroLayoutBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.common.util.CompanyHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.org.GlobalCompanyHolder
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import javax.inject.Inject
import javax.inject.Named

/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName:
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/8/25 17:12
 * @Desc: 团队介绍页面 团队详情页面
 */
@Route(path = RouteOrg.orgIntroActivity)
class OrganizationIntroActivity : MyUseBindingActivity<ActivityOrgintroLayoutBinding>() {

    override val contentViewResId: Int = R.layout.activity_orgintro_layout
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityOrgintroLayoutBinding {
        return ActivityOrgintroLayoutBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = true

    private var status: Int = 0

    @Autowired
    @JvmField
    var companyId: String = ""

    @Inject
    @field:Named(AddressbookUtil.ORGINTRO_PRESENTER)
    lateinit var presenter: OrganizationIntroConstact.OrganizationIntroPresenter

    override fun initImmersion() {
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                companyId = intent.getStringExtra("companyId") ?: ""
            }
        }
        setPageTitle("我的")
        commonToolBarId!!.visibility = View.GONE
        mImmersionBar!!.statusBarDarkFont(false).init()
        showToolBarLine()
    }

    override fun initView() {
        DaggerAddressbookComponent.builder().build().inject(this)
        setShowEmptyView(false)
    }

    override fun initLogic() {
        super.initLogic()

         binding.btnBackArrow.setOnClickListener(this)
        getOrgIntro()
        binding.companyIntroTextMore.setOnClickListener(this)
        binding.joinOrg.setOnClickListener(this)

        GlobalCompanyHolder.companyUpdateResult.observe(this, Observer {
            showLog("团队信息页面 -- 接收团队信息更新")
            if (CompanyHolder.getTotalCompanyIdMap().containsKey(companyId)) {
                getOrgIntro()
            }
        })

    }

    private fun getOrgIntro() {
        getLoadingDialog("", false)
        presenter.getOrganizationInfo(bindToLifecycle(), accessToken!!, companyId, {
            dismissDialog()
            setShowEmptyView(false)
            status = 0
            if (it.rejectJoin == 1) {
                //拒绝加入
                status = 1
            }
            if (it.isJoin == 1) {
                //已加入了该团队
                status = 2
            }
            ImageLoaderUtils.loadImage(mContext!!, binding.logo, it.logo ?:"")
            binding.name.text = it.name
            if (StringUtils.isNotBlankAndEmpty(it.type)) {
                binding.tvCooperType.text = it.type
            }

            if (StringUtils.isNotBlankAndEmpty(it.industry)) {
                binding.tvProfessionName.text = it.industry
            }

            if (StringUtils.isNotBlankAndEmpty(it.scale)) {
                binding.tvMemberCount.text = it.scale
            }
            if (StringUtils.isNotBlankAndEmpty(it.profile)) {
                binding.companyIntroText.text = it.profile
            }
            if (StringUtils.isNotBlankAndEmpty(it.officialWebsite)) {
                binding.webTv.text = it.officialWebsite
            }
            if (StringUtils.isNotBlankAndEmpty(it.linkManPhone)) {
                binding.phoneTv.text = it.linkManPhone
            }
            if (StringUtils.isNotBlankAndEmpty(it.linkManMail)) {
                binding.emailTv.text = it.linkManMail
            }
            if (StringUtils.isNotBlankAndEmpty(it.profile)
                    && it.profile.length >= 30) {
                binding.companyIntroTextMore.visibility = View.VISIBLE
            } else {
                binding.companyIntroTextMore.visibility = View.GONE
            }
            dealStatus()
        }, {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun dealStatus() {
        when (status) {
            0 -> {
                //正常
                binding.joinOrg.visibility = View.VISIBLE
                binding.textReject.visibility = View.INVISIBLE
            }
            1 -> {
                //拒绝
                binding.joinOrg.visibility = View.INVISIBLE
                binding.textReject.visibility = View.VISIBLE
            }
            2 -> {
                //已加入本团队
                binding.joinOrg.visibility = View.INVISIBLE
                binding.textReject.visibility = View.VISIBLE
                binding.textReject.text = "你已加入该企业"
            }
        }
    }

    var isOpenText=false
    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.joinOrg -> {
                // TODO: 2020/8/10 11:08 去掉申请加入团队验证，直接发起团队申请 <---陈方硕
                val intent = Intent(this@OrganizationIntroActivity,
                        VerifyApplicationActivity::class.java)
                intent.putExtra("companyId", companyId)
                startActivity(intent)
            }
            binding.btnBackArrow -> {
                finish()
            }
            binding.companyIntroTextMore->{
                if (isOpenText==false) {
                    binding.companyIntroText.maxLines=15
                    isOpenText=true
                    binding.companyIntroTextMore.text="收起"
                } else {
                    binding.companyIntroText.maxLines=2
                    binding.companyIntroTextMore.text="查看更多"
                    isOpenText=false
                }
            }
        }
    }

    override fun openEventBus(): Boolean {
        return false
    }

//    @Subscribe(threadMode = ThreadMode.MAIN)
//    fun onEvent(event: EventBusEvent<String>) {
//        showLog("收到String消息事件 ${event.data.toString()}")
//        if (event.code == EventBusAction.CURRENT_COMPANY_UPDATE) {
//            if (CompanyHolder.getAllCompanyMap().containsKey(companyId)) {
//                getOrgIntro()
//            }
//        }
//    }
}