package com.joinutech.addressbook.view.tcpimpages.madapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.addressbook.R
import com.joinutech.ddbeslibrary.bean.OrgPersonBean
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.CircleImageView
import java.util.*

class ChoosedPersonListAdapter(var context: Context, val dataList: ArrayList<OrgPersonBean>) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {


    override fun getItemCount(): Int {
        return if (dataList.isNullOrEmpty()) 0 else dataList.size
    }

    override fun onCreateViewHolder(parent: <PERSON>Group, viewType: Int): RecyclerView.ViewHolder {
        val itemView = LayoutInflater.from(context)
            .inflate(com.joinutech.ddbeslibrary.R.layout.item_selected_person_layout, parent, false)
        return OrgPersonViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val personHolder = holder as OrgPersonViewHolder
        if (dataList.isNotEmpty()) {
            val personBean = dataList[position]

            ImageLoaderUtils.loadImage(context, holder.headerIv, personBean.headimg)
            personHolder.nameTv.text = personBean.name
        }
    }



    class OrgPersonViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val headerIv = itemView.findViewById<CircleImageView>(R.id.header_civ)
        val nameTv = itemView.findViewById<TextView>(R.id.name_tv)

    }
}

