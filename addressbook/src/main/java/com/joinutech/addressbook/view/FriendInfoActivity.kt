package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.library.im.ImService
import com.ddbes.library.im.imtcp.ConstantEventCodeUtil
import com.ddbes.library.im.imtcp.Logger
import com.ddbes.library.im.imtcp.dbbean.FriendBean
import com.ddbes.library.im.imtcp.dbope.FriendDaoOpe
import com.ddbes.library.im.imtcp.dbope.MessageDaoOpe
import com.ddbes.library.im.imtcp.dbope.SessionDaoOpe
import com.ddbes.library.im.imtcp.imservice.dialogutil.ShowDialogUtil
import com.ddbes.library.im.imtcp.imservice.msghelper.SendMsgHelper
import com.ddbes.library.im.netutil.ImNetUtil
import com.ddbes.library.im.util.FriendCacheHolder
import com.joinu.im.protobuf.MsgBean
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.FriendInfoConstract
import com.joinutech.addressbook.databinding.ActivityFriendinfoLayoutBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.common.util.CacheDataHolder
import com.joinutech.common.util.CommonListPop
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.OrgMemberInfoBean
import com.joinutech.ddbeslibrary.bean.UserInfo
import com.joinutech.ddbeslibrary.utils.BottomDialogUtil
import com.joinutech.ddbeslibrary.utils.CHANGE_MEMBER_DEP
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.IMEvent
import com.joinutech.ddbeslibrary.utils.MSG_LIST_INFO_CHANGE
import com.joinutech.ddbeslibrary.utils.MyDialog
import com.joinutech.ddbeslibrary.utils.ParseJsonData.parseJsonAny
import com.joinutech.ddbeslibrary.utils.REMARKNAME
import com.joinutech.ddbeslibrary.utils.RouteIm
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.RouteTask
import com.joinutech.ddbeslibrary.utils.ScreenUtils
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.callPhoneNumber
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.utils.sendEmailInfo
import com.joinutech.ddbeslibrary.utils.toastShort
import com.marktoo.lib.cachedweb.LogUtil
import io.reactivex.Observable
import io.reactivex.ObservableOnSubscribe
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
//import kotlinx.android.synthetic.main.activity_friendinfo_layout.*
//import kotlinx.android.synthetic.main.item_complain_layout.*
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import javax.inject.Inject
import javax.inject.Named

/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName: FriendInfoActivity
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/9/15 10:34
 * @Desc: //TODO 人员信息、好友信息页面
 */
@Route(path = RouteOrg.friendInfoActivity)
class FriendInfoActivity : MyUseBindingActivity<ActivityFriendinfoLayoutBinding>() {
    /**人员id*/
    var targetUserId: String = ""
//
//    /**0 扫码查询（） 1 好友信息 2 单向好友 3 普通-群成员信息 4 团队-群成员信息，需要有companyId */
//    var type: Int = 0

    /**团队成员时需要此参数，成员信息和人员信息不同接口提供，所以必须*/
    var companyId: String = ""

    /**从哪种方式进来的添加好友 1.手机号进来传 2.群组 3.团队 4.合作团队(暂无此类型使用)*/
    private var addFriendType: Int = 0

    override val contentViewResId: Int = R.layout.activity_friendinfo_layout
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityFriendinfoLayoutBinding {
        return ActivityFriendinfoLayoutBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = false

    override fun openArouterReceive(): Boolean = true

    @Inject
    @field:Named(AddressbookUtil.ORGMEMBERINFO_PRESENTER)
    lateinit var presenter: FriendInfoConstract.FriendInfoPresenter

    private var personName: String = ""
    var depName: String = ""
    private var depId: String = "0"
    private var orgCreatorId: String = ""
    private var orgPermission = false

    //进入页面的类型
    // addFriend 添加好友（包含不确定好友关系的查看信息）
    //  friend 好友信息
    //  org 有团队的信息
    var type: String = ""

//    /**查看好友信息时，是否关注好友关系信息 用于区分好友信息更新时，是否重新获取好友关系展示*/
//    private var careRelation: Boolean = false

//    @Autowired
//    @JvmField
//    var fromImmsg: Boolean = false

    private var remarkName = ""
    private var isFriend = false
    private var isLogout = 0
    private var nowUserName = ""
    private var nowUserLogo = ""

    private var sessionId = ""
    private var mySessionId = ""

    override fun initImmersion() {
        mImmersionBar?.titleBar(R.id.cl_top_bar)
                ?.statusBarDarkFont(false, 0f)?.init()
        dealIntentContent()
        //获取自己的sessionID
        userId?.let { uid ->
            mySessionId = UserHolder.getSessionIdByChatId(uid)
            if (mySessionId.isBlank()) {
                ImNetUtil.getImSessionId(bindToLifecycle(), uid, {
                    mySessionId = it
                })//在获取好友信息后有保存对方的imid
            }
        }


    }

    var reTryTag = 0
    private fun changeRightTitleContent() {
        //如果对方已经注销了，就只显示删除好友，且把打电话和发邮件都禁用
        if (isLogout == 1) {
            //已经注销了
            popItemMap.clear()
            popItemMap.add("删除好友")
            initPop(popItemMap)
            binding.clCallLayout.setOnClickListener(null)
            binding.clAddFriend.setOnClickListener(null)
            binding.clSendEmailLayout.setOnClickListener(null)
            return
        }

        reTryTag++
        // 查看对方是否在我的黑名单里
        LogUtil.showLog("检查用户是否在黑名单中--3-->")
        ImNetUtil.checkTargetIdIsInBlackList(bindToLifecycle(), userId!!, targetUserId,
                onSuccess = {
                    //tcp右上角添加黑名单选项
                    if (isFriend == true && it == false) {
                        Logger.i("----验证title--", "------1---")
                        popItemMap.clear()
                        popItemMap.add("修改备注")
                        popItemMap.add("删除好友")
                        popItemMap.add("加入黑名单")
                    } else if (isFriend == true && it == true) {
                        Logger.i("----验证title--", "------2---")
                        popItemMap.clear()
                        popItemMap.add("修改备注")
                        popItemMap.add("删除好友")
                        popItemMap.add("移出黑名单")
                    } else if (isFriend != true && it == false) {
                        Logger.i("----验证title--", "------3---")
                        popItemMap.clear()
                        popItemMap.add("加入黑名单")
                    } else {
                        Logger.i("----验证title--", "------4---")
                        popItemMap.clear()
                        popItemMap.add("移出黑名单")
                    }
                    if (popItemMap.size != 2) {
                        initPop(popItemMap)
                    } else if (reTryTag <= 2) {
                        changeRightTitleContent()
                    }

                },
                onFailer = {
                    Logger.i("----执行--检查targetId是否在userId的黑名单列表中--", "------失败---")
                })
    }

    //接收pc端黑名单变化的指令通知ImPushMsg
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun receivePcMsgEvent(event: EventBusEvent<String>) {
        if (event.data != null && event.data is String) {
            when (event.code) {
                ConstantEventCodeUtil.TCP_BLACK_LIST_ADD -> {//加入黑名单
                    event.data.let { msg ->
                        if (msg == sessionId) {
                            if (msgPop != null) {
                                msgPop?.hidePop()
                            }
                            //加入黑名单
                            popItemMap.remove("加入黑名单")
                            popItemMap.add("移出黑名单")
                            initPop(popItemMap)
                        }
                    }
                }
                ConstantEventCodeUtil.TCP_BLACK_LIST_REMOVE -> {
                    event.data.let { msg ->
                        if (msg == sessionId) {
                            if (msgPop != null) {
                                msgPop?.hidePop()
                            }
                            //移出黑名单
                            popItemMap.remove("移出黑名单")
                            popItemMap.add("加入黑名单")
                            initPop(popItemMap)
                        }
                    }
                }
            }
        }
    }

    private fun dealIntentContent() {
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("name"))) {
                personName = intent.getStringExtra("name") ?: ""
            }
            if (intent.getIntExtra("enterType", 0) != 0) {
                addFriendType = intent.getIntExtra("enterType", 0)
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("depId"))) {
                depId = intent.getStringExtra("depId") ?: ""
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("createId"))) {
                orgCreatorId = intent.getStringExtra("createId") ?: ""
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("depName"))) {
                depName = intent.getStringExtra("depName") ?: ""
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("userId"))) {
                targetUserId = intent.getStringExtra("userId") ?: ""
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                companyId = intent.getStringExtra("companyId") ?: ""
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("type"))) {
                type = intent.getStringExtra("type") ?: ""
            }
            orgPermission = intent.getBooleanExtra("orgPermission", false)

            if (targetUserId == userId) {
                setPageName("个人信息")
            } else {
                updateTitle()
            }
        }
    }

    private fun updateTitle() {
        when (type) {
            "friend" -> {
                setPageName("好友信息")
            }
            "org" -> {
                setPageName("团队成员信息")
            }
            else -> {
                //不确定关系
                setPageName("人员信息")
            }
        }
    }

    private fun setPageName(title: String) {
        binding.tvPageTitle.text = title
    }

    override fun initView() {
        DaggerAddressbookComponent.builder().build().inject(this)
        initType()
        binding.clFriendBottomLayout.visibility = View.GONE
//        initPop(popItemMap)
        binding.ivBack.setOnClickListener(this)
        binding.ivRight.setOnClickListener(this)
    }

    override fun initLogic() {
        super.initLogic()
        getLoadingDialog("获取个人资料中", false)
        updateUserInfo()// 首次加载数据
        binding.clOrgInfoLayout.setOnClickListener(this)
        binding.clAddFriend.setOnClickListener(this)
        binding.clSendLayout.setOnClickListener(this)
        binding.clCallLayout.setOnClickListener(this)
        binding.clSendEmailLayout.setOnClickListener(this)
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.iv_back -> {
                onBackPressed()
            }
            R.id.iv_right -> {
                //团队成员更多选项
                if ((type == "org") && ((orgCreatorId == userId) || (orgCreatorId != userId && orgPermission))) {
                    // 团队查看成员，当前用户是创建者或者拥有团队管理权限
                    leaveMember()
                } else {
//                    dealFriendMore()
                    if (msgPop == null) {
                        initPop(popItemMap)
                    }
                    msgPop!!.show(findViewById(R.id.iv_right))
                }
            }
            R.id.cl_org_info_layout -> {
                //修改所在部门
                changeMemberDep()
            }
            //点击发送
            R.id.cl_send_layout -> {
                //发送新建会话的消息
                val map = linkedMapOf<String, Any>()
                map.put("handleType", 4)
                map.put("handleId", sessionId)
                map.put("appChatId", targetUserId)
                map.put("name", if (StringUtils.isNotBlankAndEmpty(remarkName)) {
                    remarkName
                } else {
                    nowUserName
                })
                map.put("headerUrl", nowUserLogo)
                val jsonData = GsonUtil.toJson(map)
                val uuid = SendMsgHelper.getUUID()
                val newSessionMsg = MsgBean.ExtMsg.newBuilder()
                        .setExt1(jsonData)
                        .build()
                val sendMsg = SendMsgHelper.getC2CMsgRequestMsgFromMutilSynchronization(mySessionId, newSessionMsg, uuid)
                val isSucceed = ImService.sendMsg(sendMsg)

                //是团队创建者且查看团队中好友的信息,发消息
                ARouter.getInstance()
                        .build(RouteIm.singleChat)
                        .withString("targetId", targetUserId)
                        .withString("targetName",
                                if (StringUtils.isNotBlankAndEmpty(remarkName)) {
                                    remarkName
                                } else {
                                    nowUserName
                                })
                        .withString("targetLogo", nowUserLogo)
                        .navigation()
            }
            //点击添加好友
            R.id.cl_add_friend -> {
                addAction()
            }
            R.id.cl_call_layout -> {
                if (StringUtils.isPhoneNumber(binding.tvPhone.text.toString())) {
                    callPhoneNumber(binding.tvPhone.text?.toString() ?: "")
                }
            }
            R.id.cl_send_email_layout -> {
                if (StringUtils.isNotBlankAndEmpty(binding.tvEmail.text.toString())
                        && binding.tvEmail.text.toString() != "未填写") {
                    sendEmailInfo(binding.tvEmail.text.toString())
                } else {
                    toastShort("该用户未设置邮箱")
                }
            }
        }
    }

    /**
     * 处理添加操作
     *
     * 验证 添加好友 次数等信息，通过后进入好友申请页面
     * */
    //添加好友
    private fun addAction() {
        showLoading()
        presenter.volidate(bindToLifecycle(), accessToken!!, targetUserId, addFriendType,
                onSuccess = {
                    hideLoading()
                    //是团队创建者且查看团队中非好友的信息,添加好友
                    val intent = Intent(mContext, VerifyApplicationActivity::class.java)
                    intent.putExtra("companyId", targetUserId)
                    intent.putExtra("type", "addFriend")
                    startActivity(intent)
                },
                onError = {
                    when (it) {
                        //今天加入次数过多
                        "好友申请上限" -> {
                            hideLoading()
                            ToastUtil.showCustomToast(null, mContext!!, true,
                                    "你今天添加该用户次数过多，请明天再试")
                        }
                        // 1405 == code 单向好友时，添加对方为好友，对方还是你的好友时，返回1405，直接添加成功
                        "已经是好友了" -> {
                            fun isFriendAlready() {
                                hideLoading()
                                ToastUtil.show(mContext!!, it)
//                                EventBusUtils.sendStickyEvent(EventBusEvent(EventBusAction.REFRESH_FRIENDLIST, ""))
                                ARouter.getInstance()
                                        .build(RouteIm.singleChat)
                                        .withString("targetId", targetUserId)
                                        .withString("targetName",
                                                if (StringUtils.isNotBlankAndEmpty(remarkName)) {
                                                    remarkName
                                                } else {
                                                    nowUserName
                                                })
                                        .withString("targetLogo", nowUserLogo)
                                        .navigation()
                            }
// 更新好友数据后跳转聊天页面
                            ImNetUtil.getAppFriendList(this, bindToLifecycle(), bindToLifecycle(), onSuccess = {
                                isFriendAlready()
                            }, onFailer = {
                            })
                        }
                        "好友添加成功！" -> {
                            ImNetUtil.getAppFriendList(this, bindToLifecycle(), bindToLifecycle(), onSuccess = {
                                hideLoading()
                                ToastUtil.show(mContext!!, "好友添加成功")
                                updateUserInfo()
                            }, onFailer = {
                                hideLoading()

                            })
                        }
                        else -> ToastUtil.show(mContext!!, it)
                    }
                })
    }

    @SuppressLint("SetTextI18n")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        fun dealChangeDepReturnResult(data: Intent?) {
            ToastUtil.showCustomToast(null, mContext!!, true, "已变更所在部门")
            if (data != null && StringUtils.isNotBlankAndEmpty(data.getStringExtra("depName"))) {
                depName = data.getStringExtra("depName") ?: ""
                binding.tvDeptName.text = depName
            }
            if (data != null && data.getStringExtra("depId") != "0") {
                depId = data.getStringExtra("depId") ?: ""
            }
            EventBusUtils.sendEvent(
                    EventBusEvent(EventBusAction.REFRESH_ORGCHARTLIST, ""))
        }
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                CHANGE_MEMBER_DEP -> {
                    dealChangeDepReturnResult(data)
                }
                REMARKNAME -> {
                    if (data != null &&
                            StringUtils.isNotBlankAndEmpty(data.getStringExtra("name"))) {
                        remarkName = data.getStringExtra("name")!!
                        if (StringUtils.isNotBlankAndEmpty(remarkName)) {
                            //如果有备注名时名字处显示备注名
                            binding.tvName.text = remarkName
                            binding.tvRemarkName.visibility = View.VISIBLE
                            binding.tvRemarkName.text = "用户名：$nowUserName"
                        } else {
                            binding.tvName.text = nowUserName
                            binding.tvRemarkName.visibility = View.GONE
                        }
                        val friendInfo = FriendBean()
                        personData?.let {
                            friendInfo.avatar = it.headimg ?: ""
                            friendInfo.remark = remarkName
                            friendInfo.name = it.name ?: ""
                            friendInfo.userId = targetUserId
                            friendInfo.logout = it.logout
                            FriendCacheHolder.updateFriendInfo(this, friendInfo,
                                    onResult = { }, onError = {})
                            val userInfoNew = UserInfo(targetUserId, it.headimg ?: "",
                                    it.name ?: "", remarkName)
                            FriendCacheHolder.saveFriend(userInfoNew)
                        }
                    }
                }
            }
        }
    }

    /**查看团队成员部门信息*/
    private fun changeMemberDep() {
        val intent = Intent(mContext, OrgDepartmentActivity::class.java)
        intent.putExtra("depName", depName)
        intent.putExtra("type", "memberInfo")
        intent.putExtra("name", personData?.name ?: "")
        intent.putExtra("name", personName)
        intent.putExtra("companyId", companyId)
        intent.putExtra("depId", depId)
        val userIds = arrayListOf(targetUserId)
        intent.putExtra("userIds", userIds)
        startActivityForResult(intent, CHANGE_MEMBER_DEP)
    }

    private var msgPop: CommonListPop<String>? = null

    // project 创建项目 task 添加任务 "" 创建项目 添加任务
    private val popItemMap = arrayListOf("修改备注", "删除好友")

    private fun initPop(showItems: ArrayList<String>?) {
        if (showItems == null) return
        msgPop = CommonListPop(this, showItems)
        msgPop!!.initView(
                onBind = { _, data, view ->
                    view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.tv_info).text = data
                },
                onItemClick = { _, data ->
                    msgPop!!.hidePop()
                    if (data == "修改备注") {
                        val intent = Intent(mContext!!, FriendRemarkNameActivity::class.java)
                        intent.putExtra("userId", targetUserId)
                        if (StringUtils.isNotBlankAndEmpty(remarkName)) {
                            intent.putExtra("remarkName", remarkName)
                        }
                        startActivityForResult(intent, REMARKNAME)
                    } else if (data == "删除好友") {
                        deleteFriend()
                    } else if (data == "加入黑名单") {
                        ShowDialogUtil.showDialog(this, bindToLifecycle(), accessToken, targetUserId, 1, onSuccess = {
                            Logger.i("---执行----加入黑名单成功----", "----targetUserId----" + targetUserId)
                            Logger.i("---执行----加入黑名单成功----", "----sessionId----" + sessionId)
                            ToastUtil.show(this, "加入黑名单成功")
//                            popItemMap.remove("加入黑名单")//此时会收到ImPushMsg，收到ImPushMsg后再更新显示
//                            popItemMap.add("移出黑名单")
//                            initPop(popItemMap)

                            //发送加入黑名单成功的指令消息,目的是通知对方我把他加入了黑名单，他的设备要在聊天界面更新黑名单关系；
                            val uuid = SendMsgHelper.getUUID()
                            val blackChangeMsg = MsgBean.ExtMsg.newBuilder()
                                    .setExt1("1")
                                    .build()
                            val sendMsg = SendMsgHelper.getC2CMsgRequestMsgFromBlack(sessionId, blackChangeMsg, uuid)
                            val isSucceed = ImService.sendMsg(sendMsg)

                        }, onFailer = {
                            ToastUtil.show(this, "加入黑名单失败")
                        })
                    } else if (data == "移出黑名单") {
                        ShowDialogUtil.showDialog(this, bindToLifecycle(), accessToken, targetUserId, 2, onSuccess = {
                            Logger.i("---执行----移出黑名单成功----", "----targetUserId----" + targetUserId)
                            Logger.i("---执行----移出黑名单成功----", "----sessionId----" + sessionId)
                            ToastUtil.show(this, "移出黑名单成功")
//                            popItemMap.remove("移出黑名单")//此时会收到ImPushMsg，收到ImPushMsg后再更新显示
//                            popItemMap.add("加入黑名单")
//                            initPop(popItemMap)
                            //发送移出黑名单成功的指令消息
                            val uuid = SendMsgHelper.getUUID()
                            val blackChangeMsg = MsgBean.ExtMsg.newBuilder()
                                    .setExt1("2")
                                    .build()
                            val sendMsg = SendMsgHelper.getC2CMsgRequestMsgFromBlack(sessionId, blackChangeMsg, uuid)
                            val isSucceed = ImService.sendMsg(sendMsg)

                        }, onFailer = {
                            ToastUtil.show(this, "移出黑名单失败")
                        })
                    }
                })
    }

    /**请离成员*/
    private fun leaveMember() {
        val view = View.inflate(mContext, com.joinutech.ddbeslibrary.R.layout.dialog_orgmember_bottom_layout, null)
        val dialog = BottomDialogUtil.showBottomDialog(mContext!!, view, Gravity.BOTTOM)
        val mSelectPicture = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.selectPicture)
        val mDismiss = view.findViewById<TextView>(R.id.dismiss)
        mSelectPicture.setOnClickListener {
            dialog.dismiss()
            leaveOrg()
        }
        mDismiss.setOnClickListener {
            dialog.dismiss()
        }
    }

    /**删除好友*/
    private fun deleteFriend() {
        val content = "删除好友之后，你将同时从对方好友列表中删除，" +
                "你和好友之间的聊天记录也会被清空"
        val dialog = MyDialog(mContext!!, 329, 199, content,
                true, needBtnCancel = true, bgResourceId = 0)
        dialog.show()
        dialog.setCanceledOnTouchOutside(true)
        dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
            override fun clickRightBtn() {
                dialog.dismiss()
                getLoadingDialog("删除好友中...", false)
                //删除好友
                presenter.deleteFriend(bindToLifecycle(), accessToken, targetUserId, {
                    //请离成功
                    ToastUtil.show(mContext!!, "删除好友成功")
                    hideLoading()
                    //清空聊天记录
                    FriendCacheHolder.removeFriend(targetUserId)// 删除好友时，清除好友信息数据
                    MessageDaoOpe.instance.deleteMessageListByUid_ChatId(this@FriendInfoActivity, userId!!, targetUserId)
                    SessionDaoOpe.instance.deleteSessionByUid_AppChatId(this@FriendInfoActivity, userId, targetUserId)
                    UserHolder.saveSessionId(targetUserId, "")
//                    EventBusUtils.sendEvent(EventBusEvent(ConstantEventCodeUtil.TCP_REFRESH_FRIEND_LIST, ""))
                    EventBusUtils.sendEvent(EventBusEvent(ConstantEventCodeUtil.TCP_REFRESH_FRIEND_ON_DELETE, targetUserId))

//                    EventBusUtils.sendEvent(EventBusEvent(IMEvent.MSGLIST_DELETE, nowUserId))
//                    EventBusUtils.sendEvent(EventBusEvent(IMEvent.CHANGE_FRIEND_MSG, nowUserId))
//                    //刷新好友列表
//                    EventBusUtils.sendStickyEvent(EventBusEvent(EventBusAction.REFRESH_FRIENDLIST, ""))
                    showLog("关闭用户信息页面")
                    <EMAIL>()
                }, { msg ->
                    run {
                        hideLoading()
                        ToastUtil.show(mContext!!, msg)
                    }
                })
            }
        })
    }

    /**请离团队*/
    private fun leaveOrg() {
        val dialog = MyDialog(mContext, 329, 166, "", needBtnConfirm = false,
                needBtnCancel = false, bgResourceId = 0)
        val view = View.inflate(mContext!!, R.layout.dialog_leaveorg, null)
        dialog.setView(view, Gravity.CENTER)
        view.findViewById<TextView>(R.id.confirm).setOnClickListener {
            dialog.dismiss()
            dismissOrgMember()
        }
        view.findViewById<TextView>(R.id.cancel).setOnClickListener {
            dialog.dismiss()
        }
        dialog.setCanceledOnTouchOutside(true)
        dialog.show()
    }

    private fun dismissOrgMember() {
        val list: ArrayList<String> = arrayListOf()
        list.add(targetUserId)
        getLoadingDialog("", false)
        //批量请离员工需要做是否有创建项目员工的判断
        presenter.leaveUserValidate(bindToLifecycle(), accessToken!!, list, companyId, {
            dismissDialog()
            if (StringUtils.isNotBlankAndEmpty(it as String)) {
                ARouter.getInstance().build(RouteTask.projectHandOverActivity)
                        .withString("handOverUserName", personData?.name ?: "")
                        .withString("handOverUserName", personName)
                        .withString("handOverUserId", targetUserId)
                        .withString("companyId", companyId)
                        .navigation()
            } else {
                dismissOrgMemberAction(list)
            }
        }, {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun dismissOrgMemberAction(list: ArrayList<String>) {
        getLoadingDialog("", false)
        presenter.leaveUser(bindToLifecycle(), accessToken!!, list, companyId, {
            //请离成功
            dismissDialog()
            ToastUtil.show(mContext!!, "请离团队成功")
            EventBusUtils.sendEvent(
                    EventBusEvent(EventBusAction.REFRESH_ORGCHARTLIST, ""))
            finish()
        }, { msg ->
            run {
                dismissDialog()
                ToastUtil.show(mContext!!, msg)
            }
        })
    }

    private fun initType() {
        when (type) {
            "org" -> {
                //从团队中查看团队成员
                if (orgCreatorId == userId || orgPermission) {
                    binding.clOrgInfoLayout.visibility = View.VISIBLE
                    binding.tvDeptName.text = depName
                    binding.empty.visibility = View.VISIBLE
                    binding.tvDeptSelect.visibility = binding.clOrgInfoLayout .visibility
                } else {
                    //是加入的团队，看其他人的信息，只能看到基本信息，不能去查看和修改所在部门
                    binding.clOrgInfoLayout.visibility = View.GONE
                    binding.empty.visibility = View.GONE
                    binding.tvDeptSelect.visibility = View.GONE
                }
            }
//            "friend" -> {
//                //从好友列表过来，不需要看团队信息
//                cl_org_info_layout.visibility = View.GONE
//                empty.visibility = View.GONE
//                tv_dept_select.visibility = View.GONE
////                sendMsgTv.text = "发消息"
//            }
            else -> {
                //不确定是否是好友
                binding.clOrgInfoLayout.visibility = View.GONE
                binding.empty.visibility = View.GONE
                binding.tvDeptSelect.visibility = View.GONE
            }
        }
    }
//tcp获取好友详情

    /**
     * 更新人员信息
     * 调用：
     * 首次加载页面时
     * 添加好友成功后
     * 空页面点击刷新时
     * */
    @SuppressLint("SetTextI18n")
    private fun updateUserInfo(checkRelation: Boolean = false) {
        // 获取团队或者个人的好友信息，内部是分两个接口获取的，搜索“tcp详情页获取数据”
        showLog("用户信息页面 重新获取用户信息")
        fun getUserInfo() {
            presenter.getCompanyUserInfo(bindToLifecycle(), accessToken!!,
                    companyId = companyId,
                    userId = targetUserId,
                    onSuccess = {
                        Logger.i("---执行---获取好友信息---", "--detail--" + GsonUtil.toJson(it))
                        setShowEmptyView(false)
                        dismissDialog()
                        val bean = parseJsonAny<OrgMemberInfoBean>(it)
                        if (bean.logout == 1) {
                            val logoutIds = FriendCacheHolder.getMyLogoutFriendIds()
                            if (!logoutIds.contains(targetUserId)) {
                                logoutIds.add(targetUserId)
                                FriendCacheHolder.updateLogoutFriendList(logoutIds)
                            }
                        }
                        //被动刷新好友信息
                        val userInfoNew = UserInfo(targetUserId, bean.headimg ?: "",
                                bean.name ?: "", bean.remark ?: "")
                        if (targetUserId != userId) {
                            //好友的头像有变动
                            if (/*fromImmsg ||*/ type == "friend") {
                                val friendInfo = FriendBean()
                                friendInfo.avatar = bean.headimg ?: ""
                                friendInfo.remark = bean.remark ?: ""
                                friendInfo.name = bean.name ?: ""
                                friendInfo.userId = targetUserId
                                friendInfo.logout = bean.logout
//                            if (bean.isFriend) {
//                                friendInfo.relation = 1
//                            } else {
//                                friendInfo.relation = 2
//                            }
                                FriendCacheHolder.updateFriendInfo(this, friendInfo,
                                        onResult = { }, onError = {})
                                FriendCacheHolder.saveFriend(userInfoNew)
                            } else {
                                CacheDataHolder.setUserInfo(userInfoNew.userId, userInfoNew)
                            }
                            EventBusUtils.sendEvent(EventBusEvent(MSG_LIST_INFO_CHANGE, ""))
                        }
                        dealDataResult(bean)
                    },
                    onError = {
                        dismissDialog()
                        ToastUtil.show(mContext!!, "获取信息失败")
                        finish()
//                    setShowEmptyView(true)
                    })
        }

        fun checkFriendRelation() {
            Observable.create(ObservableOnSubscribe<FriendBean> { emitter ->
                var bean: FriendBean? = null
                try {
                    bean = FriendDaoOpe.instance.queryFriendByUserId(mContext!!, targetUserId)
                } catch (e: Exception) {
                    showLog("查询好友 失败")
                }
                if (bean != null) {
                    emitter.onNext(bean)
                } else {
                    val friendTemp = FriendBean()
                    friendTemp.relation = 2
                    friendTemp.logout = 0
                    emitter.onNext(friendTemp)
                }
            }).compose(bindToLifecycle())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeOn(Schedulers.io())
                    .subscribe {

                        getUserInfo()
                    }
        }

        if (checkRelation) {
            checkFriendRelation()
        } else {
            getUserInfo()
        }
    }

    var personData: OrgMemberInfoBean? = null

    /**处理团队成员信息*/
    @SuppressLint("SetTextI18n")
    private fun dealDataResult(bean: OrgMemberInfoBean) {
        fun checkApproveStatus() {
            val ivTag = BaseApplication.getApproveTag(bean.approveType)
            if (ivTag > 0) {
                binding.ivApprovalTag.visibility = View.VISIBLE
                binding.ivApprovalTag.setImageResource(ivTag)
            } else {
                binding.ivApprovalTag.visibility = View.GONE
            }
        }
        checkApproveStatus()
        UserHolder.saveSessionId(targetUserId, bean.imId)
        sessionId = bean.imId
        Logger.i("----执行---验证isFriend----", "---bean.isFriend---" + bean.isFriend)
        Logger.i("----执行---验证sessionID----", "--获取的对方的sessionID---" + sessionId)
        personData = bean
        // 好友数据中，isFriend==2 才是双向好友
//        isFriend = bean.isFriend == 2
        if (targetUserId == userId) {
            setPageName("个人信息")
        } else {
            isLogout = bean.logout
            isFriend = bean.isFriend
            updateTitle()
//            if (StringUtils.isEmpty(type) && isFriend) {
//                setPageTitle("好友信息")
//            }
            if (isFriend == false || isFriend == true || ((type == "org") && ((orgCreatorId == userId) || (orgCreatorId != userId && orgPermission)))) {
                //你创建的公司或者当前信息者是你的好友，才可以请离或者删除好友
                setRightImage(R.drawable.ic_threedot_white, this)
            }
        }
        //判断该用户在没在黑名单
        changeRightTitleContent()
//        //先排除自己
//        if (targetUserId != userId) {
//
//        }
        //必显项
        nowUserLogo = bean.headimg ?: ""
        if (StringUtils.isNotBlankAndEmpty(nowUserLogo)) {
            ImageLoaderUtils.loadImage(mContext!!, binding.ivUserIcon , nowUserLogo)
        }
        nowUserName = bean.name ?: ""
        remarkName = bean.remark ?: ""
        if (StringUtils.isNotBlankAndEmpty(remarkName)) {
            binding.tvName.text = remarkName
            binding.tvRemarkName.visibility = View.VISIBLE
            binding.tvRemarkName.text = "用户名：$nowUserName"
        } else {
            binding.tvName.text = nowUserName
        }
        when (bean.gender) {
            1 -> {
                binding.ivGender.setImageResource(R.drawable.icon_friend_info_boy)
            }
            else -> {
                binding.ivGender.setImageResource(R.drawable.icon_friend_info_girl)
            }
        }
        try {
            //由出生日期获得年龄***
            if (StringUtils.isNotBlankAndEmpty(bean.birthday)) {
                binding.tvAge.visibility = View.VISIBLE
                val ageText = CommonUtils.getAge(CommonUtils.parse(bean.birthday))
                binding.tvAge.text = "$ageText 岁"
            } else {
                binding.tvAge.visibility = View.GONE
            }
        } catch (e: Exception) {
            e.printStackTrace()
            binding.tvAge.visibility = View.GONE
        }
        if (bean.logout == 1) {
            hideRightImage()
            binding.clFriendDetailLayout.visibility = View.GONE
            binding.clFriendBottomLayout.visibility = View.GONE
            binding.tvLogoutTip.visibility = View.VISIBLE
            binding.tvLogoutDelete.visibility = View.VISIBLE
            binding.tvLogoutDelete.setOnClickListener {
                deleteFriend()
            }
        } else {
            if (bean.isShow) {
                //团队中管理人员 查看人员信息
                binding.clFriendDetailLayout.visibility = View.VISIBLE
                //共同加入的团队
                if (StringUtils.isNotBlankAndEmpty(bean.commonNames) && bean.count != 0) {
                    binding.tvOrgInfo.visibility = View.VISIBLE
                    if (bean.count == 1) {
                        binding.tvOrgInfo.text = "共同加入了 ".plus(bean.commonNames)
                    } else {
                        binding.tvOrgInfo.text = "共同加入了 ".plus(
                                bean.commonNames.substring(0, bean.commonNames.indexOf(","))
                        ).plus(" 等${bean.count}个团队")
                    }
//                    val span = Spanny().append("共同加入了 ")
//                            .append(orgName, ForegroundColorSpan(
//                                    CommonUtils.getColor(mContext!!, R.color.color1E87F0)))
//                            .append(" 等${bean.count}个团队")
                }
                if (StringUtils.isNotBlankAndEmpty(bean.address)) binding.etAddress.text = bean.address
                else binding.etAddress.text = "未填写"
                if (StringUtils.isNotBlankAndEmpty(bean.birthday)) binding.tvBirthday.text = bean.birthday
                else binding.tvBirthday.text = "未填写"
                binding.tvPhone.text = bean.mobile
                if (StringUtils.isNotBlankAndEmpty(bean.email)) binding.tvEmail.text = bean.email
                else binding.tvEmail.text = "未填写"
                if (StringUtils.isNotBlankAndEmpty(bean.profession))
                    binding.tvProfession.text = bean.profession
                else binding.tvProfession.text = "未填写"
                //先判断是不是自己
                if (targetUserId == userId) {
                    binding.clFriendBottomLayout.visibility = View.GONE
                } else if (isFriend == true) {//好友
                    //是团队创建者且查看团队中好友的信息
                    binding.clFriendBottomLayout.visibility = View.VISIBLE
                    binding.clAddFriend.visibility = View.GONE
                    binding.clSendLayout.visibility = View.VISIBLE
                } else {
                    //是团队创建者且查看团队中非好友的信息
                    binding.clFriendBottomLayout.visibility = View.VISIBLE
                    binding.clAddFriend.visibility = View.VISIBLE
                    binding.clSendLayout.visibility = View.VISIBLE
                }
            } else {// 不显示详情时，
                binding.clFriendDetailLayout.visibility = View.GONE
                //先判断是不是自己
                if (targetUserId == userId) {
                    
                    binding.clFriendBottomLayout.visibility = View.GONE
                } else if (isFriend == true) {//好友而且双向好友
                    binding.clFriendBottomLayout.visibility = View.VISIBLE
                    
                    binding.clAddFriend.visibility = View.GONE
                    
                    binding.clSendLayout.visibility = View.VISIBLE
                } else {
                    binding.clFriendBottomLayout.visibility = View.VISIBLE
                    binding.clAddFriend.visibility = View.VISIBLE
                    binding.clSendLayout.visibility = View.VISIBLE
                    binding.clSendEmailLayout.visibility = View.GONE
                    binding.clCallLayout.visibility = View.GONE
                }
            }
        }

        //投诉相关
        binding.friendComplainLayout.tvComplainText.text = "对方曾被他人投诉，谨防诈骗"
//        when (2) {//测试安全提示（）
        when (bean.userStatus) {
            //安全提示显示的时候，高设置为276，不显示的时候设置为240dp
            0 -> {//正常情况
                binding.friendComplainLayout.rlComplainLayout.visibility = View.GONE
                showTopHeight(240)
            }
            1 -> {//被投诉三次及以上，没有被封禁
                binding.friendComplainLayout.rlComplainLayout.visibility = View.VISIBLE
                showTopHeight(276)
            }
            2 -> {//对方账号被封禁了
                binding.friendComplainLayout.rlComplainLayout.visibility = View.VISIBLE
                showTopHeight(276)
                //隐藏下面的四个按键
                binding.clFriendBottomLayout.visibility = View.GONE
            }
        }
    }

    override fun onEmptyRefresh() {
        updateUserInfo()// 空页面刷新好友信息
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRefreshInfo(event: EventBusEvent<String>) {
        if (IMEvent.UPDATE_FRIEND_VERSION == event.code) {
            showLog("用户信息页面 好友数据更新，更新好友关系信息")
            updateUserInfo(type == "friend")
        }
    }

    private fun showTopHeight(height: Int) {
        val pxValue = ScreenUtils.dip2px(this, height.toFloat())
        val layoutParams = binding.cdTop.layoutParams as ConstraintLayout.LayoutParams
        layoutParams.height = pxValue
        binding.cdTop.requestLayout()
    }
}