package com.joinutech.addressbook.view

import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.AddressbookConstract
import com.joinutech.addressbook.databinding.ActivityOrgannameBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.StringUtils.Companion.setEtFilter
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.toastShort
import javax.inject.Inject
import javax.inject.Named

/**
 *<AUTHOR>
 *@date 2018/11/22
 * 修改名称
 */
class OrganRenameActivity : MyUseBindingActivity<ActivityOrgannameBinding>() {
    @Inject
    @field:Named(AddressbookUtil.ADDR_PRESENTER)
    lateinit var presenter: AddressbookConstract.AddressbookPresenter

    var oldCompanyName = ""
    var companyId: String = ""

    override fun initImmersion() {
        setPageTitle("团队名称")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        whiteStatusBarBlackFont()
        setRightTitle("保存", View.OnClickListener {
            onSaveCompanyName()
        })
    }

    private fun onSaveCompanyName() {
        val newCompanyName =  binding.organRename.text?.toString()
        if (!newCompanyName.isNullOrBlank()) {
            // TODO: 2020/6/2 13:55 检查输入团队名规则
            if (oldCompanyName != newCompanyName) {
                if (StringUtils.isValidStr(newCompanyName)) {
//                    toastShort("跳转页面")
                    var map = hashMapOf<String, Any>()
                    map["name"] = newCompanyName
                    map["companyId"] = companyId
                    presenter.modifyCompany(bindToLifecycle(), accessToken!!, map, {
                        setResult(Activity.RESULT_OK, Intent().putExtra("name", binding.organRename.text.toString()))
                        finish()
                    }, {
                        ToastUtil.show(mContext!!, it)
                    })
                } else {
                    toastShort("团队名称包含特殊字符")
                }
            } else {
                finish()
            }
        } else {
            ToastUtil.show(mContext!!, "请输入团队名称")
        }
    }

    override fun initView() {
        DaggerAddressbookComponent.builder().build().inject(this)
        if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyName"))) {
            oldCompanyName = intent.getStringExtra("companyName") ?: ""
        }
        binding.organRename.setText(oldCompanyName)
        if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
            companyId = intent.getStringExtra("companyId") ?: ""
        }
    }

    override fun initLogic() {
        super.initLogic()
        setEtFilter(binding.organRename)
        binding.organRename.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (s != null && s.toString().isNotEmpty()) {
                    if (s.length > 30) {
                        ToastUtil.showCustomToast(null, mContext!!,
                                true, "请输入1-30个中文字符")
                        s.delete(30, s.length)
                    }
                    binding.organDel.visibility = View.VISIBLE
                } else {
                    binding.organDel.visibility = View.GONE
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        })
         binding.organDel.setOnClickListener { binding.organRename.text.clear() }
    }

    override val contentViewResId: Int = R.layout.activity_organname
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityOrgannameBinding {
        return ActivityOrgannameBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = false

}