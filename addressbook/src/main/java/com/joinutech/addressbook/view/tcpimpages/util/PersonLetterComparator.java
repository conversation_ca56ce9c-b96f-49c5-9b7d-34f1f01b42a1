package com.joinutech.addressbook.view.tcpimpages.util;

import com.joinutech.ddbeslibrary.bean.ContactModel;
import com.joinutech.ddbeslibrary.bean.PersonSearchBean;

import java.util.Comparator;

/**
 * 专用于按首字母排序
 *
 * <AUTHOR>
 * @fileName WaveSideBarView
 * @packageName com.nanchen.wavesidebarview
 * @date 2016/12/27  16:19
 * @github https://github.com/nanchen2251
 */

public class PersonLetterComparator implements Comparator<PersonSearchBean>{

    @Override
    public int compare(PersonSearchBean personBean1, PersonSearchBean personBean2) {
        if (personBean1 == null || personBean2 == null){
            return 0;
        }
        String lhsSortLetters = personBean1.getIndex().substring(0, 1).toUpperCase();
        String rhsSortLetters = personBean2.getIndex().substring(0, 1).toUpperCase();
        return lhsSortLetters.compareTo(rhsSortLetters);
    }
}
