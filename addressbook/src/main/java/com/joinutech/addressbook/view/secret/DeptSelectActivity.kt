package com.joinutech.addressbook.view.secret

import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.TextView
import androidx.lifecycle.Observer

import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.addressbook.R
import com.joinutech.addressbook.databinding.ActivityDeptSelectBinding
import com.joinutech.common.adapter.MyAdapter
import com.joinutech.common.adapter.impl.DepLevelListAdapter
import com.joinutech.ddbeslibrary.base.SimpleBaseActivity
import com.joinutech.ddbeslibrary.bean.Branch
import com.joinutech.ddbeslibrary.request.exception.ErrorType
import com.joinutech.ddbeslibrary.utils.*

/**
 * @PackageName: com.joinutech.addressbook.view.secret
 * @ClassName: DeptSelectActivity
 * @Desc: 部门选择页面
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/3/16 17:06
 */
@Route(path = RouteOrg.ORG_DEPT_SELECT)
class DeptSelectActivity(override val contentViewResId: Int = R.layout.activity_dept_select) : SimpleBaseActivity<ActivityDeptSelectBinding>() {

    lateinit var adapter: MyAdapter<DeptInfo>
    val data = arrayListOf<DeptInfo>()

    /**部门层级数据源*/
    private var viewLinked = mutableListOf<DeptInfo>()

    /**已选择部门id缓存*/
    private val selectDepts = hashSetOf<String>()
    private lateinit var tvOrgName: TextView
    private lateinit var ivOrgSelect: View

    private var companyId = ""
    private var ateHisDate = ""

    /**页面类型
     * 0    加载最新的团队信息
     * 其他 加载历史团队信息
     * */
    private var pageType = 0
    private var showLevel = false

    override fun initView() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back2, this)
        intent.extras?.let {
            companyId = it.getString(CommonKeys.COMPANY_ID, "")
            ateHisDate = it.getString(CommonKeys.DATE, "")
            pageType = it.getInt(CommonKeys.PAGE_TYPE, pageType)
            showLevel = it.getBoolean(CommonKeys.SHOW_LEVEL, showLevel)

            setPageTitle(it.getString(CommonKeys.TITLE_NAME, "选择部门"))
            setRightTitle(it.getString(CommonKeys.RIGHT_TITLE, "完成"), this)

            val temp = it.getStringArrayList(CommonKeys.DATA)
            if (!temp.isNullOrEmpty()) {
                selectDepts.addAll(temp)
            }
        }

        tvOrgName = binding.root.findViewById(R.id.tv_none)
        ivOrgSelect = binding.root.findViewById(R.id.iv_select)
        ivOrgSelect.setOnClickListener(this)
        binding.root.findViewById<View>(R.id.iv_arrow).visibility = View.GONE

        binding.tvSelectDeptCount.text = String.format(resources.getString(R.string.str_select_dept_count),
                selectDepts.size)

        initSearchView()
        if (showLevel) {
            initLevelView()
        }
        iniDeptView()
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityDeptSelectBinding {
        return ActivityDeptSelectBinding.inflate(layoutInflater)
    }

    private fun iniDeptView() {
        adapter = MyAdapter(this, R.layout.item_select_dept_layout,
                data,
                onBindItem = { position: Int, dept: DeptInfo, view: View ->
                    if (position == data.lastIndex) {
                        view.findViewById<View>(R.id.line_bottom).visibility = View.VISIBLE
                    } else {
                        view.findViewById<View>(R.id.line_bottom).visibility = View.GONE
                    }
                    if (dept.deptList.isNullOrEmpty()) {
                        view.findViewById<View>(R.id.iv_arrow).visibility = View.GONE
                    } else {
                        view.findViewById<View>(R.id.iv_arrow).visibility = View.VISIBLE
                    }
                    val ivSelect = view.findViewById<View>(R.id.iv_select)
                    ivSelect.isSelected = dept.deptId in selectDepts
                    ivSelect.setOnClickListener {
                        onSelect(dept.deptId)// 选择图标点击
                        adapter.notifyItemChanged(position)
                    }
                    view.findViewById<TextView>(R.id.tv_none).text = dept.deptName
                },
                onItemClick = { position: Int, dept: DeptInfo, _: View ->
                    if (!dept.deptList.isNullOrEmpty()) {
                        viewLinked.add(dept)
                        updateList(viewLinked.last())//on show next level
                    } else {
                        onSelect(dept.deptId)// 部门列表项 不存在子部门时点击
                        adapter.notifyItemChanged(position)
                    }
                })

        binding.rvList.layoutManager = LinearLayoutManager(this)
        binding.rvList.adapter = adapter
    }

    private val levelList = arrayListOf<Branch>()
    lateinit var levelAdapter: DepLevelListAdapter

    private fun initLevelView() {
        binding.rvLevelList.layoutManager = LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        levelAdapter = DepLevelListAdapter(this, levelList)
        levelAdapter.setClickListener(object : DepLevelListAdapter.ItemClickListener {
            override fun onItemClick(position: Int) {
                val subList = viewLinked.dropLast(viewLinked.size - position - 1)
                viewLinked.clear()
                viewLinked.addAll(subList)
                updateList(viewLinked.last())//level select
            }
        })
        binding.rvLevelList.adapter = levelAdapter
    }

    lateinit var searchAdapter: MyAdapter<DeptInfo>
    private val searchData = arrayListOf<DeptInfo>()
    private fun initSearchView() {
        binding.rvListSearch.visibility = View.GONE

        val searchBar = findViewById<View>(R.id.search_bar)
        val inputClear = searchBar.findViewById<View>(R.id.iv_input_clear)
        val editText = searchBar.findViewById<EditText>(R.id.et_search_input)

        val cancel = searchBar.findViewById<TextView>(R.id.tv_cancel)
        cancel.visibility = View.GONE
        inputClear.visibility = View.GONE

        cancel.setOnClickListener {
            try {
                if (currentFocus != null && currentFocus?.windowToken != null)
                    (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                            .hideSoftInputFromWindow(currentFocus!!
                                    .windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
            } catch (e: java.lang.Exception) {

            }
            editText.clearFocus()
            editText.setText("")
            inputClear.visibility = View.GONE
            cancel.visibility = View.GONE
            showSearch(false)
        }
        editText.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus && cancel.visibility != View.VISIBLE) {
                cancel.visibility = View.VISIBLE
            }
        }
        editText.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {

            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (s != null && s.isNotEmpty()) {
                    inputClear.visibility = View.VISIBLE
                } else {
                    inputClear.visibility = View.GONE
                }
            }

        })

        editText.setOnEditorActionListener { view, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH || (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER)) {
                onSearch(view.text.toString())
                true
            }
            false
        }

        inputClear.setOnClickListener {
            editText.setText("")
            inputClear.visibility = View.GONE
            onSearch("")
        }

        searchAdapter = MyAdapter(this, R.layout.item_select_dept_layout, searchData,
                onBindItem = { position: Int, dept: DeptInfo, view: View ->
                    if (position == data.lastIndex) {
                        view.findViewById<View>(R.id.line_bottom).visibility = View.VISIBLE
                    } else {
                        view.findViewById<View>(R.id.line_bottom).visibility = View.GONE
                    }
                    view.findViewById<View>(R.id.iv_arrow).visibility = View.GONE
                    val ivSelect = view.findViewById<View>(R.id.iv_select)
                    ivSelect.isSelected = dept.deptId in selectDepts
                    view.findViewById<TextView>(R.id.tv_none).text = dept.deptName
                },
                onItemClick = { position: Int, dept: DeptInfo, _: View ->
                    onSelect(dept.deptId)// 搜索列表项点击
                    searchAdapter.notifyItemChanged(position)
                })
        binding.rvListSearch.layoutManager = LinearLayoutManager(this)
        binding.rvListSearch.adapter = searchAdapter
        binding.rvListSearch.visibility = View.GONE
    }

    private fun onSearch(searchName: String) {
        searchData.clear()
        if (StringUtils.isNotBlankAndEmpty(searchName)) {
            for (key in allNames.keys) {
                if (key.contains(searchName)) {
                    searchData.add(allNames[key]!!)
                }
            }
        }
        if (searchData.isEmpty()) {
            toastShort("未找到相关部门")
            showSearch(false)
        } else {
            showSearch(true)
            searchAdapter.notifyDataSetChanged()
        }
    }

    private fun showSearch(show: Boolean) {
        if (show) {
            binding.rvListSearch.visibility = View.VISIBLE
            binding.rvLevelList.visibility = View.GONE
            binding.root.visibility = View.GONE
            binding.tvSubDept.visibility = View.GONE
            binding.rvList.visibility = View.GONE
            binding.tvSelectDeptCount.visibility = View.GONE
        } else {
            binding.rvListSearch.visibility = View.GONE
            if (showLevel) {
                binding.rvLevelList.visibility = View.VISIBLE
            }
            binding.root.visibility = View.VISIBLE
            binding.tvSubDept.visibility = View.VISIBLE
            binding.rvList.visibility = View.VISIBLE
            binding.tvSelectDeptCount.visibility = View.VISIBLE
        }
    }

    private fun onSelect(deptId: String) {
        if (selectDepts.contains(deptId)) {
            selectDepts.remove(deptId)
        } else {
            selectDepts.add(deptId)
        }
        binding.tvSelectDeptCount.text = String.format(resources.getString(R.string.str_select_dept_count), selectDepts.size)
    }

    private fun onUpLevelSelect(deptId: String, deptIds: List<String>) {
        if (selectDepts.contains(deptId)) {
            selectDepts.remove(deptId)
        } else {
            selectDepts.add(deptId)
            selectDepts.addAll(deptIds)
        }
        binding.tvSelectDeptCount.text = String.format(resources.getString(R.string.str_select_dept_count), selectDepts.size)
    }

    lateinit var viewModel: SecretModel
    private val allNames = hashMapOf<String, DeptInfo>()

    override fun initLogic() {
        super.initLogic()

        viewModel = getModel(SecretModel::class.java)
        viewModel.getDeptListResult.observe(this, Observer {
            when {
                it.success != null -> {
                    viewLinked.clear()
                    viewLinked.add(it.success!!)
                    tvOrgName.text = it.success!!.deptName
                    updateList(viewLinked.last())//first init
                    allNames.clear()
                    allNames.putAll(initCaches(it.success!!))
                }
                it.errorCode != ErrorType.SUCCESS -> {
                    if (it.extra != null && it.extra is String) {
                        toastShort(it.extra!! as String)
                    } else {
                        toastShort("保存失败")
                    }
                }
                else -> {
                    toastShort("保存失败")
                }
            }
        })

        if (pageType == 0) {
            viewModel.getDeptList(companyId, "0")
        } else {
            viewModel.getDeptHistoryList(companyId, ateHisDate)
        }
    }

    private fun initCaches(rootDept: DeptInfo): HashMap<String, DeptInfo> {
        val hashmap = hashMapOf<String, DeptInfo>()
        hashmap[rootDept.deptName] = rootDept
        if (!rootDept.deptList.isNullOrEmpty()) {
            for (dep in rootDept.deptList) {
                hashmap.putAll(initCaches(dep))
            }
        }
        return hashmap
    }

    private fun updateList(currentDept: DeptInfo) {
        tvOrgName.text = currentDept.deptName
        ivOrgSelect.isSelected = currentDept.deptId in selectDepts
        data.clear()
        data.addAll(currentDept.deptList)
        adapter.notifyDataSetChanged()
        if (showLevel) {
            if (viewLinked.size > 1) {
                if (binding.rvLevelList.visibility != View.VISIBLE) {
                    binding.rvLevelList.visibility = View.VISIBLE
                }
                levelList.clear()
                levelList.addAll(viewLinked.map { Branch(deptId = it.deptId, name = it.deptName) }.toList())
                levelAdapter.notifyDataSetChanged()
            } else {
                binding.rvLevelList.visibility = View.GONE
            }
        }
    }

    override fun onNoDoubleClick(v: View) {
        super.onNoDoubleClick(v)
        when (v.id) {
            com.joinutech.ddbeslibrary.R.id.tv_toolbar_right -> {
//                toastShort("返回所有选中的部门id")
                if (selectDepts.isNotEmpty()) {
                    showLog("已选择部门")
                    val intent = Intent()
                    val arrayList = ArrayList<String>(selectDepts.size)
                    arrayList.addAll(selectDepts.toList())
                    intent.putStringArrayListExtra(CommonKeys.RESULT_DATA, arrayList)
                    setResult(Activity.RESULT_OK, intent)
                    finish()
                } else {
                    showLog("未选择部门")
                }
            }
            com.joinutech.ddbeslibrary.R.id.iv_left -> {
                onBackPressed()
            }
            R.id.iv_select -> {
                val last = viewLinked.last()
                if (!last.deptList.isNullOrEmpty()) {
                    onUpLevelSelect(last.deptId, last.deptList.map { it.deptId }.toList())// 部门选择时，选中部门同时选中它的子部门
                    adapter.notifyDataSetChanged()
                } else {
                    onSelect(last.deptId)// 部门选择时，选中部门
                }
                ivOrgSelect.isSelected = viewLinked.last().deptId in selectDepts
            }
        }
    }

    override fun onBackPressed() {
        if (viewLinked.size > 1) {
//            if (currentDept!!.deptLevel == 1) {
//                super.onBackPressed()
//            } else {
//                val downLevel = currentDept!!.deptLevel - 1
//                if (downLevel == 1) {
//                    currentDept = rootDept
//                } else {
//                }
//
//            }

            viewLinked.removeAt(viewLinked.lastIndex)
            updateList(viewLinked.last())//后退键
        } else {
            super.onBackPressed()
        }
    }
}
