package com.joinutech.addressbook.view

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.AddressbookConstract
import com.joinutech.addressbook.databinding.ActivityMoreBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.ContactModel
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.utils.*
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import javax.inject.Inject
import javax.inject.Named

/**
 *<AUTHOR>
 *@date 2018/11/24
 * 更多
 */
class OrganizationMoreActivity : MyUseBindingActivity<ActivityMoreBinding>() {

    override val contentViewResId: Int = R.layout.activity_more
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityMoreBinding {
        return ActivityMoreBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = false

    @Inject
    @field:Named(AddressbookUtil.ADDR_PRESENTER)
    lateinit var presenter: AddressbookConstract.AddressbookPresenter

    private lateinit var companyBean: WorkStationBean

    private var rejectJoin = 0
    private var rejectInvitation = 0

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        whiteStatusBarBlackFont()
        showToolBarLine()
    }

    override fun initView() {
        if (intent.getSerializableExtra("companyBean") != null) {
            try {
                companyBean = intent.getSerializableExtra("companyBean") as WorkStationBean
            } catch (e: Exception) {
                finish()
            }
            setPageTitle(companyBean.name)
            rejectInvitation = companyBean.rejectInvitation
            rejectJoin = companyBean.rejectJoin
        }
        DaggerAddressbookComponent.builder().build().inject(this)
        if (companyBean.deptId != "0") {
            if (ORG_PERMISS_TYPE.checkSuperPermission(companyBean.power)) {
                 binding.rlPermissionSet.visibility = View.VISIBLE
                 binding.rlPerference.visibility = View.VISIBLE
            } else {
                binding.rlPermissionSet.visibility = View.GONE
                binding.rlPerference.visibility = View.GONE
            }
             binding.clChangeCreator.visibility = View.GONE
        }
    }

    override fun initLogic() {
        super.initLogic()
        binding.clChangeCreator.setOnClickListener(this)
        binding.rlPerference.setOnClickListener(this)
        binding.rlPermissionSet.setOnClickListener(this)
    }

    override fun onNoDoubleClick(v: View) {
        super.onNoDoubleClick(v)
        when (v.id) {
            R.id.rl_permissionSet -> {//权限设置
                val intent = Intent(mContext!!, OrgPermissionActivity::class.java)
                intent.putExtra("companyId", companyBean?.companyId)
                intent.putExtra("isCreator", companyBean?.deptId == "0")
                startActivity(intent)
            }
            R.id.rl_perference -> {//偏好设置
                val intent1 = Intent(mContext!!, OrganizationPerferenceActivity::class.java)
                intent1.putExtra("companyId", companyBean?.companyId)
                intent1.putExtra("rejectInvitation", rejectInvitation)
                intent1.putExtra("rejectJoin", rejectJoin)
                startActivity(intent1)
            }
            R.id.cl_change_creator -> {
                //转移团队创建者
                ARouter.getInstance()
                        .build(RouteOrg.friendListWithSearchActivity)
                        .withString("type", "selectMember")
                        .withString("companyId", companyBean!!.companyId)
                        .navigation(this, 4001)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
//        if (requestCode == 0x13 && resultCode == Activity.RESULT_OK) {
//            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST, ""))
//            finish()
//        } else {
        if (resultCode == Activity.RESULT_OK && requestCode == 4001) {
            val resultData = data?.getSerializableExtra("selectMember")
            if (resultData != null) {
                val json = data.getStringExtra("selectMember") ?: ""
                if (StringUtils.isNotBlankAndEmpty(json)) {
                    val contact = GsonUtil.fromJson<ContactModel>(json) ?: return
                    val memberBean = SearchMemberBean(userId = contact.userId,
                            name = contact.name, headimg = contact.logo)
                    val bundle = Bundle()
                    bundle.putSerializable("companyBean", companyBean!!)
                    bundle.putSerializable("member", memberBean)
                    ARouter.getInstance().build(RouteOrg.ORG_CHANGE_CREATOR).with(bundle).navigation(this)
                }
            }
        }
//        }
    }


    private var changeJoinSetting = false

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun getPreference(eventBusEvent: EventBusEvent<ArrayList<Int>>) {
        if (eventBusEvent.code == EventBusAction.ORG_PREFRENCE) {
            val preferenceList = eventBusEvent.data!!
            if (!changeJoinSetting && (rejectInvitation != preferenceList[0] || rejectJoin != preferenceList[1])) {
                changeJoinSetting = true
            }
            rejectInvitation = preferenceList[0]
            rejectJoin = preferenceList[1]
        } else if (eventBusEvent.code == "changeOrgCreatorSuccess") {
            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST, "-4"))
            onBackPressed()
        }
    }

    override fun onBackPressed() {
        if (changeJoinSetting) {
            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST, "-5"))// 邀请成员设置修改
        }
        super.onBackPressed()
    }

}