package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.app.Activity
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Typeface
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.library.im.imtcp.Logger
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.ChildDepartmentAdapter
import com.joinutech.addressbook.adapter.OrgMemberListAdapterNew
import com.joinutech.addressbook.constract.OrgChartConstract
import com.joinutech.addressbook.databinding.ActivtiyOrgchartLayoutBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.common.adapter.impl.DepLevelListAdapter
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.Branch
import com.joinutech.ddbeslibrary.bean.Member
import com.joinutech.ddbeslibrary.bean.OrgChartBean
import com.joinutech.ddbeslibrary.utils.BottomDialogUtil
import com.joinutech.ddbeslibrary.utils.BottomListDialogHelper
import com.joinutech.ddbeslibrary.utils.CommonKeys
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.ItemClickListener
import com.joinutech.ddbeslibrary.utils.MyDialog
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.toastShort
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import javax.inject.Inject
import javax.inject.Named

/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName: OrganizationChartActivity
 * @Desc: 团队架构页面
 * @Author:
 * @Leader: Ke
 * @CreateTime:
 */
@Route(path = RouteOrg.orgChartActivity)
class OrganizationChartActivity : MyUseBindingActivity<ActivtiyOrgchartLayoutBinding>() {

    override val contentViewResId: Int = R.layout.activtiy_orgchart_layout
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivtiyOrgchartLayoutBinding {
        return ActivtiyOrgchartLayoutBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return true
    }

    @Autowired
    @JvmField
    var companyId: String = ""

    @Inject
    @field:Named(AddressbookUtil.ORGCHART_PRESENTER)
    lateinit var presenter: OrgChartConstract.OrgChartPresenter
    private var depMemberList: ArrayList<Member> = arrayListOf()
    private var type: String = ""
    private var childDepartmentDataList: ArrayList<Branch> = arrayListOf()
    private lateinit var adapter: ChildDepartmentAdapter
    private lateinit var memberAdapter: OrgMemberListAdapterNew

    @Autowired
    @JvmField
    var depName = ""
    private var jumpType = ""

    /**默认部门id为0*/
    private var depId = "0"

    /**创建者id*/
    private var createId = ""

    /**当前level登记*/
    private var frontLevel = 0
    private lateinit var levelAdapter: DepLevelListAdapter
    private var levelList = arrayListOf<Branch>()
    private var isChangeDepName = false
    private var orgPermission: Boolean = false


    override fun initImmersion() {
        dealIntentContent()
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        
        binding.tvCompanyName.text = depName
        DaggerAddressbookComponent.builder().build().inject(this)
        setShowEmptyView(true)
        //防止没有数据的时候提交部门可以点击
        
        binding.tvAddDept.visibility = View.GONE
        //todo 部门列表
        
        binding.rvSubDeptList.layoutManager = object : LinearLayoutManager(mContext) {
            override fun canScrollVertically(): Boolean {
                return false
            }
        }
        //取消recycleview的滑动
        binding.rvSubDeptList.setHasFixedSize(true)
        binding.rvSubDeptList.isNestedScrollingEnabled = false
        // TODO: 2019/8/14 人员列表
        
        binding.rvDeptMemberList.layoutManager = object : LinearLayoutManager(mContext) {
            override fun canScrollVertically(): Boolean {
                return false
            }
        }
        //取消recycleview的滑动
        binding.rvDeptMemberList.setHasFixedSize(true)
        binding.rvDeptMemberList.isNestedScrollingEnabled = false
        val layoutManager = LinearLayoutManager(this)
        layoutManager.orientation = LinearLayoutManager.HORIZONTAL
        
        binding.rvLevelList.layoutManager = layoutManager
        deptSettingIcon = findViewById(R.id.iv_dept_set_icon)
    }

    private lateinit var deptSettingIcon: View

    override fun initLogic() {
        super.initLogic()
        adapter = ChildDepartmentAdapter(mContext!!, childDepartmentDataList, "")
        binding.rvSubDeptList.adapter = adapter
        memberAdapter = OrgMemberListAdapterNew(
            mContext!!, depMemberList as List<Member>,
            "orgchart", companyId
        )
        binding.rvDeptMemberList.adapter = memberAdapter
        levelAdapter = DepLevelListAdapter(mContext!!, levelList)
        levelAdapter.setClickListener(object : DepLevelListAdapter.ItemClickListener {
            override fun onItemClick(position: Int) {
                if (!levelList.isNullOrEmpty() && position in levelList.indices) {
                    depId = levelList[position].deptId
                }
                refreshOrgChartData(depId)// 面包屑跳转指定层级
            }
        })
        binding.rvLevelList.adapter = levelAdapter
        
        binding.search.setOnClickListener(this)
        
        binding.tvImportMember.setOnClickListener(this)
        binding.tvAddDept.setOnClickListener(this)
        deptSettingIcon.setOnClickListener(this)
        refreshOrgChartData(depId)// 默认初始化，加载组织架构数据
    }

    override fun onBackPressed() {
        if (this::bottomListDialog.isInitialized) {
            bottomListDialog.closeDialog()
        }
        if (levelList.isNotEmpty()) {
            if (levelList.size == 1) super.onBackPressed()
            else {
                levelList.removeAt(levelList.lastIndex)
                depId = levelList.last().deptId
                refreshOrgChartData(depId)// 后退键，层级回退，加载组织架构数据
            }

        } else {
            super.onBackPressed()
        }
    }

    override fun onEmptyRefresh() {
        super.onEmptyRefresh()
        refreshOrgChartData(depId)// 组织架构无数据时，点击刷新
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.search -> {
                val intent = Intent(mContext!!, SearchResultActivity::class.java)
                intent.putExtra("companyId", companyId)
                intent.putExtra("type", "searchOrgMember")
                startActivity(intent)
            }
            R.id.toolbar_title_left -> {
                finish()
            }
            com.joinutech.ddbeslibrary.R.id.tv_toolbar_right -> {
                dealMemberLeaveOrMove()
            }
            R.id.tv_add_dept -> {
                addChildDep()
            }
//            R.id.tvImportMember -> {
            R.id.tv_import_member -> {
                val memberIdList = arrayListOf<String>()
                if (depMemberList.isNotEmpty()) {
                    depMemberList.forEach {
                        memberIdList.add(it.userId)
                    }
                }
                ARouter.getInstance()
                    .build(RouteOrg.orgImportPersonActivity)// 导入员工
                    .withString("companyId", companyId)
                    .withString("companyName", binding.tvCompanyName.text.toString())
                    .withString("deptId", depId)// 当前部门id
                    .withString("depName", depName)// 当前部门名称
                    .withStringArrayList("depMember", memberIdList)
                    .withString("pageTitle", "导入员工")// 是否需要重新获取数据
                    .navigation()
            }
            R.id.iv_dept_set_icon -> {
                dealDepSet()
            }
        }
    }

    private fun dealIntentContent() {
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                companyId = intent.getStringExtra("companyId") ?: ""
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("type"))) {
                type = intent.getStringExtra("type") ?: ""
            } else {
                if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("depName"))) {
                    depName = intent.getStringExtra("depName") ?: ""
                }
            }
            orgPermission = intent.getBooleanExtra("orgPermission", false)
        }
        if (StringUtils.isNotBlankAndEmpty(type)) {
            if (type == "undoList") {
                setPageTitle("选择入职部门")
                setLeftTitle("暂不处理", this)
            }
        } else {
            if (StringUtils.isNotBlankAndEmpty(depName)) {
                setPageTitle(depName)
            } else {
                setPageTitle("团队架构")
            }
            showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey, View.OnClickListener {
                onBackPressed()
            })
            if (orgPermission) {
                setRightTitle("批量操作", this)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun refreshOrgChartList(eventBusEvent: EventBusEvent<String>) {
        if (eventBusEvent.code == EventBusAction.REFRESH_ORGCHARTLIST) {
            val data = eventBusEvent.data
            if (StringUtils.isNotBlankAndEmpty(data)) {
                when (data) {
                    "move" -> ToastUtil.showCustomToast(
                        null, mContext!!, true,
                        "已批量变更所在部门"
                    )
                    "leave" -> ToastUtil.showCustomToast(
                        null, mContext!!, true,
                        "已批量请离所选员工"
                    )
                    "changeDepName" -> {
                        ToastUtil.showCustomToast(
                            null, mContext!!, true,
                            "已更改部门名称"
                        )
                        //更新面包屑的逻辑，更改当前部门名称
                        if (frontLevel >= 2 && levelList.size >= 2) {
                            //更改了部门名称
                            isChangeDepName = true
                        }
                    }
                    "changeAboveDep", "deleteDep" -> {
                        //更改上级部门,删除部门
                        ToastUtil.showCustomToast(
                            null, mContext!!, true,
                            "已变更上级部门"
                        )
                        //更改或者删除当前部门后，要刷新为上一级部门的详情
                        if (frontLevel >= 2 && levelList.size >= 2) {
                            //再确保下层级为2级
                            depId = levelList[levelList.size - 2].deptId
                            levelList.removeAt((levelList.size - 1))
                            levelAdapter.setSourceList(levelList)
                        }
                    }
                }
            }
            refreshOrgChartData(depId)// 组织架构刷新事件，触发组织架构数据刷新
        }
    }

    /**获取团队部门等信息*/
    private fun refreshOrgChartData(deptId: String) {
        if (deptId == "0") {
            getLoadingDialog("请求团队架构数据", true)
        }
        //拉取团队架构数据，，，，子部门列表
        presenter.getOrgChartDataResult(bindToLifecycle(), accessToken, companyId, deptId,
            onSuccess = {
                dismissDialog()
                dealSuccessResult(it, deptId)
            },
            onError = {
                dismissDialog()
                ToastUtil.show(mContext!!, it)
            })
    }

    //处理数据
    private fun dealSuccessResult(result: OrgChartBean, deptId: String) {
        setShowEmptyView(false)
        //根据是否是1及部门显示面包屑导航的逻辑
        if (deptId == "0") {
            binding.rvLevelList.visibility = View.GONE
            
            binding.lineEmpty.visibility = View.VISIBLE
        } else {
            binding.rvLevelList.visibility = View.VISIBLE
            binding.lineEmpty.visibility = View.GONE
        }
        //根据是否创建者显示设置标识
        if (createId.isBlank()) {
            createId = result.userId
        }
        if (createId == userId!! || orgPermission) {
            deptSettingIcon.visibility = View.VISIBLE
            
            binding.twoBtnsContainRl.visibility = View.VISIBLE
        } else {
            deptSettingIcon.visibility = View.GONE
            binding.twoBtnsContainRl.visibility = View.GONE
        }
        //设置名称
        depName = result.deptName
        binding.tvCompanyName.text = depName
        //处理面包屑显示逻辑
        moreDepLevelShow(result, deptId)
        //处理接口返回的部门列表数据处理
        dealDepDataShow(result)
        //处理接口返回的部门人员列表数据处理
        dealDepMemberShow(result)
    }

    @SuppressLint("SetTextI18n")
    private fun dealDepMemberShow(result: OrgChartBean) {
        depMemberList = result.memberList as ArrayList<Member>
        if (depMemberList.isNotEmpty()) {
            
            binding.tvSubDeptCount.visibility = View.VISIBLE
            binding.tvSubDeptCount.text = "当前部门共有${depMemberList.size}个成员"
            if (orgPermission) {
                setRightTitle("批量操作", this)
            }
        } else {
            binding.tvSubDeptCount.visibility = View.INVISIBLE
            hideRightText()
        }
        memberAdapter.setCreateId(result.userId)
        memberAdapter.setOrgPermission(orgPermission)
        memberAdapter.setDepName(depName)
        memberAdapter.setDepId(depId)
        memberAdapter.setDataSourceList(depMemberList)
    }

    private fun dealDepDataShow(result: OrgChartBean) {
        childDepartmentDataList = result.branchList as ArrayList<Branch>
        if (childDepartmentDataList.isNotEmpty()) {
            
            binding.tvSubDept.visibility = View.VISIBLE
        } else {
            binding.tvSubDept.visibility = View.GONE
        }
        adapter.setSourceList(childDepartmentDataList)
        adapter.setClickListener(object : ItemClickListener {
            override fun onItemClick(position: Int) {

                if (!childDepartmentDataList.isNullOrEmpty() && position in childDepartmentDataList.indices) {
                    depId = childDepartmentDataList[position].deptId
                }

                refreshOrgChartData(depId)// 点击部门，加载部门数据
            }
        })
    }

    @SuppressLint("SetTextI18n")
    private fun moreDepLevelShow(result: OrgChartBean, deptId: String) {
        if (result.deptLevel > frontLevel) {
            // 点击了子部门，增加层级
            //接口返回的部门层级比之前的部门层级高
            //说明是进入了下级部门
            //把接口的部门数据添加
            levelList.add(Branch(deptId, result.deptName))
        } else if (result.deptLevel < frontLevel) {
            // 点击了上级部门，减少层级
            // update 更新判断逻辑，不再通过level判断，通过部门id获取选中的部门层级
            //接口返回的部门层级比之前的部门层级低
            //说明是返回了高级部门
            //因为返回上级部门，所以上级部门的部门id和名称已经记录在集合中，
            // 只需要取出从1级到接口返回级的数据即可
            val changeLevelList = arrayListOf<Branch>()
            for (branch in levelList) {
                changeLevelList.add(branch)
                if (branch.deptId == deptId) {
                    break
                }
            }
            levelList = changeLevelList
        } else {
            //接口返回的部门层级比之前的部门层级相同
            //说明此时没有改变层级或者是改变了团队名称
            if (isChangeDepName) {
                //改变了同级部门名称
                val last = levelList.removeAt((levelList.size - 1))
                if (last.deptId == deptId) {
                    levelList.add(Branch(deptId, result.deptName))
                }
            }
        }
        frontLevel = result.deptLevel
        //要求部门层级最多加5级
        if (frontLevel >= 10) {
            binding.tvAddDept.visibility = View.GONE
        } else {
            binding.tvAddDept.visibility = View.VISIBLE
        }
        levelAdapter.setSourceList(levelList)

        binding.tvDeptLevel.text = "部门层级：${result.deptLevel}级部门 当前部门及其子部门总人数:${result.count}人"
    }

    private fun dealMemberLeaveOrMove() {
        val view = View.inflate(
            mContext, com.joinutech.ddbeslibrary.R.layout.dialog_personicon_bottom_layout,
            null
        )
        val dialog = BottomDialogUtil.showBottomDialog(
            mContext!!,
            view, Gravity.BOTTOM
        )
        val mTakePicture = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.takePicture)
        val mSelectPicture = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.selectPicture)
        val mDismiss = view.findViewById<TextView>(R.id.dismiss)
        mTakePicture.text = "批量移动"
        mSelectPicture.text = "批量请离"
        mTakePicture.setOnClickListener {
            dialog.dismiss()
            if (depMemberList.isNotEmpty()) jumpToMemberSelect("move")
        }
        mSelectPicture.setOnClickListener {
            dialog.dismiss()
            if (depMemberList.isNotEmpty()) jumpToMemberSelect("leave")
        }
        mDismiss.setOnClickListener {
            dialog.dismiss()
        }
    }

    private fun jumpToMemberSelect(type: String) {
        val intent = Intent(mContext, SelectedMemberActivity::class.java)
        jumpType = type
        intent.putExtra("num", depMemberList.size)
        intent.putExtra("memberList", depMemberList)
        intent.putExtra("type", jumpType)
        intent.putExtra("depName", depName)
        intent.putExtra("createId", createId)
        intent.putExtra("companyId", companyId)
        intent.putExtra("depId", depId)
        startActivity(intent)
    }

    private fun addChildDep() {
        val dialog = MyDialog(
            mContext, 329, 209, "",
            needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0
        )
        val view = View.inflate(mContext, R.layout.dialog_add_childdep, null)
        dialog.setView(view, Gravity.CENTER)
        dialog.show()
        val editText = view.findViewById<EditText>(R.id.depEdit)
        editText.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (s != null) {
                    if (s.length > 20) {
                        //删除指定长度之后的数据
                        s.delete(20, editText.selectionEnd)
                        ToastUtil.show(mContext!!, "你输入的字数已经超过了限制")
                    }

                }
            }

            override fun beforeTextChanged(
                s: CharSequence?,
                start: Int, count: Int, after: Int
            ) {
            }

            override fun onTextChanged(
                s: CharSequence?,
                start: Int, before: Int, count: Int
            ) {
            }

        })
        dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
            override fun clickLeftBtn() {
                dialog.dismiss()
                //隐藏软键盘(只适用于Activity，所以必须给getSystemService加调用的上下文)
                if (<EMAIL> != null
                    && <EMAIL>?.windowToken != null
                )
                    (mContext!!.getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                        .hideSoftInputFromWindow(
                            <EMAIL>!!
                                .windowToken, InputMethodManager.HIDE_NOT_ALWAYS
                        )
            }

        })
        dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
            override fun clickRightBtn() {
                dialog.dismiss()
                createSubDept(editText.text.toString())
            }
        })
    }

    private fun createSubDept(depName: String) {
        if (depName.isNullOrBlank()) {
            return
        }
        getLoadingDialog("添加子部门", false)
        presenter.addChildDep(bindToLifecycle(), accessToken, companyId, depId, depName, {
            Logger.i("添加子部门","==companyId=${companyId}---depId=${depId}---depName=${depName}---")
            dismissDialog()
            ToastUtil.showCustomToast(
                null, mContext!!, true,
                "成功添加子部门"
            )
            refreshOrgChartData(depId)// 添加部门成功后，刷新组织架构数据
        }, {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    private lateinit var bottomListDialog: BottomListDialogHelper<String>
    private fun dealDepSet() {
        fun dismissDialog() {
            if (this::bottomListDialog.isInitialized) {
                bottomListDialog.closeDialog()
            }
        }
        if (!this::bottomListDialog.isInitialized) {
            bottomListDialog = BottomListDialogHelper(this,
                com.joinutech.ddbeslibrary.R.layout.dialog_white_top_corner,
                com.joinutech.ddbeslibrary.R.layout.item_center_text_with_dot, "", arrayListOf("部门设置", "部门排序"),
                onBindItem = { _, data, itemView ->
                    itemView.findViewById<TextView>(R.id.content)?.let {
                        it.typeface = Typeface.DEFAULT_BOLD
                        it.text = data
                    }
                },
                onConfirm = { position, data ->
                    if (position == 0) { // 部门设置
                        val intent = Intent(mContext, DepartmentSetActivity::class.java)
                        if (levelList.size >= 2) {
                            intent.putExtra("depName", levelList[levelList.size - 2].name)
                            intent.putExtra("depId", levelList[levelList.size - 2].deptId)
                            intent.putExtra("ownDepId", depId)
                            intent.putExtra("ownDepName", depName)
                        } else {
                            intent.putExtra("depName", binding.tvCompanyName.text)
                            intent.putExtra("depId", depId)
                        }
                        intent.putExtra("depLevel", frontLevel)
                        intent.putExtra(CommonKeys.COMPANY_ID, companyId)
                        startActivityForResult(intent, 1001)
                    } else { // 部门排序
                        val intent = Intent(mContext, DepartmentSortActivity::class.java)
                        if (levelList.size >= 2) {
                            intent.putExtra("depName", levelList[levelList.size - 2].name)
                            intent.putExtra("depId", levelList[levelList.size - 2].deptId)
                            intent.putExtra("ownDepId", depId)
                            intent.putExtra("ownDepName", depName)
                        } else {
                            intent.putExtra("depName", binding.tvCompanyName.text)
                            intent.putExtra("depId", depId)
                        }
                        intent.putExtra("depLevel", frontLevel)
                        intent.putExtra(CommonKeys.COMPANY_ID, companyId)
                        if (childDepartmentDataList.size > 1) {
                            intent.putExtra("deptList", childDepartmentDataList)
                            intent.putExtra("pageTitle", data)
                            startActivityForResult(intent, 1002)
                        } else {
                            toastShort("部门数量为${childDepartmentDataList.size},无需排序")
                        }
                        dismissDialog()
                    }
                },
                onCancel = {
                    dismissDialog()
                })
            bottomListDialog.initView()
        }
        bottomListDialog.show(
            outSizeEnable = true,
            onDismissListener = DialogInterface.OnDismissListener {
                dismissDialog()
            })
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK && requestCode == 1002) {
            refreshOrgChartData(depId)// 部门排序后，加载组织架构数据
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    //团队已解散
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun orgDissolved(event: EventBusEvent<String>) {
        if (event.code == EventBusAction.Event_DISSOLVED_ORG ||
            event.code == EventBusAction.Event_LEAVE_ORG
        ) {
            if (BaseApplication.getCurrentActivity() == OrganizationChartActivity::class.java.name) {
                if (StringUtils.isNotBlankAndEmpty(event.data) &&
                    StringUtils.isNotBlankAndEmpty(companyId)
                    && event.data == companyId
                ) {
                    val dialog = if (event.code == EventBusAction.Event_DISSOLVED_ORG) {
                        MyDialog(
                            mContext!!, 0, 0,
                            "该团队已解散", needBtnConfirm = true, needBtnCancel = false, bgResourceId = 0
                        )
                    } else {
                        MyDialog(
                            mContext!!, 0, 0,
                            "您已被请离该团队", needBtnConfirm = true, needBtnCancel = false, bgResourceId = 0
                        )
                    }
                    dialog.setBtnLeftText("好的")
                    dialog.setCanceledOnTouchOutside(false)
                    dialog.setCancelable(false)
                    dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
                        override fun clickLeftBtn() {
                            finish()
                        }

                    })
                    dialog.show()
                }
            }
        }
    }
}