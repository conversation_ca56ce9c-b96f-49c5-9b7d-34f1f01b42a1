package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.content.Intent
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.addressbook.constract.OrgDepConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.addressbook.viewModel.OrgImportPersonViewModel
import com.joinutech.ddbeslibrary.bean.OrgImportDeptBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.widget.activity.BaseOrgSelectMemberActivity
import javax.inject.Inject
import javax.inject.Named

/**
 * description ： 团队导入人员
 * author: 黄洁如
 * date : 2019/10/23
 *
 * // TODO: 2021/6/24 16:24 复制于 TimeMemberSetingActivity 此两页面bug完全一致，都需要处理
 *  bug：折叠当前部门成员，点击子部门闪退
 *  考勤组选择参与成员 1 未加入考勤组 在其他考勤组 当前部门id为0 是名称后增加“（选择则将全公司员工都选中）”，
 *                  首层不显示level
 *                  子部门有分隔栏（第一个部门元素增加标题栏）
 *                  子部门展开后显示 上级部门名称的子部门
 *  团队导入成员 2
 *  视频会议选择参与人员 3
 */
@Route(path = RouteOrg.orgImportPersonActivity)
class OrgImportPersonActivity : BaseOrgSelectMemberActivity() {

    private lateinit var viewModel: OrgImportPersonViewModel

    override fun initView() {
        super.initView()
        DaggerAddressbookComponent.builder().build().inject(this)
        viewModel = getModel(OrgImportPersonViewModel::class.java)
        getObservable()
    }

    @Inject
    @field:Named(AddressbookUtil.ORGCHARTDEP_PRESENTER)
    lateinit var presenter: OrgDepConstract.OrgDepPresenter

    override fun onBackSelectResult(memberIds: ArrayList<String>) {
        importPersonEvent(memberIds)
    }

    /**
     * 请求人员数据
     */
    override fun loadMember(companyId: String, deptId: String) {
        getLoadingDialog("正在获取信息..", false)
        viewModel.queryMembersNew(accessToken!!, companyId, deptId, bindToLifecycle())
    }

    @SuppressLint("SetTextI18n")
    private fun getObservable() {
        viewModel.queryMmebersNewSuccessObservable.observe(this,
                androidx.lifecycle.Observer { bean ->
                    onLoadData(bean)
                })
        viewModel.queryMmebersNewErrorObservable.observe(this,
                androidx.lifecycle.Observer {
                    dismissDialog()
                    ToastUtil.show(mContext!!, it)
                })
    }

    override fun goNextLevel(bean: OrgImportDeptBean) {
        // TODO: 2021/6/24 16:29 复用当前页面加载数据，本地显示二级部门，搜索也是同理
        //  再次跳转当前页面时，不会再发送needLoad字段，所以只有复用当前加载组织架构数据
        val intent = Intent(mContext, OrgImportPersonActivity::class.java)
        intent.putExtra("bean", bean)// 展示二级部门信息
        intent.putExtra("selectData", selectData)// 当前选中数据结构体
        intent.putExtra("companyId", companyId)
        intent.putExtra("companyName", companyName)
        intent.putExtra("deptId", targetDeptId)// 需要移入员工的部门id
        intent.putExtra("depName", targetDeptName)// 需要移入员工的部门名称
        intent.putExtra("pageTitle", titleInfo)
        intent.putExtra("confirmTitle", confirmTitle)
        intent.putExtra("needLoad", false)// 是否需要重新获取数据
//                        intent.putExtra("depMember", selectData.unSelectMembers)// 当前部门中已经存在的成员id
//                        intent.putExtra("allmembers", allmembers)// 当前部门所有成员
//                        intent.putExtra("selList", sel_list)// 本次选中成员
        intent.putExtra("pageFlag", pageFlag)
        intent.putExtra("isNeedEdit", isNeedEdit)
        intent.putExtra("topHintValue", topHintValue)
        intent.putExtra("maxSelect", maxSelect)
        startActivityForResult(intent, 2333)
    }

    override fun updateLevelInfo(count: Int) {
        if (maxSelect > 0) {
            tvSelectCount.text = "已选择" + count + "人，最多选择${maxSelect}人"
        } else {
            tvSelectCount.text = "已选择" + count + "人"
        }
    }

    private fun importPersonEvent(userIds: ArrayList<String>) {
        val dialog = MyDialog(mContext, 280, 166,
                "确认将选中的${userIds.size}名员工导入到本部门(${targetDeptName})内吗",
                needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0)
        dialog.show()
        dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
            override fun clickLeftBtn() {
                dialog.dismiss()
            }
        })
        dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
            override fun clickRightBtn() {
                dialog.dismiss()
                translateMemberDepAction(userIds)
            }
        })
    }

    /**
     * 批量导入员工时处理
     *
     * 先检查考勤关联组
     * 再提示是否更新考勤组
     *
     * 最后完成人员移动
     * */
    private fun translateMemberDepAction(userIds: ArrayList<String>) {

        /**导入成功后刷新团队相关信息*/
        fun dealChangeDepSuccess() {
            EventBusUtils.sendEvent(
                    EventBusEvent(EventBusAction.REFRESH_ORGCHARTLIST, "move"))
            closeAll()// 导入成员成功
        }

        /**批量导入人员到部门中*/
        fun changeMemberDept() {
            getLoadingDialog("更换部门", false)
            presenter.changeMmeberDep(bindToLifecycle<Result<Any>>(),
                    accessToken!!, companyId, targetDeptId, userIds,
                    onSuccess = {
                        dismissDialog()
                        dealChangeDepSuccess()
                    },
                    onError = {
                        dismissDialog()
                        ToastUtil.show(mContext!!, it)
                    })
        }

        /** ischange 0保持原考勤组 1变更为关联部门考勤组*/
        fun updateAttendGroup(isChange: Int = 0) {
            getLoadingDialog("批量更新用户考勤组", false)
            presenter.updateAttendanceGroup(bindToLifecycle(), accessToken!!,
                    companyId, targetDeptId, userIds, isChange,
                    onSuccess = {
                        dismissDialog()
                        changeMemberDept()
                    },
                    onError = {
                        dismissDialog()
                        ToastUtil.show(mContext!!, it)
                    })
        }

        fun showChangeAttendGroupDialog() {
            val dialog = MyDialog(mContext!!, 280, 140,
                    "变更部门的员工是否更新为新部门对应的考勤规则？",
                    needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0)
            dialog.setCanceledOnTouchOutside(false)
            dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
                override fun clickRightBtn() {
                    dialog.dismiss()
                    updateAttendGroup(1)
                }
            })
            dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
                override fun clickLeftBtn() {
                    dialog.dismiss()
                    updateAttendGroup()
                }
            })
            dialog.show()
        }

        getLoadingDialog("校验用户是否存在考勤组", false)
        presenter.validateAttendanceGroup(bindToLifecycle(),
                accessToken!!, companyId, targetDeptId, userIds,
                onSuccess = {
                    dismissDialog()
                    if (it.isDept) {
                        if (it.isExist) {
                            showChangeAttendGroupDialog()
                        } else {
                            updateAttendGroup(1)
                        }
                    } else {
                        changeMemberDept()
                    }
                },
                onError = {
                    dismissDialog()
                    ToastUtil.show(mContext!!, it)
                })
    }

}