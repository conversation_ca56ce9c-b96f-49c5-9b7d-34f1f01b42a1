package com.joinutech.addressbook.view

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.addressbook.R
import com.joinutech.addressbook.databinding.ActivityAddfriendBinding
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.PermissionUtils
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.SCAN_ADDFRIEND
import com.joinutech.ddbeslibrary.utils.ScanResultUtil.dealScanResult
import com.joinutech.ddbeslibrary.utils.ToastUtil
//import com.xys.libzxing.zxing.activity.CaptureActivity
//tcp添加好友
@Route(path = RouteOrg.addFriendActivity)
class AddFriendActivity : MyUseBindingActivity<ActivityAddfriendBinding>() {

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        setPageTitle("添加好友")
    }

    override fun initView() {
        showToolBarLine()
        whiteStatusBarBlackFont()
    }

        override fun initLogic() {
        super.initLogic()
        binding.scanLayout.setOnClickListener(this)
        binding.phoneAddressLayout.setOnClickListener(this)
            binding.search.setOnClickListener(this)
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.
            scanLayout -> {
                //去扫码添加好友
                //跳转扫描页面
                val perms = arrayOf(Manifest.permission.CAMERA,
                        Manifest.permission.READ_EXTERNAL_STORAGE
                        , Manifest.permission.WRITE_EXTERNAL_STORAGE)
                PermissionUtils.requestPermissionActivity(
                        this@AddFriendActivity, perms, "", {
//                    startActivityForResult(Intent(mContext, CaptureActivity::class.java), SCAN_ADDFRIEND)
                }, {
                    ToastUtil.show(mContext!!, "二维码扫描需要相机权限")
                } , preTips = "需要你同意使用相机权限和存储权限，才能使用二维码扫描功能")
            }
            binding.phoneAddressLayout -> {
                //去手机通讯录
                val intent = Intent(mContext!!, PhoneContactActivity::class.java)
                startActivity(intent)
            }
            binding.search -> {
                //去使用手机号搜索好友
                val intent = Intent(mContext!!, SearchResultActivity::class.java)
                intent.putExtra("type", "phoneSearch")
                startActivity(intent)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == SCAN_ADDFRIEND && resultCode == Activity.RESULT_OK) {
            //二维码包含信息
            dealScanResult(data, mContext!!)
        }
    }

    override val contentViewResId: Int
        get() = R.layout.activity_addfriend

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityAddfriendBinding {
        return ActivityAddfriendBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean {
        return true
    }



    override fun openArouterReceive(): Boolean {
        return false
    }
}