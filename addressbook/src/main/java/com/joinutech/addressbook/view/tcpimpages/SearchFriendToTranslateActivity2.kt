package com.joinutech.addressbook.view.tcpimpages

import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.EditorInfo
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.ddbes.library.im.imtcp.ConstantImMsgType
import com.ddbes.library.im.imtcp.dbbean.Message
import com.ddbes.library.im.imtcp.dbbean.Session
import com.ddbes.library.im.imtcp.dbope.SessionDaoOpe
import com.ddbes.library.im.imtcp.imservice.translatorhelper.TranslatorHelper
import com.joinutech.addressbook.R
import com.joinutech.addressbook.databinding.ActivitySearchFriendToTranslate2Binding
import com.joinutech.addressbook.view.tcpimpages.madapter.SearchFriendListAdapter
import com.joinutech.addressbook.view.tcpimpages.util.PersonLetterComparator
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.*
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.widget.wavesidebar.PinnedHeaderDecoration
import io.reactivex.Observable
import io.reactivex.ObservableOnSubscribe
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.*
import kotlin.collections.ArrayList

//提供的是会话列表供选择
@Route(path = RouteOrg.searchFriendToTranslateActivity)
class SearchFriendToTranslateActivity2 : MyUseBindingActivity<ActivitySearchFriendToTranslate2Binding>() {
    override val contentViewResId: Int
        get() = R.layout.activity_search_friend_to_translate2

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivitySearchFriendToTranslate2Binding {
        return ActivitySearchFriendToTranslate2Binding.inflate(layoutInflater)
    }

    @Autowired
    @JvmField
    var companyId: String = ""

    @Autowired
    @JvmField
    var companyName: String = ""

    @Autowired
    @JvmField
    var companyLogo: String = ""

    @Autowired
    @JvmField
    var type: String = ""

    @Autowired
    @JvmField
    var ddMessage: Message? = null

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return true
    }

    private lateinit var emptyPage: PageEmptyView
    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        when (type) {
            "transMsg", "transGroupMsg", "addOrgMsg" -> {
                if (type == "addOrgMsg") {
                    setPageTitle("邀请担当好友")
                } else {
                    setPageTitle("选择担当好友")
                }
                setRightTitle("发送", this)
            }
        }
    }


    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        binding.sessionRecycler.layoutManager = LinearLayoutManager(this)
        val decoration = PinnedHeaderDecoration()
        decoration.registerTypePinnedHeader(1) { _, _ -> true }
        binding.sessionRecycler.addItemDecoration(decoration)
        when (type) {
            "transMsg", "transGroupMsg" -> {
                binding.searchInclueLayout.cancel.visibility = View.VISIBLE
                binding.searchInclueLayout.search.hint = "搜索担当好友"
                binding.searchInclueLayout.delete.setImageResource(com.joinutech.ddbeslibrary.R.drawable.del_img)
            }
            "addOrgMsg" -> {
                binding.searchInclueLayout.search.hint = "输入要搜索的用户名"
                binding.searchInclueLayout.cancel.visibility = View.GONE
                binding.searchInclueLayout.delete.setImageResource(R.drawable.icon_delete_square)
            }
        }
        binding.searchInclueLayout.delete.visibility = View.INVISIBLE

        emptyPage = findViewById(R.id.layout_empty_layout)
    }

    private val personList = arrayListOf<PersonSearchBean>()
    private val choosedPersonList = arrayListOf<PersonSearchBean>()

    lateinit var mAdapter: SearchFriendListAdapter
    override fun initLogic() {
        super.initLogic()
        mAdapter = SearchFriendListAdapter(this, personList)
        // RecyclerView设置相关
        binding.sessionRecycler.adapter = mAdapter
        getAllUsers()
        initRecyclerView()
        initSearchAction()
        binding.searchInclueLayout.cancel.setOnClickListener(this)
        binding.searchInclueLayout.delete.setOnClickListener(this)
    }

    private fun initRecyclerView() {
        if (StringUtils.isNotBlankAndEmpty(type)) {
            /**适配器点击监听*/
            mAdapter.setItemClickListener(object : SearchFriendListAdapter.OnClickPersonListener {
                override fun onClick(view: View, position: Int) {
                    val personBean = personList.get(position)
                    when (personBean.sessionType) {
                        1 -> {
                            if (personBean.chatStatus == 1) {
                                ToastUtil.show(this@SearchFriendToTranslateActivity2, "该用户已注销")
                                return
                            }
                        }
                        2, ConstantImMsgType.SSChatMessageTypeChatGroupNotice -> {
                            if (personBean.chatStatus == 1) {
                                ToastUtil.show(this@SearchFriendToTranslateActivity2, "该群组已解散")
                                return
                            }
                        }
                    }
                    when (type) {
                        "transMsg", "transGroupMsg", "addOrgMsg" -> {

                            if (!personBean.isChoosed) {
                                if (choosedPersonList.size < 10) {
                                    personBean.isChoosed = !personBean.isChoosed
                                    dealChoosedPersonList(personBean)
                                    mAdapter.notifyItemChanged(position)
                                } else {
                                    if (type == "addOrgMsg") {
                                        ToastUtil.show(mContext!!, "最多可邀请给10个好友")
                                    } else {
                                        ToastUtil.show(mContext!!, "最多可转发给10个好友")
                                    }
                                }
                            }else{
                                personBean.isChoosed = !personBean.isChoosed
                                dealChoosedPersonList(personBean)
                                mAdapter.notifyItemChanged(position)
                            }
                        }
                    }
                }
            })
        }
    }

    private fun getAllUsers() {
        getLoadingDialog("", true)
        searchFriendList()
    }

    private fun dealChoosedPersonList(person: PersonSearchBean) {
        if (person.isChoosed == false) {
            if (choosedPersonList.contains(person)) {
                choosedPersonList.remove(person)
            }
        } else {
            if (!choosedPersonList.contains(person)) {
                choosedPersonList.add(person)
            }
        }
    }

    private fun searchFriendList(keyWord: String? = null) {
        //tcp新版im转发列表显示的是会话列表
        Observable.create(ObservableOnSubscribe<List<Session>> { it2 ->
            val sessionList = SessionDaoOpe.instance.queryAllByUid(this, userId)

            if (keyWord == null) {
                it2.onNext(sessionList.filter {
                    it.sessionType == 1 || it.sessionType == 2
                            || it.sessionType == ConstantImMsgType.SSChatMessageTypeChatGroupNotice
                })
            } else if (keyWord.isNotBlank()) {
                it2.onNext(sessionList.filter {
                    (it.sessionType == 1 || it.sessionType == 2
                            || it.sessionType == ConstantImMsgType.SSChatMessageTypeChatGroupNotice) && it.name.contains(
                        keyWord
                    )
                })
            }

        })
            .compose(bindToLifecycle())
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ sessionList ->
                Loggerr.i("---验证转发---", "---sessionList.size-" + sessionList.size)
                hideLoading()
                if (sessionList != null && sessionList.isNotEmpty()) {
                    emptyPage.visibility = View.GONE
                    binding.dataLayout.visibility = View.VISIBLE
                    personList.clear()
                    personList.addAll(dealSessionList(sessionList))
                    Loggerr.i("---验证转发---", "---personList.size-" + personList.size)
                    mAdapter.notifyDataSetChanged()
                }
            }, { throwable ->
                hideLoading()
                dealFriendSearchNoResult()
                ToastUtil.show(this, throwable.message.toString())
            })

    }

    private fun dealSessionList(sessionList: List<Session>?): ArrayList<PersonSearchBean> {
        if (sessionList == null) {
            return arrayListOf()
        }
        val list = ArrayList<PersonSearchBean>()
        if (sessionList.isNotEmpty()) {
            for (sessionBean in sessionList) {
                val personBean = PersonSearchBean(sessionBean.name ?:"")
                personBean.headerUrl = sessionBean.headerUrl
                personBean.userId = sessionBean.appChatId
                personBean.sessionType = sessionBean.sessionType
                personBean.sessionId = sessionBean.sessionId
                personBean.chatStatus = sessionBean.chatStatus

                list.add(personBean)
            }

            Collections.sort(list, PersonLetterComparator())
        }
        return list
    }

    private fun dealFriendSearchNoResult() {
        personList.clear()
        binding.dataLayout.visibility = View.GONE
        when (type) {
            "addOrgMsg", "transMsg", "transGroupMsg" -> {
                emptyPage.setContent("未找到符合条件的担当好友")
            }

        }
        emptyPage.show()
    }

    //点击事件
    override fun onNoDoubleClick(v: View) {
        when (v) {
            tv_rightTitle -> {
                dealRightEvent()
            }
            binding.searchInclueLayout.delete -> {
                binding.searchInclueLayout.search.text.clear()
                getAllUsers()
            }
            binding.searchInclueLayout.cancel -> {
                finish()
            }
        }
    }

    //点击发送
    private fun dealRightEvent() {
        fun searchPersonToUserPerson(list: ArrayList<OutMsgPersonBean>) {
            if (choosedPersonList.isNotEmpty()) {
                for (personBean in choosedPersonList) {
                    list.add(
                        OutMsgPersonBean(
                            userId = personBean.userId, relation = true,
                            name = personBean.name, avator = personBean.headerUrl,
                            sessionType = personBean.sessionType, sessionId = personBean.sessionId
                        )
                    )
                }
            }
        }
        when (type) {
            "transMsg", "transGroupMsg" -> {//转发消息
                if (choosedPersonList.isEmpty()) {
                    ToastUtil.show(this, "请先选择好友")
                    return
                }
                showLoading()
                val list = arrayListOf<OutMsgPersonBean>()
                searchPersonToUserPerson(list)
                if (ddMessage != null && list.isNotEmpty()) {
                    ddMessage!!.uid = userId
                    TranslatorHelper.translateMsgToChat(mContext!!, ddMessage!!, list)
                }
            }
            "addOrgMsg" -> {//tcp发送团队邀请给好友
                if (choosedPersonList.isEmpty()) {
                    ToastUtil.show(this, "请先选择好友")
                    return
                }
                showLoading()
                val bean = AddOrgMsgBean(companyId, companyLogo, companyName)
                val list = arrayListOf<OutMsgPersonBean>()
                searchPersonToUserPerson(list)
                TranslatorHelper.sendAddOrgMsgToFriend(
                    mContext!!,
                    userId!!,
                    bean,
                    list,
                    companyName,
                    clickLeft = {
                        hideLoading()
                    },
                    clickRight = {
                        hideLoading()
                    }
                )
//                MsgTranslator.sendAddOrgMsgToFriend(mContext!!, userId!!.toString(), bean, list, companyName)
            }
        }
    }

    /**搜索框搜索事件处理*/
    private fun initSearchAction() {
        binding.searchInclueLayout.search.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val string = s.toString()
                if (StringUtils.isNotBlankAndEmpty(string)) {
                    binding.searchInclueLayout.delete.visibility = View.VISIBLE
                } else {
                    binding.searchInclueLayout.delete.visibility = View.INVISIBLE
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        })
        binding.searchInclueLayout.search.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH ||
                (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER)
            ) {
                getLoadingDialog("", true)
                //输入内容不为空
                val keyWord = binding.searchInclueLayout.search.text.toString().trim()
                if (StringUtils.isNotBlankAndEmpty()) {
                    searchFriendList(keyWord)
                }
                return@setOnEditorActionListener true
            }
            false
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun translateMsgSuccess(event: EventBusEvent<String>) {
        if (event.code == EventBusAction.TRANSMSG_SUCCEESS) {// 监听发送成功事件
            hideLoading()
            finish()
        }
    }

}