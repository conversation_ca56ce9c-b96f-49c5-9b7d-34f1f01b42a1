package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.text.Editable
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.PopupWindow
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.DepViceHeadAdapter
import com.joinutech.addressbook.constract.DepSetConstarct
import com.joinutech.addressbook.constract.OrgDepConstract
import com.joinutech.addressbook.databinding.ActivityDepSetBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.addressbook.view.secret.SecretModel
import com.joinutech.addressbook.view.secret.SecretSettingData
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.Member
import com.joinutech.ddbeslibrary.request.exception.ErrorType
import com.joinutech.ddbeslibrary.utils.CommonKeys
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.MyDialog
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.SET_ABOVE_DEP_NAME
import com.joinutech.ddbeslibrary.utils.SET_DEP_HEAD
import com.joinutech.ddbeslibrary.utils.Spanny
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.utils.toastShort
//import kotlinx.android.synthetic.main.activity_dep_set.*
//import kotlinx.android.synthetic.main.dep_head_item_layout.*
import javax.inject.Inject
import javax.inject.Named

/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName:
 * @Desc: 部门设置页面 设置负责人 和隐私权限配置
 * @Author:
 * @Leader: Ke
 * @CreateTime: 2020/3/20 10:29 - zhaoyy
 */
class DepartmentSetActivity(override val contentViewResId: Int = R.layout.activity_dep_set) :
    MyUseBindingActivity<ActivityDepSetBinding>() {

    private var aboveDepNameText = ""
    private var depLevel: Int = 1
    private var companyId: String = ""

    /**父部门id*/
    private var depId: String = "0"

    /**父部门名称*/
    private var depName = ""

    /**当前部门id*/
    private var ownDepId: String = "0"

    /**当前部门名称*/
    private var ownDepName = ""

    private var depMemberNum = 0
    private var depHeadText = ""
    private var headUserId: String = ""
    private var headPositionId: String = ""
    private var depHeadNameText = ""
    private var list: ArrayList<Member> = arrayListOf()
    private var viceList: ArrayList<Member> = arrayListOf()
    private lateinit var adapter: DepViceHeadAdapter
    private var depHeadIcon = ""

    @Inject
    @field:Named(AddressbookUtil.DEPSET_PRESENTER)
    lateinit var presenter: DepSetConstarct.DepSetPresenter

    @Inject
    @field:Named(AddressbookUtil.ORGCHARTDEP_PRESENTER)
    lateinit var presenter2: OrgDepConstract.OrgDepPresenter

    override fun initImmersion() {
        setPageTitle("部门设置")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        dealIntentContent()
    }

    override fun initView() {
        showToolBarLine()
        whiteStatusBarBlackFont()
        DaggerAddressbookComponent.builder().build().inject(this)
        //2级以上部门才可以修改团队名称和修改上级部门
        if (depLevel >= 2) {
            binding.aboveDepLayout.visibility = View.VISIBLE
            binding.name.isClickable = true
        } else {
            binding.aboveDepLayout.visibility = View.GONE
            binding.name.isClickable = false
        }
        binding.depViceHeadRv.layoutManager = LinearLayoutManager(this)
        initSecretSettingView()
    }

    override fun initLogic() {
        super.initLogic()
        getDepHeadShow()
        adapter = DepViceHeadAdapter(mContext!!, viceList)
        adapter.setClickListener(object : DepViceHeadAdapter.ItemClickListener {
            override fun onEditClick(position: Int) {
                editDepHead(2, position)
            }

            override fun onDeleteClick(position: Int) {
                deleteDepHeadPosition(
                        viceList[position].positionName, viceList[position].positionId)
            }

        })
        binding.depViceHeadRv.adapter = adapter
        binding.name.setOnClickListener(this)
        binding.addDepHeadLayout.setOnClickListener(this)
        binding.addDepViceHeadLayout.setOnClickListener(this)
        binding.aboveDepName.setOnClickListener(this)
        binding.aboveDepNext.setOnClickListener(this)
        binding.depHeadPersonLayout.deptHeadDel.setOnClickListener(this)
        binding.depHeadPersonLayout.ivDeptHeadEditIcon.setOnClickListener(this)
        initViewModel()
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityDepSetBinding {
        return ActivityDepSetBinding.inflate(layoutInflater)
    }

    private fun getDepHeadShow() {
        getLoadingDialog("", false)
        presenter.showDepHeadDetail(bindToLifecycle(), accessToken!!, ownDepId, companyId, {
            dismissDialog()
            list = it.personList as ArrayList<Member>
            dealListData(list)
            initDepData()
        }, {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun dealListData(list: java.util.ArrayList<Member>) {
        depHeadNameText = ""
        headPositionId = ""
        depHeadText = ""
        headUserId = ""
        depHeadIcon = ""
        if (list.isNotEmpty()) {
            if (viceList.isNotEmpty()) viceList.clear()
            list.forEach {
                when (it.level) {
                    1 -> {
                        //正职
                        depHeadNameText = it.positionName
                        headPositionId = it.positionId
                        depHeadText = it.name
                        headUserId = it.userId
                        depHeadIcon = it.headimg
                    }
                    2 -> {
                        //副职
                        viceList.add(it)
                    }
                }
            }
        }
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.addDepHeadLayout -> {
                //增加部门负责人
                addDepHead(1)
            }
            R.id.iv_dept_head_edit_icon -> {
                //编辑部门负责人
                editDepHead(1, 0)
            }
            R.id.dept_head_del -> {
                //删除部门负责人
                deleteDepHeadPosition(depHeadNameText, headPositionId)
            }
            R.id.addDepViceHeadLayout -> {
                //增加部门副负责人
                addDepHead(2)
            }
            R.id.name -> {
                //再次确保2级以上部门才可以修改部门名称
                if (depLevel >= 2) changeDepName()
            }
            R.id.iv_right -> {
                //显示删除部门的popwindow
                showApprovalPopup()
            }
            R.id.aboveDepName -> {
                //修改上级部门
                changeAboveDep()
            }
            R.id.aboveDepNext -> {
                //修改上级部门
                changeAboveDep()
            }
            R.id.depMemberProtect -> {//切换隐私开关
                if (localSecretSetting == null) {
                    localSecretSetting = SecretSettingData(allowDeptIds = "", allow_type = 0, companyId = companyId, deptId = ownDepId)
                }
                if (binding.sbDepMemberProtect.isChecked) {
                    localSecretSetting!!.allow_type = 0
                } else {
                    localSecretSetting!!.allow_type = 1
                }
                updateSecretSetting(localSecretSetting!!)
            }
            R.id.depMemberProtectRange -> {//选择隐私可见部门
                if (localSecretSetting == null) {
                    localSecretSetting = SecretSettingData(allowDeptIds = "", allow_type = 0, companyId = companyId, deptId = ownDepId)
                }
                val temp = arrayListOf<String>()
                if (localSecretSetting!!.allowDeptIds.contains(",")) {
                    val list = localSecretSetting!!.allowDeptIds.split(",")
                    if (!list.isNullOrEmpty()) {
                        temp.addAll(list)
                    }
                }
                ARouter.getInstance().build(RouteOrg.ORG_SECRET_VISIBLE_RANGE)
                        .withInt(CommonKeys.PAGE_TYPE, 0)
                        .withInt(CommonKeys.POSITION, localSecretSetting!!.allow_type - 1)
                        .withString(CommonKeys.COMPANY_ID, companyId)
                        .withSerializable(CommonKeys.EXTRA_DATA, localSecretSetting!!)
                        .withString(CommonKeys.TITLE_NAME, "可见范围")
                        .withString(CommonKeys.RIGHT_TITLE, "完成")
                        .withStringArrayList(CommonKeys.DATA, temp)
                        .withStringArrayList("source", data)
                        .navigation(this, RouteOrg.CODE_DEPT_VISIBLE_SET)

//                val intent = Intent(this, SecretOpenRangeActivity::class.java)
//                intent.putExtra(CommonKeys.EXTRA_DATA, localSecretSetting)
//
//                intent.putExtra(CommonKeys.PAGE_TYPE, 0)
//                intent.putExtra(CommonKeys.POSITION, localSecretSetting!!.allow_type - 1)
//
//                intent.putExtra("source", data)
//                intent.putStringArrayListExtra(CommonKeys.DATA, temp)
//
//                intent.putExtra(CommonKeys.COMPANY_ID, companyId)
//
//                intent.putExtra(CommonKeys.TITLE_NAME, "可见范围")
//                intent.putExtra(CommonKeys.RIGHT_TITLE, "完成")
//                startActivityForResult(intent, CODE_DEPT_VISIBLE_SET)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                SET_DEP_HEAD -> dealDepHeadReturnResult(data)
                SET_ABOVE_DEP_NAME -> dealAboveDepReturnResult(data)
                RouteOrg.CODE_DEPT_VISIBLE_SET -> data?.let {
                    val position = it.getIntExtra(CommonKeys.POSITION, 0)
                    val temp: ArrayList<String> = it.getStringArrayListExtra(CommonKeys.RESULT_DATA) ?: arrayListOf()
                    val deptIds = temp.joinToString(separator = ",")

                    if (localSecretSetting == null) {
                        localSecretSetting = SecretSettingData(
                                allowDeptIds = "", allow_type = 0,
                                companyId = companyId, deptId = ownDepId)//当前部门设置指定开放信息部门
                    }
                    if (StringUtils.isNotBlankAndEmpty(deptIds)) {
                        localSecretSetting!!.allowDeptIds = deptIds
                    }
                    localSecretSetting!!.allow_type = position + 1
                    updateView(localSecretSetting!!)
                }
            }
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return false
    }

    private fun dealIntentContent() {
        if (intent != null) {
            //团队名称（若只有1级，则为当前部门的名称，否则为上级部门的名称）
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("depName"))) {
                depName = intent.getStringExtra("depName") ?: ""
            }
            //当前部门的团队名称（2级部门以上才有）
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("ownDepName"))) {
                ownDepName = intent.getStringExtra("ownDepName") ?: ""
            }
            //当前部门等级
            if (intent.getIntExtra("depLevel", 0) != 0) {
                depLevel = intent.getIntExtra("depLevel", 0)
                if (depLevel != 1) {
                    //2级部门以上才有删除的权利
                    setRightImage(com.joinutech.ddbeslibrary.R.drawable.icon_more_black, this)
                }
            }
            //企业ID
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra(CommonKeys.COMPANY_ID))) {
                companyId = intent.getStringExtra(CommonKeys.COMPANY_ID) ?: ""
            }
            //团队ID（若只有1级，则为当前部门的ID，否则为上级部门的ID）
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("depId"))) {
                depId = intent.getStringExtra("depId") ?: ""
            }
            //当前部门的团队ID（2级部门以上才有）
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("ownDepId"))) {
                ownDepId = intent.getStringExtra("ownDepId") ?: ""
            }
        }
    }

    private fun initDepData() {
        //记录成员数量判断是否可点击选择负责人
        depMemberNum = list.size
        //初始化当前部门名称和上级部门名称
        if (depLevel >= 2) {
            binding.name.text = ownDepName
            aboveDepNameText = depName
        } else {
            binding.name.text = depName
        }
        binding.aboveDepName.text = aboveDepNameText
        //正职负责人1个
        if (StringUtils.isNotBlankAndEmpty(depHeadNameText)) {
            binding.depHeadPersonLayout.deptHeadLayout.visibility = View.VISIBLE
            binding.addDepHeadLayout.visibility = View.GONE

            binding.depHeadPersonLayout.tvDeptHeadName.text = depHeadNameText
            if (StringUtils.isEmpty(headUserId)) {
                //只设了岗位没设人
                depHeadText = "未设置人员"

                binding.depHeadPersonLayout.ivDeptHeadIcon.visibility = View.GONE
                binding.depHeadPersonLayout.tvDeptHeadEmpty.visibility = View.VISIBLE
            } else {
                binding.depHeadPersonLayout.ivDeptHeadIcon.visibility = View.VISIBLE
                ImageLoaderUtils.loadImage(mContext!!, binding.depHeadPersonLayout.ivDeptHeadIcon, depHeadIcon)
                binding.depHeadPersonLayout.tvDeptHeadEmpty.visibility = View.GONE
                binding.depHeadPersonLayout.deptHeadName.text = depHeadText
            }
        } else {
            binding.depHeadPersonLayout.deptHeadLayout.visibility = View.GONE

            binding.depHeadPersonLayout.deptHeadLayout.visibility = View.VISIBLE
        }
        //副职负责人最多10个
        binding.addDepViceHeadLayout.visibility = View.VISIBLE
        if (viceList.isNotEmpty()) {
            binding.depViceHeadRv.visibility = View.VISIBLE
            if (viceList.size >= 10) {
                binding.addDepViceHeadLayout.visibility = View.GONE
            }
            if (viceList.size > 10) {
                val list = arrayListOf<Member>()
                for (i in 0..10) {
                    list.add(viceList[i])
                }
                adapter.setSourceList(list)
            } else {
                adapter.setSourceList(viceList)
            }
        } else {
            binding.depViceHeadRv.visibility = View.GONE
        }
    }

    private fun changeDepName() {
        val dialog = MyDialog(mContext, 329, 209, "",
                true, needBtnCancel = true, bgResourceId = 0)
        val view = View.inflate(mContext, R.layout.dialog_add_childdep, null)
        dialog.setView(view, Gravity.CENTER)
        dialog.show()
        val editText = view.findViewById<EditText>(R.id.depEdit)
        val textView = view.findViewById<TextView>(R.id.toDoText)
        textView.text = "修改部门名称"
        editText.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (s != null) {
                    if (s.length > 10) {
                        ToastUtil.show(mContext!!, "你输入的字数已经超过了限制")
                    }
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

        })
        dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
            override fun clickLeftBtn() {
                dialog.dismiss()
                if (<EMAIL> != null
                        && <EMAIL>?.windowToken != null)
                    (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                            .hideSoftInputFromWindow(<EMAIL>!!
                                    .windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
            }

        })
        dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
            override fun clickRightBtn() {
                dialog.dismiss()
                ownDepName = editText.text.toString()
                getLoadingDialog("修改名称", false)
                presenter.changeDepName(bindToLifecycle(), accessToken!!,
                        companyId, ownDepId, ownDepName, {
                    dismissDialog()
                    ToastUtil.showCustomToast(null, mContext!!, true,
                            "修改成功")
                        binding.name.text = ownDepName
                    EventBusUtils.sendEvent(
                            EventBusEvent(
                                    EventBusAction.REFRESH_ORGCHARTLIST, "changeDepName"))
                }, {
                    dismissDialog()
                    ToastUtil.show(mContext!!, it)
                })
            }

        })
    }

    private fun addDepHead(type: Int) {
        if (depMemberNum == 0) {
            ToastUtil.showCustomToast(null, mContext!!,
                    true, "当前部门暂无成员")
        } else {
            // TODO: 2021/7/9 增加负责人跳转
            val intent = Intent(mContext, UpdateDepHeadActivity::class.java)
            intent.putExtra("type", type)
            intent.putExtra("memberList", list)
            intent.putExtra("depId", ownDepId)
            intent.putExtra("companyId", companyId)
            startActivityForResult(intent, SET_DEP_HEAD)
        }
    }

    private fun editDepHead(type: Int, position: Int) {
        if (depMemberNum == 0) {
            ToastUtil.showCustomToast(null, mContext!!,
                    true, "当前部门暂无成员")
        } else {
            val intent = Intent(mContext, UpdateDepHeadActivity::class.java)
            intent.putExtra("type", type)
            if (type == 2) {
                // TODO: 2021/7/9 副职负责人
                //取出副职负责人的信息
                intent.putExtra("depHeadNameText", adapter.mData[position].positionName)
                intent.putExtra("depHead", adapter.mData[position].name)
                intent.putExtra("headUserId", adapter.mData[position].userId)
                intent.putExtra("positionId", adapter.mData[position].positionId)
            } else {
                // TODO: 2021/7/9 正职负责人
                intent.putExtra("depHeadNameText", depHeadNameText)
                intent.putExtra("depHead", depHeadText)
                intent.putExtra("headUserId", headUserId)
                intent.putExtra("positionId", headPositionId)
            }
            intent.putExtra("memberList", list)
            intent.putExtra("depId", ownDepId)
            intent.putExtra("companyId", companyId)
            startActivityForResult(intent, SET_DEP_HEAD)
        }
    }

    private fun changeAboveDep() {
        //只要转换上级部门都是层级大于等于2级的部门
        val intent = Intent(mContext, OrgDepartmentActivity::class.java)
        intent.putExtra("type", "changeAboveDep")
        intent.putExtra("depLevel", depLevel)
        //有自己的id，ownID，此时depID就为父级的id
        //此时depName为上级的depName，自己的name为ownDepName
        intent.putExtra("ownDepId", ownDepId)
        intent.putExtra("changeDepName", ownDepName)
        //传上级部门的id，去判断不能移动自己上级下面
        intent.putExtra("depId", depId)// 转移部门需要部门id
        intent.putExtra("companyId", companyId)
        startActivityForResult(intent, SET_ABOVE_DEP_NAME)
    }

    @SuppressLint("InflateParams", "SetTextI18n")
    private fun showApprovalPopup() {
        val mAddPopupLayout = LayoutInflater.from(mContext)
                .inflate(com.joinutech.ddbeslibrary.R.layout.dep_delete_popup_layout_own, null)
        val mAddPopup = PopupWindow(mAddPopupLayout, ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT)
        mAddPopup.isOutsideTouchable = true
        mAddPopup.isFocusable = true
        mAddPopup.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        mAddPopupLayout.setOnClickListener {
            mAddPopup.dismiss()
        }
        mAddPopup.showAtLocation(baseView!!, Gravity.NO_GRAVITY, 0, 0)
        val delLayout = mAddPopupLayout.findViewById<ConstraintLayout>(com.joinutech.ddbeslibrary.R.id.cl_del_layout)
        delLayout.setOnClickListener {
            mAddPopup.dismiss()
            val content = "你确认要删除部门"
            val dialog = MyDialog(mContext, 310, 207, content,
                    needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0)
            val view = View.inflate(mContext!!, R.layout.dialog_delete_dep, null)
            dialog.setView(view, Gravity.CENTER)
            val depName = view.findViewById<TextView>(R.id.tv_dept_name)
            val ownDepName = view.findViewById<TextView>(R.id.depDecText)
            val spanny = Spanny().append("你确认要删除部门 ")
                    .append("${binding.name.text}",
                            ForegroundColorSpan(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color107EEB))
                    ).append("吗？")
            ownDepName.text = "部门中的员工将被移动到 $aboveDepNameText 部门中"
            depName.text = spanny
            dialog.show()
            dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
                override fun clickLeftBtn() {
                    dialog.dismiss()
                }

            })
            dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
                override fun clickRightBtn() {
                    dialog.dismiss()
                    onDeptDelete()
                }
            })
        }
    }

    /**删除部门逻辑
     *
     * TODO: 2020/7/14 14:08 删除部门，前置调用
     * 先确定是不是存在关联考勤组，存在则提示是否切换考勤组，确定切换考勤规则，否 则 保持原来考勤规则
     * 不存在关联考勤组，则直接调用删除接口操作
     */
    fun onDeptDelete() {

        /**删除部门*/
        fun dealDeleteDept() {
            presenter.deleteDep(bindToLifecycle(), accessToken, companyId, ownDepId,
                    onSuccess = {
                        dismissDialog()
                        setResult(Activity.RESULT_OK)
                        EventBusUtils.sendEvent(
                                EventBusEvent(EventBusAction.REFRESH_ORGCHARTLIST, "deleteDep"))
                        finish()
                    },
                    onError = {
                        dismissDialog()
                    })
        }

        /**切换考勤组*/
        fun dealChangeAttend(userIds: ArrayList<String>, isChange: Int = 0) {
            presenter2.updateAttendanceGroup(bindToLifecycle(), accessToken,
                    companyId, ownDepId, userIds, isChange,
                    onSuccess = {
                        dismissDialog()
                        dealDeleteDept()
                    },
                    onError = {
                        dismissDialog()
                        ToastUtil.show(mContext!!, it)
                    })
        }

        /**切换考勤组提示弹窗*/
        fun showChangeAttendDialog(userIds: ArrayList<String>) {
            val dialog = MyDialog(mContext!!, 280, 140,
                    "变更部门的员工是否更新为新部门对应的考勤规则？",
                    needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0)
            dialog.setCanceledOnTouchOutside(false)
            dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
                override fun clickRightBtn() {
                    dialog.dismiss()
                    dealChangeAttend(userIds, 1)
                }
            })

            dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
                override fun clickLeftBtn() {
                    dialog.dismiss()
                    dealChangeAttend(userIds)
                }
            })
            dialog.show()
        }

        showLoading("删除部门")
        val map = hashMapOf<String, Any>()
        map["companyId"] = companyId
        map["deptId"] = ownDepId
        // 验证是否存在关联考勤组
        presenter.checkAttendGroup(bindToLifecycle(), map,
                onSuccess = {
                    dismissDialog()
                    if (it.isDept) {
                        if (it.isExist) {
                            showChangeAttendDialog(it.userIds ?: arrayListOf())
                        } else {
                            dealChangeAttend(it.userIds ?: arrayListOf())
                        }
                    } else {
                        dealDeleteDept()
                    }
                },
                onError = {
                    dismissDialog()
                    ToastUtil.show(mContext!!, "请求失败")
                })
    }


    private fun dealDepHeadReturnResult(data: Intent?) {
        if (data != null) {
            val returnType = data.getIntExtra("type", 0)
            if (returnType == 2) {
                ToastUtil.show(mContext!!, "副职负责人设置完成")
            } else {
                ToastUtil.show(mContext!!, "负责人设置完成")
            }
            //刷新页面数据
            getDepHeadShow()
            EventBusUtils.sendEvent(
                    EventBusEvent(
                            EventBusAction.REFRESH_ORGCHARTLIST, ""))
        }
    }

    private fun deleteDepHeadPosition(positionName: String, positionId: String) {
        val dialog = MyDialog(mContext!!, 280, 185, "",
                needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0)
        val view = View.inflate(mContext!!, R.layout.dialog_delete_dep_head, null)
        val topText = view.findViewById<TextView>(R.id.topText)
        val spanny = Spanny().append("你确认要删除此部门的\n")
                .append("【$positionName】", ForegroundColorSpan(
                        CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color107EEB)))
                .append("职位吗？")
        topText.text = spanny
        dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
            override fun clickRightBtn() {
                dialog.dismiss()
                getLoadingDialog("", true)
                cancelDeaHead(positionId)
            }

        })
        dialog.setView(view, Gravity.CENTER)
        dialog.show()
    }

    private fun cancelDeaHead(positionId: String) {
        getLoadingDialog("", true)
        presenter.deleteDepHeadPosition(bindToLifecycle(), accessToken!!, positionId, {
            dismissDialog()
            //刷新页面数据
            getDepHeadShow()
        }, {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun dealAboveDepReturnResult(data: Intent?) {
        ToastUtil.showCustomToast(null, mContext!!, true, "已变更上级部门")
        if (data != null && StringUtils.isNotBlankAndEmpty(data.getStringExtra("depName"))) {
            depName = data.getStringExtra("depName") ?: ""
            binding.aboveDepName.text = depName
        }
        if (data != null && !data.getStringExtra("depId").isNullOrBlank()) {
            depId = intent.getStringExtra("depId") ?: "" //转移部门后返回部门id
            EventBusUtils.sendEvent(
                    EventBusEvent(
                            EventBusAction.REFRESH_ORGCHARTLIST, "changeAboveDep"))
        }
    }

    private fun initSecretSettingView() {
        binding.depMemberProtect.setOnClickListener(this)
        binding.depMemberProtectRange.setOnClickListener(this)
        binding.sbDepMemberProtect.isClickable = false
//        onSecretSettingChanged(true)
    }

    private fun onSecretSettingChanged(isChecked: Boolean) {
        binding.sbDepMemberProtect.isChecked = isChecked
        if (isChecked) {
            binding.lineRange.visibility = View.VISIBLE
            binding.depMemberProtectRange.visibility = View.VISIBLE
        } else {
            binding.lineRange.visibility = View.GONE
            binding.depMemberProtectRange.visibility = View.GONE
        }
    }

    private lateinit var viewModel: SecretModel

    private fun initViewModel() {
        viewModel = getModel(SecretModel::class.java)
        viewModel.getSecretSettingResult.observe(this, Observer {
            when {
                it.success != null -> {
                    updateView(it.success!!)
                }
                it.errorCode != ErrorType.SUCCESS -> {
                    if (it.extra != null && it.extra is String) {
                        toastShort(it.extra!! as String)
                    } else {
                        toastShort("获取失败")
                    }
                }
                else -> {
                    toastShort("获取失败")
                }
            }
        })
        viewModel.saveSecretSettingResult.observe(this, Observer {
            when {
                it.success != null -> {
                    if (localSecretSetting != null) {
                        updateView(localSecretSetting!!)
                    }
                }
                it.errorCode != ErrorType.SUCCESS -> {
                    if (it.extra != null && it.extra is String) {
                        toastShort(it.extra!! as String)
                    } else {
                        toastShort("获取失败")
                    }
                }
                else -> {
                    toastShort("获取失败")
                }
            }
        })

        if (StringUtils.isNotBlankAndEmpty(companyId) && StringUtils.isNotBlankAndEmpty(ownDepId)) {
            getSecretSetting(companyId, ownDepId)
        }
    }

    private var localSecretSetting: SecretSettingData? = null
    private val data = arrayListOf("所有成员均不可见，包括本部门内成员", "仅本部门及所有直属上级部门可见", "仅本部门内成员可见", "仅指定部门可见")

    private fun updateView(success: SecretSettingData) {
        localSecretSetting = success
        if (success.allow_type > 0) {
            onSecretSettingChanged(true)
            binding.tvProtectRange.text = data[success.allow_type - 1]
        } else {
            onSecretSettingChanged(false)
        }
    }

    private fun updateSecretSetting(data: SecretSettingData) {
        viewModel.saveSecretSetting(data)
    }

    private fun getSecretSetting(companyId: String, deptId: String) {
        viewModel.getSecretSetting(companyId, deptId)
    }

}