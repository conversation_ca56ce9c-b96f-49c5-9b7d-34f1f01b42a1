package com.joinutech.addressbook.view.tcpimpages.madapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.addressbook.R
import com.joinutech.ddbeslibrary.imbean.BlackPersonBean
import com.joinutech.ddbeslibrary.utils.OnNoDoubleClickListener
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.CircleImageView

class BlackListAdapter(var context: Context, val dataList: ArrayList<BlackPersonBean>)
    : RecyclerView.Adapter<BlackListAdapter.BlackViewHolder>() {
    private lateinit var listener: OnClickBlackListener
    override fun getItemCount(): Int {
        return if (dataList.isNullOrEmpty()) 0 else dataList.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BlackViewHolder {
        val itemView = LayoutInflater.from(context)
                .inflate(R.layout.item_black_bean_layout, parent, false)
        return BlackViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: BlackViewHolder, position: Int) {
        holder.initView()
        if (dataList.isNotEmpty()) {
            val blackPersonBean = dataList[position]
            holder.blackName.text = blackPersonBean.userName
            ImageLoaderUtils.loadImage(context, holder.blackHeader, blackPersonBean.avatar)//显示图片
            holder.blackRemove.setOnClickListener(object : OnNoDoubleClickListener {
                override fun onNoDoubleClick(v: View) {
                    listener.onClick(v, position)
                }

            })
        }
    }

    fun setItemClickListener(listener: OnClickBlackListener) {
        this.listener = listener
    }

    interface OnClickBlackListener {
        fun onClick(view: View, position: Int)
    }

    class BlackViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        //        lateinit var time: TextView
        lateinit var blackHeader: CircleImageView
        lateinit var blackName: TextView
        lateinit var blackRemove: TextView


        fun initView() {
            blackHeader = itemView.findViewById(R.id.black_header)
            blackName = itemView.findViewById(R.id.tv_black_name)
            blackRemove = itemView.findViewById(R.id.tv_black_remove)
        }

    }
}

