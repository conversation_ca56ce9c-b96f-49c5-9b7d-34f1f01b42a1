package com.joinutech.addressbook.view.secret

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.joinutech.ddbeslibrary.request.CommonResult
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.RetrofitClient
import com.joinutech.ddbeslibrary.request.RxScheduleUtil
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.marktoo.lib.cachedweb.LogUtil
import io.reactivex.Flowable
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import java.io.Serializable

/**
 * @Description: 隐私保护相关配置
 * @Author: zhaoyy
 * @packageName: 2020-03-10
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */

//data class CommonResult<T>(val success: T? = null, val errorCode: Int = 0, val extra: Any? = null)

data class SecretSettingData(
        /**允许查看信息的部门id[空即为允许所有]*/
        var allowDeptIds: String,
        /**0 未开启 1 2 3 4 对应选项*/
        var allow_type: Int,
        var companyId: String,
        var deptId: String
) : Serializable

/**获取部门数据 实时*/
class DeptInfo(
        var deptId: String,
        var deptName: String,
        var deptLevel: Int,
        var deptList: List<DeptInfo>
) : Serializable

data class DeptHistoryData(
        /*2020-04-13*/
        val date: String,
        /**部门id，根部门为团队id*/
        val deptId: String,
        val headcount: Int,
        val logo: String,
        val name: String,
        /**子部门*/
        val children: List<DeptHistoryData>?,
        /**员工列表*/
        val staffs: List<Staff>?
) {
    fun turnToDeptInfo(): DeptInfo {
        val deptList: List<DeptInfo> = if (children.isNullOrEmpty()) {
            arrayListOf()
        } else {
            children.map { it.turnToDeptInfo() }.toList()
        }
        return DeptInfo(deptId = this.deptId, deptList = deptList, deptName = this.name, deptLevel = 0)
    }
}

data class Staff(
        val date: String,
        val name: String,
        val organizationId: String,
        val position: String,
        val signAmount: Int,
        val signCount: Int,
        val userId: String
)
//data class SettingData(
//        val allowDeptIds: String,
//        val allow_type: Int,
//        val companyId: String,
//        val create_time: Int,
//        val deptId: String
//)

interface SecretApi {
    //    val serverTag = "http://192.168.0.12:8105/"
    companion object {
        const val GATE = "org/"
    }

    @GET(GATE + "company/dept/setting/companyId/{companyId}/deptId/{deptId}")
    fun getDeptSecretSetting(@Path("companyId") companyId: String,
                             @Path("deptId") deptId: String): Flowable<Result<SecretSettingData>>

    @POST(GATE + "company/dept/setting")
    fun postDeptSecretSetting(@Body data: SecretSettingData): Flowable<Result<String>>

    //团队导入员工获取部门下的员工
    @GET(GATE + "personnel/schedule/{companyId}/{deptId}/members")
    fun getDeptList(@Path("companyId") companyId: String,
                    @Path("deptId") deptId: String): Flowable<Result<DeptInfo>>

    //获取团队历史数据
    @GET("att/report/organization/{date}/{companyId}")
    fun getDeptHistoryList(@Path("companyId") companyId: String,
                           @Path("date") date: String): Flowable<Result<DeptHistoryData>>
}

object SecretService {
    private val apiContainer = lazy { RetrofitClient.single_intance.getRxRetrofit().create(SecretApi::class.java) }

    fun getSecretSetting(companyId: String, deptId: String): Flowable<Result<SecretSettingData>> {
        return RxScheduleUtil.rxSchedulerHelper(apiContainer.value.getDeptSecretSetting(companyId, deptId))
    }

    fun saveSecretSetting(data: SecretSettingData): Flowable<Result<String>> {
        return RxScheduleUtil.rxSchedulerHelper(apiContainer.value.postDeptSecretSetting(data))
    }

    fun getDeptList(companyId: String, deptId: String): Flowable<Result<DeptInfo>> {
        return RxScheduleUtil.rxSchedulerHelper(apiContainer.value.getDeptList(companyId, deptId))
    }

    fun getDeptHistoryList(companyId: String, date: String): Flowable<Result<DeptHistoryData>> {
        return RxScheduleUtil.rxSchedulerHelper(apiContainer.value.getDeptHistoryList(companyId, date))
    }
}

class SecretModel : ViewModel() {

    private var _getSecretSettingResult = MutableLiveData<CommonResult<SecretSettingData>>()
    val getSecretSettingResult: MutableLiveData<CommonResult<SecretSettingData>> = _getSecretSettingResult

    fun getSecretSetting(companyId: String, deptId: String) {
        val tag = "获取隐私配置"
        SecretService.getSecretSetting(companyId, deptId)
                .compose(ErrorTransformer.getInstance<SecretSettingData>())
                .subscribe(object : BaseSubscriber<SecretSettingData>() {
                    override fun onError(ex: ApiException) {
                        LogUtil.showLog("$tag 失败")
                        _getSecretSettingResult.value = CommonResult(errorCode = ex.code, extra = ex.message)
                    }

                    override fun onComplete() {
                        LogUtil.showLog("$tag 结束")
                    }

                    override fun onNext(result: SecretSettingData?) {
                        if (result != null) {
                            LogUtil.showLog("$tag 返回数据")
                            _getSecretSettingResult.value = CommonResult(success = result)
                        } else {
                            LogUtil.showLog("$tag 未返回数据")
                            _getSecretSettingResult.value = CommonResult(errorCode = -9999)
                        }
                    }
                })
    }

    private var _saveSecretSettingResult = MutableLiveData<CommonResult<String>>()
    val saveSecretSettingResult: MutableLiveData<CommonResult<String>> = _saveSecretSettingResult

    fun saveSecretSetting(data: SecretSettingData) {
        val tag = "提交隐私配置"
        SecretService.saveSecretSetting(data)
                .compose(ErrorTransformer.getInstance<String>())
                .subscribe(object : BaseSubscriber<String>() {
                    override fun onError(ex: ApiException) {
                        LogUtil.showLog("$tag 失败")
                        _saveSecretSettingResult.value = CommonResult(errorCode = ex.code, extra = ex.message)
                    }

                    override fun onComplete() {
                        LogUtil.showLog("$tag 结束")
                    }

                    override fun onNext(result: String?) {
                        if (result != null) {
                            LogUtil.showLog("$tag 返回数据")
                            _saveSecretSettingResult.value = CommonResult(success = result)
                        } else {
                            LogUtil.showLog("$tag 未返回数据")
                            _saveSecretSettingResult.value = CommonResult(errorCode = -9999)
                        }
                    }
                })
    }

    private var _getDeptListResult = MutableLiveData<CommonResult<DeptInfo>>()
    val getDeptListResult: MutableLiveData<CommonResult<DeptInfo>> = _getDeptListResult

    fun getDeptList(companyId: String, deptId: String) {
        val tag = "获取部门列表"
        SecretService.getDeptList(companyId, deptId)
                .compose(ErrorTransformer.getInstance<DeptInfo>())
                .subscribe(object : BaseSubscriber<DeptInfo>() {
                    override fun onError(ex: ApiException) {
                        LogUtil.showLog("$tag 失败")
                        _getDeptListResult.value = CommonResult(errorCode = ex.code, extra = ex.message)
                    }

                    override fun onComplete() {
                        LogUtil.showLog("$tag 结束")
                    }

                    override fun onNext(result: DeptInfo?) {
                        if (result != null) {
                            LogUtil.showLog("$tag 返回数据")
                            _getDeptListResult.value = CommonResult(success = result)
                        } else {
                            LogUtil.showLog("$tag 未返回数据")
                            _getDeptListResult.value = CommonResult(errorCode = -9999)
                        }
                    }
                })
    }

    fun getDeptHistoryList(companyId: String, date: String) {
        val tag = "获取部门列表"
        SecretService.getDeptHistoryList(companyId, date)
                .compose(ErrorTransformer.getInstance<DeptHistoryData>())
                .subscribe(object : BaseSubscriber<DeptHistoryData>() {
                    override fun onError(ex: ApiException) {
                        LogUtil.showLog("$tag 失败")
                        _getDeptListResult.value = CommonResult(errorCode = ex.code, extra = ex.message)
                    }

                    override fun onComplete() {
                        LogUtil.showLog("$tag 结束")
                    }

                    override fun onNext(result: DeptHistoryData?) {
                        if (result != null) {
                            LogUtil.showLog("$tag 返回数据")
                            _getDeptListResult.value = CommonResult(success = result.turnToDeptInfo())
                        } else {
                            LogUtil.showLog("$tag 未返回数据")
                            _getDeptListResult.value = CommonResult(errorCode = -9999)
                        }
                    }
                })
    }

}