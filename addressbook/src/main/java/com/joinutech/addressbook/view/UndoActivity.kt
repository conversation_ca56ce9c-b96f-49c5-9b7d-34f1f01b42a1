package com.joinutech.addressbook.view

import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.ddbes.library.im.imtcp.ConstantEventCodeUtil
import com.ddbes.library.im.imtcp.Logger
import com.ddbes.library.im.imtcp.dbbean.Message
import com.joinutech.addressbook.AddressProvider
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.CompanyUndoListAdapter
import com.joinutech.addressbook.adapter.CompanyUndoListAdapter2
import com.joinutech.addressbook.adapter.ExternalUndoListAdapter
import com.joinutech.addressbook.adapter.FriendUndoListAdapter
import com.joinutech.addressbook.constract.UndoListConstract
import com.joinutech.addressbook.databinding.ActivityUndoLayoutBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.common.adapter.BaseAdapter
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.CompanyUndoBeanNew
import com.joinutech.ddbeslibrary.bean.CompanyUndoListBean
import com.joinutech.ddbeslibrary.bean.ExternalUndoBean
import com.joinutech.ddbeslibrary.bean.FriendUndoListBean
import com.joinutech.ddbeslibrary.utils.*
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import javax.inject.Inject
import javax.inject.Named

/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName: UndoActivity
 * @Desc: 团队首页 提醒按钮-》待处理页面
 * @Author:
 * @Leader: Ke
 * @CreateTime:
 */
@Route(path = RouteOrg.undo)
class UndoActivity : MyUseBindingActivity<ActivityUndoLayoutBinding>() {

    private var dataListCompany: ArrayList<CompanyUndoBeanNew> = arrayListOf()
    private var dataListFriend: List<FriendUndoListBean> = arrayListOf()
    private var dataListExternal: ArrayList<ExternalUndoBean> = arrayListOf()
    private var companyAdapter: CompanyUndoListAdapter? = null
    private var friendAdapter: FriendUndoListAdapter? = null
    private var externalAdapter: ExternalUndoListAdapter? = null

    @Inject
    @field:Named(AddressbookUtil.UNDOLIST_PRESENTER)
    lateinit var presenter: UndoListConstract.UndoListPresenter
    private var isRefresh = false

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        setPageTitle("待处理")
    }

    override fun initView() {
        whiteStatusBarBlackFont()

        binding.rvOrgApplyList.layoutManager = LinearLayoutManager(mContext!!)
         binding.rvFriendApplyList.layoutManager = LinearLayoutManager(mContext!!)
        binding.applyExternalRecycler.layoutManager = LinearLayoutManager(mContext!!)

//        binding.rvOrgApplyList.setHasFixedSize(true)
        binding.rvOrgApplyList.isNestedScrollingEnabled = false
        binding.rvFriendApplyList.isNestedScrollingEnabled = false
        binding.applyExternalRecycler.isNestedScrollingEnabled = false
        DaggerAddressbookComponent.builder().build().inject(this)
        binding.tvFriendMoreApply.setOnClickListener(this)
    }

    override fun initLogic() {
        super.initLogic()
        companyAdapter = CompanyUndoListAdapter(mContext!!, dataListCompany)
        friendAdapter = FriendUndoListAdapter(mContext!!, dataListFriend)
        externalAdapter = ExternalUndoListAdapter(mContext!!, dataListExternal)
        binding.rvOrgApplyList.adapter = companyAdapter
        binding.rvFriendApplyList.adapter = friendAdapter
        binding.applyExternalRecycler.adapter = externalAdapter
        binding.srlLayout.setEnableRefresh(true)
        binding.srlLayout.setEnableLoadMore(false)
        binding.srlLayout.setRefreshHeader(CustomHeader(mContext))
        loadData()
        binding.srlLayout.setOnRefreshListener {
            binding.srlLayout.finishRefresh(1500)
            isRefresh = true
            loadData()
        }
        //tcp待处理页面点击好友申请的条目
        friendAdapter!!.setOnItemClickListener(object : BaseAdapter.OnItemClickListener {
            override fun onItemClick(view: View, position: Int) {
                val intent = Intent(mContext!!, ApplicationDetailsActivity::class.java)
                val bean = friendAdapter!!.mList!![position]
                val companyBean = CompanyUndoListBean(
                    bean.createTime,
                    bean.avatar, bean.name, bean.id, bean.birthday, "",
                    bean.status, bean.gender, "", bean.message,
                    bean.applyUserId, userName!!
                )
                intent.putExtra("undobean", companyBean)
                intent.putExtra("status", friendAdapter!!.mList!![position].status)
                intent.putExtra("type", "dealAddFriend")
                startActivity(intent)
            }

        })
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun getUndoList(eventBusEvent: EventBusEvent<String>) {
//        if (!isRefresh) {
//            getLoadingDialog("", true)?.show()
//        }
        if (eventBusEvent.code == EventBusAction.REFRESH_APPLIY_LIST) {
            loadData()
        }
    }

    private fun loadData() {
        showLog("刷新申请未处理列表")
        showLoading()
        //搜索“获取待处理数据tcp”
        presenter.getUndoList(bindToLifecycle(), 1, 1, 30, {
            hideLoading()
            //                dismissDialog()
            isRefresh = false
            dataListFriend = it.friendTodoList
            if (it.companies.isNotEmpty()) {
                dataListCompany = it.companies as ArrayList<CompanyUndoBeanNew>
                Logger.i("验证批量处理","===公司总数=${dataListCompany.size}")
            }
            if (!it.external.isNullOrEmpty()) {
                dataListExternal = it.external as ArrayList<ExternalUndoBean>
                Logger.i("验证批量处理","===合作公司总数=${dataListExternal.size}")
            }
            if (dataListCompany.isNullOrEmpty() && dataListFriend.isNullOrEmpty() &&
                dataListExternal.isNullOrEmpty()
            ) {
                binding.layoutEmptyLayout.visibility = View.VISIBLE
                binding.srlLayout.visibility = View.GONE
            } else {
                binding.layoutEmptyLayout.visibility = View.GONE
                binding.srlLayout.visibility = View.VISIBLE
                dealFriendList(dataListFriend)
                dealCompanyList(dataListCompany)
                dealExternalList(dataListExternal)
            }

            if (it.count > 0) {
                AddressProvider.changeUnProcessCount(1)
//                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_UNDO_COUNT, 1))
            } else if (it.companies.isNotEmpty()) {
                var hasWait = false
                for (company in it.companies) {
                    if (company.count > 0) {
                        hasWait = true
                        break
                    }
                }
                if(!it.external.isNullOrEmpty()){
                    hasWait = true
                }
                AddressProvider.changeUnProcessCount(if (hasWait) 1 else 0)
//                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_UNDO_COUNT, if (hasWait) 1 else 0))
            } else {
                if(!it.external.isNullOrEmpty()){
                    AddressProvider.changeUnProcessCount(1)
                }else{
                    AddressProvider.changeUnProcessCount(0)
                }
//                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_UNDO_COUNT, 0))
            }

        }, {
            hideLoading()
            isRefresh = false
//                dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun dealFriendList(dataListFriend: List<FriendUndoListBean>) {
        if (dataListFriend.isNotEmpty()) {
            binding.rvFriendApplyList.visibility = View.VISIBLE
            binding.tvFriendApply.visibility = View.VISIBLE
            binding.tvFriendApplyTip.visibility = View.VISIBLE
            val newListFriend = ArrayList<FriendUndoListBean>()
            if (dataListFriend.size > 3) {
                //大于3条才显示查看更多
                for (i in 0..2) {
                    newListFriend.add(dataListFriend[i])
                }
                binding.tvFriendMoreApply.visibility = View.VISIBLE
            } else {
                newListFriend.addAll(dataListFriend)
                binding.tvFriendMoreApply.visibility = View.GONE
            }
            friendAdapter!!.setDataSourceList(newListFriend)
        } else {
            binding.tvFriendApply.visibility = View.GONE
            binding.tvFriendApplyTip.visibility = View.GONE
            binding.rvFriendApplyList.visibility = View.GONE
            binding.tvFriendMoreApply.visibility = View.GONE
        }
    }

    private fun dealCompanyList(dataListCompany: List<CompanyUndoBeanNew>) {
        if (dataListCompany.isNotEmpty()) {
            binding.rvOrgApplyList.visibility = View.VISIBLE
            binding.tvOrgApplyTitle.visibility = View.VISIBLE
            binding.text.visibility = View.VISIBLE
            companyAdapter!!.setSourceList(dataListCompany)
        } else {
            binding.tvOrgApplyTitle.visibility = View.GONE
            binding.text.visibility = View.GONE
            binding.rvOrgApplyList.visibility = View.GONE
        }
    }

    private fun dealExternalList(dataListExternal: List<ExternalUndoBean>) {
        if (dataListExternal.isNotEmpty()) {
            binding.applyExternalTitleTv.visibility = View.VISIBLE
            binding.applyExternalDesTv.visibility = View.VISIBLE
            binding.applyExternalRecycler.visibility = View.VISIBLE
            externalAdapter!!.setSourceList(dataListExternal)
        } else {
            binding.applyExternalTitleTv.visibility = View.GONE
            binding.applyExternalDesTv.visibility = View.GONE
            binding.applyExternalRecycler.visibility = View.GONE
        }
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.tvFriendMoreApply -> {//tcp点击查看更多，之后获取好友申请列表是通过relation服务，在ApplicationListActivity界面点击好友申请获取申请详情也是relation服务
                val intent = Intent(mContext!!, ApplicationListActivity::class.java)
                intent.putExtra("type", "friend")
                startActivity(intent)
            }
        }
    }

    override val contentViewResId: Int
        get() = R.layout.activity_undo_layout

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityUndoLayoutBinding {
        return ActivityUndoLayoutBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean {
        return true
    }


    override fun openArouterReceive(): Boolean {
        return true
    }

    //某条外部协作人消息处理了
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun receiveSendMsgEvent(event: EventBusEvent<String>) {
        if (event.data != null && event.data is String) {
            when (event.code) {
                EventBusAction.REFRESH_EXTERNAL_UNDO_LIST -> {
                    val paramerId = event.data
                    val targetBean = dataListExternal.find { it.externalId == paramerId }
                    val index = dataListExternal.indexOf(targetBean)
                    dataListExternal.removeAt(index)
                    externalAdapter?.notifyDataSetChanged()
                    if (!dataListExternal.isNullOrEmpty()) {
                        AddressProvider.changeUnProcessCount(1)
                    }else{
                        binding.applyExternalTitleTv.visibility = View.GONE
                        binding.applyExternalDesTv.visibility = View.GONE
                        binding.applyExternalRecycler.visibility = View.GONE
                    }
                }
            }
        }
    }
}