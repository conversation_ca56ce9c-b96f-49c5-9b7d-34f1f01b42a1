package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.Observer
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.library.im.imtcp.dbbean.FriendBean
import com.ddbes.library.im.imtcp.dbope.FriendDaoOpe
import com.ddbes.library.im.util.FriendCacheHolder
import com.joinutech.addressbook.R
import com.joinutech.addressbook.databinding.ActivityOrgExternalContactDetailBinding
import com.joinutech.addressbook.viewModel.OrgExternalContactDetailViewModel
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.ExternalContactDetailBean
import com.joinutech.ddbeslibrary.bean.ExternalContactListBean
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import io.reactivex.Observable
import io.reactivex.ObservableOnSubscribe
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/11/29 9:38
 * @packageName: com.joinutech.addressbook.view
 * @Company: JoinuTech
 */
class OrgExternalContactDetailActivity : MyUseBindingActivity<ActivityOrgExternalContactDetailBinding>() {

    override val contentViewResId: Int = R.layout.activity_org_external_contact_detail
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityOrgExternalContactDetailBinding {
        return ActivityOrgExternalContactDetailBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return false
    }

    private var externalContactName = ""
    private var externalContactId = ""
    private var externalContactIsEdit = false
    private var externalContactStatusType = 0
    private var externalContactType = ""
    private var externalContactLevel = ""
    private var externalContactPhone = ""
    private var externalHeader = ""
    private var externalIMName = ""
    private var companyId = ""
    private var typeIndex = 0
    private var active = 0
    private var status = 0
    private var bean: ExternalContactDetailBean? = null
    private var currentContactIsFriend = false
    private lateinit var viewModel: OrgExternalContactDetailViewModel
    private var subscribe: Disposable? = null

    private var external: ExternalContactListBean? = null

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        if (intent != null) {
//            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("name"))) {
//                externalContactName = intent.getStringExtra("name")
//                setTitle(externalContactName)
//            }
//            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("contactId"))) {
//                externalContactId = intent.getStringExtra("contactId")
//            }
//            active = intent.getIntExtra("active", 0)
//            status = intent.getIntExtra("status", 0)

            external = intent.getSerializableExtra("external") as ExternalContactListBean?
            setPageTitle(external?.name ?: "外部联系人")
            external?.let {
                externalContactId = it.userId
                active = it.active
                status = it.status
            }

            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                companyId = intent.getStringExtra("companyId") ?: ""
            }
            externalContactIsEdit = intent.getBooleanExtra("isEdit", false)
            if (externalContactIsEdit) {
                setRightImage(com.joinutech.ddbeslibrary.R.drawable.icon_right_top_edit, this)
            }
            typeIndex = intent.getIntExtra("typeIndex", 0)
        }
    }

    override fun initView() {
        showToolBarLine()
        whiteStatusBarBlackFont()
        viewModel = getModel(OrgExternalContactDetailViewModel::class.java)
        dealStatusType()
    }

    override fun initLogic() {
        super.initLogic()

        
        binding.clCallLayout.setOnClickListener(this)
         binding.tvSendInvite.setOnClickListener(this)
        binding.clMsgLayout.setOnClickListener(this)
        getObserver()
        getData()
    }

    private fun getData() {
        getLoadingDialog("", true)
        viewModel.getExternalContactDetail(bindToLifecycle(),
                companyId, externalContactId, accessToken!!)
    }

    private fun dealStatusType() {
        when {
            status == 1 && active == 0 -> {
                //已注册已激活
                externalContactStatusType = 0
            }
            status == 1 && active == 1 -> {
                //已注册未激活
                externalContactStatusType = 1
            }
            status == 0 && active == 0 -> {
                //未注册已激活
                externalContactStatusType = 2
            }
            status == 0 && active == 1 -> {
                //未注册未激活
                externalContactStatusType = 3
            }
        }
        when (externalContactStatusType) {
            0 -> {
                 binding.clSendLayout.visibility = View.GONE
                dealIsFriendMsgLayoutShow()
            }
            1 -> {
                if (externalContactIsEdit) binding.clSendLayout.visibility = View.VISIBLE
                dealIsFriendMsgLayoutShow()
            }
            2 -> {
                binding.clSendLayout.visibility = View.GONE
                 binding.clMsgLayout.visibility = View.GONE
            }
            3 -> {
                binding.clSendLayout.visibility = View.VISIBLE
                binding.clMsgLayout.visibility = View.GONE
            }
        }
    }

    private fun dealIsFriendMsgLayoutShow() {
        binding.clMsgLayout.visibility = View.VISIBLE
        subscribe = Observable.create(ObservableOnSubscribe<Boolean> {
            var bean: FriendBean? = null
            try {
                bean = FriendDaoOpe.instance.queryFriendByUserId(mContext!!, externalContactId)
            } catch (e: Exception) {
                showLog("查询好友---失败")
            }
            if (bean != null) {
                it.onNext(true)
            } else {
                it.onNext(false)
            }
        }).compose(bindToLifecycle())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(Schedulers.io())
                .subscribe {
                    currentContactIsFriend = it
                    if (currentContactIsFriend) {
                        //说明存在此人好友
                        binding.ivMsgIcon.setImageResource(R.drawable.icon_external_contact_msg)
                        
                        binding.tvMsgTitle.text = "发消息"
                    } else {
                        binding.ivMsgIcon.setImageResource(R.drawable.icon_external_contact_add_friend)
                        binding.tvMsgTitle.text = "加好友"
                    }
                }

    }

    @SuppressLint("SetTextI18n")
    private fun getObserver() {
        viewModel.getExternalContactDetailSuccessObservable.observe(this, Observer {
            dismissDialog()
            bean = it
            status = it.status
            active = it.active
            if (status != 0) {
                
                binding.ivHeaderImage.visibility = View.VISIBLE
                 binding.tvNoRegister.visibility = View.GONE
                if (StringUtils.isNotBlankAndEmpty(it.avatar)) {
                    ImageLoaderUtils.loadImage(mContext!!, binding.ivHeaderImage, it.avatar)
                } else if (external != null && !external!!.avatar.isNullOrBlank()) {
                    ImageLoaderUtils.loadImage(mContext!!, binding.ivHeaderImage, external!!.avatar)
                }
                if (it.active == 1) {
                    //未激活
                    binding.tvNoActive.visibility = View.VISIBLE
                } else {
                    binding.tvNoActive.visibility = View.GONE
                }
            } else {
                
                binding.tvNoActive.visibility = View.GONE
                binding.ivHeaderImage.visibility = View.INVISIBLE
                binding.tvNoRegister.visibility = View.VISIBLE
            }
            externalContactPhone = it.mobile
            externalContactType = it.type.toString()
            externalContactLevel = it.level.toString()
            
            binding.tvName.text = it.name
            externalIMName = it.name
            externalHeader = it.avatar
            binding.name.text = it.externalContactsName

            binding.tvEmail.text = it.email
            binding.tvPhone.text = externalContactPhone
            binding.tvRemark.text = it.desc
            if (it.updateTime != 0L) {
                val time = XUtil.turnToTimeStr(it.updateTime, DEFAULT_TIME_FORMAT_PATTERN)
                binding.tvSave.text = "$time 由 ${it.updateUser} 保存"
            }
            dealStatusType()
            setTypeShow()
            setLevelShow()
        })
        viewModel.getExternalContactDetailErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it.message)
            if (it.code == 4001) {
                EventBusUtils.sendEvent(EventBusEvent(
                        EventBusAction.Event_REFRESH_EXTERNAL_CONTACT_LIST, typeIndex))
                finish()
            }
        })
        viewModel.sendApplicationMsgSuccessObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, "发送邀请成功")
        })
        viewModel.sendApplicationMsgErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
        viewModel.verifyFriendApplicationSuccessObservable.observe(this, Observer {
            dismissDialog()
            val intent = Intent(mContext, VerifyApplicationActivity::class.java)
            intent.putExtra("companyId", externalContactId)
            intent.putExtra("type", "addFriend")
            startActivityForResult(intent, ADD_EXTERNAL_CONTACT_FRIEND)
        })
        viewModel.verifyFriendApplicationErrorObservable.observe(this, Observer { result ->
            dismissDialog()
            when (result) {
                "好友申请上限" -> //今天加入次数过多
                    ToastUtil.showCustomToast(null, mContext!!, true,
                            "您今天添加该用户次数过多，请明天再试")
                "已经是好友了" -> { // 1405 == code
                    // 更新好友数据后跳转聊天页面
                    fun isFriendAlready() {
                        hideLoading()
                        binding.ivMsgIcon.setImageResource(R.drawable.icon_external_contact_msg)
                        binding.tvSendInvite.text = "发送消息"
                        ToastUtil.show(mContext!!, result)
//                                EventBusUtils.sendStickyEvent(EventBusEvent(EventBusAction.REFRESH_FRIENDLIST, ""))
                        ARouter.getInstance()
                                .build(RouteIm.singleChat)
                                .withString("targetId", externalContactId)
                                .withString("targetName", externalIMName)
                                .withString("targetLogo", externalHeader)
                                .navigation()
                    }
                    FriendCacheHolder.getFriendCacheListWithCallback(this,//已经不会再调用到了tcp
                            onSuccess = {
//                                // 添加外部联系人为好友后刷新好友列表
//                                EventBusUtils.sendEvent(EventBusEvent(IMEvent.UPDATE_FRIEND_VERSION, ""))
                                isFriendAlready()
                            },
                            onError = {
                                // 手动添加好友信息
//                                bean?.let { data ->
//                                    val newFriend = FriendBean()
//                                    newFriend.userId = externalContactId
//                                    newFriend.avatar = data.avatar
//                                    newFriend.name = data.name
//                                    newFriend.remark = ""
//                                    newFriend.birthday = ""
//                                    newFriend.gender = ""
//                                    newFriend.relation = 1
//                                    newFriend.initial = CharacterParser.getFirstSpell(data.name)
//                                    newFriend.sortName = ""
//                                    newFriend.ownUserId = userId!!
//                                    newFriend.logout = 0
//                                    FriendCacheHolder.updateFriendInfo(this, newFriend, onResult = {}, onError = {})
//                                    //同意好友后，更新好友缓存信息
//                                    FriendCacheHolder.saveFriend(UserInfo(externalContactId, data.avatar, data.name))
//                                    // 添加外部联系人为好友后刷新好友列表
//                                    EventBusUtils.sendEvent(EventBusEvent(IMEvent.UPDATE_FRIEND_VERSION, ""))
//                                }
//                                isFriendAlready()
                            })
                }
                "好友添加成功！" -> {
                    FriendCacheHolder.getFriendCacheListWithCallback(this,//已经不会再调用到了
                            onSuccess = {
                                hideLoading()
                                ToastUtil.show(mContext!!, result)
                                binding.ivMsgIcon.setImageResource(R.drawable.icon_external_contact_msg)
                                binding.tvSendInvite.text = "发送消息"
                                ToastUtil.show(mContext!!, "好友添加成功")
                                // 添加外部联系人为好友后刷新好友列表
                                EventBusUtils.sendEvent(EventBusEvent(IMEvent.UPDATE_FRIEND_VERSION, ""))
                            },
                            onError = {
                                // 手动添加好友信息
//                                bean?.let { data ->
//                                    val newFriend = FriendBean()
//                                    newFriend.userId = externalContactId
//                                    newFriend.avatar = data.avatar
//                                    newFriend.name = data.name
//                                    newFriend.remark = ""
//                                    newFriend.birthday = ""
//                                    newFriend.gender = ""
//                                    newFriend.relation = 1
//                                    newFriend.initial = CharacterParser.getFirstSpell(data.name)
//                                    newFriend.sortName = ""
//                                    newFriend.ownUserId = userId!!
//                                    newFriend.logout = 0
//                                    FriendCacheHolder.updateFriendInfo(this, newFriend, onResult = {}, onError = {})
//                                    //同意好友后，更新好友缓存信息
//                                    FriendCacheHolder.saveFriend(UserInfo(externalContactId, data.avatar, data.name))
//                                    // 添加外部联系人为好友后刷新好友列表
//                                    EventBusUtils.sendEvent(EventBusEvent(IMEvent.UPDATE_FRIEND_VERSION, ""))
//                                }
//                                hideLoading()
//                                ToastUtil.show(mContext!!, "好友添加成功")
                            })
                }
                "添加失败，该用户不允许他人添加为好友" -> {
                    ToastUtil.showCustomToast(null, mContext!!, true,
                            "添加失败，对方不允许通过外部协作人员添加为好友")
                }
                else -> ToastUtil.show(mContext!!, result)
            }
        })
    }

    private fun setTypeShow() {
        val shoeTypeText = when (externalContactType) {
            "1" -> "客户"
            "2" -> "渠道商"
            "3" -> "供应商"
            "4" -> "合作伙伴"
            "5" -> "其他类型"
            else -> "1"
        }
        binding.type.text = shoeTypeText
    }

    private fun setLevelShow() {
        when (externalContactLevel) {
            "1" -> {
                
                binding.clLevelLayout.setBackgroundResource(R.drawable.shape_external_contact_ffedd4)
                 binding.ivLevelIcon.setImageResource(R.drawable.icon_external_contact_level_yellow)
                binding.tvLevelName.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.colorFAA62F))
                binding.tvLevelName.text = "非常重要"
            }
            "2" -> {
                binding.clLevelLayout.setBackgroundResource(R.drawable.shape_external_contact_d4eff)
                binding.ivLevelIcon.setImageResource(R.drawable.icon_external_contact_level_blue)
                binding.tvLevelName.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.main_blue))
                binding.tvLevelName.text = "重要"
            }
            "3" -> {
                binding.clLevelLayout.setBackgroundResource(R.drawable.shape_external_contact_e4edf5)
                binding.ivLevelIcon.setImageResource(R.drawable.icon_external_contact_level_grey)
                binding.tvLevelName.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color7EB0D6))
                binding.tvLevelName.text = "一般"
            }
        }
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            iv_rightTitle -> {
                if (bean != null) {
                    val intent = Intent(mContext!!, AddOrgExternalContactActivity::class.java)
                    intent.putExtra("bean", bean)
                    intent.putExtra("currentCompanyId", companyId)
                    intent.putExtra("typeIndex", typeIndex)
                    startActivityForResult(intent, ADD_EXTERNAL_CONTACT)
                }
            }
            binding.clCallLayout -> {
                if (StringUtils.isPhoneNumber(externalContactPhone)) {
                    callPhone()
                }
            }
            binding.tvSendInvite -> {
                getLoadingDialog("", false)
                viewModel.sendApplicationMsg(bindToLifecycle(), companyId,
                        externalContactPhone, accessToken!!)
            }
            binding.clMsgLayout -> {
                if (!currentContactIsFriend) {
                    getLoadingDialog("", false)
                    viewModel.validate(bindToLifecycle(), accessToken!!, externalContactId)
                } else {
                    ARouter.getInstance()
                            .build(RouteIm.singleChat)
                            .withString("targetId", externalContactId)
                            .withString("targetName", externalIMName)
                            .withString("targetLogo", externalHeader)
                            .navigation()
                }
            }
        }
    }

    private fun callPhone() {
        val intent = Intent(Intent.ACTION_DIAL)
        val data = Uri.parse("tel:$externalContactPhone")
        intent.data = data
        startActivity(intent)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                ADD_EXTERNAL_CONTACT -> {
                    if (data != null && data.getBooleanExtra("delete", false)) {
                        finish()
                        return
                    }
                    getData()
                }
                ADD_EXTERNAL_CONTACT_FRIEND -> {
                    ToastUtil.showCustomToast(null, mContext!!, true,
                            "添加失败，对方不允许通过外部协作人员添加为好友")
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (subscribe != null && subscribe?.isDisposed!!) {
            subscribe?.dispose()
        }
    }

}