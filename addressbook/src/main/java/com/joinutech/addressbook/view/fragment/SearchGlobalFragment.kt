package com.joinutech.addressbook.view.fragment

import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.SearchGlobalAllAdapter
import com.joinutech.addressbook.adapter.SearchGlobalOtherAdapter
import com.joinutech.addressbook.adapter.bean.GroupSearchBean
import com.joinutech.addressbook.view.SearchGlobalActivity
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.base.BaseFragment
import com.joinutech.ddbeslibrary.bean.GroupInfoBean
import com.joinutech.ddbeslibrary.utils.ItemClickListener

/**
 * @Description: 全局搜索的fragment
 * @Author: hjr
 * @Time: 2020/2/25 16:25
 * @packageName: com.joinutech.addressbook.view.fragment
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
class SearchGlobalFragment : BaseFragment() {

    override val layoutRes: Int = R.layout.fragment_search_global

    override fun openEventBus() = false

    private lateinit var contentListRv: RecyclerView
    private lateinit var allAdapter: SearchGlobalAllAdapter
    private lateinit var otherAdapter: SearchGlobalOtherAdapter
    private var allList = arrayListOf<GroupSearchBean>()
    private var otherList = arrayListOf<GroupInfoBean>()
    private lateinit var noContentLayout: PageEmptyView
    private var searchListType = 0
    private var keyWord = ""

    companion object {
        private const val SEARCH_LIST_TYPE = "search_list_type"
        private const val SEARCH_KEY_WORD = "search_key_word"
        fun newInstance(index: Int, keyWord: String = ""): SearchGlobalFragment {
            return SearchGlobalFragment().apply {
                arguments = Bundle().apply {
                    putInt(SEARCH_LIST_TYPE, index)
                    putString(SEARCH_KEY_WORD, keyWord)
                }
            }
        }
    }

    override fun initView(rootView: View) {
        if (arguments != null && arguments?.getInt(SEARCH_LIST_TYPE) != null)
            searchListType = arguments?.getInt(SEARCH_LIST_TYPE) ?: 0
        if (arguments != null && arguments?.getString(SEARCH_KEY_WORD) != null)
            keyWord = arguments?.getString(SEARCH_KEY_WORD) ?: ""
        contentListRv = rootView.findViewById(R.id.contentListRv)
        noContentLayout = rootView.findViewById(R.id.layout_empty_layout)
        contentListRv.layoutManager = LinearLayoutManager(mActivity)
    }

    fun refreshKeyWord(keyWord: String) {
        this.keyWord = keyWord
        if (searchListType == 0)
            allAdapter.setKeyWord(keyWord)
        else
            otherAdapter.setKeyWord(keyWord)
    }

    override fun initLogic() {
        super.initLogic()

        if (searchListType == 0) {
            allAdapter = SearchGlobalAllAdapter(mActivity, allList)
            allAdapter.setKeyWord(keyWord)
            allAdapter.setWatchAllListener(object : ItemClickListener {
                override fun onItemClick(position: Int) {
                    (mActivity as SearchGlobalActivity).entryOtherFragment(position + 1)
                }

            })
            contentListRv.adapter = allAdapter
        } else {
            otherAdapter = SearchGlobalOtherAdapter(mActivity, otherList, searchListType)
            otherAdapter.setKeyWord(keyWord)
            contentListRv.adapter = otherAdapter
        }
    }

    fun refreshAllListData(list: ArrayList<GroupSearchBean>) {
        if (!list.isNullOrEmpty()) {
            allList = list
            contentListRv.visibility = View.VISIBLE
            noContentLayout.hide()
        } else {
            allList.clear()
            contentListRv.visibility = View.GONE
            noContentLayout.show()
        }
        allAdapter.setSourceList(allList)
    }

    fun refreshListData(list: List<GroupInfoBean>) {
        if (!list.isNullOrEmpty()) {
            otherList = list as ArrayList<GroupInfoBean>
            contentListRv.visibility = View.VISIBLE
            noContentLayout.hide()
        } else {
            otherList.clear()
            contentListRv.visibility = View.GONE
            noContentLayout.show()
        }
        otherAdapter.setSourceList(otherList)
    }
}