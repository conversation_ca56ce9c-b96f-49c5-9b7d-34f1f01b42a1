package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.Observer
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.addressbook.R
import com.joinutech.addressbook.databinding.ActivityCooperationApplicationDetailBinding
import com.joinutech.addressbook.viewModel.CooperationApplicationViewModel
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/6 10:58
 * @packageName: com.joinutech.addressbook.view
 * @Company: JoinuTech
 */
@Route(path = RouteOrg.cooperationCompanyApplicationDetailActivity)
class CooperationCompanyApplicationDetailActivity : MyUseBindingActivity<ActivityCooperationApplicationDetailBinding>() {

    override val contentViewResId: Int = R.layout.activity_cooperation_application_detail
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityCooperationApplicationDetailBinding {
        return ActivityCooperationApplicationDetailBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = true

    @Autowired
    @JvmField
    var paramId: String = ""

    @Autowired
    @JvmField
    var messageId: String = ""

//    @Autowired
//    @JvmField
//    var position: Int = -1

    private var status = 0

    private var dealCode = 0
    private lateinit var viewModel: CooperationApplicationViewModel

    //    private var disposable: Disposable? = null
    private var createTime = 0L

    override fun initImmersion() {
        setPageTitle("申请详情")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back2)
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        viewModel = getModel(CooperationApplicationViewModel::class.java)
    }

    override fun initLogic() {
        super.initLogic()

        binding.agree.setOnClickListener(this)
        binding.ignore.setOnClickListener(this)
        getObserve()
        getLoadingDialog("", false)
        viewModel.getCooperationApplicationDetail(bindToLifecycle(), accessToken, paramId)
    }

    private fun getObserve() {
        viewModel.getDetailSuccessObservable.observe(this, Observer {
            dismissDialog()
            if (StringUtils.isNotBlankAndEmpty(it.companyLogo)) {
                ImageLoaderUtils.loadImage(mContext!!, binding.ivCompanyLogo, it.companyLogo)
            }
            binding.tvCompanyName.text = it.companyName
            if (StringUtils.isNotBlankAndEmpty(it.companyIndustry)) {
                binding.tvCooperType.text = it.companyIndustry
            } else {
                binding.tvCooperType.text = "未填写"
            }
            if (StringUtils.isNotBlankAndEmpty(it.companyType)) {
                binding.tvCooperKind.text = it.companyType
            } else {
                binding.tvCooperKind.text = "未填写"
            }
            status = when (it.active) {
                0 -> {
                    1
                }
                1 -> {
                    0
                }
                2 -> {
                    2
                }
                3 -> {
                    3
                }
                else -> {
                    it.active
                }
            }
            showStatus()
        })
        viewModel.getDetailErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
        viewModel.dealCooperationApplicationSuccessObservable.observe(this, Observer {
            dismissDialog()
            if (dealCode == 1) {
                //同意
                status = 1
                showStatus()
            } else {
                //忽略
                status = 2
                showStatus()
            }
            //发送一个通知，告诉UndoActivity将对应的已处理条目删除
            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_EXTERNAL_UNDO_LIST, paramId))
        })
        viewModel.dealCooperationApplicationErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.agree -> {
                dealCode = 1
                getLoadingDialog("", false)
                viewModel.dealCooperationApplication(bindToLifecycle(), accessToken!!,
                        dealCode, paramId)
            }
            binding.ignore -> {
                dealCode = 2
                getLoadingDialog("", false)
                viewModel.dealCooperationApplication(bindToLifecycle(), accessToken!!,
                        dealCode, paramId)
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun showStatus() {
        binding.clDealLayout.visibility = View.GONE
        binding.clStatusLayout.visibility = View.GONE
        if (createTime == 0L) {
            createTime = System.currentTimeMillis()
        }
        when (status) {
            0 -> {
                binding.clDealLayout.visibility = View.VISIBLE
            }
            1 -> {
                binding.clStatusLayout.visibility = View.VISIBLE
                if (createTime != 0L) {
                    binding.time.text = XUtil.turnToTimeStr(createTime, TIME_FORMAT_PATTERN1)
                }
                EventBusUtils.sendEvent(
                        EventBusEvent(EventBusAction.Event_REFRESH_COOPERATION_APPLICATION,
                                paramId)
                )
            }
            2 -> {
                binding.clStatusLayout.visibility = View.VISIBLE
                binding.statusText.text = "已忽略"
                if (createTime != 0L) {
                    binding.time.text = XUtil.turnToTimeStr(createTime, TIME_FORMAT_PATTERN1)
                }
            }
            3 -> {
                binding.clStatusLayout.visibility = View.VISIBLE
                binding.statusText.text = "已失效"
                if (createTime != 0L) {
                    binding.time.text = XUtil.turnToTimeStr(createTime, TIME_FORMAT_PATTERN1)
                }
                val view = View.inflate(mContext,
                        R.layout.toast_application_layout, null)
                ToastUtil.showCustomToast(view, mContext!!, true, "")
            }
        }
    }

}