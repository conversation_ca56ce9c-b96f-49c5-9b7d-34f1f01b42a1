package com.joinutech.addressbook.view

import android.app.Activity
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.OrgPermissionAdapter
import com.joinutech.addressbook.constract.AddressbookConstract
import com.joinutech.addressbook.databinding.ActivityOrgpermissionBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.OrgPermissionListBean
import com.joinutech.ddbeslibrary.utils.ItemClickListener
import com.joinutech.ddbeslibrary.utils.ORGPERMISSION_CHANGE
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import javax.inject.Inject
import javax.inject.Named

/**
 * <AUTHOR> by 黄洁如 on 2019/3/20 13:48
 * @fileName OrgPermissionActivity
 * @describe 团队权限设置页面
 * @org 加优科技
 */
//点击权限设置后跳转到的界面
class OrgPermissionActivity : MyUseBindingActivity<ActivityOrgpermissionBinding>() {

    override val contentViewResId: Int
        get() = R.layout.activity_orgpermission

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityOrgpermissionBinding {
        return ActivityOrgpermissionBinding.inflate(layoutInflater)
    }

    private var orgPermissionPersonList = ArrayList<OrgPermissionListBean>()
    private lateinit var adapter: OrgPermissionAdapter

    @Inject
    @field:Named(AddressbookUtil.ADDR_PRESENTER)
    lateinit var presenter: AddressbookConstract.AddressbookPresenter
    private var companyId: String = ""
    private var isCreator: Boolean = false

    override fun initImmersion() {
        setPageTitle("权限设置")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        setRightImage(R.drawable.icon_addpermission, this)
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                companyId = intent.getStringExtra("companyId") ?: ""
            }
            isCreator = intent.getBooleanExtra("isCreator", false)
        }
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        
        binding.layoutEmptyLayout.visibility = View.VISIBLE
         binding.orgPermissionList.visibility = View.GONE
        binding.orgPermissionList.layoutManager = LinearLayoutManager(mContext)
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun initLogic() {
        super.initLogic()
        adapter = OrgPermissionAdapter(mContext!!, orgPermissionPersonList, isCreator)
        binding.orgPermissionList.adapter = adapter
        adapter.setClickListener(object : ItemClickListener {
            override fun onItemClick(position: Int) {
                val intent = Intent(mContext!!, AddOrgPermissionPersonActivity2::class.java)
                intent.putExtra("type", "edit")
                intent.putExtra("companyId", companyId)
                intent.putExtra("bean", orgPermissionPersonList[position])
                startActivityForResult(intent, ORGPERMISSION_CHANGE)
            }

        })
        getOrgPermissionList()
    }

    private fun getOrgPermissionList() {
        getLoadingDialog("获取团队权限...", false)
        presenter.orgPermissionList(bindToLifecycle(), accessToken!!, companyId, {
            dismissDialog()
            orgPermissionPersonList.clear()
            if (it.isNotEmpty()) {
                orgPermissionPersonList.addAll(it)
                binding.layoutEmptyLayout.visibility = View.GONE
                binding.orgPermissionList.visibility = View.VISIBLE
            } else {
                binding.orgPermissionList.visibility = View.GONE
                binding.layoutEmptyLayout.visibility = View.VISIBLE
            }
            adapter.setSourceList(orgPermissionPersonList)
        }, {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            iv_rightTitle -> {
                //添加权限人员
                val intent = Intent(mContext!!, AddOrgPermissionPersonActivity2::class.java)
                intent.putExtra("type", "add")
                intent.putExtra("companyId", companyId)
                startActivityForResult(intent, ORGPERMISSION_CHANGE)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                ORGPERMISSION_CHANGE -> {
                    getOrgPermissionList()
                }
            }
        }
    }

    

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return false
    }
}