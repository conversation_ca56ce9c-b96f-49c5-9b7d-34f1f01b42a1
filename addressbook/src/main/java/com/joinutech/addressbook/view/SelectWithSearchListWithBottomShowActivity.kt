package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.FriendSelectBottomSelectPersonHeadAdapter
import com.joinutech.addressbook.adapter.FriendSelectSearchAdapter
import com.joinutech.addressbook.databinding.ActivityFriendSelectListWithSearchBinding
import com.joinutech.addressbook.viewModel.FriendSelectViewModel
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.FriendSelectBean
import com.joinutech.ddbeslibrary.bean.UserInfo
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.ItemClickListener
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.StringUtils
//import kotlinx.android.synthetic.main.activity_friend_select_list_with_search.*
//import kotlinx.android.synthetic.main.search_include_layout.*

/**
 * @Description: 好友选择页
 * @Author: hjr
 * @Time: 2020/2/24 11:14
 * @packageName: com.joinutech.addressbook.view
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
@Route(path = RouteOrg.SelectSearchListWithBottomShowActivity)
class SelectWithSearchListWithBottomShowActivity : MyUseBindingActivity<ActivityFriendSelectListWithSearchBinding>() {

    override val contentViewResId: Int = R.layout.activity_friend_select_list_with_search
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityFriendSelectListWithSearchBinding {
        return ActivityFriendSelectListWithSearchBinding.inflate(layoutInflater)
    }

    private var currentTitle = ""
    private var personList = arrayListOf<FriendSelectBean>()
    private var searchPersonList = arrayListOf<FriendSelectBean>()
    private lateinit var adapter: FriendSelectSearchAdapter
    private var maxSelectNum = Int.MAX_VALUE
    private var isNeedPersonInfo = false
    private var currentSelectNum = 0
    private var outPersonList = arrayListOf<FriendSelectBean>()
    private var bottomSelectPersonSet = arrayListOf<String>()
    private var noSelectUserIds = arrayListOf<String>()
    private lateinit var viewModel: FriendSelectViewModel
    private var searchTextValue = ""
    private lateinit var bottomSelectHeadAdapter: FriendSelectBottomSelectPersonHeadAdapter

    override fun initImmersion() {
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("title"))) {
                currentTitle = intent.getStringExtra("title") ?: ""
                setPageTitle(currentTitle)
            } else {
                setPageTitle("我的好友")
            }
            setRightTitleColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color999999),
                    "完成", View.OnClickListener { })
            if (intent.getIntExtra("maxSelectNum", 0) != 0) {
                maxSelectNum = intent.getIntExtra("maxSelectNum", 0)
            }
            isNeedPersonInfo = intent.getBooleanExtra("isNeedPersonInfo", false)
            outPersonList = (intent
                    .getSerializableExtra("outPersonList") as ArrayList<FriendSelectBean>?)
                    ?: arrayListOf()
            if (!intent.getStringArrayListExtra("noSelectUserIds").isNullOrEmpty()) {
                noSelectUserIds = intent.getStringArrayListExtra("noSelectUserIds")!!
            }
        }
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        binding.rvList.layoutManager = LinearLayoutManager(this)
        binding.rvPersonList.layoutManager = LinearLayoutManager(
                mContext, LinearLayoutManager.HORIZONTAL, false)
        viewModel = getModel(FriendSelectViewModel::class.java)
         binding.llSelectAll.visibility = View.GONE
        binding.mainSideBar.visibility = View.GONE
    }

    override fun initLogic() {
        super.initLogic()
        getObservable()
        adapter = FriendSelectSearchAdapter(mContext!!, personList, true)
        bottomSelectHeadAdapter = FriendSelectBottomSelectPersonHeadAdapter(mContext!!,
                bottomSelectPersonSet)
        binding.rvPersonList.adapter = bottomSelectHeadAdapter
        adapter.setClickListener(object : ItemClickListener {
            //点击事件
            override fun onItemClick(position: Int) {
                if (adapter.mData[position].select) {
                    if (!bottomSelectPersonSet.isNullOrEmpty() &&
                            adapter.mData[position].userId in bottomSelectPersonSet) {
                        bottomSelectPersonSet.remove(adapter.mData[position].userId)
                        bottomShowOrHide()//取消选中时更新
                    }
                    if (currentSelectNum >= 1)
                        currentSelectNum--
                    adapter.mData[position].select = !adapter.mData[position].select
                    adapter.notifyItemChanged(position)
                } else {
                    if (currentSelectNum < maxSelectNum) {
                        if ((!bottomSelectPersonSet.isNullOrEmpty() &&
                                        adapter.mData[position].userId !in bottomSelectPersonSet)
                                || bottomSelectPersonSet.isNullOrEmpty()) {
                            bottomSelectPersonSet.add(adapter.mData[position].userId)
                            bottomShowOrHide()//选中时更新
                        }
                        currentSelectNum++
                        adapter.mData[position].select = !adapter.mData[position].select
                        adapter.notifyItemChanged(position)
                    }
                }
                if (!adapter.mData.isNullOrEmpty()) {
                    viewModel.setRightCompleteColorShow(adapter.mData)
                }
            }

        })
        adapter.setTextIndex(false)
        val noUsedList = outPersonList.filter { it.logout == 1 }
        if (noUsedList.isNotEmpty()) {
            noUsedList.forEach {
                noSelectUserIds.add(it.userId)
            }
        }
        val temp = noSelectUserIds.distinct()
        noSelectUserIds.clear()
        noSelectUserIds.addAll(temp)
        adapter.setNoSelectUsers(noSelectUserIds)
        binding.rvList.adapter = adapter
        
        binding.searchBar.search.hint = "搜索"
        binding.searchBar.search.imeOptions = EditorInfo.IME_ACTION_SEARCH
        binding.searchBar.search.inputType = EditorInfo.TYPE_CLASS_TEXT
        binding.searchBar.delete.visibility = View.GONE
        binding.searchBar.search.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (s != null && s.isNotEmpty()) {
                    binding.searchBar.delete.visibility = View.VISIBLE
                } else {
                    binding.searchBar.delete.visibility = View.GONE
                }
            }

        })
        binding.searchBar.search.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH ||
                    (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER)) {
                searchTextValue = binding.searchBar.search.text.toString().trim()
                if (StringUtils.isNotBlankAndEmpty(searchTextValue)) {
                    getFriendList()//搜索好友
                }
                return@setOnEditorActionListener true
            }
            false
        }
        binding.llSelectAll.setOnClickListener(this)
        binding.searchBar.delete.setOnClickListener(this)
        binding.searchBar.cancel.setOnClickListener(this)
        getFriendList()//加载好友
        //第一次时需要复选数据给底部的set进行显示
        if (!outPersonList.isNullOrEmpty()) {
            outPersonList.forEach {
                if (it.select) {
                    bottomSelectPersonSet.add(it.userId)
                }
            }
            bottomShowOrHide()//初始化时更新
        }
    }

    @SuppressLint("SetTextI18n")
    private fun bottomShowOrHide() {
        if (!personList.isNullOrEmpty()) {
            if (bottomSelectPersonSet.isNullOrEmpty()) {
                 binding.twoBtnsContainRl.visibility = View.GONE
            } else binding.twoBtnsContainRl.visibility = View.VISIBLE
            binding.tvBottomTitle.text = "已选择${bottomSelectPersonSet.size}人,最多选择${maxSelectNum}人"
            val bottomAvatarSet = arrayListOf<String>()
            personList.forEach {
                bottomSelectPersonSet.forEach { innerBean ->
                    run {
                        if (innerBean == it.userId) {
                            bottomAvatarSet.add(it.avatar)
                        }
                    }
                }
            }
            bottomSelectHeadAdapter.setSourceList(bottomAvatarSet)
        }
    }

    private fun getObservable() {
        viewModel.getFriendListSuccessObservable.observe(this, Observer {
            dismissDialog()
            if (it.isNullOrEmpty()) {
                setShowNoContent(true)
            } else {
                setShowNoContent(false)
                if (StringUtils.isNotBlankAndEmpty(searchTextValue)) {
                    searchPersonList = it
                    personList.forEach {
                        searchPersonList.forEach { innerBean ->
                            run {
                                if (innerBean.userId == it.userId) {
                                    innerBean.select = it.select
                                }
                            }
                        }
                    }
                    showOldSelectStatus()
                } else {
                    personList = it
                }
                viewModel.setRightCompleteColorShow(personList)
                adapter.setSourceList(it)
            }
        })
        viewModel.rightCompleteColorShowObservable.observe(this, Observer {
            if (it) {
                setRightTitleColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.main_blue),
                        "完成", this)
            } else {
                setRightTitleColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color999999),
                        "完成", View.OnClickListener { })
            }
        })
    }

    private fun showOldSelectStatus() {
        if (!personList.isNullOrEmpty() && !searchPersonList.isNullOrEmpty()) {
            if (searchPersonList.isNullOrEmpty()) {
                personList.forEach {
                    searchPersonList.forEach { innerBean ->
                        run {
                            if (innerBean.userId == it.userId) {
                                innerBean.select = it.select
                            }
                        }
                    }
                }
            }
        }
    }

    private fun getFriendList() {
        getLoadingDialog("", false)
        viewModel.setIMVcPersonList(outPersonList, searchTextValue)
    }

    private fun setShowNoContent(b: Boolean) {
        if (b) {
            binding.layoutEmptyLayout.visibility = View.VISIBLE
            binding.clHaveDataLayout.visibility = View.GONE
        } else {
            binding.layoutEmptyLayout.visibility = View.GONE
            binding.clHaveDataLayout.visibility = View.VISIBLE
        }
    }

    override fun onBackPressed() {
        if (currentFocus != null && currentFocus?.windowToken != null)
            (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                    .hideSoftInputFromWindow(currentFocus!!
                            .windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        super.onBackPressed()
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            tv_rightTitle -> {
                completeEvent()
            }
            binding.searchBar.delete -> {
                if (StringUtils.isNotBlankAndEmpty(searchTextValue)) {
                    cancelSearchEvent()
                }
            }
            binding.searchBar.cancel -> {
                if (StringUtils.isNotBlankAndEmpty(searchTextValue)) {
                    cancelSearchEvent()
                }
            }
        }
    }

    private fun cancelSearchEvent() {
        binding.searchBar.search.setText("")
        try {
            if (currentFocus != null && currentFocus?.windowToken != null) {
                (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                        .hideSoftInputFromWindow(currentFocus!!.windowToken,
                                InputMethodManager.HIDE_NOT_ALWAYS)
            }
        } catch (e: java.lang.Exception) {

        }

        if (!searchPersonList.isNullOrEmpty()) {
            personList.forEach {
                searchPersonList.forEach { innerBean ->
                    run {
                        if (innerBean.userId == it.userId) {
                            it.select = innerBean.select
                        }
                    }
                }
            }
            searchPersonList.clear()
        }
        adapter.setSourceList(personList)
        if (!personList.isNullOrEmpty()) {
            viewModel.setRightCompleteColorShow(personList)
        }
    }

    private fun completeEvent() {
        if (!searchPersonList.isNullOrEmpty()) {
            val userIds = arrayListOf<FriendSelectBean>()
            searchPersonList.forEach {
                if (it.select) {
                    userIds.add(it)
                }
            }
            if (userIds.isNullOrEmpty()) {
                personList.forEach {
                    userIds.forEach { innerBean ->
                        run {
                            if (innerBean.userId == it.userId) {
                                it.select = innerBean.select
                            }
                        }
                    }
                }
            }
        }
        if (!personList.isNullOrEmpty()) {
            val selectPersonUserIdSet = hashSetOf<String>()
            val selectPersonUserSet = hashSetOf<UserInfo>()
            var nameStringBuilder = StringBuilder()
            personList.forEach {
                if (!noSelectUserIds.isNullOrEmpty()) {
                    if (it.select && it.userId !in noSelectUserIds) {
                        nameStringBuilder.append("${it.name}、")
                        selectPersonUserIdSet.add(it.userId)
                        selectPersonUserSet.add(UserInfo(it.userId, it.avatar, it.name))
                    }
                } else {
                    if (it.select) {
                        nameStringBuilder.append("${it.name}、")
                        selectPersonUserIdSet.add(it.userId)
                        selectPersonUserSet.add(UserInfo(it.userId, it.avatar, it.name))
                    }
                }
            }
            val intent = Intent()
            if (isNeedPersonInfo) {
                intent.putExtra("selectUserIds", selectPersonUserSet)
            } else {
                intent.putExtra("selectUserIds", selectPersonUserIdSet)
            }
            val userName = UserHolder.getCurrentUser()?.name
            if (!userName.isNullOrBlank() && !nameStringBuilder.contains(userName)) {
                nameStringBuilder.append("$userName、")
            }
            nameStringBuilder = nameStringBuilder.deleteCharAt(nameStringBuilder.length - 1)
            intent.putExtra("selectUserName", nameStringBuilder.toString())
            setResult(Activity.RESULT_OK, intent)
        }
        finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (!personList.isNullOrEmpty()) personList.clear()
        if (!searchPersonList.isNullOrEmpty()) searchPersonList.clear()
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return true
    }


}