package com.joinutech.addressbook.view

import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.BatchProcessUndoAdapter
import com.joinutech.addressbook.databinding.ActivityBatchProcessMsgBinding
import com.joinutech.addressbook.viewModel.BatchProcessUndoMsgViewModel
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.CompanyUndoListBean
import com.joinutech.ddbeslibrary.utils.*
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * description ： 批量处理待处理消息
 * author: 黄洁如
 * date : 2019/10/21
 */
class BatchProcessUndoMsgActivity : MyUseBindingActivity<ActivityBatchProcessMsgBinding>() {

    override val contentViewResId: Int = R.layout.activity_batch_process_msg
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityBatchProcessMsgBinding {
        return ActivityBatchProcessMsgBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = false

    private lateinit var adapter: BatchProcessUndoAdapter
    private var undoList = arrayListOf<CompanyUndoListBean>()
    private lateinit var viewModel: BatchProcessUndoMsgViewModel
    private var companyId: String = ""
    private var companyName = ""
    private var agreeSelectUserList = arrayListOf<String>()
    private var dealFlag = 0

    override fun initImmersion() {
        setPageTitle("批量处理")
        showBackButton(R.drawable.icon_cancel_batch_process)
        if (intent != null) {
            if (intent.getSerializableExtra("batchList") != null) {
                undoList = intent.getSerializableExtra("batchList") as ArrayList<CompanyUndoListBean>
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                companyId = intent.getStringExtra("companyId") ?: ""
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyName"))) {
                companyName = intent.getStringExtra("companyName") ?: ""
            }
        }
    }

    override fun initView() {
        showToolBarLine()
        whiteStatusBarBlackFont()
        binding.batchProcessRv.layoutManager = LinearLayoutManager(this)
        viewModel = getModel(BatchProcessUndoMsgViewModel::class.java)
    }

    override fun initLogic() {
        super.initLogic()
        adapter = BatchProcessUndoAdapter(undoList, mContext!!)
        binding.batchProcessRv.adapter = adapter
        binding.ignore.setOnClickListener(this)
        binding.agree.setOnClickListener(this)
        binding.allSelectLayout.setOnClickListener(this)
        getObservable()
        getList()
    }

    private fun getList() {
        getLoadingDialog("", false)
        viewModel.batchProcessUndoMsgList(bindToLifecycle(), accessToken!!, companyId)
    }

    private fun getObservable() {
        viewModel.batchProcessUndoListSuccessObservable.observe(this, Observer {
            dismissDialog()
            if (it.isNotEmpty()) {
                undoList = it as ArrayList<CompanyUndoListBean>
                binding.layoutEmptyLayout.visibility = View.GONE
                binding.haveUndoMsgRl.visibility = View.VISIBLE
                adapter.setSourceList(undoList)
            } else {
                if (dealFlag == 1 || dealFlag == 2) {
                    ToastUtil.showCustomToast(null, mContext!!, true, "批量处理完成")
                }
                binding.layoutEmptyLayout.visibility = View.VISIBLE
                binding.haveUndoMsgRl.visibility = View.GONE
            }
        })
        viewModel.batchProcessUndoListErrorObservable.observe(this, Observer {
            dismissDialog()
        })
        viewModel.batchProcessUndoSuccessObservable.observe(this, Observer {
            dismissDialog()
            dealFlag = it
            dealAgreeEvent()
        })
        viewModel.batchProcessUndoErrorObservable.observe(this, Observer {
            dismissDialog()
        })
    }

    private fun refreshProcessResult() {
        viewModel.batchProcessUndoMsgList(bindToLifecycle(), accessToken!!, companyId)
        EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_APPLIY_LIST, "org"))
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun getUndoList(eventBusEvent: EventBusEvent<String>) {
        if (eventBusEvent.code == EventBusAction.Event_BATCH_PROCESS_UNDO_SUCCESS
                && eventBusEvent.data == "true") {
            refreshProcessResult()
        }
    }


    private fun dealAgreeEvent() {
        if (dealFlag == 1) {
            //批量处理点击同意成功后
            val intent = Intent(this, OrgDepartmentActivity::class.java)
            intent.putExtra("isNeedSendRefreshEvent", true)
            intent.putExtra("depName", companyName)
            intent.putExtra("type", "undoDeal")
            intent.putExtra("userIds", agreeSelectUserList)
            intent.putExtra(ConsKeys.COMPANY_ID, companyId)
            intent.putExtra("depId", ConsValue.ROOT_DEPT_ID)
            startActivity(intent)
        } else {
            refreshProcessResult()
        }
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.ignore -> {
                val list = getSelectedRecordIdList()
                if (list.isNotEmpty()) {
                    val data = hashMapOf<String, Any>()
                    data["companyId"] = companyId
                    data["recordId"] = list
                    data["type"] = 2
                    viewModel.batchProcessUndoMsg(bindToLifecycle(), accessToken!!, data, 2)
                }
            }
            binding.agree -> {//tcp批量处理点击同意,说明，点击同意时，传递的是recordId集合，同意之后点击确认转移，传递的是userid集合
                val list = getSelectedRecordIdList()
                if (list.isNotEmpty()) {
                    agreeSelectUserList.clear()
                    agreeSelectUserList.addAll(getSelectedUserIdList())
                    val data = hashMapOf<String, Any>()
                    data["companyId"] = companyId
                    data["recordId"] = list
                    data["type"] = 1
                    viewModel.batchProcessUndoMsg(bindToLifecycle(), accessToken!!, data, 1)
                }
            }
            binding.allSelectLayout -> {
                binding.allSelectLayout.isSelected = !binding.allSelectLayout.isSelected
                if (undoList.isNotEmpty() && adapter.mData.isNotEmpty()) {
                    adapter.mData.forEach {
                        it.isSelected = binding.allSelectLayout.isSelected
                    }
                    adapter.notifyDataSetChanged()
                }
            }
        }
    }

    private fun getSelectedRecordIdList(): ArrayList<String> {
        val recordIdList = arrayListOf<String>()
        if (!adapter.mData.isNullOrEmpty())
            adapter.mData.forEach {
                if (it.isSelected) {
                    recordIdList.add(it.recordId)
                }
            }
        return recordIdList
    }

    private fun getSelectedUserIdList(): ArrayList<String> {
        val userIdList = arrayListOf<String>()
        if (!adapter.mData.isNullOrEmpty())
            adapter.mData.forEach {
                if (it.isSelected) {
                    userIdList.add(it.userId)
                }
            }
        return userIdList
    }

}