package com.joinutech.addressbook.view.tcpimpages

import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.EditorInfo
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.ddbes.library.im.imtcp.ConstantImMsgType
import com.ddbes.library.im.imtcp.dbbean.FriendBean
import com.ddbes.library.im.imtcp.dbbean.Message
import com.ddbes.library.im.imtcp.dbbean.Session
import com.ddbes.library.im.imtcp.dbope.SessionDaoOpe
import com.ddbes.library.im.imtcp.imservice.translatorhelper.TranslatorHelper
import com.ddbes.library.im.util.FriendCacheHolder
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.ContactsAdapter
import com.joinutech.addressbook.databinding.ActivityFriendListLayoutBinding
import com.joinutech.addressbook.view.OnContactSelectListener
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.AddOrgMsgBean
import com.joinutech.ddbeslibrary.bean.ContactModel
import com.joinutech.ddbeslibrary.bean.LetterComparator
import com.joinutech.ddbeslibrary.bean.OutMsgPersonBean
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.widget.wavesidebar.PinnedHeaderDecoration
import io.reactivex.Observable
import io.reactivex.ObservableOnSubscribe
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.Collections

@Deprecated("no used")//被SearchFriendToTranslateActivity2替换
class SearchFriendToTranslateActivity : MyUseBindingActivity<ActivityFriendListLayoutBinding>() {

    override val contentViewResId: Int = R.layout.activity_friend_list_layout
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityFriendListLayoutBinding {
        return ActivityFriendListLayoutBinding.inflate(layoutInflater)
    }

    @Autowired
    @JvmField
    var type: String = ""

    @Autowired
    @JvmField
    var ddMessage: Message? = null

    private lateinit var mShowModels: ArrayList<ContactModel>
    private var mAllShowModels = arrayListOf<ContactModel>()
    private lateinit var mAdapter: ContactsAdapter
    private var userSet: HashSet<String> = HashSet()

    //搜索之前保存有是否选中的列表，从上个页传入
    /**已选中过的成员id*/
    @Autowired
    @JvmField
    var selectedList: ArrayList<String>? = null

    @Autowired
    @JvmField
    var companyId: String = ""

    @Autowired
    @JvmField
    var companyName: String = ""

    @Autowired
    @JvmField
    var companyLogo: String = ""
    private var isAllSelect = false

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        when (type) {
            "transMsg", "transGroupMsg", "addOrgMsg" -> {
                if (type == "addOrgMsg") {
                    setPageTitle("邀请担当好友")
                } else {
                    setPageTitle("选择担当好友")
                }
                setRightTitle("发送", this)
            }
        }
    }

    private lateinit var emptyPage: PageEmptyView

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        if (selectedList == null) selectedList = arrayListOf()
        binding.mainRecycler.layoutManager = LinearLayoutManager(this)
        val decoration = PinnedHeaderDecoration()
        decoration.registerTypePinnedHeader(1) { _, _ -> true }
        binding.mainRecycler.addItemDecoration(decoration)
        when (type) {
            "transMsg", "transGroupMsg" -> {
                binding.searchInclueLayout.cancel.visibility = View.VISIBLE
                binding.searchInclueLayout.search.hint = "搜索担当好友"
                binding.searchInclueLayout.delete.setImageResource(com.joinutech.ddbeslibrary.R.drawable.del_img)
            }
            "addOrgMsg" -> {
                binding.searchInclueLayout.search.hint = "输入要搜索的用户名"
                binding.searchInclueLayout.cancel.visibility = View.GONE
                binding.searchInclueLayout.delete.setImageResource(R.drawable.icon_delete_square)
            }
        }
        binding.searchInclueLayout.delete.visibility = View.INVISIBLE

        emptyPage = findViewById(R.id.layout_empty_layout)
    }

    override fun initLogic() {
        super.initLogic()
        mShowModels = ArrayList()
        mAdapter = ContactsAdapter(mShowModels, mContext!!)
        mAdapter.setType(type)
        // RecyclerView设置相关
        binding.mainRecycler.adapter = mAdapter
        getAllUsers()
        initRecyclerView()
        initSearchAction()
        binding.searchInclueLayout.cancel.setOnClickListener(this)
        binding.searchInclueLayout.delete.setOnClickListener(this)
        binding.selectCheck.setOnClickListener(this)
    }

    private fun getAllUsers() {
        getLoadingDialog("", true)

        searchFriendListNew()

    }

    /**处理公司成员数据*/
    private fun dealCompanyMemberBean(t: List<SearchMemberBean>): List<FriendBean> {
        val list = arrayListOf<FriendBean>()
        if (t.isNotEmpty()) {
            for (item in t) {
                when (type) {
                    "selectProjectJoiner", "ProjectHandOverSelectMember",
                    "CreateProgramSelectProjectJoiner",/* "selectMember", */"selectMembers" -> {
                        // TODO: 2019/8/14 添加状态，处理单选和多选类型人员选择
                        val friendBean = FriendBean()
                        friendBean.avatar = item.headimg
                        friendBean.userId = item.userId
                        friendBean.name = item.name
                        if (type != "ProjectHandOverSelectMember") {
                            friendBean.remark = item.positionName
                        }
                        list.add(friendBean)
                    }
                    else -> {
                        if (item.userId != userId) {
                            val friendBean = FriendBean()
                            friendBean.avatar = item.headimg
                            friendBean.userId = item.userId
                            friendBean.name = item.name
                            list.add(friendBean)
                        }
                    }
                }
            }
        }
        return list
    }

    private fun initRecyclerView() {
        if (StringUtils.isNotBlankAndEmpty(type)) {
            handleRightTextShow()
            /**适配器点击监听*/
            mAdapter.setOnClickListener { contact ->
                when (contact.sessionType) {
                    1 -> {
                        if (contact.chatStatus == 1) {
                            ToastUtil.show(this, "该用户已注销")
                            return@setOnClickListener
                        }
                    }
                    2, ConstantImMsgType.SSChatMessageTypeChatGroupNotice -> {
                        if (contact.chatStatus == 1) {
                            ToastUtil.show(this, "该群组已解散")
                            return@setOnClickListener
                        }
                    }
                }
                contact.check = !contact.check
                if (contact.check) {
                    when (type) {
                        "transMsg", "transGroupMsg", "addOrgMsg" -> {
                            if (userSet.size < 10) {
                                userSet.add(contact.userId)
                            } else {
                                if (type == "addOrgMsg") {
                                    ToastUtil.show(mContext!!, "最多可邀请给10个好友")
                                } else {
                                    ToastUtil.show(mContext!!, "最多可转发给10个好友")
                                }
                            }
                        }
                        "givePermission", "selectProjectJoiner",
                        "CreateProgramSelectProjectJoiner", "selectMembers" -> {
                            userSet.add(contact.userId)
                        }
                    }
                } else {
                    //取消勾选
                    for (item in userSet) {
                        if (item == contact.userId) {
                            userSet.remove(item)
                            return@setOnClickListener
                        }
                    }
                }
                handleRightTextShow()
            }

            mAdapter.selectListener = object : OnContactSelectListener {
                override fun onSelect(position: Int) {
                    // TODO: 2020/8/3 10:33 单选后结果返回
                    if ("selectMember" == type || type == "shareToFriend") {
                        val intent = Intent()
                        intent.putExtra(type, GsonUtil.toJson(mShowModels[position]))
                        setResult(Activity.RESULT_OK, intent)
                        finish()
                    }
                }

                override fun onUnSelect(position: Int) {

                }
            }
        }
    }

    /**
     * 创建项目选择参与人员时业务处理
     * 默认状态 选择跳过
     * 选择人员后或者全选后，右上角显示
     * */
    private fun handleRightTextShow() {
        if (type == "CreateProgramSelectProjectJoiner") {
            if (userSet.isNotEmpty()) {
                tv_rightTitle!!.text = "完成"
                tv_rightTitle!!.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.colorFF333333))
            } else {
                tv_rightTitle!!.text = "跳过"
                tv_rightTitle!!.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color2479ED))
            }
        }
    }

    /**搜索框搜索事件处理*/
    private fun initSearchAction() {
        binding.searchInclueLayout.search.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val string = s.toString()
                if (StringUtils.isNotBlankAndEmpty(string)) {
                    binding.searchInclueLayout.delete.visibility = View.VISIBLE
                    binding.clTitleLayout.visibility = View.GONE
                } else {
                    binding.searchInclueLayout.delete.visibility = View.INVISIBLE
                    //没输入内容就显示全部列表
                    mShowModels = mAllShowModels
                    if (type != "ProjectHandOverSelectMember") {
                        dealAllCheckShow()
                    }
                    if (type == "selectMember" || type == "shareToFriend") {
                        // 全选成员不显示
                        binding.clTitleLayout.visibility = View.GONE
                    }
                    if (type == "selectMembers") {
                        binding.selectAllMember.text = "全选"
                    }
                    dealFriendAllResult()
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        })
        binding.searchInclueLayout.search.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                getLoadingDialog("", true)
                //输入内容不为空
                val keyWord = binding.searchInclueLayout.search.text.toString().trim()
                if (companyId.isNotEmpty()) {
                    searchDepMemberWithInput(keyWord)
                } else {
//                        searchFriendListWithInput(string)
                    searchFriendListWithInputNew(keyWord)
                }
                true
            }
            false
        }
    }

    /**
     * 页面数据请求后处理
     * 好友
     * 团队部门成员
     * */
    private fun dealResult(it: List<FriendBean>) {
        if (it.isNotEmpty()) {
            emptyPage.visibility = View.GONE
            binding.dataLayout.visibility = View.VISIBLE
            mShowModels = dealFriendList(it, arrayListOf())
            //同步之前的勾选状态
            for (allItem in mShowModels) {
                for (item in userSet) {
                    if (item == allItem.userId) {
                        allItem.check = true
                    }
                }
            }
            mAdapter.setDataList(mShowModels)
        } else {
            dealFriendSearchNoResult()
        }
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return true
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            tv_rightTitle -> {
                dealRightEvent()
            }
            binding.searchInclueLayout.delete -> {
                binding.searchInclueLayout.search.text.clear()
            }
            binding.searchInclueLayout.cancel -> {
                finish()
            }
            binding.selectCheck -> {
                dealAllCheck()
                handleRightTextShow()
            }
        }
    }

    /**团队人员获取后相关数据展示和业务处理*/
    private fun dealOrgMemberProjectResult(t: List<SearchMemberBean>) {
        emptyPage.visibility = View.GONE
        binding.dataLayout.visibility = View.VISIBLE
        if (type == "CreateProgramSelectProjectJoiner") {
            binding.createProgramText.visibility = View.VISIBLE
        } else {
            binding.createProgramText.visibility = View.GONE
        }
        setPageTitle("团队成员(${t.size})")
        /**处理公司成员数据，转换为好友数据后显示到页面*/
        val list: List<FriendBean> = dealCompanyMemberBean(t)
        mShowModels = dealFriendList(list, selectedList!!)
        dealFriendAllResult()
        if (type != "ProjectHandOverSelectMember") {
            dealAllCheckShow()
        }
        if (type == "selectMember") {
            // 全选成员不显示
            binding.clTitleLayout.visibility = View.GONE
            if (mShowModels.isNullOrEmpty()) {
                dealFriendSearchNoResult()
            }
        }
        if (type == "selectMembers") {
            binding.selectAllMember.text = "全选"
        }
        mAllShowModels = mShowModels
    }

    /** 搜索团队成员*/
    private fun searchOrgMember() {
        AddressbookService.searchDepMember(companyId, "")
                .compose(bindToLifecycle())
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<List<SearchMemberBean>>() {
                    override fun onError(ex: ApiException) {
                        dismissDialog()
                        dealFriendSearchNoResult()
                        ToastUtil.show(mContext!!, ex.message)
                    }

                    override fun onComplete() {}
                    override fun onNext(t: List<SearchMemberBean>?) {
                        dismissDialog()
                        if (t != null) {
                            if (t.isNotEmpty()) {
                                if (type == "selectProjectJoiner"
                                        || type == "CreateProgramSelectProjectJoiner"
                                        || type == "ProjectHandOverSelectMember"
                                        || type == "selectMember"
                                        || type == "selectMembers") {
                                    //选择项目参与人员请求团队下的人员
                                    // TODO: 2019/8/14 增加状态，以获取任务成员选择
                                    dealOrgMemberProjectResult(t)
                                } else {
                                    emptyPage.visibility = View.GONE
                                    binding.dataLayout.visibility = View.VISIBLE
                                    binding.createProgramText.visibility = View.GONE
                                    val list: List<FriendBean> = dealCompanyMemberBean(t)
                                    mShowModels = dealFriendList(list, selectedList!!)
                                    dealFriendAllResult()
                                    mAllShowModels = mShowModels
                                }
                            } else {
                                dealFriendSearchNoResult()
                            }
                        }
                    }

                })
    }

    /** 搜索好友信息*/
    private fun searchFriendListNew() {
        //tcp新版im转发列表显示的是会话列表
        Observable.create(ObservableOnSubscribe<List<Session>> { it2 ->
            val sessionList = SessionDaoOpe.instance.queryAllByUid(this, userId)

            if (type == "addOrgMsg") {//如果是邀请好友加入公司
                it2.onNext(sessionList.filter { it.sessionType == 1 })
            } else {
                it2.onNext(sessionList.filter {
                    it.sessionType == 1 || it.sessionType == 2
                            || it.sessionType == ConstantImMsgType.SSChatMessageTypeChatGroupNotice
                })
            }

        })
                .compose(bindToLifecycle())
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe({ sessionList ->
                    hideLoading()
                    emptyPage.visibility = View.GONE
                    binding.dataLayout.visibility = View.VISIBLE

                    mShowModels = dealFriendList2(sessionList)
                    dealFriendAllResult()
                    mAllShowModels = mShowModels
                }, { throwable ->
                    hideLoading()
                    dealFriendSearchNoResult()
                    ToastUtil.show(this, throwable.message.toString())
                })

    }

    private fun searchFriendListWithInputNew(keyWord: String) {
        if (keyWord.isNullOrBlank()) return
        FriendCacheHolder.searchFriend(this, bindToLifecycle(), keyWord,
                onResult = {
                    hideLoading()
                    dealResult(it)
                },
                onError = {
                    hideLoading()
                    dealFriendSearchNoResult()
                    ToastUtil.show(mContext!!, it)
                })
    }

    private fun searchFriendListWithInput(keyWord: String) {
//        UserService.getFriendList(keyWord)////获取所有好友信息，建议改成本地搜索
//                .compose(bindToLifecycle())
//                .compose(ErrorTransformer.getInstance())
//                .subscribe(object : BaseSubscriber<List<FriendBean>>() {
//                    override fun onError(ex: ApiException) {
//                        dismissDialog()
//                        dealFriendSearchNoResult()
//                        ToastUtil.show(mContext!!, ex.message)
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(t: List<FriendBean>?) {
//                        dismissDialog()
//                        if (t != null) dealResult(t)
//                    }
//
//                })
    }

    /**搜索部门成员*/
    private fun searchDepMemberWithInput(keyWord: String) {
        AddressbookService.searchDepMember(companyId, keyWord)
                .compose(bindToLifecycle())
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<List<SearchMemberBean>>() {
                    override fun onError(ex: ApiException) {
                        dismissDialog()
                        dealFriendSearchNoResult()
                        ToastUtil.show(mContext!!, ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: List<SearchMemberBean>?) {
                        dismissDialog()
                        if (t != null) {
                            val list: List<FriendBean> = dealCompanyMemberBean(t)
                            dealResult(list)
                        }
                    }

                })
    }

    /**处理全选后显示业务*/
    private fun dealAllCheckShow() {
        var noSelectNum = 0
        if (mShowModels.isNotEmpty()) {
            mShowModels.forEach {
                if (it.noSelect) noSelectNum++
            }
            if (mShowModels.size == noSelectNum) {
                //说明全部不可选，那就不存在全选非全选问题
                binding.clTitleLayout.visibility = View.GONE
            } else {
                binding.clTitleLayout.visibility = View.VISIBLE
            }
        }
    }

    /**全选数据处理逻辑*/
    private fun dealAllCheck() {
        userSet.clear()
        selectedList!!.clear()
        isAllSelect = !isAllSelect
        binding.selectCheck.isSelected = isAllSelect
        if (isAllSelect) {
            //全选了
            mAdapter.list.forEach {
                if (!it.noSelect) {
                    //不可选的不参加全选和非全选
                    it.check = true
                    userSet.add(it.userId)
                    selectedList!!.add(it.userId)
                }
            }
            mShowModels.forEach {
                if (!it.noSelect) it.check = true
            }
            mAdapter.notifyDataSetChanged()
        } else {
            //全不选
            mAdapter.list.forEach {
                if (!it.noSelect) it.check = false
            }
            mShowModels.forEach {
                if (!it.noSelect) it.check = false
            }
            mAdapter.notifyDataSetChanged()
        }
    }

    /**右上角点击事件处理*/
    private fun dealRightEvent() {
        val userList = ArrayList<String>()
        if (userSet.isNotEmpty()) {
            for (item in userSet) {
                userList.add(item)
            }
        }
        if (userList.isNotEmpty()) {
            dealDetailEvent(userList)
        }
    }

    /**转发消息时，发送成功后通知该页面关闭
     * // TODO: 2020/8/3 10:14 需要改为 选择人员后，回到调用位置，发送信息，不需要关联的到人员选择功能页面
     * */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun translateMsgSuccess(event: EventBusEvent<String>) {
        if (event.code == EventBusAction.TRANSMSG_SUCCEESS) {// 监听发送成功事件
            finish()
        }
    }

    /**请求结果加载*/
    private fun dealFriendAllResult() {
        if (userSet.isEmpty()) {
            //说明是第一次还没开始搜索或者说没有被选中的人
            if (type == "selectProjectJoiner" || type == "CreateProgramSelectProjectJoiner") {
                //添加项目新成员时需要把选择过的人员设为不可选
                dealSelectNoSelectGreyShow()
            } else {
                if (selectedList!!.isNotEmpty()) {
                    //说明从上个页面传过来了数据
                    userSet.addAll(selectedList!!)
                }
            }
        } else {
            for (item in userSet) {
                for (allItem in mShowModels) {
                    allItem.check = false
                    if (item == allItem.userId) {
                        allItem.check = true
                    }
                }
            }
        }
        /**全选按钮全选状态根据已选人员数量显示*/
        //做全选非全选按钮的显示与否
        if (userSet.size == 0) {
            isAllSelect = false
            binding.selectCheck.isSelected = isAllSelect
        } else if (userSet.size == mShowModels.size) {
            isAllSelect = true
            binding.selectCheck.isSelected = isAllSelect
        }
        mAdapter.setDataList(mShowModels)
    }

    /**创建任务时过滤当前创建者*/
    private fun dealSelectNoSelectGreyShow() {
        //创建项目时创建者不可取消
        if (type == "CreateProgramSelectProjectJoiner") {
            selectedList = arrayListOf(userId!!)
        }
        if (selectedList != null) {
            if (selectedList!!.isNotEmpty() && mShowModels.isNotEmpty()) {
                for (select in selectedList!!) {
                    for (item in mShowModels) {
                        if (select == item.userId) {
                            item.noSelect = true
                            userSet.add(item.userId)
                            break
                        }
                    }
                }
            }
        }
    }

    /**处理多选人员后，点击完成时的业务处理
     * // TODO: 2020/8/3 10:16 相关业务需要回归到业务调用处处理，不能卸载人员选择页面
     * */
    private fun dealDetailEvent(userList: ArrayList<String>) {
        //把用户的id转换为带relation的bean类
        fun userIdToUserPerson(list: ArrayList<OutMsgPersonBean>) {
            if (userList.isNotEmpty() && mShowModels.isNotEmpty()) {
                for (item in mShowModels) {
                    for (id in userList) {
                        if (id == item.userId) {
                            list.add(OutMsgPersonBean(id, item.relation == 2,
                                    name = item.name, avator = item.logo, sessionType = item.sessionType, sessionId = item.sessionId))
                            break
                        }
                    }
                }
            }
        }
        when (type) {
            "transMsg", "transGroupMsg" -> {
                val list = arrayListOf<OutMsgPersonBean>()
                userIdToUserPerson(list)
                if (ddMessage != null && userList.isNotEmpty()) {
                    if (type == "transGroupMsg") {
                        ddMessage!!.uid = userId
//                        MsgTranslator.translationGroupChatMsgToSingleChat(mContext!!, ddMessage!!, list)
                        TranslatorHelper.translateMsgToChat(mContext!!, ddMessage!!, list)
                    } else if (type == "transMsg") {
                        ddMessage!!.uid = userId
//                        MsgTranslator.translationSingleChatMsgToSingleChat(mContext!!, ddMessage!!, list)
                        TranslatorHelper.translateMsgToChat(mContext!!, ddMessage!!, list )
                    }
                }
            }
            "addOrgMsg" -> {//tcp发送团队邀请给好友
                val bean = AddOrgMsgBean(companyId, companyLogo, companyName)
                val list = arrayListOf<OutMsgPersonBean>()
                userIdToUserPerson(list)
                TranslatorHelper.sendAddOrgMsgToFriend(mContext!!, userId!!, bean, list, companyName,
                clickLeft = {},clickRight = {})
//                MsgTranslator.sendAddOrgMsgToFriend(mContext!!, userId!!.toString(), bean, list, companyName)
            }
            "givePermission", "selectProjectJoiner", "ProjectHandOverSelectMember",
            "CreateProgramSelectProjectJoiner" -> {
                //保存，并返回团队设置管理人员列表
                val userNum = if (userList.isNullOrEmpty()) {
                    0
                } else {
                    userList.size
                }
//                ToastUtil.show(mContext!!, "已选择用户数量为${userNum}人")
                when (type) {
                    "givePermission" -> EventBusUtils.sendEvent(EventBusEvent(
                            EventBusAction.SELECTEDPER_USERLIST, userList))
                    "CreateProgramSelectProjectJoiner" -> {
                        EventBusUtils.sendEvent(EventBusEvent(
                                EventBusAction.SELECTEPROJECT_JOINERLIST, userList))
                    }
                    "selectProjectJoiner" -> {
                        if (selectedList!!.isNotEmpty()) {
                            val list = arrayListOf<String>()
                            for (item in mShowModels) {
                                if (!item.noSelect && item.check) {
                                    list.add(item.userId)
                                }
                            }
                            EventBusUtils.sendEvent(EventBusEvent(
                                    EventBusAction.SELECTPROJECTJOINER_USERLIST, list))
                        } else {
                            EventBusUtils.sendEvent(EventBusEvent(
                                    EventBusAction.SELECTPROJECTJOINER_USERLIST, userList))
                        }
                    }
                }
                finish()
            }
            "selectMembers" -> {
                val selectMember = arrayListOf<ContactModel>()
                if (userList.isNotEmpty()) {
                    for (item in mShowModels) {
                        item.check
                        if (item.userId in userList) {
                            selectMember.add(item)
                        }
                    }
                }
                val intent = Intent()
                intent.putExtra(type, GsonUtil.toJson(selectMember))
                setResult(Activity.RESULT_OK, intent)
                finish()
            }
        }
    }

    /**人员复选情况处理*/
    private fun dealFriendList(it: List<FriendBean>, selectedList: ArrayList<String>):
            ArrayList<ContactModel> {
        val list = ArrayList<ContactModel>()
        if (it.isNotEmpty()) {
            for (item in it) {
                var bean = ContactModel(item.name, item.avatar)
                when (type) {
                    "selectMember", "selectProjectJoiner", "CreateProgramSelectProjectJoiner" -> {
                        bean.phoneNum = item.remark
                    }
                    else -> {
                        if (StringUtils.isNotBlankAndEmpty(item.remark)) {
                            bean = ContactModel(item.remark, item.avatar)
                        }
                    }
                }
                bean.userId = item.userId
                list.add(bean)
            }
            if (selectedList.isNotEmpty()) {
                for (item in list) {
                    for (id in selectedList) {
                        if (item.userId == id) {
                            item.check = true
                        }
                    }
                }
            }
            Collections.sort(list, LetterComparator())
        }
        return list
    }

    //tcp新加的方法
    private fun dealFriendList2(it: List<Session>):
            ArrayList<ContactModel> {
        val list = ArrayList<ContactModel>()
        if (it.isNotEmpty()) {
            for (item in it) {
                var bean = ContactModel(item.name, item.headerUrl)
                bean.userId = item.appChatId
                bean.sessionType = item.sessionType
                bean.sessionId = item.sessionId
                bean.chatStatus = item.chatStatus

                list.add(bean)
            }

            Collections.sort(list, LetterComparator())
        }
        return list
    }

    /**处理好友搜索无数据时页面显示*/
    private fun dealFriendSearchNoResult() {
        mShowModels.clear()
        binding.dataLayout.visibility = View.GONE
        when (type) {
            "shareToFriend", "transMsg", "transGroupMsg" -> {
                emptyPage.setContent("未找到符合条件的担当好友")
            }
            "selectProjectJoiner", "CreateProgramSelectProjectJoiner",
            "givePermission", "selectMember" -> {
                //团队人员列表
                emptyPage.setContent("未找到符合条件的结果")
            }
        }
        emptyPage.show()
    }
}

/*
interface OnContactSelectListener {
    fun onSelect(position: Int)
    fun onUnSelect(position: Int)
}*/
