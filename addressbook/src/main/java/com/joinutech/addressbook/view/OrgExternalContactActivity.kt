package com.joinutech.addressbook.view

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.joinutech.addressbook.R
import com.joinutech.addressbook.databinding.ActivityExternalContactBinding
import com.joinutech.addressbook.view.fragment.OrgExternalContactFragment
import com.joinutech.common.widget.OnEmptyClickListener
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.utils.ADD_EXTERNAL_CONTACT
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import net.lucode.hackware.magicindicator.ViewPagerHelper
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.ColorTransitionPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.SimplePagerTitleView
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * @Description: 团队外部联系人
 * @Author: 黄洁如
 * @Time: 2019/11/19 15:54
 * @packageName: com.joinutech.addressbook.view
 * @Company: JoinuTech
 */
class OrgExternalContactActivity : MyUseBindingActivity<ActivityExternalContactBinding>(), OnEmptyClickListener {

    override val contentViewResId: Int = R.layout.activity_external_contact
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityExternalContactBinding {
        return ActivityExternalContactBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = false

    var companyBean: WorkStationBean? = null
    private var titleList = arrayListOf<String>()
    private lateinit var adapter: ExternalContactPageAdapter
    private var currentPosition = 0
    private lateinit var fragments: Array<OrgExternalContactFragment?>
    var orgPermission: Boolean = false

    override fun initImmersion() {
        setPageTitle("外部协作人员")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        if (intent.getSerializableExtra("companyBean") != null) {
            companyBean = intent.getSerializableExtra("companyBean") as WorkStationBean?
            orgPermission = intent.getBooleanExtra("orgPermission", false)
        }
    }

    private lateinit var emptyPage: PageEmptyView

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        emptyPage = findViewById(R.id.layout_empty_layout)
    }

    override fun initLogic() {
        super.initLogic()
        titleList.add("全部")
        titleList.add("客户")
        titleList.add("渠道商")
        titleList.add("供应商")
        titleList.add("合作伙伴")
        titleList.add("其他类型")
        fragments = arrayOfNulls(titleList.size)
        for (i in fragments.indices) {
            fragments[i] = OrgExternalContactFragment.newInstance(i, companyBean?.companyId!!, orgPermission)
        }
        adapter = object : ExternalContactPageAdapter(this) {
            override fun createFragment(position: Int): Fragment {
                return fragments[position]!!
            }

        }
       
         binding.vpGroupPager.adapter = adapter
        binding.tbTopLayout.setBackgroundColor(Color.WHITE)
        val commonNavigator = CommonNavigator(this)
        commonNavigator.adapter = object : CommonNavigatorAdapter() {
            override fun getCount(): Int {
                return titleList.size
            }

            override fun getTitleView(context: Context, index: Int): IPagerTitleView {
                val simplePagerTitleView: SimplePagerTitleView = ColorTransitionPagerTitleView(context)
                simplePagerTitleView.normalColor = CommonUtils.getColor(mContext!!,
                    com.joinutech.ddbeslibrary.R.color.colorFF323232)
                simplePagerTitleView.selectedColor = CommonUtils.getColor(mContext!!,
                    com.joinutech.ddbeslibrary.R.color.color007FF4)
                simplePagerTitleView.text = titleList[index]
                simplePagerTitleView.setOnClickListener {
                    currentPosition = index
                     binding.vpGroupPager.currentItem = index
                }
                return simplePagerTitleView
            }

            override fun getIndicator(context: Context): IPagerIndicator {
                val linePagerIndicator = LinePagerIndicator(context)
                linePagerIndicator.mode = LinePagerIndicator.MODE_WRAP_CONTENT
                linePagerIndicator.setColors(CommonUtils.getColor(mContext!!,
                    com.joinutech.ddbeslibrary.R.color.color007FF4))
                return linePagerIndicator
            }
        }
         binding.vpGroupPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                currentPosition = position
//                fragments[position]!!.getData()
            }
        })
        
        binding.tbTopLayout.navigator = commonNavigator
        ViewPagerHelper.bind(binding.tbTopLayout,  binding.vpGroupPager)

        emptyPage.clickListener = this

        if (orgPermission) {
            emptyPage.setPositiveContent("点击添加")
        } else {
            emptyPage.setPositiveContent("")
        }
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            iv_rightTitle -> {
                onAction(1)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                ADD_EXTERNAL_CONTACT -> {
                    fragments[currentPosition]!!.getData()
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun isHaveData(event: EventBusEvent<Boolean>) {
        when (event.code) {
            EventBusAction.Event_ALL_EXTERNAL_LIST_ISHAVE -> {
                if (event.data!!) {
                    if (orgPermission) {
                        setRightImage(com.joinutech.ddbeslibrary.R.drawable.add_home_icon, this)
                    }
                    emptyPage.visibility = View.INVISIBLE
                    
                    binding.clConentLayout.visibility = View.VISIBLE
                } else {
                    emptyPage.visibility = View.VISIBLE
                    binding.clConentLayout.visibility = View.INVISIBLE
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun refreshData(event: EventBusEvent<Int>) {
        when (event.code) {
            EventBusAction.Event_REFRESH_EXTERNAL_CONTACT_LIST -> {
                fragments[event.data!!]!!.getData()
            }
        }
    }

    fun jumpToSearch(level: String, type: Int) {
        val intent = Intent(this, SearchExternalContactActivity::class.java)
        intent.putExtra("type", type)
        intent.putExtra("level", level)
        intent.putExtra("companyId", companyBean?.companyId)
        intent.putExtra("isEdit", companyBean?.deptId == "0")
        startActivity(intent)
    }

    abstract class ExternalContactPageAdapter(fragmentActivity: FragmentActivity)
        : FragmentStateAdapter(fragmentActivity) {
        override fun getItemCount(): Int {
            return 6
        }
    }

    override fun onAction(actionCode: Int) {
        when (actionCode) {
            1 -> {
                if (companyBean != null) {
                    val intent = Intent(mContext, AddOrgExternalContactActivity::class.java)
                    intent.putExtra("currentCompanyId", companyBean?.companyId)
                    intent.putExtra("typeIndex", currentPosition)
                    startActivityForResult(intent, ADD_EXTERNAL_CONTACT)
                }
            }
            2 -> {
            }
            else -> {
                fragments[0]!!.getData()
            }
        }
    }
}