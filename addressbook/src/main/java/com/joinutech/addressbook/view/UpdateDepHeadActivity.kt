package com.joinutech.addressbook.view

import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.SelectedMemberConstract
import com.joinutech.addressbook.databinding.ActivityAddDepHeadBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.Member
import com.joinutech.ddbeslibrary.utils.*
import javax.inject.Inject
import javax.inject.Named

/**
 * <AUTHOR>
 * @date   2019/5/29 14:39
 * @className: UpdateDepHeadActivity
 *@Description: 更新部门负责人
 */
class UpdateDepHeadActivity : MyUseBindingActivity<ActivityAddDepHeadBinding>() {

    override val contentViewResId: Int
        get() = R.layout.activity_add_dep_head

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityAddDepHeadBinding {
        return ActivityAddDepHeadBinding.inflate(layoutInflater)
    }

    private var type = 1
    private var name = ""
    private var depHeadText = "未设置人员"
    private var headUserId = "0"
    private var ownDepId = "0"
    private var list: ArrayList<Member> = arrayListOf()
    private var companyId: String = ""
    private var depHeadNameText = ""
    @Inject
    @field:Named(AddressbookUtil.SELECTEDMEMBER_PRESENTER)
    lateinit var presenter: SelectedMemberConstract.SelectedMemberPresenter
    private var returnDepHead = "未设置人员"
    private var returnDepHeadIcon = ""
    private var returnDepHeadUserId = "0"
    private var isClickNoHead = false
    private var positionId = ""

    override fun initImmersion() {
        delIntent()
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        <EMAIL>("保存", View.OnClickListener {
            val choosedPersonName=  binding.tvDeptHeadName.text.toString()
            if (choosedPersonName=="未设置人员") {
               val showToastContent=if(type==2){
                   "请设置部门副职负责人"
               }else{
                   "请设置部门负责人"
               }
                ToastUtil.show(this,showToastContent)
            }else{
                saveDepHeadData()
            }
        })
    }

    private fun delIntent() {
        if (intent != null) {
            if (intent.getIntExtra("type", 0) != 0) {
                type = intent.getIntExtra("type", 0)
            }
            if (type == 2) {
                setPageTitle("部门副职负责人")
                 binding.depHeadNameEdit.setText("部门副职负责人")
                binding.hintText.text="部门副职负责人"
            } else {
                setPageTitle("部门负责人")
                binding.depHeadNameEdit.setText("部门负责人")
                binding.hintText.text="部门负责人"
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("name"))) {
                name = intent.getStringExtra("name")!!
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("depHead"))) {
                depHeadText = intent.getStringExtra("depHead")!!
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("depHeadNameText"))) {
                depHeadNameText = intent.getStringExtra("depHeadNameText")!!
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("headUserId"))) {
                headUserId = intent.getStringExtra("headUserId")!!
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("positionId"))) {
                positionId = intent.getStringExtra("positionId")!!
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("depId"))) {
                ownDepId = intent.getStringExtra("depId")!!
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                companyId = intent.getStringExtra("companyId")!!
            }
            if (intent.getSerializableExtra("memberList") != null)
                list = intent.getSerializableExtra("memberList") as ArrayList<Member>
        } else setPageTitle("部门负责人")
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        DaggerAddressbookComponent.builder().build().inject(this)
    }

        override fun initLogic() {
        super.initLogic()

        initDataShow()
        binding.hintText.visibility = View.INVISIBLE
        binding.depHeadNameEdit.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {}

            override fun beforeTextChanged(s: CharSequence?,
                                           start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?,
                                       start: Int, before: Int, count: Int) {
                binding.depHeadNameEdit.setSelection(s.toString().length)//将光标移至文字末尾
                if (s != null) {
                    if (StringUtils.isEmpty(s.toString())){
                        binding.hintText.visibility = View.VISIBLE
                        <EMAIL>()
                    }else {
                        binding.hintText.visibility = View.INVISIBLE
                        <EMAIL>("保存", View.OnClickListener {
                            val choosedPersonName=binding.tvDeptHeadName.text.toString()
                            if (choosedPersonName=="未设置人员") {
                                val showToastContent=if(type==2){
                                    "请设置部门副职负责人"
                                }else{
                                    "请设置部门负责人"
                                }
                                ToastUtil.show(this@UpdateDepHeadActivity,showToastContent)
                            }else{
                                saveDepHeadData()
                            }
                        })
                    }
                } else {
                    binding.hintText.visibility = View.VISIBLE
                    <EMAIL>()
                }
            }

        })
        binding.depHeadNext.setOnClickListener(this)
        binding.tvDeptHeadName.setOnClickListener(this)
    }

    private fun initDataShow() {
        if (StringUtils.isNotBlankAndEmpty(depHeadText) && depHeadText != "未设置人员") {
            binding.tvDeptHeadName.setTextColor(CommonUtils.getColor(mContext!!,
                    com.joinutech.ddbeslibrary.R.color.colorFF333333))
            binding.tvDeptHeadName.text = depHeadText
        }
        if (StringUtils.isNotBlankAndEmpty(depHeadNameText)) {
            binding.depHeadNameEdit.setText(depHeadNameText)
            binding.depHeadNameEdit.setSelection(depHeadNameText.length)
            <EMAIL>("保存", View.OnClickListener {
                val choosedPersonName=binding.tvDeptHeadName.text.toString()
                if (choosedPersonName=="未设置人员") {
                    val showToastContent=if(type==2){
                        "请设置部门副职负责人"
                    }else{
                        "请设置部门负责人"
                    }
                    ToastUtil.show(this,showToastContent)
                }else{
                    saveDepHeadData()
                }
            })
        }
    }

    private fun saveDepHeadData() {
        if (returnDepHeadUserId!="0") {
            depHeadNameText = binding.depHeadNameEdit.text.toString()
            //设置了负责人
            if (returnDepHeadUserId == headUserId) {
                //是否前后设置的负责人相同，没有变化
                finish()
            } else {
                dealSetDepHead()
            }
        } else {
            if (depHeadNameText != binding.depHeadNameEdit.text.toString()
                    && !isClickNoHead && headUserId!="0") {
                //只换了名字
                //设置一个负责人
                //// TODO: 2021/7/9 14:23  负责人类型：1：正负责人。2：副负责人
                getLoadingDialog("设置负责人", false)
                presenter.changeDepHead(bindToLifecycle(), accessToken!!,
                        ownDepId, positionId, headUserId,
                        binding.depHeadNameEdit.text.toString(), type, companyId, {
                    dismissDialog()
                    val intent = Intent()
                    intent.putExtra("type", type)
                    setResult(Activity.RESULT_OK, intent)
                    finish()
                }, {
                    dismissDialog()
                    ToastUtil.show(mContext!!, it)
                })
            } else {
                depHeadNameText = binding.depHeadNameEdit.text.toString()
                //前后都是未设置负责人,并且是选择人员中的未选择人员，以防第一次进来确实没设人员
                if (returnDepHead == depHeadText && isClickNoHead) {
                    //没有变化
                    finish()
                } else {
                    setDepManager()
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                SET_DEP_HEAD -> {
                    if (data != null) {
                        if (StringUtils.isNotBlankAndEmpty(data.getStringExtra("depHead"))){
                            returnDepHead = data.getStringExtra("depHead")!!
                        }
                        if (StringUtils.isNotBlankAndEmpty(data.getStringExtra("userId"))){
                            returnDepHeadUserId =
                                    data.getStringExtra("userId")!!
                        }
                        isClickNoHead =
                                data.getBooleanExtra("isClickNoHead", false)
                        if (StringUtils.isNotBlankAndEmpty(data.getStringExtra("depHeadIcon")))
                            returnDepHeadIcon = data.getStringExtra("depHeadIcon")!!
                        if (isClickNoHead) {
                            returnDepHead = "未设置人员"
                            binding.tvDeptHeadName.setTextColor(CommonUtils.getColor(mContext!!,
                                com.joinutech.ddbeslibrary.R.color.colorFFAAAAAA))
                            binding.tvDeptHeadName.text = returnDepHead
                        } else {
                            if (StringUtils.isNotBlankAndEmpty(returnDepHead)) {
                                binding.tvDeptHeadName.setTextColor(CommonUtils.getColor(mContext!!,
                                    com.joinutech.ddbeslibrary.R.color.color1E87F0))
                                binding.tvDeptHeadName.text = returnDepHead
                            }
                        }
                    }
                }
            }
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.depHeadNext, R.id.tv_dept_head_name -> {
//            R.id.depHeadNext, R.id.tvDeptHeadName -> {
                val intent = Intent(mContext, SelectedMemberActivity::class.java)
                intent.putExtra("type", "depheadset")
                intent.putExtra("memberList", list)
                intent.putExtra("depHead", depHeadText)
                intent.putExtra("depId", ownDepId)
                intent.putExtra("headUserId", headUserId)
                intent.putExtra("companyId", companyId)
                startActivityForResult(intent, SET_DEP_HEAD)
            }
        }
    }

    private fun dealDialogContentShow(selectManagerOldPositionName: String) {
        val dialog = MyDialog(mContext, 280, 166, "",
                needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0)
        val spanny = Spanny()
                .append("你选择的人员")
                .append("【$returnDepHead】", ForegroundColorSpan(
                        CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color107EEB)))
                .append("在当前部门已任职")
                .append("【$selectManagerOldPositionName】", ForegroundColorSpan(
                        CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color107EEB)))
                .append("的职位，继续操作的话该用户的职位将发生变更")
        dialog.setContentText(spanny)
        dialog.setBtnRightText("完成")
        dialog.show()
        dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
            override fun clickRightBtn() {
                dialog.dismiss()
                setDepManager()
            }
        })
    }

    private fun dealSetDepHead() {
        var selectManagerOldPositionName = ""
        for (item in list) {
            if (item.userId == returnDepHeadUserId) {
                if (item.positionId.isNotEmpty()) {
                    selectManagerOldPositionName = item.positionName
                }
                break
            }
        }
        if (StringUtils.isNotBlankAndEmpty(selectManagerOldPositionName)) {
            //自己更换了不同职位
            dealDialogContentShow(selectManagerOldPositionName)
        } else {
            //更换了负责人
            setDepManager()
        }
    }

    private fun setDepManager() {
        //设置一个负责人
        //// TODO: 2021/7/9 14:26  负责人类型：1：正负责人。2：副负责人
        getLoadingDialog("设置负责人", false)
        presenter.changeDepHead(bindToLifecycle(), accessToken!!,
                ownDepId, positionId, returnDepHeadUserId, depHeadNameText, type, companyId, {
            dismissDialog()
            val intent = Intent()
            intent.putExtra("type", type)
            setResult(Activity.RESULT_OK, intent)
            finish()
        }, {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }


    

    override fun showToolBar(): Boolean {
        return true
    }



    override fun openArouterReceive(): Boolean {
        return false
    }
}