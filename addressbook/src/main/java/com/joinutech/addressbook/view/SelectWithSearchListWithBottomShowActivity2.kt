package com.joinutech.addressbook.view

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.addressbook.R
import com.joinutech.addressbook.databinding.ActivityFriendSelectListWithSearchBinding
import com.joinutech.addressbook.viewModel.FriendSelectViewModel
import com.joinutech.common.adapter.MyAdapter
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.FriendSelectBean
import com.joinutech.ddbeslibrary.bean.UserInfo
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.flutter.EventSelectMember
import org.greenrobot.eventbus.EventBus

/**
 * @Description: 好友选择页
 * @Author: hjr
 * @Time: 2020/2/24 11:14
 * @packageName: com.joinutech.addressbook.view
 * @Company: Copyright (C),2017- 2020,JoinuTech
 * GroupChatMessageActivity 群成员调用，注销前必须退群，所以在群内的也是未注册人员
 * GroupCallActivity 群成员选择
 */
@Route(path = RouteOrg.SelectSearchListWithBottomShowActivity2)
class SelectWithSearchListWithBottomShowActivity2 :
    MyUseBindingActivity<ActivityFriendSelectListWithSearchBinding>() {

    override val contentViewResId: Int
        get() = R.layout.activity_friend_select_list_with_search

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityFriendSelectListWithSearchBinding {
        return ActivityFriendSelectListWithSearchBinding.inflate(layoutInflater)
    }

    private var currentTitle = ""

    /**所有列表数据集合*/
    private var mainListData = arrayListOf<FriendSelectBean>()

    /**列表搜索后数据集合*/
    private var searchPersonList = arrayListOf<FriendSelectBean>()

    private var maxSelectNum = Int.MAX_VALUE
    private var isNeedPersonInfo = false

    private var currentSelectNum = 0

    //    /**外部传入人员集合*/
//    private var outPersonList = arrayListOf<FriendSelectBean>()
    private var selectedUserIds = arrayListOf<String>()

    /**不可选人员*/
    private var noSupportUserIds = arrayListOf<String>()

    /**选中人员*/
    private var bottomSelectPersonSet = arrayListOf<String>()
    private lateinit var viewModel: FriendSelectViewModel
    private var searchTextValue = ""

    private var rightTitle: String? = null

    override fun initImmersion() {
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("title"))) {
                currentTitle = intent.getStringExtra("title") ?: ""
                setPageTitle(currentTitle)
            } else {
                setPageTitle("我的好友")
            }
            rightTitle = intent.getStringExtra("rightTitle")
            setRightTitleColor(CommonUtils.getColor(
                mContext!!,
                com.joinutech.ddbeslibrary.R.color.color999999
            ),
                rightTitle ?: "完成", View.OnClickListener { })
            if (intent.getIntExtra("maxSelectNum", 0) != 0) {
                maxSelectNum = intent.getIntExtra("maxSelectNum", 0)
            }
            isNeedPersonInfo = intent.getBooleanExtra("isNeedPersonInfo", false)

            if (!intent.getStringArrayListExtra("selectedUserIds").isNullOrEmpty()) {
                selectedUserIds = intent.getStringArrayListExtra("selectedUserIds")!!
            }
            if (!intent.getStringArrayListExtra("noSupportUserIds").isNullOrEmpty()) {
                noSupportUserIds = intent.getStringArrayListExtra("noSupportUserIds")!!
            }

            val outPersonList = intent
                .getSerializableExtra("outPersonList") as ArrayList<FriendSelectBean>?
            //第一次时需要复选数据给底部的set进行显示
            if (!outPersonList.isNullOrEmpty()) {
                mainListData.clear()
                mainListData.addAll(outPersonList)
                searchPersonList.clear()
                searchPersonList.addAll(outPersonList)
                outPersonList.forEach {
                    if (it.select) {
                        bottomSelectPersonSet.add(it.userId)
                    }
                }
            }
        }
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        binding.rvList.layoutManager = LinearLayoutManager(this)
        binding.llSelectAll.visibility = View.GONE
        binding.mainSideBar.visibility = View.GONE

        viewModel = getModel(FriendSelectViewModel::class.java)

    }

    lateinit var mAdapter: MyAdapter<FriendSelectBean>

    private val tag = "(版本不支持)"
    override fun initLogic() {
        super.initLogic()
        getObservable()
        mAdapter = MyAdapter(this, R.layout.item_friend_list_select_layout, searchPersonList,
            onBindItem = { position: Int, data: FriendSelectBean, view: View ->
                //                    if (multiType) {
                view.findViewById<View>(R.id.tv_index).visibility = View.GONE
                val selector = view.findViewById<ImageView>(R.id.iv_select)
                val avatar = view.findViewById<ImageView>(R.id.iv_avatar)
                val name = view.findViewById<TextView>(R.id.tv_name)
                val line = view.findViewById<View>(R.id.line)
                ImageLoaderUtils.loadImage(mContext!!, avatar, data.avatar ?: "")

                if (position != searchPersonList.lastIndex) {
                    line.visibility = View.VISIBLE
                } else {
                    line.visibility = View.GONE
                }
                when {
                    data.userId in noSupportUserIds || data.name.contains(tag) -> {
                        try {
                            val spannableString = SpannableString(data.name)
                            val colorSpan = ForegroundColorSpan(mContext!!.resources.getColor(com.joinutech.ddbeslibrary.R.color.red))
                            val startIndex = data.name.indexOf(tag)
                            spannableString.setSpan(colorSpan, startIndex, startIndex + tag.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
                            name.text = spannableString
                        } catch (e: Exception) {
                            selector.setImageResource(com.joinutech.ddbeslibrary.R.drawable.shape_oval_white_grey_invalid)
                            name.text = data.name ?: ""
                        }
                    }

                    data.userId in selectedUserIds -> {
                        selector.setImageResource(com.joinutech.ddbeslibrary.R.drawable.icon_normal)
                        name.text = data.name
                    }

                    else -> {
                        selector.setImageResource(com.joinutech.ddbeslibrary.R.drawable.selector_circle_select)
                        selector.isSelected = data.select
                        name.text = data.name
                    }
                }
            },
            onItemClick = { position: Int, data: FriendSelectBean, _: View ->
                if (data.userId !in selectedUserIds && data.userId !in noSupportUserIds) {
                    if (!data.select) {
                        if (currentSelectNum < maxSelectNum) {
                            data.select = true
                            currentSelectNum++
                        } else {
                            toastShort("超过最多可选人数限制")
                        }
                    } else {
                        data.select = false
                        if (currentSelectNum > 0) {
                            currentSelectNum--
                        }
                    }
                    mainListData.find { it.userId == data.userId }?.let {
                        it.select = data.select
                    }
                    if (data.select) {
                        if (!bottomSelectPersonSet.contains(data.userId)) {
                            bottomSelectPersonSet.add(data.userId)
                        }
                    } else {
                        if (bottomSelectPersonSet.contains(data.userId)) {
                            bottomSelectPersonSet.remove(data.userId)
                        }
                    }
                    mAdapter.notifyItemChanged(position)
                    viewModel.chargeRightCompleteColorShow(bottomSelectPersonSet)
                }
            })
        binding.rvList.adapter = mAdapter
        binding.searchBar.search.hint = "搜索"
        binding.searchBar.search.imeOptions = EditorInfo.IME_ACTION_SEARCH
        binding.searchBar.search.inputType = EditorInfo.TYPE_CLASS_TEXT
        binding.searchBar.delete.visibility = View.GONE
        binding.searchBar.search.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (s != null && s.isNotEmpty()) {
                    binding.searchBar.delete.visibility = View.VISIBLE
                } else {
                    binding.searchBar.delete.visibility = View.GONE
                }
            }

        })

        binding.searchBar.search.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH ||
                (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER)
            ) {
                searchTextValue = binding.searchBar.search.text.toString().trim()
                if (StringUtils.isNotBlankAndEmpty(searchTextValue)) {
                    getFriendList()//搜索好友
                }
                return@setOnEditorActionListener true
            }
            false
        }
        binding.llSelectAll.setOnClickListener(this)
        binding.searchBar.delete.setOnClickListener(this)
        binding.searchBar.cancel.setOnClickListener(this)
        getFriendList()//加载好友
    }

    private fun getObservable() {
        viewModel.getFriendListSuccessObservable.observe(this, Observer { searchResult ->
            dismissDialog()
            searchPersonList.clear()
            if (searchResult.isNullOrEmpty()) {
                setShowNoContent(true)
            } else {
                setShowNoContent(false)
                searchPersonList.addAll(searchResult)
                mAdapter.notifyDataSetChanged()
                viewModel.chargeRightCompleteColorShow(bottomSelectPersonSet)
            }
        })
        viewModel.rightCompleteColorShowObservable.observe(this, Observer {
            if (it) {
                setRightTitleColor(
                    CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.main_blue),
                    rightTitle ?: "完成", this
                )
            } else {
                setRightTitleColor(CommonUtils.getColor(
                    mContext!!,
                    com.joinutech.ddbeslibrary.R.color.color999999
                ),
                    rightTitle ?: "完成", View.OnClickListener { })
            }
        })
    }

    private fun getFriendList() {
        getLoadingDialog("", false)
        viewModel.setIMVcPersonList(mainListData, searchTextValue)
    }

    private fun setShowNoContent(b: Boolean) {
        if (b) {
            binding.layoutEmptyLayout.visibility = View.VISIBLE
            binding.clHaveDataLayout.visibility = View.GONE
        } else {
            binding.layoutEmptyLayout.visibility = View.GONE
            binding.clHaveDataLayout.visibility = View.VISIBLE
        }
    }

    override fun onBackPressed() {
        if (currentFocus != null && currentFocus?.windowToken != null)
            (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                .hideSoftInputFromWindow(
                    currentFocus!!
                        .windowToken, InputMethodManager.HIDE_NOT_ALWAYS
                )
        super.onBackPressed()
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            tv_rightTitle -> {
                completeEvent()
            }

            binding.searchBar.delete -> {
                if (StringUtils.isNotBlankAndEmpty(searchTextValue)) {
                    cancelSearchEvent()
                }
            }

            binding.searchBar.cancel -> {
                if (StringUtils.isNotBlankAndEmpty(searchTextValue)) {
                    cancelSearchEvent()
                }
            }
        }
    }

    private fun cancelSearchEvent() {
        binding.searchBar.search.setText("")
        try {
            if (currentFocus != null && currentFocus?.windowToken != null) {
                (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                    .hideSoftInputFromWindow(
                        currentFocus!!.windowToken,
                        InputMethodManager.HIDE_NOT_ALWAYS
                    )
            }
        } catch (e: java.lang.Exception) {

        }

//        if (!searchPersonList.isNullOrEmpty()) {
//            mainListData.forEach {
//                searchPersonList.forEach { innerBean ->
//                    run {
//                        if (innerBean.userId == it.userId) {
//                            it.select = innerBean.select
//                        }
//                    }
//                }
//            }
//        }
        searchPersonList.clear()
        searchPersonList.addAll(mainListData)
        mAdapter.notifyDataSetChanged()
        viewModel.chargeRightCompleteColorShow(bottomSelectPersonSet)
    }

    private fun completeEvent() {
        if (!mainListData.isNullOrEmpty()) {
            val temp = arrayListOf<String>()
            if (!selectedUserIds.isNullOrEmpty()) {
                temp.addAll(selectedUserIds)
            }
            if (!noSupportUserIds.isNullOrEmpty()) {
                temp.addAll(noSupportUserIds)
            }

            val intent = Intent()
            val selectUsers = mainListData.filter {
                it.select && it.userId !in temp
            }.map { UserInfo(it.userId, it.avatar, it.name) }.toList()
            val bundle = Bundle()
            if (isNeedPersonInfo) {
                val set = hashSetOf<UserInfo>()
                set.addAll(selectUsers)
                bundle.putSerializable("selectUserIds", set)
            } else {
                val list = arrayListOf<String>()
                list.addAll(selectUsers.map { it.userId }.toList())
                bundle.putStringArrayList("selectUserIds", list)
            }
            val selectUserNames = selectUsers.map { it.userName }.toList().joinToString("、")
            bundle.putString("selectUserName", selectUserNames)
            intent.putExtras(bundle)
            setResult(Activity.RESULT_OK, intent)
        }
        finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (!mainListData.isNullOrEmpty()) mainListData.clear()
        if (!searchPersonList.isNullOrEmpty()) searchPersonList.clear()
    }


    override fun showToolBar(): Boolean {
        return true
    }


    override fun openArouterReceive(): Boolean {
        return true
    }


}