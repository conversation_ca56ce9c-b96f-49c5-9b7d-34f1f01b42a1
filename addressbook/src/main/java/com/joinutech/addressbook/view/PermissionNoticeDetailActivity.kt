package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.addressbook.R
import com.joinutech.addressbook.databinding.ActivityPermissionNoticeDetailLayoutBinding
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.common.adapter.MyAdapter
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.ORG_PERMISS_TYPE
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.StringUtils

/**
 * <AUTHOR>
 * @date   2019/4/15 16:14
 * @className: PermissionNoticeDetailActivity
 *@Description: 类作用描述
 */
/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName: PermissionNoticeDetailActivity
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/9/14 15:28
 * @Desc: //TODO 重构页面 通过列表形式实现所有权限展示
 */
//权限变更详情页
@Route(path = RouteOrg.permissionNoticeDetailActivity)
class PermissionNoticeDetailActivity : MyUseBindingActivity<ActivityPermissionNoticeDetailLayoutBinding>() {

    override val contentViewResId: Int = R.layout.activity_permission_notice_detail_layout
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityPermissionNoticeDetailLayoutBinding {
        return ActivityPermissionNoticeDetailLayoutBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = true

    @Autowired
    @JvmField
    var permission: String = ""

    @Autowired
    @JvmField
    var managerName: String = ""

    @Autowired
    @JvmField
    var noticeTitle: String = ""

    override fun initImmersion() {
        setPageTitle("管理员分配")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("managerName"))) {
                managerName = intent.getStringExtra("managerName") ?: ""
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("permission"))) {
                permission = intent.getStringExtra("permission") ?: ""
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("context"))) {
                noticeTitle = intent.getStringExtra("context") ?: ""
            }
        }
    }

    override fun initView() {
        showToolBarLine()
        whiteStatusBarBlackFont()
    }

    //添加权限项
    private val permissionList = arrayListOf(
        PermissionData(
            icon = R.drawable.per_org,
            name = "团队管理权限",
            desc = "可对团队资料、成员及架构进行调整，可编辑公告",
            powerValue = "1"
        ),
        PermissionData(
            icon = R.drawable.per_attendace,
            name = "考勤管理权限",
            desc = "可查看全团队考勤数据、调整考勤规则等",
            powerValue = "2"
        ),
        PermissionData(
            icon = R.drawable.icon_task_manager,
            name = "项目创建权限",
            desc = "可在任务功能中创建新项目",
            powerValue = "3"
        ),
        PermissionData(
            icon = com.joinutech.ddbeslibrary.R.drawable.icon_approval_permission,
            name = "审批管理权限",
            desc = "可快速启/停用审批模板，可编辑审批流程",
            powerValue = "4"
        ),
        PermissionData(
            icon = R.drawable.icon_report_permission,
            name = "汇报管理权限",
            desc = "可设定工作汇报规则，查看工作汇报统计",
            powerValue = "5"
        ),
        PermissionData(
            icon = R.drawable.icon_number_report_permission,
            name = "企业数字报告预览权限",
            desc = "可在管理团队模块查看本团队的企业数字报告",
            powerValue = "6"
        ),
        PermissionData(
            icon = R.drawable.icon_total_approval,
            name = "查看全部审批权限",
            desc = "可查看团队全员发起的审批",
            powerValue = "7"
        ),
        PermissionData(
            icon = R.drawable.icon_health_statistics,
            name = "健康上报统计权限",
            desc = "可查看团队全员提交的健康上报，并导出表格",
            powerValue = "8"
        ),
        PermissionData(
            icon = R.drawable.icon_cloud_manage,
            name = "云文档管理权限",
            desc = "可对团队所有部门文件进行查看、删除等操作",
            powerValue = "9"
        ),
//        PermissionData(//访客系统，工作通知点击显示详情
//            icon = R.drawable.icon_visitor_per,
//            name = "访客管理权限",
//            desc = "可对访客申请进行查看、发放通行证等操作",
//            powerValue = "a"
//        )
    )

    @SuppressLint("SetTextI18n")
    override fun initLogic() {
        super.initLogic()
        if (StringUtils.isNotBlankAndEmpty(managerName)) {
            binding.tvManageName.text = managerName
        }

        binding.tvAlSet.text = noticeTitle ?:""

        if (StringUtils.isNotBlankAndEmpty(permission)) {
            if (ORG_PERMISS_TYPE.checkSuperPermission(permission)) {
                 binding.ivPerIcon.visibility = View.VISIBLE
            } else {
                binding.ivPerIcon.visibility = View.GONE
            }
            val permissions = arrayListOf<PermissionData>()

             binding.ivNoPer.visibility = View.GONE
            binding.tvNoPer.visibility = View.GONE

            binding.tvPerTitle.visibility = View.VISIBLE
            if (ORG_PERMISS_TYPE.checkSuperPermission(permission)) {
                permissions.addAll(permissionList)
            } else {
                permissionList.forEach {
                    if (it.powerValue in permission) {
                        permissions.add(it)
                    }
                }
            }
            val adapter = MyAdapter(this,
                R.layout.item_permission_layout,
                permissions,
                { position: Int, data: PermissionData, view: View ->
                    view.findViewById<ImageView>(R.id.iv_icon).setImageResource(data.icon)
                    view.findViewById<TextView>(R.id.tv_name).text = data.name
                    view.findViewById<TextView>(R.id.tv_desc).text = data.desc
                    view.findViewById<View>(R.id.sb_toggle).visibility = View.GONE

                    if (position == permissions.lastIndex) {
                        view.findViewById<View>(R.id.line_bottom).visibility = View.GONE
                    } else {
                        view.findViewById<View>(R.id.line_bottom).visibility = View.VISIBLE
                    }
                },
                { _: Int, _: PermissionData, _: View ->

                })
             binding.rvList.layoutManager = LinearLayoutManager(this)
            binding.rvList.adapter = adapter
            binding.rvList.visibility = View.VISIBLE
        } else {
            binding.ivNoPer.visibility = View.VISIBLE
            binding.tvNoPer.visibility = View.VISIBLE

            binding.ivPerIcon.visibility = View.GONE
            binding.tvPerTitle.visibility = View.GONE
            binding.rvList.visibility = View.GONE
        }
    }

}