package com.joinutech.addressbook.view

import android.app.Activity
import android.view.View
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.addressbook.R
import com.joinutech.addressbook.viewModel.OrgChartViewModel
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.bean.Branch
import com.joinutech.ddbeslibrary.bean.OrgDeptSortBean
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.utils.CommonKeys
import com.joinutech.common.adapter.MyDragAdapter
import com.joinutech.ddbeslibrary.utils.toastShort

/**
 * @Description: TODO 新增部门设置页面 包含 部门排序 和 部门设置 功能入口
 * @Author: zhaoyy
 * @Time: 2021/6/3 14:54
 * @packageName: com.joinutech.addressbook.view
 * @Company: Copyright (C),2019- 2021,JoinuTech
 */
class DepartmentSortActivity : MyUseBaseActivity() {
    override val contentViewResId: Int = com.joinutech.ddbeslibrary.R.layout.common_list_layout

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return false
    }

    override fun initImmersion() {
        whiteStatusBarBlackFont()
    }

    var deptList: ArrayList<Branch>? = null

    override fun initView() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back2, this)
        showToolBarLine()
        deptList = intent.getSerializableExtra("deptList") as ArrayList<Branch>?
        val pageTitle = intent.getStringExtra("pageTitle") ?: "部门排序"
        setPageTitle(pageTitle)

        val emptyView = findViewById<PageEmptyView>(R.id.layout_empty_layout)
        val listView = findViewById<RecyclerView>(R.id.rv_list)
        if (deptList.isNullOrEmpty()) {
            emptyView.visibility = View.VISIBLE
            emptyView.setContent("暂无部门数据")
            listView.visibility = View.GONE
        } else {
            if (deptList!!.size > 1) {
                setRightTitle("保存", this)
            }
            emptyView.visibility = View.GONE
            listView.visibility = View.VISIBLE
            val adapter = MyDragAdapter(this, R.layout.item_dept_sort_layout,
                    dragViewId = R.id.iv_drag,
                    data = deptList!!,
                    onBindItem = { holder, _, data, _ ->
                        holder.setText(R.id.tv_name, data.name).showView(R.id.iv_drag)
                    },
                    onItemClick = { _, _, _ ->
                    })

            adapter.attachDragEvent(listView)
            listView.layoutManager = LinearLayoutManager(this)
            listView.adapter = adapter
        }
    }

    lateinit var viewModel: OrgChartViewModel
    var isSorted = false

    override fun initLogic() {
        super.initLogic()
        viewModel = getModel(OrgChartViewModel::class.java)
        viewModel.deptSortResult.observe(this, Observer { result ->
            hideLoading()
            RequestHelper.onResponse(result,
                    onSuccess = {
                        if (!isSorted) {
                            isSorted = true
                        }
                        toastShort("排序成功")
                        onBackPressed()
                    },
                    onError = { _, msg ->
                        if (msg.isNullOrBlank()) {
                            toastShort("排序失败")
                        } else {
                            toastShort(msg)
                        }
                    },
                    onDefault = { msg -> })
        })
    }

    override fun onNoDoubleClick(v: View) {
        if (v.id == com.joinutech.ddbeslibrary.R.id.iv_left) {
            onBackPressed()
        } else if (v.id == com.joinutech.ddbeslibrary.R.id.tv_toolbar_right) {
            showLog(deptList ?: arrayListOf<Branch>())
            if (!deptList.isNullOrEmpty()) {
                val map = hashMapOf<String, Any>()
                map[CommonKeys.COMPANY_ID] = intent.getStringExtra(CommonKeys.COMPANY_ID) ?: ""
                map["deptIds"] = deptList!!.map { it.deptId }.toMutableList()
                map["parentId"] = intent.getStringExtra("depId") ?: ""

//                viewModel.orgDeptSort(map)
                viewModel.orgDeptSort(
                        OrgDeptSortBean(intent.getStringExtra(CommonKeys.COMPANY_ID) ?: "",
                                deptList!!.map { it.deptId }.toMutableList() as ArrayList<String>,
                                intent.getStringExtra("depId") ?: "")
                )
            }
        }
        super.onNoDoubleClick(v)
    }

    override fun onBackPressed() {
//        super.onBackPressed()
        if (isSorted) {
            setResult(Activity.RESULT_OK)
        }
        finish()
    }

}