package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import androidx.core.content.ContextCompat
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.ddbes.library.im.ImService
import com.ddbes.library.im.imtcp.ConstantEventCodeUtil
import com.ddbes.library.im.imtcp.Logger
import com.ddbes.library.im.imtcp.dbope.MessageDaoOpe
import com.ddbes.library.im.imtcp.dbope.SessionDaoOpe
import com.ddbes.library.im.imtcp.imservice.msghelper.SendMsgHelper
import com.ddbes.library.im.imtcp.imservice.msghelper.SendMsgToDBMsgUtil
import com.ddbes.library.im.imtcp.imservice.sessionhelper.SessionToDBUtil
import com.ddbes.library.im.util.FriendCacheHolder
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.ApplicationDetailConstract
import com.joinutech.addressbook.databinding.ActivityApplicationdetailLayoutBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.common.util.CompanyHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.CompanyUndoListBean
import com.joinutech.ddbeslibrary.bean.UserInfo
import com.joinutech.ddbeslibrary.imbean.AddFriendDetailBean
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.ParseJsonData
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import javax.inject.Inject
import javax.inject.Named

/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName: ApplicationDetailsActivity
 * @Desc: 好友申请详情页面
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/4/15 14:14
 */
//tcp好友申请详情页
@Route(path = RouteOrg.applicationDetailActivity)
class ApplicationDetailsActivity : MyUseBindingActivity<ActivityApplicationdetailLayoutBinding>() {

    override val contentViewResId: Int = R.layout.activity_applicationdetail_layout
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityApplicationdetailLayoutBinding {
        return ActivityApplicationdetailLayoutBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = true

    @Autowired
    @JvmField
    var paramId: String = ""

    var status: Int = 0

    @Inject
    @field:Named(AddressbookUtil.APPLICATIONDETAIL_PRESENTER)
    lateinit var presenter: ApplicationDetailConstract.ApplicationDetailPresenter
    private var companyId: String = ""
    private var personId: String = ""

    /**列表传参则通过序列化获取，其他通过参数接口查询*/
    lateinit var beanCompany: CompanyUndoListBean
    lateinit var bean: AddFriendDetailBean

    companion object {
        const val COMPANY_APPLY = 2
        const val FRIEND_APPLY = 3
        const val FRIEND_APPLY_AGREE = 1
        const val FRIEND_APPLY_IGNORE = 2
    }

    var companyName = ""
    var nameText = ""

    var enterType = COMPANY_APPLY

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        setPageTitle("申请详情")
        if (intent != null) {
            val type = intent.getStringExtra("type") ?: ""
            enterType = if (type == "dealAddFriend") {
                FRIEND_APPLY//好友加入申请
            } else {
                COMPANY_APPLY//团队加入申请
            }
            if (intent.getIntExtra("status", 0) != 0) {
                status = intent.getIntExtra("status", 0)
            }
            if (StringUtils.isEmpty(paramId) && intent.getSerializableExtra("undobean") != null) {
                beanCompany = intent.getSerializableExtra("undobean") as CompanyUndoListBean
                paramId = beanCompany.recordId
            }
        }
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun initLogic() {
        super.initLogic()

        //不是aRouter过来的
        if (StringUtils.isEmpty(paramId)) {
            dealShowInfo(beanCompany)
        } else {
            getLoadingDialog("请求申请详情...", false)
            Logger.i("---执行--开始-获取好友申请详情---", "--开始---")
            //tcp现在这个接口里边根据enterType分别获取申请加入公司或者申请加好友的申请详情
            presenter.getApplicationDetailData(bindToLifecycle(), accessToken, paramId, enterType,
                    onSuccess = {
                        dismissDialog()
                        if (enterType == 2) {
                            Logger.i("---执行---获取加入组织申请详情-成功--", "---" + GsonUtil.toJson(it))
                            //团队加入申请
                            beanCompany = ParseJsonData.parseJsonAny<CompanyUndoListBean>(it)
                        } else if (enterType == 3) {
                            Logger.i("---执行---获取好友申请详情-成功--", "---" + GsonUtil.toJson(it))
                            //好友加入申请
                            bean = ParseJsonData
                                    .parseJsonAny<AddFriendDetailBean>(it)
                            val companyBean = CompanyUndoListBean(bean.createTime.toString(),
                                    bean.headimg, bean.name, "recordId", bean.birthday, "",
                                    bean.status, bean.gender.toInt(), "", bean.message,
                                    bean.userId, userName!!, userStatus = bean.userStatus)
                            beanCompany = companyBean
                        }
                        dealShowInfo(beanCompany)
                    },
                    onError = {
                        dismissDialog()
                        ToastUtil.show(mContext!!, it)
                    })
        }
        binding.agree.setOnClickListener(this)
        binding.ignore.setOnClickListener(this)
    }

    @SuppressLint("SetTextI18n")
    private fun dealStatus(isFirstEnter: Boolean = false, handlerName: String = "") {
        when (status) {
            // 0待处理 1已同意 2已忽略 3已过期 4团队已解散
            //需要处理
            0 -> {
                binding.clDealLayout.visibility = View.VISIBLE
                binding.clStatusLayout.visibility = View.INVISIBLE
                binding.outdated.visibility = View.INVISIBLE
            }
            //同意
//            1 -> {
//                cl_deal_layout.visibility = View.INVISIBLE
//                cl_status_layout.visibility = View.VISIBLE
//                outdated.visibility = View.INVISIBLE
//                statusText.text = "已同意"
//                val dealTime = CommonUtils.stampToDate(System.currentTimeMillis())
//                //好友申请不需要处理人
////                if (StringUtils.isNotBlankAndEmpty(type)) {
//                if (enterType == FRIEND_APPLY) {
//                    time.text = dealTime
//                } else {
//                    if (!isFirstEnter) {
//                        time.text = "$dealTime 处理人：$userName"
//                    } else {
//                        time.text = "$dealTime 处理人：$handlerName"
//                    }
//                }
//            }
            //忽略
            1, 2 -> {
                binding.clDealLayout.visibility = View.INVISIBLE
                binding.clStatusLayout.visibility = View.VISIBLE
                binding.outdated.visibility = View.INVISIBLE
                binding.statusText.text = if (status == 1) "已同意" else "已忽略"
                val dealTime = CommonUtils.stampToDate(System.currentTimeMillis())
                //好友申请不需要处理人
                if (enterType == FRIEND_APPLY) {
                    binding.time.text = dealTime
                } else {
                    if (!isFirstEnter) {
                        binding.time.text = "$dealTime 处理人：$userName"
                    } else {
                        binding.time.text = "$dealTime 处理人：$handlerName"
                    }
                }
            }
            //过期
            3 -> {
                ToastUtil.showCustomToast(null, mContext!!, true,
                        "申请超过7天没有处理，已过期")
                binding.clDealLayout.visibility = View.INVISIBLE
                binding.clStatusLayout.visibility = View.INVISIBLE
                binding.outdated.visibility = View.VISIBLE
                binding.outdated.text = "申请已过期"
            }
            //手动添加的公司已解散
            4 -> {
                binding.clDealLayout.visibility = View.INVISIBLE
                binding.clStatusLayout.visibility = View.INVISIBLE
                binding.outdated.visibility = View.VISIBLE
                binding.outdated.text = "该团队已解散，无法处理此请求"
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun dealShowInfo(beanCompany: CompanyUndoListBean?) {
        if (beanCompany != null) {
            if (StringUtils.isNotBlankAndEmpty(beanCompany.headimg)) {
                ImageLoaderUtils.loadImage(mContext!!, binding.ivUserIcon, beanCompany.headimg)//显示图片
            }
            try {
                //由出生日期获得年龄***
                if (StringUtils.isNotBlankAndEmpty(beanCompany.birthday)) {
                    val ageText = CommonUtils.getAge(CommonUtils.parse(beanCompany.birthday))
                    binding.age.text = "$ageText 岁"
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            when (beanCompany.gender) {
                1 -> {
                    binding.gender.visibility = View.VISIBLE
                    binding.gender.setImageResource(com.joinutech.ddbeslibrary.R.drawable.icon_personinfo_man_small)
                }
                2 -> {
                    binding.gender.visibility = View.VISIBLE
                    binding.gender.setImageResource(com.joinutech.ddbeslibrary.R.drawable.icon_personinfo_girl_small)
                }
                else -> {
                    binding.gender.visibility = View.VISIBLE
                    binding.gender.setImageResource(com.joinutech.ddbeslibrary.R.mipmap.icon_sex_none)
                }
            }
            if (enterType == COMPANY_APPLY) {
                if (StringUtils.isNotBlankAndEmpty(beanCompany.companyName)) {
                    companyName = beanCompany.companyName
                }
                binding.company.text = "申请加入：${beanCompany.companyName}"
                companyId = beanCompany.companyId
            } else {
                binding.company.text = "申请添加好友"
            }
            binding.name.text = beanCompany.name
            if (StringUtils.isNotBlankAndEmpty(beanCompany.name)) {
                nameText = beanCompany.name
            }
            personId = beanCompany.userId
            binding.reasonText.text = beanCompany.content
            if (status == 0) {
                status = beanCompany.status
            }
            dealStatus(true, beanCompany.handlerName)
        }

        //投诉相关
        binding.rlComplainLayout.tvComplainText.text = "对方曾被他人投诉，请核实身份再通过验证，谨防诈骗"
//        when (2) {//测试安全提示（）
        when (beanCompany?.userStatus) {
            0 -> {//正常情况
                binding.rlComplainLayout.rlComplainLayout.visibility = View.GONE
            }
            1 -> {//被投诉三次及以上，没有被封禁
                binding.rlComplainLayout.rlComplainLayout.visibility = View.VISIBLE
            }
            2 -> {//对方账号被封禁了
                binding.rlComplainLayout.rlComplainLayout.visibility = View.VISIBLE
                //隐藏同意和忽略的按钮，变成提示语
                binding.agree.text = "对方账号已被封禁"
                binding.agree.isClickable = false
                binding.agree.setBackgroundColor(ContextCompat.getColor(this, com.joinutech.ddbeslibrary.R.color.gray_dfe7ed))
                binding.agree.setTextColor(ContextCompat.getColor(this, com.joinutech.ddbeslibrary.R.color.red))
                binding.ignore.visibility = View.GONE
            }
        }
    }

    //点击事件
    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.ignore -> {//点击忽略
                if (enterType == COMPANY_APPLY) {//加入团队
                    getLoadingDialog("忽略请求...", false)

                    if (beanCompany == null) {
                        dismissDialog()
                        return
                    }
                    val map = hashMapOf<String, String>()
                    map["companyId"] = companyId
                    map["userId"] = personId
                    map["recordId"] = beanCompany.recordId

                    presenter.rejectUserJoin(bindToLifecycle(), accessToken, map,
                            {
                                dismissDialog()
                                status = 2
                                dealStatus()
                                // 拒绝加入团队 刷新团队请求列表 刷新所有待处理列表 刷新首页待处理红点
                                EventBusUtils.sendEvent(EventBusEvent(
                                        EventBusAction.REFRESH_APPLIY_LIST, "org"))
                            },
                            {
                                dismissDialog()
                                ToastUtil.show(mContext!!, it)
                            })
                } else {//tcp加好友
                    getLoadingDialog("忽略请求.", false)
//                    val map = hashMapOf<String, String>()
//                    map["applyId"] = paramId
//                    map["opt"] = 2.toString() // tcp这两个字段修改为handleID和opt
                    presenter.rejectAddFriendJoin(bindToLifecycle(), accessToken, paramId, FRIEND_APPLY_IGNORE,
                            onSuccess = {
                                Logger.i("---执行---拒绝好友申请----", "--成功-----")
                                Logger.i("---执行----拒绝好友请求返回数据---", "--it--" + GsonUtil.toJson(it))
                                dismissDialog()
                                status = 2
                                dealStatus()
                                // 拒绝好友成功
                                // 首页待处理红点，好友请求列表，所有待处理，
                                EventBusUtils.sendEvent(EventBusEvent(
                                        EventBusAction.REFRESH_APPLIY_LIST, "friend"))
                            },
                            onError = {
                                Logger.i("---执行---拒绝好友申请----", "--失败-----")
                                dismissDialog()
                                ToastUtil.show(mContext!!, it)
                            })
                }
            }
            binding.agree -> {//点击同意
                if (enterType == COMPANY_APPLY) {//加入团队
                    getLoadingDialog("同意加入...", false)

                    if (beanCompany == null) {
                        dismissDialog()
                        return
                    }
                    val map = hashMapOf<String, String>()
                    map["companyId"] = companyId
                    map["recordId"] = beanCompany.recordId
                    map["userId"] = personId
                    Logger.i("---执行--同意团队加入申请-", "--companyId--" + companyId)
                    Logger.i("---执行--同意团队加入申请-", "--beanCompany.recordId--" + beanCompany.recordId)
                    Logger.i("---执行--同意团队加入申请-", "--personId--" + personId)

                    presenter.orgAgreeUserJoin(bindToLifecycle(), accessToken, map,
                            onSuccess = {
                                Logger.i("---执行--同意团队加入申请-", "--成功--")
                                Logger.i("---执行----同意团队加入申请---", "--返回json数据--" + GsonUtil.toJson(it))
                                dismissDialog()
                                status = 1
                                dealStatus()
                                CompanyHolder.setOrgInfo(// 同意加入团队后，更新团队信息
                                        UserInfo(beanCompany.companyId, beanCompany.headimg, beanCompany.companyName))
                                // 同意加入团队 显示团队成员的位置需要刷新数据以显示新加入成员
                                // 团队组织架构刷新 团队成员列表刷新
                                EventBusUtils.sendEvent(EventBusEvent(
                                        EventBusAction.REFRESH_APPLIY_LIST, "org"))
                                //分配部门
                                jumpToAssignDep()
                                finish()
                            },
                            onError = {
                                Logger.i("---执行--同意团队加入申请-", "--失败--")
                                Logger.i("---执行----同意团队加入申请---", "--返回json数据--" + GsonUtil.toJson(it))
                                dismissDialog()
                                ToastUtil.show(mContext!!, it)
                                if (it.contains("该团队已解散")) {
                                    // 团队 已解散 刷新团队列表??
                                    EventBusUtils.sendEvent(EventBusEvent(
                                            EventBusAction.REFRESH_APPLIY_LIST, "org"))
                                    status = 4
                                    dealStatus()
                                }
                            })
                } else {//加好友
                    getLoadingDialog("同意添加好友.", false)
//                    val map = hashMapOf<String, String>()
//                    map["handleId"] = paramId
//                    map["opt"] = 1.toString() // tcp这两个字段修改为handleID和opt
                    presenter.agreeAddFriend(bindToLifecycle(), accessToken, paramId, FRIEND_APPLY_AGREE,
                            onSuccess = {
                                Logger.i("---执行---同意好友申请----", "--成功-----")
                                Logger.i("---执行----同意好友请求返回数据---", "--it--" + GsonUtil.toJson(it))
                                dismissDialog()
                                status = 1
                                dealStatus()
                                FriendCacheHolder.saveFriend(//同意好友后，更新好友缓存信息
                                        UserInfo(beanCompany.userId, beanCompany.headimg, beanCompany.name))

                                // 同意添加好友成功 同意好友请求后需要刷新：
                                // 好友列表版本数据更新
                                EventBusUtils.sendEvent(EventBusEvent(
                                        ConstantEventCodeUtil.TCP_REFRESH_FRIEND_LIST, ""))
                                // 首页待处理红点，好友请求列表，所有待处理，
                                EventBusUtils.sendEvent(EventBusEvent(
                                        EventBusAction.REFRESH_APPLIY_LIST, "friend"))

                                ////给对方发送一条添加成功的文本消息
                                val sendMsgContent = "我们已经是好友了,快来和我一起聊天吧!"
                                val uuid = SendMsgHelper.getUUID()
                                val sendMsg = SendMsgHelper.getC2CMsgRequestMsg(bean.imId, sendMsgContent, uuid)
                                val isSucceed = ImService.sendMsg(sendMsg)
                                //发送的消息存入数据库
                                val dbMessage = SendMsgToDBMsgUtil.getDbMsgFromText(userId!!, bean.userId, bean.imId, sendMsgContent, uuid)
                                Logger.i("--执行-发送文本-", "--isSucceed-" + isSucceed)
                                if (!isSucceed) {
                                    dbMessage.isSuccess = 0
                                }
                                MessageDaoOpe.instance.insertMessage(this, dbMessage)
                                //发送数据之后保存会话
                                val sessionDB = SessionToDBUtil.getSessionDB(userId, bean.name, bean.headimg, 0, dbMessage)
                                SessionDaoOpe.instance.insertSession(this, sessionDB)
                                //发送会话变化的通知
                                EventBusUtils.sendEvent(EventBusEvent(ConstantEventCodeUtil.TCP_SESSION_CHANGE))
                            },
                            onError = {
                                Logger.i("---执行---同意好友申请----", "--失败-----")
                                dismissDialog()
                                ToastUtil.show(mContext!!, it)
                            })
                }
            }
        }
    }

    private fun jumpToAssignDep() {
        val intent = Intent(mContext, OrgDepartmentActivity::class.java)
        intent.putExtra("type", "undoDeal")
        //公司名称
        intent.putExtra("depName", companyName)
        val userIds = arrayListOf(personId)
        intent.putExtra("userIds", userIds)
        intent.putExtra("depId", "0")
        intent.putExtra("companyId", companyId)
        //待处理员工姓名
        intent.putExtra("name", nameText)
        startActivity(intent)
    }
}