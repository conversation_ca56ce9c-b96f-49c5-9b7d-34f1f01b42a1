package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.ddbes.library.im.imtcp.dbbean.FriendBean
import com.ddbes.library.im.util.FriendCacheHolder
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.FriendSelectBottomSelectPersonHeadAdapter
import com.joinutech.addressbook.adapter.FriendSelectSearchAdapter
import com.joinutech.addressbook.databinding.ActivityFriendSelectListWithSearchBinding
import com.joinutech.addressbook.viewModel.FriendSelectViewModel
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.FriendSelectBean
import com.joinutech.ddbeslibrary.bean.OutMsgPersonBean
import com.joinutech.ddbeslibrary.bean.UserInfo
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.ItemClickListener
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.toastShort
//import kotlinx.android.synthetic.main.activity_friend_select_list_with_search.*
//import kotlinx.android.synthetic.main.search_include_layout.*

/**
 * @Description: 好友选择页
 * @Author: hjr
 * @Time: 2020/2/24 11:14
 * @packageName: com.joinutech.addressbook.view
 * @Company: Copyright (C),2017- 2020,JoinuTech
 * 选择好友 分享汇报
 * 选择好友 添加群成员
 */
@Route(path = RouteOrg.FriendSelectWithSearchList)
class FriendSelectWithSearchListActivity2 : MyUseBindingActivity<ActivityFriendSelectListWithSearchBinding>() {

    override val contentViewResId: Int = R.layout.activity_friend_select_list_with_search
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityFriendSelectListWithSearchBinding {
        return ActivityFriendSelectListWithSearchBinding.inflate(layoutInflater)
    }


    private var currentTitle = ""

    //是否是多选的类型
    private var isMultiType = false

    /**
     * 进入要选择的类型
     * 0 创建群聊
     * 1转换群组创建者
     * 2群组加新人但需要返回的是选择人的信息
     * 3 选择带底部选择头像的多选类型
     */
    private var enterType = 0

    private var noSelectUserIds = arrayListOf<String>()
    private lateinit var adapter: FriendSelectSearchAdapter
    private var maxSelectNum = Int.MAX_VALUE
    private var isNeedPersonInfo = false
    private var isUseToSendImMsg = false

    private var oldSelectIndex = 0
    private var bottomSelectPersonSet = arrayListOf<String>()
    private lateinit var viewModel: FriendSelectViewModel
    private var searchTextValue = ""
    private lateinit var bottomSelectHeadAdapter: FriendSelectBottomSelectPersonHeadAdapter

    override fun initImmersion() {
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("title"))) {
                currentTitle = intent.getStringExtra("title") ?: ""
                setPageTitle(currentTitle)
            } else {
                setPageTitle("我的好友")
            }
            isMultiType = intent.getBooleanExtra("isMultiType", false)
            if (isMultiType) {
                setRightTitleColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color999999),
                        "完成", View.OnClickListener { })
            }
            enterType = intent.getIntExtra("type", 0)
            if (!intent.getStringArrayListExtra("noSelectUserIds").isNullOrEmpty()) {
                noSelectUserIds = intent.getStringArrayListExtra("noSelectUserIds") ?: arrayListOf()
            }
            if (intent.getIntExtra("maxSelectNum", 0) != 0) {
                maxSelectNum = intent.getIntExtra("maxSelectNum", 0)
            }
            isNeedPersonInfo = intent.getBooleanExtra("isNeedPersonInfo", false)
            isUseToSendImMsg = intent.getBooleanExtra("isUseToSendImMsg", false)
        }
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        
        binding.rvList.layoutManager = LinearLayoutManager(this)
        
        binding.rvPersonList.layoutManager = LinearLayoutManager(
                mContext, LinearLayoutManager.HORIZONTAL, false)
        viewModel = getModel(FriendSelectViewModel::class.java)
        if (isMultiType && enterType != 3) {
            
            binding.llSelectAll.visibility = View.VISIBLE
        } else {
            binding.llSelectAll.visibility = View.GONE
        }
    }

    override fun initLogic() {
        super.initLogic()
        getObservable()
        loadFriendList()//加载好友信息
    }

    @SuppressLint("SetTextI18n")
    private fun bottomShowOrHide() {
        if (bottomSelectPersonSet.isNullOrEmpty()) {
            
            binding.twoBtnsContainRl.visibility = View.GONE
        } else {
            binding.twoBtnsContainRl.visibility = View.VISIBLE
        }
        
        binding.tvBottomTitle.text = "已选择${bottomSelectPersonSet.size}人,最多选择${maxSelectNum}人"
        bottomSelectHeadAdapter.setSourceList(bottomSelectPersonSet)
    }

    /**
     * 加载所有数据
     * 选择后更新UI
     * 搜索框输入后，search后显示搜索到的内容，本地搜索
     * 取消输入后，显示所有数据以及标识所有的选择项
     * */
    private val sourceFriend = arrayListOf<FriendBean>()
    private val allFriends = arrayListOf<FriendSelectBean>()
    private val selectedUserIds = arrayListOf<String>()
    private val searchList = arrayListOf<FriendSelectBean>()

    private fun loadFriendList() {
        showLoading()
        FriendCacheHolder.loadFriend(this, bindToLifecycle(),
                onResult = {
                    hideLoading()
                    allFriends.clear()
                    sourceFriend.clear()
                    if (it.isNotEmpty()) {
                        setShowNoContent(false)
                        sourceFriend.addAll(it)
                        val temp = it.map { friend ->
                            val name = if (friend.relation == 1) {
                                if (!friend.remark.isNullOrBlank()) friend.remark else friend.name
                            } else {
                                friend.name
                            }
                            FriendSelectBean(userId = friend.userId, name = name, avatar = friend.avatar,
                                    initial = friend.initial, logout = friend.logout)
                        }.toList()
                        allFriends.addAll(temp)
                        searchList.addAll(temp)
                    } else {
                        setShowNoContent(true)
                    }
                    showFriendList()
                },
                onError = {
                    hideLoading()
                    setShowNoContent(false)
                })
    }

    private fun showFriendList() {
        adapter = FriendSelectSearchAdapter(mContext!!, searchList, isMultiType)
        bottomSelectHeadAdapter = FriendSelectBottomSelectPersonHeadAdapter(mContext!!,
                bottomSelectPersonSet)
        binding.rvPersonList.adapter = bottomSelectHeadAdapter
        adapter.setClickListener(object : ItemClickListener {
            override fun onItemClick(position: Int) {
                onFriendItemClick(position)
            }
        })
        val list = searchList.filter { it.logout == 1 }
        if (list.isNotEmpty()) {
            list.forEach { noSelectUserIds.add(it.userId) }
        }
        val noSelect = noSelectUserIds.distinct()
        noSelectUserIds.clear()
        noSelectUserIds.addAll(noSelect)
        adapter.setNoSelectUsers(noSelectUserIds)
        binding.rvList.adapter = adapter
        // 侧边设置相关
        
        binding.mainSideBar.setOnSelectIndexItemListener { letter ->
            // TODO: 2020/7/23 8:59 优化后需要验证
            if (allFriends.isNotEmpty()) {
                val first = allFriends.find { it.initial == letter }
                if (first != null) {
                    first.select = true
                    allFriends[oldSelectIndex].select = false
                    oldSelectIndex = allFriends.indexOf(first)
                    adapter.notifyDataSetChanged()
                    showLog("切换联系人索引后 更新 联系人列表----")
                    (binding.rvList.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(oldSelectIndex, 0)
                }
            }
        }
        
        binding.searchBar.search.hint = "搜索好友"
        binding.searchBar.search.imeOptions = EditorInfo.IME_ACTION_SEARCH
        binding.searchBar.search.inputType = EditorInfo.TYPE_CLASS_TEXT
        
        binding.searchBar.delete.visibility = View.GONE
        binding.searchBar.search.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (s != null && s.isNotEmpty()) {
                    binding.searchBar.delete.visibility = View.VISIBLE
                } else {
                    binding.searchBar.delete.visibility = View.GONE
                }
            }

        })
        binding.searchBar.search.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                searchTextValue = binding.searchBar.search.text.toString().trim()
                if (StringUtils.isNotBlankAndEmpty(searchTextValue)) {
                    searchFriend()// 搜索好友
                }
                return@setOnEditorActionListener true
            }
            false
        }
        binding.llSelectAll.setOnClickListener(this)
        binding.searchBar.delete.setOnClickListener(this)
        binding.searchBar.cancel.setOnClickListener(this)
    }

    private fun searchFriend() {
        showLoading()
        FriendCacheHolder.searchFriend(this, bindToLifecycle(), searchTextValue,
                onResult = {
                    hideLoading()
                    searchList.clear()
                    if (it.isNotEmpty()) {
                        setShowNoContent(false)
                        val temp = it.map { friend ->
                            val name = if (friend.relation == 1) {
                                if (!friend.remark.isNullOrBlank()) friend.remark else friend.name
                            } else {
                                friend.name
                            }
//                            val selectBean = FriendSelectBean()
//                            selectBean.name = name
//                            selectBean.userId = friend.userId
//                            selectBean.avatar = friend.avatar
//                            selectBean.initial = friend.initial
//                            selectBean.relation = friend.relation
//                            selectBean.logout = friend.logout
//                            selectBean.select = friend.userId in selectedUserIds
//                            selectBean
                            FriendSelectBean(userId = friend.userId, name = name, avatar = friend.avatar,
                                    initial = friend.initial,
                                    logout = friend.logout, select = friend.userId in selectedUserIds)
                        }.toList()
                        searchList.addAll(temp)
                    } else {
                        setShowNoContent(true)
                    }
                    adapter.notifyDataSetChanged()
                },
                onError = {
                    hideLoading()
                    setShowNoContent(false)
                })
    }

    private fun onFriendItemClick(position: Int) {
        if (isMultiType) {
            val data = searchList[position]
            if (data.logout == 0) {
                if (!data.select) {
                    if (selectedUserIds.size < maxSelectNum) {
                        data.select = true
                        if (!selectedUserIds.contains(data.userId)) {
                            selectedUserIds.add(data.userId)
                        }

                        binding.llSelectAll.isSelected = isSelectedAll()
                    } else {
                        toastShort("超过最多可选人数限制")
                    }
                } else {
                    binding.llSelectAll.isSelected = false
                    data.select = false
                    if (selectedUserIds.contains(data.userId)) {
                        selectedUserIds.remove(data.userId)
                    }
                }
                allFriends.find { it.userId == data.userId }?.let {
                    it.select = data.select
                }
                if (enterType == 3) {
                    if (data.select) {
                        if (!bottomSelectPersonSet.contains(data.avatar)) {
                            bottomSelectPersonSet.add(data.avatar)
                        }
                    } else {
                        if (bottomSelectPersonSet.contains(data.avatar)) {
                            bottomSelectPersonSet.remove(data.avatar)
                        }
                    }
                    bottomShowOrHide()
                }
                adapter.notifyItemChanged(position)
                viewModel.chargeRightCompleteColorShow(selectedUserIds)
            } else {
                toastShort("该好友已注销账号")
            }
        } else {
            val data = adapter.mData[position]
            if (data.logout == 0) {
                val singleSelectUserId = data.userId
                val intent = Intent()
                intent.putExtra("userId", singleSelectUserId)
                setResult(Activity.RESULT_OK, intent)
                finish()
            } else {
                toastShort("该好友已注销账号")
            }
        }
    }

    private fun isSelectedAll(): Boolean {
        return searchList.find { it.userId !in noSelectUserIds && it.userId !in selectedUserIds } == null
    }

    private fun getObservable() {
        viewModel.rightCompleteColorShowObservable.observe(this, Observer {
            if (it) {
                setRightTitleColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.main_blue),
                        "完成", this)
            } else {
                setRightTitleColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color999999),
                        "完成", View.OnClickListener { })
            }
        })
    }

    private fun setShowNoContent(b: Boolean) {
        if (b) {
            
            binding.layoutEmptyLayout.visibility = View.VISIBLE
             binding.clHaveDataLayout.visibility = View.GONE
        } else {
            binding.layoutEmptyLayout.visibility = View.GONE
            binding.clHaveDataLayout.visibility = View.VISIBLE
        }
    }

    override fun onBackPressed() {
        if (currentFocus != null && currentFocus?.windowToken != null) {
            (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                    .hideSoftInputFromWindow(currentFocus!!
                            .windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        }
        super.onBackPressed()
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            tv_rightTitle -> {
                completeEvent2()
            }
            binding.searchBar.delete -> {
                if (StringUtils.isNotBlankAndEmpty(searchTextValue))
                    cancelSearchEvent()//取消输入搜索内容
            }
            binding.searchBar.cancel -> {
                if (StringUtils.isNotBlankAndEmpty(searchTextValue)) {
                    cancelSearchEvent()//取消按钮点击
                }
            }
            binding.llSelectAll -> {
                if (isCanBeSelect()) {
                    binding.llSelectAll.isSelected = !binding.llSelectAll.isSelected
                    dealAllSelectList(searchList, binding.llSelectAll.isSelected)
                    adapter.notifyDataSetChanged()
                    viewModel.chargeRightCompleteColorShow(selectedUserIds)
                }
            }
        }
    }

    private fun isCanBeSelect(): Boolean {
        return searchList.find { it.userId !in noSelectUserIds } != null
    }

    private fun dealAllSelectList(list: ArrayList<FriendSelectBean>, allSelect: Boolean) {
        list.forEach {
            if (it.userId !in noSelectUserIds) {
                it.select = allSelect
                if (allSelect && !selectedUserIds.contains(it.userId)) {
                    selectedUserIds.add(it.userId)
                } else if (!allSelect && selectedUserIds.contains(it.userId)) {
                    selectedUserIds.remove(it.userId)
                }
            }
        }
        allFriends.forEach {
            it.select = it.userId in selectedUserIds
        }
    }

    private fun cancelSearchEvent() {
        binding.searchBar.search.setText("")
        try {
            if (currentFocus != null && currentFocus?.windowToken != null)
                (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                        .hideSoftInputFromWindow(currentFocus!!
                                .windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        } catch (e: java.lang.Exception) {
        }
        searchList.clear()
        searchList.addAll(allFriends)
        adapter.notifyDataSetChanged()
        if (isMultiType && enterType != 3) {
            binding.llSelectAll.isSelected = isSelectedAll()
        }
        viewModel.chargeRightCompleteColorShow(selectedUserIds)
    }

    //点击完成
    private fun completeEvent2() {
        if (!allFriends.isNullOrEmpty()) {
            if (enterType == 0 && selectedUserIds.size > 20) {
                ToastUtil.show(mContext!!, "创建群组人数最多为20人")
                return
            }
            val selectPersonUserIdSet = hashSetOf<String>()
            selectPersonUserIdSet.addAll(selectedUserIds)
            val intent = Intent()
            if (isNeedPersonInfo) {
                if (!isUseToSendImMsg) {
                    val selectPersonUserSet = hashSetOf<UserInfo>()
                    val selectUsers = allFriends.filter {
                        it.userId !in noSelectUserIds && it.select
                    }.map {
                        UserInfo(it.userId, it.avatar, it.name)
                    }.toMutableList()
                    selectPersonUserSet.addAll(selectUsers)
                    intent.putExtra("selectUserIds", selectPersonUserSet)
                } else {
                    val selectUsers = sourceFriend.filter {
                        it.userId !in noSelectUserIds && it.userId in selectedUserIds
                    }.map {
                        OutMsgPersonBean(it.userId, it.relation == 0)
                    }.toMutableList() as ArrayList
                    intent.putExtra("selectUserIds", selectUsers)
                }
            } else {
                intent.putExtra("selectUserIds", selectPersonUserIdSet)
            }
            val selectNames = allFriends.filter {
                it.userId !in noSelectUserIds && it.select
            }.joinToString("、") { it.name }
            intent.putExtra("selectUserName", selectNames)
            setResult(Activity.RESULT_OK, intent)
        }
        finish()
    }

    override fun onDestroy() {
        if (!allFriends.isNullOrEmpty()) allFriends.clear()
        if (!searchList.isNullOrEmpty()) searchList.clear()
        super.onDestroy()
    }

    override fun showToolBar(): Boolean = true
    override fun openArouterReceive(): Boolean = true
}