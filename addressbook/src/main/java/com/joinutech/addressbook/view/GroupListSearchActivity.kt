package com.joinutech.addressbook.view

import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import androidx.lifecycle.Observer

import androidx.recyclerview.widget.LinearLayoutManager
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.GroupSearchListAdapter
import com.joinutech.addressbook.adapter.bean.GroupSearchBean
import com.joinutech.addressbook.databinding.ActivityGroupSearchBinding
import com.joinutech.addressbook.viewModel.GroupListViewModel
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
//import kotlinx.android.synthetic.main.activity_group_search.*
//import kotlinx.android.synthetic.main.search_include_layout.*

/**
 * @Description: 群组列表搜索页
 * @Author: hjr
 * @Time: 2020/2/25 15:05
 * @packageName: com.joinutech.addressbook.view
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
class GroupListSearchActivity : MyUseBindingActivity<ActivityGroupSearchBinding>() {

    override val contentViewResId: Int
        get() = R.layout.activity_group_search

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityGroupSearchBinding {
        return ActivityGroupSearchBinding.inflate(layoutInflater)
    }

    private var searchTextValue = ""
    private var searchGroupList = arrayListOf<GroupSearchBean>()
    private lateinit var adapter: GroupSearchListAdapter
    private lateinit var viewModel: GroupListViewModel

    override fun initImmersion() {
        setPageTitle("搜索群组")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        binding.searchBar.search.hint = "搜索群组"
        binding.searchBar.search.imeOptions = EditorInfo.IME_ACTION_SEARCH
        binding.searchBar.search.inputType = EditorInfo.TYPE_CLASS_TEXT
        binding.searchBar.delete.visibility = View.GONE
        
        binding.searchBar.cancel.visibility = View.GONE
        
        binding.searchGroupRv.layoutManager = LinearLayoutManager(this)
        viewModel = getModel(GroupListViewModel::class.java)
    }

        override fun initLogic() {
        super.initLogic()
        getObservable()
        binding.searchBar.search.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {

            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (s != null && s.isNotEmpty()) {
                    binding.searchBar.delete.visibility = View.VISIBLE
                    binding.searchBar.cancel.visibility = View.VISIBLE
                } else {
                    binding.searchBar.delete.visibility = View.GONE
                    binding.searchBar.cancel.visibility = View.GONE
                    cancelSearchEvent(false)
                }
            }

        })

        binding.searchBar.search.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH ||
                    (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER)) {
                searchTextValue = binding.searchBar.search.text.toString().trim()
                if (StringUtils.isNotBlankAndEmpty(searchTextValue))
                    getSearchGroupData()
                return@setOnEditorActionListener true
            }
            false
        }
        adapter = GroupSearchListAdapter(mContext!!, searchGroupList)
        binding.searchGroupRv.adapter = adapter
        binding.searchBar.delete.setOnClickListener(this)
        binding.searchBar.cancel.setOnClickListener(this)
    }

    private fun getObservable() {
        viewModel.getAllGroupListSuccessObservable.observe(this, Observer {
            dismissDialog()
            adapter.setKeyWord(searchTextValue)
            if (!it.isNullOrEmpty()) {
                searchGroupList = it as ArrayList<GroupSearchBean>
            }
            adapter.setSourceList(searchGroupList)
            isShowNoData()
        })
        viewModel.getAllGroupListErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }


    private fun getSearchGroupData() {
        getLoadingDialog("", false)
        viewModel.getAllGroupList(this, searchTextValue)
    }

    private fun isShowNoData() {
        if (!searchGroupList.isNullOrEmpty()) {
            binding.layoutEmptyLayout.visibility = View.GONE
            binding.searchGroupRv.visibility = View.VISIBLE
        } else {
            binding.layoutEmptyLayout.visibility = View.VISIBLE
            binding.searchGroupRv.visibility = View.GONE
        }
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.searchBar.delete -> {
                if (StringUtils.isNotBlankAndEmpty(searchTextValue))
                    cancelSearchEvent(true)
            }
            binding.searchBar.cancel -> {
                if (StringUtils.isNotBlankAndEmpty(searchTextValue))
                    cancelSearchEvent(true)
            }
        }
    }

    private fun cancelSearchEvent(isClearText: Boolean) {
        if (isClearText)
            binding.searchBar.search.setText("")
        searchTextValue = ""
        try {
            if (currentFocus != null && currentFocus?.windowToken != null)
                (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                        .hideSoftInputFromWindow(currentFocus!!
                                .windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        } catch (e: java.lang.Exception) {

        }
        searchGroupList.clear()
        isShowNoData()
    }

    override fun onBackPressed() {
        if (currentFocus != null && currentFocus?.windowToken != null)
            (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                    .hideSoftInputFromWindow(currentFocus!!
                            .windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        super.onBackPressed()
    }

    
    override fun showToolBar(): Boolean {
        return true
    }



    override fun openArouterReceive(): Boolean {
        return false
    }
}