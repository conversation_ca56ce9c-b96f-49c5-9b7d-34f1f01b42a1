package com.joinutech.addressbook.view

import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.addressbook.OrgService
import com.joinutech.addressbook.OrgServiceImpl
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.MineOrganizationAdapter
import com.joinutech.addressbook.databinding.ActivityMineorganizationBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.org.GlobalCompanyHolder
import com.joinutech.ddbeslibrary.request.exception.ErrorType
import com.joinutech.ddbeslibrary.utils.BottomDialogUtil
import com.joinutech.ddbeslibrary.utils.ConsKeys
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.RouteProvider
import com.joinutech.ddbeslibrary.utils.ToastUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 *<AUTHOR>
 *@date 2018/11/20
 * 通讯录-》我的其他团队
 */
class MineOrganizationsActivity(
        override val contentViewResId: Int = R.layout.activity_mineorganization
) : MyUseBindingActivity<ActivityMineorganizationBinding>() {
    /**我创建的团队*/
    private var createList: ArrayList<WorkStationBean> = arrayListOf()

    /**我加入的团队*/
    private var addList: ArrayList<WorkStationBean> = arrayListOf()

    /**我加入的协作团队*/
    private var cooperationCompanyList: ArrayList<WorkStationBean> = arrayListOf()

//    @Inject
//    @field:Named(AddressbookUtil.ADDR_PRESENTER)
//    lateinit var presenter: AddressbookConstract.AddressbookPresenter

    private var noticeCompanyId = "0"
    private lateinit var adapter: MineOrganizationAdapter
    private var dataList = arrayListOf<WorkStationBean>()

    override fun initImmersion() {
        setPageTitle("我的团队")
        setRightImage(R.drawable.organization_add, View.OnClickListener {
            dealAddAction()
        })
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        whiteStatusBarBlackFont()
    }

    private fun dealAddAction() {
        val view = View.inflate(mContext, com.joinutech.ddbeslibrary.R.layout.dialog_personicon_bottom_layout, null)
        val dialog = BottomDialogUtil.showBottomDialog(mContext!!, view, Gravity.BOTTOM)
        val mTakePicture = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.takePicture)
        val mSelectPicture = view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.selectPicture)
        val mDismiss = view.findViewById<TextView>(R.id.dismiss)
        mTakePicture.text = "创建团队"
        mSelectPicture.text = "加入团队"
        mTakePicture.setOnClickListener {
            dialog.dismiss()
            val intent1 = Intent(this, CreateOrgActivity::class.java)
            startActivity(intent1)
        }
        mSelectPicture.setOnClickListener {
            dialog.dismiss()
            val intent1 = Intent(this, SearchResultActivity::class.java)
            intent1.putExtra("type", "searchOrg")
            startActivity(intent1)
        }
        mDismiss.setOnClickListener {
            dialog.dismiss()
        }
    }

    override fun initView() {
        DaggerAddressbookComponent.builder().build().inject(this)
        
        binding.recyclerMineo.layoutManager = LinearLayoutManager(mContext!!,
                RecyclerView.VERTICAL, false)
        adapter = MineOrganizationAdapter(mContext!!, dataList)
        adapter.setItemClickListener(object : MineOrganizationAdapter.ItemClickListener {

            override fun visitClick(position: Int) {
                val intent1 = Intent(mContext, SelectVisitMethodActivity::class.java)
                intent1.putExtra("companyName", dataList[position].name)
                intent1.putExtra("companyId", dataList[position].companyId)
                intent1.putExtra("companyLogo", dataList[position].logo)
                startActivity(intent1)
            }

            //我的团队页，点击团队架构
            override fun orgMemberClick(position: Int, orgPermission: Boolean) {
                val intent1 = Intent(this@MineOrganizationsActivity,
                        OrganizationChartActivity::class.java)
                intent1.putExtra("companyId", dataList[position].companyId)
                intent1.putExtra("depName", dataList[position].name)
                intent1.putExtra("orgPermission", orgPermission)
                startActivity(intent1)
            }

            override fun orgInfoClick(position: Int, orgPermission: Boolean) {
                val intent1 = Intent(this@MineOrganizationsActivity,
                        OrganizationInfoActivity::class.java)
                intent1.putExtra("companyBean", dataList[position])
                intent1.putExtra("orgPermission", orgPermission)
                intent1.putExtra("isCreator", dataList[position].deptId == "0")
                startActivity(intent1)
            }

            override fun orgExternalClick(position: Int, orgPermission: Boolean) {
                val intent1 = Intent(mContext, OrgExternalContactActivity::class.java)
                intent1.putExtra("companyBean", dataList[position])
                intent1.putExtra("orgPermission", orgPermission)
                startActivity(intent1)
            }

            override fun orgMoreClick(position: Int) {
                val intent1 = Intent(this@MineOrganizationsActivity,
                        OrganizationMoreActivity::class.java)
                intent1.putExtra("companyBean", dataList[position])
                startActivity(intent1)
            }

            override fun orgCooperClick(position: Int) {
                val bundle = Bundle()
                bundle.putString(ConsKeys.COMPANY_ID, dataList[position].companyId)
                bundle.putSerializable(ConsKeys.KEY_INTENT_DATA, dataList[position])
                (ARouter.getInstance().build(RouteProvider.APR_FUN_PROVIDER).navigation() as RouteServiceProvider)
                        .openPage("cooperation_approve", bundle)

//                ARouter.getInstance()
//                        .build(RouteApr.companyCooperationCompanyDetailActivity)
//                        .withString("companyId", dataList[position].companyId)
//                        .navigation()
            }
        })
        binding.recyclerMineo.adapter = adapter
    }

    override fun initLogic() {
        super.initLogic()
        GlobalCompanyHolder.companyUpdateResult.observe(this, Observer {
            showLog("我的团队列表 -- 接收团队信息更新")
            showOrgList()
        })
//        showOrgList()
        loadData()
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityMineorganizationBinding {
        return ActivityMineorganizationBinding.inflate(layoutInflater)
    }

    private var service: OrgService = OrgServiceImpl()

    private fun loadData() {
        // 获取所有团队信息
        service.service("getAllCompany", Bundle()) {
            if (it == ErrorType.SUCCESS.toString()) {
//                showOrgList()
            } else {
                ToastUtil.show(mContext!!, "加载团队信息失败")
            }
        }

//        presenter.getCompanys(bindToLifecycle(), accessToken!!, userId!!, {
//            CompanyHolder.saveMainOrgId(it.mainCompanyId ?: "")
//            CompanyHolder.saveAllOrg(it.companies)
//            CompanyHolder.saveCooperationOrg(it.externalCompanies)
//            showOrgList()
//        }, {
//            ToastUtil.show(mContext!!, it)
//        })
    }

    private fun showOrgList() {
        dataList.clear()
        val allOrg = CompanyHolder.getAllNormalOrg()
        val cooperOrg = CompanyHolder.getCooperationOrg()
        if (CompanyHolder.getTotalCompanies().isEmpty()) {
            binding.recyclerMineo.visibility = View.GONE

            binding.layoutEmptyLayout.visibility = View.VISIBLE
        } else {
            if (!allOrg.isNullOrEmpty()) {
                binding.recyclerMineo.visibility = View.VISIBLE
                binding.layoutEmptyLayout.visibility = View.GONE
                val mainCompanyId = UserHolder.getCurrentUser()?.companyId ?: ""
                createList.clear()
                addList.clear()
                //处理数据,分为我创建的和我加入的
                allOrg.filter { it.companyId != mainCompanyId }
                        .forEach {
                            it.isOuter = false
                            if (it.deptId == "0") {
                                createList.add(it)
                            } else {
                                addList.add(it)
                            }
                        }
                if (!createList.isNullOrEmpty()) {
                    dataList.add(WorkStationBean(companyId = "0", name = "我创建的团队"))
                    dataList.addAll(createList)
                }
                if (!addList.isNullOrEmpty()) {
                    dataList.add(WorkStationBean(companyId = "0", name = "我加入的团队"))
                    dataList.addAll(addList)
                }
            }
            if (!cooperOrg.isNullOrEmpty()) {
                cooperationCompanyList.clear()
                cooperOrg.forEach { bean ->
                    run {
                        //                        val bean1 = ParseJsonData.parseJsonAny<CompanyBean>(bean)
                        bean.isOuter = true
                        cooperationCompanyList.add(bean)
                    }
                }
                if (!cooperationCompanyList.isNullOrEmpty()) {
                    dataList.add(WorkStationBean(companyId = "0", name = "我的合作团队"))
                    dataList.addAll(cooperationCompanyList)
                }
            }
        }
        adapter.setSourceList(dataList)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun refreshCompanyInfo(event: EventBusEvent<Any>) {
        if (event.code == ConsKeys.ORG_INFO_UPDATE /*|| event.code == EventBusAction.CURRENT_COMPANY_UPDATE*/) {
            showLog("团队列表页面 -- 事件触发 - 团队列表")
            showOrgList()
        }
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return false
    }
}