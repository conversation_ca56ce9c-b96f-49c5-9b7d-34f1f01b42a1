package com.joinutech.addressbook.view

import android.content.Intent
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.addressbook.R
import com.joinutech.addressbook.databinding.ActivityCreatBinding
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.StringUtils.Companion.setEtFilter
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.toastShort

/**
 *<AUTHOR>
 *@date 2018/11/20
 */
@Route(path = RouteOrg.createOrgActivity)
class CreateOrgActivity : MyUseBindingActivity<ActivityCreatBinding>() {

    override val contentViewResId: Int = R.layout.activity_creat
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityCreatBinding {
        return ActivityCreatBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true
    override fun openArouterReceive(): Boolean = true

    override fun initImmersion() {
        setPageTitle("创建团队")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
    }

    override fun initLogic() {
        super.initLogic()
        setEtFilter(binding.creatEtName)
        binding.creatEtName.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (s != null && s.toString().isNotEmpty()) {
                    if (s.length > 30) {
                        ToastUtil.showCustomToast(null, mContext!!,
                                true, "请输入1-30个中文字符")
                        s.delete(30, s.length)
                    }
                    binding.cancelInput.visibility = View.VISIBLE
                } else {
                    binding.cancelInput.visibility = View.GONE
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        })
        binding.cancelInput.setOnClickListener(this)
        binding.creatNext.setOnClickListener {
//            val text = creat_et_name.editableText
            val text = binding.creatEtName.text?.toString()
            if (text != null && StringUtils.isNotBlankAndEmpty(text)) {
                // TODO: 2020/6/2 13:55 检查输入团队名规则
                if (StringUtils.isValidStr(text)) {
//                    toastShort("跳转页面")
                    val intent1 = Intent(mContext!!, CreateOrganization2Activity::class.java)
                    intent1.putExtra("name", text)
                    startActivityForResult(intent1, 0x22)
                } else {
                    toastShort("团队名称包含特殊字符")
                }
            } else {
                ToastUtil.show(mContext!!, "请输入团队名称")
            }
        }
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.cancelInput -> {
                binding.creatEtName.text.clear()
                binding.cancelInput.visibility = View.GONE
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == 0x22) {
            setResult(0x22)
            finish()
        } else {
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

}