package com.joinutech.addressbook.view.tcpimpages


import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.addressbook.R
import com.joinutech.addressbook.view.tcpimpages.madapter.BlackListAdapter
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.ddbes.library.im.imtcp.Logger
import com.ddbes.library.im.imtcp.imservice.dialogutil.ShowDialogUtil
import com.ddbes.library.im.netutil.ImNetUtil
import com.joinutech.addressbook.databinding.ActivityBlackListBinding
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.imbean.BlackPersonBean
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.widget.wavesidebar.PinnedHeaderDecoration


@Route(path = RouteOrg.blackListActivity)
class BlackListActivity : MyUseBindingActivity<ActivityBlackListBinding>() {
    override val contentViewResId: Int = R.layout.activity_black_list
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityBlackListBinding {
        return ActivityBlackListBinding.inflate(layoutInflater)
    }

    private lateinit var emptyPage: PageEmptyView
    private lateinit var blackBeanList: ArrayList<BlackPersonBean>
    private lateinit var mAdapter: BlackListAdapter

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        setPageTitle("通讯录黑名单")
    }


    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
//recyclerview简单使用
        binding.blackRecycler.layoutManager = LinearLayoutManager(this)
        val decoration = PinnedHeaderDecoration()
        decoration.registerTypePinnedHeader(1) { _, _ -> true }
        binding.blackRecycler.addItemDecoration(decoration)


        emptyPage = findViewById(R.id.layout_black_empty)
    }

    override fun initLogic() {
        super.initLogic()
        blackBeanList = arrayListOf()
        mAdapter = BlackListAdapter(mContext!!, blackBeanList)

        // RecyclerView设置相关
        binding.blackRecycler.adapter = mAdapter
        emptyPage.show()
        getBlackListData()
        initListener()
    }

    //设置监听
    private fun initListener() {
        mAdapter.setItemClickListener(object : BlackListAdapter.OnClickBlackListener {
            override fun onClick(view: View, position: Int) {
                val blackPersonBean = blackBeanList[position]
                ShowDialogUtil.showDialog(this@BlackListActivity, bindToLifecycle(), accessToken,
                        blackPersonBean.userId, 2,
                        onSuccess = {
                            Logger.i("---执行----移出黑名单成功----", "----返回数据----" + it)
                            ToastUtil.show(this@BlackListActivity, "成功将此用户从黑名单移出")
                            getBlackListData()
                        }, onFailer = {
                    ToastUtil.show(this@BlackListActivity, "移出黑名单失败")
                })

            }
        })

    }

    private fun getBlackListData() {
        ImNetUtil.getBlackList(bindToLifecycle(), accessToken, onSuccess = {
            blackBeanList.clear()
            blackBeanList.addAll(it)
            //刷新
            if (blackBeanList.size > 0) {
                emptyPage.visibility = View.GONE
            } else {
                emptyPage.show()
            }
            mAdapter.notifyDataSetChanged()
        }, onFailer = {
            ToastUtil.show(this, "黑名单列表获取失败")
        })

    }


    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return true
    }

}
