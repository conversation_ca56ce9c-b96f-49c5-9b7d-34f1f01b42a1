package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import androidx.core.content.ContextCompat
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.VerifyApplicationConstact
import com.joinutech.addressbook.databinding.ActivityVerifyapplicationBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import javax.inject.Inject
import javax.inject.Named

/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName: VerifyApplicationActivity
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/9/16 8:49
 * @Desc: //TODO 团队邀请详情-》发送团队加入申请页面
 */
class VerifyApplicationActivity : MyUseBindingActivity<ActivityVerifyapplicationBinding>() {

    override val contentViewResId: Int = R.layout.activity_verifyapplication
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityVerifyapplicationBinding {
        return ActivityVerifyapplicationBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = false

    @Inject
    @field:Named(AddressbookUtil.VERAPPLICATION_PRESENTER)
    lateinit var presenter: VerifyApplicationConstact.VerifyApplicationPresenter
    var companyId: String = ""
    private var type: String = ""

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        setPageTitle("验证申请")
        setRightTitleColor(ContextCompat.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color096AFF),
                "发送", this)
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                //type不为空时为加友的userId
                companyId = intent.getStringExtra("companyId")!!
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("type"))) {
                type = intent.getStringExtra("type")!!
            }
        }
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        DaggerAddressbookComponent.builder().build().inject(this)
        if (StringUtils.isEmpty(type)) {
            //加入团队
            binding.reason.hint = "您好，我是$userName，申请加入您的团队"
        } else {
            //加好友
            binding.reason.hint = "您好，我是$userName，可以加你为好友吗?"
            binding.text.text = "您需要发送验证申请，等待对方通过"
        }
    }

    override fun initLogic() {
        super.initLogic()
        binding.reason.addTextChangedListener(object : TextWatcher {
            @SuppressLint("SetTextI18n")
            override fun afterTextChanged(s: Editable?) {
                val text = s.toString()
                if (StringUtils.isEmpty(type)) {
                    if (text.length in 1..30) {
                        binding.num.visibility = View.VISIBLE
                        binding.num.text = "${text.length}/30"
                    }
                } else {
                    if (text.length in 1..50) {
                        binding.num.visibility = View.VISIBLE
                        binding.num.text = "${text.length}/50"
                    }
                }
                when {
                    text.isEmpty() -> binding.num.visibility = View.GONE
                    else -> {
                        val selectionEnd = binding.reason.selectionEnd
                        if (StringUtils.isEmpty(type)) {
                            if (text.length > 30) s?.delete(30, selectionEnd)
                        } else {
                            if (text.length > 50) s?.delete(50, selectionEnd)
                        }
                    }
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        })
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            com.joinutech.ddbeslibrary.R.id.tv_toolbar_right -> {
                //点击发送
                val content = if (StringUtils.isNotBlankAndEmpty(binding.reason.text.toString())) {
                    binding.reason.text.toString()
                } else {
                    val hint = binding.reason.hint.toString()
                    hint
                }
                getLoadingDialog("发送验证申请", false)
                if (StringUtils.isNotBlankAndEmpty(type)) {//tcp加好友，搜索“tcp真正添加好友”
                    presenter.verfyFriendApplication(bindToLifecycle(),
                            accessToken!!, companyId, content,
                            onSuccess = {
                                ToastUtil.show(mContext!!, "已向该用户发送验证申请")
                                dismissDialog()
                                finish()
                            },
                            onError = {
                                dismissDialog()
                                ToastUtil.show(mContext!!, it)
                            })
                } else {//加入团队
                    presenter.verifyOrgApplication(bindToLifecycle(),
                            accessToken!!, companyId, content,
                            onSuccess = {
                                ToastUtil.show(mContext!!, "已向该团队发送加入申请")
                                dismissDialog()
                                finish()
                            },
                            onError = {
                                dismissDialog()
                                ToastUtil.show(mContext!!, it)
                            })
                }
            }
        }
    }
}