package com.joinutech.addressbook.view

import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.ddbes.library.im.imtcp.ConstantEventCodeUtil
import com.ddbes.library.im.imtcp.Logger
import com.ddbes.library.im.imtcp.dbbean.FriendBean
import com.ddbes.library.im.imtcp.dbbean.Message
import com.ddbes.library.im.util.FriendCacheHolder
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.ContactsAdapter
import com.joinutech.addressbook.constract.FriendListConstract
import com.joinutech.addressbook.databinding.ActivityFriendlistBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.ContactModel
import com.joinutech.ddbeslibrary.bean.OrgImportPeopleBean
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.widget.wavesidebar.PinnedHeaderDecoration
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import javax.inject.Inject
import javax.inject.Named

/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName: FriendListActivity
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/9/15 9:55
 * @Desc: //TODO 好友列表
 */
class FriendListActivity : MyUseBindingActivity<ActivityFriendlistBinding>() {

    override val contentViewResId: Int = R.layout.activity_friendlist
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityFriendlistBinding {
        return ActivityFriendlistBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = false

    private lateinit var mShowModels: List<ContactModel>
    private lateinit var mAdapter: ContactsAdapter
    private var oldPosition = 0

    @Inject
    @field:Named(AddressbookUtil.FRIENDLIST_PRESENTER)
    lateinit var presenter: FriendListConstract.FriendListPresenter

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back2)
        setPageTitle("我的好友")
    }

    override fun initView() {
        showToolBarLine()
        whiteStatusBarBlackFont()
        DaggerAddressbookComponent.builder().build().inject(this)
        binding.mainRecycler.layoutManager = LinearLayoutManager(this)
        val decoration = PinnedHeaderDecoration()
        decoration.registerTypePinnedHeader(1) { _, _ -> true }
        binding.mainRecycler.addItemDecoration(decoration)
    }

    override fun initLogic() {
        super.initLogic()
        initViewShow()
        loadFriendList()
        binding.search.setOnClickListener(this)
    }

    private fun loadFriendList() {
        showLoading()
        FriendCacheHolder.loadFriend(this, bindToLifecycle(),
                onResult = {
                    hideLoading()
                    Logger.i("---执行---从数据库取出好友列表---", "--json数据--" + GsonUtil.toJson(it))
                    dealResult(it)
                },
                onError = {
                    hideLoading()
                    dealResult(arrayListOf())
                })
    }

    //接收到im通知
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun receiveEvent(event: EventBusEvent<Message>) {
        if (event.code != null) {
            when (event.code) {
                ConstantEventCodeUtil.TCP_REFRESH_FRIEND_LIST_FINISH -> {
                    hideLoading()
                    loadFriendList()
                }
            }
        }

    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.search -> {
                val intent = Intent(mContext!!, SearchResultActivity::class.java)
                intent.putExtra("type", "searchFriend")
                startActivity(intent)
            }
        }
    }

    private val temp = arrayListOf<ContactModel>()
    private lateinit var pageType: String
    private fun initViewShow() {
        pageType = intent.getStringExtra("type") ?: ""
        mShowModels = ArrayList()
        mAdapter = ContactsAdapter(mShowModels, mContext!!)
        mAdapter.setType(pageType)
        mAdapter.setOnClickListener { contact ->
            if (pageType == "shareFile") {
                contact.check = !contact.check
                if (contact.check) {
                    temp.add(contact)
                } else {
                    temp.find { it.userId == contact.userId }?.let {
                        temp.remove(it)
                    }
                }
//                mAdapter.notifyDataSetChanged()
            }
        }
        // RecyclerView设置相关
        binding.mainRecycler.adapter = mAdapter
        // 侧边设置相关
        binding.mainSideBar.setmTextHighColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color1E87F0))
        binding.mainSideBar.setOnSelectIndexItemListener { letter ->
            // TODO: 2020/7/23 8:59 优化后需要验证
            if (mShowModels.isNotEmpty()) {
                val first = mShowModels.find { it.index == letter }
                if (first != null) {
                    first.select = true
                    mShowModels[oldPosition].select = false
                    oldPosition = mShowModels.indexOf(first)
                    mAdapter.notifyDataSetChanged()
                    showLog("切换联系人索引后 更新 联系人列表----")
                    (binding.mainRecycler.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(oldPosition, 0)
                }
            }
        }

        if (pageType == "shareFile") {
            setRightTitle("确定", this)
        }
    }

    override fun onClick(v: View) {
        if (v.id == com.joinutech.ddbeslibrary.R.id.tv_toolbar_right) {
            if ("shareFile" == pageType) {
                val result = temp.map { contact ->
                    val people = OrgImportPeopleBean()
                    people.userId = contact.userId
                    people.headimg = contact.logo
                    people.name = contact.name
                    people
                }.toMutableList()
                EventBusUtils.sendEvent(EventBusEvent(intent.getStringExtra("pageFlag") ?: "",
                        result))
                finish()
            }
        }
    }

    private fun dealResult(it: List<FriendBean>?) {

        if (!it.isNullOrEmpty()) {
            binding.layoutEmptyLayout.visibility = View.GONE
            binding.friendListLayout.visibility = View.VISIBLE
            mShowModels = presenter.dealFriendList(it)
            mShowModels[oldPosition].select = true
            mAdapter.setLogoutUserIds(FriendCacheHolder.getMyLogoutFriendIds())
            mAdapter.setDataList(mShowModels)
        } else {
            binding.layoutEmptyLayout.visibility = View.VISIBLE
            binding.friendListLayout.visibility = View.GONE
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        (mShowModels as ArrayList).clear()
    }

}