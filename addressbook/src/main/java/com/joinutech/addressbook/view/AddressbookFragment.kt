package com.joinutech.addressbook.view

import android.content.Intent
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.addressbook.AddressProvider
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.AddressCooperationCompanyListAdapter
import com.joinutech.addressbook.constract.AddressbookConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.common.helper.OnFragmentResumeListener
import com.joinutech.common.util.CommonListPop
import com.joinutech.common.util.CompanyHolder
import com.joinutech.ddbeslibrary.base.BaseFragment
import com.joinutech.ddbeslibrary.bean.UndoBean
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.org.GlobalCompanyHolder
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.CircleImageView
import com.marktoo.lib.cachedweb.LogUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import javax.inject.Inject
import javax.inject.Named

/**
 *<AUTHOR>
 *@date 2018/11/6
 */
@Suppress("DEPRECATION")
class AddressbookFragment : BaseFragment() {

    override val layoutRes: Int = R.layout.fragment_addressbook
    var listener: OnFragmentResumeListener? = null

    @Inject
    @field:Named(AddressbookUtil.ADDR_PRESENTER)
    lateinit var presenter: AddressbookConstract.AddressbookPresenter

    private lateinit var layoutHasCooper: ConstraintLayout
    private lateinit var layoutHas: ConstraintLayout
    private lateinit var layoutNon: ConstraintLayout
    private lateinit var tvName: TextView
    private lateinit var ivLogo: CircleImageView
    private lateinit var layout_watchMember: ConstraintLayout
    private lateinit var layout_watchInfo: ConstraintLayout
    private lateinit var layout_moreinfo: ConstraintLayout
    private lateinit var visitLayout: LinearLayout
    private lateinit var orgSize: TextView
    private lateinit var externalContactLayout: ConstraintLayout
    private lateinit var cooperationRv: RecyclerView
    private lateinit var otherLayout: View
    private lateinit var tvCreat: TextView
    private lateinit var tvAdd: TextView
    private lateinit var search: TextView
    private lateinit var friendLayout: RelativeLayout

    private var mainOrg: WorkStationBean? = null
    private var cooperationCompanyList = arrayListOf<WorkStationBean>()
    private lateinit var adapter: AddressCooperationCompanyListAdapter

    private var orgPermission: Boolean = false
    private var hasInvitePermission: Boolean = false
    private lateinit var clMyOrg: View
    private lateinit var cooperationTitle: TextView
    private lateinit var groupLayout: ConstraintLayout

    private lateinit var titleBar: View
    private lateinit var titleBarContainer: View

    private lateinit var toolbarTitle: TextView

    private lateinit var ivToolbarRight: ImageView
    private lateinit var ivToolbarRight2: ImageView
    private lateinit var ivDotRight2: View

    override fun initView(rootView: View) {
        DaggerAddressbookComponent.builder().build().inject(this)
        fun initTitleBar(rootView: View) {
            titleBar = rootView.findViewById(com.joinutech.ddbeslibrary.R.id.title_bar_root)
            titleBar.setBackgroundColor(mActivity.resources.getColor(com.joinutech.ddbeslibrary.R.color.white))

            titleBarContainer = titleBar.findViewById(com.joinutech.ddbeslibrary.R.id.title_bar_container)

            toolbarTitle = titleBar.findViewById(com.joinutech.ddbeslibrary.R.id.toolbar_title)
            toolbarTitle.text = "通讯录"

            ivToolbarRight = titleBar.findViewById(R.id.iv_right)
            ivToolbarRight.visibility = View.VISIBLE
            ivToolbarRight.setImageResource(com.joinutech.ddbeslibrary.R.drawable.add_home_icon)
            ivToolbarRight.setOnClickListener(this)

            ivToolbarRight2 = titleBar.findViewById(com.joinutech.ddbeslibrary.R.id.iv_right2)
            ivToolbarRight2.visibility = View.VISIBLE
            ivToolbarRight2.setImageResource(R.drawable.ic_addr_book_notify)
            ivToolbarRight2.setOnClickListener(this)

            ivDotRight2 = titleBar.findViewById(com.joinutech.ddbeslibrary.R.id.iv_dot_right2)

            setStatusBarView(titleBarContainer)
            fun checkDot(count: Int) {
                LogUtil.showLog("通讯录收到待处理数量 $count ,showDot = ${count > 0}")
                if (count > 0) {
                    showRightRedDotNew()
                } else {
                    hideRightRedDotNew()
                }
            }

            checkDot(AddressProvider.getUnProcessCount().value ?: 0)
            AddressProvider.getUnProcessCount().observe(this, Observer {
                checkDot(it)
            })
        }

        fun initNoOrg() {
            layoutNon = rootView.findViewById(R.id.layout_non)
            tvCreat = rootView.findViewById(R.id.tv_creat)
            tvAdd = rootView.findViewById(R.id.tv_add)
        }

        fun initOrg() {
            layoutHas = rootView.findViewById(R.id.layout_has)

            tvName = rootView.findViewById(R.id.tv_name)
            rootView.findViewById<View>(R.id.tv_desc).visibility = View.VISIBLE
            ivLogo = rootView.findViewById(R.id.iv_logo)

            visitLayout = rootView.findViewById(R.id.ll_visit_layout)

            layout_watchInfo = rootView.findViewById(R.id.cl_watch_info)
            layout_watchMember = rootView.findViewById(R.id.cl_member_watch)
            externalContactLayout = rootView.findViewById(R.id.cl_external_layout)
            layout_moreinfo = rootView.findViewById(R.id.cl_more_layout)

            otherLayout = rootView.findViewById(R.id.other_layout)
        }

        fun initCooperation() {
            layoutHasCooper = rootView.findViewById(R.id.cl_cooperation_layout)
            cooperationTitle = rootView.findViewById(R.id.tv_cooperation_title)
            cooperationRv = rootView.findViewById(R.id.rv_cooperation_list)
            cooperationRv.layoutManager = LinearLayoutManager(mActivity)
            cooperationRv.isNestedScrollingEnabled = false
        }

        initTitleBar(rootView)
        search = rootView.findViewById(R.id.search)
        friendLayout = rootView.findViewById(R.id.rl_friend_layout)
        groupLayout = rootView.findViewById(R.id.cl_group_layout)

        clMyOrg = rootView.findViewById(R.id.rl_main_org_layout)
        orgSize = rootView.findViewById(R.id.tv_org_count)
        initNoOrg()
        initOrg()
        initCooperation()
        showEmptyView()//默认显示空页面
    }

    override fun initLogic() {
        super.initLogic()
        search.setOnClickListener(this)
        friendLayout.setOnClickListener(this)
        otherLayout.setOnClickListener(this)
        tvCreat.setOnClickListener(this)
        tvAdd.setOnClickListener(this)
        visitLayout.setOnClickListener(this)
        layout_watchMember.setOnClickListener(this)
        layout_watchInfo.setOnClickListener(this)
        externalContactLayout.setOnClickListener(this)
        groupLayout.setOnClickListener(this)
        adapter = AddressCooperationCompanyListAdapter(mActivity, cooperationCompanyList)
        cooperationRv.adapter = adapter

        GlobalCompanyHolder.companyUpdateResult.observe(this, Observer {
            showLog("通讯录 -- 接收团队信息更新")
            when (it.type) {
                1 -> {// 显示我的团队信息
                    showLog("显示我的团队信息")
//                    mainOrg = CompanyHolder.getMainOrg()
//                    if (mainOrg != null) {
//                        showCompanyInfo(mainOrg!!)
//                    }
                    showOrgInfo()
                }
                2 -> {// 显示我的协作团队信息
                    showLog("显示我的协作团队信息")
                    if (CompanyHolder.getCurrentOrg() == null) {
                        showCooperationInfo(CompanyHolder.getCooperationOrg())// 更新协作团队信息
                    }
                }
                else -> {// 当前无任何团队信息
                    showLog("当前无任何团队信息")
                    mainOrg = null
                    cooperationCompanyList.clear()
                    showEmptyView()//没有任何团队数据时显示无团队页面
                }
            }
        })
        showOrgInfo()
    }

    private fun showOrgInfo() {
        mainOrg = CompanyHolder.getMainOrg()
        cooperationCompanyList = CompanyHolder.getCooperationOrg()
        if (mainOrg != null) {
            // 显示当前主要团队
            showCompanyInfo(mainOrg!!)
        } else if (!cooperationCompanyList.isNullOrEmpty()) {
            // 显示当前协作团队
            showCooperationInfo(cooperationCompanyList)
        } else {
            showEmptyView()//没有任何团队数据时显示无团队页面
        }
    }

    private fun showMainOrg() {
        if (mainOrg != null) {
            if (StringUtils.isNotBlankAndEmpty(mainOrg!!.name)) { //请求接口时间内取本地缓存
                if (StringUtils.isNotBlankAndEmpty(mainOrg!!.logo)) {
//                    Glide.with(mActivity).load(companyLogo).into(ivLogo)
//                    if (mainOrg!!.logo.startsWith("http")) {
                    ImageLoaderUtils.loadImage(mActivity, ivLogo, mainOrg!!.logo)
//                    } else {
//                        ImageLoaderUtils.loadImage(mActivity, ivLogo, FileStorage.WEB_IMAGE_BASE_URL + mainOrg!!.logo)
//                    }
                }
                tvName.text = mainOrg!!.name
                layoutHas.visibility = View.VISIBLE
                layoutNon.visibility = View.GONE
            }
        } else {
            layoutHas.visibility = View.GONE
            layoutNon.visibility = View.VISIBLE
        }
    }

    private var receiveEvent = false

    override fun onResume() {
        showLog("++++++ fragment address book onResume()")
        super.onResume()
        if (!receiveEvent) {
            listener?.onResumed(4)
        }
//        updateStatusBar(true)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun refreshUndoRedDot(event: EventBusEvent<String>) {
        if (!receiveEvent) {
            receiveEvent = true
        }
        // // TODO: 2021/7/9 16:16 好友申请 和 团队申请 同意或忽略后
        if (event.code == EventBusAction.REFRESH_APPLIY_LIST) {
            showLog("首页刷新待处理团队和好友数据以更新提醒红点---")
            /**首页刷新待处理团队和好友数据以更新提醒红点*/
            AddressbookService.getUndoList(1, 1, 1)
                    .compose(bindToLifecycle())
                    .compose(ErrorTransformer.getInstance<UndoBean>())
                    .subscribe(object : BaseSubscriber<UndoBean>() {
                        override fun onError(ex: ApiException) {
                            hideRightRedDotNew()
                        }

                        override fun onComplete() {

                        }

                        override fun onNext(it: UndoBean?) {
                            showLog("首页判断待处理数量...")
                            if (it != null) {
                                if (it.count > 0) {
                                    AddressProvider.changeUnProcessCount(1)
//                                    EventBusUtils.sendEvent(EventBusEvent(
//                                            EventBusAction.REFRESH_UNDO_COUNT, 1))
                                } else if (it.companies.isNotEmpty()) {
                                    var hasWait = false
                                    for (company in it.companies) {
                                        if (company.count > 0) {
                                            hasWait = true
                                            break
                                        }
                                    }
                                    if(!it.external.isNullOrEmpty()){
                                        hasWait = true
                                    }
                                    AddressProvider.changeUnProcessCount(if (hasWait) 1 else 0)
//                                    EventBusUtils.sendEvent(EventBusEvent(
//                                            EventBusAction.REFRESH_UNDO_COUNT, if (hasWait) 1 else 0))
                                }else if(!it.external.isNullOrEmpty()){
                                    AddressProvider.changeUnProcessCount(1)
                                }
                            } else {
                                AddressProvider.changeUnProcessCount(0)
//                                EventBusUtils.sendEvent(EventBusEvent(
//                                        EventBusAction.REFRESH_UNDO_COUNT, 0))
                            }
                        }
                    })
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun refreshCompanyInfo(event: EventBusEvent<Int>) {
        if (!receiveEvent) {
            receiveEvent = true
        }
        if (event.code == ConsKeys.ORG_MAIN_CHANGE || event.code == ConsKeys.ORG_INFO_UPDATE) {
            mainOrg = CompanyHolder.getMainOrg()
            showMainOrg()
        }
    }

    private fun showEmptyView() {
        layoutNon.visibility = View.VISIBLE
        orgSize.visibility = View.GONE
        layoutHas.visibility = View.GONE
        layoutHasCooper.visibility = View.GONE
    }

    /**团队信息*/
    private fun showCompanyInfo(data: WorkStationBean) {
        orgSize.visibility = View.VISIBLE
        layoutHas.visibility = View.VISIBLE
        layoutHasCooper.visibility = View.GONE
        layoutNon.visibility = View.GONE

        val count = CompanyHolder.getAllNormalOrg().size
        if (count != 0) {
            orgSize.visibility = View.VISIBLE
            orgSize.text = "我的团队（共${count}个）"
        } else {
            orgSize.visibility = View.GONE
        }
        if (StringUtils.isNotBlankAndEmpty(data.companyId) && data.companyId != "0") {
            // 创建者  包含超级管理员权限
            orgPermission = data.deptId == "0" || ORG_PERMISS_TYPE.checkSuperPermission(data.power)

            /**创建者 超级管理员或组织管理员*/
             hasInvitePermission = data.deptId == "0" || ORG_PERMISS_TYPE.checkInvitePermission(data.power)

            val showInvite = data.rejectJoin == 0 && (hasInvitePermission || data.rejectInvitation == 0)

            if (showInvite) {
                visitLayout.visibility = View.VISIBLE
            } else {
                visitLayout.visibility = View.GONE
            }

            if (StringUtils.isNotBlankAndEmpty(data.logo)) {
                ImageLoaderUtils.loadImage(mActivity, ivLogo, data.logo)
            }
            tvName.text = data.name
            //确定当前显示的主要团队是你创建的企业，更多处显示分配权限
            if (orgPermission) {
                layout_moreinfo.setOnClickListener(this)
                layout_moreinfo.visibility = View.VISIBLE
            } else {
                layout_moreinfo.setOnClickListener(null)
                layout_moreinfo.visibility = View.INVISIBLE
            }
        } else {
            orgSize.visibility = View.GONE
            layoutHas.visibility = View.GONE
            layoutNon.visibility = View.VISIBLE
        }
    }

    /**协作人列表*/
    private fun showCooperationInfo(list: ArrayList<WorkStationBean>) {
        cooperationCompanyList = list
        layoutNon.visibility = View.VISIBLE
        layoutHas.visibility = View.GONE
        layoutHasCooper.visibility = View.VISIBLE
        cooperationTitle.visibility = View.VISIBLE
        adapter.setSourceList(cooperationCompanyList)
    }

    private fun showRightRedDotNew() {
        ivDotRight2.visibility = View.VISIBLE
    }

    private fun hideRightRedDotNew() {
        ivDotRight2.visibility = View.GONE
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.tv_add -> {
                val intent = Intent(requireActivity(), SearchResultActivity::class.java)
                intent.putExtra("type", "searchOrg")
                startActivity(intent)
            }
            R.id.tv_creat -> {
                val intent1 = Intent(requireActivity(), CreateOrgActivity::class.java)
                startActivityForResult(intent1, 0x22)
            }
            R.id.ll_visit_layout -> {// 邀请人员
                val intent1 = Intent(requireActivity(), SelectVisitMethodActivity::class.java)
                intent1.putExtra("companyName", mainOrg?.name)
                intent1.putExtra("companyId", mainOrg?.companyId)
                intent1.putExtra("companyLogo", mainOrg?.logo)
                startActivity(intent1)
            }
            R.id.other_layout -> {
                val intent1 = Intent(requireActivity(), MineOrganizationsActivity::class.java)
                startActivity(intent1)
            }
            R.id.cl_member_watch -> {//通讯录页，点击团队架构
                //查看团队架构
                //如果是管理者默认有团队管理权限
//                if (mainOrg?.deptId == "0") orgPermission = true
                val intent1 = Intent(requireActivity(), OrganizationChartActivity::class.java)
                intent1.putExtra("companyId", mainOrg?.companyId)
                intent1.putExtra("depName", mainOrg?.name)
                intent1.putExtra("orgPermission", hasInvitePermission)//tcp修复被授权组织管理权限后仍然不能管理的bug
                startActivity(intent1)
            }
            R.id.cl_watch_info -> {
                //如果是管理者默认有团队管理权限
                val isCreator: Boolean = mainOrg?.deptId == "0"
//                if (mainOrg?.deptId == "0") {
//                    orgPermission = true
//                }
                if (mainOrg != null) {
                    val intent1 = Intent(context, OrganizationInfoActivity::class.java)
                    intent1.putExtra("companyBean", mainOrg)
                    intent1.putExtra("orgPermission", orgPermission)
                    intent1.putExtra("isCreator", isCreator)
                    startActivity(intent1)
                }
            }
            R.id.cl_more_layout -> {
                if (mainOrg != null) {
                    val intent1 = Intent(requireActivity(), OrganizationMoreActivity::class.java)
                    intent1.putExtra("companyBean", mainOrg)
                    startActivity(intent1)
                }
            }
            R.id.cl_external_layout -> {
                if (mainOrg != null) {
                    val intent1 = Intent(requireActivity(), OrgExternalContactActivity::class.java)
                    intent1.putExtra("companyBean", mainOrg)
                    intent1.putExtra("orgPermission", orgPermission)
                    startActivity(intent1)
                }
            }
            R.id.search -> {//通讯录页------点击搜索，全局搜索
                val intent = Intent(requireActivity()!!, SearchGlobalActivity::class.java)
                startActivity(intent)
//                val intent = Intent(activity!!, SearchGlobalActivity::class.java)
//                startActivity(intent)
            }
            R.id.rl_friend_layout -> {
                val intent = Intent(requireActivity()!!, FriendListActivity::class.java)
                intent.putExtra("type", "")
                startActivity(intent)
            }
            R.id.iv_right -> {
//                EventBusUtils.sendEvent(EventBusEvent(
//                        EventBusAction.Event_CLICK_HOME_ADDRESS_RIGHT_ADD, ""))
                if (msgPop == null) {
                    initPop()
                }
                msgPop!!.show(ivToolbarRight)
            }
            com.joinutech.ddbeslibrary.R.id.iv_right2 -> {//点击小铃铛
                val intent = Intent(mActivity, UndoActivity::class.java)
                startActivity(intent)
            }
            R.id.cl_group_layout -> {//点击我的群组
                val intent = Intent(mActivity, GroupListActivity::class.java)
                startActivity(intent)
            }
        }
    }

    private var msgPop: CommonListPop<String>? = null

    private fun initPop() {
        val items = arrayListOf("添加好友", "创建群组", "扫一扫")
        msgPop = CommonListPop(mActivity, items)
        msgPop!!.initView(
                onBind = { position, data, view ->
                    view.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.tv_info).text = data

                },
                onItemClick = { position, _ ->
                    msgPop!!.hidePop()
                    when (position) {
                        0 -> {
                            //添加好友
                            ARouter.getInstance().build(RouteOrg.addFriendActivity).navigation()
                        }
                        1 -> {
                            //创建群组
                            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.HOME_CREATE_GROUP, ""))
                        }
                        else -> {
                            //跳转扫描页面
                            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.HOME_SCAN, ""))
                        }
                    }
                })
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == 0x22 && resultCode == 0x22) {
//            // 通过刷新所有团队，来刷洗主要团队信息
//            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST, "2"))
        } else {
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

}