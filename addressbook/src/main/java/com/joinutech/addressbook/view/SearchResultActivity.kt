package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.InputType
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.ddbes.library.im.netutil.ImNetUtil
import com.ddbes.library.im.util.FriendCacheHolder
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.SearchUserListAdapter
import com.joinutech.addressbook.constract.SearchResultListContract
import com.joinutech.addressbook.databinding.ActivityMembersearchresultBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.common.adapter.BaseAdapter
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.CompanyBean
import com.joinutech.ddbeslibrary.bean.FriendSimpleBean
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.utils.*
//import kotlinx.android.synthetic.main.activity_membersearchresult.*
//import kotlinx.android.synthetic.main.search_include_layout.*
import javax.inject.Inject
import javax.inject.Named

/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName:
 * @Author:
 * @Leader:
 * @CreateTime:
 * @Desc: 搜索页面
 */
@Route(path = RouteOrg.searchResultActivity)
class SearchResultActivity : MyUseBindingActivity<ActivityMembersearchresultBinding>() {
    @Autowired
    @JvmField
    var type: String = ""

    /** type 类型 searchOrgMember 搜索团队成员 0
     *            searchOrg 搜索团队 1
     *            searchFriend 搜索好友 2
     *            phoneSearch 按手机号搜索 3
     *            addExternalContact 搜索并添加外部协作人 4
     */
    var typeInt = 0

    override val contentViewResId: Int = R.layout.activity_membersearchresult
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityMembersearchresultBinding {
        return ActivityMembersearchresultBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = true

    private lateinit var adapter: SearchUserListAdapter

    private var allMemberList: ArrayList<CompanyBean> = arrayListOf()
    private var companyId = ""

    @Inject
    @field:Named(AddressbookUtil.SEARCHLIST_PRESENTER)
    lateinit var presenter: SearchResultListContract.SearchResultListPresenter

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("type"))) {
                type = intent.getStringExtra("type") ?: ""
            }
        }
        if (StringUtils.isNotBlankAndEmpty(type)) {
            when (type) {
                "searchOrgMember" -> {
                    if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                        setPageTitle("团队成员")
                        companyId = intent.getStringExtra("companyId") ?: ""
                    }
                    typeInt = 0
                }
                "searchOrg" -> {
                    setPageTitle("加入团队")
                    typeInt = 1
                }
                "searchFriend" -> {
                    setPageTitle("搜索好友")
                    typeInt = 2
                }
                "phoneSearch" -> {
                    setPageTitle("添加好友")
                    typeInt = 3
                }
                "addExternalContact" -> {
                    setPageTitle("我的好友")
                    typeInt = 4
                }
            }
        }
    }

    private lateinit var emptyPage: PageEmptyView

    override fun initView() {
        emptyPage = findViewById(R.id.layout_empty_layout)
        whiteStatusBarBlackFont()
        showToolBarLine()
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN)
        DaggerAddressbookComponent.builder().build().inject(this)
        binding.searchInclueLayout.search.requestFocus()
        //回车变搜索
        binding.searchInclueLayout.search.imeOptions = EditorInfo.IME_ACTION_SEARCH
        binding.searchInclueLayout.search.inputType = EditorInfo.TYPE_CLASS_TEXT

        binding.searchUserList.layoutManager = LinearLayoutManager(this)
        //显示是团队搜索还是成员搜索
        initCurrentShow()
    }

    override fun initLogic() {
        super.initLogic()
        binding.searchInclueLayout.delete.setOnClickListener(this)
        binding.searchInclueLayout.cancel.setOnClickListener(this)
        adapter = SearchUserListAdapter(this, allMemberList, type)
        binding.searchUserList.adapter = adapter
        dealEditTextEvent()
        adapter.setOnItemClickListener(object : BaseAdapter.OnItemClickListener {
            override fun onItemClick(view: View, position: Int) {
                dealAdapterItemClick(position)
            }
        })
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.searchInclueLayout.delete -> {
                clearEditText()
            }
            binding.searchInclueLayout.cancel -> {
                clearEditText()
            }
        }
    }

    override fun onBackPressed() {
        if (currentFocus != null && currentFocus?.windowToken != null) {
            (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                    .hideSoftInputFromWindow(<EMAIL>!!
                            .windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        }
        super.onBackPressed()
    }

    /**
     * 清空搜索内容
     */
    private fun clearEditText() {
        binding.searchInclueLayout.search.setText("")
        val emptyList: List<CompanyBean> = arrayListOf()
        adapter.setDataSourceList(emptyList)
        binding.searchInclueLayout.delete.visibility = View.INVISIBLE
        binding.searchUserList.visibility = View.GONE
        emptyPage.visibility = View.VISIBLE
        try {
            if (currentFocus != null && currentFocus?.windowToken != null)
                (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                        .hideSoftInputFromWindow(currentFocus!!
                                .windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        } catch (e: java.lang.Exception) {

        }
    }

    private fun initCurrentShow() {
        binding.searchInclueLayout.delete.visibility = View.INVISIBLE
        when (typeInt) {
            1 -> {
                //搜索的加入的团队
                binding.searchInclueLayout.search.hint = "请输入你要查找的团队名称"
                dealCompanySearchNoResult()
            }
            2 -> {
                //搜索的好友
                binding.searchInclueLayout.search.hint = "搜索担当好友"
                binding.searchInclueLayout.delete.visibility = View.INVISIBLE
                dealFriendSearchNoResult()
            }
            3 -> {
                //通过手机号添加好友
                binding.searchInclueLayout.search.hint = "请输入要添加的用户手机号"
                binding.searchInclueLayout.search.inputType = InputType.TYPE_CLASS_PHONE
                binding.searchInclueLayout.search.maxEms = 11
                binding.searchInclueLayout.search.setSingleLine(true)
                dealPhoneSearchNoResult("")
            }
            4 -> {
                //搜索团队管理人员
                binding.searchInclueLayout.search.hint = "输入要搜索的用户名"
                dealSearchFriend("")
            }
            else -> {
                //其他暂为团队成员
                binding.searchUserList.visibility = View.GONE
                emptyPage.visibility = View.VISIBLE
            }
        }
    }

    private fun dealAdapterItemClick(position: Int) {
        if (typeInt == 1) {
            //搜索的加入的团队
            if (allMemberList.isNotEmpty()) {
                val intent = Intent(mContext!!, OrganizationIntroActivity::class.java)
                intent.putExtra("companyId", allMemberList[position].companyId)
                startActivity(intent)
            }
        } else if (typeInt == 4) {
            //给外部联系人使用,好友列表点击事件tcp
            val companyBean = allMemberList[position]
            if (companyBean.logout == 0) {
                val bean = FriendSimpleBean(companyBean.name, companyBean.address,
                        companyBean.area)
                intent.putExtra("bean", bean)
                setResult(Activity.RESULT_OK, intent)
                finish()
            } else {
                toastShort("该好友已注销账号")
            }
        } else {  //搜索的用户
            val intent = Intent(mContext!!, FriendInfoActivity::class.java)
            intent.putExtra("userId", allMemberList[position].companyId)
            when (typeInt) {
                0 -> {
                    //用户是团队成员
                    intent.putExtra("type", "org")
                    intent.putExtra("companyId", companyId)
                }
                2 -> {
                    //好友列表的搜索好友
                    intent.putExtra("type", "friend")
                }
                3 -> {
                    //先判断是否是自己
                    if (allMemberList[position].companyId == userId) {
                        intent.putExtra("type", "friend")
                    } else {
                        //手机搜索添加好友，好友信息页需显示为申请详情
                        intent.putExtra("type", "")
                        intent.putExtra("enterType", 1)//搜索好友标记
                    }
                }
            }
            startActivity(intent)
        }
    }

    private fun dealEditTextEvent() {
        binding.searchInclueLayout.search.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (s != null && StringUtils.isNotBlankAndEmpty(s.toString())) {
                    binding.searchInclueLayout.delete.visibility = View.VISIBLE
                } else {
                    binding.searchInclueLayout.delete.visibility = View.GONE
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        })

        binding.searchInclueLayout.search.setOnEditorActionListener { view, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                val string = view.text.toString()
                if (StringUtils.isNotBlankAndEmpty(string)) {
                    binding.searchInclueLayout.delete.visibility = View.VISIBLE
                    //输入内容不为空
                    when (typeInt) {
                        1 -> //搜的团队
                            dealSearchCompany(string)
                        2, 4 -> //搜的好友
                            dealSearchFriend(string)
                        3 ->//搜的手机号
                            dealSearchPhone(string)
                        else -> //搜的团队成员
                            dealMemberSearchData(string)
                    }
                } else {
                    binding.searchInclueLayout.delete.visibility = View.GONE
                    if (typeInt == 4) {
                        dealSearchFriend("")
                    } else {
                        showNoResult()
                    }
                }
                true
            }
            false
        }
    }

    private fun dealSearchPhone(string: String) {
        if (StringUtils.isPhoneNumber(string)) {
            //先判断是手机号再发送请求
            presenter.searchPhoneResult(bindToLifecycle(), accessToken!!,
                    string,
                    onSuccess = {
                        allMemberList.clear()
                        val bean = CompanyBean("", "", it.userId,
                                "", "", "", "",
                                "", "", it.avatar, it.name,
                                "", "", "", 0,
                                0, "", "", "",
                                0, "", 0, "")
                        allMemberList.add(bean)
                        binding.searchUserList.visibility = View.VISIBLE
                        emptyPage.visibility = View.GONE
                        adapter.setUserId(userId!!)
                        adapter.setDataSourceList(allMemberList)
                    },
                    onError = {
                        ToastUtil.show(mContext!!, it)
                        if (it == "未注册使用担当") {
                            //判断返回列表是否为空
                            //手机号正确但此用户未注册
                            dealPhoneSearchNoResult("该用户尚未注册使用担当")
                        } else {
                            dealPhoneSearchNoResult("没有找到符合条件的用户\n请检查你的输入是否正确")
                        }
                    })
        } else {
            ToastUtil.show(mContext!!, "请检查你的输入是否正确")
        }
    }

    private fun dealSearchFriend(keyWord: String) {
//        if (typeInt == 4) {
//            searchExternalFriendList(keyWord)
//        } else {
//            searchNormalFriendList(keyWord)
//        }
        searchNormalFriendList(keyWord)
    }

    /**搜索本地好友信息*/
    private fun searchNormalFriendList(keyWord: String) {
        showLoading()
        //获取好友列表tcp
        ImNetUtil.getFriendListWithPhoneAndEmail(bindToLifecycle(), keyWord, onSuccess = { resultList ->
            hideLoading()
            allMemberList.clear()
            for (item in resultList) {
                val friendInfo = FriendCacheHolder.getFriend(item.userId)
                var mName = ""
                if (friendInfo != null) {
//                    val userNick = friendInfo.userNick
                    mName = if (friendInfo.userNick.isNullOrBlank()) {
                        item.name
                    } else {
//                        userNick
                        friendInfo.userNick!!
                    }
                }
                val bean = CompanyBean(item.phone ?: "", item.email ?: "",
                        item.userId, "", "", "", "",
                        "", "", item.avatar, mName,
                        "", "", "", 0,
                        0, "", "", "", 0,
                        "", 0, "", logout = item.logout)
                allMemberList.add(bean)
            }
            binding.searchUserList.visibility = View.VISIBLE
            emptyPage.visibility = View.GONE
            adapter.setUserId(userId!!)
            adapter.setDataSourceList(allMemberList)
        }, onFailer = {
            dismissDialog()
            hideLoading()
            ToastUtil.show(mContext!!, it)
            dealFriendSearchNoResult("未找到符合条件的担当好友")
        })
    }

    private fun dealMemberSearchData(string: String) {
        presenter.dealMemberResult(bindToLifecycle<Result<List<SearchMemberBean>>>(), accessToken!!,
                string, companyId,
                onSuccess = {
                    allMemberList.clear()
                    if (it.isNullOrEmpty()) {
                        binding.searchUserList.visibility = View.GONE
                        emptyPage.visibility = View.VISIBLE
                    } else {
                        //因为和团队共用一个搜索页面，所以需构建bean类
                        for (item in it) {
                            val bean = CompanyBean("", "", item.userId,
                                    "", "", "", "",
                                    "", "", item.headimg, item.name
                                    , "", "", "",
                                    0, 0,
                                    "", "", "", 0, "", 0, "")
                            allMemberList.add(bean)
                        }
                        binding.searchUserList.visibility = View.VISIBLE
                        emptyPage.visibility = View.GONE
                        adapter.setEditText(string)
                    }
                    adapter.setDataSourceList(allMemberList)
                },
                onError = {
                    ToastUtil.show(mContext!!, it)
                    binding.searchUserList.visibility = View.GONE
                    emptyPage.visibility = View.VISIBLE
                })
    }

    private fun showNoResult() {
        binding.searchInclueLayout.delete.visibility = View.INVISIBLE
        binding.searchUserList.visibility = View.GONE
        emptyPage.visibility = View.GONE
        //显示无结果并清空存储数据的集合
        allMemberList.clear()
        adapter.setDataSourceList(allMemberList)
    }

    private fun dealSearchCompany(string: String) {
        presenter.searchResult(bindToLifecycle(), accessToken!!, string, {
            allMemberList.clear()
            if (it.isNullOrEmpty()) {
                dealCompanySearchNoResult("未找到符合条件的团队")
            } else {
//                allMemberList = it as ArrayList<CompanyBean>
                allMemberList.addAll(it)
                binding.searchUserList.visibility = View.VISIBLE
                emptyPage.visibility = View.GONE
                adapter.setEditText(string)
            }
            adapter.setDataSourceList(allMemberList)
        }, {
            ToastUtil.show(mContext!!, it)
            dealCompanySearchNoResult("未找到符合条件的团队")
        })
    }

    private fun dealCompanySearchNoResult(s: String = "") {
        binding.searchUserList.visibility = View.GONE
        if (StringUtils.isEmpty(s)) {
            emptyPage.hide()
        } else {
            emptyPage.setContent(s)
            emptyPage.show()
        }
    }

    private fun dealFriendSearchNoResult(s: String = "") {
        binding.searchUserList.visibility = View.GONE
        if (StringUtils.isEmpty(s)) {
            emptyPage.hide()
        } else {
            emptyPage.setContent(s)
            emptyPage.show()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun dealPhoneSearchNoResult(s: String) {
        binding.searchUserList.visibility = View.GONE
        if (StringUtils.isEmpty(s)) {
            emptyPage.hide()
        } else {
            emptyPage.setContent(s)
            emptyPage.show()
        }
    }
}