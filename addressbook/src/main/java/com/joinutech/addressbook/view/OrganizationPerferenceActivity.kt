package com.joinutech.addressbook.view

import android.view.LayoutInflater
import android.view.View
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.AddressbookConstract
import com.joinutech.addressbook.databinding.ActivityOrganMoresettingBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.StringUtils
import javax.inject.Inject
import javax.inject.Named

/**
 *<AUTHOR>
 *@date 2018/11/23
 * 团队偏好设置
 */
class OrganizationPerferenceActivity : MyUseBindingActivity<ActivityOrganMoresettingBinding>() {

    override val contentViewResId: Int = R.layout.activity_organ_moresetting
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityOrganMoresettingBinding {
        return ActivityOrganMoresettingBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true
    override fun openArouterReceive(): Boolean = false

    private var companyId: String = ""
    private var rejectJoin = 0
    private var rejectInvitation = 0

    @Inject
    @field:Named(AddressbookUtil.ADDR_PRESENTER)
    lateinit var presenter: AddressbookConstract.AddressbookPresenter

    override fun initImmersion() {
        setPageTitle("偏好设置")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        whiteStatusBarBlackFont()
        showToolBarLine()
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                companyId = intent.getStringExtra("companyId")!!
            }
            rejectJoin = intent.getIntExtra("rejectJoin", 0)
            rejectInvitation = intent.getIntExtra("rejectInvitation", 0)
        }
    }

    override fun initView() {
        DaggerAddressbookComponent.builder().build().inject(this)
        binding.organSw1.setCheckedNoEvent(rejectJoin == 0)
        binding.organSw2.setCheckedNoEvent(rejectInvitation == 0)
        if (!binding.organSw1.isChecked) {
            binding.enableVisitLayout.visibility = View.GONE
            binding.visitHintText.visibility = View.GONE
        } else {
            binding.enableVisitLayout.visibility = View.VISIBLE
            binding.visitHintText.visibility = View.VISIBLE
        }
    }

    override fun initLogic() {
        super.initLogic()
        binding.organSw1.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                val map = hashMapOf<String, Any>()
                map["companyId"] = companyId
                rejectJoin = 0
                map["rejectJoin"] = rejectJoin
                presenter.modifyCompany(bindToLifecycle(), accessToken, map,
                        onSuccess = {
                            binding.enableVisitLayout.visibility = View.VISIBLE
                            binding.visitHintText.visibility = View.VISIBLE
                            // 团队设置不需要刷新团队列表和通讯录页面
//                            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ADDRFRAGMENT, 0))
//                            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST, ""))
                            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.ORG_PREFRENCE,
                                    arrayListOf(rejectInvitation, rejectJoin)))
                        },
                        onError = {
                            //以防不成功
                            binding.organSw1.setCheckedNoEvent(false)
                        })
            } else {
                val map = hashMapOf<String, Any>()
                map["companyId"] = companyId
                rejectJoin = 1
                map["rejectJoin"] = rejectJoin
                presenter.modifyCompany(bindToLifecycle(), accessToken!!, map, {
                    binding.enableVisitLayout.visibility = View.GONE
                    binding.visitHintText.visibility = View.GONE
//                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ADDRFRAGMENT, 0))
//                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST, ""))
                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.ORG_PREFRENCE,
                            arrayListOf(rejectInvitation, rejectJoin)))
                }, {
                    //以防不成功
                    binding.organSw1.setCheckedNoEvent(true)
                })
            }
        }
        binding.organSw2.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                val map = hashMapOf<String, Any>()
                map["companyId"] = companyId
                rejectInvitation = 0
                map["rejectInvitation"] = rejectInvitation
                presenter.modifyCompany(bindToLifecycle(), accessToken!!, map,
                        onSuccess = {
//                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ADDRFRAGMENT, 0))
//                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST, ""))
                            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.ORG_PREFRENCE,
                                    arrayListOf(rejectInvitation, rejectJoin)))
                        },
                        onError = {
                            //以防不成功
                            binding.organSw2.setCheckedNoEvent(false)
                        })
            } else {
                val map = hashMapOf<String, Any>()
                map["companyId"] = companyId
                rejectInvitation = 1
                map["rejectInvitation"] = rejectInvitation
                presenter.modifyCompany(bindToLifecycle(), accessToken!!, map,
                        onSuccess = {
//                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ADDRFRAGMENT, 0))
//                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST, ""))
                            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.ORG_PREFRENCE,
                                    arrayListOf(rejectInvitation, rejectJoin)))
                        },
                        onError = {
                            //以防不成功
                            binding.organSw2.setCheckedNoEvent(true)
                        })
            }
        }
    }

}