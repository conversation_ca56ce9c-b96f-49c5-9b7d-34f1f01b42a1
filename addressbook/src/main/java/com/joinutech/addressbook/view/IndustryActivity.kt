package com.joinutech.addressbook.view

import android.app.Activity
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.IndustryAdapter
import com.joinutech.addressbook.adapter.IndustryAdapter2
import com.joinutech.addressbook.constract.AddressbookConstract
import com.joinutech.addressbook.databinding.ActivityIndustryBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.JobChoiceBean
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import javax.inject.Inject
import javax.inject.Named

/**
 *<AUTHOR>
 *@date 2018/11/21
 * 所属行业
 */
@Route(path = RouteOrg.industryActivity)
class IndustryActivity : MyUseBindingActivity<ActivityIndustryBinding>() {

    override val contentViewResId: Int = R.layout.activity_industry
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityIndustryBinding {
        return ActivityIndustryBinding.inflate(layoutInflater)
    }

    @Inject
    @field:Named(AddressbookUtil.ADDR_PRESENTER)
    lateinit var presenter: AddressbookConstract.AddressbookPresenter
    private lateinit var adapter1: IndustryAdapter
    private var adapter2: IndustryAdapter2? = null

    @Autowired
    @JvmField
    var type = ""

    @Autowired
    @JvmField
    var profession = ""
    var companyId: String = "-2"

    override fun initImmersion() {
        setPageTitle("所属行业")
        setRightTitle("保存", this)
        whiteStatusBarBlackFont()
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
    }

    private var isManager: Int = 0

    override fun initView() {
        if (intent.hasExtra("isManager")) {
            isManager = intent.getIntExtra("isManager", 0)
        }
        if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
            companyId = intent.getStringExtra("companyId") ?: ""
        }
        DaggerAddressbookComponent.builder().build().inject(this)
        
        binding.recycler1Industry.layoutManager =
                LinearLayoutManager(mContext!!,
                        LinearLayoutManager.VERTICAL, false)
        binding.recycler2Industry.layoutManager = LinearLayoutManager(mContext!!,
                LinearLayoutManager.VERTICAL, false)
    }

    override fun initLogic() {
        super.initLogic()
        if (StringUtils.isEmpty(type)) {
            type = ""
        }
        presenter.getIndustry(bindToLifecycle(), accessToken!!, type, {
            if (it.isNullOrEmpty()) return@getIndustry
            if (StringUtils.isEmpty(type) || StringUtils.isEmpty(profession)) {
                for (item in it) {
                    item.type = 2
                    for (itemSon in item.son!!) {
                        itemSon.type = 2
                    }
                }
                it[0].type = 1
                if (!it[0].son.isNullOrEmpty()) {
                    it[0].son!![0].type = 1
                    adapter2 = IndustryAdapter2(this,
                            (it[0].son as ArrayList<JobChoiceBean.IndustrysBean>?)!!)
                }
            } else {// 个人信息页面才会传参
                if (it.isNotEmpty()) {
                    for (item in it) {
                        item.type = 2
                        for (itemSon in item.son!!) {
                            itemSon.type = 2
                            if (itemSon.name == profession) {
                                item.type = 1
                                itemSon.type = 1
                                adapter2 = IndustryAdapter2(this,
                                        (item.son as ArrayList<JobChoiceBean.IndustrysBean>?)!!)
                            }
                        }
                    }
                    if (adapter2 == null) {
                        it[0].type = 1
                        if (!it[0].son.isNullOrEmpty()) {
                            it[0].son!![0].type = 1
                            adapter2 = IndustryAdapter2(this,
                                    (it[0].son as ArrayList<JobChoiceBean.IndustrysBean>?)!!)
                        }
                    }
                }
            }
            adapter1 = IndustryAdapter(this, it as ArrayList<JobChoiceBean>, adapter2!!)
            binding.recycler1Industry.adapter = adapter1
            binding.recycler2Industry.adapter = adapter2
            if (StringUtils.isEmpty(type) || StringUtils.isEmpty(profession)) {
                adapter2?.selBean = it[0].son!![0]
            } else {
                if (it.isNotEmpty()) {
                    for (item in it) {
                        for (itemSon in item.son!!) {
                            if (itemSon.name == profession) {
                                adapter2?.selBean = itemSon
                            }
                        }
                    }
                }
            }
        }, {
            ToastUtil.show(mContext!!, it)
        })
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            com.joinutech.ddbeslibrary.R.id.tv_toolbar_right -> {
                if (StringUtils.isEmpty(type)) {
                    if (isManager == 0) {
                        val intent1 = Intent()
                        intent1.putExtra("industry", adapter2?.selBean?.name)
                        setResult(0x22, intent1)
                        finish()
                    } else {
                        val map = hashMapOf<String, Any>()
                        map["industry"] = adapter2?.selBean?.name!!
                        map["companyId"] = companyId
                        presenter.modifyCompany(bindToLifecycle(), accessToken!!, map, {
                            val intent1 = Intent()
                            intent1.putExtra("industry", adapter2?.selBean?.name)
                            setResult(Activity.RESULT_OK, intent1)
                            finish()
                        }, {
                            ToastUtil.show(mContext!!, it)
                        })
                    }
                } else {
                    getLoadingDialog("提交职业选择", false)
                    presenter.commitPersonInfo(bindToLifecycle(), adapter2?.selBean?.name!!,
                            "profession", {
                        dismissDialog()
                        setResult(Activity.RESULT_OK, intent.putExtra("industry",
                                adapter2?.selBean?.name!!))
                        finish()
                    }, {
                        dismissDialog()
                        ToastUtil.show(mContext!!, it)
                    })
                }
            }
        }
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return true
    }

}