package com.joinutech.addressbook.view.secret

import android.app.Activity
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.lifecycle.Observer

import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.addressbook.R
import com.joinutech.addressbook.databinding.ActivitySecretOpenRangeBinding
import com.joinutech.common.adapter.MyAdapter
import com.joinutech.ddbeslibrary.base.SimpleBaseActivity
import com.joinutech.ddbeslibrary.request.exception.ErrorType
import com.joinutech.ddbeslibrary.utils.*

/**
 * @PackageName: com.joinutech.addressbook.view.secret
 * @ClassName: SecretOpenRangeActivity
 * @Desc: 隐私可见范围设置
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/3/16 16:56
 */
@Route(path = RouteOrg.ORG_SECRET_VISIBLE_RANGE)
class SecretOpenRangeActivity : SimpleBaseActivity<ActivitySecretOpenRangeBinding>() {

    override val contentViewResId: Int = R.layout.activity_secret_open_range
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivitySecretOpenRangeBinding {
        return ActivitySecretOpenRangeBinding.inflate(layoutInflater)
    }

//    companion object {
//        val data = arrayListOf("所有成员均不可见，包括本部门内成员", "仅本部门及所有直属上级部门可见", "仅本部门内成员可见", "仅指定部门可见")
//    }

    lateinit var adapter: MyAdapter<String>
    private var selectPosition = 0
    //    private var selectDeptIds = ""
    private var secretSetting: SecretSettingData? = null
    private val selectDeptList = arrayListOf<String>()

    val data = arrayListOf<String>()
    var companyId = ""
    /**部门隐私设置 默认加载实时部门数据
     * 数字报告 获取部门设置，根据时间判断*/
    private var pageType = 0
    private var ateHisDate = ""
    override fun initView() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back2)
        intent.extras?.let {
            pageType = it.getInt(CommonKeys.PAGE_TYPE)
            if (pageType == 0) {
                secretSetting = intent?.getSerializableExtra(CommonKeys.EXTRA_DATA) as SecretSettingData?
            }
            selectPosition = it.getInt(CommonKeys.POSITION, 0)
            ateHisDate = it.getString(CommonKeys.DATE, "")
            /**已选择部门ids*/
            val temp1 = it.getStringArrayList(CommonKeys.DATA)
            if (!temp1.isNullOrEmpty()) {
                selectDeptList.addAll(temp1)
            }
            /**数据源*/
            val temp = it.getStringArrayList("source")
            if (temp.isNullOrEmpty()) {
                data.addAll(arrayListOf("所有成员均不可见，包括本部门内成员", "仅本部门及所有直属上级部门可见", "仅本部门内成员可见", "仅指定部门可见"))
            } else {
                data.addAll(temp)
            }
            companyId = it.getString(CommonKeys.COMPANY_ID, "")
            setPageTitle(it.getString(CommonKeys.TITLE_NAME, "可见范围"))
            setRightTitle(it.getString(CommonKeys.RIGHT_TITLE, "完成"), this)
        }

        adapter = MyAdapter(this, R.layout.item_secret_range_layout, data,
                onBindItem = { position: Int, info: String, view: View ->
                    if (position > 0) {
                        view.findViewById<View>(R.id.line_top).visibility = View.GONE
                    } else {
                        view.findViewById<View>(R.id.line_top).visibility = View.VISIBLE
                    }
                    val range = view.findViewById<View>(R.id.ll_range)
                    if (position == data.lastIndex && selectPosition == data.lastIndex) {
                        range.visibility = View.VISIBLE
                        range.setOnClickListener(object : OnNoDoubleClickListener {
                            override fun onNoDoubleClick(v: View) {
                                ARouter.getInstance().build(RouteOrg.ORG_DEPT_SELECT)
                                        .withString(CommonKeys.COMPANY_ID, companyId)
                                        .withString(CommonKeys.DATE, ateHisDate)
                                        .withString(CommonKeys.TITLE_NAME, "选择部门")
                                        .withString(CommonKeys.RIGHT_TITLE, "完成")
                                        .withInt(CommonKeys.PAGE_TYPE, pageType)
                                        .withStringArrayList(CommonKeys.DATA, selectDeptList)
                                        .navigation(this@SecretOpenRangeActivity, 601)
//                                val intent = Intent(mContext, DeptSelectActivity::class.java)
//                                intent.putExtra(CommonKeys.COMPANY_ID, companyId)
//                                intent.putExtra(CommonKeys.TITLE_NAME, "选择部门")
//                                intent.putExtra(CommonKeys.RIGHT_TITLE, "完成")
//                                startActivityForResult(intent, 601)
                            }
                        })
                    } else {
                        range.visibility = View.GONE
                    }

                    if (selectPosition == position) {
                        view.findViewById<View>(R.id.iv_none).visibility = View.VISIBLE
                    } else {
                        view.findViewById<View>(R.id.iv_none).visibility = View.GONE
                    }
                    view.findViewById<TextView>(R.id.tv_none).text = info
                },
                onItemClick = { position: Int, s: String, view: View ->
                    val oldPosition = selectPosition
                    selectPosition = position
                    adapter.notifyItemChanged(oldPosition)
                    adapter.notifyItemChanged(position)
                })
        binding.rvList.layoutManager = LinearLayoutManager(this)
        binding.rvList.adapter = adapter
    }

    private lateinit var viewModel: SecretModel

        override fun initLogic() {
        super.initLogic()

        viewModel = getModel(SecretModel::class.java)
        viewModel.saveSecretSettingResult.observe(this, Observer {
            when {
                it.success != null -> {
                    intent.putExtra(CommonKeys.POSITION, selectPosition)
                    if (selectPosition == data.lastIndex) {
                        intent.putExtra(CommonKeys.RESULT_DATA, selectDeptList)//回调上一页面
                    } else {
                        intent.putExtra(CommonKeys.RESULT_DATA, arrayListOf<String>())
                    }
                    setResult(Activity.RESULT_OK, intent)
                    finish()
                }
                it.errorCode != ErrorType.SUCCESS -> {
                    if (it.extra != null && it.extra is String) {
                        toastShort(it.extra!! as String)
                    } else {
                        toastShort("保存失败")
                    }
                }
                else -> {
                    toastShort("保存失败")
                }
            }
        })
    }

    //    var selectDeptIdList = arrayListOf<String>()
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK && requestCode == 601) {
            selectDeptList.clear()
            selectDeptList.addAll(data?.getStringArrayListExtra(CommonKeys.RESULT_DATA)
                    ?: arrayListOf())
        } else {
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

    override fun onNoDoubleClick(v: View) {
        super.onNoDoubleClick(v)
        when (v.id) {
            com.joinutech.ddbeslibrary.R.id.tv_toolbar_right -> {
                if (pageType == 0) {
                    if (secretSetting != null) {
                        if (selectPosition == data.lastIndex) {
                            if (!selectDeptList.isNullOrEmpty()) {//提交新设置判断
                                val selectDeptIds = selectDeptList.joinToString(separator = ",")
                                val setting = secretSetting!!.copy(allow_type = selectPosition + 1, allowDeptIds = selectDeptIds)
                                viewModel.saveSecretSetting(setting)
                            } else {
                                toastShort("请指定可见部门")
                            }
                        } else {
                            val setting = secretSetting!!.copy(allow_type = selectPosition + 1, allowDeptIds = "")
                            viewModel.saveSecretSetting(setting)
                        }
                    }
                } else {
                    intent.putExtra(CommonKeys.POSITION, selectPosition)
                    intent.putExtra(CommonKeys.DATE, ateHisDate)
                    if (selectPosition == data.lastIndex) {
                        intent.putExtra(CommonKeys.RESULT_DATA, selectDeptList)//回调上一页面
                    } else {
                        intent.putExtra(CommonKeys.RESULT_DATA, arrayListOf<String>())
                    }
                    setResult(Activity.RESULT_OK, intent)
                    finish()
                }
            }
        }
    }

}
