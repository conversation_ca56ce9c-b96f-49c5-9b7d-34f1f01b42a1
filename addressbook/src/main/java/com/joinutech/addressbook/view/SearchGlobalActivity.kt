package com.joinutech.addressbook.view

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Observer

import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.bean.GroupSearchBean
import com.joinutech.addressbook.view.fragment.SearchGlobalFragment
import com.joinutech.addressbook.viewModel.GroupListViewModel
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.ddbes.library.im.imtcp.Logger
import com.ddbes.library.im.imtcp.imservice.msghelper.GroupInfoUtil
import com.joinutech.addressbook.databinding.ActivitySearchGlobalBinding
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
//import kotlinx.android.synthetic.main.activity_search_global.*
//import kotlinx.android.synthetic.main.search_include_layout.*
import net.lucode.hackware.magicindicator.ViewPagerHelper
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.ColorTransitionPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.SimplePagerTitleView

/**
 * @Description: 全局搜索
 * @Author: hjr
 * @Time: 2020/2/25 16:12
 * @packageName: com.joinutech.addressbook.view
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
class SearchGlobalActivity : MyUseBindingActivity<ActivitySearchGlobalBinding>() {

    override val contentViewResId: Int
        get() = R.layout.activity_search_global

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivitySearchGlobalBinding {
        return ActivitySearchGlobalBinding.inflate(layoutInflater)
    }

    private lateinit var pageAdapter: SearchGlobalPageAdapter
    private var titleList = arrayListOf<String>()
    private var currentPosition = 0
    private var searchTextValue = ""
    private lateinit var fragments: Array<SearchGlobalFragment?>
    private lateinit var viewModel: GroupListViewModel

    override fun initImmersion() {
        setPageTitle("搜索")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        binding.searchBar.search.hint = "搜索群组"
        binding.searchBar.search.imeOptions = EditorInfo.IME_ACTION_SEARCH
        binding.searchBar.search.inputType = EditorInfo.TYPE_CLASS_TEXT
        binding.searchBar.delete.visibility = View.GONE
        binding.searchBar.cancel.visibility = View.GONE
        titleList.add("全部")
        titleList.add("担当好友")
        titleList.add("团队群组")
        titleList.add("私有群组")
        fragments = arrayOfNulls(titleList.size)
        for (i in fragments.indices) {
            fragments[i] = SearchGlobalFragment.newInstance(i, searchTextValue)
        }
        viewModel = getModel(GroupListViewModel::class.java)
    }

    override fun initLogic() {
        super.initLogic()

        getObservable()
        binding.searchBar.search.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (s != null && s.isNotEmpty()) {
                    binding.searchBar.delete.visibility = View.VISIBLE
                    binding.searchBar.cancel.visibility = View.VISIBLE
                } else {
                    binding.searchBar.delete.visibility = View.GONE
                    binding.searchBar.cancel.visibility = View.GONE
                    cancelSearchEvent(false)
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

        })

        binding.searchBar.search.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH ||
                    (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER)) {
                searchTextValue = binding.searchBar.search.text.toString().trim()
                if (StringUtils.isNotBlankAndEmpty(searchTextValue))
                    getSearchGroupData()
                return@setOnEditorActionListener true
            }
            false
        }
        pageAdapter = object : SearchGlobalPageAdapter(this) {
            override fun createFragment(position: Int): Fragment {
                return fragments[position]!!
            }
        }
        binding.vpGroupPager.offscreenPageLimit = fragments.size
        binding.vpGroupPager.adapter = pageAdapter
        val commonNavigator = CommonNavigator(this)
        commonNavigator.adapter = object : CommonNavigatorAdapter() {
            override fun getCount(): Int {
                return titleList.size
            }

            override fun getTitleView(context: Context, index: Int): IPagerTitleView {
                val simplePagerTitleView: SimplePagerTitleView = ColorTransitionPagerTitleView(context)
                simplePagerTitleView.normalColor = CommonUtils.getColor(mContext!!,
                    com.joinutech.ddbeslibrary.R.color.colorFF323232)
                simplePagerTitleView.selectedColor = CommonUtils.getColor(mContext!!,
                    com.joinutech.ddbeslibrary.R.color.color007FF4)
                simplePagerTitleView.text = titleList[index]
                simplePagerTitleView.setOnClickListener {
                    currentPosition = index
                    binding.vpGroupPager.currentItem = index
                }
                return simplePagerTitleView
            }

            override fun getIndicator(context: Context): IPagerIndicator {
                val linePagerIndicator = LinePagerIndicator(context)
                linePagerIndicator.mode = LinePagerIndicator.MODE_WRAP_CONTENT
                linePagerIndicator.setColors(CommonUtils.getColor(mContext!!,
                    com.joinutech.ddbeslibrary.R.color.color007FF4))
                return linePagerIndicator
            }
        }
        binding.vpGroupPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                currentPosition = position
            }
        })
         binding.tbTopLayout.navigator = commonNavigator
        ViewPagerHelper.bind(binding.tbTopLayout, binding.vpGroupPager)
        binding.searchBar.delete.setOnClickListener(this)
        binding.searchBar.cancel.setOnClickListener(this)
    }

    //tcp开始全局搜索
    private fun getSearchGroupData() {
        getLoadingDialog("", false)
        Logger.i("----执行--验证全局搜索--", "----开始搜索----")
        viewModel.getGlobalList(this, searchTextValue)
       /* GroupInfoUtil.getAllGroupList(this,searchTextValue,
                onSuccess = {
                    Logger.i("----执行--验证全局搜索--", "----搜索成功----")
                },onError = {})*/
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            binding.searchBar.delete -> {
                if (StringUtils.isNotBlankAndEmpty(searchTextValue))
                    cancelSearchEvent(true)
            }
            binding.searchBar.cancel -> {
                if (StringUtils.isNotBlankAndEmpty(searchTextValue))
                    cancelSearchEvent(true)
            }
        }
    }

    private fun getObservable() {
        //全局搜索成功
        viewModel.getGlobalListSuccessObservable.observe(this, Observer {
            Logger.i("----执行--验证全局搜索--", "----全局搜索成功----")
            dismissDialog()
            if (it.isNullOrEmpty()) {
                for (i in fragments.indices) {
                    if (i == 0) {
                        fragments[0]!!.refreshAllListData(arrayListOf())
                    } else {
                        fragments[i]!!.refreshListData(arrayListOf())
                    }
                }
            } else {
                for (i in fragments.indices) {
                    fragments[i]!!.refreshKeyWord(searchTextValue)
                    when (i) {
                        0 -> {
                            val list = arrayListOf<GroupSearchBean>()
                            if (it.containsKey("担当好友") && !it["担当好友"].isNullOrEmpty()) {
                                list.add(GroupSearchBean("担当好友", it["担当好友"]!!))
                            }
                            if (it.containsKey("团队群组") && !it["团队群组"].isNullOrEmpty()) {
                                list.add(GroupSearchBean("团队群组", it["团队群组"]!!))
                            }
                            if (it.containsKey("私有群组") && !it["私有群组"].isNullOrEmpty()) {
                                list.add(GroupSearchBean("私有群组", it["私有群组"]!!))
                            }
                            fragments[i]!!.refreshAllListData(list)
                        }
                        1 -> {
                            if (it.containsKey("担当好友") && !it["担当好友"].isNullOrEmpty()) {
                                fragments[i]!!.refreshListData(it["担当好友"]!!)
                            }
                        }
                        2 -> {
                            if (it.containsKey("团队群组") && !it["团队群组"].isNullOrEmpty()) {
                                fragments[i]!!.refreshListData(it["团队群组"]!!)
                            }
                        }
                        3 -> {
                            if (it.containsKey("私有群组") && !it["私有群组"].isNullOrEmpty()) {
                                fragments[i]!!.refreshListData(it["私有群组"]!!)
                            }
                        }
                    }
                }
            }
        })
        viewModel.getGlobalListErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun cancelSearchEvent(isClearText: Boolean) {
        if (isClearText)
            binding.searchBar.search.setText("")
        searchTextValue = ""
        try {
            if (currentFocus != null && currentFocus?.windowToken != null)
                (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                        .hideSoftInputFromWindow(currentFocus!!
                                .windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        } catch (e: java.lang.Exception) {

        }
        for (i in fragments.indices) {
            if (i == 0) {
                fragments[0]!!.refreshAllListData(arrayListOf())
            } else {
                fragments[i]!!.refreshListData(arrayListOf())
            }
        }
    }

    override fun onBackPressed() {
        if (currentFocus != null && currentFocus?.windowToken != null)
            (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                    .hideSoftInputFromWindow(currentFocus!!
                            .windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        super.onBackPressed()
    }

    fun entryOtherFragment(index: Int) {
        binding.vpGroupPager.currentItem = index
    }

    override fun showToolBar(): Boolean {
        return true
    }


    override fun openArouterReceive(): Boolean {
        return false
    }

    abstract class SearchGlobalPageAdapter(fragmentActivity: FragmentActivity)
        : FragmentStateAdapter(fragmentActivity) {
        override fun getItemCount(): Int {
            return 4
        }
    }
}