package com.joinutech.addressbook.view

import android.animation.Animator
import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Build
import android.os.Handler
import android.os.Message
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatSeekBar
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.Observer
import com.bumptech.glide.request.RequestOptions
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.AddressbookConstract
import com.joinutech.addressbook.databinding.ActivityDisbandBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.addressbook.viewModel.ChangeCreatorViewModel
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.VerifyImageBean
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import javax.inject.Inject
import javax.inject.Named

/**
 *<AUTHOR>
 *@date 2018/11/24
 * 解散团队
 */
class DisbandOrganActivity : MyUseBindingActivity<ActivityDisbandBinding>() {

    override val contentViewResId: Int = R.layout.activity_disband
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityDisbandBinding {
        return ActivityDisbandBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = false

    @Inject
    @field:Named(AddressbookUtil.ADDR_PRESENTER)
    lateinit var presenter: AddressbookConstract.AddressbookPresenter
    var companyBean: WorkStationBean? = null
    var phone = ""
    var time = 60
    var msg1 = 0x111
    private lateinit var viewModel: ChangeCreatorViewModel

    //    private var topUrl = ""
//    private var backUrl = ""
    private lateinit var bigIv: ImageView
    private lateinit var smallIv: ImageView
    private var refreshImage = false
    private var layoutParams: ConstraintLayout.LayoutParams? = null
    private lateinit var seekBar: AppCompatSeekBar
    private lateinit var maskIng: View
    private lateinit var verifySuccessIv: ImageView
    private lateinit var fileText: TextView
    private var dialog: AlertDialog? = null
    private var isFirstSendMsg = true
    private var mHandler = @SuppressLint("HandlerLeak")
    object : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            if (msg.what == msg1) {
                time--
                binding.codeDisband.isEnabled = false
                binding.codeDisband.text = "重新获取".plus(time).plus("S")
                binding.codeDisband.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.text_black))
                if (time > 0) {
                    sendEmptyMessageDelayed(msg1, 1000)
                } else {
                    time = 60
                    binding.codeDisband.isEnabled = true
                    binding.codeDisband.text = "重新获取"
                    binding.codeDisband.setTextColor(Color.parseColor("#5786EE"))
                    removeCallbacksAndMessages(null)
                }
            }
        }
    }

    override fun initImmersion() {
        setPageTitle("解散团队")
        showToolBarLine()
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        whiteStatusBarBlackFont()
    }

    override fun initView() {
        DaggerAddressbookComponent.builder().build().inject(this)
        companyBean = intent.getSerializableExtra("companyBean") as WorkStationBean?
        phone = UserHolder.getCurrentUser()?.mobile ?: ""
        ImageLoaderUtils.loadImage(this, binding.logoDisband, companyBean?.logo ?: "")
        binding.nameDisband.text = companyBean?.name
        binding.phoneDisband.text = "请验证团队创建者手机号:".plus(phone)
        binding.codeDisband.setOnClickListener(this)
        binding.buttonDisband.setOnClickListener(this)
        viewModel = getModel(ChangeCreatorViewModel::class.java)
    }

    override fun onNoDoubleClick(v: View) {
        super.onNoDoubleClick(v)
        when (v.id) {
            R.id.code_disband -> {//获取验证码
                if (StringUtils.isNotBlankAndEmpty(phone)) {
                    getLoadingDialog("", false)
                    if (isFirstSendMsg) {
                        viewModel.getVerifyImage(bindToLifecycle(), phone)
                        isFirstSendMsg = false
                    } else {
                        viewModel.sendSms(bindToLifecycle(), mContext, phone, 8)
                    }
                } else {
                    ToastUtil.show(mContext!!, "手机号错误，无法获取验证码")
                }
            }
            R.id.button_disband -> {//确认解散
                if (StringUtils.isNotBlankAndEmpty(binding.etDisband.text.toString())
                        && StringUtils.isNotBlankAndEmpty(phone) && StringUtils.isPhoneNumber(phone)
                ) {
                    showDisband()
                } else {
                    ToastUtil.show(mContext!!, "请输入验证码")
                }
            }
        }
    }

    override fun initLogic() {
        super.initLogic()

        getObserver()
    }

    private fun getObserver() {
        viewModel.getVerifyImageSuccessObservable.observe(this, Observer {
            dismissDialog()
            if (refreshImage) {
                resetImageVerifyIv(it, bigIv, smallIv)
                resetImageDistance(seekBar, smallIv, layoutParams)
            } else {
                showAndVerifyImage(it)
            }
        })
        viewModel.getVerifyImageErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })

        viewModel.verifyImageSuccessObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, "图片匹配成功")
            maskIng.visibility = View.VISIBLE
            verifySuccessIv.visibility = View.VISIBLE
            maskIng.postDelayed({
                dialog?.dismiss()
                toastShort("发送成功")
                mHandler.sendEmptyMessage(msg1)
            }, 1500)
        })
        viewModel.verifyImageErrorObservable.observe(this, Observer {
            dismissDialog()
            if (it == "匹配失败") {
                fileText.visibility = View.VISIBLE
                fileText.postDelayed({
                    fileText.visibility = View.GONE
                }, 1000)
            }
            ToastUtil.show(mContext!!, it)
            resetImageDistance(seekBar, smallIv, layoutParams)
        })

        viewModel.sendSmsSuccessObservable.observe(this, Observer {
            dismissDialog()
            toastShort("发送成功")
            mHandler.sendEmptyMessage(msg1)
        })
        viewModel.sendSmsErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun showAndVerifyImage(it: VerifyImageBean) {
        val view = View.inflate(mContext!!, com.joinutech.ddbeslibrary.R.layout.dialog_image_verify, null)
        dialog = BottomDialogUtil.showBottomDialog(mContext!!, view, Gravity.CENTER)
        dialog?.setCanceledOnTouchOutside(false)
        bigIv = view.findViewById(com.joinutech.ddbeslibrary.R.id.bigIv)
        smallIv = view.findViewById(com.joinutech.ddbeslibrary.R.id.smallIv)
        seekBar = view.findViewById(com.joinutech.ddbeslibrary.R.id.seekBar)
        fileText = view.findViewById(com.joinutech.ddbeslibrary.R.id.fileText)
        maskIng = view.findViewById(com.joinutech.ddbeslibrary.R.id.maskIng)
        verifySuccessIv = view.findViewById(com.joinutech.ddbeslibrary.R.id.verifySuccessIv)
        val refresh = view.findViewById<ImageView>(com.joinutech.ddbeslibrary.R.id.refresh)
        refresh.setOnClickListener(object : OnNoDoubleClickListener {
            override fun onNoDoubleClick(v: View) {
                //点击刷新按钮，启动动画
                v.animate().rotationBy(360f).setDuration(500)
                        .setInterpolator(AccelerateDecelerateInterpolator())
                        .setListener(object : Animator.AnimatorListener {
                            override fun onAnimationStart(animation: Animator) {}
                            override fun onAnimationEnd(animation: Animator) {
                                viewModel.getVerifyImage(bindToLifecycle(), phone)
                                refreshImage = true
                            }

                            override fun onAnimationCancel(animation: Animator) {}
                            override fun onAnimationRepeat(animation: Animator) {}
                        })
            }

        })
        resetImageVerifyIv(it, bigIv, smallIv)

        seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                layoutParams = smallIv.layoutParams as ConstraintLayout.LayoutParams
                val width: Int = bigIv.width
                val smallWidth: Int = smallIv.width
                val differenceValue = progress * 0.01 * width
                val value: Int = if (differenceValue > width - smallWidth) {
                    width - smallWidth
                } else {
                    differenceValue.toInt()
                }
                layoutParams?.leftMargin = value
                smallIv.layoutParams = layoutParams
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {

            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val progress = seekBar!!.progress
                Log.e("uploadProgressValue", progress.toString())
                getLoadingDialog("获取图片验证码", false)
                val width: Int = bigIv.width
                val smallWidth: Int = smallIv.width
                val differenceValue = progress * 0.01 * width
                val scale: Double = if (differenceValue > width - smallWidth) {
                    (width - smallWidth) * 1.00 / width
                } else {
                    differenceValue / width
                }
                val newId = if (StringUtils.isNotBlankAndEmpty(it.backUrl)) {
                    val dotIndex = it.backUrl.lastIndexOf(".")
                    val lineIndex = it.backUrl.lastIndexOf("/")
                    it.backUrl.substring(lineIndex + 1, dotIndex)
                } else ""
                val oriId = if (StringUtils.isNotBlankAndEmpty(it.topUrl)) {
                    val dotIndex = it.topUrl.lastIndexOf(".")
                    val lineIndex = it.topUrl.lastIndexOf("/")
                    it.topUrl.substring(lineIndex + 1, dotIndex)
                } else ""
                val hashMap = hashMapOf<String, Any>()
                hashMap["newId"] = newId
                hashMap["oriId"] = oriId
                hashMap["phone"] = phone
                hashMap["type"] = 8 //imageVerType 8解散团队
                hashMap["scale"] = scale
                viewModel.verifyImageWithMsg(bindToLifecycle(), hashMap)
            }
        })
    }

    private fun resetImageVerifyIv(it: VerifyImageBean, bigIv: ImageView, smallIv: ImageView): Pair<String, String> {
        if (StringUtils.isNotBlankAndEmpty(it.backUrl)) {
            val options = RequestOptions
                    .placeholderOf(com.joinutech.ddbeslibrary.R.drawable.image_placeholder_im)
                    .error(com.joinutech.ddbeslibrary.R.drawable.image_placeholder_im)
                    .centerCrop()
            ImageLoaderUtils.showImgWithOption(mContext!!, it.backUrl, bigIv, options)
        }
        if (StringUtils.isNotBlankAndEmpty(it.topUrl)) {
            ImageLoaderUtils.loadImage(this, smallIv, it.topUrl)
        }
        return Pair(it.backUrl, it.topUrl)
    }

    private fun resetImageDistance(seekBar: AppCompatSeekBar, smallIv: ImageView,
                                   layoutParams: ConstraintLayout.LayoutParams?) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            seekBar.setProgress(0, true)
        } else {
            seekBar.progress = 0
        }
        var layoutParams1 = layoutParams
        if (layoutParams1 == null) {
            layoutParams1 = smallIv.layoutParams as ConstraintLayout.LayoutParams
        }
        layoutParams1.leftMargin = 0
        smallIv.layoutParams = layoutParams1
    }

    /**团队解散*/
    private fun showDisband() {
        val view = View.inflate(mContext, R.layout.dialog_disband, null)
        val dialog = BottomDialogUtil.showBottomDialog(mContext!!, view, Gravity.CENTER)
        val confirm = view.findViewById<TextView>(R.id.confirm_disband)
        val cancel = view.findViewById<TextView>(R.id.cancle_disband)
        confirm.setOnClickListener {
            dialog.dismiss()
            binding.etDisband.text?.let {
                disHandOrg(companyBean!!.companyId, phone, it.toString())
            }
        }
        cancel.setOnClickListener {
            dialog.dismiss()
        }
    }

    private fun disHandOrg(companyId: String, phone: String, code: String) {
        presenter.disbandCompany(bindToLifecycle(), accessToken, companyId, phone, code,
                onSuccess = {
                    // 团队解散后更新团队列表，团队列表刷新后会触发通讯录页面刷新，这里不需要再单独调用
                    val allCompanies = CompanyHolder.getAllNormalOrg()
                    val org1 = allCompanies.find { it.companyId == companyBean!!.companyId }
                    if (org1 != null) {
                        allCompanies.remove(org1)
                        CompanyHolder.saveAllNormalOrg(allCompanies)
                    } else {
                        val allCooper = CompanyHolder.getCooperationOrg()
                        val org2 = allCooper.find { it.companyId == companyBean!!.companyId }
                        if (org2 != null) {
                            allCooper.remove(org2)
                            CompanyHolder.saveCooperationOrg(allCooper)
                        }
                    }
                    // TODO: 2021/1/5 15:29 解散团队后，会话列表更新逻辑
                    //  1、删除会话表中团队 Event_DISSOLVED_ORG
                    //  2、团队群组聊天项 系统消息中 Event_REFRESH_MESSAGE_LIST_GROUP_MSG_DISMISS
                    //  msgService 中 org_delete 节点处理以下两点
                    //  --3、清理本地关联团队的审批消息
                    //  --4、查询当前还存在团队中最后一条审批相关消息作为审批聚合消息中显示内容，如果没有则删除审批聚合消息项
                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.Event_DISSOLVED_ORG, companyBean!!.companyId))
//                    EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ORGANLIST, "-1"))
                    setResult(0x13)
                    finish()
                },
                onError = {
                    ToastUtil.show(mContext!!, it)
                })
    }

    override fun onDestroy() {
        super.onDestroy()
        mHandler.removeCallbacksAndMessages(null)
        if (dialog != null) {
            dialog?.dismiss()
            dialog = null
        }
    }

}