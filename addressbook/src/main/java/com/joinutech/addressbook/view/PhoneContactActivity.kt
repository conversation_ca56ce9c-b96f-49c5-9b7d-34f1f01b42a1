package com.joinutech.addressbook.view

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.ContactsAdapter
import com.joinutech.addressbook.constract.PhoneContactConstract
import com.joinutech.addressbook.databinding.ActivityPhonecontactBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.ContactModel
import com.joinutech.ddbeslibrary.bean.FriendSimpleBean
import com.joinutech.ddbeslibrary.bean.LetterComparator
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.widget.wavesidebar.PinnedHeaderDecoration
import java.util.*
import javax.inject.Inject
import javax.inject.Named
import kotlin.collections.ArrayList

/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName: PhoneContactActivity
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/12/3 15:27
 * @Desc: //TODO 通讯录添加好友 添加外部联系人
 */
@Route(path = RouteOrg.phoneContactActivity)
class PhoneContactActivity : MyUseBindingActivity<ActivityPhonecontactBinding>() {

    override val contentViewResId: Int = R.layout.activity_phonecontact
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityPhonecontactBinding {
        return ActivityPhonecontactBinding.inflate(layoutInflater)
    }

    private lateinit var mShowModels: List<ContactModel>
    private lateinit var mAdapter: ContactsAdapter
    private var oldPosition = 0
    private var isAddExternalContact = false

    @Inject
    @field:Named(AddressbookUtil.PHONECONTACTLIST_PRESENTER)
    lateinit var presenter: PhoneContactConstract.PhoneContactPresenter

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        setPageTitle("手机通讯录")
        if (intent != null) {
            isAddExternalContact = intent.getBooleanExtra(
                    "isAddExternalContact", false)
        }
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        DaggerAddressbookComponent.builder().build().inject(this)
        mShowModels = arrayListOf()
         binding.mainRecycler.layoutManager = LinearLayoutManager(this)
        val decoration = PinnedHeaderDecoration()
        decoration.registerTypePinnedHeader(1) { _, _ -> true }
        binding.mainRecycler.addItemDecoration(decoration)
         binding.layoutEmptyLayout.hide()
    }

    override fun initLogic() {
        super.initLogic()

        initViewShow()
        getPermission()
    }

    private fun initViewShow() {
        mShowModels = ArrayList()
        mAdapter = ContactsAdapter(mShowModels, mContext!!)
        // RecyclerView设置相关
        binding.mainRecycler.adapter = mAdapter
        // 侧边设置相关

        binding.mainSideBar.setmTextHighColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color1E87F0))
        binding.mainSideBar.setOnSelectIndexItemListener { letter ->
            // TODO: 2020/7/23 8:59 优化后需要验证
            showLog("当前选中index 为 $letter")
            if (mShowModels.isNotEmpty()) {
                val first = mShowModels.find { it.index == letter }
                if (first != null) {
                    first.select = true
                    mShowModels[oldPosition].select = false
                    oldPosition = mShowModels.indexOf(first)
                    mAdapter.notifyDataSetChanged()
                    showLog("切换联系人索引后 更新 联系人列表----")
                    (binding.mainRecycler.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(oldPosition, 0)
                }
            }
        }
//        binding.mainRecycler.addOnScrollListener(object : RecyclerView.OnScrollListener() {
//            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
////                val layoutManager = recyclerView.layoutManager
////                //判断是当前layoutManager是否为LinearLayoutManager
////                // 只有LinearLayoutManager才有查找第一个和最后一个可见view位置的方法
////                if (layoutManager is LinearLayoutManager) {
////                    //获取第一个可见view的位置
////                    showLog("滑动联系人列表----")
////                    if (!mShowModels.isNullOrEmpty()) {
////                        val firstPosition = layoutManager.findFirstVisibleItemPosition()
////                        val firstItem = mShowModels[firstPosition]
////                        if (firstPosition > 0) {
////                            val lastItem = mShowModels[firstPosition - 1]
////                            if (lastItem.index != firstItem.index) {
////                                mShowModels.forEach { it.select = false }
////                                firstItem.select = true
////                                binding.mainSideBar.setIndexPosition(
////                                        binding.mainSideBar.indexData.indexOf(firstItem.index)
////                                )
////                                oldPosition = firstPosition
////
////                            }
////                        } else {
////                            mShowModels.forEach { it.select = false }
////                            firstItem.select = true
////                            binding.mainSideBar.setIndexPosition(
////                                    binding.mainSideBar.indexData.indexOf(firstItem.index)
////                            )
////                            oldPosition = firstPosition
////                        }
////                    }
////                    mAdapter.notifyDataSetChanged()
////                } else {
////                    super.onScrollStateChanged(recyclerView, newState)
////                }
//            }
//        })
    }

    private fun getPermission() {
        val perms = arrayOf(Manifest.permission.READ_CONTACTS)
        val tip = "需要你同意联系人功能，才能继续查看内容"
        PermissionUtils.requestPermissionActivity(this, perms,
                "获取手机通讯录需要你开启权限", {
            binding.noPermissionLayout.visibility = View.GONE
            doThings()
        }, {
            toastShort("获取联系人列表失败,需要你开启权限")
        }, preTips = tip)
    }

    private fun doThings() {
        mShowModels = presenter.getPhoneContactList(mContext!!)
        binding.friendListLayout.visibility = View.GONE
        binding.layoutEmptyLayout.show()
        if (mShowModels.isNotEmpty()) {
            //把手机号传到服务器返回响应状态
            val phoneList = ArrayList<String>()
            for (item in mShowModels) {
                val num = item.phoneNum
                if (StringUtils.isNotBlankAndEmpty(num)) {
                    if (num.contains("+86")) {
                        num.replace("+86", "")
                    }
                    if (num.contains("-")) {
                        num.replace("-", "")
                    }
                }
                phoneList.add(num)
            }
            if (isAddExternalContact) {
                mAdapter.setType("addExternalContact")
                mAdapter.setOnClickListener {
                    val num = it.phoneNum
                    if (num.contains("+86")) {
                        num.replace("+86", "")
                    }
                    if (num.contains("-")) {
                        num.replace("-", "")
                    }
                    val bean = FriendSimpleBean(it.name, num)
                    intent.putExtra("bean", bean)
                    setResult(Activity.RESULT_OK, intent)
                    finish()
                }
                binding.friendListLayout.visibility = View.VISIBLE
                binding.layoutEmptyLayout.visibility = View.GONE
                oldPosition = 0
                mShowModels[oldPosition].select = true
                Collections.sort(mShowModels, LetterComparator())
                mAdapter.setDataList(mShowModels)
            } else {
                mAdapter.setType("phoneContact")
                mAdapter.setOnClickListener { contact ->
                    when (contact.status) {
                        1 -> {
                            //进入用户详情
                            val intent = Intent(this, FriendInfoActivity::class.java)
                            intent.putExtra("userId", contact.userId)
                            intent.putExtra("type", "")
                            intent.putExtra("enterType", 1) //搜索好友标记 ？
                            startActivity(intent)
                        }
                        2->{
                            //进入用户详情
                            val intent = Intent(this, FriendInfoActivity::class.java)
                            intent.putExtra("type", "friend")
                            intent.putExtra("userId", contact.userId)
                            startActivity(intent)
                        }
                        3->{
                            //进入用户详情
                            val intent = Intent(this, FriendInfoActivity::class.java)
                            intent.putExtra("userId", contact.userId)
                            intent.putExtra("type", "friend")
                            startActivity(intent)
                        }
                    }
                }
                verifyPhoneContactRegister(phoneList)
            }
        }
    }

    private fun verifyPhoneContactRegister(phoneList: ArrayList<String>) {
        getLoadingDialog("获取手机联系人...", false)
        presenter.getContactStatusList(bindToLifecycle(), accessToken!!, phoneList,
                onSuccess = {
                    val dealReturnPhoneContactData = presenter
                            .dealReturnPhoneContactData(it, mShowModels, userId)
                    dismissDialog()
                    binding.friendListLayout.visibility = View.VISIBLE
                    binding.layoutEmptyLayout.visibility = View.GONE
                    if (!dealReturnPhoneContactData.isNullOrEmpty()) {
                        oldPosition = 0
                        dealReturnPhoneContactData[oldPosition].select = true
                        Collections.sort(dealReturnPhoneContactData, LetterComparator())
                    }
                    mAdapter.setDataList(dealReturnPhoneContactData)
                },
                onError = {
                    dismissDialog()
                    ToastUtil.show(mContext!!, "获取联系人失败")
                })
    }

    override fun showToolBar(): Boolean {
        return true
    }


    override fun openArouterReceive(): Boolean {
        return true
    }

    override fun onDestroy() {
        super.onDestroy()
        (mShowModels as ArrayList).clear()
    }
}