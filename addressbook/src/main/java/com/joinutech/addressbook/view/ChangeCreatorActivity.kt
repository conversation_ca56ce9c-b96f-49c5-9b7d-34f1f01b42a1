package com.joinutech.addressbook.view

import android.animation.Animator
import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Build
import android.os.Handler
import android.os.Message
import android.text.style.ForegroundColorSpan
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatSeekBar
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.Observer
import com.alibaba.android.arouter.facade.annotation.Route
import com.bumptech.glide.request.RequestOptions
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.AddressbookConstract
import com.joinutech.addressbook.databinding.ActivityChangeCreatorLayoutBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.addressbook.viewModel.ChangeCreatorViewModel
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.joinutech.ddbeslibrary.bean.VerifyImageBean
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import javax.inject.Inject
import javax.inject.Named

/**
 * @className: ChangeCreatorActivity
 * @desc: 团队切换创建者
 * @author: zyy
 * @date: 2019/9/24 10:09
 * @company: joinUTech
 * @leader: ke
 */
@Route(path = RouteOrg.ORG_CHANGE_CREATOR)
class ChangeCreatorActivity : MyUseBindingActivity<ActivityChangeCreatorLayoutBinding>() {

    override val contentViewResId: Int = R.layout.activity_change_creator_layout
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityChangeCreatorLayoutBinding {
        return ActivityChangeCreatorLayoutBinding.inflate(layoutInflater)
    }

    @Inject
    @field:Named(AddressbookUtil.ADDR_PRESENTER)
    lateinit var presenter: AddressbookConstract.AddressbookPresenter
    private var companyBean: WorkStationBean? = null
    private var memberBean: SearchMemberBean? = null
    var phone = ""
    var time = 60
    var msg1 = 0x111
    private lateinit var viewModel: ChangeCreatorViewModel
    private var topUrl = ""
    private var backUrl = ""
    private lateinit var bigIv: ImageView
    private lateinit var smallIv: ImageView
    private var refreshImage = false
    private var layoutParams: ConstraintLayout.LayoutParams? = null
    private lateinit var seekBar: AppCompatSeekBar
    private lateinit var maskIng: View
    private lateinit var verifySuccessIv: ImageView
    private lateinit var fileText: TextView
    private var dialog: AlertDialog? = null
    private var isFirstSendMsg = true

    @SuppressLint("HandlerLeak")
    private var mHandler = object : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            if (msg.what == msg1) {
                time--
                binding.codeDisband.isEnabled = false
                binding.codeDisband.text = "重新获取".plus(time).plus("S")
                binding.codeDisband.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.text_black))
                if (time > 0) {
                    sendEmptyMessageDelayed(msg1, 1000)
                } else {
                    time = 60
                    binding.codeDisband.isEnabled = true
                    binding.codeDisband.text = "重新获取"
                    binding.codeDisband.setTextColor(Color.parseColor("#5786EE"))
                    removeCallbacksAndMessages(null)
                }
            }
        }
    }

    override fun initImmersion() {
        setPageTitle("转移团队创建者")
        showToolBarLine()
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        whiteStatusBarBlackFont()
        mImmersionBar?.apply {
            keyboardEnable(true).keyboardMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        }
    }

    override fun initView() {
        DaggerAddressbookComponent.builder().build().inject(this)
        if (intent.hasExtra("companyBean") && intent.getSerializableExtra("companyBean") != null) {
            companyBean = intent.getSerializableExtra("companyBean") as WorkStationBean
        }
        if (intent.hasExtra("member") && intent.getSerializableExtra("member") != null) {
            memberBean = intent.getSerializableExtra("member") as SearchMemberBean
        }

        if (memberBean != null) {
            binding.tvChangeUserTitle.text = Spanny("你确认要转移该团队的创建者身份给 ")
                    .append(memberBean!!.name, ForegroundColorSpan(
                            CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color107EEB)))
                    .append(" 吗？")
        }

        phone = UserHolder.getCurrentUser()?.mobile ?: ""
        binding.tvChangeUserMobile.text = "请验证团队创建者手机号:".plus(phone)
        companyBean?.let {
            ImageLoaderUtils.loadImage(mContext!!, binding.ivOrgIcon, it.logo)
            binding.tvChangeOrgName.text = it.name
        }
        if (companyBean != null && memberBean != null) {
            binding.codeDisband.setOnClickListener(this)
            binding.buttonDisband.setOnClickListener(this)
        } else {
            binding.codeDisband.setOnClickListener(null)
            binding.buttonDisband.setOnClickListener(null)
        }
        viewModel = getModel(ChangeCreatorViewModel::class.java)
    }

    override fun onNoDoubleClick(v: View) {
        super.onNoDoubleClick(v)
        when (v.id) {
            R.id.code_disband -> {//获取验证码
                if (StringUtils.isNotBlankAndEmpty(phone)) {
                    getLoadingDialog("", false)
                    if (isFirstSendMsg) {
                        viewModel.getVerifyImage(bindToLifecycle(), phone)
                        isFirstSendMsg = false
                    } else {
                        viewModel.sendSms(bindToLifecycle(), mContext, phone, 12)
                    }
                } else {
                    ToastUtil.show(mContext!!, "手机号错误，无法获取验证码")
                }
            }
            R.id.button_disband -> {//确认解散
                if (StringUtils.isNotBlankAndEmpty(binding.etDisband.text.toString())
                        && StringUtils.isNotBlankAndEmpty(phone) && StringUtils.isPhoneNumber(phone)
                ) {
                    changeOrgCreator()
                } else {
                    ToastUtil.show(mContext!!, "请输入验证码")
                }
            }
        }
    }

    private fun changeOrgCreator() {
        val map = hashMapOf<String, String>()
        map["companyId"] = companyBean?.companyId!!
        map["userId"] = memberBean?.userId!!
        map["phone"] = phone
        map["code"] = binding.etDisband.text.toString()
        presenter.changeOrgCreator(
                bindToLifecycle(), map,
                onSuccess = {
                    toastShort("转让成功")
                    EventBusUtils.sendEvent(EventBusEvent("changeOrgCreatorSuccess", null))
                    finish()
                },
                onError = {
                    toastShort(it)
                }
        )
    }

    private fun getObserver() {
        viewModel.getVerifyImageSuccessObservable.observe(this, Observer {
            dismissDialog()
            if (refreshImage) {
                val pair = showImageVerifyIv(it, backUrl, bigIv, topUrl, smallIv)
                backUrl = pair.first
                topUrl = pair.second
                resetImageDistance(seekBar, smallIv, layoutParams)
            } else {
                showAndVerifyImage(it)
            }
        })
        viewModel.getVerifyImageErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
        viewModel.verifyImageSuccessObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, "图片匹配成功")
            maskIng.visibility = View.VISIBLE
            verifySuccessIv.visibility = View.VISIBLE
            maskIng.postDelayed({
                dialog?.dismiss()
                toastShort("发送成功")
                mHandler.sendEmptyMessage(msg1)
            }, 1500)
        })
        viewModel.verifyImageErrorObservable.observe(this, Observer {
            dismissDialog()
            if (it == "匹配失败") {
                fileText.visibility = View.VISIBLE
                fileText.postDelayed({
                    fileText.visibility = View.GONE
                }, 1000)
            }
            ToastUtil.show(mContext!!, it)
            resetImageDistance(seekBar, smallIv, layoutParams)
        })
        viewModel.sendSmsSuccessObservable.observe(this, Observer {
            dismissDialog()
            toastShort("发送成功")
            mHandler.sendEmptyMessage(msg1)
        })
        viewModel.sendSmsErrorObservable.observe(this, Observer {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun showAndVerifyImage(it: VerifyImageBean) {
        val view = View.inflate(mContext!!, com.joinutech.ddbeslibrary.R.layout.dialog_image_verify, null)
        dialog = BottomDialogUtil.showBottomDialog(
                mContext!!, view, Gravity.CENTER)
        dialog?.setCanceledOnTouchOutside(false)
        bigIv = view.findViewById(com.joinutech.ddbeslibrary.R.id.bigIv)
        smallIv = view.findViewById(com.joinutech.ddbeslibrary.R.id.smallIv)
        seekBar = view.findViewById(com.joinutech.ddbeslibrary.R.id.seekBar)
        fileText = view.findViewById(com.joinutech.ddbeslibrary.R.id.fileText)
        maskIng = view.findViewById(com.joinutech.ddbeslibrary.R.id.maskIng)
        verifySuccessIv = view.findViewById(com.joinutech.ddbeslibrary.R.id.verifySuccessIv)
        val refresh = view.findViewById<ImageView>(com.joinutech.ddbeslibrary.R.id.refresh)
        refresh.setOnClickListener(object : OnNoDoubleClickListener {
            override fun onNoDoubleClick(v: View) {
                //点击刷新按钮，启动动画
                v.animate().rotationBy(360f).setDuration(500)
                        .setInterpolator(AccelerateDecelerateInterpolator())
                        .setListener(object : Animator.AnimatorListener {
                            override fun onAnimationStart(animation: Animator) {}
                            override fun onAnimationEnd(animation: Animator) {
                                viewModel.getVerifyImage(bindToLifecycle(), phone)
                                refreshImage = true
                            }

                            override fun onAnimationCancel(animation: Animator) {}
                            override fun onAnimationRepeat(animation: Animator) {}
                        })
            }

        })
        val pair = showImageVerifyIv(it, backUrl, bigIv, topUrl, smallIv)
        backUrl = pair.first
        topUrl = pair.second

        seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                layoutParams = smallIv.layoutParams as ConstraintLayout.LayoutParams
                val width: Int = bigIv.width
                val smallWidth: Int = smallIv.width
                val differenceValue = progress * 0.01 * width
                val value: Int = if (differenceValue > width - smallWidth) {
                    width - smallWidth
                } else {
                    differenceValue.toInt()
                }
                layoutParams?.leftMargin = value
                smallIv.layoutParams = layoutParams
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {

            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val progress = seekBar!!.progress
                showLog("uploadProgressValue $progress")
                getLoadingDialog("获取图片验证码", false)
                val width: Int = bigIv.width
                val smallWidth: Int = smallIv.width
                val differenceValue = progress * 0.01 * width
                val value: Double = if (differenceValue > width - smallWidth) {
                    (width - smallWidth) * 1.00 / width
                } else {
                    differenceValue / width
                }
                val hashMap = hashMapOf<String, Any>()
                val newId = if (StringUtils.isNotBlankAndEmpty(backUrl)) {
                    val dotIndex = backUrl.lastIndexOf(".")
                    val lineIndex = backUrl.lastIndexOf("/")
                    backUrl.substring(lineIndex + 1, dotIndex)
                } else {
                    ""
                }
                val oriId = if (StringUtils.isNotBlankAndEmpty(topUrl)) {
                    val dotIndex = topUrl.lastIndexOf(".")
                    val lineIndex = topUrl.lastIndexOf("/")
                    topUrl.substring(lineIndex + 1, dotIndex)
                } else {
                    ""
                }
                hashMap["newId"] = newId
                hashMap["oriId"] = oriId
                hashMap["phone"] = phone
                hashMap["type"] = 12 //imageVerType 12转让团队
                hashMap["scale"] = value
                viewModel.verifyImageWithMsg(bindToLifecycle(), hashMap)
            }
        })
    }

    private fun showImageVerifyIv(it: VerifyImageBean, backUrl: String, bigIv: ImageView,
                                  topUrl: String, smallIv: ImageView): Pair<String, String> {
        var backUrl1 = backUrl
        var topUrl1 = topUrl
        if (StringUtils.isNotBlankAndEmpty(it.backUrl)) {
            backUrl1 = it.backUrl
            val options = RequestOptions
                    .placeholderOf(com.joinutech.ddbeslibrary.R.drawable.image_placeholder_im)
                    .error(com.joinutech.ddbeslibrary.R.drawable.image_placeholder_im)
                    .centerCrop()
            ImageLoaderUtils.showImgWithOption(mContext!!, backUrl1, bigIv, options)
        }
        if (StringUtils.isNotBlankAndEmpty(it.topUrl)) {
            topUrl1 = it.topUrl
            ImageLoaderUtils.loadImage(this, smallIv, topUrl)
        }
        return Pair(backUrl1, topUrl1)
    }

    private fun resetImageDistance(seekBar: AppCompatSeekBar, smallIv: ImageView,
                                   layoutParams: ConstraintLayout.LayoutParams?) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            seekBar.setProgress(0, true)
        } else seekBar.progress = 0
        var layoutParams1 = layoutParams
        if (layoutParams1 == null)
            layoutParams1 = smallIv.layoutParams as ConstraintLayout.LayoutParams
        layoutParams1.leftMargin = 0
        smallIv.layoutParams = layoutParams1
    }


    override fun initLogic() {
        super.initLogic()

        getObserver()
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return false
    }

    override fun onDestroy() {
        super.onDestroy()
        mHandler.removeCallbacksAndMessages(null)
        if (dialog != null) {
            dialog?.dismiss()
            dialog = null
        }
    }

}