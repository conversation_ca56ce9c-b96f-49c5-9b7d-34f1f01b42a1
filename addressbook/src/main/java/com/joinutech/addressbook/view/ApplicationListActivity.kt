package com.joinutech.addressbook.view

import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.launcher.ARouter
import com.ddbes.library.im.netutil.ImNetUtil
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.ApplicationListAdapter
import com.joinutech.addressbook.adapter.FriendUndoListAdapter
import com.joinutech.addressbook.constract.ApplicationListConstract
import com.joinutech.addressbook.databinding.ActivityApplicationlistBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.common.adapter.BaseAdapter
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.CompanyUndoBean
import com.joinutech.ddbeslibrary.bean.CompanyUndoListBean
import com.joinutech.ddbeslibrary.bean.FriendUndoListBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.utils.BATCH_PROCESS_APPLICATION
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.CustomHeader
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import javax.inject.Inject
import javax.inject.Named

/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName: ApplicationListActivity
 * @Desc: 好友请求更多页面，超过三个请求时显示更多，跳转到该列表
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/4/15 14:22
 */
class ApplicationListActivity : MyUseBindingActivity<ActivityApplicationlistBinding>() {

    override val contentViewResId: Int = R.layout.activity_applicationlist
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityApplicationlistBinding {
        return ActivityApplicationlistBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = true

    var type: String = ""
    var page: Int = 1
    var companyId = "0"

    @Inject
    @field:Named(AddressbookUtil.APPLICATIONLIST_PRESENTER)
    lateinit var presenter: ApplicationListConstract.ApplicationListPresenter
    private var dataListCompany: ArrayList<CompanyUndoListBean> = arrayListOf()
    private var companyAdapter: ApplicationListAdapter? = null

    private var dataListFriend: ArrayList<FriendUndoListBean> = arrayListOf()
    private var friendAdapter: FriendUndoListAdapter? = null
    private var isRefresh = false
    private var companyName = ""

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back2)
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("type"))) {
                type = intent.getStringExtra("type")!!
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                companyId = intent.getStringExtra("companyId")!!
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyName"))) {
                companyName = intent.getStringExtra("companyName")!!
            }
        }
    }

    override fun initView() {
        showToolBarLine()
        whiteStatusBarBlackFont()
        DaggerAddressbookComponent.builder().build().inject(this)
        companyAdapter = ApplicationListAdapter(mContext!!, dataListCompany)
        friendAdapter = FriendUndoListAdapter(mContext!!, dataListFriend)

        companyAdapter!!.setOnItemClickListener(object : BaseAdapter.OnItemClickListener {
            override fun onItemClick(view: View, position: Int) {
                val intent = Intent(mContext!!, ApplicationDetailsActivity::class.java)
                intent.putExtra("undobean", companyAdapter!!.mList!![position])
                intent.putExtra("status", companyAdapter!!.mList!![position].status)
                startActivity(intent)
            }

        })
        //tcp好友申请列表的点击事件,跳转到申请详情
        friendAdapter!!.setOnItemClickListener(object : BaseAdapter.OnItemClickListener {
            override fun onItemClick(view: View, position: Int) {
                val bean = friendAdapter!!.mList!![position]
                ARouter.getInstance()
                        .build(RouteOrg.applicationDetailActivity)
                        .withString("paramId", bean.id)
                        .withString("type", "dealAddFriend")
                        .navigation()
            }
        })
        binding.rvOrgApplyList.layoutManager = LinearLayoutManager(mContext!!)


        when (type) {
            "friend" -> {
                setPageTitle("好友申请")
                binding.rvOrgApplyList.adapter = friendAdapter
            }
            "org" -> {
                setPageTitle("团队加入申请")
                setRightTitleColor(
                        CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.colorFFAAAAAA),
                        "批量处理", View.OnClickListener { })
                binding.rvOrgApplyList.adapter = companyAdapter
            }
            else -> {
                setPageTitle("待处理申请")
            }
        }
    }

    override fun initLogic() {
        super.initLogic()

        /*  friendAdapter!!.setOnItemClickListener(object : BaseAdapter.OnItemClickListener {
              override fun onItemClick(view: View, position: Int) {
                  val intent = Intent(mContext!!, ApplicationDetailsActivity::class.java)
                  val bean = friendAdapter!!.mList!![position]
                  val companyBean = CompanyUndoListBean(bean.createTime,
                          bean.avatar, bean.name, bean.id, bean.birthday, "",
                          bean.status, bean.gender, "", bean.message,
                          bean.applyUserId, userName!!)
                  intent.putExtra("undobean", companyBean)
                  intent.putExtra("status", friendAdapter!!.mList!![position].status)
                  intent.putExtra("type", "dealAddFriend")
                  startActivity(intent)
              }
          })*/
        loadDataList()
        if (type != "friend") {
            binding.srlLayout.apply {
                setEnableRefresh(true)
                setEnableLoadMore(true)
                setRefreshHeader(CustomHeader(mContext))

                setOnRefreshListener {
                    page = 1
                    finishRefresh(1500)
                    isRefresh = true
                    loadDataList()
                }
                setOnLoadMoreListener {
                    finishLoadMore(1500)
                    loadDataList()
                }
            }

        }
    }

    private fun updateUI(showListView: Boolean = false) {
        if (showListView) {
            binding.srlLayout.visibility = View.VISIBLE
            binding.rvOrgApplyList.visibility = View.VISIBLE
            binding.layoutEmptyLayout.visibility = View.GONE
        } else {
            binding.srlLayout.visibility = View.GONE
            binding.rvOrgApplyList.visibility = View.GONE
            binding.layoutEmptyLayout.visibility = View.VISIBLE
        }
    }

    private fun loadDataList() {
        if (!isRefresh) getLoadingDialog("", true)
        if (type == "friend") {//tcp获取好友申请列表
            ImNetUtil.getFriendApplyList(bindToLifecycle(), accessToken,
                    onSuccess = { applyList ->
                        dismissDialog()
                        isRefresh = false
                        if (applyList.isNotEmpty()) {
                            updateUI(true)
                            val list: List<FriendUndoListBean> = applyList.map { it.toFriendUndoListBean() }
                            if (page == 1) {
                                dataListFriend.clear()
//                                    dealDataNum(dataListFriend)
                            }
                            dataListFriend.addAll(list)
                            dealCompanyDataNum(list.size)
                            friendAdapter!!.notifyDataSetChanged()
                        } else {
                            updateUI()
                        }
                    },
                    onFailer = {
                        dismissDialog()
                        isRefresh = false
                        updateUI()
                        ToastUtil.show(mContext!!, it)
                    })
        } else if (type == "org") {
            presenter.getCompanyUndoList(bindToLifecycle<Result<CompanyUndoBean>>(),
                    accessToken, page, 20, companyId,
                    onSuccess = {
                        dismissDialog()
                        isRefresh = false
                        if (it.data.isNotEmpty()) {
                            setRightTitleColor(
                                    CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.text_black),
                                    "批量处理", this)
                            updateUI(true)
                            if (page == 1) {
                                dataListCompany.clear()
                            }

                            dealCompanyDataNum(it.data.size)
                            dataListCompany.addAll(it.data)
                            companyAdapter!!.notifyDataSetChanged()

                        } else {
                            setRightTitleColor(
                                    CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.colorFFAAAAAA),
                                    "批量处理", View.OnClickListener { })
                            updateUI()
                        }
                    },
                    onError = {
                        dismissDialog()
                        isRefresh = false
                        updateUI()
                        ToastUtil.show(mContext!!, it)
                    })
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun getDataList(eventBusEvent: EventBusEvent<String>) {
        if (eventBusEvent.code == EventBusAction.REFRESH_APPLIY_LIST) {
            var refreshType = ""
            if (eventBusEvent.data != null) {
                if (StringUtils.isNotBlankAndEmpty(eventBusEvent.data)) {
                    refreshType = eventBusEvent.data!!
                }
            }

            if (refreshType != type) {// 与当前数据不是一种类型时，不刷新
                return
            }
            loadDataList()
        }
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            tv_rightTitle -> {
                val intent = Intent(mContext!!, BatchProcessUndoMsgActivity::class.java)
                intent.putExtra("batchList", dataListCompany as ArrayList<CompanyUndoListBean>)
                intent.putExtra("companyId", companyId)
                intent.putExtra("companyName", companyName)
                startActivityForResult(intent, BATCH_PROCESS_APPLICATION)
            }
        }
    }

    private fun dealCompanyDataNum(currentSize: Int) {
        if (currentSize < 20) {
            binding.srlLayout.setEnableLoadMore(false)
        } else {
            binding.srlLayout.setEnableLoadMore(true)
            page++
        }
    }

}