package com.joinutech.addressbook.view

import android.content.Intent
import android.view.LayoutInflater
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.addressbook.R
import com.joinutech.common.adapter.MyAdapter2
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.base.SimpleBaseActivity
import com.joinutech.ddbeslibrary.bean.OrgImportPeopleBean
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.databinding.CommonListLayoutBinding
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.PLAN_JOIN_MEMBER
import com.joinutech.ddbeslibrary.utils.RouteOrg
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * @Group: Copyright (C),2017- 2020,JoinuTech
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName: SelectShareMemberActivity
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2021/6/30 17:03
 * @Desc: //TODO 选择好友 公司同事分享页面
 */
@Route(path = RouteOrg.selectMemberShareActivity)
class SelectShareMemberActivity : SimpleBaseActivity<CommonListLayoutBinding>() {

    override val contentViewResId: Int = com.joinutech.ddbeslibrary.R.layout.common_list_layout
    override fun inflateViewBinding(layoutInflater: LayoutInflater): CommonListLayoutBinding {
        return CommonListLayoutBinding.inflate(layoutInflater)
    }

    val data = arrayListOf<WorkStationBean>()

    private lateinit var rvList: RecyclerView
    private lateinit var pageFlag: String

    override fun initView() {
        setPageTitle("选择联系人")
        pageFlag = intent.getStringExtra("pageFlag") ?: ""

        findViewById<PageEmptyView>(R.id.layout_empty_layout).hide()
        rvList = findViewById<RecyclerView>(R.id.rv_list)
        data.add(WorkStationBean("0", name = "担当好友"))
        data.addAll(CompanyHolder.getAllNormalOrg())
        rvList.layoutManager = LinearLayoutManager(this)
        rvList.adapter = MyAdapter2<WorkStationBean>(this, R.layout.item_normal_list, data,
                onBindItem = { position, bean, holder ->
                    if (bean.companyId == "0") {
                        holder.hideView(R.id.tv_sticky_title)
                                .setText(R.id.tv_company_name, bean.name)
                                .setImage(R.id.iv_company_logo, R.drawable.icon_address_friend)
                    } else {
                        if (position > 0 && data[position - 1].companyId == "0") {
                            holder.showView(R.id.tv_sticky_title).setText(R.id.tv_sticky_title, "组织架构")
                                    .setText(R.id.tv_company_name, bean.name)
                                    .setImage(R.id.iv_company_logo, bean.logo)
                        } else {
                            holder.hideView(R.id.tv_sticky_title)
                                    .setText(R.id.tv_company_name, bean.name)
                                    .setImage(R.id.iv_company_logo, bean.logo)
                        }
                    }
                },
                onItemClick = { _, bean, _ ->
                    if (bean.companyId == "0") {
                        // to friend list select friend
                        val intent = Intent(this, FriendListActivity::class.java)
                        intent.putExtra("type", "shareFile")
                        intent.putExtra("pageFlag", pageFlag)
                        startActivity(intent)
                    } else {
                        // to company select member
                        ARouter.getInstance()
                                .build(RouteOrg.orgImportPersonActivity)// 选择分享给同事
                                .withString("companyId", bean.companyId)
                                .withString("companyName", bean.name)
                                .withString("deptId", "0")
                                .withString("depName", bean.name)
                                .withString("pageFlag", pageFlag)// 选中后返回数据事件标记
                                .withStringArrayList("depMember", arrayListOf(userId))
                                .withString("pageTitle", "请选择人员")
                                .withString("confirmTitle", "发送")
                                .withBoolean("isNeedEdit", false)
                                .withInt("maxSelect", 9)// 增加一次最多选择人数限制
                                .navigation(this, PLAN_JOIN_MEMBER)
                    }
                })
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSelectMemberResult(event: EventBusEvent<List<OrgImportPeopleBean>>) {
        if (!event.data.isNullOrEmpty() && event.code == pageFlag) {
//            toastShort("分享给 ${event.data?.joinToString { it.name }}")
            finish()
        }
    }

}