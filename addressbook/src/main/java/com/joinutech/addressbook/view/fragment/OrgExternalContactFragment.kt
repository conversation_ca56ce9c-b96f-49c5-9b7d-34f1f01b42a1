package com.joinutech.addressbook.view.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.ExternalContactTypeSelectAdapter
import com.joinutech.addressbook.adapter.OrgExternalContactPersonAdapter
import com.joinutech.addressbook.view.OrgExternalContactActivity
import com.joinutech.addressbook.viewModel.OrgExternalContactListViewModel
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.base.BaseFragment
import com.joinutech.ddbeslibrary.bean.ExternalContactListBean
import com.joinutech.ddbeslibrary.utils.*

/**
 * @Description: 外部联系人的Fragment类
 * @Author: 黄洁如
 * @Time: 2019/11/22 9:40
 * @packageName: com.joinutech.addressbook.view.fragment
 * @Company: JoinuTech
 */
class OrgExternalContactFragment : BaseFragment() {

    override val layoutRes: Int = R.layout.fragment_org_external_contact

    override fun openEventBus(): Boolean = false

    companion object {
        private const val EXTERNAL_CONTACT_INDEX = "contact_type"
        private const val EXTERNAL_COMPANY_ID = "company_id"
        private const val EXTERNAL_HAS_PERMISSION = "has_permission"

        fun newInstance(index: Int, companyId: String, orgPermission: Boolean): OrgExternalContactFragment {
            return OrgExternalContactFragment().apply {
                arguments = Bundle().apply {
                    putInt(EXTERNAL_CONTACT_INDEX, index)
                    putString(EXTERNAL_COMPANY_ID, companyId)
                    putBoolean(EXTERNAL_HAS_PERMISSION, orgPermission)
                }
            }
        }
    }

    private var contactType = 0
    private lateinit var personRv: RecyclerView
    private lateinit var num: TextView
    private lateinit var noPersonLayout: PageEmptyView
    private lateinit var havePersonLayout: ConstraintLayout
    private lateinit var rightSelect: ConstraintLayout
    private lateinit var leftSearch: ConstraintLayout
    private lateinit var adapter: OrgExternalContactPersonAdapter
    private lateinit var levelSelect: TextView
    private lateinit var selectLevelLayout: ConstraintLayout
    private lateinit var selectLevelTv: TextView
    private lateinit var cancelSelect: ImageView
    private var keyWord = ""
    private var companyId = ""
    private var isEdit = false
    private var levelType = ""
    private var isSelect = false
    private lateinit var selectNoPersonLayout: PageEmptyView
    private var dataList = arrayListOf<ExternalContactListBean>()
    private lateinit var viewModel: OrgExternalContactListViewModel

    override fun initView(rootView: View) {
        if (arguments != null && arguments?.getInt(EXTERNAL_CONTACT_INDEX) != null) {
            contactType = arguments?.getInt(EXTERNAL_CONTACT_INDEX) ?: 0
        }
        companyId = arguments?.getString(EXTERNAL_COMPANY_ID, "") ?: ""
        isEdit = arguments?.getBoolean(EXTERNAL_HAS_PERMISSION, false) ?: false
        personRv = rootView.findViewById(R.id.rv_list)
        num = rootView.findViewById(R.id.num)
        havePersonLayout = rootView.findViewById(R.id.havePersonLayout)
        noPersonLayout = rootView.findViewById(R.id.layout_empty_layout)
        rightSelect = rootView.findViewById(R.id.rightSelect)
        leftSearch = rootView.findViewById(R.id.leftSearch)
        levelSelect = rootView.findViewById(R.id.levelSelect)
        selectLevelLayout = rootView.findViewById(R.id.selectLevelLayout)
        selectLevelTv = rootView.findViewById(R.id.selectLevelTv)
        cancelSelect = rootView.findViewById(R.id.cancelSelect)
        selectNoPersonLayout = rootView.findViewById(R.id.layout_empty_data)
        personRv.layoutManager = LinearLayoutManager(mActivity)
        viewModel = getModel(OrgExternalContactListViewModel::class.java)
    }

    override fun initLogic() {
        super.initLogic()
        adapter = OrgExternalContactPersonAdapter(mActivity, dataList)
        personRv.adapter = adapter
        leftSearch.setOnClickListener(this)
        rightSelect.setOnClickListener(this)
        cancelSelect.setOnClickListener(this)
//        if ((mActivity as OrgExternalContactActivity).companyBean != null) {
//            companyId = (mActivity as OrgExternalContactActivity).companyBean!!.companyId
//            isEdit = (mActivity as OrgExternalContactActivity).orgPermission
//        }
        getObserver()
        getData()
    }

    fun getData() {
        if (!companyId.isNullOrBlank()) {
            mActivity.getLoadingDialog("", false)
            viewModel.getExternalContactList(bindToLifecycle(), companyId, keyWord, levelType,
                    contactType.toString(), mActivity.accessToken!!)
        }
    }

    @SuppressLint("SetTextI18n")
    private fun getObserver() {
        viewModel.getExternalContactListSuccessObservable.observe(this, Observer {
            mActivity.dismissDialog()
            if (!it.isNullOrEmpty()) {
                //说明全部列表没有数据
                EventBusUtils.sendEvent(EventBusEvent(
                        EventBusAction.Event_ALL_EXTERNAL_LIST_ISHAVE, true))
                dataList = it as ArrayList<ExternalContactListBean>
                noPersonLayout.hide()
                havePersonLayout.visibility = View.VISIBLE
                personRv.visibility = View.VISIBLE
                num.visibility = View.VISIBLE
                selectNoPersonLayout.hide()
                num.text = "共${dataList.size}人"
                adapter.setSourceList(dataList)
                adapter.setCompanyId(companyId)
                adapter.setIsEdit(isEdit)
                adapter.setTypeIndex(contactType)
            } else {
                if (contactType == 0 && !isSelect) {
                    //说明全部列表没有数据
                    EventBusUtils.sendEvent(EventBusEvent(
                            EventBusAction.Event_ALL_EXTERNAL_LIST_ISHAVE, false))
                }
                if (isSelect) {
                    havePersonLayout.visibility = View.VISIBLE
                    personRv.visibility = View.GONE
                    num.visibility = View.GONE
                    noPersonLayout.hide()
                    selectNoPersonLayout.show()
                } else {
                    havePersonLayout.visibility = View.GONE
                    noPersonLayout.show()
                    selectNoPersonLayout.hide()
                }
            }
        })
        viewModel.getExternalContactListErrorObservable.observe(this, Observer {
            mActivity.dismissDialog()
            ToastUtil.show(mActivity, it)
            if (contactType == 0 && !isSelect) {
                //说明全部列表没有数据
                EventBusUtils.sendEvent(EventBusEvent(
                        EventBusAction.Event_ALL_EXTERNAL_LIST_ISHAVE, false))
            }
        })
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            leftSearch -> {
                (mActivity as OrgExternalContactActivity).jumpToSearch(levelType,
                        contactType)
            }
            rightSelect -> {
                selectType()
            }
            cancelSelect -> {
                selectLevelLayout.visibility = View.GONE
                levelSelect.setTextColor(CommonUtils.getColor(mActivity, com.joinutech.ddbeslibrary.R.color.colorFF333333))
                levelSelect.setCompoundDrawablesWithIntrinsicBounds(
                        com.joinutech.ddbeslibrary.R.drawable.icon_notice_select, 0, 0, 0)
                levelType = "0"
                isSelect = false
                getData()
            }
        }
    }

    private fun selectType() {
        val dialog = object : DialogHolder(mActivity,
                com.joinutech.ddbeslibrary.R.layout.dialog_white_top_corner, Gravity.BOTTOM) {
            override fun bindView(dialogView: View) {
                val cancel = dialogView.findViewById<TextView>(R.id.cancel)
                cancel.setOnClickListener {
                    dialog?.dismiss()
                }
                val typeListRv = dialogView.findViewById<RecyclerView>(R.id.rv_list)
                typeListRv.layoutManager = LinearLayoutManager(mActivity)
                val titleList = arrayListOf("全部", "非常重要", "重要", "一般")
                val adapter = ExternalContactTypeSelectAdapter(mActivity, titleList)
                typeListRv.adapter = adapter
                adapter.setItemClickListener(object : ItemClickListener {
                    @SuppressLint("SetTextI18n")
                    override fun onItemClick(position: Int) {
                        val shoeTypeText = adapter.mData[position]
                        levelType = when (shoeTypeText) {
                            "全部" -> "0"
                            "非常重要" -> "1"
                            "重要" -> "2"
                            "一般" -> "3"
                            else -> "0"
                        }
                        levelSelect.setTextColor(CommonUtils.getColor(mActivity, com.joinutech.ddbeslibrary.R.color.main_blue))
                        levelSelect.setCompoundDrawablesWithIntrinsicBounds(
                            com.joinutech.ddbeslibrary.R.drawable.icon_notice_selected, 0, 0, 0)
                        selectLevelLayout.visibility = View.VISIBLE
                        selectLevelTv.text = "选择级别：${shoeTypeText}"
                        isSelect = true
                        getData()
                        dialog?.dismiss()
                    }

                })
            }

        }
        dialog.initView()
        dialog.show(true)
    }

    override fun onDestroy() {
        super.onDestroy()
        mActivity.dismissDialog()
    }

}