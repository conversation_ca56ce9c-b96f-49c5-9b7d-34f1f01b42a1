package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.ChildDepartmentAdapter
import com.joinutech.addressbook.constract.OrgDepConstract
import com.joinutech.addressbook.databinding.ActivityOrgDepBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.common.adapter.impl.DepLevelListAdapter
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.Branch
import com.joinutech.ddbeslibrary.bean.OrgChartDepBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.utils.*
import javax.inject.Inject
import javax.inject.Named

/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName: OrgDepartmentActivity
 * @Desc: 转移部门操作页面
 * @Author:
 * @Leader: Ke
 * @CreateTime: 2020/3/20 10:19 - zhaoyy
 */
class OrgDepartmentActivity(override val contentViewResId: Int = R.layout.activity_org_dep) :
    MyUseBindingActivity<ActivityOrgDepBinding>() {

    private var childDepartmentDataList: ArrayList<Branch> = arrayListOf()
    private lateinit var adapter: ChildDepartmentAdapter
    private var type = ""
    private var name = ""
    private var depName = ""
    private var levelList = arrayListOf<Branch>()
    private lateinit var levelAdapter: DepLevelListAdapter

    @Inject
    @field:Named(AddressbookUtil.ORGCHARTDEP_PRESENTER)
    lateinit var presenter: OrgDepConstract.OrgDepPresenter
    private var companyId: String = ""
    private var depId = "0"
    private var depList: ArrayList<Branch> = arrayListOf()
    private var frontLevel = 0
    private var userIds: ArrayList<String>? = null
    private var ownDepId = "0"
    private var changDepId = "0"
    private var changeDepLevel = 0
    private var changeDepName = ""

    //    private var ischange = 0
    private var isNeedSendRefreshEvent = false
    private var backDepId = arrayListOf("0")

    override fun initImmersion() {
        dealIntentContent()
        if (type == "undoDeal") {
            setRightTitle("暂不处理", View.OnClickListener {
                finish()
            })
        } else {
            showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey, View.OnClickListener {
                if (backDepId.last() == "0" && depId == "0") {
                    finish()
                } else {
                    depId = backDepId.last()
                    if (depId != "0") {
                        backDepId.removeAt(backDepId.size - 1)
                    }
                    refreshOrgDepData()
                }
            })
            setRightTitle("取消", View.OnClickListener {
                finish()
            })
        }
    }

    override fun initView() {
        showToolBarLine()
        whiteStatusBarBlackFont()
        DaggerAddressbookComponent.builder().build().inject(this)
        //初始化底部按钮显示
        initBottomShowContent()

        binding.rvSubDeptList.layoutManager = LinearLayoutManager(this)
        val layoutManager = LinearLayoutManager(this)
        layoutManager.orientation = LinearLayoutManager.HORIZONTAL

        binding.rvLevelList.layoutManager = layoutManager
        if (isNeedSendRefreshEvent) {
            EventBusUtils.sendEvent(
                EventBusEvent(
                    EventBusAction.Event_BATCH_PROCESS_UNDO_SUCCESS, "true"
                )
            )
        }
    }

    override fun initLogic() {
        super.initLogic()
        adapter = ChildDepartmentAdapter(mContext!!, childDepartmentDataList, "orgDep")
        binding.rvSubDeptList.adapter = adapter
        binding.moveText.setOnClickListener(this)
        levelAdapter = DepLevelListAdapter(mContext!!, levelList)
        levelAdapter.setClickListener(object : DepLevelListAdapter.ItemClickListener {
            override fun onItemClick(position: Int) {
                val clickFrontDepId = depId
                depId = levelList[position].deptId
                if (depId != "0") {
                    //说明点击的不是一级部门
                    backDepId.add(clickFrontDepId)
                }
                refreshOrgDepData()
            }

        })
        binding.rvLevelList.adapter = levelAdapter
        refreshOrgDepData()
        adapter.setClickListener(object : ItemClickListener {
            override fun onItemClick(position: Int) {
                val clickFrontDepId = depId
                depId = depList[position].deptId
                if (depId != "0") {
                    //说明点击的不是一级部门
                    backDepId.add(clickFrontDepId)
                }
                refreshOrgDepData()
            }
        })
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityOrgDepBinding {
        return ActivityOrgDepBinding.inflate(layoutInflater)
    }

    override fun onBackPressed() {
        if (backDepId.last() == "0" && depId == "0") {
            super.onBackPressed()
        } else {
            depId = backDepId.last()
            if (depId != "0") {
                backDepId.removeAt(backDepId.size - 1)
            }
            refreshOrgDepData()
        }
    }

    override fun showToolBar(): Boolean {
        return true
    }


    override fun openArouterReceive(): Boolean {
        return false
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.moveText -> {//点击确认转移
                Loggerr.i("验证批量","===name=${name}-----depid=${depId}")
                if (StringUtils.isEmpty(name) && depId == "0") {//批量处理时没有name值，depid为“0”代表还没有选择子部门
                    ToastUtil.show(this, "你还没有选择子部门")
                    return
                }

                val content: String = dealMoreTypeContent()
                val dialog = MyDialog(
                    mContext, 280, 166, content,
                    true, true, 0
                )
                dialog.show()
                dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
                    override fun clickLeftBtn() {
                        dialog.dismiss()
                    }
                })
                dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
                    override fun clickRightBtn() {
                        dialog.dismiss()
                        if (type == "selectMember" || type == "memberInfo" || type == "undoDeal") {
                            //这3种情况实际上都是来转移成员所在的部门
                            translateMemberDepAction()
                        } else if (type == "changeAboveDep") {
                            //这种情况是改变上级部门
                            changeAboveDepAction()
                        }
                    }
                })
            }
        }
    }

    private fun changeAboveDepAction() {
        getLoadingDialog("更改上级部门", false)
        presenter.changeAboveDep(bindToLifecycle<Result<Any>>(), accessToken!!
//                , companyId, ownDepId, depId, { // 移动部门时，当前部门id、选定上级部门id
            , companyId, changDepId, depId, {
                dismissDialog()
                val intent = Intent()
                intent.putExtra("depName", depName)
                intent.putExtra("depId", depId)
                setResult(Activity.RESULT_OK, intent)
                finish()
            }, {
                dismissDialog()
                ToastUtil.show(mContext!!, it)
            })
    }

    /**todo 转移部门 流程
     * 改变团队部门 org/personnel/admin/schedule/deptement
     * 验证是否存在考勤组 att/validate
     * 更新考勤组信息 att/update
     * */
    private fun translateMemberDepAction() {
        getLoadingDialog("校验用户是否存在考勤组", false)
        presenter.validateAttendanceGroup(bindToLifecycle(),
            accessToken!!,
            companyId,
            depId,//depid如果是“0”代表是最外层部门，不是“0”则代表是选中了某个子部门
            userIds!!,
            onSuccess = {
                Loggerr.i("验证批量","===校验考勤组成功===")
                dismissDialog()
                if (it.isDept) {
                    Loggerr.i("验证批量","===校验考勤组成功==是关联部门=")
                    if (it.isExist) {
                        selectIsUpdateAttendance()
                    } else {
//                            ischange = 1
                        updateAttendanceGroup(1)
                    }
                } else {
                    Loggerr.i("验证批量","===校验考勤组成功==不是关联部门=")
                    dealChangeDepSuccess()
                }
            },
            onError = {
                dismissDialog()
                ToastUtil.show(mContext!!, it)
            })
    }

    /**ischange 0保持原考勤组 1变更为关联部门考勤组*/
    private fun updateAttendanceGroup(isChange: Int) {
        getLoadingDialog("批量更新用户考勤组", false)
        presenter.updateAttendanceGroup(bindToLifecycle(), accessToken!!, companyId,
            depId, userIds!!, isChange,
            onSuccess = {
                dismissDialog()
                dealChangeDepSuccess()
            },
            onError = {
                dismissDialog()
                ToastUtil.show(mContext!!, it)
            })
    }

    private fun selectIsUpdateAttendance() {
        val dialog = MyDialog(
            mContext!!, 280, 140,
            "变更部门的员工是否更新为新部门对应的考勤规则？",
            needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0
        )
        dialog.setCanceledOnTouchOutside(false)
        dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
            override fun clickRightBtn() {
                dialog.dismiss()
//                ischange = 1
                updateAttendanceGroup(1)
            }

        })
        dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
            override fun clickLeftBtn() {
                dialog.dismiss()
//                ischange = 0
                updateAttendanceGroup(0)
            }
        })
        dialog.show()
    }

    /**处理部门人员批量转移*/
    private fun dealChangeDepSuccess() {
        getLoadingDialog("更换部门", false)
        Loggerr.i("验证批量","===开始转移===")
        presenter.changeMmeberDep(bindToLifecycle<Result<Any>>(),
            accessToken!!, companyId, depId, userIds!!,
            onSuccess = {
                Loggerr.i("验证批量","===转移成功===")
                hideLoading()
                if (type == "selectMember" && userIds?.isNotEmpty()!!) {
                    EventBusUtils.sendEvent(
                        EventBusEvent(
                            EventBusAction.REFRESH_ORGCHARTLIST,
                            "move"
                        )
                    )
                } else if (type == "memberInfo") {
                    val intent = Intent()
                    intent.putExtra("depName", depName)
                    intent.putExtra("depId", depId)
                    setResult(Activity.RESULT_OK, intent)
                }
                finish()
            },
            onError = {
                Loggerr.i("验证批量","===转移失败===")
                dismissDialog()
                ToastUtil.show(mContext!!, it)
            })
    }

    private fun dealMoreTypeContent(): String {
        return when (type) {
            "memberInfo" -> "你确认要将$name\n" +
                    "转移到${depName}部门么？"
            "selectMember" -> "你确认要将选中的${userIds?.size}名员工\n" +
                    "转移到${depName}部门么？"
            "changeAboveDep" -> "你确认要将$changeDepName\n" +
                    "转换为${depName}部门么？"
            else -> {
              val  content=if(StringUtils.isEmpty(name)){
              "这${userIds?.size?:0}人"
              }else{
                  name
              }
                "你确认要将$content\n" +
                        "入职到${depName}部门么？"
            }
        }
    }

    private fun initBottomShowContent() {
        when (type) {
            "undoDeal" -> //从待处理过来的
                binding.moveText.text = "入职到当前部门"
            "changeAboveDep" -> binding.moveText.text = "确认更改"
        }
        if (type == "changeAboveDep") {
            if (changeDepLevel == 2) {
                moveTextNoClick("当前上级部门")
            } else {
                moveTextClick()
            }
        }
    }

    private fun moveTextClick() {
        binding.moveText.isEnabled = true
        binding.moveText.isClickable = true
        binding.moveText.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color1E87F0))
        binding.moveText.text = "确认更改"
    }

    private fun moveTextNoClick(text: String) {
        binding.moveText.text = text
        binding.moveText.isEnabled = false
        binding.moveText.isClickable = false
        binding.moveText.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.colorffdedede))
    }

    private fun dealIntentContent() {
        if (intent != null) {
            isNeedSendRefreshEvent = intent.getBooleanExtra("isNeedSendRefreshEvent", false)
            //当前部门的团队名称
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("depName"))) {
                depName = intent.getStringExtra("depName")!!
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("type"))) {
                type = intent.getStringExtra("type")!!
            }
            when (type) {
                "undoDeal" -> setPageTitle("选择入职部门")
                "memberInfo" -> setPageTitle("转移部门")
                "selectMember" -> setPageTitle("转移部门")
                "changeAboveDep" -> {
                    //当前部门的团队名称
                    //此处存储当前部门的名称是为了dialog的内容xx部门转移到depName部门显示用到
                    changeDepName = intent.getStringExtra("changeDepName") ?: ""
                    //当前部门的团队id,区分上级部门id，判读是否能转移来用到
                    changDepId = intent.getStringExtra("ownDepId") ?: ""
                    setPageTitle("移动到")
                }
                else -> {
                    setPageTitle(depName)
                }
            }
            //成员姓名，成员转移部门时使用到
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("name"))) {
                name = intent.getStringExtra("name")!!
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                companyId = intent.getStringExtra("companyId")!!
            }
            //成员的userId
            if (intent.getStringArrayListExtra("userIds") != null) {
                userIds = intent.getStringArrayListExtra("userIds")
            }
            //团队ID（当是更改上级部门时，此id为上级部门的id）
            depId = intent.getStringExtra("depId") ?: "0"


            if (intent.getIntExtra("depLevel", 0) != 0) {
                changeDepLevel = intent.getIntExtra("depLevel", 0)
            }
            //用ownDepId和depID是因为面包屑要求进来都为1级部门
            //再依次递增，此时就需要一个id记录下传进来的depID，然后把DepID赋值为0，进入一级部门使用
            ownDepId = depId
            depId = "0"
        }
    }

    private fun dealChangeAboveDep() {
        //先判断层级
        if (depId == "0") {
            //说明现在是1级部门
            if (changeDepLevel == 2) {
                //改变的部门所在层级为2级
                moveTextNoClick("当前上级部门")
            } else {
                moveTextClick()
            }
        } else {
            //不是第1级的部门
            //frontLevel 是当前部门的层级
            if (frontLevel == changeDepLevel) {
                //层级相同再判断是不是相同部门
                if (depId == changDepId) {
                    //同一个部门
                    moveTextNoClick("当前部门")
                } else {
                    moveTextClick()
                }
            } else if (frontLevel < changeDepLevel) {
                //比改之前的部门层级高
                if (levelList.isNotEmpty()) {
                    //判断当前的部门id是否为要改变上级部门的上级部门id
                    //此时的ownDepID为上级部门的部门id，
                    // 因为开始时的传过来的depID已传给ownDepID，depID已不精确，不能为准
                    if (levelList[levelList.size - 1].deptId == ownDepId) {
                        moveTextNoClick("当前上级部门")
                    } else {
                        moveTextClick()
                    }
                } else {
                    moveTextClick()
                }
            } else {
                //比改之前的部门层级低
                //判断是不是自己的子部门
                moveTextClick()
                for (item in levelList) {
                    if (item.deptId == changDepId) {
                        //说明当前部门是自己的子部门
                        moveTextNoClick("当前部门的子部门")
                    }
                }
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun refreshOrgDepData() {
        getLoadingDialog("团队部门", false)
        presenter.getOrgChartDepResult(bindToLifecycle<Result<OrgChartDepBean>>(),
            accessToken!!,
            companyId,
            depId,
            {
                dismissDialog()
                depName = it.deptName

                binding.tvCompanyName.text = depName
                 binding.tvDeptLevel.text = "部门层级：${it.deptLevel}级部门"
                if (it.deptLevel > frontLevel) {
                    levelList.add(Branch(depId, it.deptName))
                } else if (it.deptLevel < frontLevel) {
                    val changeLevelList = arrayListOf<Branch>()
                    for (i in 0 until it.deptLevel) {
                        val s = levelList[i]
                        changeLevelList.add(s)
                    }
                    levelList = changeLevelList
                }
                frontLevel = it.deptLevel
                when (type) {
                    "changeAboveDep" -> {
                        dealChangeAboveDep()
                    }
                    "memberInfo", "selectMember" -> {
                        //判断是否是当前部门就行了
                        //需判断depID是否为原来进入页面的id以此来确认是否是自己部门
                        if (depId == ownDepId) {
                            moveTextNoClick("当前上级部门")
                        } else {
                            moveTextClick()
                        }
                    }
                    else -> moveTextClick()
                }
                levelAdapter.setSourceList(levelList)
                depList = it.branchList as ArrayList<Branch>
                if (depList.isEmpty()) {

                    binding.tvSubDept.visibility = View.GONE
                } else {
                    binding.tvSubDept.visibility = View.VISIBLE
                }
                adapter.setSourceList(depList)
            },
            {
                dismissDialog()
                ToastUtil.show(mContext!!, it)
            })
    }
}