package com.joinutech.addressbook.view.fragment

import android.os.Bundle
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.GroupListAdapter
import com.joinutech.addressbook.viewModel.GroupListViewModel
import com.joinutech.ddbeslibrary.base.BaseFragment
import com.joinutech.ddbeslibrary.bean.GroupListBean
import com.ddbes.library.im.imtcp.Logger
import com.joinutech.ddbeslibrary.utils.*

/**
 * @Description: 团队列表fragment
 * @Author: hjr
 * @Time: 2020/2/25 10:32
 * @packageName: com.joinutech.addressbook.view.fragment
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
class GroupListFragment : BaseFragment() {

    override val layoutRes: Int = R.layout.fragment_group_list

    override fun openEventBus() = false

    private lateinit var groupRv: RecyclerView
    private lateinit var adapter: GroupListAdapter
    private var groupList = arrayListOf<GroupListBean>()
    private var groupListType = 0

    //页面进入类型 0 正常的群组列表 1 是需要转发消息选择的群组（群聊到群聊）
    // 2 是需要转发消息选择的群组（单聊到群聊） 3直接发送的群组消息，但是需要封装消息体
    // 4拼接消息体但是不需要进入聊天页
    private var pageEntryType = 0
    private lateinit var noDataLayout: ConstraintLayout
    private lateinit var viewModel: GroupListViewModel

    companion object {
        private const val GROUP_LIST_TYPE = "group_list_type"
        private const val PAGE_ENTRY_TYPE = "page_entry_type"
        fun newInstance(index: Int, pageEntryType: Int): GroupListFragment {
            return GroupListFragment().apply {
                arguments = Bundle().apply {
                    putInt(GROUP_LIST_TYPE, index)
                    putInt(PAGE_ENTRY_TYPE, pageEntryType)
                }
            }
        }
    }

    override fun initView(rootView: View) {
        if (arguments != null && arguments?.getInt(GROUP_LIST_TYPE) != null)
            groupListType = arguments?.getInt(GROUP_LIST_TYPE) ?: 0
        if (arguments != null && arguments?.getInt(PAGE_ENTRY_TYPE) != null)
            pageEntryType = arguments?.getInt(PAGE_ENTRY_TYPE) ?: 0
        groupRv = rootView.findViewById(R.id.groupRv)
        groupRv.layoutManager = LinearLayoutManager(mActivity)
        noDataLayout = rootView.findViewById(R.id.noDataLayout)
        viewModel = getModel(GroupListViewModel::class.java)
    }

    override fun initLogic() {
        super.initLogic()

        getObservable()
        //点击事件
        adapter = GroupListAdapter(mActivity, groupList, object : ItemClickListener {
            override fun onItemClick(position: Int) {
                val groupId = adapter.mData[position].groupId
//                val msg = getMsgBean(groupId)
                when (pageEntryType) {
                    0 -> {//tcp群列表的点击事件
                        if(groupListType==0||groupListType==1){
                            //当点击的是私有群组时，“companyId”字段为空或者"",当为团队群组时，该字段有值；
                            ARouter.getInstance()
                                    .build(RouteIm.groupChat)
                                    .withString("targetId", groupId)
                                    .withString("targetName", adapter.mData[position].name)
                                    .withString("targetLogo", adapter.mData[position].logo)
                                    .withString("companyId", adapter.mData[position].orgId.toString())
                                    .navigation()
                        }

                    }
                    1 -> {//转发群消息到群组，//转发图片到群组
                       /* if (msg != null) {
                            //转发的消息体存在
                            if (msg.targitId == groupId) {
                                ToastUtil.show(mActivity, "同一群组不能转发")
                                return
                            }
                            MsgTranslator.translationMsgToGroupChat(mActivity,
                                    msg, groupId, userId!!,
                                    onSuccess = {
                                        ToastUtil.show(mActivity, "转发消息成功")
                                        ARouter.getInstance()
                                                .build(RouteIm.groupChat)
                                                .withString("targetId", groupId)
                                                .withString("targetName", adapter.mData[position].name)
                                                .withString("targetLogo", adapter.mData[position].logo)
                                                .navigation()
                                        mActivity.finish()
                                    },
                                    onError = {
                                        ToastUtil.show(mActivity, "转发消息失败")
                                        mActivity.finish()
                                    }
                            )
                        }*/
                    }
                    2 -> {//转发消息到群组
                      /*  if (msg != null) {
                            //转发的消息体存在
                            MsgTranslator.translationSingleChatMsgToGroup(mActivity,
                                    msg,
                                    groupId, {
                                ToastUtil.show(mActivity, "转发消息成功")
                                ARouter.getInstance()
                                        .build(RouteIm.groupChat)
                                        .withString("targetId", groupId)
                                        .withString("targetName", adapter.mData[position].name)
                                        .withString("targetLogo", adapter.mData[position].logo)
                                        .navigation()
                                mActivity.finish()
                            }, {
                                ToastUtil.show(mActivity, "转发消息失败")
                                mActivity.finish()
                            }
                            )
                        }*/
                    }
                    3 -> {//团队邀请到群组
                        /*if (msg != null) {
                            MsgTranslator.sendGroupToGroupMsg(mActivity,
                                    msg, groupId, userId!!,
                                    onSuccess = {
                                        ToastUtil.show(mActivity, "发送群组消息成功")
                                        ARouter.getInstance()
                                                .build(RouteIm.groupChat)
                                                .withString("targetId", groupId)
                                                .withString("targetName", adapter.mData[position].name)
                                                .withString("targetLogo", adapter.mData[position].logo)
                                                .navigation()
                                        mActivity.finish()
                                    },
                                    onError = {
                                        ToastUtil.show(mActivity, "发送群组消息失败")
                                        mActivity.finish()
                                    })
                        }*/
                    }
                    4 -> {//tcp分享到群组，然后点击某个群组
                        /*if (getMsgBean(adapter.mData[position].groupId) != null) {
                            MsgTranslator.sendGroupToGroupMsg(mActivity,
                                    ddMessage = getMsgBean(adapter.mData[position].groupId)!!,
                                    groupId = adapter.mData[position].groupId, userId = userId!!,
                                    onSuccess = {
                                        ToastUtil.show(mActivity, "发送群组消息成功")
                                        EventBusUtils.sendEvent(EventBusEvent(
                                                EventBusAction.TRANSMSG_SUCCEESS, ""))
                                        mActivity.finish()
                                    },
                                    onError = {
                                        ToastUtil.show(mActivity, "发送群组消息失败")
                                        mActivity.finish()
                                    })
                        }*/
                    }
                }
            }

        })
        groupRv.adapter = adapter
        //进入群列表页面后直接联网获取群列表
        getListData()
    }

  /*  private fun getMsgBean(groupId: String): DDMessage? {
        var msgBean: DDMessage? = null
        if ((mActivity as GroupListActivity).msgBean == null) {
            //有的时候消息不从外面带入 需要在此组装
            if ((mActivity as GroupListActivity).companyBean != null) {
                msgBean = generateOrgMsg(groupId, (mActivity as GroupListActivity).companyBean!!)
            }
            if ((mActivity as GroupListActivity).shareMap != null) {
                msgBean = generateShareMsg(groupId, (mActivity as GroupListActivity).shareMap!!)
            }
        } else {
            msgBean = (mActivity as GroupListActivity).msgBean
        }
        return msgBean
    }*/

   /* private fun generateOrgMsg(groupId: String, companyBean: AddOrgMsgBean): DDMessage {
        val msg = DDMessage()
        msg.targitId = groupId
        msg.fromUserId = userId
        msg.sendTime = System.currentTimeMillis()
        msg.voicePath = GsonUtil.toJson(companyBean)
        msg.content = "团队邀请 【${companyBean.companyName}】"
        msg.type = MESSAGE_TYPE.ADDORG
        return msg
    }*/

    /**生成群分享信息*/
   /* private fun generateShareMsg(groupId: String, shareMap: HashMap<String, String>): DDMessage {
        val msg = DDMessage()
        msg.targitId = groupId
        msg.fromUserId = userId
        msg.voicePath = GsonUtil.toJson(ReportMsgBean(
                shareMap["reportIdValue"]!!, shareMap["reportContent"]!!, shareMap["companyId"]!!))
        msg.content = shareMap["pageTitle"]!!
        msg.sendTime = System.currentTimeMillis()
        msg.type = MESSAGE_TYPE.SHARE_REPORT
        return msg
    }*/

    private fun isNoDataShow() {
        if (groupList.isNullOrEmpty()) {
            groupRv.visibility = View.GONE
            noDataLayout.visibility = View.VISIBLE
        } else {
            groupRv.visibility = View.VISIBLE
            noDataLayout.visibility = View.GONE
        }
    }

    private fun getObservable() {
        viewModel.getGroupListSuccessObservable.observe(this, Observer {
            Logger.i("---验证群组数据----","----json----"+GsonUtil.toJson(it))
            mActivity.dismissDialog()
            groupList = it as ArrayList<GroupListBean>
            adapter.setSourceList(groupList)
            isNoDataShow()
        })
        viewModel.getGroupListErrorObservable.observe(this, Observer {
            mActivity.dismissDialog()
            ToastUtil.show(mActivity, it)
        })
    }

    fun getListData(isNeedRefresh: Boolean = false) {
        mActivity.getLoadingDialog("", false)
        viewModel.getGroupList(bindToLifecycle(), if (groupListType == 0 || isNeedRefresh) {
            2
        } else {
            1
        }, mActivity.accessToken
        )
    }

}