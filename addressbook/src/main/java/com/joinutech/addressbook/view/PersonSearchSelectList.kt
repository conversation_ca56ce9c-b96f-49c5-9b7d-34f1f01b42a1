package com.joinutech.addressbook.view

import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.ddbes.library.im.imtcp.dbbean.FriendBean
import com.ddbes.library.im.util.FriendCacheHolder
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.ContactsWithSearchAdapter
import com.joinutech.addressbook.databinding.ActivityFriendtransmsgLayoutBinding
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.ContactModel
import com.joinutech.ddbeslibrary.bean.LetterComparator
import com.joinutech.ddbeslibrary.bean.OrgImportPeopleBean
import com.joinutech.ddbeslibrary.bean.OutMsgPersonBean
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.toastShort
import com.joinutech.ddbeslibrary.widget.activity.BaseOrgSelectMemberActivity
import com.joinutech.ddbeslibrary.widget.wavesidebar.PinnedHeaderDecoration
//import kotlinx.android.synthetic.main.activity_friendtransmsg_layout.*
//import kotlinx.android.synthetic.main.search_include_layout.*
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.*
import kotlin.collections.ArrayList
import kotlin.collections.HashSet

/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName: PersonSearchSelectList
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/9/15 11:25
 *
 * @Desc:
 *        团队成员选择：
 *        创建项目选择参与人员 CreateProgramSelectProjectJoiner、
 *        项目参与人 selectProjectJoiner、
 *        项目转让 ProjectHandOverSelectMember、
 *        团队权限管理 givePermission、
 *        团队转让 selectMember、
 *        好友选择：
 *        团队邀请好友为成员 addOrgMsg 、
 *        转发图片消息 transMsg、
 *        转发群组消息 transGroupMsg、
 *        考勤分享给好友 shareToFriend、
 *        聊天选择好友、
 *
 * // TODO: 2020/9/15 13:37 当前页面根据类型type，绑定了太多业务处理，需要清理业务，还原为单纯的人员选择
 *  todo 返回请求页面后处理结果或者传递路由参数，携带选择结果跳转到指定页面并结束当前页面
 */
@Route(path = RouteOrg.friendListWithSearchActivity)
class PersonSearchSelectList : MyUseBindingActivity<ActivityFriendtransmsgLayoutBinding>() {

    override val contentViewResId: Int = R.layout.activity_friendtransmsg_layout
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityFriendtransmsgLayoutBinding {
        return ActivityFriendtransmsgLayoutBinding.inflate(layoutInflater)
    }

    /**
     * CreateProgramSelectProjectJoiner 创建项目选择成员
     *
     * selectProjectJoiner 项目添加成员
     *
     * ProjectHandOverSelectMember X2 项目移交时选择项目成员、公司成员
     *
     * givePermission X2 添加管理员时选择公司成员
     *
     * selectMember 转移创建者
     *
     * shareToFriend 考勤分享给好友
     */
    @Autowired
    @JvmField
    var type: String = ""

    private lateinit var mShowModels: ArrayList<ContactModel>
    private var mAllShowModels = arrayListOf<ContactModel>()
    private lateinit var mAdapter: ContactsWithSearchAdapter
    private lateinit var selectedPeopleList: ArrayList<OrgImportPeopleBean>
    private lateinit var selectAdapter: BaseOrgSelectMemberActivity.Companion.SelectMemberAdapter

    /**已选择人员的集合*/
    private var userSet: HashSet<String> = HashSet()

    //搜索之前保存有是否选中的列表，从上个页传入
    /**已选中过的成员id，不可点击取消选中状态人员*/
    @Autowired
    @JvmField
    var selectedList: ArrayList<String>? = null

    @Autowired
    @JvmField
    var companyId: String = ""

    @Autowired
    @JvmField
    var companyName: String = ""

    @Autowired
    @JvmField
    var companyLogo: String = ""
    private var isAllSelect = false

    /**设计到公司成员相关消息烈性*/
    private val memberTypes = arrayOf("CreateProgramSelectProjectJoiner",
            "selectProjectJoiner", "ProjectHandOverSelectMember", "givePermission", "selectMember")

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        /*团队成员选择：
        创建项目选择参与人员 CreateProgramSelectProjectJoiner、
        项目参与人 selectProjectJoiner、
        项目转让 ProjectHandOverSelectMember 2、
        团队权限管理 givePermission 2、
        团队转让 selectMember、
        好友选择：
        团队邀请好友为成员 addOrgMsg 、
        转发图片消息给好友 transMsg 2、
        转发群组消息给好友 transGroupMsg、
        考勤分享给好友 shareToFriend、
        聊天选择好友、
        */
        if (type in memberTypes) {
            when (type) {
                "givePermission" -> {
                    setPageTitle("团队管理权限")
                    setRightTitle("保存", this)
                }
                "ProjectHandOverSelectMember" -> {
                    setPageTitle("团队成员")
                    hideRightText()
                }
                "selectProjectJoiner", "CreateProgramSelectProjectJoiner" -> {
                    setPageTitle("团队成员")
                    setRightTitle("完成", this)
                }
                "selectMember" -> {
                    setPageTitle("选择人员")
                }
            }
        } else {
            setPageTitle("选择好友")
        }
    }

    private lateinit var emptyPage: PageEmptyView

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        if (selectedList == null) selectedList = arrayListOf()
        showLog("接收已经选中的人员信息为：${GsonUtil.toJson(selectedList)}")
        if (!selectedList.isNullOrEmpty()) {
            userSet.addAll(selectedList!!)
        }

        binding.mainRecycler.layoutManager = LinearLayoutManager(this)
        val decoration = PinnedHeaderDecoration()
        decoration.registerTypePinnedHeader(1) { _, _ -> true }
        binding.mainRecycler.addItemDecoration(decoration)
        when (type) {
            "shareToFriend"/*, "transMsg", "transGroupMsg" */ -> {

                binding.searchInclueLayout.cancel.visibility = View.VISIBLE
                binding.searchInclueLayout.search.hint = "搜索担当好友"
                binding.searchInclueLayout.delete.setImageResource(com.joinutech.ddbeslibrary.R.drawable.del_img)
            }
            "givePermission", "selectProjectJoiner", "ProjectHandOverSelectMember",
            "CreateProgramSelectProjectJoiner"/*, "addOrgMsg"*/ -> {
                binding.searchInclueLayout.search.hint = "输入要搜索的用户名"
                binding.searchInclueLayout.cancel.visibility = View.GONE
                binding.searchInclueLayout.delete.setImageResource(R.drawable.icon_delete_square)
            }
            "selectMember"/*, "selectMembers" */ -> {
                binding.searchInclueLayout.search.hint = "输入要搜索的用户名"
            }
        }
        binding.searchInclueLayout.delete.visibility = View.INVISIBLE

        emptyPage = findViewById(R.id.layout_empty_layout)

        selectedPeopleList = arrayListOf()
         binding.rvSelectList.layoutManager = LinearLayoutManager(this,
                LinearLayoutManager.HORIZONTAL, false)
        selectAdapter = getSelectAdapter()
        binding.rvSelectList.adapter = selectAdapter
        initRecyclerView()
        initSearchAction()
        binding.searchInclueLayout.cancel.setOnClickListener(this)
        binding.searchInclueLayout.delete.setOnClickListener(this)

        binding.selectCheck.setOnClickListener(this)
    }

    private fun getSelectAdapter(): BaseOrgSelectMemberActivity.Companion.SelectMemberAdapter {
        return BaseOrgSelectMemberActivity.Companion.SelectMemberAdapter(this, selectedPeopleList, true)
    }

    private  var logoutFriendIds: Set<String> = hashSetOf()
    override fun initLogic() {
        super.initLogic()
        getAllUsers()
    }

    private fun getAllUsers() {
        getLoadingDialog("", true)
        if (StringUtils.isNotBlankAndEmpty(companyId) && type in memberTypes) {
            searchOrgMember()
        } else {
            searchFriendListNew()
//            searchFriendList()
        }
    }

    /**处理公司成员数据 SearchMemberBean -> FriendBean*/
    private fun dealCompanyMemberBean(t: List<SearchMemberBean>): List<FriendBean> {
        val list = arrayListOf<FriendBean>()
        if (t.isNotEmpty()) {
            for (item in t) {
                val friendBean = FriendBean()
                friendBean.avatar = item.headimg
                friendBean.userId = item.userId
                friendBean.name = item.name
                when (type) {
                    "selectProjectJoiner", "ProjectHandOverSelectMember",
                    "CreateProgramSelectProjectJoiner" -> {
                        // TODO: 2019/8/14 添加状态，处理单选和多选类型人员选择
                        if (type != "ProjectHandOverSelectMember") {
                            friendBean.remark = item.positionName
                        }
                        list.add(friendBean)
                    }
                    else -> {
                        // 选择成员类型不可以选择自己
                        if (item.userId != userId) {
                            list.add(friendBean)
                        }
                    }
                }
            }
        }
        return list
    }

    private fun initRecyclerView() {
        mShowModels = arrayListOf()
        mAdapter = ContactsWithSearchAdapter(mShowModels, mContext!!)
        mAdapter.setType(type)
        if (type !in memberTypes) {
            logoutFriendIds = FriendCacheHolder.getMyLogoutFriendIds()
            mAdapter.setLogoutUserIds(logoutFriendIds)
        }
        // RecyclerView设置相关
        binding.mainRecycler.adapter = mAdapter
        if (StringUtils.isNotBlankAndEmpty(type)) {
            /**适配器点击监听*/
            mAdapter.setOnClickListener { contact ->
                contact.check = !contact.check
                if (contact.check) {
                    when (type) {
                        "givePermission", "selectProjectJoiner",
                        "CreateProgramSelectProjectJoiner"/*, "selectMembers"*/ -> {
                            userSet.add(contact.userId)
                        }
                    }
                } else {
                    //取消勾选
                    userSet.find { it == contact.userId }?.let { userSet.remove(it) }
                }
                mShowModels.find { it.userId == contact.userId }?.check = contact.check
                handleRightTextShow()// 列表点选更新选中人员userId记录后 更新底部选择人数信息
            }
            mAdapter.selectListener = object : OnContactSelectListener {
                override fun onSelect(position: Int) {
                    // TODO: 2020/8/3 10:33 单选后结果返回
                    if ("selectMember" == type || type == "shareToFriend") {
                        val data = mShowModels[position]
                        if (logoutFriendIds.contains(data.userId)) {
                            toastShort("该好友已注销账号")
                            return
                        }
                        val intent = Intent()
                        intent.putExtra(type, GsonUtil.toJson(data))
                        setResult(Activity.RESULT_OK, intent)
                        finish()
                    }
                }

                override fun onUnSelect(position: Int) {

                }

            }
        }
    }

    /**
     * 创建项目选择参与人员时业务处理
     * 默认状态 选择跳过
     * 选择人员后或者全选后，右上角显示
     * */
    private fun handleRightTextShow() {
        if (type == "CreateProgramSelectProjectJoiner") {
            if (userSet.isNotEmpty()) {
                tv_rightTitle!!.text = "完成"
                tv_rightTitle!!.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.colorFF333333))
            } else {
                tv_rightTitle!!.text = "跳过"
                tv_rightTitle!!.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color2479ED))
            }
        }
        updateBottomLayout()
    }

    private var keyword = ""

    /**搜索框搜索事件处理*/
    private fun initSearchAction() {
        binding.searchInclueLayout.search.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                keyword = s?.toString() ?: ""
                mShowModels.clear()
                if (StringUtils.isNotBlankAndEmpty(keyword)) {
                    binding.searchInclueLayout.delete.visibility = View.VISIBLE
                     binding.clTitleLayout.visibility = View.GONE
                    val temp = mAllShowModels.filter { it.name.contains(keyword) }
                    mShowModels.addAll(temp)
                } else {
                    keyword = ""
                    binding.searchInclueLayout.delete.visibility = View.INVISIBLE
                    binding.clTitleLayout.visibility = View.VISIBLE
                    mShowModels.addAll(mAllShowModels)
                }
                if (mShowModels.isNullOrEmpty()) {
                    dealFriendSearchNoResult()
                } else {
                    emptyPage.visibility = View.GONE
                    binding.dataLayout.visibility = View.VISIBLE
//                    dealFriendAllResult()
                    mAdapter.setDataAndKeyword(mShowModels, keyword)
                }
//                // TODO: 2021/6/4 优化公司成员搜索流程
//                if (companyId.isNotEmpty()) {
//                    searchDepMemberWithInput(keyword)
//                } else {
////                        searchFriendListWithInput(string)
//                    searchFriendListWithInputNew(keyword)
//                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        })
//        binding.searchInclueLayout.search.setOnEditorActionListener { _, actionId, _ ->
//            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
//                getLoadingDialog("", true)
//                //输入内容不为空
//                val keyWord = binding.searchInclueLayout.search.text.toString().trim()
//                if (companyId.isNotEmpty()) {
//                    searchDepMemberWithInput(keyWord)
//                } else {
////                        searchFriendListWithInput(string)
//                    searchFriendListWithInputNew(keyWord)
//                }
//                true
//            }
//            false
//        }
    }


    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return true
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            tv_rightTitle -> {
                dealRightEvent()
            }
            binding.searchInclueLayout.delete -> {
                binding.searchInclueLayout.search.text.clear()
            }
            binding.searchInclueLayout.cancel -> {
                finish()
            }
            binding.selectCheck -> {
                dealAllCheck()
            }
        }
    }

    /**团队人员获取后相关数据展示和业务处理*/
    private fun dealOrgMemberProjectResult(list: List<SearchMemberBean>) {
        if (type == "CreateProgramSelectProjectJoiner") {
             binding.createProgramText.visibility = View.VISIBLE
        } else {
            binding.createProgramText.visibility = View.GONE
        }
        setPageTitle("团队成员(${list.size})")
        /**处理公司成员数据，转换为好友数据后显示到页面*/
        val findMemberList: List<FriendBean> = dealCompanyMemberBean(list)// 数据类型转换
        if (type == "CreateProgramSelectProjectJoiner") {
            selectedList = arrayListOf(userId!!)
        }
        mShowModels = dealFriendList(findMemberList, selectedList!!)// // TODO: 2021/8/26 13:53 项目相关 标识已经选择的人员
        dealFriendAllResult()// 项目公司成员选择相关逻辑
        if (type != "ProjectHandOverSelectMember") {
            dealAllCheckShow()
        }
        if (type == "selectMember") {
            // 全选成员不显示
            binding.clTitleLayout.visibility = View.GONE
            if (mShowModels.isNullOrEmpty()) {
                dealFriendSearchNoResult()
            }
        }
//        if (type == "selectMembers") {
//            selectAllMember.text = "全选"
//        }
    }

    /** 搜索团队成员*/
    private fun searchOrgMember() {
        AddressbookService.searchDepMember(companyId, "")
                .compose(bindToLifecycle())
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<List<SearchMemberBean>>() {
                    override fun onError(ex: ApiException) {
                        dismissDialog()
                        dealFriendSearchNoResult()
                        ToastUtil.show(mContext!!, ex.message)
                    }

                    override fun onComplete() {}
                    override fun onNext(list: List<SearchMemberBean>?) {
                        dismissDialog()
                        mAllShowModels.clear()
                        if (list != null) {
                            if (list.isNotEmpty()) {
                                emptyPage.visibility = View.GONE
                                binding.dataLayout.visibility = View.VISIBLE
                                if (type in memberTypes && type != "givePermission") {
                                    //选择项目参与人员请求团队下的人员
                                    // TODO: 2019/8/14 增加状态，以获取任务成员选择
                                    // TODO: 2021/6/7 公司成员选择相关逻辑
                                    dealOrgMemberProjectResult(list)
                                } else {
                                    // TODO: 2021/6/7 公司成员授权选择相关
                                    binding.createProgramText.visibility = View.GONE
                                    val friendList: List<FriendBean> = dealCompanyMemberBean(list)
                                    mShowModels = dealFriendList(friendList, selectedList!!)// // TODO: 2021/8/26 13:52 成员数据
                                    dealFriendAllResult()// 公司成员相关显示
                                }
                                mAllShowModels.addAll(mShowModels)
                                handleRightTextShow() // 所有数据处理完成后处理已选底部提示
                            } else {
                                dealFriendSearchNoResult()
                            }
                        }
                    }

                })
    }

    /** 搜索好友信息*/
    private fun searchFriendListNew() {
        FriendCacheHolder.loadFriend(this, bindToLifecycle(),
                onResult = {
                    hideLoading()
                    mAllShowModels.clear()
                    emptyPage.visibility = View.GONE
                    binding.dataLayout.visibility = View.VISIBLE
                    mShowModels = dealFriendList(it, selectedList!!)// TODO: 2021/8/26  好友数据
                    dealFriendAllResult()// 加载好友数据后更新其他状态
                    mAllShowModels.addAll(mShowModels)
                    handleRightTextShow()// 所有数据处理完成后处理已选底部提示
                },
                onError = {
                    hideLoading()
                    dealFriendSearchNoResult()
                    ToastUtil.show(mContext!!, it)
                })
    }

    /**处理全选后显示业务*/
    private fun dealAllCheckShow() {
        var noSelectNum = 0
        if (mShowModels.isNotEmpty()) {
            mShowModels.forEach {
                if (it.noSelect) noSelectNum++
            }
            if (mShowModels.size == noSelectNum) {
                //说明全部不可选，那就不存在全选非全选问题
                binding.clTitleLayout.visibility = View.GONE
            } else {
                binding.clTitleLayout.visibility = View.VISIBLE
            }
        }
    }

    /**全选数据处理逻辑*/
    private fun dealAllCheck() {
        userSet.clear()
        selectedList!!.clear()
        isAllSelect = !isAllSelect
        binding.selectCheck.isSelected = isAllSelect
        if (isAllSelect) {
            //全选了
            mAdapter.list.forEach {
                if (!it.noSelect) {
                    //不可选的不参加全选和非全选
                    it.check = true
                    userSet.add(it.userId)
                    selectedList!!.add(it.userId)
                }
            }
            mShowModels.forEach {
                if (!it.noSelect) it.check = true
            }
            mAdapter.notifyDataSetChanged()
        } else {
            //全不选
            mAdapter.list.forEach {
                if (!it.noSelect) it.check = false
            }
            mShowModels.forEach {
                if (!it.noSelect) it.check = false
            }
            mAdapter.notifyDataSetChanged()
        }
        handleRightTextShow()// 全选后处理已选中状态后，更新底部选择框中人数
    }

    private fun updateBottomLayout() {
        selectedPeopleList.clear()
        selectedPeopleList.addAll(
                mAllShowModels.filter { it.userId in userSet }.map { member ->
                    val bean = OrgImportPeopleBean()
                    bean.userId = member.userId
                    bean.headimg = member.logo
                    bean.name = member.name
                    bean
                }.toList()
        )
        if (selectedPeopleList.isNotEmpty()) {
             binding.llSelectedLayout.visibility = View.VISIBLE
            binding.numSelTv.text = "已选择人员"
            selectAdapter.notifyDataSetChanged()
        } else {
            binding.llSelectedLayout.visibility = View.GONE
        }
    }

    /**右上角点击事件处理*/
    private fun dealRightEvent() {
        val userList = ArrayList<String>()
        if (userSet.isNotEmpty()) {
            for (item in userSet) {
                userList.add(item)
            }
        }
//        if (userList.isNotEmpty()) {
        dealDetailEvent(userList)
//        }
    }

    /**转发消息时，发送成功后通知该页面关闭
     * // TODO: 2020/8/3 10:14 需要改为 选择人员后，回到调用位置，发送信息，不需要关联的到人员选择功能页面
     * */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun translateMsgSuccess(event: EventBusEvent<String>) {
        if (event.code == EventBusAction.TRANSMSG_SUCCEESS) {// 监听发送成功事件
            finish()
        }
    }

    /**请求结果加载*/
    private fun dealFriendAllResult() {
        if (userSet.isEmpty()) {
            //说明是第一次还没开始搜索或者说没有被选中的人
            if (type == "selectProjectJoiner" || type == "CreateProgramSelectProjectJoiner") {
                // 添加项目新成员时需要把选择过的人员设为不可选
                dealSelectNoSelectGreyShow()
            } else {
                if (selectedList!!.isNotEmpty()) {
                    //说明从上个页面传过来了数据
                    userSet.addAll(selectedList!!)
                }
            }
        } else {
            for (item in mShowModels) {
                item.check = item.userId in userSet
                if (type == "selectProjectJoiner" || type == "CreateProgramSelectProjectJoiner") {
                    item.noSelect = item.userId in selectedList!!
                } else {
                    item.noSelect = false
                }
            }
        }
        //全选按钮全选状态根据已选人员数量显示
        //做全选非全选按钮的显示与否
        if (userSet.size == 0) {
            isAllSelect = false
            binding.selectCheck.isSelected = isAllSelect
        } else {
            isAllSelect = true
            for (item in mShowModels) {
                if (item.userId !in userSet) {
                    isAllSelect = false
                    break
                }
            }
            binding.selectCheck.isSelected = isAllSelect
        }
        mAdapter.setDataAndKeyword(mShowModels, keyword)
    }

    /**创建任务时过滤当前创建者 项目中已选择人员不可以再修改*/
    private fun dealSelectNoSelectGreyShow() {
        //创建项目时创建者不可取消
        if (selectedList != null && selectedList!!.isNotEmpty() && mShowModels.isNotEmpty()) {
            for (item in mShowModels) {
                if (item.userId in selectedList!!) {
                    item.noSelect = true// 标记结果状态，不可以操作
                    userSet.add(item.userId)// 标记已选中
                }
            }
        }
    }

    /**处理多选人员后，点击完成时的业务处理
     * // TODO: 2020/8/3 10:16 相关业务需要回归到业务调用处处理，不能写在人员选择页面
     * */
    private fun dealDetailEvent(userList: ArrayList<String>) {
        //把用户的id转换为带relation的bean类
        fun userIdToUserPerson(list: ArrayList<OutMsgPersonBean>) {
            if (userList.isNotEmpty() && mShowModels.isNotEmpty()) {
                for (item in mShowModels) {
                    for (id in userList) {
                        if (id == item.userId) {
                            list.add(OutMsgPersonBean(id, item.relation == 2))
                            break
                        }
                    }
                }
            }
        }
        when (type) {
//            "transMsg", "transGroupMsg" -> {
//                val list = arrayListOf<OutMsgPersonBean>()
//                userIdToUserPerson(list)
//                if (ddMessage != null && userList.isNotEmpty()) {
//                    if (type == "transGroupMsg") {
//                        ddMessage!!.fromUserId = userId
//                        MsgTranslator.translationGroupChatMsgToSingleChat(mContext!!, ddMessage!!, list)
//                    } else if (type == "transMsg") {
//                        ddMessage!!.fromUserId = userId
//                        MsgTranslator.translationSingleChatMsgToSingleChat(mContext!!, ddMessage!!, list)
//                    }
//                }
//            }
//            "addOrgMsg" -> {
//                val bean = AddOrgMsgBean(companyId, companyLogo, companyName)
//                val list = arrayListOf<OutMsgPersonBean>()
//                userIdToUserPerson(list)
//                MsgTranslator.sendAddOrgMsgToFriend(mContext!!, userId!!.toString(), bean, list, companyName)
//            }
            "givePermission", "selectProjectJoiner", "ProjectHandOverSelectMember",
            "CreateProgramSelectProjectJoiner" -> {
                //保存，并返回团队设置管理人员列表
//                val userNum = if (userList.isNullOrEmpty()) {
//                    0
//                } else {
//                    userList.size
//                }
//                ToastUtil.show(mContext!!, "已选择用户数量为${userNum}人")
                when (type) {
                    "givePermission" -> EventBusUtils.sendEvent(EventBusEvent(
                            EventBusAction.SELECTEDPER_USERLIST, userList))
                    "CreateProgramSelectProjectJoiner" -> {
                        EventBusUtils.sendEvent(EventBusEvent(
                                EventBusAction.SELECTEPROJECT_JOINERLIST, userList))
                    }
                    "selectProjectJoiner" -> {
                        if (selectedList!!.isNotEmpty()) {
                            val list = arrayListOf<String>()
                            for (item in mShowModels) {
                                if (!item.noSelect && item.check) {
                                    list.add(item.userId)
                                }
                            }
                            EventBusUtils.sendEvent(EventBusEvent(
                                    EventBusAction.SELECTPROJECTJOINER_USERLIST, list))
                        } else {
                            EventBusUtils.sendEvent(EventBusEvent(
                                    EventBusAction.SELECTPROJECTJOINER_USERLIST, userList))
                        }
                    }
                }
                finish()
            }
//            "selectMembers" -> {
//                val selectMember = arrayListOf<ContactModel>()
//                if (userList.isNotEmpty()) {
//                    for (item in mShowModels) {
//                        item.check
//                        if (item.userId in userList) {
//                            selectMember.add(item)
//                        }
//                    }
//                }
//                val intent = Intent()
//                intent.putExtra(type, GsonUtil.toJson(selectMember))
//                setResult(Activity.RESULT_OK, intent)
//                finish()
//            }
        }
    }

    /**好友选择 复选情况处理 friendBean -> ContactModel*/
    private fun dealFriendList(findMemberList: List<FriendBean>, selectedList: ArrayList<String>): ArrayList<ContactModel> {
        val list = ArrayList<ContactModel>()
        if (findMemberList.isNotEmpty()) {
            for (member in findMemberList) {
                var bean = ContactModel(member.name, member.avatar)
                when (type) {
                    "selectMember", "selectProjectJoiner", "CreateProgramSelectProjectJoiner" -> {
                        bean.phoneNum = member.remark
                    }
                    else -> {
                        if (StringUtils.isNotBlankAndEmpty(member.remark)) {
                            bean = ContactModel(member.remark, member.avatar)
                        }
                    }
                }
                bean.userId = member.userId
                if (selectedList.isNotEmpty()) {
                    bean.check = member.userId in selectedList// 标记已选中状态
                }
                list.add(bean)
            }
            Collections.sort(list, LetterComparator())
        }
        return list
    }

    /**处理好友搜索无数据时页面显示*/
    private fun dealFriendSearchNoResult() {
        mShowModels.clear()
        binding.dataLayout.visibility = View.GONE
        when (type) {
            "shareToFriend"/*, "transMsg", "transGroupMsg" */ -> {
                emptyPage.setContent("未找到符合条件的担当好友")
            }
            "selectProjectJoiner", "CreateProgramSelectProjectJoiner",
            "givePermission", "selectMember" -> {
                //团队人员列表
                emptyPage.setContent("未找到符合条件的结果")
            }
        }
        emptyPage.show()
    }
}

interface OnContactSelectListener {
    fun onSelect(position: Int)
    fun onUnSelect(position: Int)
}