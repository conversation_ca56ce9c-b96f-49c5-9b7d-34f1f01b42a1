package com.joinutech.addressbook.view

import android.app.Activity
import android.content.Intent
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.addressbook.R
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.bean.Branch
import com.joinutech.common.adapter.MyAdapter

/**
 * @Description: 未采用，已废弃 新增部门设置页面 包含 部门排序 和 部门设置 功能入口
 * @Author: zhaoyy
 * @Time: 2021/6/3 14:54
 * @packageName: com.joinutech.addressbook.view
 * @Company: Copyright (C),2019- 2021,JoinuTech
 */
@Deprecated("no used")
class DepartmentSettingActivity : MyUseBaseActivity() {
    override val contentViewResId: Int = com.joinutech.ddbeslibrary.R.layout.common_list_layout

    override fun showToolBar(): <PERSON>olean {
        return true
    }

    override fun openArouterReceive(): <PERSON><PERSON><PERSON> {
        return false
    }

    override fun initImmersion() {
        whiteStatusBarBlackFont()
    }

    override fun initView() {
        setPageTitle("部门管理")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back2, this)
        showToolBarLine()
        val emptyView = findViewById<PageEmptyView>(R.id.layout_empty_layout)
        emptyView.visibility = View.GONE

        val list = intent.getSerializableExtra("deptList") as ArrayList<Branch>
        val adapter = MyAdapter<String>(this, com.joinutech.ddbeslibrary.R.layout.item_common_pop_layout,
                mData = if (!list.isNullOrEmpty()) {
                    arrayListOf("部门设置", "部门排序")
                } else {
                    arrayListOf("部门设置")
                },
                onBindItem = { _, data, itemView ->
                    itemView.findViewById<TextView>(com.joinutech.ddbeslibrary.R.id.tv_info).text = data
                },
                onItemClick = { position, _, _ ->
                    when (position) {
                        0 -> {
                            val intent = Intent(mContext, DepartmentSetActivity::class.java)
                            intent.putExtras(getIntent())
                            startActivity(intent)
                        }
                        else -> {
                            val intent = Intent(mContext, DepartmentSortActivity::class.java)
                            intent.putExtras(getIntent())
                            startActivityForResult(intent, 10001)
                        }
                    }
                })
        val listView = findViewById<RecyclerView>(R.id.rv_list)
        listView.layoutManager = LinearLayoutManager(this)
        listView.adapter = adapter
    }

    var needRefresh = false
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK && requestCode == 10001) {
            showLog("-->>>排序成功了")
            needRefresh = true
            onBackPressed()
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    override fun onNoDoubleClick(v: View) {
        if (v.id == com.joinutech.ddbeslibrary.R.id.iv_left) {
            onBackPressed()
        }
        super.onNoDoubleClick(v)
    }

    override fun onBackPressed() {
//        super.onBackPressed()
        if (needRefresh) {
            showLog("-->>>排序成功后，需要更新当前部门数据")
            setResult(Activity.RESULT_OK)
        }
        finish()
    }

}