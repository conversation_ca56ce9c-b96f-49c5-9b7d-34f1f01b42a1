package com.joinutech.addressbook.view

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.SelectedMemberAdapter
import com.joinutech.addressbook.constract.SelectedMemberConstract
import com.joinutech.addressbook.databinding.ActivitySelectedMemberBinding
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.Member
import com.joinutech.ddbeslibrary.utils.*
import javax.inject.Inject
import javax.inject.Named

/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName: SelectedMemberActivity
 * @Desc: 批量操作人员页面
 * @Author:
 * @Leader: Ke
 * @CreateTime: 2020/3/20 10:18 - zhaoyy
 */
class SelectedMemberActivity(override val contentViewResId: Int = R.layout.activity_selected_member) 
    : MyUseBindingActivity<ActivitySelectedMemberBinding>() {

    private lateinit var adapter: SelectedMemberAdapter
    private var list: ArrayList<Member> = arrayListOf()
    private var type = "move"
    private var selectMemberName = ""
    private var selectMember: Member? = null
    private var depName = ""
    private var depHead = "未设置人员"
    private var num = 0
    private var isClickNoHead = false
    private var createId = ""
    private var companyId: String = ""
    private var depId = "0"
    @Inject
    @field:Named(AddressbookUtil.SELECTEDMEMBER_PRESENTER)
    lateinit var presenter: SelectedMemberConstract.SelectedMemberPresenter

    override fun initImmersion() {
        setPageTitle("请选择成员")
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
        dealIntentContent()
    }

    override fun initView() {
        showToolBarLine()
        whiteStatusBarBlackFont()
        DaggerAddressbookComponent.builder().build().inject(this)
        initMmeberShow()
         binding.rvDeptMemberList.layoutManager = LinearLayoutManager(this)

    }

        override fun initLogic() {
        super.initLogic()
        adapter = SelectedMemberAdapter(mContext!!, list, type)
        binding.rvDeptMemberList.adapter = adapter
        setMemberData()
        binding.nextText.setOnClickListener(this)
            binding.noSetDepHead.setOnClickListener(this)
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivitySelectedMemberBinding {
        return ActivitySelectedMemberBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return false
    }

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.nextText -> {
                dealNext()
            }
            R.id.noSetDepHead -> {
                //取消此部门的负责人
                clickNoSetDepHead()
            }
        }
    }


    private fun dealIntentContent() {
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("type"))) {
                type = intent.getStringExtra("type") ?: ""
            }
            if (type == "move" || type == "leave") {
                if (intent.getIntExtra("num", 0) != 0) {
                    num = intent.getIntExtra("num", 0)
                }
                if (!intent.getStringExtra("createId").isNullOrBlank()) {
                    createId = intent.getStringExtra("createId") ?: ""
                }
            } else if (type == "depheadset") {
                if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("depHead"))) {
                    depHead = intent.getStringExtra("depHead") ?: ""
                }
            }
            if (intent.getSerializableExtra("memberList") != null)
                list = intent.getSerializableExtra("memberList") as ArrayList<Member>
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("companyId"))) {
                companyId = intent.getStringExtra("companyId") ?: ""
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("depId"))) {
                depId = intent.getStringExtra("depId") ?: ""
            }
        }
    }

    private fun initMmeberShow() {
        when (type) {
            "move" -> {
                //移动人员
                binding.nextText.text = "下一步"
            }
            "leave" -> {
                binding.nextText.text = "请离"
                binding.nextText.setTextColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.colorFF333333))
            }
            "depheadset" -> {
                binding.nextText.visibility = View.GONE
                binding.noSetDepHead.visibility = View.VISIBLE
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun setMemberData() {
        //处理人员列表显示
        if (list.isEmpty()) {
            setShowEmptyView(true)
        } else {
            setShowEmptyView(false)
            binding.listNumText.text = "当前部门共有${list.size}个成员"
            for (item in list) {
                if (item.name == depHead) {
                    item.positionId = "1"
                } else item.positionId = "0"
            }
            adapter.setSourceList(list)
        }
        if (type == "depheadset") {
            //当前部门没设置上级负责人
            if (depHead == "未设置人员") setNoSetHead()
            adapter.setClickListener(object : SelectedMemberAdapter.ItemClickListener {
                override fun onItemClick(position: Int) {
                    isClickNoHead = false
                    selectMemberName = list[position].name
                    selectMember = Member(list[position].headimg, list[position].name,
                            "1", list[position].userId, "",
                            0)

                    binding.itemConfirmIv.visibility = View.GONE
                    dealComplete()
                }
            })
        }
    }

    private fun clickNoSetDepHead() {
        for (item in list) {
            if (item.positionId == "1") {
                item.positionId = "0"
                adapter.notifyDataSetChanged()
            }
        }
        setNoSetHead()
        dealComplete()
    }

    //当前是未设置负责人
    private fun setNoSetHead() {
        binding.itemConfirmIv.visibility = View.VISIBLE
        isClickNoHead = true
        selectMemberName = "未设置人员"
        selectMember = null
    }

    private fun dealComplete() {
        val intent = Intent()
        if (selectMember != null) {
            intent.putExtra("userId", selectMember!!.userId)
            intent.putExtra("depHeadIcon", selectMember!!.headimg)
        }
        intent.putExtra("depHead", selectMemberName)
        intent.putExtra("isClickNoHead", isClickNoHead)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    private fun dealNext() {
        if (adapter.getSelectedMember().isEmpty()) {
            ToastUtil.show(mContext!!, "请选择操作的成员")
        } else {
            if (type == "move") {
                moveDepMember()
            } else if (type == "leave") {
                leaveMember()
            }
        }
    }

    private fun leaveMember() {
        for (item in adapter.getSelectedMember()) {
            if (item.userId == createId) {
                ToastUtil.showCustomToast(null, mContext!!, true,
                        "${item.name} 为团队创建者，不可请离")
                return
            }
            if (item.userId == userId) {
                ToastUtil.showCustomToast(null, mContext!!, true,
                        "不可请离自己，请取消选择")
                return
            }
        }
        val content = "你确认要将选中员工请离团队么？\n" +
                "好员工来之不易，请三思"
        val dialog = MyDialog(mContext, 280, 166, content,
                needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0)
        dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
            override fun clickRightBtn() {
                dialog.dismiss()
                leaveMemberAction()
            }
        })
        dialog.show()
    }

    private fun leaveMemberAction() {
        val list = arrayListOf<String>()
        for (i in 0 until adapter.getSelectedMember().size) {
            list.add(adapter.getSelectedMember()[i].userId)
        }
        getLoadingDialog("", false)
        //批量请离员工需要做是否有创建项目员工的判断
        presenter.leaveUserValidate(bindToLifecycle(), accessToken!!, list, companyId, {
            dismissDialog()
            var name = it as String
            if (StringUtils.isNotBlankAndEmpty(name)) {
                if (name.length > 14) {
                    name = name.substring(0, 15) + "等"
                }
                val dialog = MyDialog(mContext!!, 280, 166,
                        "${name}在本团队内有创建的项目，为保证项目的顺利进行，" +
                                "请单独进行请离操作",
                        needBtnConfirm = true, needBtnCancel = false, bgResourceId = 0)
                dialog.setCanceledOnTouchOutside(true)
                dialog.show()
            } else {
                dismissOrgMembers(list)
            }
        }, {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun dismissOrgMembers(list: ArrayList<String>) {
        getLoadingDialog("请离员工", false)
        presenter.leaveUser(bindToLifecycle(), accessToken!!,
                list, companyId, {
            dismissDialog()
            EventBusUtils.sendEvent(
                    EventBusEvent(
                            EventBusAction.REFRESH_ORGCHARTLIST, "leave"))
            finish()
        }, {
            dismissDialog()
            ToastUtil.show(mContext!!, it)
        })
    }

    private fun moveDepMember() {
        val intent = Intent(mContext, OrgDepartmentActivity::class.java)
        intent.putExtra("depName", depName)
        intent.putExtra("type", "selectMember")
        val userIds = arrayListOf<String>()
        for (i in 0 until adapter.getSelectedMember().size) {
            userIds.add(adapter.getSelectedMember()[i].userId)
        }
        intent.putExtra("userIds", userIds)
        intent.putExtra("companyId", companyId)
        intent.putExtra("depId", depId)
        startActivity(intent)
        finish()
    }
}