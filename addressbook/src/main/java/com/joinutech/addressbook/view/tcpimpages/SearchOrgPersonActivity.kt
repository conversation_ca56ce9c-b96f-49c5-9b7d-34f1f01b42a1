package com.joinutech.addressbook.view.tcpimpages

import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.ddbes.library.im.util.FriendCacheHolder
import com.joinutech.addressbook.R
import com.joinutech.addressbook.databinding.ActivitySearchOrgPersonBinding
import com.joinutech.addressbook.view.tcpimpages.madapter.ChoosedPersonListAdapter
import com.joinutech.addressbook.view.tcpimpages.madapter.OrgPersonListAdapter
import com.joinutech.addressbook.view.tcpimpages.util.OrgPersonLetterComparator
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.OrgPersonBean
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.joinutech.ddbeslibrary.utils.*
import java.util.*
import kotlin.collections.ArrayList
import kotlin.collections.HashSet

//提供组织成员供选择
@Route(path = RouteOrg.searchOrgPersonActivity)
class SearchOrgPersonActivity : MyUseBindingActivity<ActivitySearchOrgPersonBinding>() {

    @Autowired
    @JvmField
    var userChoosedList: ArrayList<OrgPersonBean>? = null//跳转到这个页面之前已经被选择的人员id

    @Autowired
    @JvmField
    var isMulty: String = ""//1代表单选，2代表多选

    @Autowired
    @JvmField
    var hasSelectMember : String = ""  // 选择外勤审核人员的时候使用， 之前已经选择过的人员id

    @Autowired
    @JvmField
    var eventCode: String = ""//点击保存时把结果通过eventBus事件发送出去

    @Autowired
    @JvmField
    var isOwner: String = ""//1代表可以选择自己，0代表不可以选择自己

    var currentUserId: String = ""

    @Autowired
    @JvmField
    var title: String = ""

    @Autowired
    @JvmField
    var companyId: String = ""//当前的公司id

    val personDataList: ArrayList<OrgPersonBean> = arrayListOf()//供选择的人员列表，接口获取的数据
    val personChoosedList: ArrayList<OrgPersonBean> = arrayListOf()//展示已经选择人员列表的集合

    val personTotalList: ArrayList<OrgPersonBean> = arrayListOf()//临时存储所有的人员，方便筛选时候使用；

    private var logoutUserIds: HashSet<String> = hashSetOf()

    override val contentViewResId: Int
        get() = R.layout.activity_search_org_person

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivitySearchOrgPersonBinding {
        return ActivitySearchOrgPersonBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openArouterReceive(): Boolean {
        return true
    }

    override fun initImmersion() {
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)

        setPageTitle(title)
        setRightTitle("保存", this)
        binding.searchInclueLayout.cancel.visibility = View.GONE
        binding.searchInclueLayout.search.hint = "输入要搜索的用户名"
        binding.searchInclueLayout.delete.setImageResource(com.joinutech.ddbeslibrary.R.drawable.del_img)
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        binding.chooseTotalIv.isSelected = false
        binding.chooseTotalIv.setOnClickListener(this)
        if(isMulty=="2"){//多选
            binding.chooseTotalCl.visibility=View.VISIBLE
        }else{//单选
            binding.chooseTotalCl.visibility=View.GONE
        }
        //初始化两个列表
        initPersonListRecycler()
        initPersonChoosedRecycler()

        currentUserId = UserHolder.getUserId() ?: ""
        logoutUserIds = FriendCacheHolder.getMyLogoutFriendIds()

    }

    private lateinit var personListAdapter: OrgPersonListAdapter
    private lateinit var choosedListAdapter: ChoosedPersonListAdapter
    private lateinit var layoutManager: LinearLayoutManager

    //初始化供选择的人员列表
    private fun initPersonListRecycler() {
        layoutManager = LinearLayoutManager(this)
        binding.personListRecycler.layoutManager = layoutManager
        personListAdapter = OrgPersonListAdapter(this, personDataList)
        binding.personListRecycler.adapter = personListAdapter
    }

    //初始化展示已选择人员的列表
    private fun initPersonChoosedRecycler() {
        binding.choosedListRecycler.layoutManager = LinearLayoutManager(
            this,
            LinearLayoutManager.HORIZONTAL, false
        )
        choosedListAdapter = ChoosedPersonListAdapter(this, personChoosedList)
        binding.choosedListRecycler.adapter = choosedListAdapter
    }

    //搜索事件处理
    private var keyword = ""
    private fun initSearchAction() {
        binding.searchInclueLayout.search.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                keyword = s?.toString() ?: ""
                personDataList.clear()
                if (StringUtils.isNotBlankAndEmpty(keyword)) {
                    binding.searchInclueLayout.delete.visibility = View.VISIBLE
                    binding.chooseTotalCl.visibility = View.GONE
                    val temp = personTotalList.filter { it.name.contains(keyword) }
                    personDataList.addAll(temp)
                } else {
                    keyword = ""
                    binding.searchInclueLayout.delete.visibility = View.INVISIBLE
                    if (isMulty=="2") {
                        binding.chooseTotalCl.visibility = View.VISIBLE
                    }
                    personDataList.addAll(personTotalList)
                }
                if (personDataList.isNullOrEmpty()) {
                    showNoData()
                } else {
                    binding.emptyLayoutView.hide()
                    binding.contentTotalLl.visibility = View.VISIBLE
                    personListAdapter.setKeyWord(keyword)
                    personListAdapter.notifyDataSetChanged()
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        })
    }


    override fun initLogic() {
        getOrgPersonsData()

        initListener()
        initSearchAction()
    }

    private fun initListener() {

        binding.searchInclueLayout.delete.setOnClickListener(this)

        //点击item
        personListAdapter.setItemClickListener(object :
            OrgPersonListAdapter.OnClickOrgPersonListener {
            override fun onClick(view: View?, position: Int) {
                val clickBean = personDataList.get(position)
                if (logoutUserIds.contains(clickBean.userId)) {
                    //该用户已经注销了
                    ToastUtil.show(this@SearchOrgPersonActivity, "该用户已经注销账号")
                    return
                }

                if (isOwner == "0") {
                    //不可以选择自己
                    if (clickBean.userId == currentUserId) {
                        ToastUtil.show(this@SearchOrgPersonActivity, "不可以选择自己")
                        return
                    }
                }

                clickBean.isSelect = !clickBean.isSelect
                if (isMulty == "1" && clickBean.isSelect == true) {
                    //单选,且是选中的话,有可能是通过关键字筛选出的集合，所以遍历时使用personTotalList集合
                    personTotalList.filter { it.isSelect == true }.forEach {
                        if (it.userId != clickBean.userId) {
                            it.isSelect = false
                        }
                    }
                    personChoosedList.clear()
                } else {
                    //多选的话不做处理了
                }
                personListAdapter.notifyDataSetChanged()
                updateBottomRecyclerShow(userId = clickBean.userId)

            }
        })

    }

    //加载数据
    fun getOrgPersonsData() {
        showLoading()
        AddressbookService.searchDepMember(companyId, "")
            .compose(bindToLifecycle())
            .compose(ErrorTransformer.getInstance())
            .subscribe(object : BaseSubscriber<List<SearchMemberBean>>() {
                override fun onError(ex: ApiException) {
                    hideLoading()
                    showNoData()
                    ToastUtil.show(mContext!!, ex.message)
                }

                override fun onComplete() {}
                override fun onNext(list: List<SearchMemberBean>?) {
                    hideLoading()
                    if (list != null) {
                        if (list.isNotEmpty()) {
                            binding.emptyLayoutView.hide()
                            binding.contentTotalLl.visibility = View.VISIBLE
                            keyword = ""
                            personDataList.clear()
                            //接口接收到的数据模型前人写的太复杂，这里重新建立简单的模型
                            personDataList.addAll(list.map { memberBean ->
                                OrgPersonBean().apply {
                                    headimg = memberBean.headimg
                                    initial = memberBean.initial
                                    name = memberBean.name
                                    positionLevel = memberBean.positionLevel
                                    positionName = memberBean.positionName
                                    serialVersionUID = memberBean.serialVersionUID
                                    userId = memberBean.userId
                                }
                            })
                            Collections.sort(personDataList, OrgPersonLetterComparator())
                            personTotalList.clear()
                            personTotalList.addAll(personDataList)
                            initBottomRecyclerShow()
                            initPersonRecyclerShow()

                        } else {
                            showNoData()
                        }
                    } else {
                        showNoData()
                    }
                }

            })
    }

    private fun showNoData() {
        personDataList.clear()
        binding.contentTotalLl.visibility = View.GONE
        binding.emptyLayoutView.setContent("未找到符合条件的结果")
        binding.emptyLayoutView.show()
    }

    private fun initBottomRecyclerShow() {
        if (personDataList.isNullOrEmpty()) {
            binding.choosedListContainLl.visibility = View.GONE
            return
        }
        if (userChoosedList.isNullOrEmpty()) {
            binding.choosedListContainLl.visibility = View.GONE
        } else {
            binding.choosedListContainLl.visibility = View.VISIBLE
            personChoosedList.clear()
            userChoosedList?.forEach { personBean ->
                personDataList.firstOrNull { it.userId == personBean.userId }?.let {
                    personChoosedList.add(it)
                }
            }
            choosedListAdapter.notifyDataSetChanged()
        }
    }

    //根据参数userId是否为空，分别处理两种情况；
    private fun updateBottomRecyclerShow(userId: String) {
        if (StringUtils.isNotBlankAndEmpty(userId)) {
            //单击一条的时候
            val clickPerson=personDataList.firstOrNull { it.userId==userId }
            if(clickPerson?.isSelect==false){
                //取消选中
                if (personChoosedList.contains(clickPerson)) {
                    personChoosedList.remove(clickPerson)
                }
            }else{
                //选中
                if (!personChoosedList.contains(clickPerson)) {
                    personChoosedList.add(clickPerson!!)
                }
            }
        } else {
            //全选的时候
            val choosedTempList = personDataList.filter { it.isSelect == true }
            personChoosedList.clear()
            personChoosedList.addAll(choosedTempList)
        }

        if (personChoosedList.isEmpty()) {
            binding.choosedListContainLl.visibility = View.GONE
        } else {
            binding.choosedListContainLl.visibility = View.VISIBLE
            choosedListAdapter.notifyDataSetChanged()
        }
    }

    private fun initPersonRecyclerShow() {
        if (personDataList.isNullOrEmpty()) {
            return
        }
        val idList = personChoosedList.map { it.userId }
        personDataList.forEach {
            if (idList.contains(it.userId)) {
                it.isSelect = true
            }
        }

        Log.i("moon", "hasSelectMember....${hasSelectMember}")
        if (StringUtils.isNotBlankAndEmpty(hasSelectMember)){
            personDataList.forEach {
                if (it.userId == hasSelectMember){
                    it.isSelect = true
                }
            }
        }

        personListAdapter.notifyDataSetChanged()
    }

    //点击事件
    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            com.joinutech.ddbeslibrary.R.id.tv_toolbar_right -> {//点击保存
                val resultIdString = GsonUtil.toJson(personChoosedList)

                EventBusUtils.sendEvent(
                    EventBusEvent(
                        "outAttendChecker",
                        personChoosedList
                    )
                )

                EventBusUtils.sendEvent(
                    EventBusEvent(
                        eventCode,
                        resultIdString
                    )
                )
                finish()
            }
            R.id.choose_total_iv -> {//点击全选
                if (binding.chooseTotalIv.isSelected) {
                    //取消全选
                    personDataList.forEach {
                        it.isSelect = false
                    }
                    binding.chooseTotalIv.isSelected = false
                } else {
                    //执行全选
                    personDataList.forEach {
                        it.isSelect = true
                    }
                    if (isOwner == "0") {
                        personDataList.firstOrNull { it.userId == currentUserId }?.isSelect = false
                    }
                    binding.chooseTotalIv.isSelected = true
                }
                personListAdapter.notifyDataSetChanged()
                updateBottomRecyclerShow(userId = "")

            }
            R.id.delete -> {
                binding.searchInclueLayout.search.setText("")
            }
        }
    }


}