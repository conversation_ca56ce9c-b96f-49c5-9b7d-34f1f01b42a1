package com.joinutech.addressbook.view

import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.FriendSelectSearchAdapter
import com.joinutech.addressbook.databinding.ActivityFriendSelectListWithSearchBinding
import com.joinutech.addressbook.viewModel.FriendSelectViewModel
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.FriendSelectBean
import com.joinutech.ddbeslibrary.bean.UserInfo
import com.joinutech.ddbeslibrary.utils.*
//import kotlinx.android.synthetic.main.activity_friend_select_list_with_search.*
//import kotlinx.android.synthetic.main.search_include_layout.*

/**
 * @Description: 好友选择页
 * @Author: hjr
 * @Time: 2020/2/24 11:14
 * @packageName: com.joinutech.addressbook.view
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
@Route(path = RouteOrg.WithDataListSelectActivity)
class WithDataListSelectActivity : MyUseBindingActivity<ActivityFriendSelectListWithSearchBinding>() {

    override val contentViewResId: Int = R.layout.activity_friend_select_list_with_search
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityFriendSelectListWithSearchBinding {
        return ActivityFriendSelectListWithSearchBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = true

    private var currentTitle = ""

    //是否是多选的类型
    private var isMultiType = false

    /**
     * 进入要选择的类型
     *  0 创建群聊
     *  1转换群组创建者
     *  2群组加新人但需要返回的是选择人的信息
     *  3移出人员
     *  4 底部有已选人员列表及带复选的添加IM音视频聊天
     */
    private var enterType = 0
    private var personList = arrayListOf<FriendSelectBean>()
    private var searchPersonList = arrayListOf<FriendSelectBean>()
    private var noSelectUserIds = arrayListOf<String>()
    private lateinit var adapter: FriendSelectSearchAdapter
    private lateinit var viewModel: FriendSelectViewModel
    private var outPersonList = arrayListOf<UserInfo>()
    private var searchTextValue = ""
    private var isAllSelect = false

    override fun initImmersion() {
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("title"))) {
                currentTitle = intent.getStringExtra("title")!!
                setPageTitle(currentTitle)
            } else {
                setPageTitle("我的好友")
            }
            isMultiType = intent.getBooleanExtra("isMultiType", false)
            if (isMultiType) {
                setRightTitleColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color999999),
                        "完成", View.OnClickListener { })
            }
            enterType = intent.getIntExtra("type", 0)
            if (!intent.getStringArrayListExtra("noSelectUserIds").isNullOrEmpty()) {
                noSelectUserIds = intent.getStringArrayListExtra("noSelectUserIds")!!
            }
            if (intent.getSerializableExtra("outPersonList") != null) {
                outPersonList = intent.getSerializableExtra("outPersonList") as ArrayList<UserInfo>
            }
        }
        showBackButton(com.joinutech.ddbeslibrary.R.drawable.back_grey)
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        showToolBarLine()
        binding.rvList.layoutManager = LinearLayoutManager(this)
        viewModel = getModel(FriendSelectViewModel::class.java)
        if (isMultiType && enterType != 3) {
            binding.llSelectAll.visibility = View.VISIBLE
        } else {
            binding.llSelectAll.visibility = View.GONE
        }
        binding.mainSideBar.visibility = View.GONE
    }

    override fun initLogic() {
        super.initLogic()
        getObservable()
        adapter = FriendSelectSearchAdapter(mContext!!, personList, isMultiType)
        adapter.setTextIndex(false)
        adapter.setClickListener(object : ItemClickListener {
            override fun onItemClick(position: Int) {
                if (isMultiType) {
                    if (adapter.mData[position].select) {
                        adapter.mData[position].select = !adapter.mData[position].select
                        adapter.notifyItemChanged(position)
                    } else {
                        adapter.mData[position].select = !adapter.mData[position].select
                        adapter.notifyItemChanged(position)
                    }
                    if (enterType != 3) isAllSelectEvent(adapter.mData)
                    if (!adapter.mData.isNullOrEmpty()) {
                        viewModel.setRightCompleteColorShow(adapter.mData)
                    }
                } else {
                    val friendSelectBean = adapter.mData[position]
                    val singleSelectUserId = friendSelectBean.userId
                    val intent = Intent()
                    if (enterType == 3) {
                        intent.putExtra("userId", UserInfo(friendSelectBean.userId,
                                friendSelectBean.avatar, friendSelectBean.name))
                    } else intent.putExtra("userId", singleSelectUserId)
                    setResult(Activity.RESULT_OK, intent)
                    finish()
                }
            }

        })
        adapter.setNoSelectUsers(noSelectUserIds)
        binding.rvList.adapter = adapter
        if (enterType == 3) {
            binding.searchBar.search.hint = "请输入要搜索的用户名"
        } else binding.searchBar.search.hint = "搜索好友"
        binding.searchBar.search.imeOptions = EditorInfo.IME_ACTION_SEARCH
        binding.searchBar.search.inputType = EditorInfo.TYPE_CLASS_TEXT
        binding.searchBar.delete.visibility = View.GONE
        binding.searchBar.search.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (s != null && s.isNotEmpty()) {
                    binding.searchBar.delete.visibility = View.VISIBLE
                } else {
                    binding.searchBar.delete.visibility = View.GONE
                }
            }
        })
        binding.searchBar.search.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH ||
                    (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER)) {
                searchTextValue = binding.searchBar.search.text.toString().trim()
                if (StringUtils.isNotBlankAndEmpty(searchTextValue)) {
                    getFriendList()
                }
                return@setOnEditorActionListener true
            }
            false
        }
        binding.llSelectAll.setOnClickListener(this)
        binding.searchBar.delete.setOnClickListener(this)
        binding.searchBar.cancel.setOnClickListener(this)
        getFriendList()
    }

    private fun isAllSelectEvent(list: ArrayList<FriendSelectBean>) {
        if (!list.isNullOrEmpty()) {
            var currentValue = true
            list.forEach {
                if (!it.select) {
                    currentValue = false
                    return@forEach
                }
            }
            if (isAllSelect != currentValue) {
                isAllSelect = currentValue
                binding.llSelectAll.isSelected = isAllSelect
            }
        }
    }

    private fun getObservable() {
        viewModel.getFriendListSuccessObservable.observe(this, Observer {
            dismissDialog()
            if (it.isNullOrEmpty()) {
                setShowNoContent(true)
            } else {
                setShowNoContent(false)
                if (StringUtils.isNotBlankAndEmpty(searchTextValue)) {
                    searchPersonList = it
                    if (isMultiType && enterType != 3) {
                        isAllSelectEvent(searchPersonList)
                    }
                    showOldSelectStatus()
                    adapter.setSourceList(searchPersonList)
                } else {
                    personList = it
                    adapter.setSourceList(personList)
                }
            }
        })
        viewModel.rightCompleteColorShowObservable.observe(this, Observer {
            if (it) {
                setRightTitleColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.main_blue),
                        "完成", this)
            } else {
                setRightTitleColor(CommonUtils.getColor(mContext!!, com.joinutech.ddbeslibrary.R.color.color999999),
                        "完成", View.OnClickListener { })
            }
        })
    }

    private fun showOldSelectStatus() {
        if (!personList.isNullOrEmpty() && !searchPersonList.isNullOrEmpty()) {
            if (searchPersonList.isNullOrEmpty()) {
                personList.forEach {
                    searchPersonList.forEach { innerBean ->
                        run {
                            if (innerBean.userId == it.userId) {
                                innerBean.select = it.select
                            }
                        }
                    }
                }
            }
        }
    }

    private fun getFriendList() {
        getLoadingDialog("", false)
        viewModel.setPersonList(outPersonList, searchTextValue)
    }

    private fun setShowNoContent(b: Boolean) {
        if (b) {
            binding.layoutEmptyLayout.visibility = View.VISIBLE
            binding.clHaveDataLayout.visibility = View.GONE
        } else {
            binding.layoutEmptyLayout.visibility = View.GONE
            binding.clHaveDataLayout.visibility = View.VISIBLE
        }
    }

    override fun onBackPressed() {
        if (currentFocus != null && currentFocus?.windowToken != null)
            (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                    .hideSoftInputFromWindow(currentFocus!!
                            .windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        super.onBackPressed()
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            tv_rightTitle -> {
                completeEvent()
            }
            binding.searchBar.delete -> {
                if (StringUtils.isNotBlankAndEmpty(searchTextValue))
                    cancelSearchEvent()
            }
            binding.searchBar.cancel -> {
                if (StringUtils.isNotBlankAndEmpty(searchTextValue))
                    cancelSearchEvent()
            }
            binding.llSelectAll -> {
                isAllSelect = !isAllSelect
                binding.llSelectAll.isSelected = isAllSelect
                if (StringUtils.isNotBlankAndEmpty(searchTextValue)) {
                    dealAllSelectList(searchPersonList, isAllSelect)
                    if (!searchPersonList.isNullOrEmpty()) {
                        viewModel.setRightCompleteColorShow(searchPersonList)
                    }
                } else {
                    dealAllSelectList(personList, isAllSelect)
                    if (!personList.isNullOrEmpty()) {
                        viewModel.setRightCompleteColorShow(personList)
                    }
                }
            }
        }
    }

    private fun dealAllSelectList(list: ArrayList<FriendSelectBean>, allSelect: Boolean) {
        if (!list.isNullOrEmpty()) {
            if (isAllSelect) {
                list.forEach {
                    it.select = allSelect
                }
            } else {
                list.forEach {
                    it.select = allSelect
                }
            }
        }
        adapter.setSourceList(list)
    }

    private fun cancelSearchEvent() {
        binding.searchBar.search.setText("")
        try {
            if (currentFocus != null && currentFocus?.windowToken != null)
                (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
                        .hideSoftInputFromWindow(currentFocus!!
                                .windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        } catch (e: java.lang.Exception) {

        }

        if (!searchPersonList.isNullOrEmpty()) {
            personList.forEach {
                searchPersonList.forEach { innerBean ->
                    run {
                        if (innerBean.userId == it.userId) {
                            it.select = innerBean.select
                        }
                    }
                }
            }
            searchPersonList.clear()
        }
        adapter.setSourceList(personList)
        if (isMultiType && enterType != 3) isAllSelectEvent(personList)
        if (!personList.isNullOrEmpty()) {
            viewModel.setRightCompleteColorShow(personList)
        }
    }

    private fun completeEvent() {
        val selectPersonUserIdSet = hashSetOf<String>()

        if (!searchPersonList.isNullOrEmpty()) {
            val userIds = arrayListOf<FriendSelectBean>()
            searchPersonList.forEach {
                if (it.select) {
                    userIds.add(it)
                    selectPersonUserIdSet.add(it.userId)
                }
            }
            if (userIds.isNullOrEmpty()) {
                personList.forEach {
                    userIds.forEach { innerBean ->
                        run {
                            if (innerBean.userId == it.userId) {
                                it.select = innerBean.select
                            }
                        }
                    }
                }
            }
        }
        if (!personList.isNullOrEmpty()) {

            val selectPersonUserSet = hashSetOf<UserInfo>()
            personList.forEach {
                if (!noSelectUserIds.isNullOrEmpty()) {
                    if (it.select && it.userId !in noSelectUserIds) {
                        selectPersonUserIdSet.add(it.userId)
                        selectPersonUserSet.add(UserInfo(it.userId, it.avatar, it.name))
                    }
                } else {
                    if (it.select) {
                        selectPersonUserIdSet.add(it.userId)
                        selectPersonUserSet.add(UserInfo(it.userId, it.avatar, it.name))
                    }
                }
            }
            if (enterType == 3) {
                val dialog = MyDialog(mContext!!, 278, 144,
                        "确定要将选中的用户移出群组吗？",
                        needBtnConfirm = true, needBtnCancel = true, bgResourceId = 0)
                dialog.setCanceledOnTouchOutside(true)
                dialog.show()
                dialog.setBtnRightlistener(object : MyDialog.BtnRightListener {
                    override fun clickRightBtn() {
                        dialog.dismiss()
                        val intent = Intent()
                        intent.putExtra("selectUserIds", selectPersonUserIdSet)
                        setResult(Activity.RESULT_OK, intent)
                        finish()
                    }
                })
                dialog.setBtnLeftListener(object : MyDialog.BtnLeftListener {
                    override fun clickLeftBtn() {
                        dialog.dismiss()
                    }
                })
            } else {
                val intent = Intent()
                if (enterType == 2) {
                    intent.putExtra("selectUserIds", selectPersonUserSet)
                } else {
                    intent.putExtra("selectUserIds", selectPersonUserIdSet)
                }
                setResult(Activity.RESULT_OK, intent)
            }
        }
        if (enterType != 3) finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (!personList.isNullOrEmpty()) personList.clear()
        if (!searchPersonList.isNullOrEmpty()) searchPersonList.clear()
    }

}