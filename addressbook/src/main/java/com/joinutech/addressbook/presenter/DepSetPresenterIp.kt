package com.joinutech.addressbook.presenter

import com.joinutech.addressbook.constract.DepSetConstarct
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.OrgDepHeadBean
import com.joinutech.ddbeslibrary.bean.ValidateAttendanceBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

class DepSetPresenterIp @Inject internal constructor() : DepSetConstarct.DepSetPresenter {

    @Inject
    @field:Named(AddressbookUtil.DEPSET_MODULE)
    lateinit var module: DepSetConstarct.DepSetModule

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun changeDepName(life: LifecycleTransformer<Result<Any>>, token: String,
                               companyId: String, deptId: String, depName: String,
                               onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.changeDepName(life, token, companyId, deptId, depName, onSuccess, onError)
    }

    override fun checkAttendGroup(life: LifecycleTransformer<Result<ValidateAttendanceBean>>, data: Any, onSuccess: (ValidateAttendanceBean) -> Unit, onError: (String) -> Unit) {
        module.checkAttendGroup(life, data, onSuccess, onError)
    }

    override fun deleteDep(life: LifecycleTransformer<Result<Any>>, token: String,
                           companyId: String, deptId: String,
                           onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.deleteDep(life, token, companyId, deptId, onSuccess, onError)
    }

    override fun showDepHeadDetail(life: LifecycleTransformer<Result<OrgDepHeadBean>>,
                                   token: String, deptId: String, companyId: String,
                                   onSuccess: (OrgDepHeadBean) -> Unit, onError: (String) -> Unit) {
        module.showDepHeadDetail(life, token, deptId, companyId, onSuccess, onError)
    }

    override fun deleteDepHeadPosition(life: LifecycleTransformer<Result<Any>>,
                                       token: String, positionId: String,
                                       onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.deleteDepHeadPosition(life, token, positionId, onSuccess, onError)
    }
}