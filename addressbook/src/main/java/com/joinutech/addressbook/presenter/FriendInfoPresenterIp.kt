package com.joinutech.addressbook.presenter

import com.joinutech.addressbook.constract.FriendInfoConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

class FriendInfoPresenterIp @Inject internal constructor()
    : FriendInfoConstract.
FriendInfoPresenter {

    @Inject
    @field:Named(AddressbookUtil.ORGMEMBERINFO_MODULE)
    lateinit var module: FriendInfoConstract.FriendInfoModule

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun getCompanyUserInfo(life: LifecycleTransformer<Result<Any>>,
                             token: String, companyId: String, userId: String,
                             onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.getCompanyUserInfo(life, token = token, companyId = companyId, userId = userId, onSuccess = onSuccess, onError = onError)
    }

    override fun leaveUser(life: LifecycleTransformer<Result<Any>>,
                           token: String,
                           users: ArrayList<String>, companyId: String,
                           onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.leaveUser(life, token, users, companyId, onSuccess, onError)
    }

    override fun deleteFriend(life: LifecycleTransformer<Result<Any>>, token: String,
                              targetUserId: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.deleteFriend(life, token, targetUserId, onSuccess, onError)
    }

    override fun volidate(life: LifecycleTransformer<Result<Any>>, token: String,
                          userId: String, type: Int,
                          onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.volidate(life, token, userId, type, onSuccess, onError)
    }

    override fun leaveUserValidate(life: LifecycleTransformer<Result<Any>>,
                                   token: String, users: ArrayList<String>,
                                   companyId: String, onSuccess: (Any) -> Unit,
                                   onError: (String) -> Unit) {
        module.leaveUserValidate(life, token, users, companyId, onSuccess, onError)
    }
}