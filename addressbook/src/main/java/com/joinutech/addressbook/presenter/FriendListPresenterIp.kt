package com.joinutech.addressbook.presenter

import com.joinutech.addressbook.constract.FriendListConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.ContactModel
import com.joinutech.ddbeslibrary.bean.LetterComparator
import com.ddbes.library.im.imtcp.dbbean.FriendBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.trello.rxlifecycle3.LifecycleTransformer
import java.util.*
import javax.inject.Inject
import javax.inject.Named

class FriendListPresenterIp @Inject internal constructor():FriendListConstract.FriendListPresenter{

    @Inject
    @field:Named(AddressbookUtil.FRIENDLIST_MODULE)
    lateinit var module:FriendListConstract.FriendListModule
    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun getFriendList(life: LifecycleTransformer<Result<List<FriendBean>>>,
                               token: String, content:String,
                               onSuccess: (List<FriendBean>) -> Unit, onError: (String) -> Unit) {
//        module.getFriendList(life, token, content,onSuccess, onError)
    }

    override fun dealFriendList(it: List<FriendBean>): ArrayList<ContactModel> {
        val list = ArrayList<ContactModel>()
        if (it.isNotEmpty()){
            for (item in it){
                val bean :ContactModel = if (StringUtils.isNotBlankAndEmpty(item.remark)){
                    ContactModel(item.remark,item.avatar)
                }else {
                    ContactModel(item.name,item.avatar)
                }
                bean.userId = item.userId
                bean.relation = item.relation
                list.add(bean)
            }
        }
        Collections.sort(list, LetterComparator())
        return list
    }
}