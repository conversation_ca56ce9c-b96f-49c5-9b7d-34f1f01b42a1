package com.joinutech.addressbook.presenter

import com.joinutech.addressbook.constract.AddressbookConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.*
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

/**
 *<AUTHOR>
 *@date 2018/11/19
 */
class AddressbookPresenterIp @Inject internal constructor() : AddressbookConstract.AddressbookPresenter {

    @Inject
    @field:Named(AddressbookUtil.ADDR_MODULE)
    lateinit var module: AddressbookConstract.AddressbookModule

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun commitPersonInfo(life: LifecycleTransformer<Result<PersonInfoBean>>,
                                  name: String, type: String, onSuccess: (PersonInfoBean) -> Unit,
                                  onError: (String) -> Unit) {
        module.commitPersonInfo(life, name, type, onSuccess, onError)
    }

//    override fun getCompanys(life: LifecycleTransformer<Result<CompanyListBean>>, token: String, userId: String, onSuccess: (CompanyListBean) -> Unit, onError: (String) -> Unit) {
//        module.getCompanys(life, token, userId, onSuccess, onError)
//    }

    override fun getIndustry(life: LifecycleTransformer<Result<List<JobChoiceBean>>>, token: String, type: String, onSuccess: (List<JobChoiceBean>) -> Unit, onError: (String) -> Unit) {
        module.getIndustry(life, token, type, onSuccess, onError)
    }

    override fun getCompanyTypes(life: LifecycleTransformer<Result<List<CompanyTypeBean>>>, token: String, onSuccess: (List<CompanyTypeBean>) -> Unit, onError: (String) -> Unit) {
        module.getCompanyTypes(life, token, onSuccess, onError)
    }

    override fun creatCompany(life: LifecycleTransformer<Result<Any>>, token: String, bean: Any, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.creatCompany(life, token, bean, onSuccess, onError)
    }

    override fun modifyCompany(life: LifecycleTransformer<Result<Any>>, token: String, bean: Any, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.modifyCompany(life, token, bean, onSuccess, onError)
    }

    override fun changeOrgCreator(life: LifecycleTransformer<Result<Any>>, bean: Any, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.changeOrgCreator(life, bean, onSuccess, onError)
    }

    override fun queryCompanyById(life: LifecycleTransformer<Result<CompanyBean>>, token: String, id: String, onSuccess: (CompanyBean) -> Unit, onError: (String) -> Unit) {
        module.queryCompanyById(life, token, id, onSuccess, onError)
    }

    override fun queryValidate(life: LifecycleTransformer<Result<Any>>, token: String, id: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.queryValidate(life, token, id, onSuccess, onError)
    }

    override fun getMainCompany(life: LifecycleTransformer<Result<CompanyBean>>, token: String, onSuccess: (CompanyBean) -> Unit, onError: (String) -> Unit) {
        module.getMainCompany(life, token, onSuccess, onError)
    }

    override fun disbandCompany(life: LifecycleTransformer<Result<Any>>, token: String, id: String, phone: String, code: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.disbandCompany(life, token, id, phone, code, onSuccess, onError)
    }

    override fun quitCompany(life: LifecycleTransformer<Result<Any>>, token: String, id: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.quitCompany(life, token, id, onSuccess, onError)
    }

    override fun creatGroup(life: LifecycleTransformer<Result<Any>>, token: String, userId: ArrayList<String>, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.creatGroup(life, token, userId, onSuccess, onError)
    }

    override fun queryGroup(life: LifecycleTransformer<Result<List<GroupBean>>>, token: String, onSuccess: (List<GroupBean>) -> Unit, onError: (String) -> Unit) {
        module.queryGroup(life, token, onSuccess, onError)
    }

    override fun orgPermissionList(life: LifecycleTransformer<Result<List<OrgPermissionListBean>>>,
                                   token: String, companyId: String,
                                   onSuccess: (List<OrgPermissionListBean>) -> Unit,
                                   onError: (String) -> Unit) {
        module.orgPermissionList(life, token, companyId, onSuccess, onError)
    }

    override fun editManager(life: LifecycleTransformer<Result<Any>>, token: String,
                             companyId: String, map: Map<String, Any>,
                             onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.editManager(life, token, companyId, map, onSuccess, onError)
    }

    override fun addManager(life: LifecycleTransformer<Result<Any>>,
                            token: String, companyId: String, map: Map<String, Any>,
                            onSuccess: (Any) -> Unit,
                            onError: (String) -> Unit) {
        module.addManager(life, token, companyId, map, onSuccess, onError)
    }

    override fun deleteManager(life: LifecycleTransformer<Result<Any>>, token: String,
                               companyId: String, managerId: String,
                               onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.deleteManager(life, token, companyId, managerId, onSuccess, onError)
    }

}