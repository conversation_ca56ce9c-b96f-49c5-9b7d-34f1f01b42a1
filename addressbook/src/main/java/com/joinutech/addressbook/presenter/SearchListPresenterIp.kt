package com.joinutech.addressbook.presenter

import com.joinutech.addressbook.constract.SearchResultListContract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.CompanyBean
import com.joinutech.ddbeslibrary.bean.FriendSimpleBean
import com.joinutech.ddbeslibrary.bean.PhoneSearchFriendBean
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.ddbes.library.im.imtcp.dbbean.FriendBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

class SearchListPresenterIp @Inject internal constructor() :
        SearchResultListContract.SearchResultListPresenter {

    @Inject
    @field:Named(AddressbookUtil.SEARCHLIST_MODULE)
    lateinit var module: SearchResultListContract.SearchResultListModule

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun searchResult(life: LifecycleTransformer<Result<List<CompanyBean>>>,
                              token: String, content: String,
                              onSuccess: (List<CompanyBean>) -> Unit, onError: (String) -> Unit) {
        module.searchResult(life, token, content, onSuccess, onError)
    }

    override fun dealMemberResult(life: LifecycleTransformer<Result<List<SearchMemberBean>>>,
                                  token: String, content: String, companyId: String,
                                  onSuccess: (List<SearchMemberBean>) -> Unit,
                                  onError: (String) -> Unit) {
        module.dealMemberResult(life, token, content, companyId, onSuccess, onError)
    }

    override fun searchPhoneResult(life: LifecycleTransformer<Result<PhoneSearchFriendBean>>,
                                   token: String, content: String,
                                   onSuccess: (PhoneSearchFriendBean) -> Unit,
                                   onError: (String) -> Unit) {
        module.searchPhoneResult(life, token, content, onSuccess, onError)
    }

    override fun searchFriendList(life: LifecycleTransformer<Result<List<FriendBean>>>,
                                  token: String, content: String,
                                  onSuccess: (List<FriendBean>) -> Unit, onError: (String) -> Unit) {
        module.searchFriendList(life, token, content, onSuccess, onError)
    }

    override fun searchExternalFriendList(life: LifecycleTransformer<Result<List<FriendSimpleBean>>>,
                                          token: String, content: String,
                                          onSuccess: (List<FriendSimpleBean>) -> Unit,
                                          onError: (String) -> Unit) {
        module.searchExternalFriendList(life, token, content, onSuccess, onError)
    }
}