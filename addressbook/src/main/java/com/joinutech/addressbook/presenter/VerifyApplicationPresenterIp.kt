package com.joinutech.addressbook.presenter

import com.joinutech.addressbook.constract.VerifyApplicationConstact
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

class VerifyApplicationPresenterIp @Inject internal constructor():VerifyApplicationConstact.VerifyApplicationPresenter {

    @Inject
    @field:Named(AddressbookUtil.VERAPPLICATION_MODULE)
    lateinit var module:VerifyApplicationConstact.VerifyApplicationModule

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun verifyOrgApplication(life: LifecycleTransformer<Result<Int>>,
                                      token: String, companyId: String,
                                      content: String, onSuccess: (Int) -> Unit,
                                      onError: (String) -> Unit) {
        module.verifyOrgApplication(life, token, companyId, content, onSuccess, onError)
    }

    override fun verfyFriendApplication(life: LifecycleTransformer<Result<Any>>,
                                        token: String, userId  : String,
                                        message: String,
                                        onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.verfyFriendApplication(life, token, userId, message, onSuccess, onError)
    }
}