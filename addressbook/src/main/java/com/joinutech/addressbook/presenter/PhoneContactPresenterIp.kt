package com.joinutech.addressbook.presenter

import android.annotation.SuppressLint
import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.provider.ContactsContract
import com.joinutech.addressbook.R
import com.joinutech.addressbook.constract.PhoneContactConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.ContactModel
import com.joinutech.ddbeslibrary.bean.PhoneContactDataBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.trimEmpty
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

class PhoneContactPresenterIp @Inject internal constructor() :
        PhoneContactConstract.PhoneContactPresenter {

    @Inject
    @field:Named(AddressbookUtil.PHONECONTACTLIST_MODULE)
    lateinit var module: PhoneContactConstract.PhoneContactModule
    private lateinit var mContext: Context
    private val PHONES_PROJECTION = arrayOf(
            ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
            ContactsContract.CommonDataKinds.Phone.NUMBER,
            ContactsContract.Contacts.Photo.PHOTO_ID,
            ContactsContract.CommonDataKinds.Phone.CONTACT_ID)

    /**联系人显示名称 */

    private val PHONES_DISPLAY_NAME_INDEX = 0

    /**电话号码 */

    private val PHONES_NUMBER_INDEX = 1

    /**头像ID */

    private val PHONES_PHOTO_ID_INDEX = 2

    /**联系人的ID */

    private val PHONES_CONTACT_ID_INDEX = 3

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun getContactStatusList(life: LifecycleTransformer<Result<List<PhoneContactDataBean>>>,
                                      token: String, phones: ArrayList<String>,
                                      onSuccess: (List<PhoneContactDataBean>) -> Unit,
                                      onError: (String) -> Unit) {
        module.getContactStatusList(life, token, phones, onSuccess, onError)
    }

    override fun getPhoneContactList(mContext: Context): List<ContactModel> {
        this.mContext = mContext
        return doThings()
    }

    @SuppressLint("Recycle")
    private fun doThings(): List<ContactModel> {
        val mShowModels = ArrayList<ContactModel>()
        val resolver = mContext.contentResolver
        try {
            val cursor: Cursor = mContext.contentResolver.query(
                    ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                    PHONES_PROJECTION, null, null, null)!!
            while (cursor.moveToNext()) {
                val phoneNumber: String = cursor.getString(PHONES_NUMBER_INDEX)
                // 当手机号码为空的或者为空字段 跳过当前循环
                if (StringUtils.isEmpty(phoneNumber)) continue
                val phoneName: String = cursor.getString(PHONES_DISPLAY_NAME_INDEX)
                val phoneInfoBean = ContactModel(phoneName, "")
                //得到联系人ID
                val contactid = cursor.getLong(PHONES_CONTACT_ID_INDEX)
                //得到联系人头像ID
                val photoid = cursor.getLong(PHONES_PHOTO_ID_INDEX)
                //得到联系人头像Bitamp
                //photoid 大于0 表示联系人有头像 如果没有给此人设置头像则给他一个默认的
                val contactPhoto = if (photoid > 0) {
                    getPhoto(resolver, contactid.toString())
                } else {
                    BitmapFactory.decodeResource(mContext.resources, com.joinutech.ddbeslibrary.R.drawable.icon_userdefault)
                }
                phoneInfoBean.bitmap = contactPhoto
                if (StringUtils.isNotBlankAndEmpty(phoneNumber)) {
                    phoneInfoBean.phoneNum = phoneNumber.trimEmpty(phoneNumber)
                }
                mShowModels.add(phoneInfoBean)
            }
            cursor.close()
        } catch (e: Exception) {
            //权限被禁止
            ToastUtil.show(mContext, "获取联系人列表失败")
        }
        return mShowModels
    }

    override fun dealReturnPhoneContactData(list: List<PhoneContactDataBean>,
                                            mShowModels: List<ContactModel>,
                                            userId: String?):
            ArrayList<ContactModel> {
        val contactList = mShowModels as ArrayList
        if (list.isNotEmpty()) {
            for (item1 in contactList) {
                for (item2 in list) {
                    if (item2.mobile == item1.phoneNum) {
                        item1.logo = item2.headimg
                        item1.userId = item2.id
                        if (item2.id == userId) {
                            //说明是自己
                            item1.status = 3
                        } else {
                            item1.status = item2.status
                        }
                    }
                }
            }
        }
        return contactList
    }

    /**
     * 获取联系人的图片
     */
    fun getPhoto(contentResolver: ContentResolver, contactId: String): Bitmap? {
        var photo: Bitmap? = null
        val dataCursor = contentResolver.query(ContactsContract.Data.CONTENT_URI,
                arrayOf("data15"),
                ContactsContract.Data.CONTACT_ID + "=?" + " AND "
                        + ContactsContract.Data.MIMETYPE + "='" +
                        ContactsContract.CommonDataKinds.Photo.CONTENT_ITEM_TYPE + "'",
                arrayOf((contactId).toString()), null)
        if (dataCursor != null) {
            if (dataCursor.count > 0) {
                dataCursor.moveToFirst()
                val bytes = dataCursor.getBlob(dataCursor.getColumnIndex("data15"))
                if (bytes != null) {
                    photo = BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
                }
            }
            dataCursor.close()
        }
        return photo
    }
}