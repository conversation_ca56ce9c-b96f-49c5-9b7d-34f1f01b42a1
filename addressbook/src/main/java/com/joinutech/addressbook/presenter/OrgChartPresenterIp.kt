package com.joinutech.addressbook.presenter

import com.joinutech.addressbook.constract.OrgChartConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.OrgChartBean
import com.joinutech.ddbeslibrary.bean.OrgChartDepBean
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named
import com.joinutech.ddbeslibrary.request.Result

class OrgChartPresenterIp  @Inject internal constructor():OrgChartConstract.OrgChartPresenter {

    @Inject
    @field:Named(AddressbookUtil.ORGCHART_MODULE)
    lateinit var module:OrgChartConstract.OrgChartModule

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun getOrgChartDataResult(life: LifecycleTransformer<Result<OrgChartBean>>,
                                       token: String, companyId: String, deptId : String,
                                       onSuccess: (OrgChartBean) -> Unit, onError: (String) -> Unit) {
            module.getOrgChartDataResult(life,token, companyId,deptId, onSuccess, onError)
    }

    override fun addChildDep(life: LifecycleTransformer<Result<Any>>,
                             token: String, companyId: String, deptId : String,
                             name: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
            module.addChildDep(life, token, companyId, deptId, name, onSuccess, onError)
    }
}