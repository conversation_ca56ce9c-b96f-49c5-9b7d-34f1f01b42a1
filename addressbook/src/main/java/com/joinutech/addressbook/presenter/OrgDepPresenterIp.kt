package com.joinutech.addressbook.presenter

import com.joinutech.addressbook.constract.OrgDepConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.OrgChartDepBean
import com.joinutech.ddbeslibrary.bean.ValidateAttendanceBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

class OrgDepPresenterIp @Inject internal constructor() : OrgDepConstract.OrgDepPresenter {

    @Inject
    @field:Named(AddressbookUtil.ORGCHARTDEP_MODULE)
    lateinit var module: OrgDepConstract.OrgDepModule

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun getOrgChartDepResult(life: LifecycleTransformer<Result<OrgChartDepBean>>,
                                      token: String, companyId: String, deptId: String,
                                      onSuccess: (OrgChartDepBean) -> Unit, onError: (String) -> Unit) {
        module.getOrgChartDepResult(life, token, companyId, deptId, onSuccess, onError)
    }

    override fun changeMmeberDep(life: LifecycleTransformer<Result<Any>>,
                                 token: String, companyId: String, deptId: String, userIds: ArrayList<String>,
                                 onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.changeMmeberDep(life, token, companyId, deptId, userIds, onSuccess, onError)
    }

    override fun changeAboveDep(life: LifecycleTransformer<Result<Any>>,
                                token: String, companyId: String,
                                deptId: String, targetDeptId  : String,
                                onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.changeAboveDep(life, token, companyId, deptId, targetDeptId, onSuccess, onError)
    }

    override fun addChildDep(life: LifecycleTransformer<Result<Any>>,
                             token: String, companyId: String, deptId: String,
                             name: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.addChildDep(life, token, companyId, deptId, name, onSuccess, onError)
    }

    override fun validateAttendanceGroup(life: LifecycleTransformer<Result<ValidateAttendanceBean>>,
                                         token: String,
                                         companyId: String, deptId: String,
                                         userIds: ArrayList<String>, onSuccess: (ValidateAttendanceBean) -> Unit,
                                         onError: (String) -> Unit) {
        module.validateAttendanceGroup(life, token, companyId, deptId, userIds, onSuccess, onError)
    }

    override fun updateAttendanceGroup(life: LifecycleTransformer<Result<Any>>, token: String,
                                       companyId: String, deptId: String, userIds: ArrayList<String>,
                                       ischange: Int, onSuccess: (Any) -> Unit,
                                       onError: (String) -> Unit) {
        module.updateAttendanceGroup(life, token, companyId, deptId,
                userIds, ischange, onSuccess, onError)
    }
}
