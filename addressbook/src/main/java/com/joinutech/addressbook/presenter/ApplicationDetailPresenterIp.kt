package com.joinutech.addressbook.presenter

import com.joinutech.addressbook.constract.ApplicationDetailConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

class ApplicationDetailPresenterIp @Inject internal constructor() :
        ApplicationDetailConstract.ApplicationDetailPresenter {

    @Inject
    @field:Named(AddressbookUtil.APPLICATIONDETAIL_MODULE)
    lateinit var module: ApplicationDetailConstract.ApplicationDetailModule

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun orgAgreeUserJoin(life: LifecycleTransformer<Result<Any>>,
                                  token: String, map: Any,
                                  onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.orgAgreeUserJoin(life, token, map, onSuccess, onError)
    }

    override fun rejectUserJoin(life: LifecycleTransformer<Result<Any>>,
                                token: String, map: Any,
                                onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.rejectUserJoin(life, token, map, onSuccess, onError)
    }

    override fun agreeAddFriend(life: LifecycleTransformer<Result<Any>>,
                                token: String, applyId: String, opt: Int,
                                onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.agreeAddFriend(life, token, applyId, opt, onSuccess, onError)
    }

    override fun rejectAddFriendJoin(life: LifecycleTransformer<Result<Any>>,
                                     token: String, applyId: String, opt: Int,
                                     onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.rejectAddFriendJoin(life, token, applyId, opt, onSuccess, onError)
    }

    override fun getApplicationDetailData(life: LifecycleTransformer<Result<Any>>,
                                          token: String, paramId: String, type: Int,
                                          onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.getApplicationDetailData(life, token, paramId, type, onSuccess, onError)
    }
}