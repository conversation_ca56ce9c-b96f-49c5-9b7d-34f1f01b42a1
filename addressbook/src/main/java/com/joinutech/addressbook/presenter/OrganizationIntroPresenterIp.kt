package com.joinutech.addressbook.presenter

import com.joinutech.addressbook.constract.OrganizationIntroConstact
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.CompanyBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

class OrganizationIntroPresenterIp @Inject internal  constructor():
        OrganizationIntroConstact.OrganizationIntroPresenter {

    @Inject
    @field:Named(AddressbookUtil.ORGINTRO_MODULE)
    lateinit var module:OrganizationIntroConstact.OrganizationIntroMoudle

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun getOrganizationInfo(life: LifecycleTransformer<Result<CompanyBean>>,
                                     token: String, companyId: String,
                                     onSuccess: (CompanyBean) -> Unit, onError: (String) -> Unit) {
        module.getOrganizationInfo(life,token, companyId, onSuccess, onError)
    }
    override fun volidate(life: LifecycleTransformer<Result<Int>>,
                          token: String, companyId: String,
                          onSuccess: (Int) -> Unit, onError: (String) -> Unit) {
        module.volidate(life, token, companyId, onSuccess, onError)
    }
}