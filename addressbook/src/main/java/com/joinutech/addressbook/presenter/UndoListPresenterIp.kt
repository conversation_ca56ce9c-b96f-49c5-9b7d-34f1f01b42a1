package com.joinutech.addressbook.presenter

import com.joinutech.addressbook.constract.UndoListConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.UndoBean
import com.trello.rxlifecycle3.LifecycleTransformer
import com.joinutech.ddbeslibrary.request.Result
import javax.inject.Inject
import javax.inject.Named

class UndoListPresenterIp @Inject internal constructor()
    : UndoListConstract.UndoListPresenter {

    @Inject
    @field:Named(AddressbookUtil.UNDOLIST_MODULE)
    lateinit var module: UndoListConstract.UndoListModule

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun getUndoList(life: LifecycleTransformer<Result<UndoBean>>, status: Int, page: Int, size: Int,
                             onSuccess: (UndoBean) -> Unit, onError: (String) -> Unit) {
        module.getUndoList(life, status, page, size, onSuccess, onError)
    }
}