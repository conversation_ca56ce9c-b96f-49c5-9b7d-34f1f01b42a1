package com.joinutech.addressbook.presenter

import com.joinutech.addressbook.constract.ApplicationListConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.CompanyUndoBean
import com.joinutech.ddbeslibrary.bean.UndoBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

class ApplicationListPresenterIp @Inject internal constructor()
    : ApplicationListConstract.ApplicationListPresenter {
    @Inject
    @field:Named(AddressbookUtil.APPLICATIONLIST_MODULE)
    lateinit var module: ApplicationListConstract.ApplicationListModule
    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun getFriendUndoList(life: LifecycleTransformer<Result<UndoBean>>,
                                   token: String, type: Int, page: Int,
                                   size: Int, onSuccess: (UndoBean) -> Unit,
                                   onError: (String) -> Unit) {
        module.getFriendUndoList(life, token, type, page, size, onSuccess, onError)
    }

    override fun getCompanyUndoList(life: LifecycleTransformer<Result<CompanyUndoBean>>,
                                    token: String,  page: Int,
                                    size: Int,companyId:String, onSuccess: (CompanyUndoBean) -> Unit,
                                    onError: (String) -> Unit) {
        module.getCompanyUndoList(life, token, page, size, companyId,onSuccess, onError)
    }
}