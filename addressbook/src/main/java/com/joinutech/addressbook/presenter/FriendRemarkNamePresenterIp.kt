package com.joinutech.addressbook.presenter

import com.joinutech.addressbook.constract.FriendRemarkNameConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

class FriendRemarkNamePresenterIp @Inject internal constructor():
        FriendRemarkNameConstract.FriendRemarkNamePresenter {
    @Inject
    @field:Named(AddressbookUtil.REMARKNAME_MODULE)
    lateinit var module:FriendRemarkNameConstract.FriendRemarkNameModule
    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun remarkFriend(life: LifecycleTransformer<Result<Any>>,
                              token: String, remark: String,
                              targetUserId  : String,
                              onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        module.remarkFriend(life, token, remark, targetUserId, onSuccess, onError)
    }
}