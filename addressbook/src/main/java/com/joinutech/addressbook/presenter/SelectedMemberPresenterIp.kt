package com.joinutech.addressbook.presenter

import com.joinutech.addressbook.constract.SelectedMemberConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

class SelectedMemberPresenterIp @Inject internal constructor():SelectedMemberConstract.SelectedMemberPresenter {

    @Inject
    @field:Named(AddressbookUtil.SELECTEDMEMBER_MODULE)
    lateinit var module:SelectedMemberConstract.SelectedMemberModule

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    override fun leaveUser(life: LifecycleTransformer<Result<Any>>,
                           token: String, users: ArrayList<String>,
                           companyId: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
            module.leaveUser(life, token, users, companyId, onSuccess, onError)
    }

    override fun changeDepHead(life: LifecycleTransformer<Result<Any>>,
                               token: String, deptId : String,positionId  : String, userId  : String,
                               positionName:String,type:Int,
                               companyId: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
            module.changeDepHead(life, token, deptId, positionId, userId,
                    positionName, type, companyId, onSuccess, onError)
    }

    override fun leaveUserValidate(life: LifecycleTransformer<Result<Any>>,
                                   token: String, users: ArrayList<String>,
                                   companyId: String, onSuccess: (Any) -> Unit,
                                   onError: (String) -> Unit) {
            module.leaveUserValidate(life, token, users, companyId, onSuccess, onError)
    }
}