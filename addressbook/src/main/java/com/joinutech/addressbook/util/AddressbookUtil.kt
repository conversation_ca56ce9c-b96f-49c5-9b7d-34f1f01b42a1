package com.joinutech.addressbook.util

/**
 *<AUTHOR>
 *@date 2018/11/20
 */
object AddressbookUtil{
    const val ADDR_PRESENTER="addr_presenter"
    const val ADDR_MODULE="addr_module"
    const val APPLICATIONDETAIL_PRESENTER = "application_detail_presenter"
    const val APPLICATIONDETAIL_MODULE = "application_detail_module"
    const val ORGMEMBERINFO_PRESENTER = "orgmemberinfo_presenter"
    const val ORGMEMBERINFO_MODULE = "orgmemberinfo_module"
    const val UNDOLIST_PRESENTER = "undolist_presenter"
    const val UNDOLIST_MODULE = "undolist_module"
    const val ORGINTRO_PRESENTER = "orgintro_presenter"
    const val ORGINTRO_MODULE = "orgintro_module"
    const val VERAPPLICATION_PRESENTER = "verapplication_presenter"
    const val VERAPPLICATION_MODULE = "verapplication_module"
    const val SEARCHLIST_PRESENTER = "searchlist_presenter"
    const val SEARCHLIST_MODULE = "searchlist_module"
    const val ORGCHART_PRESENTER = "orgchart_presenter"
    const val ORGCHART_MODULE = "orgchart_module"
    const val ORGCHARTDEP_PRESENTER = "orgchartdep_presenter"
    const val ORGCHARTDEP_MODULE = "orgchartdep_module"
    const val SELECTEDMEMBER_PRESENTER = "selectedmember_presenter"
    const val SELECTEDMEMBER_MODULE = "selectedmember_module"
    const val DEPSET_PRESENTER = "depset_presenter"
    const val DEPSET_MODULE = "depset_module"
    const val REMARKNAME_PRESENTER = "remark_presenter"
    const val REMARKNAME_MODULE = "remark_module"
    const val FRIENDLIST_PRESENTER = "friendlist_presenter"
    const val FRIENDLIST_MODULE = "friendlist_module"
    const val PHONECONTACTLIST_PRESENTER = "phonecontact_presenter"
    const val PHONECONTACTLIST_MODULE = "phonecontact_module"
    const val APPLICATIONLIST_PRESENTER = "application_presenter"
    const val APPLICATIONLIST_MODULE = "application_module"
    const val SELECT_VISIT_MODULE = "select_visit_module"
    const val BATCH_PROCESS_MODULE = "batch_process_module"
//    const val WORK_NOTICE_SET_MODULE = "work_notice_set_module"
    //修改密码图片验证
    const val CHANGE_PASSWORD_REPOSITORY = "change_password_repository"
    const val ADD_ORG_EXTERNAL_CONTACT_MODULE = "add_org_external_contact_module"
    const val ORG_EXTERNAL_CONTACT_LIST_MODULE = "org_external_contact_list_module"
    const val ORG_EXTERNAL_CONTACT_DETAIL_MODULE = "org_external_contact_detail_module"
    const val GET_COOPERATION_APPLICATION_MODULE = "get_cooperation_application_module"
    const val GROUP_LIST_MODULE = "group_list_module"
}