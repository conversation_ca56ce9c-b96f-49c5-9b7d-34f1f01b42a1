package com.joinutech.addressbook.module

import com.joinutech.ddbeslibrary.bean.CooperationApplicationDetailBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/10 10:08
 * @packageName: com.joinutech.addressbook.module
 * @Company: JoinuTech
 */
class CooperationApplicationModule @Inject internal constructor() {

    //根据id查询合作申请详情
    fun getCooperationApplicationDetail(life: LifecycleTransformer<Result<
            CooperationApplicationDetailBean>>,
                                        token: String, id :String,
                                        onSuccess: (CooperationApplicationDetailBean) -> Unit,
                                        onError: (String) -> Unit) {
        AddressbookService.getCooperationApplicationDetail(token,id)
                .compose(life)
                .compose(ErrorTransformer.getInstance<CooperationApplicationDetailBean>())
                .subscribe(object : BaseSubscriber<CooperationApplicationDetailBean>(){
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: CooperationApplicationDetailBean?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
    //处理合作申请
    fun dealCooperationApplication(life: LifecycleTransformer<Result<Any>>,
                                        token: String, code:Int,id :String, onSuccess: (Any) -> Unit,
                                        onError: (String) -> Unit) {
        AddressbookService.dealCooperationApplication(token,id,code)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>(){
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}