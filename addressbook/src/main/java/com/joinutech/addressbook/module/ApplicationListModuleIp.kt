package com.joinutech.addressbook.module

import com.joinutech.addressbook.constract.ApplicationListConstract
import com.joinutech.ddbeslibrary.bean.CompanyUndoBean
import com.joinutech.ddbeslibrary.bean.UndoBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

class ApplicationListModuleIp @Inject internal constructor()
    : ApplicationListConstract.ApplicationListModule {

    override fun getFriendUndoList(life: LifecycleTransformer<Result<UndoBean>>,
                                   token: String, type: Int, page: Int, size: Int,
                                   onSuccess: (UndoBean) -> Unit,
                                   onError: (String) -> Unit) {
        AddressbookService.getUndoList(type, page, size)
//        UserService.getFriendUndoList(token,type, page, size)
                .compose(life)
                .compose(ErrorTransformer.getInstance<UndoBean>())
                .subscribe(object : BaseSubscriber<UndoBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: UndoBean?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun getCompanyUndoList(life: LifecycleTransformer<Result<CompanyUndoBean>>,
                                    token: String, page: Int,
                                    size: Int, companyId: String, onSuccess: (CompanyUndoBean) -> Unit,
                                    onError: (String) -> Unit) {
        AddressbookService.getCompanyUndoList(token, page, size, companyId)
                .compose(life)
                .compose(ErrorTransformer.getInstance<CompanyUndoBean>())
                .subscribe(object : BaseSubscriber<CompanyUndoBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: CompanyUndoBean?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}