package com.joinutech.addressbook.module

import com.ddbes.library.im.imtcp.Logger
import com.joinutech.addressbook.constract.OrgDepConstract
import com.joinutech.ddbeslibrary.bean.OrgChartDepBean
import com.joinutech.ddbeslibrary.bean.ValidateAttendanceBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.joinutech.ddbeslibrary.utils.XUtil
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

class OrgDepModuleIp @Inject internal constructor() : OrgDepConstract.OrgDepModule {

    override fun getOrgChartDepResult(life: LifecycleTransformer<Result<OrgChartDepBean>>,
                                      token: String, companyId: String, deptId: String,
                                      onSuccess: (OrgChartDepBean) -> Unit, onError: (String) -> Unit) {
        AddressbookService.quitChartDep(token, companyId, deptId)
                .compose(life)
                .compose(ErrorTransformer.getInstance<OrgChartDepBean>())
                .subscribe(object : BaseSubscriber<OrgChartDepBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(t: OrgChartDepBean?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    //批量转移
    override fun changeMmeberDep(life: LifecycleTransformer<Result<Any>>,
                                 token: String, companyId: String, deptId: String,
                                 userIds: ArrayList<String>, onSuccess: (Any) -> Unit,
                                 onError: (String) -> Unit) {
        val map = hashMapOf<String, Any>()
        map["companyId"] = companyId
        map["deptId"] = deptId
        map["userIds"] = userIds
        Logger.i("验证批量","===companyId=${companyId}---")
        Logger.i("验证批量","===deptId=${deptId}---")
        Logger.i("验证批量","===userIds=${userIds.size}---")
        Logger.i("验证批量","===userIds.get(0)=${userIds.get(0)}---")
        AddressbookService.changeMemberDep(token, map)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                        XUtil.errorLogToPrint(ex)
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun validateAttendanceGroup(life: LifecycleTransformer<Result<ValidateAttendanceBean>>,
                                         token: String, companyId: String, deptId: String,
                                         userIds: ArrayList<String>,
                                         onSuccess: (ValidateAttendanceBean) -> Unit,
                                         onError: (String) -> Unit) {
        val map = hashMapOf<String, Any>()
        map["companyId"] = companyId
        map["deptId"] = if (deptId == "1") {
            "0"
        } else {
            deptId
        }
        map["userIds"] = userIds
        AddressbookService.validateAttendanceGroup(token, map)
                .compose(life)
                .compose(ErrorTransformer.getInstance<ValidateAttendanceBean>())
                .subscribe(object : BaseSubscriber<ValidateAttendanceBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(t: ValidateAttendanceBean?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun updateAttendanceGroup(life: LifecycleTransformer<Result<Any>>,
                                       token: String, companyId: String, deptId: String,
                                       userIds: ArrayList<String>, ischange: Int,
                                       onSuccess: (Any) -> Unit, onError: (String) -> Unit) {

        val map = hashMapOf<String, Any>()
        map["companyId"] = companyId
        map["deptId"] = if (deptId == "1") {
            "0"
        } else {
            deptId
        }
        map["userIds"] = userIds
        map["ischange"] = ischange
        AddressbookService.updateAttendanceGroup(token, map)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun changeAboveDep(life: LifecycleTransformer<Result<Any>>,
                                token: String, companyId: String, deptId: String,
                                targetDeptId: String,
                                onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        val map = hashMapOf<String, Any>()
        map["deptId"] = deptId // 当前部门 上级部门？应该用当前部门id
        map["targetDeptId"] = targetDeptId // 目标部门id
        AddressbookService.changeAboveDep(token, companyId, map)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun addChildDep(life: LifecycleTransformer<Result<Any>>,
                             token: String, companyId: String, deptId: String,
                             name: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        val map = hashMapOf<String, Any>()
        map["name"] = name
        map["companyId"] = companyId
//        AddressbookService.addChildDep(token, deptId, map)
//                .compose(life)
//                .compose(ErrorTransformer.getInstance<Any>())
//                .subscribe(object : BaseSubscriber<Any>() {
//                    override fun onError(ex: ApiException) {
//                        onError.invoke(ex.message)
//                    }
//
//                    override fun onComplete() {
//                    }
//
//                    override fun onNext(t: Any?) {
//                        onSuccess.invoke(t!!)
//                    }
//
//                })
    }
}