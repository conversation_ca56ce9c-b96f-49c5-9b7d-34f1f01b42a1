package com.joinutech.addressbook.module

import com.ddbes.library.im.util.UserService
import com.joinutech.ddbeslibrary.bean.ExternalContactDetailBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/2 11:08
 * @packageName: com.joinutech.addressbook.module
 * @Company: JoinuTech
 */
class OrgExternalContactDetailModuel @Inject internal constructor() {

    //得到外部联系人详情
    fun getExternalContactDetail(life: LifecycleTransformer<Result<ExternalContactDetailBean>>,
                                 companyId: String, userId: String, token: String,
                                 onSuccess: (ExternalContactDetailBean) -> Unit,
                                 onError: (ApiException) -> Unit) {
        AddressbookService.getExternalContactDetail(companyId, userId, token)
                .compose(life)
                .compose(ErrorTransformer.getInstance<ExternalContactDetailBean>())
                .subscribe(object : BaseSubscriber<ExternalContactDetailBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex)
                    }

                    override fun onComplete() {}

                    override fun onNext(t: ExternalContactDetailBean?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    //发送邀请消息
    fun sendApplicationMsg(life: LifecycleTransformer<Result<Any>>,
                           companyId: String, phone: String, token: String,
                           onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.sendApplicationMsg(companyId, phone, token)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {}

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    //好友添加校验
    fun validate(life: LifecycleTransformer<Result<Any>>, token: String,
                 userId: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        val map = hashMapOf<String, Any>()
        map["type"] = 4
        map["userId"] = userId
        UserService.volidate(token, map)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}