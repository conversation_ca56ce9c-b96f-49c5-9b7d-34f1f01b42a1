package com.joinutech.addressbook.module

import com.ddbes.library.im.util.UserService
import com.joinutech.addressbook.constract.ApplicationDetailConstract
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

class ApplicationDetailModuleIp @Inject internal constructor() : ApplicationDetailConstract.ApplicationDetailModule {
    //同意加入团队tcp
    override fun orgAgreeUserJoin(life: LifecycleTransformer<Result<Any>>,
                                  token: String,
                                  map: Any,
                                  onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.orgAgreeUserJoin(token, map)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun rejectUserJoin(life: LifecycleTransformer<Result<Any>>,
                                token: String, map: Any,
                                onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.rejectUserJoin(token, map)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    //tcp同意加好友
    override fun agreeAddFriend(life: LifecycleTransformer<Result<Any>>,
                                token: String, applyId: String, opt: Int,
                                onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        UserService.agreeAddFriend(token, applyId, opt)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    //tcp忽略好友请求
    override fun rejectAddFriendJoin(life: LifecycleTransformer<Result<Any>>,
                                     token: String, applyId: String, opt: Int,
                                     onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        UserService.rejectAddFriendJoin(token, applyId, opt)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    //获取申请详情tcp
    override fun getApplicationDetailData(life: LifecycleTransformer<Result<Any>>,
                                          token: String, paramId: String, status: Int,
                                          onSuccess: (Any) -> Unit,
                                          onError: (String) -> Unit) {
        AddressbookService.getApplicationDetailData(token, status, paramId)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}