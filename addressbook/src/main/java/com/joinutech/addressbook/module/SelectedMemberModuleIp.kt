package com.joinutech.addressbook.module

import com.joinutech.addressbook.constract.SelectedMemberConstract
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

class SelectedMemberModuleIp @Inject internal constructor() :
        SelectedMemberConstract.SelectedMemberModule {

    override fun leaveUser(life: LifecycleTransformer<Result<Any>>, token: String,
                           users: List<String>, companyId: String,
                           onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.leaveUser(token, users, companyId)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun leaveUserValidate(life: LifecycleTransformer<Result<Any>>, token: String,
                                   users: ArrayList<String>, companyId: String,
                                   onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.leaveUserValidate(token, users, companyId)
                .compose(life)
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun changeDepHead(life: LifecycleTransformer<Result<Any>>,
                               token: String, deptId: String, positionId: String, userId: String,
                               positionName: String, type: Int,
                               companyId: String, onSuccess: (Any) -> Unit,
                               onError: (String) -> Unit) {
        val map = hashMapOf<String, Any>()
        map["companyId"] = companyId
        map["deptId"] = deptId
        map["positionName"] = positionName
        map["userId"] = userId
        map["type"] = type
        if (StringUtils.isEmpty(positionId)) {
            AddressbookService.addDepHead(token, map)
                    .compose(life)
                    .compose(ErrorTransformer.getInstance())
                    .subscribe(object : BaseSubscriber<Any>() {
                        override fun onError(ex: ApiException) {
                            onError.invoke(ex.message)
                        }

                        override fun onComplete() {

                        }

                        override fun onNext(t: Any?) {
                            onSuccess.invoke(t!!)
                        }

                    })
        } else {
            map["positionId"] = positionId
            AddressbookService.changeDepHead(token, map)
                    .compose(life)
                    .compose(ErrorTransformer.getInstance())
                    .subscribe(object : BaseSubscriber<Any>() {
                        override fun onError(ex: ApiException) {
                            onError.invoke(ex.message)
                        }

                        override fun onComplete() {

                        }

                        override fun onNext(t: Any?) {
                            onSuccess.invoke(t!!)
                        }

                    })
        }
    }
}