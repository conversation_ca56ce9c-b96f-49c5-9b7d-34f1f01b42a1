package com.joinutech.addressbook.module

import com.joinutech.ddbeslibrary.bean.BatchProcessUndoBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

/**
 * description ： 批量处理待处理信息Module
 * author: 黄洁如
 * date : 2019/10/22
 */
class BatchProcessUndoMsgModule @Inject internal constructor(){

    //批量待处理列表
    fun batchProcessUndoMsgList(life: LifecycleTransformer<Result<BatchProcessUndoBean>>,
                                token: String, companyId:String,
                                onSuccess: (BatchProcessUndoBean) -> Unit,
                                onError: (String) -> Unit) {
        AddressbookService.batchProcessUndoMsgList(token,companyId)
                .compose(life)
                .compose(ErrorTransformer.getInstance<BatchProcessUndoBean>())
                .subscribe(object : BaseSubscriber<BatchProcessUndoBean>(){
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: BatchProcessUndoBean?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    //批量待处理
    fun batchProcessUndoMsg(life: LifecycleTransformer<Result<Any>>,
                                token: String, data:Any,
                                onSuccess: (Any) -> Unit,
                                onError: (String) -> Unit) {
        AddressbookService.batchProcessUndoMsg(token,data)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>(){
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}