package com.joinutech.addressbook.module

import com.joinutech.addressbook.constract.DepSetConstarct
import com.joinutech.ddbeslibrary.bean.OrgDepHeadBean
import com.joinutech.ddbeslibrary.bean.ValidateAttendanceBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

class DepSetModuleIp @Inject internal constructor() : DepSetConstarct.DepSetModule {

    override fun changeDepName(life: LifecycleTransformer<Result<Any>>,
                               token: String, companyId: String, deptId: String,
                               depName: String,
                               onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        val map = hashMapOf<String, Any>()
        map["name"] = depName
        AddressbookService.changeDepName(token, companyId, deptId, map)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun checkAttendGroup(life: LifecycleTransformer<Result<ValidateAttendanceBean>>, data: Any,
                                  onSuccess: (ValidateAttendanceBean) -> Unit, onError: (String) -> Unit) {
        AddressbookService.validateAttendanceGroup2(data)
                .compose(life)
                .compose(ErrorTransformer.getInstance<ValidateAttendanceBean>())
                .subscribe(object : BaseSubscriber<ValidateAttendanceBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(t: ValidateAttendanceBean?) {
                        if (t != null) {
                            onSuccess.invoke(t)
                        } else {
                            onError.invoke("")
                        }
                    }

                })
    }


    override fun deleteDep(life: LifecycleTransformer<Result<Any>>, token: String,
                           companyId: String, deptId: String,
                           onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.deleteDep(token, companyId, deptId)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun showDepHeadDetail(life: LifecycleTransformer<Result<OrgDepHeadBean>>,
                                   token: String, deptId: String, companyId: String,
                                   onSuccess: (OrgDepHeadBean) -> Unit, onError: (String) -> Unit) {
        AddressbookService.showDeaHeadDetail(token, companyId, deptId)
                .compose(life)
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<OrgDepHeadBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(t: OrgDepHeadBean?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun deleteDepHeadPosition(life: LifecycleTransformer<Result<Any>>,
                                       token: String, positionId: String,
                                       onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.deleteDepHeadPosition(token, positionId)
                .compose(life)
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}