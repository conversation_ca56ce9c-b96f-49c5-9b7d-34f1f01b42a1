package com.joinutech.addressbook.module

import com.joinutech.addressbook.constract.SearchResultListContract
import com.joinutech.ddbeslibrary.bean.CompanyBean
import com.joinutech.ddbeslibrary.bean.FriendSimpleBean
import com.joinutech.ddbeslibrary.bean.PhoneSearchFriendBean
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.ddbes.library.im.imtcp.dbbean.FriendBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.ddbes.library.im.util.UserService
import com.marktoo.lib.cachedweb.LogUtil
import com.trello.rxlifecycle3.LifecycleTransformer

class SearchListModuleIp : SearchResultListContract.SearchResultListModule {

    override fun dealMemberResult(life: LifecycleTransformer<Result<List<SearchMemberBean>>>,
                                  token: String, keyword: String, companyId: String,
                                  onSuccess: (List<SearchMemberBean>) -> Unit,
                                  onError: (String) -> Unit) {
        AddressbookService.searchDepMember(companyId, keyword)
                .compose(life)
                .compose(ErrorTransformer.getInstance<List<SearchMemberBean>>())
                .subscribe(object : BaseSubscriber<List<SearchMemberBean>>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: List<SearchMemberBean>?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun searchResult(life: LifecycleTransformer<Result<List<CompanyBean>>>,
                              token: String, content: String,
                              onSuccess: (List<CompanyBean>) -> Unit, onError: (String) -> Unit) {
        AddressbookService.searchResult(token, content)
                .compose(life)
                .compose(ErrorTransformer.getInstance<List<CompanyBean>>())
                .subscribe(object : BaseSubscriber<List<CompanyBean>>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: List<CompanyBean>?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun searchPhoneResult(life: LifecycleTransformer<Result<PhoneSearchFriendBean>>,
                                   token: String, content: String,
                                   onSuccess: (PhoneSearchFriendBean) -> Unit,
                                   onError: (String) -> Unit) {
        UserService.searchPhone(token, content)
                .compose(life)
                .compose(ErrorTransformer.getInstance<PhoneSearchFriendBean>())
                .subscribe(object : BaseSubscriber<PhoneSearchFriendBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {
                        LogUtil.showLog("网络请求完成")
                    }

                    override fun onNext(t: PhoneSearchFriendBean?) {
                        if (t!!.userId.isNotEmpty()) {
                            onSuccess.invoke(t)
                        } else {
                            onError.invoke("未注册使用担当")
                        }
                    }

                })
    }

    override fun searchFriendList(life: LifecycleTransformer<Result<List<FriendBean>>>,
                                  token: String, content: String,
                                  onSuccess: (List<FriendBean>) -> Unit, onError: (String) -> Unit) {
//        UserService.getFriendList(content)//获取所有好友信息，建议改成本地搜索
//                .compose(life)
//                .compose(ErrorTransformer.getInstance<List<FriendBean>>())
//                .subscribe(object : BaseSubscriber<List<FriendBean>>() {
//                    override fun onError(ex: ApiException) {
//                        onError.invoke(ex.message)
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(t: List<FriendBean>?) {
//                        onSuccess.invoke(t!!)
//                    }
//
//                })
    }

    override fun searchExternalFriendList(life: LifecycleTransformer<Result<List<FriendSimpleBean>>>,
                                          token: String, content: String,
                                          onSuccess: (List<FriendSimpleBean>) -> Unit,
                                          onError: (String) -> Unit) {
//        UserService.searchExternalFriendList(token, content)
//                .compose(life)
//                .compose(ErrorTransformer.getInstance<List<FriendSimpleBean>>())
//                .subscribe(object : BaseSubscriber<List<FriendSimpleBean>>() {
//                    override fun onError(ex: ApiException) {
//                        onError.invoke(ex.message)
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(t: List<FriendSimpleBean>?) {
//                        onSuccess.invoke(t!!)
//                    }
//
//                })
    }
}