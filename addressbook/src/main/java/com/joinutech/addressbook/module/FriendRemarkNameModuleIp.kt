package com.joinutech.addressbook.module

import com.joinutech.addressbook.constract.FriendRemarkNameConstract
import com.ddbes.library.im.util.UserService
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

class FriendRemarkNameModuleIp @Inject internal constructor()
    : FriendRemarkNameConstract.FriendRemarkNameModule {

    override fun remarkFriend(life: LifecycleTransformer<Result<Any>>,
                              token: String, remark: String,
                              targetUserId: String,
                              onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        UserService.remarkFriend(token, remark, targetUserId)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}