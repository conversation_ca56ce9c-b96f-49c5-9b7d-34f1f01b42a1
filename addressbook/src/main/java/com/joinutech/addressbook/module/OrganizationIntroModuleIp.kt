package com.joinutech.addressbook.module

import com.joinutech.addressbook.constract.OrganizationIntroConstact
import com.joinutech.ddbeslibrary.bean.CompanyBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

class OrganizationIntroModuleIp  @Inject internal  constructor()
    :OrganizationIntroConstact.OrganizationIntroMoudle {

    override fun getOrganizationInfo(life: LifecycleTransformer<Result<CompanyBean>>,
                                     token: String, companyId: String,
                                     onSuccess: (CompanyBean) -> Unit, onError: (String) -> Unit) {
        AddressbookService.queryCompanyById(token, companyId)
                .compose(life)
                .compose(ErrorTransformer.getInstance<CompanyBean>())
                .subscribe(object : BaseSubscriber<CompanyBean>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: CompanyBean?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
    override fun volidate(life: LifecycleTransformer<Result<Int>>, token: String,
                          companyId: String, onSuccess: (Int) -> Unit, onError: (String) -> Unit) {
        val map = hashMapOf<String,Any>()
        map["companyId"]=companyId
        AddressbookService.volidate(token,map)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Int>())
                .subscribe(object :BaseSubscriber<Int>(){
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Int?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}