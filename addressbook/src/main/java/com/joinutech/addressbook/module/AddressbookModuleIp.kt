package com.joinutech.addressbook.module

import com.joinutech.addressbook.constract.AddressbookConstract
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.bean.*
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.joinutech.ddbeslibrary.service.PersonService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

/**
 *<AUTHOR>
 *@date 2018/11/19
 */
class AddressbookModuleIp @Inject internal constructor() : AddressbookConstract.AddressbookModule {

    override fun commitPersonInfo(life: LifecycleTransformer<Result<PersonInfoBean>>,
                                  name: String, type: String,
                                  onSuccess: (PersonInfoBean) -> Unit, onError: (String) -> Unit) {
        val data = HashMap<String, Any>()
        data["profession"] = name
        val token = UserHolder.getAccessToken()
        PersonService.commitPersonInfo(data, token)//profession
                .compose(life)
                .compose(ErrorTransformer.getInstance<PersonInfoBean>())
                .subscribe(object : BaseSubscriber<PersonInfoBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {}

                    override fun onNext(t: PersonInfoBean?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun queryMmebersNew(token: String, companyId: String,
                                 deptId: String, life: LifecycleTransformer<Result<OrgImportDeptBean>>,
                                 onSuccess: (OrgImportDeptBean) -> Unit, onError: (String) -> Unit) {
        AddressbookService.queryOrgDept(companyId, deptId)
                .compose(life)
                .compose(ErrorTransformer.getInstance<OrgImportDeptBean>())
                .subscribe(object : BaseSubscriber<OrgImportDeptBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(t: OrgImportDeptBean?) {
                        if (t != null)
                            onSuccess.invoke(t)
                    }

                })
    }

//    override fun getCompanys(life: LifecycleTransformer<Result<CompanyListBean>>,
//                             token: String, userId: String, onSuccess: (CompanyListBean) -> Unit,
//                             onError: (String) -> Unit) {
//        AddressbookService.getCompanies(token, userId)
//                .compose(life)
//                .compose(ErrorTransformer.getInstance<CompanyListBean>())
//                .subscribe(object : BaseSubscriber<CompanyListBean>() {
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onError(ex: ApiException) {
//                        // TODO: 2019/9/20 弹窗后不再toast
//                        if (ex.code != ERROR_RELOGIN) {
//                            onError.invoke(ex.message)
//                        }
//                    }
//
//                    override fun onNext(t: CompanyListBean?) {
//                        if (t != null) onSuccess.invoke(t)
//                        else onError.invoke("请求数据错误")
//                    }
//
//                })
//    }

    override fun getIndustry(life: LifecycleTransformer<Result<List<JobChoiceBean>>>, token: String, type: String, onSuccess: (List<JobChoiceBean>) -> Unit, onError: (String) -> Unit) {
        AddressbookService.getIndustry(token, type).compose(life).compose(ErrorTransformer.getInstance<List<JobChoiceBean>>())
                .subscribe(object : BaseSubscriber<List<JobChoiceBean>>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: List<JobChoiceBean>?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun getCompanyTypes(life: LifecycleTransformer<Result<List<CompanyTypeBean>>>, token: String, onSuccess: (List<CompanyTypeBean>) -> Unit, onError: (String) -> Unit) {
        AddressbookService.getCompanyTypes(token).compose(life).compose(ErrorTransformer.getInstance<List<CompanyTypeBean>>())
                .subscribe(object : BaseSubscriber<List<CompanyTypeBean>>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: List<CompanyTypeBean>?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun creatCompany(life: LifecycleTransformer<Result<Any>>, token: String, bean: Any, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.createCompany(token, bean).compose(life).compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun modifyCompany(life: LifecycleTransformer<Result<Any>>, token: String, bean: Any, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.modifyCompany(token, bean).compose(life).compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun changeOrgCreator(life: LifecycleTransformer<Result<Any>>, bean: Any,
                                  onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.changeOrgCreator(bean)
                .compose(life).compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun queryCompanyById(life: LifecycleTransformer<Result<CompanyBean>>, token: String, id: String, onSuccess: (CompanyBean) -> Unit, onError: (String) -> Unit) {
        AddressbookService.queryCompanyById(token, id).compose(life).compose(ErrorTransformer.getInstance<CompanyBean>())
                .subscribe(object : BaseSubscriber<CompanyBean>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: CompanyBean?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun queryValidate(life: LifecycleTransformer<Result<Any>>, token: String, id: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.queryValidate(token, id).compose(life).compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun getMainCompany(life: LifecycleTransformer<Result<CompanyBean>>, token: String, onSuccess: (CompanyBean) -> Unit, onError: (String) -> Unit) {
        AddressbookService.getMainCompany(token).compose(life).compose(ErrorTransformer.getInstance<CompanyBean>())
                .subscribe(object : BaseSubscriber<CompanyBean>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: CompanyBean?) {
                        onSuccess.invoke(t!!)
                    }
                })
    }

    override fun disbandCompany(life: LifecycleTransformer<Result<Any>>, token: String,
                                id: String, phone: String, code: String,
                                onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.disbandConpany(token, id, phone, code).compose(life).compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun quitCompany(life: LifecycleTransformer<Result<Any>>, token: String, id: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.quitCompany(token, id).compose(life).compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun creatGroup(life: LifecycleTransformer<Result<Any>>, token: String, userId: ArrayList<String>, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.createGroup(token, userId).compose(life).compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun queryGroup(life: LifecycleTransformer<Result<List<GroupBean>>>, token: String, onSuccess: (List<GroupBean>) -> Unit, onError: (String) -> Unit) {
        AddressbookService.getGroup(token).compose(life).compose(ErrorTransformer.getInstance<List<GroupBean>>())
                .subscribe(object : BaseSubscriber<List<GroupBean>>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: List<GroupBean>?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun orgPermissionList(life: LifecycleTransformer<Result<List<OrgPermissionListBean>>>,
                                   token: String, companyId: String,
                                   onSuccess: (List<OrgPermissionListBean>) -> Unit,
                                   onError: (String) -> Unit) {
        AddressbookService.orgPermissionList(token, companyId)
                .compose(life)
                .compose(ErrorTransformer.getInstance<List<OrgPermissionListBean>>())
                .subscribe(object : BaseSubscriber<List<OrgPermissionListBean>>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: List<OrgPermissionListBean>?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun editManager(life: LifecycleTransformer<Result<Any>>, token: String,
                             companyId: String, map: Map<String, Any>,
                             onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.editManager(token, companyId, map)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun addManager(life: LifecycleTransformer<Result<Any>>,
                            token: String, companyId: String, map: Map<String, Any>,
                            onSuccess: (Any) -> Unit,
                            onError: (String) -> Unit) {
        AddressbookService.addManager(token, companyId, map)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun deleteManager(life: LifecycleTransformer<Result<Any>>, token: String,
                               companyId: String, managerId: String,
                               onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.deleteManager(token, companyId, managerId)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onComplete() {
                    }

                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

}