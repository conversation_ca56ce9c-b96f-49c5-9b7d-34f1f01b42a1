package com.joinutech.addressbook.module

import com.joinutech.addressbook.constract.UndoListConstract
import com.joinutech.ddbeslibrary.bean.UndoBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

class UndoListModuleIp @Inject internal constructor() :
        UndoListConstract.UndoListModule {

    //获取待处理数据tcp
    override fun getUndoList(life: LifecycleTransformer<Result<UndoBean>>,
                             status: Int, page: Int,
                             size: Int, onSuccess: (UndoBean) -> Unit,
                             onError: (String) -> Unit) {
        AddressbookService.getUndoList(status, page, size)
                .compose(life)
                .compose(ErrorTransformer.getInstance<UndoBean>())
                .subscribe(object : BaseSubscriber<UndoBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: UndoBean?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}