package com.joinutech.addressbook.module

import android.content.Context
import com.joinutech.ddbeslibrary.bean.VerifyImageBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.request.exception.NoDataTransformer
import com.joinutech.ddbeslibrary.service.LoginService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/11/27 16:41
 * @packageName: com.joinutech.addressbook.module
 * @Company: JoinuTech
 */
class ChangeCreatorReposity @Inject internal constructor() {

    fun getVerifyImage(life: LifecycleTransformer<Result<VerifyImageBean>>, phone: String,
                       onSuccess: (VerifyImageBean) -> Unit, onError: (String) -> Unit) {
        //获取图片验证码
        LoginService.getVerifyImage(phone)
                .compose(life)
                .compose(ErrorTransformer.getInstance<VerifyImageBean>())
                .subscribe(object : BaseSubscriber<VerifyImageBean>() {
                    override fun onError(ex: ApiException) {
                        //该手机号未注册
                        onError.invoke(if (ex.code == 1104) {
                            "该手机号未注册"
                        } else {
                            ex.message
                        })
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: VerifyImageBean?) {
                        if (t != null) {
                            onSuccess.invoke(t)
                        }
                    }

                })
    }

    fun sendSms(life: LifecycleTransformer<Result<Any>>, context: Context?,
                phone: String, type: Int, onSuccess: (Any) -> Unit,
                onError: (String) -> Unit) {
        //转让团队或者解散团队
        LoginService.sendSms(phone, type)
                .compose(life)
                .compose(NoDataTransformer)
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        //该手机号未注册
                        onError.invoke(if (ex.code == 1104) {
                            "该手机号未注册"
                        } else ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    fun verifyImageWithMsg(life: LifecycleTransformer<Result<Any>>, dataMap: Any,
                           onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        //验证图片验证码带短信验证码
        LoginService.verifyImageWithMsg(dataMap)//转让 图片验证并发送验证码
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        if (t != null) {
                            onSuccess.invoke(t)
                        }
                    }

                })
    }
}