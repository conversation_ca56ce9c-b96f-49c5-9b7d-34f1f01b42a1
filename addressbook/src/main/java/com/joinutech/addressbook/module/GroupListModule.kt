package com.joinutech.addressbook.module

import android.content.Context
import com.ddbes.library.im.bean.AppGroupBean
import com.ddbes.library.im.imtcp.dbope.GroupDaoOpe
import com.ddbes.library.im.imtcp.imservice.msghelper.GroupInfoUtil
import com.ddbes.library.im.netutil.ImNetUtil
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.bean.GroupInfoBean
import com.joinutech.ddbeslibrary.bean.GroupListBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import io.reactivex.Observable
import io.reactivex.ObservableOnSubscribe
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import javax.inject.Inject

/**
 * @Description:群组module
 * @Author: hjr
 * @Time: 2020/3/4 11:01
 * @packageName: com.joinutech.addressbook.module
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
class GroupListModule @Inject internal constructor() {

    //分别获取群组列表
    fun getGroupList(life: LifecycleTransformer<Result<List<AppGroupBean>>>,
                     type: Int, token: String,
                     onSuccess: (List<GroupListBean>) -> Unit, onError: (String) -> Unit) {
        ImNetUtil.getAppGroupListByType(life, type, onSuccess = {
            onSuccess.invoke(it)
        }, onFailer = {
            onError.invoke(it)
        })


//        GroupService.getGroupList(type, token)
//                .compose(life)
//                .compose(ErrorTransformer.getInstance())
//                .subscribe(object : BaseSubscriber<List<GroupListBean>>() {
//                    override fun onError(ex: ApiException) {
//                        onError.invoke(ex.message)
//                    }
//
//                    override fun onComplete() {}
//
//                    override fun onNext(t: List<GroupListBean>?) {
//                        onSuccess.invoke(t!!)
//                    }
//
//                })
    }

    //搜索获取全部群组列表
    fun getAllGroupList(context: Context,
                        onSuccess: (List<GroupInfoBean>) -> Unit, onError: (String) -> Unit) {
        Observable.create(ObservableOnSubscribe<List<GroupInfoBean>> { it2 ->
            val groupList = GroupDaoOpe.instance.queryAllGroupListByUserId(context, UserHolder.getUserId()).toMutableList()
            it2.onNext(GroupInfoUtil.dbListToGroupInfoList(groupList))
        })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe({ groupInfoBeanList ->
                    onSuccess.invoke(groupInfoBeanList)
                }, { throwable ->
                })
    }
}