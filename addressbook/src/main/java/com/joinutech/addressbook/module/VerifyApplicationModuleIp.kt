package com.joinutech.addressbook.module

import com.joinutech.addressbook.constract.VerifyApplicationConstact
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.ddbes.library.im.util.UserService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

class VerifyApplicationModuleIp @Inject internal constructor()
    :VerifyApplicationConstact.VerifyApplicationModule {

    override fun verifyOrgApplication(life: LifecycleTransformer<Result<Int>>,
                                      token: String, companyId: String,
                                      content: String,
                                      onSuccess: (Int) -> Unit, onError: (String) -> Unit) {
        val map = hashMapOf<String,Any>()
        map["companyId"]=companyId
        map["content"]=content
        map["userName"]= UserHolder.getCurrentUser()?.name ?:""
        AddressbookService.verifyOrgApplication(token,map)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Int>())
                .subscribe(object :BaseSubscriber<Int>(){
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Int?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
//tcp真正添加好友
    override fun verfyFriendApplication(life: LifecycleTransformer<Result<Any>>,
                                        token: String, userId  : String, message: String,
                                        onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        val map = hashMapOf<String,Any>()
        map["targetUserId"]=userId//修改
        map["message"]=message
        UserService.verifyFriendApplication(token,map)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object :BaseSubscriber<Any>(){
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}