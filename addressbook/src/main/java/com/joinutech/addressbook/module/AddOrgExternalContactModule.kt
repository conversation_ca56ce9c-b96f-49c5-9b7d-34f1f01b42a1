package com.joinutech.addressbook.module

import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/2 9:15
 * @packageName: com.joinutech.addressbook.module
 * @Company: JoinuTech
 */
class AddOrgExternalContactModule @Inject internal constructor(){

    //添加外部联系人
    fun addExternalContact(life: LifecycleTransformer<Result<Any>>,
                            data: Any, token: String,
                            onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.addExternalContact(data, token)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {}

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    //修改外部联系人
    fun updateExternalContact(life: LifecycleTransformer<Result<Any>>,
                           data: Any, token: String,
                           onSuccess: (Any) -> Unit, onError: (ApiException) -> Unit) {
        AddressbookService.updateExternalContact(data, token)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex)
                    }

                    override fun onComplete() {}

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    //删除外部联系人
    fun deleteExternalContact(life: LifecycleTransformer<Result<Any>>,
                              companyId: String,userId:String,token:String,
                              onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.deleteExternalContact(companyId, userId,token)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {}

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}