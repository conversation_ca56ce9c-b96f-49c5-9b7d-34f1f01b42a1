package com.joinutech.addressbook.module

import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date   2019/5/27 9:26
 * @className: SelectVisitModel
 *@Description: 类作用描述
 */
class SelectVisitModel  @Inject internal constructor(){

   fun getMsgShortUrl(life: LifecycleTransformer<Result<String>>, token: String, companyId: String,
                      userId  : String, url: String,
                      onSuccess: (String) -> Unit, onError: (String) -> Unit) {
        AddressbookService.getMsgShortUrl(token,companyId,userId,url)
                .compose(life)
                .compose(ErrorTransformer.getInstance<String>())
                .subscribe(object : BaseSubscriber<String>(){
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: String?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}