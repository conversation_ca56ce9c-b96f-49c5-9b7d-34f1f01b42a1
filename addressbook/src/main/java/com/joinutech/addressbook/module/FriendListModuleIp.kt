package com.joinutech.addressbook.module

import com.joinutech.addressbook.constract.FriendListConstract
import com.ddbes.library.im.imtcp.dbbean.FriendBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

@Deprecated("no used")
class FriendListModuleIp @Inject internal constructor() : FriendListConstract.FriendListModule {

    override fun getFriendList(life: LifecycleTransformer<Result<List<FriendBean>>>,
                               token: String, content: String, onSuccess: (List<FriendBean>) -> Unit,
                               onError: (String) -> Unit) {
//        UserService.getFriendList(content)//获取所有好友信息，建议改成本地搜索
//                .compose(life)
//                .compose(ErrorTransformer.getInstance())
//                .subscribe(object : BaseSubscriber<List<FriendBean>>() {
//                    override fun onError(ex: ApiException) {
//                        onError.invoke(ex.message)
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(t: List<FriendBean>?) {
//                        onSuccess.invoke(t!!)
//                    }
//
//                })
    }

}