package com.joinutech.addressbook.module

import com.joinutech.ddbeslibrary.bean.ExternalContactListBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/2 10:13
 * @packageName: com.joinutech.addressbook.module
 * @Company: JoinuTech
 */
@Deprecated("no used")
class OrgExternalContactListModuel @Inject internal constructor() {

    //得到外部联系人列表
    fun getExternalContactList(life: LifecycleTransformer<Result<List<ExternalContactListBean>>>,
                               companyId: String, keyword: String, level: String, type: String,
                               token: String,
                               onSuccess: (List<ExternalContactListBean>) -> Unit,
                               onError: (String) -> Unit) {
        AddressbookService.getExternalContactList(companyId, keyword, level, type, token)
                .compose(life)
                .compose(ErrorTransformer.getInstance<List<ExternalContactListBean>>())
                .subscribe(object : BaseSubscriber<List<ExternalContactListBean>>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {}

                    override fun onNext(t: List<ExternalContactListBean>?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}