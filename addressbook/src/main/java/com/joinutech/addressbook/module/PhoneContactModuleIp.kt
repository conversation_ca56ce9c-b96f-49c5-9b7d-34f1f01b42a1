package com.joinutech.addressbook.module

import com.joinutech.addressbook.constract.PhoneContactConstract
import com.ddbes.library.im.util.UserService
import com.joinutech.ddbeslibrary.bean.PhoneContactDataBean
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.trello.rxlifecycle3.LifecycleTransformer
import com.joinutech.ddbeslibrary.request.Result
import javax.inject.Inject

class PhoneContactModuleIp@Inject internal constructor():
        PhoneContactConstract.PhoneContactModule {

    override fun getContactStatusList(life: LifecycleTransformer<Result<List<PhoneContactDataBean>>>,
                                      token: String, phones: ArrayList<String>,
                                      onSuccess: (List<PhoneContactDataBean>) -> Unit,
                                      onError: (String) -> Unit) {

        UserService.getPhoneContactStatus(token,phones)
                .compose(life)
                .compose(ErrorTransformer.getInstance<List<PhoneContactDataBean>>())
                .subscribe(object : BaseSubscriber<List<PhoneContactDataBean>>(){
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: List<PhoneContactDataBean>?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}