package com.joinutech.addressbook.module

import com.joinutech.addressbook.constract.OrgChartConstract
import com.joinutech.ddbeslibrary.bean.OrgChartBean
import com.joinutech.ddbeslibrary.bean.OrgChartDepBean
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService

class OrgChartModuleIp @Inject internal constructor() : OrgChartConstract.OrgChartModule {

    override fun getOrgChartDataResult(life: LifecycleTransformer<Result<OrgChartBean>>,
                                       token: String, companyId: String, deptId: String,
                                       onSuccess: (OrgChartBean) -> Unit, onError: (String) -> Unit) {
        AddressbookService.quitChartMember(token, companyId, deptId)
                .compose(life)
                .compose(ErrorTransformer.getInstance<OrgChartBean>())
                .subscribe(object : BaseSubscriber<OrgChartBean>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(t: OrgChartBean?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun addChildDep(life: LifecycleTransformer<Result<Any>>,
                             token: String, companyId: String, deptId : String, name: String,
                             onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        val map = hashMapOf<String, Any>()
        map["name"] = name
        map["companyId"] = companyId
        AddressbookService.addChildDep(deptId, map)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}