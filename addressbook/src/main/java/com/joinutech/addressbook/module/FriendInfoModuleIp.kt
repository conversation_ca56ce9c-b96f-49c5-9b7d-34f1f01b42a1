package com.joinutech.addressbook.module

import com.ddbes.library.im.imtcp.Logger
import com.joinutech.addressbook.constract.FriendInfoConstract
import com.ddbes.library.im.util.UserService
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject

class FriendInfoModuleIp @Inject internal constructor() :
        FriendInfoConstract.FriendInfoModule {

    //tcp详情页获取数据,获取好友信息tcp
    override fun getCompanyUserInfo(life: LifecycleTransformer<Result<Any>>,
                                    token: String, companyId: String, userId: String,
                                    onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        if (StringUtils.isNotBlankAndEmpty(companyId)) {
            Logger.i("---执行---获取好友信息---", "---团队好友--" + userId)
            UserService.getCompanyUserInfoV2(token, companyId = companyId, userId = userId)
        } else {
            Logger.i("---执行---获取好友信息---", "---个人好友--" + userId)
            UserService.getUserInfo(token, userId)
        }.compose(life).compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    override fun leaveUser(life: LifecycleTransformer<Result<Any>>,
                           token: String, users: List<String>, companyId: String,
                           onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.leaveUser(token, users, companyId)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })

    }

    override fun leaveUserValidate(life: LifecycleTransformer<Result<Any>>, token: String,
                                   users: ArrayList<String>, companyId: String,
                                   onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        AddressbookService.leaveUserValidate(token, users, companyId)
                .compose(life)
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }

    //tcp删除好友
    override fun deleteFriend(life: LifecycleTransformer<Result<Any>>, token: String,
                              targetUserId: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        UserService.deleteFriend(token, targetUserId)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })

    }

    override fun volidate(life: LifecycleTransformer<Result<Any>>, token: String,
                          userId: String, type: Int,
                          onSuccess: (Any) -> Unit, onError: (String) -> Unit) {
        val map = hashMapOf<String, Any>()
        map["type"] = type //type = 1,2,3,4
        map["userId"] = userId
        UserService.volidate(token, map)
                .compose(life)
                .compose(ErrorTransformer.getInstance<Any>())
                .subscribe(object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke(ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(t: Any?) {
                        onSuccess.invoke(t!!)
                    }

                })
    }
}