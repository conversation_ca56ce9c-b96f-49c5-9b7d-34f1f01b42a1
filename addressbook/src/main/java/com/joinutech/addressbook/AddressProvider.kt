package com.joinutech.addressbook

import android.content.Context
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.alibaba.android.arouter.facade.annotation.Route
import com.ddbes.library.im.imtcp.Logger
import com.ddbes.library.im.imtcp.notice.NoticeUtil
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.bean.*
import com.joinutech.ddbeslibrary.org.CompanyUpdateBean
import com.joinutech.ddbeslibrary.org.GlobalCompanyHolder
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.request.exception.ErrorType
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.joinutech.ddbeslibrary.service.PersonService
import com.joinutech.ddbeslibrary.utils.ConsKeys
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.MMKVUtil
import com.joinutech.ddbeslibrary.utils.MSG_LIST_INFO_CHANGE
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.marktoo.lib.cachedweb.LogUtil

object AddressProvider {

    private val unProcessCount: MutableLiveData<Int> by lazy {
        MutableLiveData<Int>()
    }

    fun getUnProcessCount(): LiveData<Int> {
        return unProcessCount
    }

    fun changeUnProcessCount(count: Int) {
        LogUtil.showLog("更新待处理数量 $count ,showDot = ${count > 0}")
        unProcessCount.value = count
    }
}

/**
 * @PackageName: com.joinutech.addressbook
 * @ClassName: FriendService
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/7/24 8:46
 * @Desc: //TODO 公司模块相关功能对外访问接口
 */
@Route(path = "/addressbook/service/org")
class OrgServiceImpl : OrgService {

    override fun init(context: Context?) {
    }

    override fun openPage(path: String, params: Bundle) {
    }

    override fun service(path: String, params: Bundle, result: (data: String) -> Unit) {
        when (path) {
            "getAllCompany" -> {
                getAllCompany(
                        token = params.getString("token", UserHolder.getAccessToken()),
                        userId = params.getString("userId", UserHolder.getUserId() ?: ""),
                        result = result)
            }
            "getAprWaitCount" -> {
                getAprWaitCount(result)
            }
            "getGroupInfo" -> {

            }
            "getUserInfo" -> {
                getUserInfo(params.getString("userId") ?: "", result)
            }
            "getKingDeeToken" -> {
                getKingDeeToken(
                        params.getString(ConsKeys.COMPANY_ID, CompanyHolder.getCurrentOrg()?.companyId ?: ""),
                        params.getString(ConsKeys.USER_ID, UserHolder.getUserId() ?: ""),
                        result)
            }
            "getKingDeeListUrl" -> {
                getKingDeeListUrl(params.getString(ConsKeys.COMPANY_ID, CompanyHolder.getCurrentOrg()?.companyId ?: ""),
                        result)
            }
            //金蝶相关，新接口
            //添加一个新接口，整合金蝶和交接班,商城，访客，工单等功能入口
            "getInteGrateData"->{
                getInteGrateData(params.getString(ConsKeys.COMPANY_ID, CompanyHolder.getCurrentOrg()?.companyId ?: ""),
                    result)
            }
            "getGrateDataLastTime"->{
                getGrateDataLastTime(params.getString(ConsKeys.COMPANY_ID, CompanyHolder.getCurrentOrg()?.companyId ?: ""),
                    result)
            }
            "entroyListUnRead"->{
                getEntryListUnRead(params.getString(ConsKeys.COMPANY_ID, CompanyHolder.getCurrentOrg()?.companyId ?: ""),
                    result)
            }
            "getShopingData"->{
                getShopingData(params.getString(ConsKeys.COMPANY_ID, CompanyHolder.getCurrentOrg()?.companyId ?: ""),
                    result)
            }
            "getKingDeeTipCount" -> {
                getKingDeeTipCount(result)
            }
        }
    }

    /**金蝶token缓存*/
    private fun findCacheKingDeeToken(tag: String): String {
        val json = MMKVUtil.getString(tag)
        if (!StringUtils.isNotBlankAndEmpty(json)) {
            return ""
        }
        try {
            val kingDeeToken = GsonUtil.fromJson<KingDeeTokenBean>(json) ?: return ""
            if (System.currentTimeMillis() < (kingDeeToken.expireTime - 60000)) {
                return kingDeeToken.token
            }
        } catch (e: Exception) {
        }
        return ""
    }

    fun getKingDeeToken(companyId: String, userId: String, result: (data: String) -> Unit) {
        val tag = "kingDeeToken_${companyId}_${userId}"
        val token = findCacheKingDeeToken(tag)
        if (!token.isNullOrBlank()) {
            showLog("获取缓存中 kingDee token 使用")
            result.invoke(token)
        } else {
            AddressbookService.getKingDeeAccessToken()
                    .compose(ErrorTransformer.getInstance())
                    .subscribe(object : BaseSubscriber<String>() {
                        override fun onError(ex: ApiException) {
                            result("err:".plus(ex.message))
                        }

                        override fun onComplete() {
                        }

                        override fun onNext(data: String) {
                            MMKVUtil.saveString(tag, GsonUtil.toJson(data))
                            result.invoke(data)
                        }
                    })
        }
    }

    //金蝶权限tcp,查看是否显示金蝶图标
    private fun getKingDeeListUrl(companyId: String, result: (data: String) -> Unit) {
        AddressbookService.getKingDeeListUrl(companyId)
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<KingDeeListUrl>() {
                    override fun onError(ex: ApiException) {
                        result("")
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(data: KingDeeListUrl?) {
                        result.invoke(data?.webUrl ?: "")
                    }
                })
    }

    //金蝶权限tcp,查看是否显示金蝶图标,同时拉取交换班信息,商城，工单，访客等的入口
    private fun getInteGrateData(companyId: String, result: (data: String) -> Unit) {
        AddressbookService.getInteGrateData(companyId)
            .compose(ErrorTransformer.getInstance())
            .subscribe(object : BaseSubscriber<InteGrateBean>() {
                override fun onError(ex: ApiException) {
                    result("")
                }

                override fun onComplete() {
                }

                override fun onNext(data: InteGrateBean) {
                    result.invoke(GsonUtil.toJson(data))
                }
            })
    }

    //拉取所有应用入口的未读数
    private fun getEntryListUnRead(companyId: String, result: (data: String) -> Unit) {
        AddressbookService.getEntryListUnRead(companyId)
            .compose(ErrorTransformer.getInstance())
            .subscribe(object : BaseSubscriber<InteGrateBean>() {
                override fun onError(ex: ApiException) {
                    result("")
                }

                override fun onComplete() {
                }

                override fun onNext(data: InteGrateBean) {
                    result.invoke(GsonUtil.toJson(data))
                }
            })
    }

    //判断是不是重新请求入口列表
    private fun getGrateDataLastTime(companyId: String, result: (data: String) -> Unit) {
        AddressbookService.getGrateDataLastTime(companyId)
            .compose(ErrorTransformer.getInstance())
            .subscribe(object : BaseSubscriber<Long>() {
                override fun onError(ex: ApiException) {
                    result("")
                }

                override fun onComplete() {
                }

                override fun onNext(data: Long) {
                    result.invoke(data.toString())
                }
            })
    }

    //商城权限tcp,查看是否显示商城图标
    private fun getShopingData(companyId: String, result: (data: String) -> Unit) {
        AddressbookService.getShopingData(companyId)
            .compose(ErrorTransformer.getInstance())
            .subscribe(object : BaseSubscriber<InteGrateBean>() {
                override fun onError(ex: ApiException) {
                    result("")
                }

                override fun onComplete() {
                }

                override fun onNext(data: InteGrateBean) {
                    result.invoke(GsonUtil.toJson(data))
                }
            })
    }

    private fun getKingDeeTipCount(result: (data: String) -> Unit) {
        AddressbookService.updateKingDeeTipCount()
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<Int>() {
                    override fun onError(ex: ApiException) {
                        result("")
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(data: Int) {
                        result.invoke(data.toString())
                    }
                })
    }

    private fun getUserInfo(userId: String, result: (data: String) -> Unit) {
        val token = UserHolder.getAccessToken()
        if (token.isNullOrBlank() || userId.isNullOrBlank() || userId == "0") return
        PersonService.getUserInfo(userId)
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<WithRemarkUserInfo>() {
                    override fun onComplete() {}
                    override fun onNext(it: WithRemarkUserInfo?) {
                        if (it != null && StringUtils.isNotBlankAndEmpty(it.remark)) {
                            val user1 = UserInfo(userId, it.avatar ?: "", it.remark ?: "")
                        }
                    }

                    override fun onError(ex: ApiException) {
//                        messageList.postDelayed({ getUserInfo(id) }, 3000)
                    }
                })
    }

    private fun getAprWaitCount(result: (data: String) -> Unit) {
        val orgIds = CompanyHolder.getAllNormalOrg().map { it.companyId }.toList()//获取所有公司的companyId
        AddressbookService.updateAprCount(orgIds)
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<List<Int>>() {
                    override fun onError(ex: ApiException) {
                        result("")
                    }
//是我的你是華英雄华硬话話haul話話話话话话
                    override fun onComplete() {
                    }

                    override fun onNext(result: List<Int>?) {
                        val temp = hashMapOf<String, Int>()
                        if (!result.isNullOrEmpty()) {
                            result.forEachIndexed { index, value ->
                                if (value > 0) {
                                    temp[orgIds[index]] = value
                                }
                            }
                        }
                        result(GsonUtil.toJson(temp))
                    }
                })
    }

    private fun getAllCompany(token: String, userId: String, result: (data: String) -> Unit) {
        val tag = "获取所有团队数据"
        showLog("$tag 开始")
        if (token.isNullOrBlank() || userId.isNullOrBlank()) return
        AddressbookService.getCompanies()
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<CompanyListBean>() {
                    override fun onError(ex: ApiException) {
                        showLog("$tag 失败 ${ex.code} -- ${ex.message}")
                        AddressProvider.changeUnProcessCount(0)
                        showLog("$tag 失败，需要加载缓存中数据给用户--->")
                        val companies = CompanyHolder.getAllNormalOrg()
                        if (!companies.isNullOrEmpty()) {
                            // 更新团队信息时，更新当前团队信息
                            val work = if (CompanyHolder.getCurrentOrg() != null) {
                                companies.find { it.companyId == CompanyHolder.getCurrentOrg()?.companyId }
                                        ?: companies[0]
                            } else {
                                companies[0]
                            }
                            GlobalCompanyHolder.setCurrentCompany(work)
                            GlobalCompanyHolder.companyUpdateResult.value = CompanyUpdateBean(1, companies)
                            result.invoke(ErrorType.SUCCESS.toString())
                        } else if (!CompanyHolder.getCooperationOrg().isNullOrEmpty()) {
                            GlobalCompanyHolder.setCurrentCompany(null)
                            GlobalCompanyHolder.companyUpdateResult.value = CompanyUpdateBean(2, CompanyHolder.getCooperationOrg())
                            result.invoke(ErrorType.SUCCESS.toString())
                        } else {
                            GlobalCompanyHolder.companyUpdateResult.value = CompanyUpdateBean(0, arrayListOf())
                            result.invoke("${ex.code}")
                        }
                    }

                    override fun onComplete() {
                        showLog("$tag  完成")
                    }

                    override fun onNext(orgList: CompanyListBean?) {
                        Logger.i("全部公司数据","===当前主公司mainCompanyId=="+orgList?.mainCompanyId)
                        Logger.i("全部公司数据","===json==${GsonUtil.toJson(orgList)}")
                        //小红点，启动APP就刷新小红点的地方，还有处理了外部协作人申请之后，会刷新组织，也就会同时刷新这里
                        AddressProvider.changeUnProcessCount(orgList?.unprocessedMessage ?: 0)
                        if (orgList != null) {
                            CompanyHolder.saveMainOrgId(orgList.mainCompanyId)
                            if (!CompanyHolder.getTotalCompanies().isNullOrEmpty()) {
                                //先将缓存中的公司的审批未读数取出来
                                val haveApprovalCompanies = CompanyHolder.getTotalCompanies().filter { it.haveApprove != 0 }
                                haveApprovalCompanies.forEach { work ->
                                    orgList.companies.find { it.companyId == work.companyId }?.let {
                                        it.haveApprove = work.haveApprove
                                    }
                                    orgList.externalCompanies.find { it.companyId == work.companyId }?.let {
                                        it.haveApprove = work.haveApprove
                                    }
                                }
                            }
                            showLog("保存所有团队数据--->")
                            CompanyHolder.saveAllNormalOrg(orgList.companies)
                            showLog("保存所有协作团队数据--->")
                            CompanyHolder.saveCooperationOrg(orgList.externalCompanies)

                            //只有刷新组织的时候才创建通知的tagId========开始==========
                            var workTagIdChange = 20001
                            var approvalTagIdChange = 30001
                            orgList.companies.forEach { work ->
                                // 创建相关公司的通知id，分别创建审批和工作通知的
                                NoticeUtil.saveWorkTagId(work.companyId, workTagIdChange)
                                workTagIdChange++
                                NoticeUtil.saveApprovalTagId(work.companyId, approvalTagIdChange)
                                approvalTagIdChange++
                            }

                            var workTagIdChange1 = 22001
                            var approvalTagIdChange1 = 32001
                            orgList.externalCompanies.forEach { cooperationWork ->

                                NoticeUtil.saveWorkTagId(cooperationWork.companyId, workTagIdChange1)
                                workTagIdChange1++
                                NoticeUtil.saveApprovalTagId(cooperationWork.companyId, approvalTagIdChange1)
                                approvalTagIdChange1++
                            }
                            //只有刷新组织的时候才创建通知的tagId========结束==========
                            handleCompanyList(orgList)
                        } else {
                            CompanyHolder.saveMainOrgId("")
                            showLog("保存所有团队数据--->")
                            CompanyHolder.saveAllNormalOrg(arrayListOf())
                            showLog("保存所有协作团队数据--->")
                            CompanyHolder.saveCooperationOrg(arrayListOf())
                            showLog("团队信息不存在时处理")
                            GlobalCompanyHolder.companyUpdateResult.value = CompanyUpdateBean(0, arrayListOf())
                            GlobalCompanyHolder.setCurrentCompany(null)
//                            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.CURRENT_COMPANY_UPDATE, 0))
                            EventBusUtils.sendEvent(EventBusEvent(MSG_LIST_INFO_CHANGE, ""))
                        }
//                        // 刷新通讯录
//                        EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_ADDRFRAGMENT, "1"))
//                        EventBus.getDefault().post(EventBusAction.REFRESH_ADDRFRAGMENT)
//                        EventBus.getDefault().post(EventBusAction.REFRESH_PROGRAMLIST)// 刷新项目列表
                        result.invoke(ErrorType.SUCCESS.toString())
                    }
                })
    }

    /**
     * work头部显示更新
     * 设置当前团队信息
     * 查找当前选中团队，更新团队信息
     * 设置头部标题更新
     * 保存所有团队id和基本信息到缓存
     * */
    private fun handleCompanyList(listData: CompanyListBean) {
        showLog("处理收到所有团队信息")

        // 首页和通讯录团队显示：
        // 加入过团队时，显示加入的团队
        // 如果未加入团队，但是有协作团队时，显示协作团队
        // 两种团队都没有时，显示无任何团队信息
        // 增加缓存后
        // 先读取缓存，按上述显示团队信息，
        // 所有团队接口刷新后更新数据时，事件同步到使用相关数据的页面，
        // 数据取用仍然是通过缓存方法获取更新

        if (!listData.companies.isNullOrEmpty()) {
            // 更新团队信息时，更新当前团队信息
            val work = if (CompanyHolder.getCurrentOrg() != null) {
                listData.companies.find { it.companyId == CompanyHolder.getCurrentOrg()?.companyId }
                        ?: (listData.companies.find { it.companyId == listData.mainCompanyId }
                                ?: listData.companies[0])
            } else {
                listData.companies[0]
            }
            GlobalCompanyHolder.setCurrentCompany(work)
            GlobalCompanyHolder.companyUpdateResult.value = CompanyUpdateBean(1, listData.companies)
//            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.CURRENT_COMPANY_UPDATE, 1))
        } else if (!listData.externalCompanies.isNullOrEmpty()) {
            GlobalCompanyHolder.setCurrentCompany(null)
            GlobalCompanyHolder.companyUpdateResult.value = CompanyUpdateBean(2, listData.externalCompanies)
//            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.CURRENT_COMPANY_UPDATE, 2))
        } else {
            GlobalCompanyHolder.companyUpdateResult.value = CompanyUpdateBean(0, arrayListOf())
//            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.CURRENT_COMPANY_UPDATE, 0))
        }
    }

    override fun openPageWithResult(activity: FragmentActivity, path: String, params: Bundle, result: (data: String) -> Unit) {
    }

    override fun openPageWithResult1(
        activity: FragmentActivity,
        path: String,
        params: Bundle,
        result: (data: String) -> Unit
    ) {

    }

}

interface OrgService : RouteServiceProvider

/**
 * @PackageName: com.joinutech.addressbook
 * @ClassName: FriendService
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/7/24 8:46
 * @Desc: //TODO 好友相关功能模块对外访问接口
 */
@Route(path = "/addressbook/service/friend")
class FriendServiceImpl : FriendService {

    override fun init(context: Context?) {
    }

    override fun openPage(path: String, params: Bundle) {
    }

    override fun service(path: String, params: Bundle, result: (data: String) -> Unit) {
    }

    override fun openPageWithResult(activity: FragmentActivity, path: String, params: Bundle, result: (data: String) -> Unit) {
    }

    override fun openPageWithResult1(
        activity: FragmentActivity,
        path: String,
        params: Bundle,
        result: (data: String) -> Unit
    ) {

    }

}

interface FriendService : RouteServiceProvider