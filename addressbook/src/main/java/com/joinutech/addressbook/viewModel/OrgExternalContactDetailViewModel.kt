package com.joinutech.addressbook.viewModel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.module.OrgExternalContactDetailModuel
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.ExternalContactDetailBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/2 11:07
 * @packageName: com.joinutech.addressbook.viewModel
 * @Company: JoinuTech
 */
class OrgExternalContactDetailViewModel : ViewModel() {

    @Inject
    @field:Named(AddressbookUtil.ORG_EXTERNAL_CONTACT_DETAIL_MODULE)
    lateinit var module: OrgExternalContactDetailModuel
    var getExternalContactDetailSuccessObservable = MutableLiveData<ExternalContactDetailBean>()
    var getExternalContactDetailErrorObservable: MutableLiveData<ApiException> = MutableLiveData()
    var sendApplicationMsgSuccessObservable = MutableLiveData<Any>()
    var sendApplicationMsgErrorObservable: MutableLiveData<String> = MutableLiveData()
    var verifyFriendApplicationSuccessObservable = MutableLiveData<Any>()
    var verifyFriendApplicationErrorObservable: MutableLiveData<String> = MutableLiveData()

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    fun getExternalContactDetail(life: LifecycleTransformer<Result<ExternalContactDetailBean>>,
                                 companyId: String, userId: String, token: String) {
        module.getExternalContactDetail(life, companyId, userId, token, {
            getExternalContactDetailSuccessObservable.value = it
        }, {
            getExternalContactDetailErrorObservable.value = it
        })
    }

    fun sendApplicationMsg(life: LifecycleTransformer<Result<Any>>,
                           companyId: String, phone: String, token: String) {
        module.sendApplicationMsg(life, companyId, phone, token, {
            sendApplicationMsgSuccessObservable.value = it
        }, {
            sendApplicationMsgErrorObservable.value = it
        })
    }

    //好友添加校验
    fun validate(life: LifecycleTransformer<Result<Any>>, token: String, userId: String) {
        module.validate(life, token, userId, {
            verifyFriendApplicationSuccessObservable.value = it
        }, {
            verifyFriendApplicationErrorObservable.value = it
        })
    }
}