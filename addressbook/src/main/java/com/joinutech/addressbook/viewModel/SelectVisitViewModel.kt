package com.joinutech.addressbook.viewModel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.module.SelectVisitModel
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

/**
 * <AUTHOR>
 * @date   2019/5/27 9:25
 * @className: SelectVisitViewModel
 *@Description: 类作用描述
 */
class SelectVisitViewModel :ViewModel() {

    @Inject
    @field:Named(AddressbookUtil.SELECT_VISIT_MODULE)
    lateinit var module: SelectVisitModel
    var selectVisitObservable = MutableLiveData<String>()
    var selectVisitErrorObservable: MutableLiveData<String> = MutableLiveData()

    init{
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    fun getMsgShortUrl(life: LifecycleTransformer<Result<String>>, token: String,
                       companyId: String, userId: String, url: String){
        module.getMsgShortUrl(life,token,companyId,userId,url,{
            selectVisitObservable.value = it
        },{
            selectVisitErrorObservable.value = it
        })
    }
}