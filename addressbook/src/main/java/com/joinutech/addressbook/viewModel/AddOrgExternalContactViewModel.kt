package com.joinutech.addressbook.viewModel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.module.AddOrgExternalContactModule
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/2 9:14
 * @packageName: com.joinutech.addressbook.viewModel
 * @Company: JoinuTech
 */
class AddOrgExternalContactViewModel :ViewModel(){

    @Inject
    @field:Named(AddressbookUtil.ADD_ORG_EXTERNAL_CONTACT_MODULE)
    lateinit var module: AddOrgExternalContactModule
    var addOrgExternalContactSuccessObservable = MutableLiveData<Any>()
    var addOrgExternalContactErrorObservable: MutableLiveData<String> = MutableLiveData()
    var updateOrgExternalContactSuccessObservable = MutableLiveData<Any>()
    var updateOrgExternalContactErrorObservable: MutableLiveData<ApiException> = MutableLiveData()
    var deleteOrgExternalContactSuccessObservable = MutableLiveData<Any>()
    var deleteOrgExternalContactErrorObservable: MutableLiveData<String> = MutableLiveData()
    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    fun addExternalContact(life: LifecycleTransformer<Result<Any>>,
                           data: Any, token: String){
        module.addExternalContact(life, data, token,{
            addOrgExternalContactSuccessObservable.value = it
        },{
            addOrgExternalContactErrorObservable.value = it
        })
    }

    fun updateExternalContact(life: LifecycleTransformer<Result<Any>>,
                           data: Any, token: String){
        module.updateExternalContact(life, data, token,{
            updateOrgExternalContactSuccessObservable.value = it
        },{
            updateOrgExternalContactErrorObservable.value = it
        })
    }

    fun deleteExternalContact(life: LifecycleTransformer<Result<Any>>,
                              companyId: String,userId:String,token:String){
        module.deleteExternalContact(life,companyId,userId,token,{
            deleteOrgExternalContactSuccessObservable.value = it
        },{
            deleteOrgExternalContactErrorObservable.value = it
        })
    }
}