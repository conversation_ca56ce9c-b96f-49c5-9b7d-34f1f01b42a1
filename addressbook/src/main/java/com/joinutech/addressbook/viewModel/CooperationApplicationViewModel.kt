package com.joinutech.addressbook.viewModel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.module.CooperationApplicationModule
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.CooperationApplicationDetailBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/10 10:08
 * @packageName: com.joinutech.addressbook.viewModel
 * @Company: JoinuTech
 */
class CooperationApplicationViewModel :ViewModel(){

    @Inject
    @field:Named(AddressbookUtil.GET_COOPERATION_APPLICATION_MODULE)
    lateinit var module: CooperationApplicationModule
    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }
    var getDetailSuccessObservable = MutableLiveData<CooperationApplicationDetailBean>()
    var getDetailErrorObservable: MutableLiveData<String> = MutableLiveData()
    var dealCooperationApplicationSuccessObservable = MutableLiveData<Any>()
    var dealCooperationApplicationErrorObservable: MutableLiveData<String> = MutableLiveData()

    fun getCooperationApplicationDetail(life: LifecycleTransformer<Result<
            CooperationApplicationDetailBean>>,
                                        token: String, id :String){
        module.getCooperationApplicationDetail(life,token,id,{
            getDetailSuccessObservable.value = it
        },{
            getDetailErrorObservable.value = it
        })
    }

    fun dealCooperationApplication(life: LifecycleTransformer<Result<Any>>,
                                   token: String, code:Int,id :String){
        module.dealCooperationApplication(life,token,code,id,{
            dealCooperationApplicationSuccessObservable.value = it
        },{
            dealCooperationApplicationErrorObservable.value = it
        })
    }
}