package com.joinutech.addressbook.viewModel

import android.content.Context
import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.ddbes.library.im.bean.AppGroupBean
import com.ddbes.library.im.imtcp.dbbean.FriendBean
import com.ddbes.library.im.imtcp.dbbean.GroupInfoDbBean
import com.ddbes.library.im.imtcp.dbope.FriendDaoOpe
import com.ddbes.library.im.imtcp.dbope.GroupDaoOpe
import com.ddbes.library.im.util.FriendCacheHolder
import com.joinutech.addressbook.adapter.bean.GroupSearchBean
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.module.GroupListModule
import com.joinutech.addressbook.util.AddressbookUtil
import com.ddbes.library.im.util.GroupCacheHolder
import com.joinutech.common.base.isDebug
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.bean.GroupInfoBean
import com.joinutech.ddbeslibrary.bean.GroupListBean
import com.joinutech.ddbeslibrary.bean.GroupMemberBean
import com.joinutech.ddbeslibrary.bean.UserInfo
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.trello.rxlifecycle3.LifecycleTransformer
import io.reactivex.Observable
import io.reactivex.ObservableOnSubscribe
import io.reactivex.Observer
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import javax.inject.Inject
import javax.inject.Named

/**
 * @Description: 群组列表viewModel
 * @Author: hjr
 * @Time: 2020/3/4 11:00
 * @packageName: com.joinutech.addressbook.viewModel
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
class GroupListViewModel : ViewModel() {

    @Inject
    @field:Named(AddressbookUtil.GROUP_LIST_MODULE)
    lateinit var module: GroupListModule

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    var getGroupListSuccessObservable = MutableLiveData<List<GroupListBean>>()
    var getGroupListErrorObservable: MutableLiveData<String> = MutableLiveData()
    var getAllGroupListSuccessObservable = MutableLiveData<List<GroupSearchBean>>()
    var getAllGroupListErrorObservable: MutableLiveData<String> = MutableLiveData()
    var getGlobalListSuccessObservable = MutableLiveData<HashMap<String, List<GroupInfoBean>>>()
    var getGlobalListErrorObservable: MutableLiveData<String> = MutableLiveData()

    fun getGroupList(life: LifecycleTransformer<Result<List<AppGroupBean>>>,
                     type: Int, token: String) {
        module.getGroupList(life, type, token, {
            getGroupListSuccessObservable.value = it
        }, {
            getGroupListErrorObservable.value = it
        })
    }

    /**保存到数据库*/
    private fun saveDataIntoDb(list: List<GroupInfoBean>) {
        //        GroupCacheHolder.saveGroupList(list)
        fun turnGroupToDb(groupInfo: GroupInfoBean): GroupInfoDbBean {
            if (isDebug) {
                GroupCacheHolder.showLog("转换群组信息 ${GsonUtil.toJson(groupInfo)}")
            }
            GroupCacheHolder.setGroupInfo(UserInfo(groupInfo.groupId, groupInfo.logo, groupInfo.name))

            val bean = GroupInfoDbBean()
            bean.createTime = groupInfo.createTime
            bean.createUserId = groupInfo.createUserId
            bean.logo = groupInfo.logo
            bean.name = groupInfo.name
            bean.orgId = groupInfo.orgId
            bean.groupId = groupInfo.groupId
            bean.receiveMode = groupInfo.receiveMode
            bean.type = groupInfo.type
            bean.initial = groupInfo.initial
            if (!groupInfo.users.isNullOrEmpty()) {
                groupInfo.users.forEach { groupMemberBean ->
                    try {
                        val daoBean = FriendDaoOpe.instance
                                .queryFriendByUserId(BaseApplication.joinuTechContext, groupMemberBean.id)
                        if (daoBean != null && StringUtils.isNotBlankAndEmpty(daoBean.remark)) {
                            groupMemberBean.remarkName = daoBean.remark
                            FriendCacheHolder.saveFriend( // 更新群成员信息
                                    UserInfo(userId = groupMemberBean.id, userIcon = groupMemberBean.headimg,
                                            userName = groupMemberBean.name, userNick = daoBean.remark))
                        } else {
                            UserHolder.saveUser(// 更新群成员信息
                                    UserInfo(groupMemberBean.id, groupMemberBean.headimg,
                                            groupMemberBean.name))
                        }
                    } catch (e: Exception) {
                    }
                }
                bean.users = GsonUtil.toJson(groupInfo.users)
            } else {
                bean.users = GsonUtil.toJson(arrayListOf<GroupMemberBean>())
            }
            return bean
        }
        Observable.create(ObservableOnSubscribe<String> {
            try {
                val dbList = arrayListOf<GroupInfoDbBean>()
                list?.forEach { groupInfo ->
                    run {
                        // 保存群组信息
                        dbList.add(turnGroupToDb(groupInfo))
                    }
                }
                GroupDaoOpe.instance.saveDataList(BaseApplication.joinuTechContext, dbList)
                it.onNext("1")
            } catch (e: Exception) {
                it.onError(Throwable("0"))
            }
        })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(object : Observer<String> {
                    override fun onComplete() {
                    }

                    override fun onSubscribe(d: Disposable) {
                    }

                    override fun onNext(data: String) {
                    }

                    override fun onError(e: Throwable) {
                    }
                })
//        val dbList = arrayListOf<GroupInfoDbBean>()
//        it.forEach { innerBean ->
//            run {
//                val bean = GroupInfoDbBean()
//                bean.createTime = innerBean.createTime
//                bean.createUserId = innerBean.createUserId
//                bean.logo = innerBean.logo
//                bean.name = innerBean.name
//                bean.orgId = innerBean.orgId
//                bean.groupId = innerBean.groupId
//                bean.receiveMode = innerBean.receiveMode
//                bean.type = innerBean.type
//                bean.initial = innerBean.initial
//                if (!innerBean.users.isNullOrEmpty()) {
//                    innerBean.users.forEach { groupMemberBean ->
//                        run {
//                            var daoBean: FriendBean? = null
//                            try {
//                                daoBean =
//                                        FriendBeanDaoOpe.instance.queryFriendByUserId(
//                                                BaseApplication.joinuTechContext,
//                                                groupMemberBean.id)
//                            } catch (e: Exception) {
//                                Log.e("查询好友", "失败")
//                            }
//                            if (daoBean != null &&
//                                    StringUtils.isNotBlankAndEmpty(daoBean.remark)) {
//                                groupMemberBean.remarkName = daoBean.remark
//                            }
//                        }
//                    }
//                    bean.users = GsonUtil.toJson(innerBean.users)
//                }
//                dbList.add(bean)
//            }
//        }
//        GroupDaoOpe.instance.saveDataList(BaseApplication.joinuTechContext, dbList)
    }

    /**搜索好友数据*/
    private fun dealFriendListSearch(searchValue: String, searchList: HashMap<String,
            List<GroupInfoBean>>) {
        val friendList = hashSetOf<GroupInfoBean>()
        try {
            val allFriendList =
                    FriendDaoOpe.instance.getAllFriendList(
                            BaseApplication.joinuTechContext)
            if (!allFriendList.isNullOrEmpty()) {
                val toMutableSet = allFriendList.filter { allBean ->
                    allBean.name.contains(searchValue) ||
                            (StringUtils.isNotBlankAndEmpty(allBean.remark)
                                    && allBean.remark.contains(searchValue))
                }.toMutableSet()
                toMutableSet.forEach { innerBean ->
                    run {
                        friendList.add(GroupInfoBean(name = innerBean.name,
                                logo = innerBean.avatar,
                                createUserId = innerBean.userId,
                                searchMatchUsers = arrayListOf(), users = arrayListOf()))
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("查询好友列表", "失败")
        }
        if (!friendList.isNullOrEmpty()) {
            searchList["担当好友"] = friendList.toMutableList()
        }
    }

    /**筛选群组数据*/
    private fun dealGroupSearch(it: List<GroupInfoBean>, searchValue: String,
                                searchList: HashMap<String, List<GroupInfoBean>>) {
        val privateGroupList = hashSetOf<GroupInfoBean>()
        val orgGroupList = hashSetOf<GroupInfoBean>()
        if (!it.isNullOrEmpty()) {
            it.forEach { bean ->
                run {
                    if (!bean.users.isNullOrEmpty()) {
                        bean.users.forEach { userBean ->
                            run {
                                var daoBean: FriendBean? = null
                                try {
                                    daoBean =
                                            FriendDaoOpe.instance.queryFriendByUserId(
                                                    BaseApplication.joinuTechContext,
                                                    userBean.id)
                                } catch (e: Exception) {
                                    Log.e("查询好友", "失败")
                                }
                                if (daoBean != null &&
                                        StringUtils.isNotBlankAndEmpty(daoBean.remark)) {
                                    userBean.remarkName = daoBean.remark
                                }
                            }
                        }
                    }
                    if (bean.name.contains(searchValue)) {
                        if (bean.type == 2) {
                            privateGroupList.add(bean)
                        } else {
                            orgGroupList.add(bean)
                        }
                    }
                    if (!bean.users.isNullOrEmpty()) {
                        bean.users.forEach { userBean ->
                            run {
                                val searchMatchUsers =
                                        arrayListOf<GroupMemberBean>()
                                if (userBean.name.contains(searchValue) ||
                                        (StringUtils.isNotBlankAndEmpty(userBean.remarkName)
                                                && userBean.remarkName.contains(searchValue))) {
                                    searchMatchUsers.add(userBean)
                                    if (bean.type == 2) {
                                        privateGroupList.add(bean)
                                    } else {
                                        orgGroupList.add(bean)
                                    }
                                }
                                if (!searchMatchUsers.isNullOrEmpty())
                                    bean.searchMatchUsers = searchMatchUsers
                            }
                        }
                    }
                }
            }
            if (!orgGroupList.isNullOrEmpty()) {
                searchList["团队群组"] = orgGroupList.toMutableList()
            }
            if (!privateGroupList.isNullOrEmpty()) {
                searchList["私有群组"] = privateGroupList.toMutableList()
            }
        }
    }

    fun getGlobalList(context: Context,
                      searchValue: String) {
        module.getAllGroupList(context, {
            if (!it.isNullOrEmpty()) {
//                saveDataIntoDb(it)
                val searchList =
                        hashMapOf<String, List<GroupInfoBean>>()
                dealFriendListSearch(searchValue, searchList)
                dealGroupSearch(it, searchValue, searchList)
                getGlobalListSuccessObservable.value = searchList
            }
        }, {
            getGlobalListErrorObservable.value = it
        })
    }

    fun getAllGroupList(context: Context,
                        searchValue: String) {
        module.getAllGroupList(context, {
            if (!it.isNullOrEmpty()) {
                saveDataIntoDb(it)
                val searchList =
                        hashMapOf<String, List<GroupInfoBean>>()
                dealGroupSearch(it, searchValue, searchList)
                val list = arrayListOf<GroupSearchBean>()
                if (!searchList.isNullOrEmpty()) {
                    searchList.forEach { (key, value) ->
                        list.add(GroupSearchBean(key, value))
                    }
                }
                getAllGroupListSuccessObservable.value = list
            }
        }, {
            getAllGroupListErrorObservable.value = it
        })
    }
}