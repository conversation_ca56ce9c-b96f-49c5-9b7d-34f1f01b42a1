package com.joinutech.addressbook.viewModel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.joinutech.addressbook.constract.FriendListConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.FriendSelectBean
import com.joinutech.ddbeslibrary.bean.UserInfo
import com.joinutech.ddbeslibrary.utils.StringUtils
import javax.inject.Inject
import javax.inject.Named

/**
 * @Description: 好友选择列表ViewModel
 * @Author: hjr
 * @Time: 2020/2/24 12:39
 * @packageName: com.joinutech.addressbook.viewModel
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
class FriendSelectViewModel : ViewModel() {

    @Inject
    @field:Named(AddressbookUtil.FRIENDLIST_MODULE)
    lateinit var module: FriendListConstract.FriendListModule

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    /**搜索好友后监听*/
    var getFriendListSuccessObservable = MutableLiveData<ArrayList<FriendSelectBean>>()
//    /**加载和搜索好友后回调*/
//    var getFriendListErrorObservable: MutableLiveData<String> = MutableLiveData()

    var rightCompleteColorShowObservable: MutableLiveData<Boolean> = MutableLiveData()

    fun setPersonList(list: ArrayList<UserInfo>, searchTextValue: String) {
        if (!list.isNullOrEmpty()) {
            val returnList = arrayListOf<FriendSelectBean>()
            list.forEach {
                if (StringUtils.isEmpty(searchTextValue)) {
//                    returnList.add(FriendSelectBean(it.userName, it.userId, it.userIcon))
                    returnList.add(FriendSelectBean(userId = it.userId, name = it.userName, avatar = it.userIcon))
                } else {
                    if (it.userName.contains(searchTextValue)) {
//                        returnList.add(FriendSelectBean(it.userName, it.userId, it.userIcon))
                        returnList.add(FriendSelectBean(userId = it.userId, name = it.userName, avatar = it.userIcon))
                    }
                }
            }
            getFriendListSuccessObservable.value = returnList
        }
    }

    fun setIMVcPersonList(list: ArrayList<FriendSelectBean>, searchTextValue: String) {
        if (!list.isNullOrEmpty()) {
            val returnList = arrayListOf<FriendSelectBean>()
            list.forEach {
                if (StringUtils.isEmpty(searchTextValue))
                    returnList.add(it)
                else {
                    if (it.name.contains(searchTextValue)) {
                        returnList.add(it)
                    }
                }
            }
            getFriendListSuccessObservable.value = returnList
        }
    }

    fun setRightCompleteColorShow(list: ArrayList<FriendSelectBean>) {
        if (!list.isNullOrEmpty()) {
            var isShow = false
            list.forEach {
                if (it.select) {
                    isShow = true
                    return@forEach
                }
            }
            rightCompleteColorShowObservable.value = isShow
        } else {
            rightCompleteColorShowObservable.value = false
        }
    }

    fun chargeRightCompleteColorShow(list: ArrayList<String>) {
        rightCompleteColorShowObservable.value = !list.isNullOrEmpty()
    }
}