package com.joinutech.addressbook.viewModel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.module.BatchProcessUndoMsgModule
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.BatchProcessUndoBean
import com.joinutech.ddbeslibrary.bean.CompanyUndoListBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

/**
 * description ： 批量处理待处理信息viewModel
 * author: 黄洁如
 * date : 2019/10/22
 */
class BatchProcessUndoMsgViewModel :ViewModel(){

    @Inject
    @field:Named(AddressbookUtil.BATCH_PROCESS_MODULE)
    lateinit var module: BatchProcessUndoMsgModule
    var batchProcessUndoListSuccessObservable = MutableLiveData<List<CompanyUndoListBean>>()
    var batchProcessUndoListErrorObservable: MutableLiveData<String> = MutableLiveData()
    var batchProcessUndoSuccessObservable = MutableLiveData<Int>()
    var batchProcessUndoErrorObservable: MutableLiveData<String> = MutableLiveData()

    init{
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    fun batchProcessUndoMsgList(life: LifecycleTransformer<Result<BatchProcessUndoBean>>,
                                token: String, companyId:String) {
        module.batchProcessUndoMsgList(life,token,companyId,{
            batchProcessUndoListSuccessObservable.value =it.data
        },{
            batchProcessUndoListErrorObservable.value = it
        })
    }
    fun batchProcessUndoMsg(life: LifecycleTransformer<Result<Any>>,
                            token: String, data: Any, dealFlag: Int) {
        module.batchProcessUndoMsg(life,token,data,{
            batchProcessUndoSuccessObservable.value =dealFlag
        },{
            batchProcessUndoErrorObservable.value = it
        })
    }
}