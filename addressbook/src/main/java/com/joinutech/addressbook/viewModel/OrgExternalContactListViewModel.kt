package com.joinutech.addressbook.viewModel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.module.OrgExternalContactListModuel
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.ExternalContactListBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/2 10:12
 * @packageName: com.joinutech.addressbook.viewModel
 * @Company: JoinuTech
 */
class OrgExternalContactListViewModel : ViewModel() {

    @Inject
    @field:Named(AddressbookUtil.ORG_EXTERNAL_CONTACT_LIST_MODULE)
    lateinit var module: OrgExternalContactListModuel
    var getExternalContactListSuccessObservable = MutableLiveData<List<ExternalContactListBean>>()
    var getExternalContactListErrorObservable: MutableLiveData<String> = MutableLiveData()

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    fun getExternalContactList(life: LifecycleTransformer<Result<List<ExternalContactListBean>>>,
                               companyId: String, keyword: String, level: String, type: String,
                               token: String) {
        module.getExternalContactList(life, companyId, keyword, level, type, token, {
            getExternalContactListSuccessObservable.value = it
        }, {
            getExternalContactListErrorObservable.value = it
        })
    }
}