package com.joinutech.addressbook.viewModel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.joinutech.addressbook.constract.AddressbookConstract
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.OrgChartBean
import com.joinutech.ddbeslibrary.bean.OrgImportDeptBean
import com.joinutech.ddbeslibrary.request.CommonResult
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.service.AddressbookService
import com.trello.rxlifecycle3.LifecycleTransformer
import io.reactivex.Flowable
import javax.inject.Inject
import javax.inject.Named

/**
 * description ： TODO:类的作用
 * author: 黄洁如
 * date : 2019/10/23
 */
class OrgImportPersonViewModel : ViewModel() {

    @Inject
    @field:Named(AddressbookUtil.ADDR_MODULE)
    lateinit var module: AddressbookConstract.AddressbookModule
    var queryMmebersNewSuccessObservable = MutableLiveData<OrgImportDeptBean>()
    var queryMmebersNewErrorObservable = MutableLiveData<String>()

    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    fun queryMembersNew(token: String?, companyId: String, deptId: String,
                        life: LifecycleTransformer<Result<OrgImportDeptBean>>) {

        if (token == null) return
        module.queryMmebersNew(token, companyId, deptId, life, {
            queryMmebersNewSuccessObservable.value = it
        }, {
            queryMmebersNewErrorObservable.value = it
        })
    }
}

// TODO: 2021/6/24 按部门查询成员 搜索公司成员 @Route(path = RouteOrg.searchResultActivity) class SearchResultActivity
class OrgMemberSelectViewModel/*(val repository: OrgMemberSelectRepository)*/ : ViewModel() {
    // 实例化数据仓库，kotlin更好的方式是通过 koin scope 注解到构造方法中
    private val repository: OrgMemberSelectRepository by lazy {
        OrgMemberSelectRepository()
    }
    private var _queryMemberResult = MutableLiveData<CommonResult<OrgChartBean>>()
    private var _queryAllMemberResult = MutableLiveData<CommonResult<OrgImportDeptBean>>()

    fun getMemberResult(): MutableLiveData<CommonResult<OrgChartBean>> {
        return _queryMemberResult
    }

    fun queryMemberByDeptId(companyId: String, deptId: String) {
        repository.queryMemberByDeptId(companyId, deptId, _queryMemberResult)
    }

    fun getAllMemberResult(): MutableLiveData<CommonResult<OrgImportDeptBean>> {
        return _queryAllMemberResult
    }

    fun queryAllDeptAndMember(companyId: String, deptId: String) {
        repository.queryAllDeptAndMember(companyId, deptId, _queryAllMemberResult)
    }

}

class OrgMemberSelectRepository : BaseRepository() {

    fun queryAllDeptAndMember(companyId: String, deptId: String,
                              _queryMemberResult: MutableLiveData<CommonResult<OrgImportDeptBean>>) {
        // 这里既可以执行网络请求，也可以执行本地查询，隔离的用户对数据来源的疑惑
        request(AddressbookService.service.queryOrgDept(companyId, deptId),
                _queryMemberResult, "获取组织架构")
    }

    fun queryMemberByDeptId(companyId: String, deptId: String,
                            _queryMemberResult: MutableLiveData<CommonResult<OrgChartBean>>) {
        // 这里既可以执行网络请求，也可以执行本地查询，隔离的用户对数据来源的疑惑
        request(AddressbookService.service.quitChartMember(companyId, deptId),
                _queryMemberResult, "获取部门成员")
    }
}

open class BaseRepository {
    fun <T> request(api: Flowable<Result<T>>, _result: MutableLiveData<CommonResult<T>>, tag: String = "_commonRequest") {
        RequestHelper.onRequest(api, _result, tag)
    }
}