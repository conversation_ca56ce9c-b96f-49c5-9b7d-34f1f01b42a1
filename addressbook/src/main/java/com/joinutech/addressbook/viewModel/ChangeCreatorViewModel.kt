package com.joinutech.addressbook.viewModel

import android.content.Context
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.joinutech.addressbook.inject.DaggerAddressbookComponent
import com.joinutech.addressbook.module.ChangeCreatorReposity
import com.joinutech.addressbook.util.AddressbookUtil
import com.joinutech.ddbeslibrary.bean.VerifyImageBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer
import javax.inject.Inject
import javax.inject.Named

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/11/27 16:40
 * @packageName: com.joinutech.addressbook.viewModel
 * @Company: JoinuTech
 */
class ChangeCreatorViewModel :ViewModel(){

    @Inject
    @field:Named(AddressbookUtil.CHANGE_PASSWORD_REPOSITORY)
    lateinit var module: ChangeCreatorReposity
    var getVerifyImageSuccessObservable = MutableLiveData<VerifyImageBean>()
    var getVerifyImageErrorObservable: MutableLiveData<String> = MutableLiveData()
    var verifyImageSuccessObservable = MutableLiveData<Any>()
    var verifyImageErrorObservable: MutableLiveData<String> = MutableLiveData()
    var sendSmsSuccessObservable = MutableLiveData<Any>()
    var sendSmsErrorObservable: MutableLiveData<String> = MutableLiveData()
    init {
        DaggerAddressbookComponent.builder().build().inject(this)
    }

    fun getVerifyImage(life: LifecycleTransformer<Result<VerifyImageBean>>, phone: String){
        module.getVerifyImage(life,phone,{
            getVerifyImageSuccessObservable.value =it
        },{
            getVerifyImageErrorObservable.value =it
        })
    }

    fun sendSms(life: LifecycleTransformer<Result<Any>>, context: Context?,
                phone: String, type:Int) {
        module.sendSms(life,context,phone,type,{
            sendSmsSuccessObservable.value =it
        },{
            sendSmsErrorObservable.value =it
        })
    }

    fun verifyImageWithMsg(life: LifecycleTransformer<Result<Any>>, dataMap: Any){
        module.verifyImageWithMsg(life,dataMap,{
            verifyImageSuccessObservable.value =it
        },{
            verifyImageErrorObservable.value =it
        })
    }
}