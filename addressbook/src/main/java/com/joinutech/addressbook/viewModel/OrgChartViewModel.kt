package com.joinutech.addressbook.viewModel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.joinutech.ddbeslibrary.bean.OrgDeptSortBean
import com.joinutech.ddbeslibrary.request.CommonResult
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.AddressbookService

/**
 * @Description: TODO
 * @Author: zhaoyy
 * @Time: 2021/6/4 10:57
 * @packageName: com.joinutech.addressbook.viewModel
 * @Company: Copyright (C),2019- 2021,JoinuTech
 */
class OrgChartViewModel : ViewModel() {

    private val _deptSortResult = MutableLiveData<CommonResult<String>>()
    val deptSortResult: LiveData<CommonResult<String>> = _deptSortResult


    fun orgDeptSort(data: OrgDeptSortBean) {
        RequestHelper.onRequest(AddressbookService.orgDeptSort(data), _deptSortResult, "部门排序")
//        RxScheduleUtil.rxSchedulerHelper(AddressbookService.orgDeptSort(data))
//                .compose(ErrorTransformer.getInstance<Any>())
//                .subscribe(object : BaseSubscriber<Any>() {
//                    override fun onError(ex: ApiException) {
//                        _deptSortResult.value = CommonResult(errorCode = ex.code,extra = ex.message)
//                    }
//
//                    override fun onComplete() {
//                    }
//
//                    override fun onNext(t: Any) {
//                        _deptSortResult.value = CommonResult(t)
//                    }
//
//                })
    }
}