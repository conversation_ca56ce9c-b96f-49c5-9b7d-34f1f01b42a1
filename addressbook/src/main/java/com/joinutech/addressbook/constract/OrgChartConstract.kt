package com.joinutech.addressbook.constract

import com.joinutech.ddbeslibrary.bean.OrgChartBean
import com.joinutech.ddbeslibrary.bean.OrgChartDepBean
import com.trello.rxlifecycle3.LifecycleTransformer
import com.joinutech.ddbeslibrary.request.Result

interface OrgChartConstract {

    interface OrgChartPresenter {
        fun getOrgChartDataResult(life: LifecycleTransformer<Result<OrgChartBean>>,
                                  token: String,
                                  companyId: String,deptId : String,
                                  onSuccess: (OrgChartBean) -> Unit, onError: (String) -> Unit)
        fun addChildDep(life: LifecycleTransformer<Result<Any>>,
                                  token: String,
                                  companyId: String,deptId : String,name:String,
                                  onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }

    interface OrgChartModule {
        fun getOrgChartDataResult(life: LifecycleTransformer<Result<OrgChartBean>>,
                                  token: String,
                                  companyId: String,deptId : String,
                                  onSuccess: (OrgChartBean) -> Unit, onError: (String) -> Unit)
        fun addChildDep(life: LifecycleTransformer<Result<Any>>,
                        token: String,
                        companyId: String,deptId : String,name:String,
                        onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }
}