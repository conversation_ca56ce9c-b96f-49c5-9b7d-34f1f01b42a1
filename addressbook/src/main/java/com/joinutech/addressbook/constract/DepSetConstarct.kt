package com.joinutech.addressbook.constract

import com.joinutech.ddbeslibrary.bean.OrgDepHeadBean
import com.joinutech.ddbeslibrary.bean.ValidateAttendanceBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer

interface DepSetConstarct {

    interface DepSetPresenter {
        fun changeDepName(life: LifecycleTransformer<Result<Any>>,
                          token: String,
                          companyId: String, deptId: String, depName: String,
                          onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun checkAttendGroup(life: LifecycleTransformer<Result<ValidateAttendanceBean>>, data: Any,
                             onSuccess: (ValidateAttendanceBean) -> Unit, onError: (String) -> Unit)

        fun deleteDep(life: LifecycleTransformer<Result<Any>>,
                      token: String, companyId: String, deptId: String,
                      onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun showDepHeadDetail(life: LifecycleTransformer<Result<OrgDepHeadBean>>,
                              token: String, deptId: String, companyId: String,
                              onSuccess: (OrgDepHeadBean) -> Unit, onError: (String) -> Unit)

        fun deleteDepHeadPosition(life: LifecycleTransformer<Result<Any>>,
                                  token: String, positionId: String,
                                  onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }

    interface DepSetModule {
        fun changeDepName(life: LifecycleTransformer<Result<Any>>,
                          token: String,
                          companyId: String, deptId: String, depName: String,
                          onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun checkAttendGroup(life: LifecycleTransformer<Result<ValidateAttendanceBean>>, data: Any,
                             onSuccess: (ValidateAttendanceBean) -> Unit, onError: (String) -> Unit)

        fun deleteDep(life: LifecycleTransformer<Result<Any>>,
                      token: String, companyId: String, deptId: String,
                      onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun showDepHeadDetail(life: LifecycleTransformer<Result<OrgDepHeadBean>>,
                              token: String, deptId: String, companyId: String,
                              onSuccess: (OrgDepHeadBean) -> Unit, onError: (String) -> Unit)

        fun deleteDepHeadPosition(life: LifecycleTransformer<Result<Any>>,
                                  token: String, positionId: String,
                                  onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }
}