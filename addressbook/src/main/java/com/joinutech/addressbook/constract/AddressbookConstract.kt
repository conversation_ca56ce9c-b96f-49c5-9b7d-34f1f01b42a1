package com.joinutech.addressbook.constract

import com.joinutech.ddbeslibrary.bean.*
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer

/**
 *<AUTHOR>
 *@date 2018/11/6
 */
interface AddressbookConstract {
    interface AddressbookPresenter {
//        fun getCompanys(life: LifecycleTransformer<Result<CompanyListBean>>, token: String, userId: String, onSuccess: (CompanyListBean) -> Unit, onError: (String) -> Unit)
        fun getIndustry(life: LifecycleTransformer<Result<List<JobChoiceBean>>>, token: String, type: String, onSuccess: (List<JobChoiceBean>) -> Unit, onError: (String) -> Unit)
        fun getCompanyTypes(life: LifecycleTransformer<Result<List<CompanyTypeBean>>>, token: String, onSuccess: (List<CompanyTypeBean>) -> Unit, onError: (String) -> Unit)
        fun creatCompany(life: LifecycleTransformer<Result<Any>>, token: String, bean: Any, onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun modifyCompany(life: LifecycleTransformer<Result<Any>>, token: String, bean: Any, onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun changeOrgCreator(life: LifecycleTransformer<Result<Any>>, bean: Any, onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun queryCompanyById(life: LifecycleTransformer<Result<CompanyBean>>, token: String, companyId: String, onSuccess: (CompanyBean) -> Unit, onError: (String) -> Unit)
        fun queryValidate(life: LifecycleTransformer<Result<Any>>, token: String, companyId: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun getMainCompany(life: LifecycleTransformer<Result<CompanyBean>>, token: String, onSuccess: (CompanyBean) -> Unit, onError: (String) -> Unit)
        fun disbandCompany(life: LifecycleTransformer<Result<Any>>, token: String, companyId: String, phone: String, code: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun quitCompany(life: LifecycleTransformer<Result<Any>>, token: String, companyId: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun creatGroup(life: LifecycleTransformer<Result<Any>>, token: String, userId: ArrayList<String>, onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun queryGroup(life: LifecycleTransformer<Result<List<GroupBean>>>,
                       token: String, onSuccess: (List<GroupBean>) -> Unit, onError: (String) -> Unit)

        fun orgPermissionList(life: LifecycleTransformer<Result<List<OrgPermissionListBean>>>,
                              token: String, companyId: String, onSuccess: (List<OrgPermissionListBean>) -> Unit,
                              onError: (String) -> Unit)

        fun addManager(life: LifecycleTransformer<Result<Any>>,
                       token: String, companyId: String, map: Map<String, Any>,
                       onSuccess: (Any) -> Unit,
                       onError: (String) -> Unit)

        fun editManager(life: LifecycleTransformer<Result<Any>>,
                        token: String, companyId: String, map: Map<String, Any>,
                        onSuccess: (Any) -> Unit,
                        onError: (String) -> Unit)

        fun deleteManager(life: LifecycleTransformer<Result<Any>>,
                          token: String, companyId: String, managerId: String,
                          onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun commitPersonInfo(life: LifecycleTransformer<Result<PersonInfoBean>>,
                             name: String, type: String,
                             onSuccess: (PersonInfoBean) -> Unit, onError: (String) -> Unit)
    }

    interface AddressbookModule {
//        fun getCompanys(life: LifecycleTransformer<Result<CompanyListBean>>, token: String, userId: String, onSuccess: (CompanyListBean) -> Unit, onError: (String) -> Unit)
        fun getIndustry(life: LifecycleTransformer<Result<List<JobChoiceBean>>>, token: String, type: String, onSuccess: (List<JobChoiceBean>) -> Unit, onError: (String) -> Unit)
        fun getCompanyTypes(life: LifecycleTransformer<Result<List<CompanyTypeBean>>>, token: String, onSuccess: (List<CompanyTypeBean>) -> Unit, onError: (String) -> Unit)
        fun creatCompany(life: LifecycleTransformer<Result<Any>>, token: String, bean: Any, onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun modifyCompany(life: LifecycleTransformer<Result<Any>>, token: String, bean: Any, onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun changeOrgCreator(life: LifecycleTransformer<Result<Any>>, bean: Any, onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun queryCompanyById(life: LifecycleTransformer<Result<CompanyBean>>, token: String, companyId: String, onSuccess: (CompanyBean) -> Unit, onError: (String) -> Unit)
        fun queryValidate(life: LifecycleTransformer<Result<Any>>, token: String, companyId: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun getMainCompany(life: LifecycleTransformer<Result<CompanyBean>>, token: String, onSuccess: (CompanyBean) -> Unit, onError: (String) -> Unit)
        fun disbandCompany(life: LifecycleTransformer<Result<Any>>, token: String, companyId: String, phone: String, code: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun quitCompany(life: LifecycleTransformer<Result<Any>>, token: String, companyId: String, onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun creatGroup(life: LifecycleTransformer<Result<Any>>, token: String, userId: ArrayList<String>, onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun queryGroup(life: LifecycleTransformer<Result<List<GroupBean>>>, token: String, onSuccess: (List<GroupBean>) -> Unit, onError: (String) -> Unit)
        fun orgPermissionList(life: LifecycleTransformer<Result<List<OrgPermissionListBean>>>,
                              token: String, companyId: String,
                              onSuccess: (List<OrgPermissionListBean>) -> Unit,
                              onError: (String) -> Unit)

        fun addManager(life: LifecycleTransformer<Result<Any>>,
                       token: String, companyId: String, map: Map<String, Any>,
                       onSuccess: (Any) -> Unit,
                       onError: (String) -> Unit)

        fun editManager(life: LifecycleTransformer<Result<Any>>,
                        token: String, companyId: String, map: Map<String, Any>,
                        onSuccess: (Any) -> Unit,
                        onError: (String) -> Unit)

        fun deleteManager(life: LifecycleTransformer<Result<Any>>,
                          token: String, companyId: String, managerId: String,
                          onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun commitPersonInfo(life: LifecycleTransformer<Result<PersonInfoBean>>,
                             name: String, type: String,
                             onSuccess: (PersonInfoBean) -> Unit, onError: (String) -> Unit)

        fun queryMmebersNew(token: String, companyId: String,
                            deptId: String, life: LifecycleTransformer<Result<OrgImportDeptBean>>,
                            onSuccess: (OrgImportDeptBean) -> Unit, onError: (String) -> Unit)
    }

}