package com.joinutech.addressbook.constract

import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer

interface FriendRemarkNameConstract {

    interface FriendRemarkNamePresenter{
        fun remarkFriend(life: LifecycleTransformer<Result<Any>>,
                         token: String, remark: String, targetUserId  : String,
                         onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }

    interface FriendRemarkNameModule{
        fun remarkFriend(life: LifecycleTransformer<Result<Any>>,
                         token: String, remark: String,targetUserId  : String,
                         onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }
}