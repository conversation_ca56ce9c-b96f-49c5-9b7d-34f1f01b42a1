package com.joinutech.addressbook.constract

import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer

interface FriendInfoConstract {

    interface FriendInfoPresenter {
        fun leaveUser(life: LifecycleTransformer<Result<Any>>,
                      token: String,
                      users: ArrayList<String>, companyId: String,
                      onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun leaveUserValidate(life: LifecycleTransformer<Result<Any>>,
                              token: String,
                              users: ArrayList<String>, companyId: String,
                              onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun deleteFriend(life: LifecycleTransformer<Result<Any>>,
                         token: String, targetUserId: String,
                         onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun getCompanyUserInfo(life: LifecycleTransformer<Result<Any>>,
                        token: String, companyId: String = "", userId: String,
                        onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun volidate(life: LifecycleTransformer<Result<Any>>,
                     token: String, userId: String, type: Int,
                     onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }

    interface FriendInfoModule {
        fun leaveUser(life: LifecycleTransformer<Result<Any>>,
                      token: String,
                      users: List<String>, companyId: String,
                      onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun leaveUserValidate(life: LifecycleTransformer<Result<Any>>,
                              token: String,
                              users: ArrayList<String>, companyId: String,
                              onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun deleteFriend(life: LifecycleTransformer<Result<Any>>,
                         token: String, targetUserId: String,
                         onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun getCompanyUserInfo(life: LifecycleTransformer<Result<Any>>,
                        token: String, companyId: String = "", userId: String,
                        onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun volidate(life: LifecycleTransformer<Result<Any>>,
                     token: String, userId: String, type: Int,
                     onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }
}