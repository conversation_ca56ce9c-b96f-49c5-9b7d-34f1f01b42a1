package com.joinutech.addressbook.constract

import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer

interface SelectedMemberConstract {

    interface SelectedMemberPresenter{
        fun leaveUser(life: LifecycleTransformer<Result<Any>>,
                      token: String,
                      users: ArrayList<String>, companyId: String,
                      onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun leaveUserValidate(life: LifecycleTransformer<Result<Any>>,
                      token: String,
                      users: ArrayList<String>, companyId: String,
                      onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun changeDepHead(life: LifecycleTransformer<Result<Any>>,
                          token: String,deptId : String,positionId  : String,
                          userId : String, positionName:String,type:Int, companyId: String,
                          onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }

    interface SelectedMemberModule{
        fun leaveUser(life: LifecycleTransformer<Result<Any>>,
                      token: String,
                      users:List<String>,companyId: String,
                      onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun leaveUserValidate(life: LifecycleTransformer<Result<Any>>,
                              token: String,
                              users: ArrayList<String>, companyId: String,
                              onSuccess: (Any) -> Unit, onError: (String) -> Unit)
        fun changeDepHead(life: LifecycleTransformer<Result<Any>>,
                          token: String,deptId : String,positionId  : String,
                          userId : String, positionName:String,type:Int, companyId: String,
                          onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }
}