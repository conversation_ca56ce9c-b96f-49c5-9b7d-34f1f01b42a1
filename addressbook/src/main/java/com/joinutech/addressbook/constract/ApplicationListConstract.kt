package com.joinutech.addressbook.constract

import com.joinutech.ddbeslibrary.bean.*
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer

interface ApplicationListConstract {

    interface ApplicationListPresenter{
        fun getFriendUndoList(life: LifecycleTransformer<Result<UndoBean>>,
                              token: String, type:Int,
                              page:Int, size:Int,
                              onSuccess: (UndoBean) -> Unit,
                              onError: (String) -> Unit)
        fun getCompanyUndoList(life: LifecycleTransformer<Result<CompanyUndoBean>>,
                               token: String, page:Int, size:Int,companyId:String,
                               onSuccess: (CompanyUndoBean) -> Unit,
                               onError: (String) -> Unit)
    }

    interface ApplicationListModule{
        fun getFriendUndoList(life: LifecycleTransformer<Result<UndoBean>>,
                              token: String, type:Int,
                              page:Int, size:Int,
                              onSuccess: (UndoBean) -> Unit,
                              onError: (String) -> Unit)
        fun getCompanyUndoList(life: LifecycleTransformer<Result<CompanyUndoBean>>,
                               token: String, page:Int, size:Int,companyId:String,
                               onSuccess: (CompanyUndoBean) -> Unit,
                               onError: (String) -> Unit)
    }
}