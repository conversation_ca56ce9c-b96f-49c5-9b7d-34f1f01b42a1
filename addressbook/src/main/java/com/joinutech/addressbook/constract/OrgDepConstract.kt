package com.joinutech.addressbook.constract

import com.joinutech.ddbeslibrary.bean.OrgChartDepBean
import com.joinutech.ddbeslibrary.bean.ValidateAttendanceBean
import com.trello.rxlifecycle3.LifecycleTransformer
import com.joinutech.ddbeslibrary.request.Result

interface OrgDepConstract {
    interface OrgDepPresenter {
        fun getOrgChartDepResult(life: LifecycleTransformer<Result<OrgChartDepBean>>,
                                 token: String,
                                 companyId: String, deptId: String,
                                 onSuccess: (OrgChartDepBean) -> Unit, onError: (String) -> Unit)

        fun changeMmeberDep(life: LifecycleTransformer<Result<Any>>,
                            token: String,
                            companyId: String, deptId: String, userIds: ArrayList<String>,
                            onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun changeAboveDep(life: LifecycleTransformer<Result<Any>>,
                           token: String,
                           companyId: String, deptId: String, targetDeptId: String,
                           onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        @Deprecated("no used")
        fun addChildDep(life: LifecycleTransformer<Result<Any>>,
                        token: String,
                        companyId: String, deptId: String, name: String,
                        onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun validateAttendanceGroup(life: LifecycleTransformer<Result<ValidateAttendanceBean>>,
                                    token: String,
                                    companyId: String, deptId: String,
                                    userIds: ArrayList<String>, onSuccess: (ValidateAttendanceBean) -> Unit,
                                    onError: (String) -> Unit)

        fun updateAttendanceGroup(life: LifecycleTransformer<Result<Any>>, token: String,
                                  companyId: String, deptId: String, userIds: ArrayList<String>,
                                  ischange: Int, onSuccess: (Any) -> Unit,
                                  onError: (String) -> Unit)
    }

    interface OrgDepModule {
        fun getOrgChartDepResult(life: LifecycleTransformer<Result<OrgChartDepBean>>,
                                 token: String,
                                 companyId: String, deptId: String,
                                 onSuccess: (OrgChartDepBean) -> Unit, onError: (String) -> Unit)

        fun changeMmeberDep(life: LifecycleTransformer<Result<Any>>,
                            token: String,
                            companyId: String, deptId: String, userIds: ArrayList<String>,
                            onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun changeAboveDep(life: LifecycleTransformer<Result<Any>>,
                           token: String,
                           companyId: String, deptId: String, targetDeptId: String,
                           onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        @Deprecated("no used")
        fun addChildDep(life: LifecycleTransformer<Result<Any>>,
                        token: String,
                        companyId: String, deptId: String, name: String,
                        onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun validateAttendanceGroup(life: LifecycleTransformer<Result<ValidateAttendanceBean>>,
                                    token: String,
                                    companyId: String, deptId: String,
                                    userIds: ArrayList<String>, onSuccess: (ValidateAttendanceBean) -> Unit,
                                    onError: (String) -> Unit)

        fun updateAttendanceGroup(life: LifecycleTransformer<Result<Any>>, token: String,
                                  companyId: String, deptId: String, userIds: ArrayList<String>,
                                  ischange: Int, onSuccess: (Any) -> Unit,
                                  onError: (String) -> Unit)
    }
}