package com.joinutech.addressbook.constract

import com.joinutech.ddbeslibrary.bean.CompanyBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer

interface OrganizationIntroConstact  {

    interface OrganizationIntroPresenter{
        fun getOrganizationInfo(life: LifecycleTransformer<Result<CompanyBean>>,
                                token: String,
                                companyId: String,
                                onSuccess: (CompanyBean) -> Unit, onError: (String) -> Unit)
        fun volidate(life: LifecycleTransformer<Result<Int>>,
                     token: String,  companyId: String,
                     onSuccess: (Int) -> Unit, onError: (String) -> Unit)
    }
    interface OrganizationIntroMoudle{
        fun getOrganizationInfo(life: LifecycleTransformer<Result<CompanyBean>>,
                                token: String,
                                companyId: String,
                                onSuccess: (CompanyBean) -> Unit, onError: (String) -> Unit)
        fun volidate(life: LifecycleTransformer<Result<Int>>,
                     token: String,  companyId: String,
                     onSuccess: (Int) -> Unit, onError: (String) -> Unit)
    }
}