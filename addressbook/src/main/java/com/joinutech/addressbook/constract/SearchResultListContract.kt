package com.joinutech.addressbook.constract

import com.joinutech.ddbeslibrary.bean.CompanyBean
import com.joinutech.ddbeslibrary.bean.FriendSimpleBean
import com.joinutech.ddbeslibrary.bean.PhoneSearchFriendBean
import com.joinutech.ddbeslibrary.bean.SearchMemberBean
import com.ddbes.library.im.imtcp.dbbean.FriendBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer

interface SearchResultListContract {

    interface SearchResultListPresenter {
        fun searchResult(life: LifecycleTransformer<Result<List<CompanyBean>>>,
                         token: String,
                         content: String,
                         onSuccess: (List<CompanyBean>) -> Unit, onError: (String) -> Unit)

        fun dealMemberResult(life: LifecycleTransformer<Result<List<SearchMemberBean>>>,
                             token: String, content: String, companyId: String,
                             onSuccess: (List<SearchMemberBean>) -> Unit, onError: (String) -> Unit)

        fun searchPhoneResult(life: LifecycleTransformer<Result<PhoneSearchFriendBean>>,
                              token: String, content: String,
                              onSuccess: (PhoneSearchFriendBean) -> Unit, onError: (String) -> Unit)

        @Deprecated("no used")
        fun searchFriendList(life: LifecycleTransformer<Result<List<FriendBean>>>,
                             token: String, content: String,
                             onSuccess: (List<FriendBean>) -> Unit, onError: (String) -> Unit)

        @Deprecated("no used")
        fun searchExternalFriendList(life: LifecycleTransformer<Result<List<FriendSimpleBean>>>,
                                     token: String, content: String,
                                     onSuccess: (List<FriendSimpleBean>) -> Unit, onError: (String) -> Unit)
    }

    interface SearchResultListModule {
        fun searchResult(life: LifecycleTransformer<Result<List<CompanyBean>>>,
                         token: String,
                         content: String,
                         onSuccess: (List<CompanyBean>) -> Unit, onError: (String) -> Unit)

        fun dealMemberResult(life: LifecycleTransformer<Result<List<SearchMemberBean>>>,
                             token: String, content: String, companyId: String,
                             onSuccess: (List<SearchMemberBean>) -> Unit, onError: (String) -> Unit)

        fun searchPhoneResult(life: LifecycleTransformer<Result<PhoneSearchFriendBean>>,
                              token: String,
                              content: String,
                              onSuccess: (PhoneSearchFriendBean) -> Unit, onError: (String) -> Unit)

        @Deprecated("no used")
        fun searchFriendList(life: LifecycleTransformer<Result<List<FriendBean>>>,
                             token: String, content: String,
                             onSuccess: (List<FriendBean>) -> Unit, onError: (String) -> Unit)

        @Deprecated("no used")
        fun searchExternalFriendList(life: LifecycleTransformer<Result<List<FriendSimpleBean>>>,
                                     token: String, content: String,
                                     onSuccess: (List<FriendSimpleBean>) -> Unit,
                                     onError: (String) -> Unit)
    }
}