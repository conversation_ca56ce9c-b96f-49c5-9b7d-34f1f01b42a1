package com.joinutech.addressbook.constract

import android.content.Context
import com.joinutech.ddbeslibrary.bean.ContactModel
import com.joinutech.ddbeslibrary.bean.PhoneContactDataBean
import com.trello.rxlifecycle3.LifecycleTransformer
import com.joinutech.ddbeslibrary.request.Result

interface PhoneContactConstract {

    interface PhoneContactPresenter{

        fun getContactStatusList(life: LifecycleTransformer<Result<List<PhoneContactDataBean>>>,
                        token: String, phones:ArrayList<String>,
                        onSuccess: (List<PhoneContactDataBean>) -> Unit, onError: (String) -> Unit)
        fun getPhoneContactList(mContext: Context):List<ContactModel>
        fun dealReturnPhoneContactData(list: List<PhoneContactDataBean>, mShowModels: List<ContactModel>, userId  : String?):ArrayList<ContactModel>
    }

    interface PhoneContactModule{

        fun getContactStatusList(life: LifecycleTransformer<Result<List<PhoneContactDataBean>>>,
                                 token: String, phones:ArrayList<String>,
                                 onSuccess: (List<PhoneContactDataBean>) -> Unit,
                                 onError: (String) -> Unit)
    }
}