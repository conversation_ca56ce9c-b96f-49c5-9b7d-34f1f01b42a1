package com.joinutech.addressbook.constract

import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer

interface VerifyApplicationConstact {

    interface VerifyApplicationPresenter{
        fun verifyOrgApplication(life: LifecycleTransformer<Result<Int>>,
                                 token: String,
                                 targetUserId  : String,
                                 content:String,
                                 onSuccess: (Int) -> Unit, onError: (String) -> Unit)
        fun verfyFriendApplication(life: LifecycleTransformer<Result<Any>>,
                                   token: String,
                                   userId  : String,
                                   message:String,
                                   onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }
    interface VerifyApplicationModule{
        fun verifyOrgApplication(life: LifecycleTransformer<Result<Int>>,
                                 token: String,
                                 companyId  : String,
                                 content:String,
                                 onSuccess: (Int) -> Unit, onError: (String) -> Unit)
        fun verfyFriendApplication(life: LifecycleTransformer<Result<Any>>,
                                   token: String,
                                   userId  : String,
                                   message:String,
                                   onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }
}