package com.joinutech.addressbook.constract

import com.joinutech.ddbeslibrary.bean.CompanyUndoListBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer

interface ApplicationDetailConstract {

    interface ApplicationDetailPresenter {
        /**同意加入团队*/
        fun orgAgreeUserJoin(life: LifecycleTransformer<Result<Any>>,
                             token: String,
                             map: Any,
                             onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        /**拒绝加入团队*/
        fun rejectUserJoin(life: LifecycleTransformer<Result<Any>>,
                           token: String,
                           map: Any,
                           onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        /**同意添加好友*/
        fun agreeAddFriend(life: LifecycleTransformer<Result<Any>>,
                           token: String, applyId: String, opt: Int,
                           onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        /**拒绝添加好友*/
        fun rejectAddFriendJoin(life: LifecycleTransformer<Result<Any>>,
                                token: String,applyId: String, opt: Int,
                                onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun getApplicationDetailData(life: LifecycleTransformer<Result<Any>>,
                                     token: String, paramId: String, status: Int,
                                     onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }

    interface ApplicationDetailModule {
        fun orgAgreeUserJoin(life: LifecycleTransformer<Result<Any>>,
                             token: String,
                             map: Any,
                             onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun rejectUserJoin(life: LifecycleTransformer<Result<Any>>,
                           token: String,
                           map: Any,
                           onSuccess: (Any) -> Unit, onError: (String) -> Unit)


        fun agreeAddFriend(life: LifecycleTransformer<Result<Any>>,
                           token: String, applyId: String, opt: Int,
                           onSuccess: (Any) -> Unit, onError: (String) -> Unit)


        fun rejectAddFriendJoin(life: LifecycleTransformer<Result<Any>>,
                                token: String, applyId: String, opt: Int,
                                onSuccess: (Any) -> Unit, onError: (String) -> Unit)

        fun getApplicationDetailData(life: LifecycleTransformer<Result<Any>>,
                                     token: String, paramId: String, type: Int,
                                     onSuccess: (Any) -> Unit, onError: (String) -> Unit)
    }
}