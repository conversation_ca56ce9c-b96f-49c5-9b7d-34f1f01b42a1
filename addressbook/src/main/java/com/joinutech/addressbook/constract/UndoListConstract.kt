package com.joinutech.addressbook.constract

import com.joinutech.ddbeslibrary.bean.UndoBean
import com.trello.rxlifecycle3.LifecycleTransformer
import com.joinutech.ddbeslibrary.request.Result

interface UndoListConstract {

    interface UndoListPresenter {
        fun getUndoList(life: LifecycleTransformer<Result<UndoBean>>, status: Int, page: Int, size: Int,
                        onSuccess: (UndoBean) -> Unit, onError: (String) -> Unit)
    }

    interface UndoListModule {
        fun getUndoList(life: LifecycleTransformer<Result<UndoBean>>, status: Int, page: Int, size: Int,
                        onSuccess: (UndoBean) -> Unit, onError: (String) -> Unit)
    }
}