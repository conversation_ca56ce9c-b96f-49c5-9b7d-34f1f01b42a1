package com.joinutech.addressbook.constract

import com.joinutech.ddbeslibrary.bean.ContactModel
import com.ddbes.library.im.imtcp.dbbean.FriendBean
import com.joinutech.ddbeslibrary.request.Result
import com.trello.rxlifecycle3.LifecycleTransformer

interface FriendListConstract {

    interface FriendListPresenter {
        @Deprecated("no used")
        fun getFriendList(life: LifecycleTransformer<Result<List<FriendBean>>>,
                          token: String, content: String,
                          onSuccess: (List<FriendBean>) -> Unit, onError: (String) -> Unit)

        fun dealFriendList(it: List<FriendBean>): ArrayList<ContactModel>
    }

    interface FriendListModule {
        @Deprecated("no used")
        fun getFriendList(life: LifecycleTransformer<Result<List<FriendBean>>>,
                          token: String, content: String,
                          onSuccess: (List<FriendBean>) -> Unit,
                          onError: (String) -> Unit)
    }
}