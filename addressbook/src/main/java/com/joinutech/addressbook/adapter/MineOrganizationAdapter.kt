package com.joinutech.addressbook.adapter

import android.content.Context
import android.view.View
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import com.joinutech.addressbook.R
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.utils.ORG_PERMISS_TYPE
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter
import com.marktoo.lib.cachedweb.LogUtil

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/5 16:51
 * @packageName: com.joinutech.addressbook.adapter
 * @Company: JoinuTech
 */
class MineOrganizationAdapter(context: Context, dataList: ArrayList<WorkStationBean>)
    : CommonAdapter<WorkStationBean>(context, dataList, R.layout.item_mine_organization_new) {

    private var itemClickListener: ItemClickListener? = null
    private var noticeCompanyId = ""

    fun setItemClickListener(itemClickListener: ItemClickListener) {
        this.itemClickListener = itemClickListener
    }

    fun setNoticeCompanyId(noticeCompanyId: String) {
        this.noticeCompanyId = noticeCompanyId
        notifyDataSetChanged()
    }

    override fun bindData(holder: ViewHolder, data: WorkStationBean, position: Int) {
        val itemTitleLayout = holder.getView<View>(R.id.itemTitleLayout)
        val externalCompanyLayout = holder.getView<View>(R.id.externalCompanyLayout)
        val innerCompanyLayout = holder.getView<View>(R.id.cl_org_item_root)
        itemTitleLayout.visibility = View.GONE
        externalCompanyLayout.visibility = View.GONE
        innerCompanyLayout.visibility = View.GONE
        if (data.companyId == "0") {
            //title 分组标题
            itemTitleLayout.visibility = View.VISIBLE
            holder.setText(R.id.tv_item, data.name)
        } else {
            // 公司数据
            //是真正的item区分是不是合作团队
            if (data.isOuter) {
                externalCompanyLayout.visibility = View.VISIBLE
                holder.setText(R.id.orgName, data.name)
                ImageLoaderUtils.loadImage(mContext, holder.getView(R.id.orgLogo), data.logo)
//                Glide.with(mContext).load(
//                        data.logo).into(holder.getView(R.id.orgLogo))
                holder.setOnItemClickListener {
                    itemClickListener?.orgCooperClick(position)
                }
            } else {
                innerCompanyLayout.visibility = View.VISIBLE
                if (position == mData.lastIndex || mData[position + 1].companyId == "0") {
                    innerCompanyLayout.findViewById<View>(R.id.pd_bottom).visibility = View.GONE
                } else {
                    innerCompanyLayout.findViewById<View>(R.id.pd_bottom).visibility = View.VISIBLE
                }

                var hasPermission = false
                var hasInvitePermission = false

                fun newPowerCheck() {
                    // 创建者  包含超级管理员权限
                    hasPermission = data.deptId == "0" || ORG_PERMISS_TYPE.checkSuperPermission(data.power)

                    /**创建者 超级管理员或组织管理员*/
                     hasInvitePermission = data.deptId == "0" || ORG_PERMISS_TYPE.checkInvitePermission(data.power)

                    val showInvite = data.rejectJoin == 0 && (hasInvitePermission || data.rejectInvitation == 0)

                    if (hasPermission) {
                        holder.setViewVisibility(R.id.cl_more_layout, View.VISIBLE)
                    } else {
                        holder.setViewVisibility(R.id.cl_more_layout, View.INVISIBLE)
                    }
                    if (showInvite) {
                        holder.setViewVisibility(R.id.ll_visit_layout, View.VISIBLE)
                    } else {
                        holder.setViewVisibility(R.id.ll_visit_layout, View.GONE)
                    }
                }

                fun oldPowerCheck() {
                    LogUtil.showLog("---->>>>current power is ${data?.power}")

                    if (data.deptId == "0") {
                        //判断是否是创建的团队
                        hasPermission = true
                        holder.setViewVisibility(R.id.cl_more_layout, View.VISIBLE)
                        if (data.rejectJoin == 0) {
                            holder.setViewVisibility(R.id.ll_visit_layout, View.VISIBLE)
                        } else {
                            holder.setViewVisibility(R.id.ll_visit_layout, View.GONE)
                        }
                    } else {
                        hasPermission = StringUtils.isNotBlankAndEmpty(
                                data.power) && data.power.contains(ORG_PERMISS_TYPE.ORG_PERMISSION)
                        if (data.companyId == noticeCompanyId) {
                            hasPermission = true
                        }
                        if (data.power == ORG_PERMISS_TYPE.SUPER_PERMISSION) {
                            hasPermission = true
                            holder.setViewVisibility(R.id.cl_more_layout, View.VISIBLE)
                        } else {
                            holder.setViewVisibility(R.id.cl_more_layout, View.INVISIBLE)
                        }
                        if (data.rejectJoin == 1) {
                            holder.setViewVisibility(R.id.ll_visit_layout, View.GONE)
                        } else {
                            if (hasPermission || data.rejectInvitation == 0) {
                                holder.setViewVisibility(R.id.ll_visit_layout, View.VISIBLE)
                            } else {
                                holder.setViewVisibility(R.id.ll_visit_layout, View.GONE)
                            }
                        }
                    }
                }
                newPowerCheck()
                holder.setText(R.id.tv_name, data.name)
                if (StringUtils.isNotBlankAndEmpty(data.logo)) {
//                    val imageUrl = if (data.logo.startsWith("http")) data.logo else FileStorage.WEB_IMAGE_BASE_URL + data.logo
//                    ImageLoaderUtils.loadImage(mContext, holder.getView(R.id.iv_logo), imageUrl)
                    ImageLoaderUtils.loadImage(mContext, holder.getView(R.id.iv_logo), data.logo)
                }
                holder.getView<ConstraintLayout>(R.id.cl_member_watch).setOnClickListener {
                    //我的团队页，点击团队架构，全局搜索“orgMemberClick”
                    itemClickListener?.orgMemberClick(position, hasInvitePermission)
                }
                holder.getView<ConstraintLayout>(R.id.cl_watch_info).setOnClickListener {
                    itemClickListener?.orgInfoClick(position, hasPermission)
                }
                holder.getView<ConstraintLayout>(R.id.cl_external_layout).setOnClickListener {
                    itemClickListener?.orgExternalClick(position, hasPermission)
                }
                if (holder.getView<ConstraintLayout>(R.id.cl_more_layout).visibility == View.VISIBLE) {
                    holder.getView<ConstraintLayout>(R.id.cl_more_layout).setOnClickListener {
                        itemClickListener?.orgMoreClick(position)
                    }
                } else {
                    holder.getView<ConstraintLayout>(R.id.cl_more_layout).setOnClickListener(null)
                }
                holder.getView<LinearLayout>(R.id.ll_visit_layout).setOnClickListener {
                    itemClickListener?.visitClick(position)
                }
            }
        }
    }

    interface ItemClickListener {
        /**团队邀请*/
        fun visitClick(position: Int)

        /**团队权限管理*/
        fun orgMemberClick(position: Int, orgPermission: Boolean)

        /**团队信息*/
        fun orgInfoClick(position: Int, orgPermission: Boolean)

        /**团队外部协作人管理*/
        fun orgExternalClick(position: Int, orgPermission: Boolean)

        /**团队更多设置*/
        fun orgMoreClick(position: Int)

        /**协作团队点击*/
        fun orgCooperClick(position: Int)
    }
}