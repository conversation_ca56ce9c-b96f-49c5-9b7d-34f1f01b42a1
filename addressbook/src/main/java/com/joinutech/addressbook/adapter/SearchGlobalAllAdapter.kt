package com.joinutech.addressbook.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.bean.GroupSearchBean
import com.joinutech.addressbook.databinding.ItemSearchGlobalBinding
import com.joinutech.ddbeslibrary.bean.GroupInfoBean
import com.joinutech.ddbeslibrary.utils.ItemClickListener
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter
import com.joinutech.common.adapter.CommonBindingAdapter

/**
 * @Description: 全局搜索的adapter
 * @Author: hjr
 * @Time: 2020/2/25 17:21
 * @packageName: com.joinutech.addressbook.adapter
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
class SearchGlobalAllAdapter(context: Context, dataList: ArrayList<GroupSearchBean>)
    : CommonBindingAdapter<GroupSearchBean,ItemSearchGlobalBinding>(context, dataList, R.layout.item_search_global) {

    private var searchKeyWord = ""

    fun setKeyWord(searchKeyWord: String = "") {
        this.searchKeyWord = searchKeyWord
    }

    private var listener: ItemClickListener? = null

    fun setWatchAllListener(listener: ItemClickListener) {
        this.listener = listener
    }

    

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ItemSearchGlobalBinding {
        return ItemSearchGlobalBinding.inflate(layoutInflater)
    }

    override fun onBindItem(
        binding: ItemSearchGlobalBinding,
        item: GroupSearchBean?,
        position: Int,
    ) {
       item?.let { data->
           var list = arrayListOf<GroupInfoBean>()
           if (!data.list.isNullOrEmpty())
               list = data.list as ArrayList<GroupInfoBean>
           val type = when (data.title) {
               "团队群组" -> {
                   2
               }
               "私有群组" -> {
                   3
               }
               else -> {
                   1
               }
           }
           if (list.size == 5) {
               binding.tvWatchAll.visibility = View.VISIBLE
               binding.tvWatchAll.setOnClickListener {
                   listener?.onItemClick(position)
               }
           } else {
               binding.tvWatchAll.visibility = View.GONE
           }
           binding.tvGroupTypeName.text = data.title
           binding.rvListGroup.isNestedScrollingEnabled = false
           binding.rvListGroup.layoutManager = LinearLayoutManager(mContext)
           val adapter = SearchGlobalOtherAdapter(mContext, list, type)
           adapter.setKeyWord(searchKeyWord)
           binding.rvListGroup.adapter = adapter
       }
    }
}