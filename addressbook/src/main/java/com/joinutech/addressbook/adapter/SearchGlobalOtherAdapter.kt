package com.joinutech.addressbook.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.addressbook.R
import com.joinutech.addressbook.databinding.ItemSearchGlobalOtherBinding
import com.joinutech.ddbeslibrary.bean.GroupInfoBean
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.RouteIm
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter
import com.joinutech.common.adapter.CommonBindingAdapter
import java.util.regex.Pattern

/**
 * @Description: 全局搜索具体项adapter
 * @Author: hjr
 * @Time: 2020/2/25 17:57
 * @packageName: com.joinutech.addressbook.adapter
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
class SearchGlobalOtherAdapter(context: Context, dataList: ArrayList<GroupInfoBean>,
                               adapterType: Int = 1)
    : CommonBindingAdapter<GroupInfoBean,ItemSearchGlobalOtherBinding>(context, dataList, R.layout.item_search_global_other) {

    /**
     * 0 全部
     * 1 好友
     * 2 团队群组
     * 3 私有群组
     * */
    private var type = adapterType

    private var searchKeyWord = ""

    fun setKeyWord(searchKeyWord: String = "") {
        this.searchKeyWord = searchKeyWord
    }


    override fun inflateViewBinding(layoutInflater: LayoutInflater): ItemSearchGlobalOtherBinding {
        return ItemSearchGlobalOtherBinding.inflate(layoutInflater)
    }

    //全局搜索的点击事件可以在这里找到tcp
    override fun onBindItem(
        binding: ItemSearchGlobalOtherBinding,
        item: GroupInfoBean?,
        position: Int,
    ) {
        item?.let { data->
            if (StringUtils.isNotBlankAndEmpty(data.logo)) {
                ImageLoaderUtils.loadImage(mContext, binding.ivIcon, data.logo)
            }
            when (type) {
                1 -> { // 好友
                    if (StringUtils.isNotBlankAndEmpty(searchKeyWord) &&
                        StringUtils.isNotBlankAndEmpty(data.name) &&
                        data.name.contains(searchKeyWord)) {
                        val allKeyWordList = arrayListOf<String>()
                        val p1 = Pattern.compile(searchKeyWord)
                        val m1 = p1.matcher(data.name)
                        while (m1.find()) {
                            allKeyWordList.add(m1.group())
                        }
                        val spanColorStr = StringUtils.setSpanColorStr(
                            data.name, allKeyWordList,
                            CommonUtils.getColor(mContext, com.joinutech.ddbeslibrary.R.color.colorFF5000))
                        binding.tvName.text = spanColorStr
                    } else {
                        binding.tvName.text = data.name
                    }
                    binding.root.setOnClickListener {
                        // 查询好友信息
                        ARouter.getInstance()
                            .build(RouteOrg.friendInfoActivity)// 查看好友详情
                            .withString("userId", data.createUserId)
                            .withString("type", "friend")
                            .navigation()
                    }
                }
                2, 3 -> { // 团队和私有群组
                    if (!data.searchMatchUsers.isNullOrEmpty()) {
                        binding.tvContent.visibility = View.VISIBLE
                        if (StringUtils.isNotBlankAndEmpty(searchKeyWord) &&
                            !data.searchMatchUsers.isNullOrEmpty()) {
                            val allKeyWordList = arrayListOf<String>()
                            val groupMemberName = StringBuilder()
                            data.searchMatchUsers.forEach {
                                if (StringUtils.isNotBlankAndEmpty(it.remarkName)) {
                                    groupMemberName.append("${it.remarkName}、")
                                } else groupMemberName.append("${it.name}、")
                            }
                            if (StringUtils.isNotBlankAndEmpty(groupMemberName.toString())) {
                                var groupSearchMemberNameValue =
                                    groupMemberName.substring(0, groupMemberName.lastIndex)
                                val p1 = Pattern.compile(searchKeyWord)
                                val m1 = p1.matcher(groupSearchMemberNameValue)
                                while (m1.find()) {
                                    allKeyWordList.add(m1.group())
                                }
                                groupSearchMemberNameValue = "包含用户：${groupSearchMemberNameValue}"
                                val spanColorStr = StringUtils.setSpanColorStr(
                                    groupSearchMemberNameValue, allKeyWordList,
                                    CommonUtils.getColor(mContext, com.joinutech.ddbeslibrary.R.color.colorFF5000))
                                binding.tvContent.text = spanColorStr
                            } else {
                                binding.tvContent.visibility = View.GONE
                            }
                        } else {
                            binding.tvContent.visibility = View.GONE
                        }
                    } else {
                        binding.tvContent.visibility = View.GONE
                    }
                    if (StringUtils.isNotBlankAndEmpty(searchKeyWord) &&
                        StringUtils.isNotBlankAndEmpty(data.name) &&
                        data.name.contains(searchKeyWord)) {
                        val allKeyWordList = arrayListOf<String>()
                        val p1 = Pattern.compile(searchKeyWord)
                        val m1 = p1.matcher(data.name)
                        while (m1.find()) {
                            allKeyWordList.add(m1.group())
                        }
                        val spanColorStr = StringUtils.setSpanColorStr(
                            data.name, allKeyWordList,
                            CommonUtils.getColor(mContext, com.joinutech.ddbeslibrary.R.color.colorFF5000))
                        binding.tvName.text = spanColorStr
                    } else {
                        binding.tvName.text = data.name
                    }
                    binding.root.setOnClickListener {
                        ARouter.getInstance()
                            .build(RouteIm.groupChat)
                            .withString("targetId", data.groupId)
                            .withString("targetName", data.name)
                            .withString("targetLogo", data.logo)
                            .withString("companyId", data.orgId)//tcp新加的
                            .navigation()
                    }
                }
            }
        }
    }
}