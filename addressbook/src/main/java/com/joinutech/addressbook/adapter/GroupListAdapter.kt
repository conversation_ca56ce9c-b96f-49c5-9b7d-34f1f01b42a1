package com.joinutech.addressbook.adapter

import android.content.Context
import android.view.LayoutInflater
import com.joinutech.addressbook.R
import com.joinutech.addressbook.databinding.ItemGroupListBinding
import com.joinutech.ddbeslibrary.bean.GroupListBean
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.ItemClickListener
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter
import com.joinutech.common.adapter.CommonBindingAdapter
import java.util.regex.Pattern

/**
 * @Description: 团队列表Adapter
 * @Author: hjr
 * @Time: 2020/2/25 10:45
 * @packageName: com.joinutech.addressbook.adapter
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
class GroupListAdapter(context: Context, dataList: ArrayList<GroupListBean>,
                       clickItemListener: ItemClickListener)
    : CommonBindingAdapter<GroupListBean,ItemGroupListBinding>(context, dataList, R.layout.item_group_list) {

    private var searchKeyWord = ""
    private var listener = clickItemListener

    fun setKeyWord(searchKeyWord: String = "") {
        this.searchKeyWord = searchKeyWord
    }
    
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ItemGroupListBinding {
        return ItemGroupListBinding.inflate(layoutInflater)
    }

    override fun onBindItem(binding: ItemGroupListBinding, item: GroupListBean?, position: Int) {
        item?.let { data->
            if (StringUtils.isNotBlankAndEmpty(data.logo)) {
                ImageLoaderUtils.loadImage(mContext, binding.groupIcon, data.logo)
            }
            if (StringUtils.isNotBlankAndEmpty(searchKeyWord) && data.name.contains(searchKeyWord)) {
                val allKeyWordList = arrayListOf<String>()
                val p1 = Pattern.compile(searchKeyWord)
                val m1 = p1.matcher(data.name)
                while (m1.find()) {
                    allKeyWordList.add(m1.group())
                }
                val spanColorStr = StringUtils.setSpanColorStr(data.name,
                    allKeyWordList,
                    CommonUtils.getColor(mContext, com.joinutech.ddbeslibrary.R.color.colorFF5000))
                binding.groupName.text = spanColorStr
            } else {
                binding.groupName.text = data.name
            }
            binding.root.setOnClickListener {
                listener.onItemClick(position)
            }
        }
    }
}