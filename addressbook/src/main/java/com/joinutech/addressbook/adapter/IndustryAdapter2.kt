package com.joinutech.addressbook.adapter

import android.app.Activity
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.joinutech.addressbook.R
import com.joinutech.ddbeslibrary.bean.JobChoiceBean
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter

/**
 *<AUTHOR>
 *@date 2018/11/21
 */
class IndustryAdapter2(var mcontext:Activity, var list:ArrayList<JobChoiceBean.IndustrysBean>)
    : CommonAdapter<JobChoiceBean.IndustrysBean>(mcontext,list,R.layout.item_industry2){

    var selBean:JobChoiceBean.IndustrysBean?=null

    override fun bindData(holder: ViewHolder, data: JobChoiceBean.IndustrysBean, position: Int) {
        if (StringUtils.isNotBlankAndEmpty(data.name)){
            val tv = holder.getView<TextView>(R.id.tv2_item_industry)
            val iv2 = holder.getView<ImageView>(R.id.iv2_item_industry)
            tv.text = data.name
            if(data.type==1){
                tv.setTextColor(mcontext.resources.getColor(com.joinutech.ddbeslibrary.R.color.main_blue))
                iv2.visibility=View.VISIBLE
            }else{
                tv.setTextColor(mcontext.resources.getColor(com.joinutech.ddbeslibrary.R.color.text_black))
                iv2.visibility=View.INVISIBLE
            }
            holder.setOnItemClickListener {
                for (i in 0 until mData.size){
                    if(i==position){
                        mData[i].type=1
                    }else{
                        mData[i].type=2
                    }
                }
                if(mData[position].type==1){
                    tv.setTextColor(mcontext.resources.getColor(com.joinutech.ddbeslibrary.R.color.main_blue))
                    iv2.visibility=View.VISIBLE
                }else{
                    tv.setTextColor(mcontext.resources.getColor(com.joinutech.ddbeslibrary.R.color.text_black))
                    iv2.visibility=View.INVISIBLE
                }
                selBean = mData[position]
                notifyDataSetChanged()
            }
        }
    }
}