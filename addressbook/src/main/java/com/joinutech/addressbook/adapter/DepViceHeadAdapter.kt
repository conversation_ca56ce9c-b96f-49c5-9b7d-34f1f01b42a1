package com.joinutech.addressbook.adapter

import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.joinutech.addressbook.R
import com.joinutech.ddbeslibrary.bean.Member
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter

/**
 * <AUTHOR>
 * @date   2019/5/29 15:59
 * @className: DepViceHeadAdapter
 *@Description: 类作用描述
 */
class DepViceHeadAdapter (context: Context,memberList:ArrayList<Member>)
    : CommonAdapter<Member>(context,memberList, R.layout.dep_head_item_layout) {

    private var listner: ItemClickListener? = null

    override fun bindData(holder: ViewHolder, data: Member, position: Int) {
        if (StringUtils.isNotBlankAndEmpty(data.positionId)){
            holder.setText(R.id.tv_dept_head_name, data.positionName)
            val textView = holder.getView<TextView>(R.id.dept_head_name)
            if (data.userId == "0"){
                holder.setViewVisibility(R.id.iv_dept_head_icon, View.GONE)
                holder.setViewVisibility(R.id.tv_dept_head_empty, View.VISIBLE)
            }else {
                textView.text = data.name
                holder.setViewVisibility(R.id.iv_dept_head_icon, View.VISIBLE)
                if (StringUtils.isNotBlankAndEmpty(data.headimg)){
                    ImageLoaderUtils.loadImage(mContext,holder.getView(R.id.iv_dept_head_icon),
                            data.headimg)
                }
                holder.setViewVisibility(R.id.tv_dept_head_empty, View.GONE)
            }
            holder.getView<ImageView>(R.id.dept_head_del).setOnClickListener {
                if (listner!=null) listner?.onDeleteClick(holder.adapterPosition)
            }
            holder.getView<ImageView>(R.id.iv_dept_head_edit_icon).setOnClickListener {
                if (listner!=null) listner?.onEditClick(holder.adapterPosition)
            }
        }
    }

    interface ItemClickListener{
        fun onEditClick(position: Int)
        fun onDeleteClick(position: Int)
    }

    fun setClickListener(listner:ItemClickListener){
        this.listner = listner
    }
}