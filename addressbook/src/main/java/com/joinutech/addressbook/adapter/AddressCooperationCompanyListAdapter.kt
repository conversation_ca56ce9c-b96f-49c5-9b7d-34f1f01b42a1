package com.joinutech.addressbook.adapter

import android.content.Context
import android.os.Bundle
import android.view.View
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.addressbook.R
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.utils.ConsKeys
import com.joinutech.ddbeslibrary.utils.RouteProvider
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/12/2 9:06
 * @packageName: com.joinutech.addressbook.adapter
 * @Company: JoinuTech
 */
class AddressCooperationCompanyListAdapter(context: Context, dataList: ArrayList<WorkStationBean>)
    : CommonAdapter<WorkStationBean>(context, dataList, com.joinutech.ddbeslibrary.R.layout.item_cooperation_company) {

    override fun bindData(holder: ViewHolder, data: WorkStationBean, position: Int) {
        holder.setViewVisibility(com.joinutech.ddbeslibrary.R.id.line, View.GONE)
        holder.setViewVisibility(com.joinutech.ddbeslibrary.R.id.tv_count_dot, View.GONE)
        if (StringUtils.isNotBlankAndEmpty(data.logo)) {
            ImageLoaderUtils.loadImage(mContext, holder.getView(com.joinutech.ddbeslibrary.R.id.iv_left_org_icon), data.logo)
        }
        holder.setText(com.joinutech.ddbeslibrary.R.id.tv_left_org_name, data.name)
        if (data.haveApprove != 0) {
            holder.setViewVisibility(com.joinutech.ddbeslibrary.R.id.tv_count_dot, View.VISIBLE)
            holder.setText(com.joinutech.ddbeslibrary.R.id.tv_count_dot, data.haveApprove.toString())
        } else {
            holder.setViewVisibility(com.joinutech.ddbeslibrary.R.id.tv_count_dot, View.GONE)
        }
        holder.setOnItemClickListener {
            val bundle = Bundle()
            bundle.putString(ConsKeys.COMPANY_ID, data.companyId)
            bundle.putSerializable(ConsKeys.KEY_INTENT_DATA, data)
            (ARouter.getInstance().build(RouteProvider.APR_FUN_PROVIDER).navigation() as RouteServiceProvider)
                    .openPage("cooperation_approve", bundle)

//            ARouter.getInstance()
//                    .build(RouteApr.companyCooperationCompanyDetailActivity)
//                    .withString("companyId", data.companyId)
//                    .withSerializable("company", data)
//                    .navigation()
        }
    }
}