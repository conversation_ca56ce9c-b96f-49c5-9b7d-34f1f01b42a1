package com.joinutech.addressbook.adapter

import android.content.Context
import android.content.Intent
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.joinutech.addressbook.R
import com.joinutech.addressbook.view.OrgExternalContactDetailActivity
import com.joinutech.ddbeslibrary.bean.ExternalContactListBean
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter

/**
 * @Description: 外部协作人列表 数据适配器
 * @Author: 黄洁如
 * @Time: 2019/11/29 9:05
 * @packageName: com.joinutech.addressbook.adapter
 * @Company: JoinuTech
 */
class OrgExternalContactPersonAdapter(context: Context, dataList: ArrayList<ExternalContactListBean>)
    : CommonAdapter<ExternalContactListBean>(context, dataList, R.layout.item_org_external_contact) {

    private var externalContactType = "1"
    private var externalContactLevel = "1"
    private lateinit var type: TextView
    private lateinit var levelLayout: ConstraintLayout
    private lateinit var levelIv: ImageView
    private lateinit var levelTv: TextView
    private var companyId = ""
    private var isEdit = false
    private var typeIndex = 0

    fun setCompanyId(companyId: String) {
        this.companyId = companyId
    }

    fun setIsEdit(isEdit: Boolean) {
        this.isEdit = isEdit
    }

    fun setTypeIndex(typeIndex: Int) {
        this.typeIndex = typeIndex
    }

    override fun bindData(holder: ViewHolder, data: ExternalContactListBean, position: Int) {
        type = holder.getView(R.id.tv_cooper_type)
        levelLayout = holder.getView(R.id.cl_level_layout)
        levelIv = holder.getView(R.id.iv_level_icon)
        levelTv = holder.getView(R.id.tv_level_name)
        if (data.status == 0) {
            holder.setViewVisibility(R.id.tv_no_register, View.VISIBLE)
            holder.setViewVisibility(R.id.iv_header_image, View.INVISIBLE)
            holder.setViewVisibility(R.id.tv_no_active, View.GONE)
        } else {
            holder.setViewVisibility(R.id.tv_no_register, View.GONE)
            holder.setViewVisibility(R.id.iv_header_image, View.VISIBLE)
            if (StringUtils.isNotBlankAndEmpty(data.avatar)) {
                ImageLoaderUtils.loadImage(mContext, holder.getView(R.id.iv_header_image), data.avatar)
            }
            if (data.active == 0) {
                // 激活
                holder.setViewVisibility(R.id.tv_no_active, View.GONE)
            } else {
                holder.setViewVisibility(R.id.tv_no_active, View.VISIBLE)
            }
        }
        holder.setText(R.id.name, data.name)
        externalContactType = data.type.toString()
        setTypeShow()
        externalContactLevel = data.level.toString()
        setLevelShow()
        holder.setOnItemClickListener {
            val intent = Intent(mContext, OrgExternalContactDetailActivity::class.java)
//            intent.putExtra("contactId", data.userId)
//            intent.putExtra("active", data.active)
//            intent.putExtra("status", data.status)
//            intent.putExtra("name", data.name)
            intent.putExtra("external", data)
            intent.putExtra("companyId", companyId)
            intent.putExtra("isEdit", isEdit)
            intent.putExtra("typeIndex", typeIndex)
            mContext.startActivity(intent)
        }
    }

    private fun setTypeShow() {
        val shoeTypeText = when (externalContactType) {
            "1" -> "客户"
            "2" -> "渠道商"
            "3" -> "供应商"
            "4" -> "合作伙伴"
            "5" -> "其他类型"
            else -> "1"
        }
        type.text = shoeTypeText
    }

    private fun setLevelShow() {
        when (externalContactLevel) {
            "1" -> {
                levelLayout.setBackgroundResource(R.drawable.shape_external_contact_ffedd4)
                levelIv.setImageResource(R.drawable.icon_external_contact_level_yellow)
                levelTv.setTextColor(CommonUtils.getColor(mContext, com.joinutech.ddbeslibrary.R.color.colorFAA62F))
                levelTv.text = "非常重要"
            }
            "2" -> {
                levelLayout.setBackgroundResource(R.drawable.shape_external_contact_d4eff)
                levelIv.setImageResource(R.drawable.icon_external_contact_level_blue)
                levelTv.setTextColor(CommonUtils.getColor(mContext, com.joinutech.ddbeslibrary.R.color.main_blue))
                levelTv.text = "重要"
            }
            "3" -> {
                levelLayout.setBackgroundResource(R.drawable.shape_external_contact_e4edf5)
                levelIv.setImageResource(R.drawable.icon_external_contact_level_grey)
                levelTv.setTextColor(CommonUtils.getColor(mContext, com.joinutech.ddbeslibrary.R.color.color7EB0D6))
                levelTv.text = "一般"
            }
        }
    }
}