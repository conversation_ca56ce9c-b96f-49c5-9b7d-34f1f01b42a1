package com.joinutech.addressbook.adapter

import android.content.Context
import android.content.Intent
import android.view.View
import android.widget.TextView
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.addressbook.R
import com.joinutech.addressbook.view.ApplicationListActivity
import com.joinutech.ddbeslibrary.bean.CompanyUndoBeanNew
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter
import com.joinutech.ddbeslibrary.bean.ExternalUndoBean
import com.joinutech.ddbeslibrary.utils.RouteOrg

/**
 * 公司消息待处理列表
 */
class ExternalUndoListAdapter (context: Context, dataList:ArrayList<ExternalUndoBean>)
    : CommonAdapter<ExternalUndoBean>(context,dataList, R.layout.item_undolist_external){

    override fun bindData(holder: ViewHolder, data: ExternalUndoBean, position: Int) {
        if (StringUtils.isNotBlankAndEmpty(data.companyId) && data.companyId!="0"){
            holder.setText(R.id.undoCompanyText,data.name)
            if (StringUtils.isNotBlankAndEmpty(data.logo)){
                ImageLoaderUtils.loadImage(mContext,holder.getView(R.id.undoCompanyIcon),data.logo)
            }

            holder.setOnItemClickListener {
                ARouter.getInstance()
                    .build(RouteOrg.cooperationCompanyApplicationDetailActivity)
                    .withString("paramId", data.externalId)
                    .navigation()
            }
        }
    }
}