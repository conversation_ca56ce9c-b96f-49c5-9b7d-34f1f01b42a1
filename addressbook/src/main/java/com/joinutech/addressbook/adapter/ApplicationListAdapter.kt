package com.joinutech.addressbook.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.joinutech.addressbook.R
import com.joinutech.ddbeslibrary.bean.CompanyUndoListBean
import com.joinutech.common.adapter.BaseAdapter
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils

class ApplicationListAdapter(var context: Context, dataListCompany: List<CompanyUndoListBean>)
    : BaseAdapter<CompanyUndoListBean>(dataListCompany, context) {

    override fun setDataSourceList(list: List<CompanyUndoListBean>?) {
        mList = list!!
        notifyDataSetChanged()
    }

    override fun addMoreData(t: CompanyUndoListBean) {
        (mList as ArrayList).add(t)
        notifyDataSetChanged()
    }

    fun addDataMoreList(list: List<CompanyUndoListBean>) {
        if (list.isNotEmpty()) {
            (mList as ArrayList).addAll(list)
            notifyDataSetChanged()
        }
    }

    override fun creatView(parent: ViewGroup): View? {
        val inflater = LayoutInflater.from(mContext)
        return inflater.inflate(R.layout.item_nodolist_layout, parent, false)
    }

    @SuppressLint("SetTextI18n")
    override fun updateView(holder: BaseViewHolder, position: Int) {
        val userIcon = holder.itemView.findViewById<ImageView>(R.id.iv_user_icon)
        val reason = holder.itemView.findViewById<TextView>(R.id.reason)
        val name = holder.itemView.findViewById<TextView>(R.id.name)
        val company = holder.itemView.findViewById<TextView>(R.id.company)
        val watch = holder.itemView.findViewById<TextView>(R.id.watch)
        val status = holder.itemView.findViewById<TextView>(R.id.status)
        val time = holder.itemView.findViewById<TextView>(R.id.time)
        if (mList != null && mList!!.isNotEmpty()) {
            val bean = mList!![position]
            if (StringUtils.isNotBlankAndEmpty(bean.headimg)) {
                ImageLoaderUtils.loadImage(context, userIcon, bean.headimg)
            }
            when (bean.status) {
                0 -> {
                    watch.visibility = View.VISIBLE
                    status.visibility = View.GONE
                    time.visibility = View.GONE
                    reason.maxEms = 16
                }
                1 -> {
                    watch.visibility = View.GONE
                    status.visibility = View.VISIBLE
                    time.visibility = View.VISIBLE
                    status.text = "已同意"
                    time.text = bean.createTime
                    reason.maxEms = 10
                }
                2 -> {
                    watch.visibility = View.GONE
                    status.visibility = View.VISIBLE
                    time.visibility = View.VISIBLE
                    status.text = "已忽略"
                    time.text = bean.createTime
                    reason.maxEms = 10
                }
                4 -> {
                    watch.visibility = View.GONE
                    status.visibility = View.VISIBLE
                    time.visibility = View.VISIBLE
                    status.text = "已过期"
                    time.text = bean.createTime
                    reason.maxEms = 10
                }
            }
            name.text = bean.name
            if (StringUtils.isNotBlankAndEmpty(bean.companyName)) {
                company.visibility = View.VISIBLE
                company.text = "申请加入：${bean.companyName}"
            } else company.visibility = View.INVISIBLE
            if (StringUtils.isEmpty(bean.content)) reason.visibility = View.INVISIBLE
            else {
                reason.visibility = View.VISIBLE
                reason.text = "申请理由：${bean.content}"
            }
        }
    }
}