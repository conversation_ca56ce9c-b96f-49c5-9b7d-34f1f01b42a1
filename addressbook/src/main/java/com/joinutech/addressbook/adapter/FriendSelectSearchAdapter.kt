package com.joinutech.addressbook.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import com.joinutech.addressbook.R
import com.joinutech.addressbook.databinding.ItemFriendListSelectLayoutBinding
import com.joinutech.ddbeslibrary.bean.FriendSelectBean
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.ItemClickListener
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter
import com.joinutech.common.adapter.CommonBindingAdapter

/**
 * @Description: 好友选择adapter
 * @Author: hjr
 * @Time: 2020/2/24 11:57
 * @packageName: com.joinutech.addressbook.adapter
 * @Company: Copyright (C),2017- 2020,JoinuTech
 * WithDataListSelectActivity 群组成员操作，为UserInfo数据转换为FriendSelectBean，注销和单双向情况未知
 * SelectWithSearchListWithBottomShowActivity 创建群组时使用，好友数据创建群组 数据来源好友数据
 * FriendSelectWithSearchListActivity2 好友选择操作
 */
class FriendSelectSearchAdapter(context: Context, dataList: ArrayList<FriendSelectBean>,
                                isMultiType: Boolean = false)
    : CommonBindingAdapter<FriendSelectBean,ItemFriendListSelectLayoutBinding>(context, dataList, R.layout.item_friend_list_select_layout) {

    private var multiType = isMultiType

    private var textIndex = true

    private var listener: ItemClickListener? = null

    private var noSelectUsers = arrayListOf<String>()

    fun setNoSelectUsers(noSelectUsers: ArrayList<String>) {
        this.noSelectUsers = noSelectUsers
    }

    fun setClickListener(listener: ItemClickListener) {
        this.listener = listener
    }

    fun setTextIndex(noTextIndex: Boolean) {
        this.textIndex = noTextIndex
    }
    

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ItemFriendListSelectLayoutBinding {
        return ItemFriendListSelectLayoutBinding.inflate(layoutInflater)
    }

    override fun onBindItem(
        binding: ItemFriendListSelectLayoutBinding,
        item: FriendSelectBean?,
        position: Int,
    ) {
        item?.let { data->
            if (multiType) {
                binding.ivSelect.visibility = View.VISIBLE
                when {
                    data.logout == 1 -> {
                        //此人不可选，选择框要置为灰色
                        //                binding.ivSelect.setImageResource(R.drawable.shape_oval_white_grey_invalid)
                        binding.root.findViewById<View>(R.id.view_logout).visibility = View.VISIBLE
                        binding.root.setOnClickListener {
                            ToastUtil.show(mContext, "该好友已注销账号")
                        }
                    }
                    !noSelectUsers.isNullOrEmpty() && data.userId in noSelectUsers -> {
                        //此人不可选，选择框要置为灰色
                        binding.ivSelect.setImageResource(com.joinutech.ddbeslibrary.R.drawable.shape_oval_white_grey_invalid)
                        binding.root.setOnClickListener {
                            ToastUtil.show(mContext, "该用户不可选择")
                        }
                    }
                    else -> {
                        binding.root.findViewById<View>(R.id.view_logout).visibility = View.GONE
                        binding.ivSelect.setImageResource(com.joinutech.ddbeslibrary.R.drawable.selector_circle_select)
                        binding.ivSelect.isSelected = data.select
                        binding.root.setOnClickListener {
                            listener?.onItemClick(position)
                        }
                    }
                }
            } else {
                if ((!noSelectUsers.isNullOrEmpty() && data.userId in noSelectUsers) || data.logout == 1) {
                    //此人不可选，选择框要置为灰色
//                binding.ivSelect.setImageResource(R.drawable.shape_oval_white_grey_invalid)
                    binding.root.findViewById<View>(R.id.view_logout).visibility = View.VISIBLE
                    binding.root.setOnClickListener {
                        ToastUtil.show(mContext, "该用户不可选择")
                    }
                } else {
                    binding.root.findViewById<View>(R.id.view_logout).visibility = View.GONE
                    binding.ivSelect.visibility = View.GONE
                    binding.root.setOnClickListener {
                        listener?.onItemClick(position)
                    }
                }
            }
            if ((position == 0 || mData[position - 1].initial != data.initial) && textIndex) {
                binding.tvIndex.visibility = View.VISIBLE
                binding.tvIndex.text = data.initial
                if (data.isCurrentIndex) {
                    binding.tvIndex.setTextColor(CommonUtils.getColor(
                        mContext, com.joinutech.ddbeslibrary.R.color.color1E87F0))
                } else {
                    binding.tvIndex.setTextColor(CommonUtils.getColor(
                        mContext, com.joinutech.ddbeslibrary.R.color.colorBDBDBD))
                }
            } else {
                binding.tvIndex.visibility = View.GONE
            }
            if (StringUtils.isNotBlankAndEmpty(data.avatar)) {
                binding.ivAvatar.visibility = View.VISIBLE
                ImageLoaderUtils.loadImage(mContext, binding.ivAvatar, data.avatar)
            } else {
                binding.ivAvatar.visibility = View.GONE
            }
            binding.tvName.text = data.name
            
        }
    }


}