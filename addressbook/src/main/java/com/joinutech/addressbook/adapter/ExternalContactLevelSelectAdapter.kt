package com.joinutech.addressbook.adapter

import android.content.Context
import android.widget.TextView
import com.joinutech.addressbook.R
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.ItemClickListener
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter

/**
 * <AUTHOR>
 * @className: ExternalContactLevelSelectAdapter
 *@Description: 类作用描述
 */
class ExternalContactLevelSelectAdapter (var context:Context, dataList:ArrayList<String>)
    : CommonAdapter<String>(context,dataList, R.layout.item_external_level_select){

    private var listener :ItemClickListener? =null

    override fun bindData(holder: ViewHolder, data: String, position: Int) {
        if (StringUtils.isNotBlankAndEmpty(data)){
            val content = holder.getView<TextView>(R.id.content)
            when(data){
                "1" ->{
                    holder.setImageResource(R.id.tv_level_name,R.drawable.icon_external_contact_level_yellow)
                    content.setTextColor(CommonUtils.getColor(context, com.joinutech.ddbeslibrary.R.color.colorFAA62F))
                    content.text = "非常重要"
                }
                "2" ->{
                    holder.setImageResource(R.id.tv_level_name,R.drawable.icon_external_contact_level_blue)
                    content.setTextColor(CommonUtils.getColor(context, com.joinutech.ddbeslibrary.R.color.main_blue))
                    content.text = "重要"
                }
                "3" ->{
                    holder.setImageResource(R.id.tv_level_name,R.drawable.icon_external_contact_level_grey)
                    content.setTextColor(CommonUtils.getColor(context, com.joinutech.ddbeslibrary.R.color.color7EB0D6))
                    content.text = "一般"
                }
            }
            if (listener!=null){
                holder.setOnItemClickListener {
                    listener?.onItemClick(holder.adapterPosition)
                }
            }
        }
    }

    fun setItemClickListener(listener: ItemClickListener) {
        this.listener = listener
    }
}