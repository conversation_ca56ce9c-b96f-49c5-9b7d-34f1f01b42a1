package com.joinutech.addressbook.adapter

import android.content.Context
import android.widget.ImageView
import com.joinutech.addressbook.R
import com.joinutech.ddbeslibrary.bean.CompanyUndoListBean
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter

/**
 * description ： 批处理信息处理类
 * author: 黄洁如
 * date : 2019/10/21
 */
class BatchProcessUndoAdapter (dataList:ArrayList<CompanyUndoListBean>,context: Context)
    : CommonAdapter<CompanyUndoListBean>(context,dataList, R.layout.item_batch_process_application){

    override fun bindData(holder: ViewHolder, data: CompanyUndoListBean, position: Int) {
        val selectIv = holder.getView<ImageView>(R.id.allSelectIv)
        selectIv.isSelected = data.isSelected
        holder.setOnItemClickListener {
            selectIv.isSelected = !selectIv.isSelected
            data.isSelected = !data.isSelected
        }
        if (StringUtils.isNotBlankAndEmpty(data.headimg)) {
            ImageLoaderUtils.loadImage(mContext,holder.getView(R.id.iv_user_icon),data.headimg)
        }
        holder.setText(R.id.name,data.name)
        holder.setText(R.id.company,"申请加入：${data.companyName}")
        holder.setText(R.id.reason,"申请加入：${data.content}")
    }
}