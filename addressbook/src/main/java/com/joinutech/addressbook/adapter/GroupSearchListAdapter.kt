package com.joinutech.addressbook.adapter

import android.content.Context
import android.view.LayoutInflater
import androidx.recyclerview.widget.LinearLayoutManager
import com.joinutech.addressbook.R
import com.joinutech.addressbook.adapter.bean.GroupSearchBean
import com.joinutech.addressbook.databinding.ItemGroupSearchListBinding
import com.joinutech.ddbeslibrary.bean.GroupInfoBean
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter
import com.joinutech.common.adapter.CommonBindingAdapter

/**
 * @Description: 群组搜索列表嵌套recycleview的adapter
 * @Author: hjr
 * @Time: 2020/2/25 15:26
 * @packageName: com.joinutech.addressbook.adapter
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
class GroupSearchListAdapter(context: Context, dataList: ArrayList<GroupSearchBean>) :
        CommonBindingAdapter<GroupSearchBean,ItemGroupSearchListBinding>(context, dataList, R.layout.item_group_search_list) {

    private var searchKeyWord = ""

    fun setKeyWord(searchKeyWord: String = "") {
        this.searchKeyWord = searchKeyWord
    }
    

    override fun inflateViewBinding(layoutInflater: LayoutInflater): ItemGroupSearchListBinding {
        return ItemGroupSearchListBinding.inflate(layoutInflater)
    }

    override fun onBindItem(
        binding: ItemGroupSearchListBinding,
        item: GroupSearchBean?,
        position: Int,
    ) {
       item?.let { data->
           binding.tvGroupTypeName.text = data.title
           binding.rvListGroup.isNestedScrollingEnabled = false
           binding.rvListGroup.layoutManager = LinearLayoutManager(mContext)
           val adapter = SearchGlobalOtherAdapter(mContext, data.list as ArrayList<GroupInfoBean>,
               2)
           adapter.setKeyWord(searchKeyWord)
           binding.rvListGroup.adapter = adapter
           
       }
    }

}