package com.joinutech.addressbook.adapter

import android.content.Context
import com.joinutech.addressbook.R
import com.joinutech.ddbeslibrary.utils.ItemClickListener
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter

/**
 * <AUTHOR>
 * @className: ExternalContactTypeSelectAdapter
 *@Description: 类作用描述
 */
class ExternalContactTypeSelectAdapter (var context:Context, dataList:ArrayList<String>)
    : CommonAdapter<String>(context,dataList, com.joinutech.ddbeslibrary.R.layout.item_center_text_with_dot){

    private var listener :ItemClickListener? =null

    override fun bindData(holder: ViewHolder, data: String, position: Int) {
        if (StringUtils.isNotBlankAndEmpty(data)){
            holder.setText(R.id.content,data)
            if (listener!=null){
                holder.setOnItemClickListener {
                    listener?.onItemClick(holder.adapterPosition)
                }
            }
        }
    }

    fun setItemClickListener(listener: ItemClickListener) {
        this.listener = listener
    }
}