package com.joinutech.addressbook.adapter

import android.content.Context
import com.joinutech.addressbook.R
import com.joinutech.ddbeslibrary.utils.ItemClickListener
import com.joinutech.ddbeslibrary.bean.Branch
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter

class ChildDepartmentAdapter(context: Context,
                             data: ArrayList<Branch>, var type: String) :
        CommonAdapter<Branch>(context, data, R.layout.item_child_department) {

    private var listner: ItemClickListener? = null

    override fun bindData(holder: ViewHolder, data: Branch, position: Int) {
        holder.setText(R.id.itemName, data.name)
        holder.setOnItemClickListener {
            if (listner != null) listner?.onItemClick(holder.adapterPosition)
        }
    }

    fun setClickListener(listner: ItemClickListener) {
        this.listner = listner
    }
}