package com.joinutech.addressbook.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.joinutech.addressbook.R
import com.joinutech.ddbeslibrary.bean.FriendUndoListBean
import com.ddbes.library.im.imtcp.TimeUtils
import com.joinutech.common.adapter.BaseAdapter
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.CircleImageView

class FriendUndoListAdapter(var context: Context, dataListCompany: List<FriendUndoListBean>)
    : BaseAdapter<FriendUndoListBean>(dataListCompany, context) {

    override fun setDataSourceList(list: List<FriendUndoListBean>?) {
        mList = list!!
        notifyDataSetChanged()
    }

    fun addDataMoreList(list: List<FriendUndoListBean>) {
        if (list.isNotEmpty()) {
            (mList as ArrayList).addAll(list)
            notifyDataSetChanged()
        }
    }

    override fun addMoreData(t: FriendUndoListBean) {
        (mList as ArrayList).add(t)
        notifyDataSetChanged()
    }

    override fun creatView(parent: ViewGroup): View? {
        val inflater = LayoutInflater.from(mContext)
        return inflater.inflate(R.layout.item_nodolist_layout, parent, false)
    }

    override fun updateView(holder: BaseViewHolder, position: Int) {
        val userIcon = holder.itemView.findViewById<CircleImageView>(R.id.iv_user_icon)
        val time = holder.itemView.findViewById<TextView>(R.id.time)
        val name = holder.itemView.findViewById<TextView>(R.id.name)
        val company = holder.itemView.findViewById<TextView>(R.id.company)
        val watch = holder.itemView.findViewById<TextView>(R.id.watch)
        val status = holder.itemView.findViewById<TextView>(R.id.status)
        val reason = holder.itemView.findViewById<TextView>(R.id.reason)
        if (mList != null && mList!!.isNotEmpty()) {
            val bean = mList!![position]
            if (StringUtils.isNotBlankAndEmpty(bean.avatar)) {
                ImageLoaderUtils.loadImage(context, userIcon, bean.avatar)
            }
            val createTime = TimeUtils.getTime2(bean.createTime.toLong())//格式化时间
            when (bean.status) {
                0 -> {
                    watch.visibility = View.VISIBLE
                    status.visibility = View.GONE
                    time.visibility = View.GONE
                    reason.maxEms = 16
                }
                1 -> {
                    watch.visibility = View.GONE
                    status.visibility = View.VISIBLE
                    time.visibility = View.VISIBLE
                    status.text = "已同意"
                    time.text = createTime
                    reason.maxEms = 10
                }
                2 -> {
                    watch.visibility = View.GONE
                    status.visibility = View.VISIBLE
                    time.visibility = View.VISIBLE
                    status.text = "已忽略"
                    time.text = createTime
                    reason.maxEms = 10
                }
                4 -> {
                    watch.visibility = View.GONE
                    status.visibility = View.VISIBLE
                    time.visibility = View.VISIBLE
                    status.text = "已过期"
                    time.text = createTime
                    reason.maxEms = 10
                }
            }
            name.text = bean.name
            company.text = "申请添加您为好友"
            if (StringUtils.isEmpty(bean.message)) {
                reason.visibility = View.INVISIBLE
            } else {
                reason.text = "申请理由：${bean.message}"
            }
        }
    }
}