package com.joinutech.addressbook.adapter

import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.addressbook.R
import com.joinutech.ddbeslibrary.bean.Member
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter

class SelectedMemberAdapter (context:Context,
                             data:ArrayList<Member>,
                             var type:String)
    : CommonAdapter<Member>(context,data, R.layout.item_select_member) {

    private var listner:ItemClickListener? =null
    override fun bindData(holder: ViewHolder, data: Member, position: Int) {
        val check = holder.getView<ImageView>(R.id.checkMember)
        val confirmIv = holder.getView<ImageView>(R.id.item_confirm_iv)
        val userIcon = holder.getView<ImageView>(R.id.iv_user_icon)
        val name = holder.getView<TextView>(R.id.name)
        name.text =data.name
        if (StringUtils.isNotBlankAndEmpty(data.headimg)){
            ImageLoaderUtils.loadImage(mContext,userIcon,data.headimg)
        }
        if (type =="depheadset"){
            check.visibility = View.GONE
            if (data.positionId =="1"){
                confirmIv.visibility =View.VISIBLE
            }else {
                confirmIv.visibility =View.GONE
            }
            holder.setOnItemClickListener {
                clickEvent(data, confirmIv,holder.adapterPosition)
            }
        }else {
            check.isSelected = data.positionId == "1"

            check.setOnClickListener {
                if (check.isSelected){
                    //从被选中到未选中
                    check.isSelected = false
                    data.positionId = "0"
                }else {
                    check.isSelected = true
                    data.positionId = "1"
                }
            }
        }
    }

    private fun clickEvent(data: Member, confirmIv: ImageView, adapterPosition: Int) {
        if (listner != null) {
            listner?.onItemClick(adapterPosition)
        }
        for (item in mData) {
            item.positionId = "0"
        }
        data.positionId = "1"
        confirmIv.visibility = View.VISIBLE
        notifyDataSetChanged()
    }

    interface ItemClickListener{
        fun onItemClick(position: Int)
    }

    fun setClickListener(listner:ItemClickListener){
        this.listner = listner
    }

    fun getSelectedMember():ArrayList<Member>{
        val list = ArrayList<Member>()
        for(item in mData){
            if (item.positionId == "1"){
                list.add(item)
            }
        }
        return list
    }
}