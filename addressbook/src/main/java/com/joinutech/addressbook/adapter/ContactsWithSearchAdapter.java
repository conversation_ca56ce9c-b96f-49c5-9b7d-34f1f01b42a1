package com.joinutech.addressbook.adapter;

import android.content.Context;

import com.joinutech.addressbook.R;
import com.joinutech.ddbeslibrary.bean.ContactModel;
import com.joinutech.ddbeslibrary.utils.CommonUtils;
import com.joinutech.ddbeslibrary.utils.StringUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName: ContactsAdapter
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/9/15 10:34
 */
public class ContactsWithSearchAdapter extends ContactsAdapter {
    public ContactsWithSearchAdapter(List<ContactModel> contacts, Context mContext) {
        super(contacts, mContext);
    }

    private String keyword = "";

    public void setDataAndKeyword(List<ContactModel> list, String keyword) {
        setDataList(list);
        this.keyword = keyword;
    }

    @Override
    public void onBindViewHolder(@NotNull ContactsViewHolder holder, int position) {
        super.onBindViewHolder(holder, position);
        String name = getList().get(position).getName();
        if (name != null && !name.trim().isEmpty() && !keyword.trim().isEmpty() && name.contains(keyword)) {
            List<String> allKeyWordList = new ArrayList<>();
            Pattern p1 = Pattern.compile(keyword);
            Matcher m1 = p1.matcher(name);
            while (m1.find()) {
                allKeyWordList.add(m1.group());
            }
            holder.tvName.setText(StringUtils.Companion.setSpanColorStr(
                    name, allKeyWordList, CommonUtils.INSTANCE.getColor(context, com.joinutech.ddbeslibrary.R.color.colorFF5000)));
        }
    }
}
