package com.joinutech.addressbook.adapter

import android.content.Context
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.joinutech.addressbook.R
import com.joinutech.common.adapter.BaseAdapter
import com.joinutech.ddbeslibrary.bean.CompanyBean
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.marktoo.lib.cachedweb.LogUtil

class SearchUserListAdapter(var context: Context, dataList: List<CompanyBean>, val type: String)
    : BaseAdapter<CompanyBean>(dataList, context) {

    private var editText: String = ""
    private var userId: String = ""

    fun setUserId(userId: String) {
        this.userId = userId
    }

    override fun setDataSourceList(list: List<CompanyBean>?) {
        mList = list!!
        notifyDataSetChanged()
    }

    override fun addMoreData(t: CompanyBean) {
        (mList as ArrayList).add(t)
        notifyDataSetChanged()
    }

    override fun creatView(parent: ViewGroup): View? {
        val inflater = LayoutInflater.from(mContext)
        val layoutId = if (type == "searchOrg") {
            R.layout.item_search_company_result_new
        } else {
            R.layout.item_search_user_result_new
        }
        return inflater.inflate(layoutId, parent, false)
    }

    override fun updateView(holder: BaseViewHolder, position: Int) {
        val itemView = holder.itemView
        val name = itemView.findViewById<TextView>(R.id.name)
        val userIcon = itemView.findViewById<ImageView>(R.id.iv_user_icon)
        val edit = itemView.findViewById<TextView>(R.id.edit)
        val editLine = itemView.findViewById<View>(R.id.editLine)
        val line = itemView.findViewById<View>(R.id.line)
        val company = mList!![position]
        LogUtil.showLog("当前好友信息为：${GsonUtil.toJson(company)}")
        if (this.userId == company.companyId) {
            //是自己
            edit.visibility = View.VISIBLE
            edit.text = "自己"
        } else {
            edit.visibility = View.GONE
        }
        editLine.visibility = View.GONE
        line.visibility = View.GONE
        if (StringUtils.isNotBlankAndEmpty(editText) && StringUtils.isNotBlankAndEmpty(company.name)
                && company.name.contains(editText)) {
            val index = company.name.indexOf(editText)
            val frontText = company.name.substring(0, index)
            val backText = company.name.substring(index + editText.length)
            val spanned = Spanny().append(frontText)
                    .append(editText, ForegroundColorSpan(
                            CommonUtils.getColor(context, com.joinutech.ddbeslibrary.R.color.color1E87F0)))
                    .append(backText)
            name.text = spanned
        } else {
            name.text = company.name
        }
        if (StringUtils.isNotBlankAndEmpty(company.logo)) {
            ImageLoaderUtils.loadImage(context, userIcon, company.logo ?:"")
        }

        if (company.logout == 1) {
            itemView.findViewById<View>(R.id.view_logout).visibility = View.VISIBLE
        } else {
            itemView.findViewById<View>(R.id.view_logout).visibility = View.GONE
        }
    }

    fun setEditText(text: String) {
        editText = text
    }
}