package com.joinutech.addressbook.adapter

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ddbes.library.im.imtcp.Logger
import com.joinutech.addressbook.R
import com.joinutech.addressbook.view.ApplicationListActivity
import com.joinutech.ddbeslibrary.bean.CompanyUndoBeanNew
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.CircleImageView
import java.util.ArrayList

class CompanyUndoListAdapter2(var context: Context, val dataList: ArrayList<CompanyUndoBeanNew>) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {


    override fun getItemCount(): Int {
        return if (dataList.isNullOrEmpty()) 0 else dataList.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val itemView = LayoutInflater.from(context)
            .inflate(R.layout.item_undolist_company, parent, false)
        return CompanyUndoViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        Logger.i("验证批量处理","=onBindViewHolder=执行一次---")
        val companyHolder = holder as CompanyUndoViewHolder
        if (dataList.isNotEmpty()) {
            val data = dataList[position]
            if (StringUtils.isNotBlankAndEmpty(data.companyId) && data.companyId!="0"){
                companyHolder.nameTv.text=data.name
                if (StringUtils.isNotBlankAndEmpty(data.logo)){
                    ImageLoaderUtils.loadImage(context,companyHolder.headerCiv,data.logo)
                }
                if (data.count!=0){
                    companyHolder.undoCountTv.visibility =View.VISIBLE
                    companyHolder.undoCountTv.text = data.count.toString()
                }else {
                    companyHolder.undoCountTv.visibility =View.GONE
                }
            }


            companyHolder.itemView.setOnClickListener(object : View.OnClickListener {
                override fun onClick(v: View?) {
                    val intent = Intent(context, ApplicationListActivity::class.java)
                    intent.putExtra("type", "org")
                    intent.putExtra("companyId", data.companyId)
                    intent.putExtra("companyName", data.name)
                    context.startActivity(intent)
                }
            })
        }
    }

    fun setSourceList(dataListCompany: List<CompanyUndoBeanNew>) {
        dataList.clear()
        dataList.addAll(dataListCompany)
        Logger.i("验证批量处理","==dataList.size=${dataList.size}---")
        notifyDataSetChanged()
    }

    class CompanyUndoViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val headerCiv = itemView.findViewById<CircleImageView>(R.id.undoCompanyIcon)
        val nameTv = itemView.findViewById<TextView>(R.id.undoCompanyText)
        val arrowIv = itemView.findViewById<ImageView>(R.id.arrow)
        val undoCountTv = itemView.findViewById<TextView>(R.id.undoMsgCountText)

    }
}

