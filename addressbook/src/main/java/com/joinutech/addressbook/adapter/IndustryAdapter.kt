package com.joinutech.addressbook.adapter

import android.app.Activity
import android.view.View
import android.widget.TextView
import com.joinutech.addressbook.R
import com.joinutech.ddbeslibrary.bean.JobChoiceBean
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter

/**
 *<AUTHOR>
 *@date 2019/10/9
 */
class IndustryAdapter(var mcontext: Activity, var list: ArrayList<JobChoiceBean>
                      , var adapter2: IndustryAdapter2)
    : CommonAdapter<JobChoiceBean>(mcontext, list, R.layout.item_industry) {

    override fun bindData(holder: ViewHolder, data: JobChoiceBean, position: Int) {
        if (StringUtils.isNotBlankAndEmpty(data.name)) {
            val tv = holder.getView<TextView>(R.id.tv1_item_industry)
            val iv = holder.getView<View>(R.id.iv1_item_industry)
            tv.text = data.name
            if (data.type == 1) {
                holder.itemView.setBackgroundColor(mcontext.resources.getColor(com.joinutech.ddbeslibrary.R.color.white))
                tv.setTextColor(mcontext.resources.getColor(com.joinutech.ddbeslibrary.R.color.main_blue))
                iv.visibility = View.VISIBLE
            } else {
                holder.itemView.setBackgroundColor(mcontext.resources.getColor(com.joinutech.ddbeslibrary.R.color.bg_lowgray))
                tv.setTextColor(mcontext.resources.getColor(com.joinutech.ddbeslibrary.R.color.text_black))
                iv.visibility = View.INVISIBLE
            }
            holder.setOnItemClickListener {
                for (i in 0 until mData.size) {
                    if (i == position) {
                        mData[i].type = 1
                        var haveSelect =false
                        for (element in mData[i].son!!) {
                            if (element.type == 1){
                                haveSelect = true
                            }
                        }
                        if (!haveSelect) mData[i].son!![0].type = 1
                    } else {
                        for (element in mData[i].son!!) {
                            element.type = 2
                        }
                        mData[i].type = 2
                    }
                }
                adapter2.setSourceList(mData[position].son)
                adapter2.notifyDataSetChanged()
                notifyDataSetChanged()
            }
        }
    }

}