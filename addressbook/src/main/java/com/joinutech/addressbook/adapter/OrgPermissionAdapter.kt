package com.joinutech.addressbook.adapter

import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.ddbes.library.im.imtcp.Logger
import com.joinutech.addressbook.R
import com.joinutech.ddbeslibrary.bean.OrgPermissionListBean
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.ItemClickListener
import com.joinutech.ddbeslibrary.utils.ORG_PERMISS_TYPE
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter

/**
 * <AUTHOR> by 黄洁如 on 2019/3/20 15:29
 * @fileName OrgPermissionAdapter
 * @describe TODO
 * @org 加优科技
 */
class OrgPermissionAdapter(
    var context: Context, data: ArrayList<OrgPermissionListBean>,
    private val creator: Boolean
) : CommonAdapter<OrgPermissionListBean>(context, data, R.layout.item_orgpermission) {

    private var listner: ItemClickListener? = null

    fun setClickListener(listner: ItemClickListener) {
        this.listner = listner
    }

    override fun bindData(holder: ViewHolder, data: OrgPermissionListBean, position: Int) {
        if (data.userIds.isNotEmpty()) {
            holder.getView<TextView>(R.id.tv_per_user_name).setTextColor(
                CommonUtils.getColor(context, com.joinutech.ddbeslibrary.R.color.colorFF323232)
            )
            holder.setText(R.id.tv_per_user_name, data.userNames)

            val moreIv = holder.getView<ImageView>(R.id.show_more_names_iv)
            val namesTv = holder.getView<TextView>(R.id.tv_per_user_name)
            moreIv.visibility=View.VISIBLE
            moreIv.setOnClickListener {
                if (namesTv.maxLines == 1) {
                    namesTv.maxLines = 20
                    moreIv.setImageResource(R.drawable.icon_up_arrow)
                } else {
                    namesTv.maxLines = 1
                    moreIv.setImageResource(R.drawable.icon_down_arrow)
                }
            }
        } else {
            holder.getView<TextView>(R.id.tv_per_user_name).setTextColor(
                CommonUtils.getColor(context, com.joinutech.ddbeslibrary.R.color.colorFFAAAAAA)
            )
            holder.setText(R.id.tv_per_user_name, "未选择")
            holder.getView<ImageView>(R.id.show_more_names_iv).visibility=View.GONE
        }
        holder.setText(R.id.tv_per_name, data.managerName)
        if (ORG_PERMISS_TYPE.checkSuperPermission(data.managerPower)) {
            if (creator) {
                holder.setViewVisibility(R.id.iv_per_set_icon, View.VISIBLE)
                holder.setOnItemClickListener { listner?.onItemClick(position) }
            } else {
                holder.setViewVisibility(R.id.iv_per_set_icon, View.GONE)
            }
        } else {
            holder.setViewVisibility(R.id.iv_per_set_icon, View.VISIBLE)
            holder.setOnItemClickListener {
                listner?.onItemClick(position)
            }
        }
        holder.setViewVisibility(R.id.icons_contain_ll,View.GONE)
        holder.setViewVisibility(R.id.iv_org_per, View.GONE)
        holder.setViewVisibility(R.id.iv_attend_per, View.GONE)
        holder.setViewVisibility(R.id.iv_task_per, View.GONE)
        holder.setViewVisibility(R.id.iv_approval_per, View.GONE)
        holder.setViewVisibility(R.id.iv_per_icon, View.GONE)
        holder.setViewVisibility(R.id.iv_report_per, View.GONE)
        holder.setViewVisibility(R.id.iv_digital_per, View.GONE)
        holder.setViewVisibility(R.id.iv_total_approval_per, View.GONE)
        holder.setViewVisibility(R.id.iv_health_statistics_per, View.GONE)
        holder.setViewVisibility(R.id.iv_cloud_manage_per, View.GONE)
        holder.setViewVisibility(R.id.iv_visitor_manage_per, View.GONE)//访客系统
        Logger.i("权限设置页面","拉取到的权限值=${data.managerPower}")
        if (StringUtils.isNotBlankAndEmpty(data.managerPower)) {
            holder.setViewVisibility(R.id.icons_contain_ll,View.VISIBLE)
            holder.setViewVisibility(R.id.tv_no_set_per, View.GONE)
            if (ORG_PERMISS_TYPE.checkSuperPermission(data.managerPower)) {
                holder.setViewVisibility(R.id.iv_per_icon, View.VISIBLE)
                holder.setViewVisibility(R.id.iv_org_per, View.VISIBLE)
                holder.setViewVisibility(R.id.iv_attend_per, View.VISIBLE)
                holder.setViewVisibility(R.id.iv_task_per, View.VISIBLE)
                holder.setViewVisibility(R.id.iv_approval_per, View.VISIBLE)
                holder.setViewVisibility(R.id.iv_report_per, View.VISIBLE)
                holder.setViewVisibility(R.id.iv_digital_per, View.VISIBLE)
                holder.setViewVisibility(R.id.iv_total_approval_per, View.VISIBLE)
                holder.setViewVisibility(R.id.iv_health_statistics_per, View.VISIBLE)
                holder.setViewVisibility(R.id.iv_cloud_manage_per, View.VISIBLE)
                holder.setViewVisibility(R.id.iv_visitor_manage_per, View.VISIBLE)//访客系统
            } else {
                for (item in data.managerPower.toCharArray()) {
                    when (item) {
                        //1：团队，2：考勤，3：任务，4:审批 5汇报权限
                        ORG_PERMISS_TYPE.ORG_PERMISSION -> {
                            holder.setViewVisibility(R.id.iv_org_per, View.VISIBLE)
                        }
                        ORG_PERMISS_TYPE.ATTENDANCE_PERMISSION -> {
                            holder.setViewVisibility(R.id.iv_attend_per, View.VISIBLE)
                        }
                        ORG_PERMISS_TYPE.TASK_PERMISSION -> {
                            holder.setViewVisibility(R.id.iv_task_per, View.VISIBLE)
                        }
                        ORG_PERMISS_TYPE.APPROVAL_PERMISSION -> {
                            holder.setViewVisibility(R.id.iv_approval_per, View.VISIBLE)
                        }
                        ORG_PERMISS_TYPE.REPORT_PERMISSION -> {
                            holder.setViewVisibility(R.id.iv_report_per, View.VISIBLE)
                        }
                        ORG_PERMISS_TYPE.DIGITAL_REPORT -> {
                            holder.setViewVisibility(R.id.iv_digital_per, View.VISIBLE)
                        }
                        ORG_PERMISS_TYPE.TOTAL_APPROVAL -> {
                            holder.setViewVisibility(R.id.iv_total_approval_per, View.VISIBLE)
                        }
                        ORG_PERMISS_TYPE.HEALTH_STATISTICS -> {
                            holder.setViewVisibility(R.id.iv_health_statistics_per, View.VISIBLE)
                        }
                        ORG_PERMISS_TYPE.CLOUD_MANAGE -> {
                            holder.setViewVisibility(R.id.iv_cloud_manage_per, View.VISIBLE)
                        }
                        ORG_PERMISS_TYPE.VISITOR_MANAGE -> {//访客系统，小图标是否显示,完成
                            holder.setViewVisibility(R.id.iv_visitor_manage_per, View.VISIBLE)
                        }
                    }
                }
            }
        } else {
            holder.setViewVisibility(R.id.tv_no_set_per, View.VISIBLE)
        }
    }

}