package com.joinutech.addressbook.adapter;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;
import com.joinutech.addressbook.R;
import com.joinutech.addressbook.view.FriendInfoActivity;
import com.joinutech.addressbook.view.FriendListActivity;
import com.joinutech.addressbook.view.OnContactSelectListener;
import com.joinutech.addressbook.view.PersonSearchSelectList;
import com.joinutech.addressbook.view.PhoneContactActivity;
import com.joinutech.ddbeslibrary.bean.ContactModel;
import com.joinutech.ddbeslibrary.utils.CommonUtils;
import com.joinutech.ddbeslibrary.utils.StringUtils;
import com.joinutech.ddbeslibrary.utils.ToastUtil;
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils;
import com.joinutech.ddbeslibrary.widget.CircleImageView;
import com.marktoo.lib.cachedweb.LogUtil;

import org.jetbrains.annotations.NotNull;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @PackageName: com.joinutech.addressbook.view
 * @ClassName: ContactsAdapter
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/9/15 10:34
 * @Desc: //TODO Contact联系人适配器
 * @see PhoneContactActivity // 通讯录联系人
 * @see PersonSearchSelectList// 人员搜索
 * @see FriendListActivity // 好友列表
 */
public class ContactsAdapter extends RecyclerView.Adapter<ContactsAdapter.ContactsViewHolder> {

    private List<ContactModel> contacts;
    private static final String TAG = "ContactsAdapter";
    protected Context context;
    private String adapterType = "";
    //    private onItemCheckListener listener;
    private onItemClickListener clickListener;

//    public void setCheckListener(onItemCheckListener listener) {
//        this.listener = listener;
//    }

    public void setOnClickListener(onItemClickListener clickListener) {
        this.clickListener = clickListener;
    }

    public ContactsAdapter(List<ContactModel> contacts, Context mContext) {
        this.contacts = contacts;
        this.context = mContext;
    }

    private Set<String> logoutUserIds = new HashSet<>();

    public void setLogoutUserIds(Set<String> logoutUserIds) {
        this.logoutUserIds = logoutUserIds;
    }

    @NotNull
    @Override
    public ContactsViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        View view = inflater.inflate(R.layout.layaout_item_contacts, null);
        return new ContactsViewHolder(view);
    }

    private OnContactSelectListener selectListener;

    public OnContactSelectListener getSelectListener() {
        return selectListener;
    }

    public void setSelectListener(OnContactSelectListener selectListener) {
        this.selectListener = selectListener;
    }

    @Override
    public void onBindViewHolder(@NotNull final ContactsViewHolder holder, int position) {
        final ContactModel contact = contacts.get(position);
        LogUtil.INSTANCE.showLog("onBindViewHolder: index:" + contact.getIndex());
        holder.checkBoxMember.setVisibility(View.GONE);
        holder.positionEr.setVisibility(View.GONE);
        if (StringUtils.Companion.isNotBlankAndEmpty(adapterType)) {
            switch (adapterType) {
                // 联系人类型数据
                case "phoneContact": { // 通讯录页面
                    holder.statusText.setVisibility(View.VISIBLE);
                    holder.itemView.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (logoutUserIds.contains(contact.getUserId())) {
                                ToastUtil.INSTANCE.show(context, "该好友已注销账号");
                            } else {
                                clickListener.onClick(contact);
                            }
                        }
                    });
                    //1 仅注册担当 和2 担当用户并与登录用户为好友
                    switch (contact.getStatus()) {
                        case 0: {
                            holder.statusText.setText("未注册");
                            holder.statusText.setBackground(null);
                            break;
                        }
                        case 1: {
                            holder.statusText.setText("未添加");
                            holder.statusText.setBackground(null);
//                            holder.itemView.setOnClickListener(new View.OnClickListener() {
//                                @Override
//                                public void onClick(View v) {
////                                    Intent intent = new Intent(context, FriendInfoActivity.class); //进入用户详情
////                                    intent.putExtra("userId", contact.getUserId());
////                                    intent.putExtra("type", "");
////                                    intent.putExtra("enterType", 1); //搜索好友标记 ？
////                                    context.startActivity(intent);
//                                    clickListener.onClick(contact);
//                                }
//                            });
                            break;
                        }
                        case 2: {
                            holder.statusText.setText("已添加");
                            holder.statusText.setTextColor(CommonUtils.INSTANCE.
                                    getColor(context, com.joinutech.ddbeslibrary.R.color.color1E87F0));
//                            holder.itemView.setOnClickListener(new View.OnClickListener() {
//                                @Override
//                                public void onClick(View v) {
////                                    Intent intent = new Intent(context, FriendInfoActivity.class); //进入用户详情
////                                    intent.putExtra("type", "friend");
////                                    intent.putExtra("userId", contact.getUserId());
////                                    context.startActivity(intent);
//                                    clickListener.onClick(contact);
//                                }
//                            });
                            break;
                        }
                        case 3: {
                            holder.statusText.setText("(自己)");
                            holder.statusText.setBackground(null);
//                            holder.itemView.setOnClickListener(new View.OnClickListener() {
//                                @Override
//                                public void onClick(View v) {
//                                    Intent intent = new Intent(context, FriendInfoActivity.class); //进入用户详情
//                                    intent.putExtra("userId", contact.getUserId());
//                                    intent.putExtra("type", "friend");
//                                    context.startActivity(intent);
//                                }
//                            });
                            break;
                        }
                    }
                    break;
                }
                case "addExternalContact": { // 选择添加外部联系人
                    holder.statusText.setVisibility(View.VISIBLE);
                    //1 仅注册担当 和2 担当用户并与登录用户为好友
                    switch (contact.getStatus()) {
                        case 0: {
                            holder.statusText.setText("未注册");
                            holder.statusText.setBackground(null);
                            break;
                        }
                        case 1: {
                            holder.statusText.setText("未添加");
                            holder.statusText.setBackground(null);
                            break;
                        }
                        case 2: {
                            holder.statusText.setText("已添加");
                            holder.statusText.setTextColor(CommonUtils.INSTANCE.
                                    getColor(context, com.joinutech.ddbeslibrary.R.color.color1E87F0));
                            break;
                        }
                        case 3: {
                            holder.statusText.setText("(自己)");
                            holder.statusText.setBackground(null);
                            break;
                        }
                    }
                    holder.itemView.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (logoutUserIds.contains(contact.getUserId())) {
                                ToastUtil.INSTANCE.show(context, "该好友已注销账号");
                            } else {
                                clickListener.onClick(contact);
                            }
                        }
                    });
                    break;
                }
                // 多选类型
                case "transMsg":
                case "transGroupMsg":
                case "givePermission":
                case "shareFile":
                case "addOrgMsg": {
                    holder.checkBoxMember.setVisibility(View.VISIBLE);
                    if (contact.getNoSelect()) {
                        //需要处理有不可选的情况,创建项目时创建者不可取消
                        holder.checkBoxMember.setImageResource(com.joinutech.ddbeslibrary.R.drawable.icon_person_noselect_permission);
                    } else {
                        holder.checkBoxMember.setImageResource(com.joinutech.ddbeslibrary.R.drawable.checkbox_friend_selector);
                        holder.itemView.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                if (logoutUserIds.contains(contact.getUserId())) {
                                    ToastUtil.INSTANCE.show(context, "该好友已注销账号");
                                } else {
//                                    if (listener != null) {
//                                        listener.onCkeck(contact, holder.checkBoxMember);
//                                    }
                                    if (clickListener != null) {
                                        holder.checkBoxMember.setSelected(!holder.checkBoxMember.isSelected());
                                        clickListener.onClick(contact);
                                    }
                                }
                            }
                        });
                    }
                    holder.checkBoxMember.setSelected(contact.getCheck());
                    break;
                }
//                case "selectMembers":
                case "selectProjectJoiner":
                case "CreateProgramSelectProjectJoiner": {
                    holder.checkBoxMember.setVisibility(View.VISIBLE);
                    //列表中增加职位显示
                    if (StringUtils.Companion.isNotBlankAndEmpty(contact.getPhoneNum())) {
                        holder.positionEr.setVisibility(View.VISIBLE);
                        holder.positionEr.setText(contact.getPhoneNum());
                    } else {
                        holder.positionEr.setVisibility(View.GONE);
                    }
                    if (contact.getNoSelect()) {
                        //需要处理有不可选的情况,创建项目时创建者不可取消
                        holder.checkBoxMember.setImageResource(com.joinutech.ddbeslibrary.R.drawable.icon_person_noselect_permission);
                    } else {
                        holder.checkBoxMember.setImageResource(com.joinutech.ddbeslibrary.R.drawable.checkbox_friend_selector);
                        holder.itemView.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                if (logoutUserIds.contains(contact.getUserId())) {
                                    ToastUtil.INSTANCE.show(context, "该好友已注销账号");
                                } else {
                                    holder.checkBoxMember.setSelected(contact.getCheck());
//                                    if (listener != null) {
//                                        listener.onCkeck(contact, holder.checkBoxMember);
//                                    }
                                    if (clickListener != null) {
                                        holder.checkBoxMember.setSelected(!holder.checkBoxMember.isSelected());
                                        clickListener.onClick(contact);
                                    }
                                }
                            }
                        });
                    }
                    holder.checkBoxMember.setSelected(contact.getCheck());
                    break;
                }
                // 单选类型
                case "shareToFriend":
                case "selectMember": {
                    /*选择成员时处理布局样式*/
                    holder.statusText.setVisibility(View.GONE);
                    holder.itemView.setTag(position);
                    holder.itemView.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (logoutUserIds.contains(contact.getUserId())) {
                                ToastUtil.INSTANCE.show(context, "该好友已注销账号");
                            } else {
                                if (selectListener != null) {
                                    selectListener.onSelect((int) holder.itemView.getTag());
                                }
                            }
                        }
                    });
                    break;
                }
                // 默认类型
                default: {
                    holder.statusText.setVisibility(View.GONE);
                    holder.itemView.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (logoutUserIds.contains(contact.getUserId())) {
                                ToastUtil.INSTANCE.show(context, "该好友已注销账号");
                            } else {
                                if (adapterType.equals("ProjectHandOverSelectMember")) {
                                    Intent intent = ((PersonSearchSelectList) context).getIntent();
                                    intent.putExtra("personName", contact.getName());
                                    intent.putExtra("personUserId", contact.getUserId());
                                    ((PersonSearchSelectList) context).setResult(Activity.RESULT_OK, intent);
                                    ((PersonSearchSelectList) context).finish();
                                } else {
                                    Intent intent = new Intent(context, FriendInfoActivity.class); //进入用户详情
                                    intent.putExtra("type", "friend");
                                    intent.putExtra("userId", contact.getUserId());
                                    context.startActivity(intent);
                                }
                            }
                        }
                    });
                    break;
                }
            }
        } else { // 未设置任务类型，跳转用户详情页面
            holder.statusText.setVisibility(View.GONE);
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (logoutUserIds.contains(contact.getUserId())) {
                        ToastUtil.INSTANCE.show(context, "该好友已注销账号");
                    } else {
                        Intent intent = new Intent(context, FriendInfoActivity.class); //进入用户详情
                        intent.putExtra("type", "friend");
                        intent.putExtra("userId", contact.getUserId());
                        context.startActivity(intent);
                    }
                }
            });
        }
        if (position == 0 || !contacts.get(position - 1).getIndex().equals(contact.getIndex())) {
            holder.tvIndex.setVisibility(View.VISIBLE);
            holder.tvIndex.setText(contact.getIndex());
            if (contact.getSelect()) {
                holder.tvIndex.setTextColor(CommonUtils.
                        INSTANCE.getColor(context, com.joinutech.ddbeslibrary.R.color.color1E87F0));
            } else {
                holder.tvIndex.setTextColor(CommonUtils.
                        INSTANCE.getColor(context, com.joinutech.ddbeslibrary.R.color.colorBDBDBD));
            }
        } else {
            holder.tvIndex.setVisibility(View.GONE);
        }
        holder.tvName.setText(contact.getName());
        if (StringUtils.Companion.isNotBlankAndEmpty(contact.getLogo())) {
            ImageLoaderUtils.INSTANCE.loadImage(context, holder.ivAvatar, contact.getLogo());
        } else {
            Glide.with(context).load(contact.getBitmap())
                    .apply(RequestOptions.bitmapTransform(new CircleCrop())).into(holder.ivAvatar);
        }

        if (logoutUserIds.contains(contact.getUserId())) {
            holder.logoutCover.setVisibility(View.VISIBLE);
        } else {
            holder.logoutCover.setVisibility(View.GONE);
        }
    }

    public List<ContactModel> getList() {
        return this.contacts;
    }

    public void setDataList(List<ContactModel> list) {
        contacts = list;
        notifyDataSetChanged();
    }

    public void addData(ContactModel contactModel) {
        contacts.add(contactModel);
        notifyDataSetChanged();
    }

//    public interface onItemCheckListener {
//        void onCkeck(ContactModel contact, ImageView checkBoxMember);
//    }

    public interface onItemClickListener {
        void onClick(ContactModel contact);
    }

    public void setType(String type) {
        this.adapterType = type;
    }

    @Override
    public int getItemCount() {
        return contacts.size();
    }

    class ContactsViewHolder extends RecyclerView.ViewHolder {
        TextView tvIndex;
        CircleImageView ivAvatar;
        TextView tvName;
        TextView statusText;
        TextView positionEr;
        ImageView checkBoxMember;
        View logoutCover;

        ContactsViewHolder(View itemView) {
            super(itemView);
            tvIndex = itemView.findViewById(R.id.tv_index);
            ivAvatar = itemView.findViewById(R.id.iv_avatar);
            tvName = itemView.findViewById(R.id.tv_name);
            statusText = itemView.findViewById(R.id.statusText);
            positionEr = itemView.findViewById(R.id.positionEr);
            checkBoxMember = itemView.findViewById(R.id.checkBoxMember);
            logoutCover = itemView.findViewById(R.id.view_logout);
        }
    }
}
