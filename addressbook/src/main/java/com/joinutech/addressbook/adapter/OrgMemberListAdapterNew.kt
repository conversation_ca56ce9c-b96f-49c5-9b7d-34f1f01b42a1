package com.joinutech.addressbook.adapter

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.common.adapter.BaseAdapter
import com.joinutech.addressbook.R
import com.joinutech.addressbook.view.FriendInfoActivity
import com.joinutech.ddbeslibrary.bean.Member
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.ddbeslibrary.widget.CircleImageView

class OrgMemberListAdapterNew(context: Context,
                              dataList: List<Member>,
                              var type: String, var companyId: String)
    : BaseAdapter<Member>(dataList, context) {

    private var createId: String = ""
    private var depId: String = "0"
    private var depName: String = ""
    private var orgPermission = false

    override fun setDataSourceList(list: List<Member>?) {
        mList = list!!
        notifyDataSetChanged()
    }

    override fun addMoreData(t: Member) {
        (mList as ArrayList).add(t)
        notifyDataSetChanged()
    }

    override fun creatView(parent: ViewGroup): View? {
        val inflater = LayoutInflater.from(mContext)
        return inflater.inflate(R.layout.item_search_user_result_new, parent, false)
    }

    fun setCreateId(createId: String) {
        this.createId = createId
    }

    fun setOrgPermission(orgPermission: Boolean) {
        this.orgPermission = orgPermission
    }

    fun setDepName(depName: String) {
        this.depName = depName
    }

    fun setDepId(depId: String) {
        this.depId = depId
    }

    override fun updateView(holder: BaseViewHolder, position: Int) {
        val itemView = holder.itemView
        val name = itemView.findViewById<TextView>(R.id.name)
        val userIcon = itemView.findViewById<CircleImageView>(R.id.iv_user_icon)
        val createMark = itemView.findViewById<TextView>(R.id.createMark)
        val managerMark = itemView.findViewById<TextView>(R.id.managerMark)
        val managerViceMark = itemView.findViewById<TextView>(R.id.managerViceMark)
        val edit = itemView.findViewById<TextView>(R.id.edit)
        val editLine = itemView.findViewById<View>(R.id.editLine)
        val line = itemView.findViewById<View>(R.id.line)
        if (mList != null && mList!!.isNotEmpty()) {
            val member = mList!![position]
            if (position == mList!!.size - 1) {
                line.visibility = View.GONE
            } else line.visibility = View.VISIBLE
            if (mList!!.isNotEmpty()) {
                name.text = member.name
                if (StringUtils.isNotBlankAndEmpty(member.headimg)) {
                    ImageLoaderUtils.loadImage(mContext, userIcon, member.headimg)
                }
            }
            if (type == "member") {
                //只有成员信息列表
                createMark.visibility = View.GONE
                managerMark.visibility = View.GONE
                edit.visibility = View.GONE
                editLine.visibility = View.GONE
            } else {
                //团队架构页
                when (member.level) {
                    0 -> {
                        managerMark.visibility = View.GONE
                        managerViceMark.visibility = View.GONE
                    }
                    1 -> {
                        managerMark.visibility = View.VISIBLE
                        managerViceMark.visibility = View.GONE
                    }
                    2 -> {
                        managerMark.visibility = View.GONE
                        managerViceMark.visibility = View.VISIBLE
                    }
                }

                if (createId == member.userId) {
                    createMark.visibility = View.VISIBLE
                } else {
                    createMark.visibility = View.GONE
                }
                if (orgPermission) {
                    edit.visibility = View.VISIBLE
                    editLine.visibility = View.VISIBLE
                } else {
                    edit.visibility = View.GONE
                    editLine.visibility = View.GONE
                }
            }
            itemView.setOnClickListener {
                val intent = Intent(mContext, FriendInfoActivity::class.java)
                intent.putExtra("enterType", 3) //团队群组标记
                intent.putExtra("name", member.name)
                intent.putExtra("userId", member.userId)
                intent.putExtra("companyId", companyId)
                intent.putExtra("depName", depName)
                intent.putExtra("depId", depId)
                intent.putExtra("createId", createId)
                intent.putExtra("orgPermission", orgPermission)
                intent.putExtra("type", "org")
                mContext.startActivity(intent)
            }
        }
    }
}