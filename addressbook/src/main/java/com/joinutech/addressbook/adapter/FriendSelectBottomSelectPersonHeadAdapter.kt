package com.joinutech.addressbook.adapter

import android.content.Context
import android.view.LayoutInflater
import com.joinutech.addressbook.R
import com.joinutech.addressbook.databinding.ItemFriendSelectBottomSelectHeadBinding
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter
import com.joinutech.common.adapter.CommonBindingAdapter

/**
 * @Description: 好友选择带搜索页面底部选择头像的adapter
 * @Author: hjr
 * @Time: 2020/3/13 10:19
 * @packageName: com.joinutech.addressbook.adapter
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
class FriendSelectBottomSelectPersonHeadAdapter(context: Context, dataList: ArrayList<String>)
    : CommonBindingAdapter<String,ItemFriendSelectBottomSelectHeadBinding>(context, dataList, R.layout.item_friend_select_bottom_select_head) {


    override fun inflateViewBinding(layoutInflater: LayoutInflater): ItemFriendSelectBottomSelectHeadBinding {
        return ItemFriendSelectBottomSelectHeadBinding.inflate(layoutInflater)
    }

    override fun onBindItem(
        binding: ItemFriendSelectBottomSelectHeadBinding,
        item: String?,
        position: Int,
    ) {
        item?.let { data->
            if (StringUtils.isNotBlankAndEmpty(data)) {
                ImageLoaderUtils.loadImage(mContext, binding.ivHeaderImage, data)
            }
        }

    }
}