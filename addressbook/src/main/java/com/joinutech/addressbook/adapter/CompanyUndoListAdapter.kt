package com.joinutech.addressbook.adapter

import android.content.Context
import android.content.Intent
import android.view.View
import android.widget.TextView
import com.ddbes.library.im.imtcp.Logger
import com.joinutech.addressbook.R
import com.joinutech.addressbook.view.ApplicationListActivity
import com.joinutech.ddbeslibrary.bean.CompanyUndoBeanNew
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter

/**
 * 公司消息待处理列表
 */
class CompanyUndoListAdapter (context: Context,dataList:ArrayList<CompanyUndoBeanNew>)
    : CommonAdapter<CompanyUndoBeanNew>(context,dataList, R.layout.item_undolist_company){

    override fun bindData(holder: ViewHolder, data: CompanyUndoBeanNew, position: Int) {
        Logger.i("验证批量处理","==companyId=${data.companyId}---")
        if (StringUtils.isNotBlankAndEmpty(data.companyId) && data.companyId!="0"){
            holder.setText(R.id.undoCompanyText,data.name)
            if (StringUtils.isNotBlankAndEmpty(data.logo)){
                ImageLoaderUtils.loadImage(mContext,holder.getView(R.id.undoCompanyIcon),data.logo)
            }
            val undoMsgCountText = holder.getView<TextView>(R.id.undoMsgCountText)
            if (data.count!=0){
                undoMsgCountText.visibility =View.VISIBLE
                undoMsgCountText.text = data.count.toString()
            }else {
                undoMsgCountText.visibility =View.GONE
            }
            holder.setOnItemClickListener {
                val intent = Intent(mContext, ApplicationListActivity::class.java)
                intent.putExtra("type", "org")
                intent.putExtra("companyId", data.companyId)
                intent.putExtra("companyName", data.name)
                mContext.startActivity(intent)
            }
        }
    }
}