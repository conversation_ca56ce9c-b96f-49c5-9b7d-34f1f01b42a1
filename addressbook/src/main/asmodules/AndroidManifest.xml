<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.joinutech.addressbook">
    <application
        android:allowBackup="true"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:ignore="GoogleAppIndexingWarning">
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".MainActivity">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />

            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
        </activity>
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.UndoActivity" />
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.MineOrganizationsActivity"/>
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.CreateOrgActivity"/>
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.CreateOrganization2Activity"/>
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.FriendInfoActivity" />
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.SearchResultActivity" />
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.IndustryActivity"/>
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.OrganizationIntroActivity"/>
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.SelectVisitMethodActivity"/>
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.VerifyApplicationActivity"/>
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.ApplicationDetailsActivity"/>
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.OrganizationChartActivity" />
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.SelectedMemberActivity" />
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.OrgDepartmentActivity" />
        <activity android:configChanges="orientation" android:screenOrientation="portrait" android:name=".view.DepartmentSetActivity" />
    </application>
</manifest>