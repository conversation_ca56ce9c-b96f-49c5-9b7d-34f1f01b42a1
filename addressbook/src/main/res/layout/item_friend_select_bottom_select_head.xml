<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="24dp"
    android:paddingStart="0dp"
    android:paddingEnd="8dp">

    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/iv_header_image"
        android:layout_width="24dp"
        android:layout_height="24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>