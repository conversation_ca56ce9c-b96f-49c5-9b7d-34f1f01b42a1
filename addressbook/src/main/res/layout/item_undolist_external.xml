<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/undoCompanyIcon"
        android:layout_width="43dp"
        android:layout_height="43dp"
        android:layout_marginStart="@dimen/dp_14"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginBottom="@dimen/dp_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/undoCompanyText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:ellipsize="end"
        android:maxEms="10"
        android:singleLine="true"
        android:text="加优科技有限公司"
        android:textColor="@color/colorFF323232"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/undoCompanyIcon"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/arrow"
        android:layout_width="9dp"
        android:layout_height="15dp"
        android:layout_marginEnd="@dimen/dp_14"
        android:src="@drawable/icon_undocompany_arrow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/undoMsgCountText"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="@dimen/dp_18"
        android:background="@drawable/oval_ff3300"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/arrow"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="1"
        tools:visibility="visible" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_grey"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>