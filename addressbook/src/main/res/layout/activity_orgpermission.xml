<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/org_permission_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="12dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent" />

    <!--默认空白页面-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:ev_content="暂无添加的管理员"
        app:ev_desc="点击右上角的加号，可以添加管理员"
        app:ev_icon="@drawable/ic_empty_friend"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>