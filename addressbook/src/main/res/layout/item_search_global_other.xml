<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_58">

    <com.joinutech.ddbeslibrary.widget.roundimage.RoundedImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_36"
        android:layout_marginStart="@dimen/dp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:riv_corner_radius="@dimen/dp_12" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:paddingStart="@dimen/dp_15"
        android:text="设计的部门"
        android:textColor="@color/colorFF323232"
        android:textSize="@dimen/sp_13"
        app:layout_constraintBottom_toTopOf="@id/tv_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:includeFontPadding="false"
        android:paddingStart="@dimen/dp_15"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toBottomOf="@id/tv_name"
        tools:text="包含用户：我的朋友"
        tools:visibility="visible" />

    <!--todo 减少层级和布局案例-->

    <!--    <androidx.constraintlayout.widget.ConstraintLayout-->
    <!--        android:id="@+id/rl_friend_layout"-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="48dp"-->
    <!--        android:layout_marginStart="15dp"-->
    <!--        android:visibility="gone"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        app:layout_constraintStart_toEndOf="@id/userIv"-->
    <!--        app:layout_constraintTop_toTopOf="parent">-->

    <!--        <TextView-->
    <!--            android:id="@+id/userNameTv"-->
    <!--            android:layout_width="0dp"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:gravity="center_vertical"-->
    <!--            android:text="我的同学-李湘"-->
    <!--            android:textColor="@color/colorFF323232"-->
    <!--            android:textSize="@dimen/sp_13"-->
    <!--            app:layout_constraintBottom_toBottomOf="parent"-->
    <!--            app:layout_constraintEnd_toEndOf="parent"-->
    <!--            app:layout_constraintStart_toStartOf="parent"-->
    <!--            app:layout_constraintTop_toTopOf="parent" />-->
    <!--    </androidx.constraintlayout.widget.ConstraintLayout>-->

    <!--    <androidx.constraintlayout.widget.ConstraintLayout-->
    <!--        android:id="@+id/cl_group_layout"-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="58dp"-->
    <!--        android:layout_marginStart="15dp"-->
    <!--        android:visibility="gone"-->
    <!--        tools:visibility="visible"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        app:layout_constraintStart_toEndOf="@id/userIv"-->
    <!--        app:layout_constraintTop_toTopOf="parent">-->

    <!--        <androidx.constraintlayout.widget.ConstraintLayout-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            app:layout_constraintBottom_toBottomOf="parent"-->
    <!--            app:layout_constraintTop_toTopOf="parent">-->

    <!--            <TextView-->
    <!--                android:id="@+id/groupNameTv"-->
    <!--                android:layout_width="0dp"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:gravity="center_vertical"-->
    <!--                android:text="设计的部门"-->
    <!--                android:textColor="@color/colorFF323232"-->
    <!--                android:textSize="@dimen/sp_13"-->
    <!--                app:layout_constraintEnd_toEndOf="parent"-->
    <!--                app:layout_constraintStart_toStartOf="parent"-->
    <!--                app:layout_constraintTop_toTopOf="parent" />-->

    <!--            <TextView-->
    <!--                android:id="@+id/groupInfo"-->
    <!--                android:layout_width="0dp"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:layout_marginTop="8dp"-->
    <!--                android:text="包含用户：我的朋友"-->
    <!--                android:textColor="@color/color999999"-->
    <!--                android:textSize="@dimen/sp_12"-->
    <!--                android:visibility="gone"-->
    <!--                app:layout_constraintEnd_toEndOf="parent"-->
    <!--                app:layout_constraintStart_toStartOf="parent"-->
    <!--                app:layout_constraintTop_toBottomOf="@id/groupNameTv" />-->
    <!--        </androidx.constraintlayout.widget.ConstraintLayout>-->

    <!--    </androidx.constraintlayout.widget.ConstraintLayout>-->

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_color"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>