<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:scrollbars="none">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="14dp"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="6dp"
            android:text="基本信息"
            android:textColor="@color/text_lowgray"
            android:textSize="@dimen/sp_13" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/info_r1"
                android:layout_width="match_parent"
                android:layout_height="82dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="14dp"
                    android:text="企业LOGO"
                    android:textColor="@color/text_black"
                    android:textSize="@dimen/sp_16" />

                <ImageView
                    android:id="@+id/arraw1_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="14dp"
                    android:src="@drawable/arraw_right" />

                <com.joinutech.ddbeslibrary.widget.CircleImageView
                    android:id="@+id/info_logo"
                    android:layout_width="52dp"
                    android:layout_height="52dp"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="10dp"
                    android:layout_toLeftOf="@id/arraw1_info" />
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginLeft="14dp"
                android:background="@color/line_grey" />

            <RelativeLayout
                android:id="@+id/info_r2"
                android:layout_width="match_parent"
                android:layout_height="50dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="14dp"
                    android:text="团队名称"
                    android:textColor="@color/text_black"
                    android:textSize="@dimen/sp_16" />

                <ImageView
                    android:id="@+id/arraw2_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="14dp"
                    android:src="@drawable/arraw_right" />

                <TextView
                    android:id="@+id/info_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="10dp"
                    android:layout_toLeftOf="@id/arraw2_info"
                    android:textColor="#666666"
                    android:textSize="@dimen/sp_13" />
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginLeft="14dp"
                android:background="@color/line_grey" />

            <RelativeLayout
                android:id="@+id/info_r3"
                android:layout_width="match_parent"
                android:layout_height="50dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="14dp"
                    android:text="团队所属行业"
                    android:textColor="@color/text_black"
                    android:textSize="@dimen/sp_16" />

                <ImageView
                    android:id="@+id/arraw3_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="14dp"
                    android:src="@drawable/arraw_right" />

                <TextView
                    android:id="@+id/info_industry"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="10dp"
                    android:layout_toLeftOf="@id/arraw3_info"
                    android:textColor="#666666"
                    android:textSize="@dimen/sp_13" />
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginLeft="14dp"
                android:background="@color/line_grey" />

            <RelativeLayout
                android:id="@+id/info_r4"
                android:layout_width="match_parent"
                android:layout_height="50dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="14dp"
                    android:text="团队类型"
                    android:textColor="@color/text_black"
                    android:textSize="@dimen/sp_16" />

                <ImageView
                    android:id="@+id/arraw4_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="14dp"
                    android:src="@drawable/arraw_right" />

                <TextView
                    android:id="@+id/info_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="10dp"
                    android:layout_toLeftOf="@id/arraw4_info"
                    android:textColor="#666666"
                    android:textSize="@dimen/sp_13" />
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginLeft="14dp"
                android:background="@color/line_grey" />

            <RelativeLayout
                android:id="@+id/info_r5"
                android:layout_width="match_parent"
                android:layout_height="50dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="14dp"
                    android:text="团队人数"
                    android:textColor="@color/text_black"
                    android:textSize="@dimen/sp_16" />

                <ImageView
                    android:id="@+id/arraw5_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="14dp"
                    android:src="@drawable/arraw_right" />

                <TextView
                    android:id="@+id/info_nums"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="10dp"
                    android:layout_toLeftOf="@id/arraw5_info"
                    android:textColor="#666666"
                    android:textSize="@dimen/sp_13" />
            </RelativeLayout>
        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="14dp"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="6dp"
            android:text="详细信息"
            android:textColor="@color/text_lowgray"
            android:textSize="@dimen/sp_13" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/companyWebLayout"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/white">

            <TextView
                android:id="@+id/webHint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="14dp"
                android:text="官方网站"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_16"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginStart="14dp"
                android:background="@color/line_grey"
                app:layout_constraintBottom_toBottomOf="parent" />

            <ImageView
                android:id="@+id/web_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginEnd="14dp"
                android:src="@drawable/arraw_right"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/webEdit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="9dp"
                android:ellipsize="end"
                android:singleLine="true"
                android:text="未填写"
                android:textColor="#666666"
                android:textSize="@dimen/sp_13"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/web_info"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/companyPhoneLayout"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/white">

            <TextView
                android:id="@+id/phoneHint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="14dp"
                android:text="联系电话"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_16"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginStart="14dp"
                android:background="@color/line_grey"
                app:layout_constraintBottom_toBottomOf="parent" />

            <ImageView
                android:id="@+id/phone_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginEnd="14dp"
                android:src="@drawable/arraw_right"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/phoneEdit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="9dp"
                android:ellipsize="end"
                android:singleLine="true"
                android:text="未填写"
                android:textColor="#666666"
                android:textSize="@dimen/sp_13"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/phone_info"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/companyEmailLayout"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/white">

            <TextView
                android:id="@+id/emailHint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="14dp"
                android:text="邮箱"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_16"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginStart="14dp"
                android:background="@color/line_grey"
                app:layout_constraintBottom_toBottomOf="parent" />

            <ImageView
                android:id="@+id/email_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginEnd="14dp"
                android:src="@drawable/arraw_right"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/emailEdit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="9dp"
                android:ellipsize="end"
                android:singleLine="true"
                android:text="未填写"
                android:textColor="#666666"
                android:textSize="@dimen/sp_13"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/email_info"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/companyIntroLayout"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/white"
            android:paddingBottom="8dp">

            <TextView
                android:id="@+id/shortInfoHint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="14dp"
                android:text="团队简介"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_16"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/shortInfo_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginEnd="14dp"
                android:src="@drawable/arraw_right"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/shortInfoEdit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="9dp"
                android:layout_marginEnd="9dp"
                android:ellipsize="end"
                android:gravity="end"
                android:singleLine="true"
                android:text="未填写"
                android:textColor="#666666"
                android:textSize="@dimen/sp_13"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/shortInfo_info"
                app:layout_constraintStart_toEndOf="@id/shortInfoHint"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</ScrollView>