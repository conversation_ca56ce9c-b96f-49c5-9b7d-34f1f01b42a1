<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/cl_friend_bottom_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_friend_root_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/dp_10">
            <!--蓝色区域-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/lv_top"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <!--安全提示显示的时候，高设置为276，不显示的时候设置为240-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cd_top"
                    android:layout_width="match_parent"
                    android:layout_height="276dp"
                    android:background="@drawable/bg_user_info_top"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.joinutech.ddbeslibrary.widget.CircleImageView
                        android:id="@+id/iv_user_icon"
                        android:layout_width="@dimen/dp_67"
                        android:layout_height="@dimen/dp_67"
                        android:layout_marginTop="@dimen/dp_96"
                        android:layout_marginEnd="@dimen/dp_35"
                        app:civ_border_color="@color/white"
                        app:civ_border_width="@dimen/dp_1"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <!--严格UI样式 位置固定-->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/view_bg_friend_top"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_35"
                        android:layout_marginTop="@dimen/dp_108"
                        app:layout_constraintEnd_toStartOf="@id/iv_user_icon"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <!--依据头像，左侧数据以头像为中心上下浮动-->
                        <!--                    <androidx.constraintlayout.widget.ConstraintLayout-->
                        <!--                        android:id="@+id/view_bg_friend_top"-->
                        <!--                        android:layout_width="0dp"-->
                        <!--                        android:layout_height="wrap_content"-->
                        <!--                        android:layout_marginStart="@dimen/dp_35"-->
                        <!--                        app:layout_constraintBottom_toBottomOf="@id/iv_user_icon"-->
                        <!--                        app:layout_constraintEnd_toStartOf="@id/iv_user_icon"-->
                        <!--                        app:layout_constraintStart_toStartOf="parent"-->
                        <!--                        app:layout_constraintTop_toTopOf="@id/iv_user_icon">-->

                        <TextView
                            android:id="@+id/tv_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:includeFontPadding="false"
                            android:maxEms="10"
                            android:maxLines="1"
                            android:singleLine="true"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp_18"
                            android:textStyle="bold"
                            app:layout_constraintEnd_toStartOf="@id/iv_gender"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="爱新觉罗.叶赫那拉.玄烨.康熙" />

                        <ImageView
                            android:id="@+id/iv_gender"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingStart="@dimen/dp_8"
                            android:src="@mipmap/icon_sex_none"
                            app:layout_constraintBottom_toBottomOf="@id/tv_name"
                            app:layout_constraintStart_toEndOf="@id/tv_name"
                            app:layout_constraintTop_toTopOf="@id/tv_name" />

                        <TextView
                            android:id="@+id/tv_remark_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:includeFontPadding="false"
                            android:maxEms="10"
                            android:maxLines="1"
                            android:paddingTop="@dimen/dp_5"
                            android:singleLine="true"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp_12"
                            android:visibility="gone"
                            app:layout_constraintStart_toStartOf="@id/tv_name"
                            app:layout_constraintTop_toBottomOf="@id/tv_name"
                            tools:text="nickName"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tv_age"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingTop="@dimen/dp_5"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp_12"
                            android:visibility="gone"
                            app:layout_constraintStart_toStartOf="@id/tv_remark_name"
                            app:layout_constraintTop_toBottomOf="@id/tv_remark_name"
                            app:layout_goneMarginTop="8dp"
                            tools:text="34岁"
                            tools:visibility="visible" />

                        <ImageView
                            android:id="@+id/iv_approval_tag"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingTop="@dimen/dp_5"
                            android:src="@drawable/ic_work_out"
                            android:visibility="gone"
                            app:layout_constraintStart_toStartOf="@id/tv_age"
                            app:layout_constraintTop_toBottomOf="@id/tv_age"
                            tools:visibility="visible" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <TextView
                        android:id="@+id/tv_org_info"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="#5A9DF3"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:maxLines="1"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingBottom="@dimen/dp_10"
                        android:singleLine="true"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp_12"
                        android:visibility="gone"
                        app:layout_constraintBottom_toTopOf="@+id/rl_complain_layout"
                        tools:text=" 共同加入了 企业测试 等2个组织"
                        tools:visibility="visible" />

                    <!--安全提示-->
                    <include
                        android:id="@+id/friend_complain_layout"
                        layout="@layout/item_complain_layout"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_36"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <!--已注销标记-->
                <TextView
                    android:id="@+id/tv_logout_tip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_20"
                    android:paddingBottom="@dimen/dp_20"
                    android:text="该好友已注销账号"
                    android:textColor="#CF0116"
                    android:textSize="@dimen/sp_14"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/cd_top"
                    tools:visibility="gone" />

                <TextView
                    android:id="@+id/tv_logout_delete"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_80"
                    android:layout_marginTop="@dimen/dp_30"
                    android:layout_marginEnd="@dimen/dp_80"
                    android:background="@drawable/rounded_white_5dp"
                    android:gravity="center"
                    android:padding="@dimen/dp_10"
                    android:text="删除"
                    android:textColor="@color/text_black"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/lv_top"
                    tools:visibility="gone" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_friend_detail_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_0_5"
                android:background="@color/white"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/lv_top"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="14dp"
                    android:text="地址"
                    android:textColor="#999999"
                    android:textSize="12sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/et_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:textColor="@color/colorFF323232"
                    android:textSize="15sp"
                    app:layout_constraintStart_toStartOf="@id/address"
                    app:layout_constraintTop_toBottomOf="@id/address" />

                <View
                    android:id="@+id/line_et_address"
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="13dp"
                    android:background="@color/line_grey"
                    app:layout_constraintTop_toBottomOf="@id/et_address" />

                <TextView
                    android:id="@+id/profession"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="12dp"
                    android:text="职位"
                    android:textColor="#999999"
                    android:textSize="12sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/line_et_address" />

                <TextView
                    android:id="@+id/tv_profession"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textColor="@color/colorFF323232"
                    android:textSize="15sp"
                    app:layout_constraintStart_toStartOf="@id/profession"
                    app:layout_constraintTop_toBottomOf="@id/profession" />

                <View
                    android:id="@+id/line_profession"
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="13dp"
                    android:background="@color/line_grey"
                    app:layout_constraintTop_toBottomOf="@id/tv_profession" />

                <TextView
                    android:id="@+id/date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="12dp"
                    android:text="出生日期"
                    android:textColor="#999999"
                    android:textSize="12sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/line_profession" />

                <TextView
                    android:id="@+id/tv_birthday"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textColor="@color/colorFF323232"
                    android:textSize="15sp"
                    app:layout_constraintStart_toStartOf="@id/date"
                    app:layout_constraintTop_toBottomOf="@id/date" />

                <View
                    android:id="@+id/line_birthday"
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="13dp"
                    android:background="@color/line_grey"
                    app:layout_constraintTop_toBottomOf="@id/tv_birthday" />

                <TextView
                    android:id="@+id/phone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="12dp"
                    android:text="电话"
                    android:textColor="#999999"
                    android:textSize="12sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/line_birthday" />

                <TextView
                    android:id="@+id/tv_phone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:textColor="@color/colorFF323232"
                    android:textSize="15sp"
                    app:layout_constraintStart_toStartOf="@id/phone"
                    app:layout_constraintTop_toBottomOf="@id/phone" />

                <View
                    android:id="@+id/line_phone"
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="13dp"
                    android:background="@color/line_grey"
                    app:layout_constraintTop_toBottomOf="@id/tv_phone" />

                <TextView
                    android:id="@+id/email"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="12dp"
                    android:text="邮箱"
                    android:textColor="#999999"
                    android:textSize="12sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/line_phone" />

                <TextView
                    android:id="@+id/tv_email"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:paddingBottom="13dp"
                    android:textColor="@color/colorFF323232"
                    android:textSize="15sp"
                    app:layout_constraintStart_toStartOf="@id/email"
                    app:layout_constraintTop_toBottomOf="@id/email" />

                <View
                    android:id="@+id/empty"
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:background="@color/background"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@id/tv_email" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_org_info_layout"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:background="@color/white"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/cl_friend_detail_layout"
                tools:visibility="visible">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:text="所在部门"
                    android:textColor="#ff333333"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/next"
                    android:layout_width="8dp"
                    android:layout_height="14dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/arraw_right"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_dept_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:textColor="#ff1e87f0"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/next"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_dept_select"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="14dp"
                android:text="点击修改该用户所在部门"
                android:textColor="@color/color999999"
                android:textSize="@dimen/sp_12"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_org_info_layout"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_top_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_45"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/dp_41"
            android:layout_height="match_parent"
            android:paddingStart="@dimen/dp_10"
            android:paddingEnd="@dimen/dp_20"
            android:src="@drawable/back_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_page_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_60"
            android:layout_marginEnd="@dimen/dp_60"
            android:ellipsize="end"
            android:gravity="center"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:singleLine="true"
            android:text="用户信息"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_17"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_right"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingStart="@dimen/dp_20"
            android:paddingEnd="@dimen/dp_10"
            android:src="@drawable/ic_threedot_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--底部4个按键-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_friend_bottom_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background"
        android:paddingStart="@dimen/dp_50"
        android:paddingTop="@dimen/dp_10"
        android:paddingEnd="@dimen/dp_50"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintBottom_toBottomOf="parent">

        <!--发消息-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_send_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@id/cl_add_friend"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/iv_send_msg"
                android:layout_width="40dp"
                android:layout_height="42dp"
                android:background="@drawable/oval_blue_friend_info_bottom_button_bg"
                android:padding="@dimen/dp_10"
                android:src="@drawable/icon_friend_info_send_msg"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_send_invite"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10"
                android:text="发消息"
                android:textColor="@color/colorFF323232"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@id/iv_send_msg"
                app:layout_constraintStart_toStartOf="@id/iv_send_msg"
                app:layout_constraintTop_toBottomOf="@id/iv_send_msg" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <!--添加好友-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_add_friend"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@id/cl_call_layout"
            app:layout_constraintStart_toEndOf="@id/cl_send_layout"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/iv_add_friend"
                android:layout_width="@dimen/dp_42"
                android:layout_height="@dimen/dp_42"
                android:background="@drawable/oval_blue_friend_info_bottom_button_bg"
                android:padding="@dimen/dp_10"
                android:src="@drawable/icon_friend_info_add_friend"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_add_friend"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10"
                android:text="添加好友"
                android:textColor="@color/colorFF323232"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@id/iv_add_friend"
                app:layout_constraintStart_toStartOf="@id/iv_add_friend"
                app:layout_constraintTop_toBottomOf="@id/iv_add_friend" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <!--打电话-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_call_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toStartOf="@id/cl_send_email_layout"
            app:layout_constraintStart_toEndOf="@id/cl_add_friend"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_call_icon"
                android:layout_width="42dp"
                android:layout_height="42dp"
                android:background="@drawable/oval_blue_friend_info_bottom_button_bg"
                android:padding="@dimen/dp_10"
                android:src="@drawable/icon_friend_info_call_phone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_call_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10"
                android:text="打电话"
                android:textColor="@color/colorFF323232"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@id/iv_call_icon"
                app:layout_constraintStart_toStartOf="@id/iv_call_icon"
                app:layout_constraintTop_toBottomOf="@id/iv_call_icon" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <!--发邮件-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_send_email_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/cl_call_layout"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_send_email_icon"
                android:layout_width="42dp"
                android:layout_height="42dp"
                android:background="@drawable/oval_blue_friend_info_bottom_button_bg"
                android:padding="@dimen/dp_10"
                android:src="@drawable/icon_friend_info_send_email"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_send_email_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10"
                android:text="发邮件"
                android:textColor="@color/colorFF323232"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@id/iv_send_email_icon"
                app:layout_constraintStart_toStartOf="@id/iv_send_email_icon"
                app:layout_constraintTop_toBottomOf="@id/iv_send_email_icon" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>