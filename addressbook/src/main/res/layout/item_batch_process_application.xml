<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="82dp"
    android:background="@color/white">

    <ImageView
        android:id="@+id/allSelectIv"
        android:layout_width="41dp"
        android:layout_height="41dp"
        android:padding="12dp"
        android:src="@drawable/selector_batch_process_select"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/iv_user_icon"
        android:layout_width="43dp"
        android:layout_height="43dp"
        android:layout_marginStart="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/allSelectIv"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="7dp"
        android:textColor="#323232"
        android:textSize="@dimen/sp_15"
        app:layout_constraintLeft_toRightOf="@id/iv_user_icon"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/company"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:textColor="#999999"
        android:textSize="@dimen/sp_12"
        app:layout_constraintLeft_toRightOf="@id/iv_user_icon"
        app:layout_constraintTop_toBottomOf="@id/name" />

    <TextView
        android:id="@+id/reason"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="3dp"
        android:maxEms="18"
        android:singleLine="true"
        android:textColor="#999999"
        android:textSize="@dimen/sp_12"
        app:layout_constraintLeft_toRightOf="@id/iv_user_icon"
        app:layout_constraintTop_toBottomOf="@id/company" />
</androidx.constraintlayout.widget.ConstraintLayout>