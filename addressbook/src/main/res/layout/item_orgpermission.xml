<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/background"
    android:paddingBottom="9dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingBottom="20dp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_per_icon"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginStart="13dp"
            android:src="@drawable/icon_super_permission_tag"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_per_name"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_per_name"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_per_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="10dp"
            android:textColor="@color/colorFF323232"
            android:textSize="@dimen/sp_17"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_per_icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginStart="13dp"
            tools:text="考勤管理员" />

        <!--姓名-->

        <TextView
            android:id="@+id/tv_per_user_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="13dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="10dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/colorFF323232"
            android:textSize="@dimen/sp_13"
            app:layout_constraintEnd_toStartOf="@+id/show_more_names_iv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/icons_contain_ll"
            tools:text="张三 李四 王五" />

        <ImageView
            android:id="@+id/show_more_names_iv"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginEnd="16dp"
            android:paddingStart="6dp"
            android:paddingEnd="6dp"
            android:paddingBottom="10dp"
            android:src="@drawable/icon_down_arrow"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tv_per_user_name"
            app:layout_constraintTop_toTopOf="@+id/tv_per_user_name" />

        <ImageView
            android:id="@+id/iv_per_set_icon"
            android:layout_width="19dp"
            android:layout_height="20dp"
            android:layout_marginEnd="14dp"
            android:src="@drawable/icon_set_blue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!--小图标的容器-->
        <LinearLayout
            android:id="@+id/icons_contain_ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="3dp"
            android:layout_marginTop="17dp"
            android:orientation="horizontal"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_per_name"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/iv_attend_per"
                android:layout_width="19dp"
                android:layout_height="19dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/per_attendace"
                android:visibility="gone"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/iv_org_per"
                android:layout_width="19dp"
                android:layout_height="19dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/per_org"
                android:visibility="gone"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/iv_task_per"
                android:layout_width="19dp"
                android:layout_height="19dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/icon_task_manage_small"
                android:visibility="gone"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/iv_approval_per"
                android:layout_width="19dp"
                android:layout_height="19dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/icon_approval_permission"
                android:visibility="gone"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/iv_report_per"
                android:layout_width="19dp"
                android:layout_height="19dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/icon_report_permission"
                android:visibility="gone"
                tools:visibility="visible" />

            <!--数字报告的图标-->
            <ImageView
                android:id="@+id/iv_digital_per"
                android:layout_width="19dp"
                android:layout_height="19dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/icon_number_report_permission"
                android:visibility="gone"
                tools:visibility="visible" />

            <!--全部审批权限的图标-->
            <ImageView
                android:id="@+id/iv_total_approval_per"
                android:layout_width="19dp"
                android:layout_height="19dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/icon_total_approval"
                android:visibility="gone"
                tools:visibility="visible" />

            <!--健康统计权限图标-->
            <ImageView
                android:id="@+id/iv_health_statistics_per"
                android:layout_width="19dp"
                android:layout_height="19dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/icon_health_statistics"
                android:visibility="gone"
                tools:visibility="visible" />

            <!--云文档管理权限图标-->
            <ImageView
                android:id="@+id/iv_cloud_manage_per"
                android:layout_width="19dp"
                android:layout_height="19dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/icon_cloud_manage"
                android:visibility="gone"
                tools:visibility="visible" />

            <!--访客系统，权限小图标-->
            <ImageView
                android:id="@+id/iv_visitor_manage_per"
                android:layout_width="19dp"
                android:layout_height="19dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/icon_visitor_per"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_no_set_per"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="32dp"
            android:layout_marginTop="8dp"
            android:text="暂未分配权限"
            android:textColor="@color/color999999"
            android:textSize="@dimen/sp_13"
            android:visibility="visible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_per_user_name"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>