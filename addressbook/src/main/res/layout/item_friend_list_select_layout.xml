<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:minHeight="@dimen/dp_78">

    <TextView
        android:id="@+id/tv_index"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:background="@color/background"
        android:gravity="center_vertical"
        android:paddingStart="14dp"
        android:textColor="#BDBDBD"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/iv_select"
        android:layout_width="@dimen/dp_17"
        android:layout_height="@dimen/dp_17"
        android:layout_marginStart="@dimen/dp_15"
        android:src="@drawable/selector_circle_select"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_index" />


    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_34"
        android:layout_marginStart="@dimen/dp_15"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_select"
        app:layout_constraintTop_toBottomOf="@id/tv_index" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_15"
        android:layout_marginEnd="@dimen/dp_20"
        android:textColor="@color/colorFF323232"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toBottomOf="@id/tv_index" />

    <View
        android:id="@+id/view_logout"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_78"
        android:background="#63999999"
        android:minHeight="@dimen/dp_78"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#E6E6E6"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>