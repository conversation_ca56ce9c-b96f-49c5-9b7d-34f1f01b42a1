<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:orientation="vertical">

    <TextView
        android:layout_marginTop="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:text="团队信息"
        android:textColor="@color/text_black"
        android:textSize="15sp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="8dp"
        android:text="补充团队部分基本信息，有助于让其他人更了解你的企业"
        android:textSize="12sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/creat2_logo"
            android:layout_width="86dp"
            android:layout_height="86dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="36dp"
            android:background="@drawable/shape_creat"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="8dp"
                android:src="@drawable/icon_camera" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="点击设置"
                android:textColor="@color/text_lowgray"
                android:textSize="12sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="团队LOGO"
                android:textColor="@color/text_lowgray"
                android:textSize="12sp" />
        </LinearLayout>
        <com.joinutech.ddbeslibrary.widget.CircleImageView
            android:id="@+id/iv_logo"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="36dp"
            android:visibility="gone"
            android:layout_width="86dp"
            android:layout_height="86dp" />
        <TextView
            android:id="@+id/creat2_name"
            android:layout_marginTop="14dp"
            android:layout_gravity="center_horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/main_blue"
            android:textSize="17sp" />
        <RelativeLayout
            android:id="@+id/creat2_r1"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="34dp"
            android:paddingLeft="14dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="所属行业"
                android:textColor="@color/text_black"
                android:textSize="16sp" />

            <ImageView
                android:id="@+id/creat2_arr1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="14dp"
                android:src="@drawable/arraw_right" />
            <TextView
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:layout_toLeftOf="@id/creat2_arr1"
                android:textSize="13sp"
                android:textColor="@color/text_lowgray"
                android:id="@+id/creat2_tv1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/line_grey" />

        <RelativeLayout
            android:id="@+id/creat2_r2"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:paddingLeft="14dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="团队类型"
                android:textColor="@color/text_black"
                android:textSize="16sp" />
            <ImageView
                android:id="@+id/creat2_arr2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="14dp"
                android:src="@drawable/arraw_right" />
            <TextView
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:layout_toLeftOf="@id/creat2_arr2"
                android:textSize="13sp"
                android:textColor="@color/text_lowgray"
                android:id="@+id/creat2_tv2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/line_grey" />

        <RelativeLayout
            android:id="@+id/creat2_r3"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:paddingLeft="14dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="团队规模"
                android:textColor="@color/text_black"
                android:textSize="16sp" />

            <ImageView
                android:id="@+id/creat2_arr3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="14dp"
                android:src="@drawable/arraw_right" />
            <TextView
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:layout_toLeftOf="@id/creat2_arr3"
                android:textSize="13sp"
                android:textColor="@color/text_lowgray"
                android:id="@+id/creat2_tv3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </RelativeLayout>

        <TextView
            android:id="@+id/creat2_next"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginLeft="40dp"
            android:layout_marginTop="62dp"
            android:layout_marginRight="40dp"
            android:background="@drawable/shape_tv_corner3"
            android:gravity="center"
            android:text="跳过"
            android:textColor="@color/white"
            android:textSize="14sp" />

    </LinearLayout>
</LinearLayout>
