<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <!--todo 只有List布局和空数据显示，可以合并布局-->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/contentListRv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent" />

    <!--默认空白页面-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:ev_content="未找到符合条件的结果"
        app:ev_icon="@drawable/ic_empty_search"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>