<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:paddingTop="@dimen/dp_10">

    <!--权限设置-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rl_permissionSet"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_46"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/permissionSetIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:src="@drawable/icon_permission"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:text="权限设置"
            android:textColor="@color/text_black"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/permissionSetIcon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="14dp"
            android:src="@drawable/arraw_right"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--偏好设置-->
    <RelativeLayout
        android:id="@+id/rl_perference"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_46"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@color/white"
        app:layout_constraintBottom_toTopOf="@id/cl_change_creator"
        app:layout_constraintTop_toBottomOf="@id/rl_permissionSet">

        <ImageView
            android:id="@+id/iv1_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="14dp"
            android:layout_marginRight="14dp"
            android:src="@drawable/icon_organsetting" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/iv1_more"
            android:text="偏好设置"
            android:textColor="@color/text_black"
            android:textSize="16sp" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="14dp"
            android:src="@drawable/arraw_right" />
    </RelativeLayout>

    <!--转移团队创建者-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_change_creator"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_46"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@id/rl_perference">

        <ImageView
            android:id="@+id/ccc_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:src="@mipmap/icon_change_creator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:text="转移团队创建者"
            android:textColor="@color/text_black"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/ccc_icon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="14dp"
            android:src="@drawable/arraw_right"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>