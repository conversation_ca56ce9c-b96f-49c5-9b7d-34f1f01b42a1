<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_data_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <include
            android:id="@+id/search_bar"
            layout="@layout/search_include_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_have_data_layout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/search_bar">

            <LinearLayout
                android:id="@+id/ll_select_all"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:gravity="center_vertical|start"
                android:orientation="horizontal"
                android:paddingStart="@dimen/dp_15"
                android:paddingTop="@dimen/dp_8"
                android:paddingEnd="@dimen/dp_15"
                android:paddingBottom="@dimen/dp_5"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/iv_select_list"
                    android:layout_width="@dimen/dp_17"
                    android:layout_height="@dimen/dp_17"
                    android:src="@drawable/selector_circle_select" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_10"
                    android:text="全选"
                    android:textColor="@color/colorFF323232"
                    android:textSize="@dimen/sp_13" />
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_list"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintBottom_toTopOf="@id/two_btns_contain_rl"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ll_select_all" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/two_btns_contain_rl"
                android:layout_width="match_parent"
                android:layout_height="67dp"
                android:background="@color/white"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent">

                <TextView
                    android:id="@+id/tv_bottom_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="11dp"
                    android:layout_marginEnd="10dp"
                    android:textColor="@color/color999999"
                    android:textSize="@dimen/sp_12"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_person_list"
                    android:layout_width="0dp"
                    android:layout_height="24dp"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="11dp"
                    android:layout_marginEnd="14dp"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_bottom_title" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.joinutech.ddbeslibrary.widget.wavesidebar.WaveSideBarView
                android:id="@+id/main_side_bar"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:padding="8dp"
                app:layout_constraintBottom_toBottomOf="@id/rv_list"
                app:layout_constraintEnd_toEndOf="@id/rv_list"
                app:layout_constraintStart_toStartOf="@id/rv_list"
                app:layout_constraintTop_toBottomOf="@id/ll_select_all"
                app:sidebar_lazy_respond="false"
                app:sidebar_text_color="@color/colorBDBDBD"
                app:sidebar_text_select_color="@color/main_blue"
                app:sidebar_text_size="@dimen/sp_12" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--默认空白页面-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:ev_content="暂无符合条件好友"
        app:ev_icon="@drawable/ic_empty_search"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>