<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/tb_top_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="@dimen/dp_15"
        app:layout_constraintTop_toTopOf="parent">

        <com.joinutech.ddbeslibrary.widget.CircleImageView
            android:id="@+id/iv_company_logo"
            android:layout_width="76dp"
            android:layout_height="76dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_company_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_12"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/colorFF323232"
            android:textSize="@dimen/sp_17"
            app:layout_constraintBottom_toTopOf="@id/tv_cooper_kind"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_company_logo"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/tv_cooper_kind"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@drawable/shape_cooperation_application_1a2479ed_2"
            android:padding="@dimen/dp_5"
            android:textColor="@color/main_blue"
            android:textSize="@dimen/sp_11"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_company_name"
            app:layout_constraintTop_toBottomOf="@id/tv_company_name" />

        <TextView
            android:id="@+id/tv_cooper_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            android:background="@drawable/shape_cooperation_application_1a2479ed_2"
            android:padding="@dimen/dp_5"
            android:textColor="@color/main_blue"
            android:textSize="@dimen/sp_11"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_cooper_kind"
            app:layout_constraintStart_toEndOf="@id/tv_cooper_kind" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_content_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@color/white"
        android:padding="@dimen/dp_17"
        app:layout_constraintTop_toBottomOf="@id/tb_top_layout">

        <TextView
            android:id="@+id/hintTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="邀请你成为本团队的外部协作人员"
            android:textColor="@color/colorFF323232"
            android:textSize="@dimen/sp_15"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_12"
            android:maxLines="2"
            android:text="确认之后，即可成为本团队的外部协作人员参与审批、任务等工作协作"
            android:textColor="@color/color999999"
            android:textSize="@dimen/sp_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/hintTitle" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_deal_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="29dp"
        android:visibility="visible"
        app:layout_constraintTop_toBottomOf="@id/cl_content_layout"
        tools:visibility="visible">

        <TextView
            android:id="@+id/agree"
            android:layout_width="312dp"
            android:layout_height="42dp"
            android:background="@drawable/rounded_blue_agree_bg"
            android:gravity="center"
            android:text="确认"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/ignore"
            android:layout_width="312dp"
            android:layout_height="42dp"
            android:layout_marginTop="14dp"
            android:background="@drawable/roundedcircle_blue_ignore_bg"
            android:gravity="center"
            android:text="忽略"
            android:textColor="@color/main_blue"
            android:textSize="@dimen/sp_16"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/agree" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_status_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="43dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/cl_content_layout">

        <TextView
            android:id="@+id/statusText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="已同意"
            android:textColor="#999999"
            android:textSize="@dimen/sp_12"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:textColor="#8C9EB0"
            android:textSize="@dimen/sp_12"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/statusText" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>