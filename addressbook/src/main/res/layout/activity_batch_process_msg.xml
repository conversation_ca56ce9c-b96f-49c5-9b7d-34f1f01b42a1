<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <RelativeLayout
        android:id="@+id/haveUndoMsgRl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="24dp"
            android:background="#FDFCED">

            <TextView
                android:id="@+id/topHint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="同一次批量办理多个人员入职时，将会入职到同一个部门"
                android:textColor="#ffff7800"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/allSelectLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="29dp"
            android:orientation="horizontal">

            <View
                android:id="@+id/allSelectIv"
                android:layout_width="17dp"
                android:layout_height="17dp"
                android:layout_marginStart="12dp"
                android:background="@drawable/selector_batch_process_select" />

            <TextView
                android:id="@+id/allSelectTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="11dp"
                android:layout_toRightOf="@id/allSelectIv"
                android:text="全选"
                android:textColor="#ff323232"
                android:textSize="@dimen/sp_13" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/two_btns_contain_rl"
            android:layout_width="match_parent"
            android:layout_height="47dp"
            android:layout_alignParentBottom="true"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/ignore"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@color/white"
                android:gravity="center"
                android:text="忽略"
                android:textColor="#ff999999"
                android:textSize="@dimen/sp_15" />

            <TextView
                android:id="@+id/agree"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@color/main_blue"
                android:gravity="center"
                android:text="同意"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_15" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/batchProcessRv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/two_btns_contain_rl"
            android:layout_below="@id/allSelectLayout"
            android:layout_marginTop="5dp" />
    </RelativeLayout>

    <!--默认空白页面-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:ev_content="暂无待处理的团队申请"
        app:ev_icon="@drawable/ic_empty_friend" />

</FrameLayout>