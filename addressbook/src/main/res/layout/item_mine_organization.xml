<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_org_item_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:visibility="visible">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_166"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent">

        <com.joinutech.ddbeslibrary.widget.CircleImageView
            android:id="@+id/iv_logo"
            android:layout_width="38dp"
            android:layout_height="38dp"
            android:layout_marginStart="14dp"
            android:layout_marginTop="14dp"
            android:src="@drawable/default_heading"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:singleLine="true"
            android:text="加优科技有限公司"
            android:textColor="@color/text_black"
            android:textSize="@dimen/sp_17"
            app:layout_constraintBottom_toTopOf="@id/tv_desc"
            app:layout_constraintEnd_toStartOf="@id/ll_visit_layout"
            app:layout_constraintStart_toEndOf="@id/iv_logo"
            app:layout_constraintTop_toTopOf="@id/iv_logo" />

        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="10dp"
            android:ellipsize="end"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:singleLine="true"
            android:text="主要团队"
            android:textColor="#ffaaaaaa"
            android:textSize="@dimen/sp_13"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/iv_logo"
            app:layout_constraintEnd_toStartOf="@id/ll_visit_layout"
            app:layout_constraintStart_toStartOf="@id/tv_name"
            app:layout_constraintTop_toBottomOf="@id/tv_name" />

        <LinearLayout
            android:id="@+id/ll_visit_layout"
            android:layout_width="65dp"
            android:layout_height="25dp"
            android:layout_centerVertical="true"
            android:layout_marginTop="21dp"
            android:layout_marginEnd="14dp"
            android:background="@drawable/shape_invite"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="4dp"
                android:src="@drawable/icon5_addr" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="邀请"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_12" />

            <!--            <TextView-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="match_parent"-->
            <!--                android:background="@drawable/shape_invite"-->
            <!--                android:drawableLeft="@drawable/icon5_addr"-->
            <!--                android:drawablePadding="@dimen/dp_5"-->
            <!--                android:gravity="center_vertical"-->
            <!--                android:paddingStart="@dimen/dp_10"-->
            <!--                android:paddingEnd="@dimen/dp_10"-->
            <!--                android:text="邀请"-->
            <!--                android:textColor="@color/white" />-->
        </LinearLayout>

        <View
            android:id="@+id/line"
            android:layout_width="match_parent"
            android:layout_height="0.2dp"
            android:layout_marginTop="14dp"
            android:background="@color/line_grey"
            app:layout_constraintTop_toBottomOf="@id/iv_logo" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_member_watch"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="23dp"
            android:layout_marginEnd="@dimen/dp_5"
            app:layout_constraintEnd_toStartOf="@id/cl_watch_info"
            app:layout_constraintHorizontal_weight="4"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line">

            <ImageView
                android:id="@+id/iv_member_watch"
                android:layout_width="23dp"
                android:layout_height="23dp"
                android:src="@drawable/icon4_addr"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="团队架构"
                android:textColor="@color/text_black"
                android:textSize="@dimen/sp_15"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_member_watch" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_watch_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="23dp"
            android:layout_marginEnd="@dimen/dp_5"
            app:layout_constraintEnd_toStartOf="@id/cl_external_layout"
            app:layout_constraintHorizontal_weight="4"
            app:layout_constraintStart_toEndOf="@id/cl_member_watch"
            app:layout_constraintTop_toBottomOf="@id/line">

            <ImageView
                android:id="@+id/iv_watch_icon"
                android:layout_width="23dp"
                android:layout_height="23dp"
                android:src="@drawable/icon3_addr"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="团队资料"
                android:textColor="@color/text_black"
                android:textSize="@dimen/sp_15"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_watch_icon" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_external_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="23dp"
            android:layout_marginEnd="@dimen/dp_5"
            app:layout_constraintEnd_toStartOf="@id/cl_more_layout"
            app:layout_constraintHorizontal_weight="5.2"
            app:layout_constraintStart_toEndOf="@id/cl_watch_info"
            app:layout_constraintTop_toBottomOf="@id/line">

            <ImageView
                android:id="@+id/iv_external_icon"
                android:layout_width="23dp"
                android:layout_height="23dp"
                android:src="@drawable/icon_addr_external_contact"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:singleLine="true"
                android:text="外部协作人员"
                android:textColor="@color/text_black"
                android:textSize="@dimen/sp_15"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_external_icon" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_more_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="23dp"
            android:layout_marginEnd="@dimen/dp_15"
            android:visibility="invisible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="2"
            app:layout_constraintStart_toEndOf="@id/cl_external_layout"
            app:layout_constraintTop_toBottomOf="@id/line">

            <ImageView
                android:id="@+id/iv_more_icon"
                android:layout_width="23dp"
                android:layout_height="23dp"
                android:src="@drawable/icon2_addr"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/textView"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="更多"
                android:textColor="@color/text_black"
                android:textSize="@dimen/sp_15"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_more_icon" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/pd_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_10" />
</LinearLayout>