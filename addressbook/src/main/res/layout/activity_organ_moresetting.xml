<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:background="@color/white">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="14dp"
            android:text="团队是否允许申请加入"
            android:textColor="@color/text_black"
            android:textSize="16sp" />

        <com.kyleduo.switchbutton.SwitchButton
            android:id="@+id/organ_sw1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="16sp"
            android:checked="true"
            app:kswAnimationDuration="300"
            app:kswBackDrawable="@drawable/ios_back_drawable"
            app:kswThumbDrawable="@drawable/ios_thumb_selector"
            app:kswThumbMarginBottom="-8dp"
            app:kswThumbMarginLeft="-5dp"
            app:kswThumbMarginRight="-5dp"
            app:kswThumbMarginTop="-2.5dp"
            app:kswThumbRangeRatio="1.4" />
    </RelativeLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="6dp"
        android:text="关闭后，团队外的用户无法申请加入本团队"
        android:textColor="@color/text_lowgray"
        android:textSize="12sp" />

    <RelativeLayout
        android:id="@+id/enableVisitLayout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:background="@color/white">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="14dp"
            android:text="是否允许普通员工邀请加入团队"
            android:textColor="@color/text_black"
            android:textSize="16sp" />

        <com.kyleduo.switchbutton.SwitchButton
            android:id="@+id/organ_sw2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="16sp"
            android:checked="true"
            app:kswAnimationDuration="300"
            app:kswBackDrawable="@drawable/ios_back_drawable"
            app:kswThumbDrawable="@drawable/ios_thumb_selector"
            app:kswThumbMarginBottom="-8dp"
            app:kswThumbMarginLeft="-5dp"
            app:kswThumbMarginRight="-5dp"
            app:kswThumbMarginTop="-2.5dp"
            app:kswThumbRangeRatio="1.4"  />
    </RelativeLayout>

    <TextView
        android:id="@+id/visitHintText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="6dp"
        android:text="关闭后，普通员工邀请功能将不可用"
        android:textColor="@color/text_lowgray"
        android:textSize="12sp" />
</LinearLayout>