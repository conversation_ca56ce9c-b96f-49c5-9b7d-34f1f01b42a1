<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_complain_layout"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_36"
    android:visibility="gone"
    tools:visibility="visible"
    android:background="@color/color_complain">

    <TextView
        android:id="@+id/tv_complain_text"
        android:layout_width="wrap_content"
        android:layout_height="36dp"
        android:layout_centerHorizontal="true"
        android:gravity="center_vertical"
        android:textColor="@color/red"
        android:textSize="14sp"
        android:text="对方曾被他人投诉，谨防诈骗。" />

<!--    <ImageView-->
<!--        android:id="@+id/iv_close_complain_btn"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_alignTop="@+id/tv_complain_text"-->
<!--        android:layout_alignBottom="@+id/tv_complain_text"-->
<!--        android:layout_alignParentEnd="true"-->
<!--        android:layout_marginRight="20dp"-->
<!--        android:src="@drawable/ic_close_fork" />-->
</RelativeLayout>