<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/tb_top_layout"
            android:layout_width="match_parent"
            android:layout_height="110dp"
            android:background="@color/white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.joinutech.ddbeslibrary.widget.CircleImageView
                android:id="@+id/iv_header_image"
                android:layout_width="57dp"
                android:layout_height="57dp"
                android:layout_marginStart="19dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_no_register"
                android:layout_width="57dp"
                android:layout_height="57dp"
                android:layout_marginStart="19dp"
                android:background="@drawable/oval_main_blue"
                android:gravity="center"
                android:text="未注册"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_11"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_no_active"
                android:layout_width="57dp"
                android:layout_height="57dp"
                android:layout_marginStart="19dp"
                android:background="@drawable/oval_translucent_mask"
                android:gravity="center"
                android:text="未激活"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_11"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="14dp"
                app:layout_constraintBottom_toBottomOf="@id/iv_header_image"
                app:layout_constraintStart_toEndOf="@id/iv_header_image"
                app:layout_constraintTop_toTopOf="@id/iv_header_image">

                <TextView
                    android:id="@+id/name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/colorFF333333"
                    android:textSize="@dimen/sp_16"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:textColor="@color/color999999"
                    android:textSize="@dimen/sp_12"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/name" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_level_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="19dp"
                android:paddingStart="2dp"
                android:paddingTop="4dp"
                android:paddingEnd="7dp"
                android:paddingBottom="4dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/iv_level_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_level_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_5"
                    android:textSize="@dimen/sp_12"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/iv_level_icon"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_name_layout"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_marginTop="10dp"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/tb_top_layout">

            <TextView
                android:id="@+id/tv_name_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="18dp"
                android:text="用户名"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="18dp"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_phone_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_52"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/cl_name_layout">

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_0_5"
                android:background="@color/line_grey"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_phone_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_18"
                android:text="手机号"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_phone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_18"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_email_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_52"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/cl_phone_layout">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/line_grey"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_email_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="18dp"
                android:text="邮箱"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_email"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="18dp"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_remark_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_52"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/cl_email_layout">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/line_grey"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_remark_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_18"
                android:text="备注"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_remark"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_18"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_send_layout"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_marginTop="10dp"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/cl_remark_layout"
            app:layout_goneMarginTop="0dp">

            <TextView
                android:id="@+id/tv_send_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="18dp"
                android:text="发送激活邀请给该用户"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_send_invite"
                android:layout_width="95dp"
                android:layout_height="33dp"
                android:layout_marginEnd="19dp"
                android:background="@drawable/shape_send_msg_button_rounded"
                android:gravity="center"
                android:text="发送激活邀请"
                android:textColor="@color/main_blue"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_call_layout"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_marginTop="10dp"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/cl_send_layout">


            <ImageView
                android:id="@+id/iv_call_icon"
                android:layout_width="21dp"
                android:layout_height="21dp"
                android:src="@drawable/icon_external_contact_call"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_call_name"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_call_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:text="呼叫"
                android:textColor="#ff666666"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_call_icon"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_msg_layout"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/cl_call_layout">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/line_grey"
                app:layout_constraintTop_toTopOf="parent" />


            <ImageView
                android:id="@+id/iv_msg_icon"
                android:layout_width="21dp"
                android:layout_height="21dp"
                android:src="@drawable/icon_external_contact_msg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_msg_title"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_msg_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:text="发消息"
                android:textColor="@color/color666666"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_msg_icon"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_14"
            android:textColor="@color/colorFFAAAAAA"
            android:textSize="@dimen/sp_12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cl_msg_layout" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>