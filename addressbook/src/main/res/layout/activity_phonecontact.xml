<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/noPermissionLayout"
        android:layout_width="match_parent"
        android:layout_height="261dp"
        android:visibility="visible"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/infoIv"
            android:layout_width="67dp"
            android:layout_height="67dp"
            android:layout_marginTop="60dp"
            android:src="@drawable/icon_phoneinfo"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/topText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:text="未能获取到通讯录用户信息"
            android:textColor="#333333"
            android:textSize="17sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/infoIv" />

        <TextView
            android:id="@+id/tv_dept_select"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:text="请检查你是否为担当开启了通讯录使用权限"
            android:textColor="@color/color999999"
            android:textSize="@dimen/sp_12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/topText" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/friendListLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/main_recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.joinutech.ddbeslibrary.widget.wavesidebar.WaveSideBarView
            android:id="@+id/main_side_bar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="8dp"
            app:sidebar_lazy_respond="false"
            app:sidebar_text_color="#BDBDBD" />
    </FrameLayout>

    <!--默认空白页面-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:ev_content="暂无联系人"
        app:ev_icon="@drawable/ic_empty_friend"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>