<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!--头部 title bar-->
    <include layout="@layout/layout_common_toolbar_left" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:overScrollMode="never"
        android:scrollbars="none">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!--搜索栏-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_search_friend"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:background="@color/white"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/search"
                    android:layout_width="match_parent"
                    android:layout_height="29dp"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="14dp"
                    android:background="@drawable/rounded_membersearch_edit_bg"
                    android:drawableLeft="@drawable/iconsmallsearch"
                    android:drawablePadding="5dp"
                    android:gravity="center_vertical"
                    android:paddingStart="10dp"
                    android:text="搜索好友/群组"
                    android:textColor="#CBCBCB"
                    android:textSize="@dimen/sp_12"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!--好友-->
            <RelativeLayout
                android:id="@+id/rl_friend_layout"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="@dimen/dp_10"
                android:background="@color/white"
                app:layout_constraintTop_toBottomOf="@id/cl_search_friend">

                <ImageView
                    android:id="@+id/iv_friend_icon"
                    android:layout_width="38dp"
                    android:layout_height="38dp"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="14dp"
                    android:src="@drawable/icon_address_friend" />

                <ImageView
                    android:id="@+id/iv_friend_arrow"
                    android:layout_width="34dp"
                    android:layout_height="12dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:paddingStart="14dp"
                    android:paddingEnd="14dp"
                    android:src="@drawable/icon_black_arrawnext" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="12dp"
                    android:layout_toRightOf="@id/iv_friend_icon"
                    android:text="担当好友"
                    android:textColor="#ff333333"
                    android:textSize="@dimen/sp_17" />
            </RelativeLayout>

            <!--群组-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_group_layout"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="@dimen/dp_10"
                android:background="@color/white"
                app:layout_constraintTop_toBottomOf="@id/rl_friend_layout">

                <ImageView
                    android:id="@+id/iv_group_icon"
                    android:layout_width="39dp"
                    android:layout_height="39dp"
                    android:layout_marginStart="14dp"
                    android:src="@drawable/icon_address_group"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:text="我的群组"
                    android:textColor="@color/colorFF323232"
                    android:textSize="@dimen/sp_17"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/iv_group_icon"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:layout_width="7dp"
                    android:layout_height="12dp"
                    android:layout_marginEnd="14dp"
                    android:src="@drawable/icon_black_arrawnext"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!--我的团队-->
            <RelativeLayout
                android:id="@+id/rl_main_org_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10"
                app:layout_constraintTop_toBottomOf="@id/cl_group_layout">

                <TextView
                    android:id="@+id/tv_org_count"
                    android:layout_width="match_parent"
                    android:layout_height="24dp"
                    android:layout_marginBottom="8dp"
                    android:gravity="center"
                    android:text="我的团队（）"
                    android:textColor="#ff666666"
                    android:textSize="@dimen/sp_13"
                    android:visibility="gone"
                    app:layout_constraintTop_toTopOf="parent" />

                <!--未加入团队-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_non"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:layout_below="@id/tv_org_count"
                    android:background="@color/white"
                    android:visibility="gone"
                    tools:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="38dp"
                        android:text="你还未创建或加入团队"
                        android:textColor="@color/text_black"
                        android:textSize="16sp"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_creat"
                        android:layout_width="0dp"
                        android:layout_height="36dp"
                        android:layout_marginLeft="34dp"
                        android:layout_marginRight="24dp"
                        android:layout_marginBottom="50dp"
                        android:background="@drawable/shape_tv_corner3"
                        android:gravity="center"
                        android:text="创建团队"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHorizontal_weight="1"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="@id/tv_add" />

                    <TextView
                        android:id="@+id/tv_add"
                        android:layout_width="0dp"
                        android:layout_height="36dp"
                        android:layout_marginRight="34dp"
                        android:layout_marginBottom="50dp"
                        android:background="@drawable/shape_stroke_2479ed"
                        android:gravity="center"
                        android:text="加入团队"
                        android:textColor="@color/color2479ED"
                        android:textSize="14sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHorizontal_weight="1"
                        app:layout_constraintLeft_toRightOf="@id/tv_creat"
                        app:layout_constraintRight_toRightOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <!--已加入团队-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_has"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/tv_org_count"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <!--已加入团队中的 默认团队-->
                    <include
                        android:id="@+id/include_addr"
                        layout="@layout/item_mine_organization"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/line1"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_10"
                        android:background="@color/background"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/include_addr" />

                    <!--我加入的其他团队-->
                    <RelativeLayout
                        android:id="@+id/other_layout"
                        android:layout_width="match_parent"
                        android:layout_height="60dp"
                        android:background="@color/white"
                        app:layout_constraintTop_toBottomOf="@id/line1">

                        <ImageView
                            android:id="@+id/img_other"
                            android:layout_width="38dp"
                            android:layout_height="38dp"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="14dp"
                            android:src="@drawable/icon6_addr" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="14dp"
                            android:src="@drawable/arraw_right" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:paddingStart="@dimen/dp_12"
                            android:layout_toRightOf="@id/img_other"
                            android:text="我的其他团队"
                            android:textColor="@color/text_black"
                            android:textSize="@dimen/sp_16" />

                    </RelativeLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>
            </RelativeLayout>

            <!--我的合作团队-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_cooperation_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/rl_main_org_layout"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tv_cooperation_title"
                    android:layout_width="match_parent"
                    android:layout_height="28dp"
                    android:layout_marginStart="15dp"
                    android:gravity="center_vertical"
                    android:text="我的合作团队"
                    android:textColor="@color/color999999"
                    android:textSize="@dimen/sp_13"
                    android:visibility="visible"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_cooperation_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    app:layout_constraintTop_toBottomOf="@id/tv_cooperation_title" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</LinearLayout>