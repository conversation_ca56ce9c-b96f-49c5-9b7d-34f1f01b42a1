<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_top_search"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/search"
            android:layout_width="match_parent"
            android:layout_height="29dp"
            android:layout_marginStart="14dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="14dp"
            android:background="@drawable/rounded_membersearch_edit_bg"
            android:drawableLeft="@drawable/iconsmallsearch"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:paddingStart="10dp"
            android:text="搜索群组"
            android:textColor="#CBCBCB"
            android:textSize="@dimen/sp_12"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_group"
        android:layout_width="match_parent"
        android:layout_height="33dp"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@id/cl_top_search">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_join_layout"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingStart="14dp"
            android:paddingEnd="12dp"
            app:layout_constraintStart_toStartOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                app:layout_constraintStart_toStartOf="parent">

                <TextView
                    android:id="@+id/tv_join_private"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/main_blue"
                    android:textSize="@dimen/sp_13"
                    android:text="私有群组"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/line_join_private"
                    android:layout_width="27dp"
                    android:layout_height="1dp"
                    android:background="@color/main_blue"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_un_join_layout"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            app:layout_constraintStart_toEndOf="@id/cl_join_layout">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                app:layout_constraintStart_toStartOf="parent">

                <TextView
                    android:id="@+id/tv_un_join"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/color999999"
                    android:textSize="@dimen/sp_13"
                    android:text="团队群组"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/line_un_join"
                    android:layout_width="27dp"
                    android:layout_height="1dp"
                    android:background="@color/main_blue"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/vp_group_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_group" />
</androidx.constraintlayout.widget.ConstraintLayout>