<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_sticky_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/dp_15"
        android:paddingTop="@dimen/dp_6"
        android:paddingEnd="@dimen/dp_15"
        android:paddingBottom="@dimen/dp_6"
        android:textColor="@color/color666666"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        tools:text="组织架构"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white">

        <com.joinutech.ddbeslibrary.widget.CircleImageView
            android:id="@+id/iv_company_logo"
            android:layout_width="@dimen/dp_35"
            android:layout_height="@dimen/dp_35"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginBottom="@dimen/dp_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_placeholder" />

        <TextView
            android:id="@+id/tv_company_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxEms="10"
            android:singleLine="true"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_arrow"
            app:layout_constraintStart_toEndOf="@id/iv_company_logo"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="加优科技有限公司加优科技有限公司加优科技有限公司加优科技有限公司加优科技有限公司加优科技有限公司" />

        <ImageView
            android:id="@+id/iv_arrow"
            android:layout_width="@dimen/dp_14"
            android:layout_height="@dimen/dp_14"
            android:layout_marginEnd="@dimen/dp_15"
            android:src="@drawable/ic_arrow_right_black"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_2"
            android:background="@color/line_grey"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>