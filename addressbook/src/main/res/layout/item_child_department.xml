<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="48dp">

    <TextView
        android:id="@+id/itemName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="17dp"
        android:text="研发部 (12)"
        android:textColor="#ff333333"
        android:textSize="@dimen/sp_14"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/next"
        android:layout_width="8dp"
        android:layout_height="14dp"
        android:layout_marginTop="17dp"
        android:layout_marginEnd="16dp"
        android:src="@drawable/arraw_right"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#E6E6E6"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>