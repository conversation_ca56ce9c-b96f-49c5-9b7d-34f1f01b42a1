<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingLeft="40dp"
    android:paddingRight="40dp">

    <ImageView
        android:id="@+id/logo_disband"
        android:layout_width="86dp"
        android:layout_height="86dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="20dp"
        android:src="@drawable/icon6_addr" />

    <TextView
        android:id="@+id/name_disband"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="14dp"
        android:layout_marginBottom="33dp"
        android:textColor="@color/text_black"
        android:textSize="17sp" />

    <TextView
        android:id="@+id/phone_disband"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_lowgray"
        android:textSize="14sp" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="20dp">

        <EditText
            android:id="@+id/et_disband"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:background="@null"
            android:hint="请输入验证码"
            android:inputType="number"
            android:maxEms="6"
            android:minWidth="200dp"
            android:singleLine="true"
            android:textColor="@color/text_black"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/code_disband"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:text="获取验证码"
            android:textColor="@color/main_blue" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="20dp"
            android:layout_centerVertical="true"
            android:layout_marginRight="40dp"
            android:layout_toLeftOf="@id/code_disband"
            android:background="@color/line_grey" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:background="@color/line_grey" />
    </RelativeLayout>

    <TextView
        android:id="@+id/button_disband"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginTop="48dp"
        android:background="@drawable/shape_tv_corner3"
        android:gravity="center"
        android:text="确认解散"
        android:textColor="@color/white"
        android:textSize="14sp" />
</LinearLayout>