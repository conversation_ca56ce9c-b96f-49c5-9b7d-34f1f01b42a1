<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".view.tcpimpages.SearchFriendToTranslateActivity2">

    <LinearLayout
        android:id="@+id/dataLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <include
            android:id="@+id/search_inclue_layout"
            layout="@layout/search_include_layout"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/session_recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible" />
    </LinearLayout>

    <!--默认空白页面-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:ev_content="未找到符合条件的担当好友"
        app:ev_icon="@drawable/ic_empty_friend" />

</androidx.constraintlayout.widget.ConstraintLayout>