<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/search_bar"
        layout="@layout/search_include_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_conent_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search_bar">

        <net.lucode.hackware.magicindicator.MagicIndicator
            android:id="@+id/tb_top_layout"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/white"
            app:layout_constraintBottom_toTopOf="@id/vp_group_pager"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp_group_pager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tb_top_layout"
            app:layout_constraintVertical_weight="1" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>