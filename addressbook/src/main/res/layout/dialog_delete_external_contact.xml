<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="272dp"
        android:layout_height="155dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@drawable/shape_white_rounded_dialog"
        app:layout_constraintTop_toTopOf="parent"
        >
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:text="确认删除这个外部协作人员吗？"
            android:textColor="#ff393939"
            android:textSize="@dimen/sp_17"
            android:layout_marginTop="28dp"
            android:id="@+id/title"
            />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="删除之后，该用户不可再参与\n本团队的工作执行环节"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="16dp"
            android:textColor="#ff999999"
            android:textSize="13sp"
            />
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="2dp"
            >
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/line_grey"
                app:layout_constraintTop_toTopOf="parent"
                />
            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                android:text="取消"
                android:textColor="#ff999999"
                android:textSize="@dimen/sp_15"
                android:gravity="center"
                android:id="@+id/cancel"
                app:layout_constraintEnd_toStartOf="@id/delete"
                />
            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintEnd_toEndOf="parent"
                android:text="删除"
                android:id="@+id/delete"
                app:layout_constraintStart_toEndOf="@id/cancel"
                android:textColor="#ffff3300"
                android:textSize="@dimen/sp_15"
                android:gravity="center"
                />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>