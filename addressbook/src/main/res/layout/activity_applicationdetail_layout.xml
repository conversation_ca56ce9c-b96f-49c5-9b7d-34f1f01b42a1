<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <!--安全提示-->
    <include
        android:id="@+id/rl_complain_layout"
        layout="@layout/item_complain_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_36"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/info"
        android:layout_width="match_parent"
        android:layout_height="132dp"
        android:background="@color/white"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rl_complain_layout">

        <com.joinutech.ddbeslibrary.widget.CircleImageView
            android:id="@+id/iv_user_icon"
            android:layout_width="76dp"
            android:layout_height="76dp"
            android:layout_marginStart="24dp"
            android:src="@drawable/default_heading"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="19dp"
            android:layout_marginTop="45dp"
            android:ellipsize="end"
            android:maxEms="9"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="#323232"
            android:textSize="17sp"
            app:layout_constraintStart_toEndOf="@id/iv_user_icon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/gender"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginStart="@dimen/dp_15"
            app:layout_constraintBottom_toBottomOf="@id/name"
            app:layout_constraintEnd_toStartOf="@id/age"
            app:layout_constraintStart_toEndOf="@id/name"
            app:layout_constraintTop_toTopOf="@id/name"
            tools:src="@drawable/icon_personinfo_man_small" />

        <TextView
            android:id="@+id/age"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_13"
            android:textColor="#323232"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="@id/gender"
            app:layout_constraintStart_toEndOf="@id/gender"
            app:layout_constraintTop_toTopOf="@id/gender"
            tools:text="26岁" />

        <TextView
            android:id="@+id/company"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="19dp"
            android:layout_marginTop="10dp"
            android:textColor="#323232"
            android:textSize="13sp"
            app:layout_constraintLeft_toRightOf="@id/iv_user_icon"
            app:layout_constraintTop_toBottomOf="@id/name" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <TextView
        android:id="@+id/content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingStart="@dimen/dp_14"
        android:paddingTop="@dimen/dp_6"
        android:paddingBottom="@dimen/dp_6"
        android:text="验证申请"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/info" />

    <TextView
        android:id="@+id/reasonText"
        android:layout_width="match_parent"
        android:layout_height="96dp"
        android:background="@color/white"
        android:paddingStart="24dp"
        android:paddingTop="19dp"
        android:paddingEnd="24dp"
        android:textColor="@color/colorFF323232"
        android:textSize="@dimen/sp_14"
        app:layout_constraintTop_toBottomOf="@id/content" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_deal_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_30"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/reasonText"
        tools:visibility="visible">

        <TextView
            android:id="@+id/agree"
            android:layout_width="312dp"
            android:layout_height="42dp"
            android:background="@drawable/rounded_blue_agree_bg"
            android:gravity="center"
            android:text="同意"
            android:textColor="@color/white"
            android:textSize="15sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/ignore"
            android:layout_width="312dp"
            android:layout_height="42dp"
            android:layout_marginTop="14dp"
            android:background="@drawable/roundedcircle_blue_ignore_bg"
            android:gravity="center"
            android:text="忽略"
            android:textColor="#2479ED"
            android:textSize="15sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/agree" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_status_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="43dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/reasonText">

        <TextView
            android:id="@+id/statusText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="已同意"
            android:textColor="#8C9EB0"
            android:textSize="15sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="2018/11/09 09:56 处理人：韩天阔"
            android:textColor="#8C9EB0"
            android:textSize="13sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/statusText" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/outdated"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="43dp"
        android:gravity="center"
        android:text="申请已过期"
        android:textColor="#8C9EB0"
        android:textSize="15sp"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/reasonText" />
</androidx.constraintlayout.widget.ConstraintLayout>