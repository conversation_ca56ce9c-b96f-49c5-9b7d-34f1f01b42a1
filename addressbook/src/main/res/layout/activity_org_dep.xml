<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/search"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@color/white"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_search"
            android:layout_width="match_parent"
            android:layout_height="29dp"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="14dp"
            android:background="@drawable/rounded_membersearch_edit_bg"
            android:drawableStart="@drawable/iconsmallsearch"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:paddingStart="10dp"
            android:text="搜索"
            android:textColor="#CBCBCB"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_level_list"
        android:layout_width="match_parent"
        android:layout_height="29dp"
        android:paddingStart="11dp"
        android:paddingEnd="11dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/search" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/company"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="@dimen/dp_14"
        app:layout_constraintTop_toBottomOf="@id/rv_level_list">

        <TextView
            android:id="@+id/tv_company_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="加优科技有限公司"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toTopOf="@id/tv_dept_level"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_dept_level"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:text="部门层级：1级部门"
            android:textColor="@color/color999999"
            android:textSize="@dimen/sp_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_company_name" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_sub_dept"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:background="@color/background"
        android:gravity="center"
        android:text="子部门"
        android:textColor="#999999"
        android:textSize="12sp"
        app:layout_constraintTop_toBottomOf="@id/company" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_sub_dept_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toTopOf="@id/moveText"
        app:layout_constraintTop_toBottomOf="@id/tv_sub_dept" />

    <TextView
        android:id="@+id/moveText"
        android:layout_width="match_parent"
        android:layout_height="47dp"
        android:background="@color/white"
        android:gravity="center"
        android:text="移动到这里"
        android:textColor="#ff1e87f0"
        android:textSize="15sp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>