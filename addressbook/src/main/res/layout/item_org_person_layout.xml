<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/index_tv"
        tools:text="首字母"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_24"
        android:background="@color/background"
        android:gravity="center_vertical"
        android:paddingStart="14dp"
        android:textColor="#BDBDBD"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RelativeLayout
        android:id="@+id/content_contain_rl"
        android:layout_width="match_parent"
        android:layout_height="53dp"
        android:background="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/index_tv">

        <ImageView
            android:id="@+id/choose_tag_iv"
            android:layout_width="17dp"
            android:layout_height="17dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="3dp"
            tools:visibility="visible"
            android:src="@drawable/checkbox_friend_selector"
            android:visibility="visible" />

        <com.joinutech.ddbeslibrary.widget.CircleImageView
            android:id="@+id/header_civ"
            android:layout_width="38dp"
            android:layout_height="38dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="14dp"
            android:layout_toEndOf="@id/choose_tag_iv" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="10dp"
            android:layout_toEndOf="@+id/header_civ">

            <TextView
                android:id="@+id/name_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                tools:text="姓名"
                android:ellipsize="end"
                android:maxEms="10"
                android:singleLine="true"
                android:textColor="#ff333333"
                android:textSize="14sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/position_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:ellipsize="end"
                android:maxEms="20"
                android:singleLine="true"
                android:textColor="#ff999999"
                android:textSize="12sp"
                tools:text="position"
                tools:visibility="visible"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/name_tv" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true" />

        <TextView
            android:id="@+id/status_tv"
            android:layout_width="53dp"
            android:layout_height="28dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="36dp"
            android:background="@drawable/rounded_blue_phonecontact"
            android:gravity="center"
            android:text="已添加"
            tools:visibility="visible"
            android:textColor="#ff999999"
            android:textSize="12sp"
            android:visibility="gone" />
    </RelativeLayout>

    <View
        android:id="@+id/view_logout"
        android:layout_width="wrap_content"
        android:layout_height="53dp"
        android:background="#63999999"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/content_contain_rl"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/content_contain_rl"
        tools:visibility="gone" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/c_eeeeee"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content_contain_rl" />
</androidx.constraintlayout.widget.ConstraintLayout>