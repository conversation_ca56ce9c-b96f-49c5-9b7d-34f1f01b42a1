<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/dataLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <include
            android:id="@+id/search_inclue_layout"
            layout="@layout/search_include_layout"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/createProgramText"
            android:layout_width="match_parent"
            android:layout_height="24dp"
            android:background="#FDFCED"
            android:gravity="center"
            android:text="项目创建已完成，请选择项目参与人员"
            android:textColor="#ffff7800"
            android:textSize="@dimen/sp_12"
            android:visibility="gone" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_title_layout"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:background="@color/white"
            android:visibility="gone">

            <ImageView
                android:id="@+id/select_check"
                android:layout_width="47dp"
                android:layout_height="47dp"
                android:padding="15dp"
                android:src="@drawable/checkbox_friend_selector"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/selectAllMember"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="项目全体成员"
                android:textColor="#ff333333"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@id/select_check"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/main_recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible" />
    </LinearLayout>

    <!--默认空白页面-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:ev_content="未找到符合条件的担当好友"
        app:ev_icon="@drawable/ic_empty_friend" />
    <!--    cl_empty_layout-->
</androidx.constraintlayout.widget.ConstraintLayout>