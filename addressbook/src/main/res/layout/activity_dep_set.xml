<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/depNameLayout"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginTop="9dp"
            android:background="@color/white"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="12dp"
                android:text="部门名称"
                android:textColor="#ff333333"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingEnd="14dp"
                android:textColor="#ff1e87f0"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/aboveDepLayout"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginTop="10dp"
            android:background="@color/white"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/depNameLayout">

            <TextView
                android:id="@+id/aboveDep"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="12dp"
                android:text="上级部门"
                android:textColor="#ff333333"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/aboveDepNext"
                android:layout_width="33dp"
                android:layout_height="14dp"
                android:paddingStart="9dp"
                android:paddingEnd="16dp"
                android:src="@drawable/arraw_right"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/aboveDepName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="加优科技有限公司"
                android:textColor="#ff1e87f0"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toLeftOf="@id/aboveDepNext"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/depHeadLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/aboveDepLayout">

            <TextView
                android:id="@+id/depHead"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="12dp"
                android:text="部门负责人"
                android:textColor="#999999"
                android:textSize="12sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!--正职负责人-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/addDepHeadLayout"
                android:layout_width="278dp"
                android:layout_height="42dp"
                android:layout_marginTop="21dp"
                android:background="@drawable/rounded_c4d5e7_set_person_bg"
                android:visibility="visible"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/depHead">

                <ImageView
                    android:id="@+id/next"
                    android:layout_width="17dp"
                    android:layout_height="17dp"
                    android:layout_marginEnd="@dimen/dp_10"
                    android:src="@drawable/icon_add_dep_head"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/abovePerson"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/abovePerson"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="添加部门负责人"
                    android:textColor="#ffffffff"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/next"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <include
                android:id="@+id/depHeadPersonLayout"
                layout="@layout/dep_head_item_layout"
                android:layout_width="match_parent"
                android:layout_height="86dp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/depHead" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/depViceHeadLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/depHeadLayout">

            <TextView
                android:id="@+id/depViceHead"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="12dp"
                android:text="部门副职负责人"
                android:textColor="#999999"
                android:textSize="12sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/depViceHeadRv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:overScrollMode="never"
                android:scrollbars="none"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/depViceHead" />

            <!--副职负责人-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/addDepViceHeadLayout"
                android:layout_width="278dp"
                android:layout_height="42dp"
                android:layout_marginTop="19dp"
                android:background="@drawable/rounded_c4d5e7_set_person_bg"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/depViceHeadRv">

                <ImageView
                    android:id="@+id/viceNext"
                    android:layout_width="17dp"
                    android:layout_height="17dp"
                    android:layout_marginEnd="@dimen/dp_10"
                    android:src="@drawable/icon_add_dep_head"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/vicePerson"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/vicePerson"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="添加副职负责人"
                    android:textColor="#ffffffff"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/viceNext"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/depMemberProtect"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@color/white"
            android:paddingStart="@dimen/dp_14"
            android:paddingEnd="@dimen/dp_14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/depViceHeadLayout">

            <com.kyleduo.switchbutton.SwitchButton
                android:id="@+id/sb_dep_member_protect"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                app:kswAnimationDuration="300"
                app:kswBackDrawable="@drawable/drawable_member_protect"
                app:kswThumbDrawable="@drawable/ios_thumb_selector"
                app:kswThumbMarginBottom="-8dp"
                app:kswThumbMarginLeft="-5dp"
                app:kswThumbMarginRight="-5dp"
                app:kswThumbMarginTop="-2.5dp"
                app:kswThumbRangeRatio="1.4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvDepMemberProtect"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="本部门员工信息保护"
                android:textColor="@color/color999999"
                android:textSize="@dimen/sp_14"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/sb_dep_member_protect"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/line_range"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_2"
            android:background="@color/c_E5E5E5"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/depMemberProtect" />

        <LinearLayout
            android:id="@+id/depMemberProtectRange"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_38"
            android:background="@color/white"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_14"
            android:paddingEnd="@dimen/dp_14"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line_range">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="可见范围"
                android:textColor="@color/color666666"
                android:textSize="@dimen/sp_12" />

            <TextView
                android:id="@+id/tv_protect_range"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_10"
                android:layout_marginEnd="@dimen/dp_10"
                android:layout_weight="1"
                android:ellipsize="end"
                android:gravity="end"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/color2479ED"
                android:textSize="@dimen/sp_12" />

            <ImageView
                android:layout_width="@dimen/dp_8"
                android:layout_height="@dimen/dp_14"
                android:src="@drawable/arraw_right" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>