<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_48">

    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/iv_user_icon"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_34"
        android:layout_marginStart="@dimen/dp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:paddingStart="@dimen/dp_13"
        android:paddingEnd="@dimen/dp_5"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_13"
        android:maxLines="2"
        android:ellipsize="end"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/checkMember"
        app:layout_constraintStart_toEndOf="@id/iv_user_icon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="安安" />

    <ImageView
        android:id="@+id/checkMember"
        android:layout_width="@dimen/dp_52"
        android:layout_height="@dimen/dp_48"
        android:paddingStart="@dimen/dp_16"
        android:paddingTop="@dimen/dp_14"
        android:paddingEnd="@dimen/dp_16"
        android:paddingBottom="@dimen/dp_14"
        android:src="@drawable/checkbox_memberselect_selector"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/item_confirm_iv"
        android:layout_width="@dimen/dp_52"
        android:layout_height="@dimen/dp_48"
        android:paddingStart="@dimen/dp_16"
        android:paddingTop="@dimen/dp_14"
        android:paddingEnd="@dimen/dp_16"
        android:paddingBottom="@dimen/dp_14"
        android:src="@drawable/icon_personaddress_confirm"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>