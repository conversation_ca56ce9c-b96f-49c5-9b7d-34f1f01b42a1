<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="65dp">

    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/iv_header_image"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_marginStart="15dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_no_register"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_marginStart="15dp"
        android:background="@drawable/oval_main_blue"
        android:gravity="center"
        android:text="未注册"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_11"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_no_active"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_marginStart="15dp"
        android:background="@drawable/oval_translucent_mask"
        android:gravity="center"
        android:text="未激活"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_11"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="9dp"
        android:layout_marginTop="8dp"
        android:textColor="@color/colorFF323232"
        android:textSize="@dimen/sp_16"
        app:layout_constraintStart_toEndOf="@id/iv_header_image"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_level_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="6dp"
        android:paddingStart="2dp"
        android:paddingTop="4dp"
        android:paddingEnd="7dp"
        android:paddingBottom="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/name">

        <ImageView
            android:id="@+id/iv_level_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_level_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:textColor="#fffa9e1e"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_level_icon"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_cooper_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:background="@drawable/shape_external_contact_e1f1f0"
        android:paddingStart="8dp"
        android:paddingTop="4dp"
        android:paddingEnd="7dp"
        android:paddingBottom="4dp"
        android:textColor="#ff17c194"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@id/cl_level_layout"
        app:layout_constraintStart_toEndOf="@id/cl_level_layout"
        app:layout_constraintTop_toTopOf="@id/cl_level_layout" />

    <ImageView
        android:layout_width="6dp"
        android:layout_height="12dp"
        android:layout_marginEnd="15dp"
        android:src="@drawable/arraw_right"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_grey"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>