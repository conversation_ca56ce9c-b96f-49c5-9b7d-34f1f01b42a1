<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_15"
    android:paddingTop="@dimen/dp_20"
    android:paddingEnd="@dimen/dp_15">

    <TextView
        android:id="@+id/tv_change_user_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="你确认要转移该团队的创建者身份给xxxxx吗？"
        android:textColor="@color/text_black"
        android:textSize="@dimen/sp_18" />

    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/iv_org_icon"
        android:layout_width="@dimen/dp_76"
        android:layout_height="@dimen/dp_76"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_15" />

    <TextView
        android:id="@+id/tv_change_org_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:gravity="center_horizontal"
        android:textColor="@color/text_black" />

    <TextView
        android:id="@+id/tv_change_user_mobile"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_30"
        android:text="请验证团队创建者手机号"
        android:textColor="@color/text_black"
        android:textSize="@dimen/sp_14" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="20dp">

        <EditText
            android:id="@+id/et_disband"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:background="@null"
            android:hint="请输入验证码"
            android:inputType="number"
            android:maxEms="6"
            android:minWidth="200dp"
            android:singleLine="true"
            android:textColor="@color/text_black"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/code_disband"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:text="获取验证码"
            android:textColor="@color/main_blue" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="20dp"
            android:layout_centerVertical="true"
            android:layout_marginRight="40dp"
            android:layout_toLeftOf="@id/code_disband"
            android:background="@color/line_grey" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:background="@color/line_grey" />
    </RelativeLayout>

    <TextView
        android:id="@+id/button_disband"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginTop="48dp"
        android:background="@drawable/shape_tv_corner3"
        android:gravity="center"
        android:text="确认转移"
        android:textColor="@color/white"
        android:textSize="14sp" />


    <TextView
        android:id="@+id/tv_change_tips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:text="1.完成转移之后，你将失去在本团队内的团队创建者身份（你的部门负责人或部门副职负责人职位将得以保留）\n2.你所使用的其他担当办公系列产品（如担当企业云盘、担当云资产）中，本团队的创建者身份也将同时转移给该用户\n3.转移操作一旦完成不可撤销，请谨慎操作"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_13" />

</LinearLayout>