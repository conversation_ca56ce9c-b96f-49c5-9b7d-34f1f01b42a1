<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <FrameLayout
        android:id="@+id/friendListLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_search_friend"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:background="@color/white">

                <TextView
                    android:id="@+id/search"
                    android:layout_width="match_parent"
                    android:layout_height="29dp"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="14dp"
                    android:background="@drawable/rounded_membersearch_edit_bg"
                    android:drawableLeft="@drawable/iconsmallsearch"
                    android:drawablePadding="5dp"
                    android:gravity="center_vertical"
                    android:paddingStart="10dp"
                    android:text="搜索担当好友"
                    android:textColor="#CBCBCB"
                    android:textSize="12sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/main_recycler"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="visible" />
        </LinearLayout>

        <com.joinutech.ddbeslibrary.widget.wavesidebar.WaveSideBarView
            android:id="@+id/main_side_bar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="8dp"
            app:sidebar_lazy_respond="false"
            app:sidebar_text_color="#BDBDBD" />
    </FrameLayout>

    <!--默认空白页面-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:ev_content="暂无好友"
        app:ev_icon="@drawable/ic_empty_friend"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>