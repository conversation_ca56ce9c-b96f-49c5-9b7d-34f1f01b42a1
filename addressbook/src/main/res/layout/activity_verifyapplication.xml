<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <TextView
        android:id="@+id/text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="5dp"
        android:text="你需要发送申请理由，等待团队管理员通过"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="96dp"
        android:layout_marginTop="6dp"
        app:layout_constraintTop_toBottomOf="@id/text">

        <EditText
            android:id="@+id/reason"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:gravity="left"
            android:paddingStart="@dimen/dp_24"
            android:paddingTop="@dimen/dp_20"
            android:paddingEnd="@dimen/dp_24"
            android:paddingBottom="@dimen/dp_20"
            android:textColor="@color/colorFF323232"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:id="@+id/num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            android:layout_marginBottom="5dp"
            android:textColor="@color/colorFF323232"
            android:textSize="@dimen/sp_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>