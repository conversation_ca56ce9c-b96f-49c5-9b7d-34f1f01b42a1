<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".view.AddOrgPermissionPersonActivity2"
    tools:ignore="MissingDefaultResource">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/manageName"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_marginTop="10dp"
        android:background="@color/white"
        android:focusable="true"
        android:focusableInTouchMode="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_manage_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:text="管理员名称"
            android:textColor="#ff333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_per_icon"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginEnd="6dp"
            android:src="@drawable/icon_super_permission_tag"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/editName"
            app:layout_constraintEnd_toStartOf="@id/editName"
            app:layout_constraintTop_toTopOf="@id/editName" />

        <EditText
            android:id="@+id/editName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:background="@color/white"
            android:gravity="end"
            android:hint="请输入管理员名称"
            android:textColor="#ff323232"
            android:textColorHint="#aaaaaa"
            android:textSize="@dimen/sp_13"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.2dp"
            android:background="#E2E2E2"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/selectedPerson"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:background="@color/white"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/manageName">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:text="选择人员"
            android:textColor="#ff333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/selectedLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/selectedIcon"
                android:layout_width="6dp"
                android:layout_height="12dp"
                android:src="@drawable/arraw_right"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/selectedText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:hint="请选择人员"
                android:textColorHint="@color/colorFFAAAAAA"
                android:textColor="@color/text_black"
                android:textSize="@dimen/sp_13"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toLeftOf="@id/selectedIcon"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/toDoText"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:layout_marginStart="14dp"
        android:gravity="center_vertical"
        android:text="可分配权限"
        android:textColor="#ff666666"
        android:textSize="@dimen/sp_12"
        app:layout_constraintTop_toBottomOf="@+id/selectedPerson" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/deletePermission"
        app:layout_constraintTop_toBottomOf="@id/toDoText" />

    <TextView
        android:id="@+id/deletePermission"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_20"
        android:paddingTop="@dimen/dp_20"
        android:text="删除此类管理员"
        android:textColor="@color/red"
        android:textSize="@dimen/sp_14"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
