<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_content"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_60"
        android:paddingTop="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.joinutech.ddbeslibrary.widget.CircleImageView
            android:id="@+id/iv_user_icon"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginStart="@dimen/dp_14"
            android:src="@drawable/default_heading"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxEms="10"
            android:maxLines="1"
            android:paddingStart="@dimen/dp_8"
            android:paddingTop="@dimen/dp_5"
            android:paddingBottom="@dimen/dp_5"
            android:singleLine="true"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_user_icon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="人员姓名信息" />

        <TextView
            android:id="@+id/createMark"
            android:layout_width="51dp"
            android:layout_height="19dp"
            android:layout_marginStart="10dp"
            android:background="@drawable/rounded_blue_createmark"
            android:gravity="center"
            android:text="创建者"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/name"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/managerMark"
            android:layout_width="51dp"
            android:layout_height="19dp"
            android:layout_marginStart="10dp"
            android:background="@drawable/rounded_green_managermark"
            android:gravity="center"
            android:text="负责人"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/createMark"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/managerViceMark"
            android:layout_width="43dp"
            android:layout_height="19dp"
            android:layout_marginStart="10dp"
            android:background="@drawable/rounded_8fc31f_vice_managermark"
            android:gravity="center"
            android:text="副职"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/managerMark"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <View
            android:id="@+id/editLine"
            android:layout_width="@dimen/dp_0_5"
            android:layout_height="24dp"
            android:layout_marginEnd="15dp"
            android:background="#E2E2E2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/edit"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/edit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:text="编辑"
            android:textColor="#ff1e87f0"
            android:textSize="@dimen/sp_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/view_logout"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_60"
        android:background="#63999999"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/cl_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/cl_content"
        tools:visibility="visible" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:layout_marginStart="@dimen/dp_18"
        android:layout_marginEnd="@dimen/dp_18"
        android:background="#E6E6E6"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>