<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/manageName"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:layout_marginTop="10dp"
            android:background="@color/white"
            android:focusable="true"
            android:focusableInTouchMode="true"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_manage_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:text="管理员名称"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_14"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_per_icon"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_marginEnd="6dp"
                android:src="@drawable/icon_super_permission_tag"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/editName"
                app:layout_constraintEnd_toStartOf="@id/editName"
                app:layout_constraintTop_toTopOf="@id/editName" />

            <EditText
                android:id="@+id/editName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:background="@color/white"
                android:gravity="end"
                android:hint="请输入管理员名称"
                android:textColor="#ff323232"
                android:textColorHint="#aaaaaa"
                android:textSize="@dimen/sp_13"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.2dp"
                android:background="#E2E2E2"
                app:layout_constraintBottom_toBottomOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/selectedPerson"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:background="@color/white"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/manageName">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:text="选择人员"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_14"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/selectedLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="14dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/selectedIcon"
                    android:layout_width="6dp"
                    android:layout_height="12dp"
                    android:src="@drawable/arraw_right"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/selectedText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="请选择人员"
                    android:textColor="#ffaaaaaa"
                    android:textSize="@dimen/sp_13"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/selectedIcon"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/toDoText"
            android:layout_width="match_parent"
            android:layout_height="24dp"
            android:layout_marginStart="14dp"
            android:gravity="center_vertical"
            android:text="可分配权限"
            android:textColor="#ff666666"
            android:textSize="@dimen/sp_12"
            app:layout_constraintTop_toBottomOf="@+id/selectedPerson" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/normalManagerSetPermissionLayout"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:background="@color/white"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/toDoText">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:text="普通管理员设置权限"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/sp_14"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.2dp"
                android:background="#E2E2E2"
                app:layout_constraintBottom_toBottomOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/orgLikePermissionLayout"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:background="@color/white"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/normalManagerSetPermissionLayout">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:text="团队偏好设置权限"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_14"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.2dp"
                android:background="#E2E2E2"
                app:layout_constraintBottom_toBottomOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/AllPermission"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/orgLikePermissionLayout">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:text="全部权限"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_14"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.kyleduo.switchbutton.SwitchButton
                android:id="@+id/allPermissionSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="14dp"
                app:kswAnimationDuration="300"
                app:kswBackDrawable="@drawable/ios_back_drawable"
                app:kswThumbDrawable="@drawable/ios_thumb_selector"
                app:kswThumbMarginBottom="-8dp"
                app:kswThumbMarginLeft="-5dp"
                app:kswThumbMarginRight="-5dp"
                app:kswThumbMarginTop="-2.5dp"
                app:kswThumbRangeRatio="1.4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.2dp"
                android:background="#E2E2E2"
                app:layout_constraintBottom_toBottomOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/OrgPermission"
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/AllPermission">

            <ImageView
                android:id="@+id/iv_org_per"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginStart="12dp"
                android:src="@drawable/per_org"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_org_per"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="9dp"
                android:layout_marginTop="12dp"
                android:text="团队管理权限"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_14"
                app:layout_constraintLeft_toRightOf="@id/iv_org_per"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="9dp"
                android:layout_marginTop="5dp"
                android:text="可对团队资料、成员及架构进行调整，可编辑公告"
                android:textColor="#ff666666"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toStartOf="@id/orgPerSwitch"
                app:layout_constraintStart_toEndOf="@id/iv_org_per"
                app:layout_constraintTop_toBottomOf="@id/tv_org_per" />

            <com.kyleduo.switchbutton.SwitchButton
                android:id="@+id/orgPerSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="14dp"
                app:kswAnimationDuration="300"
                app:kswBackDrawable="@drawable/ios_back_drawable"
                app:kswThumbDrawable="@drawable/ios_thumb_selector"
                app:kswThumbMarginBottom="-8dp"
                app:kswThumbMarginLeft="-5dp"
                app:kswThumbMarginRight="-5dp"
                app:kswThumbMarginTop="-2.5dp"
                app:kswThumbRangeRatio="1.4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_attend_layout"
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/OrgPermission">

            <ImageView
                android:id="@+id/iv_attend_per"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginStart="12dp"
                android:src="@drawable/per_attendace"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_attend_per"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="9dp"
                android:layout_marginTop="12dp"
                android:text="考勤管理权限"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_14"
                app:layout_constraintLeft_toRightOf="@id/iv_attend_per"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="9dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="12dp"
                android:text="可查看全团队考勤数据、调整考勤规则等"
                android:textColor="#ff666666"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/attendancePerSwitch"
                app:layout_constraintStart_toEndOf="@id/iv_attend_per" />

            <com.kyleduo.switchbutton.SwitchButton
                android:id="@+id/attendancePerSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="14dp"
                app:kswAnimationDuration="300"
                app:kswBackDrawable="@drawable/ios_back_drawable"
                app:kswThumbDrawable="@drawable/ios_thumb_selector"
                app:kswThumbMarginBottom="-8dp"
                app:kswThumbMarginLeft="-5dp"
                app:kswThumbMarginRight="-5dp"
                app:kswThumbMarginTop="-2.5dp"
                app:kswThumbRangeRatio="1.4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_task_layout"
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/cl_attend_layout">

            <ImageView
                android:id="@+id/iv_task_per"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginStart="12dp"
                android:src="@drawable/icon_task_manager"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_task_per"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="9dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="10dp"
                android:text="项目创建权限"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_14"
                app:layout_constraintEnd_toStartOf="@id/taskPerSwitch"
                app:layout_constraintStart_toEndOf="@id/iv_task_per"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="9dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="12dp"
                android:text="可在任务功能中创建新项目"
                android:textColor="#ff666666"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/taskPerSwitch"
                app:layout_constraintStart_toEndOf="@id/iv_task_per" />

            <com.kyleduo.switchbutton.SwitchButton
                android:id="@+id/taskPerSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="14dp"
                app:kswAnimationDuration="300"
                app:kswBackDrawable="@drawable/ios_back_drawable"
                app:kswThumbDrawable="@drawable/ios_thumb_selector"
                app:kswThumbMarginBottom="-8dp"
                app:kswThumbMarginLeft="-5dp"
                app:kswThumbMarginRight="-5dp"
                app:kswThumbMarginTop="-2.5dp"
                app:kswThumbRangeRatio="1.4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/approvalPermission"
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:background="@color/white"
            android:visibility="visible"
            app:layout_constraintTop_toBottomOf="@id/cl_task_layout">

            <ImageView
                android:id="@+id/iv_approval_per"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginStart="12dp"
                android:src="@drawable/icon_approval_permission"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/approvalPerText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="9dp"
                android:layout_marginTop="12dp"
                android:text="审批管理权限"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_14"
                app:layout_constraintLeft_toRightOf="@id/iv_approval_per"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="9dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="12dp"
                android:text="可快速启/停用审批模板，可编辑审批流程"
                android:textColor="#ff666666"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/approvalPerSwitch"
                app:layout_constraintStart_toEndOf="@id/iv_approval_per" />

            <com.kyleduo.switchbutton.SwitchButton
                android:id="@+id/approvalPerSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="14dp"
                app:kswAnimationDuration="300"
                app:kswBackDrawable="@drawable/ios_back_drawable"
                app:kswThumbDrawable="@drawable/ios_thumb_selector"
                app:kswThumbMarginBottom="-8dp"
                app:kswThumbMarginLeft="-5dp"
                app:kswThumbMarginRight="-5dp"
                app:kswThumbMarginTop="-2.5dp"
                app:kswThumbRangeRatio="1.4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/workReportPermission"
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:background="@color/white"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/approvalPermission">

            <ImageView
                android:id="@+id/workReportPerIcon"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginStart="12dp"
                android:src="@drawable/icon_report_permission"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/workReportPerText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="9dp"
                android:layout_marginTop="12dp"
                android:text="汇报管理权限"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_14"
                app:layout_constraintLeft_toRightOf="@id/workReportPerIcon"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="9dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="12dp"
                android:text="可设定工作汇报规则，查看工作汇报统计"
                android:textColor="#ff666666"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/workReportPerSwitch"
                app:layout_constraintStart_toEndOf="@id/workReportPerIcon" />

            <com.kyleduo.switchbutton.SwitchButton
                android:id="@+id/workReportPerSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="14dp"
                app:kswAnimationDuration="300"
                app:kswBackDrawable="@drawable/ios_back_drawable"
                app:kswThumbDrawable="@drawable/ios_thumb_selector"
                app:kswThumbMarginBottom="-8dp"
                app:kswThumbMarginLeft="-5dp"
                app:kswThumbMarginRight="-5dp"
                app:kswThumbMarginTop="-2.5dp"
                app:kswThumbRangeRatio="1.4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/deletePermission"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="50dp"
            android:paddingBottom="10dp"
            android:text="删除此类管理员"
            android:textColor="#ffff0000"
            android:textSize="14sp"
            android:visibility="visible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/workReportPermission" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>