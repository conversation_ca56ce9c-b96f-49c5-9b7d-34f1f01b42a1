<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_company_info_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_300"
            android:background="@drawable/bg_company_info_top"
            app:layout_constraintTop_toTopOf="parent">

            <RelativeLayout
                android:id="@+id/tb_top_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:minHeight="?android:attr/actionBarSize"
                    app:contentInsetStart="0dp"
                    app:title="">

                    <ImageView
                        android:id="@+id/btn_back_arrow"
                        android:layout_width="19dp"
                        android:layout_height="26dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="15dp"
                        android:padding="5dp"
                        android:src="@drawable/back_white" />

                    <TextView
                        android:id="@+id/toolbar_title_left"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="企业简介"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp_18" />
                </androidx.appcompat.widget.Toolbar>
            </RelativeLayout>

            <com.joinutech.ddbeslibrary.widget.CircleImageView
                android:id="@+id/logo"
                android:layout_width="76dp"
                android:layout_height="76dp"
                android:layout_marginTop="112dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_15"
                android:layout_marginTop="@dimen/dp_17"
                android:layout_marginEnd="@dimen/dp_15"
                android:ellipsize="end"
                android:gravity="center_horizontal"
                android:maxLines="2"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_17"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/logo" />

            <LinearLayout
                android:id="@+id/ll_profession"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp_30"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/ll_member"
                app:layout_constraintHorizontal_chainStyle="spread"
                app:layout_constraintStart_toStartOf="parent">

                <ImageView
                    android:id="@+id/iv_profession_icon"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/icon_orgprofession" />

                <TextView
                    android:id="@+id/tv_profession_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_5"
                    android:text="未设置"
                    android:textColor="#ffffffff"
                    android:textSize="@dimen/sp_13" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_member"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp_30"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ll_profession">

                <ImageView
                    android:id="@+id/iv_member_icon"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/icon_orgnum" />

                <TextView
                    android:id="@+id/tv_member_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_5"
                    android:text="未设置"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_13" />
            </LinearLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/companyIntroContentLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/cl_company_info_layout">

            <TextView
                android:id="@+id/companyIntroHint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="19dp"
                android:layout_marginTop="14dp"
                android:text="企业介绍"
                android:textColor="#ff323232"
                android:textSize="@dimen/sp_15"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/companyIntroLine"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginStart="19dp"
                android:layout_marginTop="14dp"
                android:background="@color/line_grey"
                app:layout_constraintTop_toBottomOf="@id/companyIntroHint" />

            <TextView
                android:id="@+id/companyIntroText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:layout_marginEnd="20dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:paddingBottom="5dp"
                android:text="未填写"
                android:textColor="#ff999999"
                android:textSize="@dimen/sp_13"
                app:layout_constraintTop_toBottomOf="@id/companyIntroLine" />

            <TextView
                android:id="@+id/companyIntroTextMore"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:layout_marginTop="5dp"
                android:text="查看更多"
                android:textColor="#666666"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"

                app:layout_constraintTop_toBottomOf="@id/companyIntroText" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/companyIntroMoreInfoLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            android:background="@color/white"
            android:paddingBottom="12dp"
            app:layout_constraintTop_toBottomOf="@id/companyIntroContentLayout">

            <ImageView
                android:id="@+id/typeIv"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_marginStart="25dp"
                android:layout_marginTop="24dp"
                android:src="@drawable/icon_orgtype"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_cooper_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="13dp"
                android:text="未设置"
                android:textColor="#808080"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="@id/typeIv"
                app:layout_constraintLeft_toRightOf="@id/typeIv"
                app:layout_constraintTop_toTopOf="@id/typeIv" />

            <ImageView
                android:id="@+id/webIv"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_marginStart="25dp"
                android:layout_marginTop="22dp"
                android:src="@drawable/icon_org_web"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/typeIv" />

            <TextView
                android:id="@+id/webTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="13dp"
                android:text="未填写"
                android:textColor="#808080"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="@id/webIv"
                app:layout_constraintLeft_toRightOf="@id/webIv"
                app:layout_constraintTop_toTopOf="@id/webIv" />

            <ImageView
                android:id="@+id/phoneIv"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_marginStart="25dp"
                android:layout_marginTop="22dp"
                android:src="@drawable/icon_org_phone"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/webIv" />

            <TextView
                android:id="@+id/phoneTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="13dp"
                android:text="未填写"
                android:textColor="#808080"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="@id/phoneIv"
                app:layout_constraintLeft_toRightOf="@id/phoneIv"
                app:layout_constraintTop_toTopOf="@id/phoneIv" />

            <ImageView
                android:id="@+id/emailIv"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_marginStart="25dp"
                android:layout_marginTop="22dp"
                android:src="@drawable/icon_org_email"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/phoneIv" />

            <TextView
                android:id="@+id/emailTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="13dp"
                android:text="未填写"
                android:textColor="#808080"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="@id/emailIv"
                app:layout_constraintLeft_toRightOf="@id/emailIv"
                app:layout_constraintTop_toTopOf="@id/emailIv" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/joinOrg"
            android:layout_width="312dp"
            android:layout_height="42dp"
            android:layout_marginTop="15dp"
            android:background="@drawable/rounded_blue_agree_bg"
            android:gravity="center"
            android:text="申请加入企业"
            android:textColor="#ffffffff"
            android:textSize="15sp"
            android:visibility="visible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/companyIntroMoreInfoLayout" />

        <TextView
            android:id="@+id/textReject"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="29dp"
            android:paddingBottom="10dp"
            android:text="该团队拒绝申请加入"
            android:textColor="#ffcccccc"
            android:textSize="15sp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/companyIntroMoreInfoLayout" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>