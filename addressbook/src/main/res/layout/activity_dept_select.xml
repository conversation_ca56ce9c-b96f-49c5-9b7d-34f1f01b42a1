<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    tools:context=".view.secret.DeptSelectActivity">

    <include
        android:id="@+id/search_bar"
        layout="@layout/layout_search_bar_dept"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_level_list"
        android:layout_width="match_parent"
        android:layout_height="29dp"
        android:paddingStart="11dp"
        android:paddingEnd="11dp"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search_bar" />

    <include
        android:id="@+id/org_root"
        layout="@layout/item_select_dept_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_58"
        android:layout_marginTop="@dimen/dp_1"
        app:layout_constraintTop_toBottomOf="@id/rv_level_list" />

    <TextView
        android:id="@+id/tv_sub_dept"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_42"
        android:gravity="center"
        android:text="子部门"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        app:layout_constraintTop_toBottomOf="@id/org_root" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tv_select_dept_count"
        app:layout_constraintTop_toBottomOf="@id/tv_sub_dept" />

    <TextView
        android:id="@+id/btn_select_dept"
        android:layout_width="match_parent"
        android:layout_height="47dp"
        android:background="@color/white"
        android:gravity="center"
        android:text="选择此部门"
        android:textColor="#ff1e87f0"
        android:textSize="15sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_select_dept_count"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_47"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp_14"
        android:text="@string/str_select_dept_count"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_13"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_list_search"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search_bar" />
</androidx.constraintlayout.widget.ConstraintLayout>