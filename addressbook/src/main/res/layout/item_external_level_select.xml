<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="53dp">

    <View
        android:layout_width="match_parent"
        android:layout_height="0.2dp"
        android:background="@color/line_grey"
        app:layout_constraintBottom_toBottomOf="parent" />

    <ImageView
        android:id="@+id/tv_level_name"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:layout_marginStart="140dp"
        android:src="@drawable/icon_external_contact_level_yellow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="9dp"
        android:text="非常重要"
        android:textColor="#faa62f"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_level_name"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>