<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_48"
    android:background="@color/white"
    android:orientation="vertical">

    <View
        android:id="@+id/line_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_5"
        android:background="@color/c_E5E5E5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_none"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_10"
        android:background="@color/white"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:singleLine="true"
        android:text="所有成员均不可见，包括本部门内成员"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toTopOf="@id/line_bottom"
        app:layout_constraintEnd_toStartOf="@id/iv_arrow"
        app:layout_constraintStart_toEndOf="@id/iv_select"
        app:layout_constraintTop_toBottomOf="@id/line_top" />

    <ImageView
        android:id="@+id/iv_select"
        android:layout_width="@dimen/dp_41"
        android:layout_height="@dimen/dp_37"
        android:paddingStart="@dimen/dp_14"
        android:paddingTop="@dimen/dp_10"
        android:paddingEnd="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        android:src="@drawable/selector_circle_select"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_arrow"
        android:layout_width="@dimen/dp_14"
        android:layout_height="@dimen/dp_14"
        android:layout_marginEnd="@dimen/dp_14"
        android:src="@drawable/arraw_right"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/line_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_5"
        android:background="@color/c_E5E5E5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>