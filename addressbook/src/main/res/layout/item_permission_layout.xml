<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/OrgPermission"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp_0_2"
    android:background="@color/white"
    tools:ignore="MissingDefaultResource">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:layout_marginStart="@dimen/dp_12"
        android:src="@drawable/per_org"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:paddingStart="@dimen/dp_10"
        android:paddingTop="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_10"
        android:text="团队管理权限"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toTopOf="@id/tv_desc"
        app:layout_constraintEnd_toStartOf="@id/sb_toggle"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="spread" />

    <TextView
        android:id="@+id/tv_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="start"
        android:includeFontPadding="false"
        android:maxLines="2"
        android:paddingStart="@dimen/dp_10"
        android:paddingTop="@dimen/dp_5"
        android:paddingEnd="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_12"
        android:text="可对团队资料、成员及架构进行调整，可编辑公告及架构进行调整，可编辑公告及架构进行调整，可编辑公告及架构进行调整，可编辑公告"
        android:textColor="@color/color666666"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/sb_toggle"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <com.kyleduo.switchbutton.SwitchButton
        android:id="@+id/sb_toggle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_15"
        android:paddingTop="@dimen/dp_6"
        app:kswAnimationDuration="300"
        app:kswBackDrawable="@drawable/ios_back_drawable"
        app:kswThumbDrawable="@drawable/ios_thumb_selector"
        app:kswThumbMarginBottom="-8.5dp"
        app:kswThumbMarginLeft="-5dp"
        app:kswThumbMarginRight="-5dp"
        app:kswThumbMarginTop="-2.5dp"
        app:kswThumbRangeRatio="1.4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <View
        android:id="@+id/line_bottom"
        android:layout_width="match_parent"
        android:layout_height="0.2dp"
        android:background="@color/line_grey"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>