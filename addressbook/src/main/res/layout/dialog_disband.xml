<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_20"
    android:paddingEnd="@dimen/dp_20">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_dialog_wipecache"
        android:orientation="vertical">

        <ImageView
            android:layout_width="@dimen/dp_38"
            android:layout_height="@dimen/dp_38"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_30"
            android:src="@drawable/icon_disband_tip" />

        <TextView
            android:id="@+id/title_disband"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_14"
            android:text="你确认要将团队解散吗？"
            android:textColor="@color/text_black"
            android:textSize="@dimen/sp_16" />

        <TextView
            android:id="@+id/content_disband"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_20"
            android:text="解散之后，你团队内的全部员工都将退出本团队；"
            android:textColor="@color/text_lowgray"
            android:textSize="@dimen/sp_12" />

        <TextView
            android:id="@+id/content2_disband"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_14"
            android:layout_marginEnd="@dimen/dp_20"
            android:text="同时，你在担当云资产、云盘等担当产品中的对应"
            android:textColor="@color/text_lowgray"
            android:textSize="@dimen/sp_12" />

        <TextView
            android:id="@+id/content3_disband"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_14"
            android:layout_marginEnd="@dimen/dp_20"
            android:text="团队也将同步解散。请谨慎操作！"
            android:textColor="@color/text_lowgray"
            android:textSize="@dimen/sp_12" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0_2"
            android:layout_marginTop="@dimen/dp_24"
            android:background="@color/line_grey" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/cancle_disband"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="取消"
                android:textColor="@color/text_black"
                android:textSize="@dimen/sp_16" />

            <View
                android:layout_width="@dimen/dp_0_2"
                android:layout_height="match_parent"
                android:background="@color/line_grey" />

            <TextView
                android:id="@+id/confirm_disband"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="确认"
                android:textColor="@color/main_blue"
                android:textSize="@dimen/sp_16" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>