<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="329dp"
    android:layout_height="157dp"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView
        android:id="@+id/toDoText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="14dp"
        android:text="添加子部门"
        android:textColor="#ff333333"
        android:textSize="17sp"
        />
    <EditText
        android:id="@+id/depEdit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@id/toDoText"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:hint="请输入子部门名称（1-20字）"
        android:textColor="#ff999999"
        android:textSize="15sp"
        android:singleLine="true"
        android:layout_marginTop="50dp"
        />
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        app:layout_constraintTop_toBottomOf="@id/depEdit"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginStart="30dp"
        android:layout_marginEnd="30dp"
        android:layout_marginTop="16dp"
        android:background="#CDCDCD"
        />
</androidx.constraintlayout.widget.ConstraintLayout>