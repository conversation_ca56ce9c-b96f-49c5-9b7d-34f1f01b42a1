<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/topTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:text="外部联系人信息"
            android:textColor="#ff666666"
            android:textSize="@dimen/sp_13"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_name_layout"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/topTitle">

            <TextView
                android:id="@+id/tv_name_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="14dp"
                android:text="姓名"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/nameRightIv"
                android:layout_width="56dp"
                android:layout_height="match_parent"
                android:paddingStart="18dp"
                android:paddingEnd="18dp"
                android:src="@drawable/icon_add_external_contact_address"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/nameEdit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="105dp"
                android:background="@color/white"
                android:ellipsize="end"
                android:gravity="start"
                android:hint="外部联系人备注姓名"
                android:maxLength="10"
                android:singleLine="true"
                android:textColor="#ff333333"
                android:textColorHint="#ff9a9a9a"
                android:textSize="15sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/nameRightIv"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_phone_layout"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/cl_name_layout">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/line_grey"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_phone_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="14dp"
                android:text="手机号"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/phoneEdit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="105dp"
                android:background="@color/white"
                android:ellipsize="end"
                android:gravity="start"
                android:hint="外部联系人手机号"
                android:inputType="phone"
                android:maxLength="11"
                android:singleLine="true"
                android:textColor="#ff333333"
                android:textColorHint="#ff9a9a9a"
                android:textSize="15sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_email_layout"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/cl_phone_layout">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/line_grey"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_email_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="14dp"
                android:text="邮箱"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/emailEdit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="105dp"
                android:background="@color/white"
                android:ellipsize="end"
                android:gravity="start"
                android:hint="请填写邮箱号码"
                android:singleLine="true"
                android:textColor="#ff333333"
                android:textColorHint="#ff9a9a9a"
                android:textSize="15sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_remark_layout"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/cl_email_layout">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/line_grey"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_remark_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="14dp"
                android:text="备注"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/remarkEdit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="105dp"
                android:background="@color/white"
                android:ellipsize="end"
                android:gravity="start"
                android:hint="请填写备注"
                android:maxLength="10"
                android:singleLine="true"
                android:textColor="#ff333333"
                android:textColorHint="#ff9a9a9a"
                android:textSize="15sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/centerTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:text="外部联系人属性"
            android:textColor="#ff666666"
            android:textSize="@dimen/sp_13"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cl_remark_layout" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/kindLayout"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:paddingStart="@dimen/dp_14"
            android:paddingEnd="@dimen/dp_14"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/centerTitle">

            <TextView
                android:id="@+id/kindLeftText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="类型"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/kindRightIv"
                android:layout_width="@dimen/dp_12"
                android:layout_height="@dimen/dp_12"
                android:src="@drawable/arraw_right"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/kindRightTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingEnd="@dimen/dp_10"
                android:text="未选择"
                android:textColor="@color/c_ff9a9a9a"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="@id/kindRightIv"
                app:layout_constraintEnd_toStartOf="@id/kindRightIv"
                app:layout_constraintTop_toTopOf="@id/kindRightIv" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_level_layout"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:paddingStart="@dimen/dp_14"
            android:paddingEnd="@dimen/dp_14"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/kindLayout">

            <TextView
                android:id="@+id/levelLeftText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="重要级别"
                android:textColor="@color/colorFF333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/levelRightIv"
                android:layout_width="@dimen/dp_12"
                android:layout_height="@dimen/dp_12"
                android:src="@drawable/arraw_right"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/levelRightTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="未选择"
                android:textColor="@color/c_ff9a9a9a"
                android:paddingEnd="@dimen/dp_10"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="@id/levelRightIv"
                app:layout_constraintEnd_toStartOf="@id/levelRightIv"
                app:layout_constraintTop_toTopOf="@id/levelRightIv" />

            <ImageView
                android:id="@+id/levelRightIcon"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_marginEnd="5dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/levelRightTv"
                app:layout_constraintEnd_toStartOf="@id/levelRightTv"
                app:layout_constraintTop_toTopOf="@id/levelRightTv" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/sendSmsLayout"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_marginTop="10dp"
            tools:visibility="gone"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/cl_level_layout">

            <TextView
                android:id="@+id/sendSmsLeftText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="14dp"
                android:text="发送激活邀请给该用户"
                android:textColor="#ff333333"
                android:textSize="@dimen/sp_15"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.kyleduo.switchbutton.SwitchButton
                android:id="@+id/sendMsgSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="14dp"
                android:checked="true"
                app:kswAnimationDuration="300"
                app:kswBackDrawable="@drawable/ios_back_drawable"
                app:kswThumbDrawable="@drawable/ios_thumb_selector"
                app:kswThumbMarginBottom="-8dp"
                app:kswThumbMarginLeft="-5dp"
                app:kswThumbMarginRight="-5dp"
                app:kswThumbMarginTop="-2.5dp"
                app:kswThumbRangeRatio="1.4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="75dp"
            android:text="删除外部联系人"
            android:textColor="#ffff3300"
            android:textSize="@dimen/sp_14"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/sendSmsLayout" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>