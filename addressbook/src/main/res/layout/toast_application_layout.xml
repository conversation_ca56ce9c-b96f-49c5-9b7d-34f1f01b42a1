<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_application_bg_toast"
    android:gravity="center_vertical"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="29dp"
        android:layout_marginRight="29dp"
        android:paddingTop="16dp"
        android:paddingBottom="16dp">

        <TextView
            android:id="@+id/hintText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="很抱歉，由于该团队对外部协作人员信息\n的修改，此邀请已失效"
            android:textColor="#ffffffff"
            android:textSize="12sp"/>
    </RelativeLayout>
</LinearLayout>