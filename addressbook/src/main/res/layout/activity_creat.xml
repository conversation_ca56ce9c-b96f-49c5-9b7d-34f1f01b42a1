<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="70dp"
        android:orientation="vertical"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="40dp"
            android:text="团队名称"
            android:textColor="@color/text_black"
            android:textSize="@dimen/sp_17" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="40dp"
            android:layout_marginTop="10dp"
            android:text="请输入完整的团队名称,以便其他员工正确识别"
            android:textSize="@dimen/sp_13" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="40dp"
            android:layout_marginTop="45dp"
            android:layout_marginRight="40dp">

            <EditText
                android:id="@+id/creat_et_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="25dp"
                android:background="@null"
                android:hint="请输入1-30个中文字符"
                android:textColor="#ff333333"
                android:textColorHint="#ffaaaaaa"
                android:textSize="@dimen/sp_14" />

            <ImageView
                android:id="@+id/cancelInput"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:paddingStart="10dp"
                android:src="@drawable/del_img"
                android:visibility="visible" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginStart="40dp"
            android:layout_marginTop="14dp"
            android:layout_marginEnd="40dp"
            android:background="@color/line_grey" />

        <TextView
            android:id="@+id/creat_next"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginLeft="40dp"
            android:layout_marginTop="48dp"
            android:layout_marginRight="40dp"
            android:background="@drawable/shape_tv_corner3"
            android:gravity="center"
            android:text="下一步"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>