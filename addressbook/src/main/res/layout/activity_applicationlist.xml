<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/srl_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_org_apply_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/background"
            android:visibility="gone" />
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <!--默认空白页面-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:ev_content="暂无待处理的申请"
        app:ev_icon="@drawable/ic_empty_friend"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>