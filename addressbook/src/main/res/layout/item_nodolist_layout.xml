<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <com.joinutech.ddbeslibrary.widget.CircleImageView
        android:id="@+id/iv_user_icon"
        android:layout_width="@dimen/dp_43"
        android:layout_height="@dimen/dp_43"
        android:layout_marginStart="@dimen/dp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:paddingStart="@dimen/dp_10"
        android:textColor="@color/text_black"
        android:textSize="@dimen/sp_15"
        app:layout_constraintEnd_toStartOf="@id/agreeLayout"
        app:layout_constraintStart_toEndOf="@id/iv_user_icon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="name" />

    <TextView
        android:id="@+id/company"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:paddingStart="@dimen/dp_10"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toStartOf="@id/agreeLayout"
        app:layout_constraintStart_toEndOf="@id/iv_user_icon"
        app:layout_constraintTop_toBottomOf="@id/name"
        tools:text="company name" />

    <TextView
        android:id="@+id/reason"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:layout_marginBottom="@dimen/dp_10"
        android:maxEms="10"
        android:paddingStart="@dimen/dp_10"
        android:singleLine="true"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/agreeLayout"
        app:layout_constraintStart_toEndOf="@id/iv_user_icon"
        app:layout_constraintTop_toBottomOf="@id/company"
        tools:text="reason info " />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/agreeLayout"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/dp_14"
        android:paddingStart="@dimen/dp_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="已同意"
            android:textColor="@color/color999999"
            android:textSize="@dimen/sp_12"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/time"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:text="2018/08/20 09:30"
            android:textColor="@color/color999999"
            android:textSize="@dimen/sp_12"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/status" />

        <TextView
            android:id="@+id/watch"
            android:layout_width="53dp"
            android:layout_height="28dp"
            android:background="@drawable/rounded_blue_watch_bg"
            android:gravity="center"
            android:text="查看"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_13"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_2"
        android:background="@color/line_grey"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>