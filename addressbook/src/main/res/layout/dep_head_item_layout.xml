<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/dept_head_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="86dp"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_dept_head_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="26dp"
            android:layout_marginTop="15dp"
            android:maxEms="10"
            android:singleLine="true"
            android:textColor="#ff323232"
            android:textSize="15sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.joinutech.ddbeslibrary.widget.CircleImageView
            android:id="@+id/iv_dept_head_icon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="26dp"
            android:layout_marginTop="10dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_dept_head_name" />

        <TextView
            android:id="@+id/tv_dept_head_empty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="25dp"
            android:layout_marginTop="5dp"
            android:text="未设置人员"
            android:textColor="@color/color999999"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_dept_head_name" />

        <TextView
            android:id="@+id/dept_head_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="2dp"
            android:maxEms="6"
            android:singleLine="true"
            android:textColor="#ff323232"
            android:textSize="12sp"
            app:layout_constraintLeft_toRightOf="@id/iv_dept_head_icon"
            app:layout_constraintTop_toTopOf="@id/iv_dept_head_icon" />

        <ImageView
            android:id="@+id/iv_dept_head_edit_icon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="18dp"
            android:src="@drawable/icon_dep_head_edit"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/dept_head_del"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="37dp"
            android:src="@drawable/icon_dep_head_delete"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@id/iv_dept_head_edit_icon"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/empty"
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:background="@color/background"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>