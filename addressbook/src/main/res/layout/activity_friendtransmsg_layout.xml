<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include
        android:id="@+id/search_inclue_layout"
        layout="@layout/search_include_layout" />

    <LinearLayout
        android:id="@+id/dataLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/createProgramText"
            android:layout_width="match_parent"
            android:layout_height="24dp"
            android:gravity="center"
            android:text="项目创建已完成，请选择项目参与人员"
            android:textColor="#ffff7800"
            android:textSize="@dimen/sp_12"
            android:visibility="gone"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_title_layout"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:background="@color/white"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/select_check"
                android:layout_width="47dp"
                android:layout_height="47dp"
                android:padding="15dp"
                android:src="@drawable/checkbox_friend_selector"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/selectAllMember"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="项目全体成员"
                android:textColor="#ff333333"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@id/select_check"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/main_recycler"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:visibility="visible" />

        <LinearLayout
            android:id="@+id/ll_selected_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible"
            android:paddingStart="@dimen/dp_15"
            android:paddingTop="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_10">

            <TextView
                android:id="@+id/num_sel_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color999999"
                android:textSize="@dimen/sp_12"
                tools:text="已选择" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <!-- android:id="@+id/recycler_selmember" -->
                <!--            <com.joinutech.ddbeslibrary.widget.recyclerview.ScrollRecyclerView-->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_select_list"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/dp_15"
                    android:layout_weight="1" />

                <View
                    android:id="@+id/line_confirm"
                    android:layout_width="@dimen/dp_0_2"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/dp_5"
                    android:layout_marginBottom="@dimen/dp_5"
                    android:background="@color/c_E5E5E5"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tv_confirm"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:paddingStart="@dimen/dp_15"
                    android:paddingEnd="@dimen/dp_15"
                    android:text="确定"
                    android:textColor="@color/main_blue"
                    android:textSize="@dimen/sp_12"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

    <!--默认空白页面-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:ev_content="未找到符合条件的担当好友"
        app:ev_icon="@drawable/ic_empty_friend" />
    <!--    cl_empty_layout-->
</LinearLayout>