<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/background">

    <TextView
        android:id="@+id/tv_group_type_name"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:gravity="center"
        android:text="团队群组"
        android:textColor="@color/color666666"
        android:textSize="@dimen/sp_12"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_list_group"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@id/tv_group_type_name" />
</androidx.constraintlayout.widget.ConstraintLayout>