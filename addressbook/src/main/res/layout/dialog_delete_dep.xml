<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="310dp"
    android:layout_height="155dp"
    android:background="@color/white"
    android:gravity="center"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        >
        <TextView
            android:id="@+id/tv_dept_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            android:text="你确认要删除部门研发部吗？"
            app:layout_constraintStart_toStartOf="parent"
            android:gravity="center"
            android:textColor="#ff333333"
            android:textSize="17sp"
            android:layout_marginEnd="20dp"
            />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/depDecText"
            app:layout_constraintTop_toBottomOf="@id/tv_dept_name"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:text="部门中的员工将被移动到 1 级部门中"
            android:textColor="#ff999999"
            android:textSize="14sp"
            android:layout_marginTop="18dp"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>