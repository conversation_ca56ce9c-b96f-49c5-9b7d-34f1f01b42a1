<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:id="@+id/line_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_none"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:background="@color/white"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:paddingStart="@dimen/dp_14"
        android:paddingEnd="@dimen/dp_30"
        android:singleLine="true"
        android:text="所有成员均不可见，包括本部门内成员"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_14"
        app:layout_constraintTop_toBottomOf="@id/line_top" />

    <ImageView
        android:id="@+id/iv_none"
        android:layout_width="@dimen/dp_14"
        android:layout_height="@dimen/dp_14"
        android:layout_marginEnd="@dimen/dp_14"
        android:src="@drawable/iv_select_right"
        app:layout_constraintBottom_toBottomOf="@id/tv_none"
        app:layout_constraintEnd_toEndOf="@id/tv_none"
        app:layout_constraintTop_toTopOf="@id/tv_none" />

    <View
        android:id="@+id/line_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_5"
        android:background="@color/c_E5E5E5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_none" />

    <LinearLayout
        android:id="@+id/ll_range"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_38"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_14"
        android:paddingEnd="@dimen/dp_14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line_bottom">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:text="可见范围"
            android:textColor="@color/color666666"
            android:textSize="@dimen/sp_12" />

        <TextView
            android:id="@+id/tv_range_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_10"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:singleLine="true" />

        <ImageView
            android:layout_width="@dimen/dp_8"
            android:layout_height="@dimen/dp_14"
            android:src="@drawable/arraw_right" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>