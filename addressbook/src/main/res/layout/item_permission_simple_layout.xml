<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp_0_2"
    android:background="@color/white"
    android:minHeight="@dimen/dp_46"
    android:paddingStart="@dimen/dp_15"
    android:paddingEnd="@dimen/dp_15"
    tools:ignore="MissingDefaultResource">

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_5"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:singleLine="true"
        android:text="全部权限"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/sb_toggle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.kyleduo.switchbutton.SwitchButton
        android:id="@+id/sb_toggle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dp_6"
        app:kswAnimationDuration="300"
        app:kswBackDrawable="@drawable/ios_back_drawable"
        app:kswThumbDrawable="@drawable/ios_thumb_selector"
        app:kswThumbMarginBottom="-8.5dp"
        app:kswThumbMarginLeft="-5dp"
        app:kswThumbMarginRight="-5dp"
        app:kswThumbMarginTop="-2.5dp"
        app:kswThumbRangeRatio="1.4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
