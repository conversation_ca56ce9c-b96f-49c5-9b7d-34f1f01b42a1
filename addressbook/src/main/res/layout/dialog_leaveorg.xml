<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="329dp"
    android:layout_height="166dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/firstText"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="28dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:text="你确认要将此员工请离团队么？"
        android:textColor="#ff333333"
        android:textSize="17sp"
        />
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/secondText"
        app:layout_constraintTop_toBottomOf="@id/firstText"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="23dp"
        android:text="好员工来之不易，请三思"
        android:textColor="#ff999999"
        android:textSize="12sp"
        />
    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="0.2dp"
        android:background="@color/line_grey"
        app:layout_constraintTop_toBottomOf="@id/secondText"
        android:layout_marginTop="24dp"
        />
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="51dp"
        app:layout_constraintTop_toBottomOf="@id/line"
        >
        <View
            android:layout_width="0.2dp"
            android:layout_height="match_parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:background="@color/line_grey"
            />
        <TextView
            android:id="@+id/cancel"
            android:layout_width="0dp"
            app:layout_constraintHorizontal_weight="1"
            android:layout_height="match_parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/confirm"
            android:gravity="center"
            android:text="取消"
            android:textColor="#ff333333"
            android:textSize="17sp"
            />
        <TextView
            android:id="@+id/confirm"
            android:layout_width="0dp"
            android:gravity="center"
            app:layout_constraintLeft_toRightOf="@id/cancel"
            app:layout_constraintRight_toRightOf="parent"
            android:text="确认"
            android:textColor="#ff1ca7fb"
            android:textSize="17sp"
            app:layout_constraintHorizontal_weight="1"
            android:layout_height="match_parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>