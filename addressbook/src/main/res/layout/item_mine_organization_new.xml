<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <include layout="@layout/item_tv" />

    <include
        layout="@layout/item_mine_organization"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/externalCompanyLayout"
        android:layout_width="match_parent"
        android:layout_height="67dp"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone">

        <View
            android:layout_width="match_parent"
            android:layout_height="0.2dp"
            android:background="@color/line_grey"
            app:layout_constraintBottom_toBottomOf="parent" />

        <com.joinutech.ddbeslibrary.widget.CircleImageView
            android:id="@+id/orgLogo"
            android:layout_width="38dp"
            android:layout_height="38dp"
            android:layout_marginStart="14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/nextIv"
            android:layout_width="34dp"
            android:layout_height="40dp"
            android:padding="14dp"
            android:src="@drawable/arraw_right"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/orgName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="#ff333333"
            android:textSize="@dimen/sp_17"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/nextIv"
            app:layout_constraintStart_toEndOf="@id/orgLogo"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>