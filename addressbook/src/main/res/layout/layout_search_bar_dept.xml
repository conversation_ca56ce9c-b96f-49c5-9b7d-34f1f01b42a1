<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_48"
    android:background="@color/white"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_11"
    android:paddingTop="@dimen/dp_10"
    android:paddingEnd="@dimen/dp_12"
    android:paddingBottom="@dimen/dp_10">

    <EditText
        android:id="@+id/et_search_input"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_28"
        android:layout_marginEnd="8dp"
        android:background="@drawable/bg_gray_corner_10"
        android:hint="搜索"
        android:imeOptions="actionSearch"
        android:maxLines="1"
        android:paddingStart="@dimen/dp_30"
        android:paddingEnd="@dimen/dp_30"
        android:singleLine="true"
        android:textColor="@color/text_black"
        android:textColorHint="@color/colorFFAAAAAA"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_cancel"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="@dimen/dp_0" />

    <ImageView
        android:layout_width="@dimen/dp_17"
        android:layout_height="@dimen/dp_17"
        android:layout_marginStart="@dimen/dp_8"
        android:src="@drawable/iconsmallsearch"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_input_clear"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:padding="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_5"
        android:src="@drawable/del_img"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/et_search_input"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="取消"
        android:textSize="@dimen/sp_15"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>