<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/search_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="horizontal"
    android:paddingTop="5dp"
    android:paddingBottom="5dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="29dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="14dp"
        android:layout_marginEnd="@dimen/dp_10"
        android:layout_weight="1"
        android:focusable="true"
        android:focusableInTouchMode="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/cancel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="@dimen/dp_14">

        <EditText
            android:id="@+id/search"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/rounded_membersearch_edit_bg"
            android:drawableLeft="@drawable/iconsmallsearch"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:hint="团队员工姓名"
            android:imeOptions="actionSearch"
            android:maxLines="1"
            android:paddingLeft="10dp"
            android:singleLine="true"
            android:textColor="#000000"
            android:textColorHint="#CBCBCB"
            android:textSize="@dimen/sp_12"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/delete"
            android:layout_width="39dp"
            android:layout_height="29dp"
            android:paddingStart="10dp"
            android:paddingTop="7dp"
            android:paddingEnd="14dp"
            android:paddingBottom="7dp"
            android:src="@drawable/del_img"
            android:visibility="gone"
            app:layout_constraintRight_toRightOf="@id/search"
            app:layout_constraintTop_toTopOf="@id/search"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="14dp"
        android:text="取消"
        android:textColor="@color/color1E87F0"
        android:textSize="@dimen/sp_13"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>