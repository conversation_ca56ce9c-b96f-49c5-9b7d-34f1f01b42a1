<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/background">

    <TextView
        android:id="@+id/tv_group_type_name"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:gravity="center_vertical"
        android:paddingStart="14dp"
        android:paddingEnd="0dp"
        android:textColor="@color/color666666"
        android:textSize="@dimen/sp_12"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_list_group"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@id/tv_group_type_name" />

    <TextView
        android:id="@+id/tv_watch_all"
        android:layout_width="match_parent"
        android:layout_height="29dp"
        android:background="@color/white"
        android:gravity="center"
        android:text="查看全部"
        android:textColor="@color/colorFF323232"
        android:textSize="@dimen/sp_13"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/rv_list_group" />
</androidx.constraintlayout.widget.ConstraintLayout>