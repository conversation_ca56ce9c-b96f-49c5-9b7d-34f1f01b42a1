<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/two_btns_contain_rl"
        android:layout_width="match_parent"
        android:layout_height="47dp"
        android:layout_alignParentBottom="true"
        android:background="@color/white">

        <TextView
            android:id="@+id/tv_import_member"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="导入员工"
            android:textColor="@color/text_black"
            android:textSize="@dimen/sp_15"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_add_dept"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_add_dept"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="添加一个子部门"
            android:textColor="@color/text_black"
            android:textSize="@dimen/sp_15"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@id/tv_import_member"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsv_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/two_btns_contain_rl">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/search"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:background="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tv_search"
                    android:layout_width="match_parent"
                    android:layout_height="29dp"
                    android:layout_marginStart="14dp"
                    android:layout_marginEnd="14dp"
                    android:background="@drawable/rounded_membersearch_edit_bg"
                    android:drawableStart="@drawable/iconsmallsearch"
                    android:drawablePadding="5dp"
                    android:gravity="center_vertical"
                    android:paddingStart="10dp"
                    android:text="搜索"
                    android:textColor="#CBCBCB"
                    android:textSize="@dimen/sp_12"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/empty"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/search">

                <View
                    android:id="@+id/line_empty"
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_level_list"
                    android:layout_width="match_parent"
                    android:layout_height="29dp"
                    android:paddingStart="11dp"
                    android:paddingEnd="11dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/company"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:padding="@dimen/dp_14"
                app:layout_constraintTop_toBottomOf="@id/empty">

                <TextView
                    android:id="@+id/tv_company_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_10"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:textColor="@color/colorFF333333"
                    android:textSize="@dimen/sp_14"
                    app:layout_constraintEnd_toStartOf="@id/iv_dept_set_icon"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed"
                    tools:text="加优科技有限公司" />

                <TextView
                    android:id="@+id/tv_dept_level"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginEnd="@dimen/dp_10"
                    android:textColor="@color/color999999"
                    android:textSize="@dimen/sp_12"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/iv_dept_set_icon"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_company_name"
                    tools:text="部门层级：1级部门" />

                <ImageView
                    android:id="@+id/iv_dept_set_icon"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"
                    android:src="@drawable/icon_org_chart_set"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/company">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tv_sub_dept"
                        android:layout_width="match_parent"
                        android:layout_height="24dp"
                        android:background="@color/background"
                        android:gravity="center"
                        android:text="子部门"
                        android:textColor="#999999"
                        android:textSize="@dimen/sp_15"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_sub_dept_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        app:layout_constraintTop_toBottomOf="@id/tv_sub_dept" />

                    <TextView
                        android:id="@+id/tv_sub_dept_count"
                        android:layout_width="match_parent"
                        android:layout_height="24dp"
                        android:background="@color/background"
                        android:gravity="center"
                        android:paddingStart="12dp"
                        android:textColor="@color/color999999"
                        android:textSize="@dimen/sp_15"
                        app:layout_constraintTop_toBottomOf="@id/rv_sub_dept_list"
                        tools:text="当前部门共有4个成员" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_dept_member_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        app:layout_constraintTop_toBottomOf="@id/tv_sub_dept_count" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </ScrollView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</RelativeLayout>