<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <TextView
        android:id="@+id/listNumText"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:gravity="center_vertical"
        android:paddingStart="14dp"
        android:text="当前部门共有4个成员"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/noSetDepHead"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/listNumText">

        <ImageView
            android:id="@+id/userDefaultIcon"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginStart="14dp"
            android:layout_marginTop="7dp"
            android:src="@drawable/icon_userdefault"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:text="不选择人员"
            android:textColor="#ff333333"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/userDefaultIcon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/item_confirm_iv"
            android:layout_width="17dp"
            android:layout_height="17dp"
            android:layout_centerVertical="true"
            android:layout_marginTop="15dp"
            android:layout_marginEnd="18dp"
            android:src="@drawable/icon_personaddress_confirm"
            android:visibility="gone"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="7dp"
            android:background="@color/line_grey"
            app:layout_constraintTop_toBottomOf="@id/userDefaultIcon" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_dept_member_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toTopOf="@id/nextText"
        app:layout_constraintTop_toBottomOf="@id/noSetDepHead" />

    <TextView
        android:id="@+id/nextText"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:background="@color/white"
        android:gravity="center"
        android:text="下一步"
        android:textColor="@color/color1E87F0"
        android:textSize="@dimen/sp_15"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>