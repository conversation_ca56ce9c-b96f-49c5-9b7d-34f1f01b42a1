<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/tb_top_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:minHeight="?android:attr/actionBarSize"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:title="">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/iv_top_back"
                android:layout_width="37dp"
                android:layout_height="16dp"
                android:src="@drawable/back2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_top_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="15dp"
                android:paddingStart="15dp"
                android:text="取消"
                android:textColor="#ff1e87f0"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/et_top_search"
                android:layout_width="0dp"
                android:layout_height="29dp"
                android:background="@drawable/rounded_membersearch_edit_bg"
                android:hint="请输入团队备注名或手机号"
                android:paddingStart="14dp"
                android:paddingEnd="12dp"
                android:singleLine="true"
                android:textColor="#000000"
                android:textColorHighlight="#999999"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_top_cancel"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toEndOf="@id/iv_top_back"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_top_cancel"
                android:layout_width="40dp"
                android:layout_height="28dp"
                android:paddingStart="12dp"
                android:paddingTop="6dp"
                android:paddingEnd="12dp"
                android:paddingBottom="6dp"
                android:src="@drawable/del_img"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_top_cancel"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/line_grey"
        app:layout_constraintTop_toBottomOf="@id/tb_top_layout" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tb_top_layout" />

    <!--默认空白页面-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:ev_content="没有搜索结果"
        app:ev_icon="@drawable/ic_empty_search"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>