<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <EditText
        android:id="@+id/companyIntroEdit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="start"
        android:hint="请输入团队简介（最多200字）"
        android:minHeight="209dp"
        android:paddingStart="22dp"
        android:paddingTop="19dp"
        android:paddingEnd="22dp"
        android:textColor="@color/text_black"
        android:textColorHint="#aaaaaa"
        android:textSize="@dimen/sp_13"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/companyIntroSave"
        android:layout_width="278dp"
        android:layout_height="36dp"
        android:layout_marginTop="22dp"
        android:background="@color/main_blue"
        android:gravity="center"
        android:text="保存"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/companyIntroEdit" />
</androidx.constraintlayout.widget.ConstraintLayout>