<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:ignore="MissingDefaultResource">

    <TextView
        android:id="@+id/text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginTop="5dp"
        android:paddingBottom="5dp"
        android:text="邀请员工加入团队"
        android:textColor="#808080"
        android:textSize="12sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--邀请好友-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/addOrgLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_42"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@id/text">

        <ImageView
            android:id="@+id/addOrg"
            android:layout_width="21dp"
            android:layout_height="21dp"
            android:layout_marginStart="26dp"
            android:src="@drawable/icon_addorg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:text="通过聊天邀请"
            android:textColor="#000000"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/addOrg"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--邀请微信-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/wxLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_42"
        android:layout_marginTop="@dimen/dp_0_2"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@id/addOrgLayout">

        <ImageView
            android:id="@+id/weixin"
            android:layout_width="21dp"
            android:layout_height="21dp"
            android:layout_marginStart="26dp"
            android:src="@drawable/weixin_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:text="邀请微信好友"
            android:textColor="#000000"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/weixin"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--邀请短信-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_msg_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_42"
        android:layout_marginTop="@dimen/dp_0_2"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@id/wxLayout">

        <ImageView
            android:id="@+id/msg"
            android:layout_width="21dp"
            android:layout_height="21dp"
            android:layout_marginStart="26dp"
            android:src="@drawable/msgicon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:text="通过短信邀请"
            android:textColor="#000000"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/msg"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--邀请二维码-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/qrCodeLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_42"
        android:layout_marginTop="@dimen/dp_0_2"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@id/cl_msg_layout">

        <ImageView
            android:id="@+id/iv_qr_code"
            android:layout_width="21dp"
            android:layout_height="21dp"
            android:layout_marginStart="26dp"
            android:src="@mipmap/icon_qr_code"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:text="二维码邀请"
            android:textColor="#000000"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/iv_qr_code"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>