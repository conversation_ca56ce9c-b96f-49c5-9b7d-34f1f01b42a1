<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/havePersonLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/tb_top_layout"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@color/white"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/leftSearch"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/rightSelect"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableStart="@drawable/search"
                    android:drawablePadding="8dp"
                    android:text="搜索"
                    android:textColor="#ff333333"
                    android:textSize="@dimen/sp_13"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/rightSelect"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toEndOf="@id/leftSearch"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/levelSelect"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableStart="@drawable/icon_notice_select"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:text="重要级别"
                    android:textColor="#ff333333"
                    android:textSize="@dimen/sp_13"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/selectLevelLayout"
            android:layout_width="match_parent"
            android:layout_height="29dp"
            android:background="#DCE7F5"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/tb_top_layout">

            <TextView
                android:id="@+id/selectLevelTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:text="选择级别：重要"
                android:textColor="#ff2479ed"
                android:textSize="@dimen/sp_13"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/cancelSelect"
                android:layout_width="45dp"
                android:layout_height="match_parent"
                android:paddingStart="15dp"
                android:paddingEnd="15dp"
                android:src="@drawable/icon_blue_cancel_with_circle"
                app:layout_constraintEnd_toEndOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--默认空白页面-->
        <com.joinutech.common.widget.PageEmptyView
            android:id="@+id/layout_empty_data"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:ev_content="没有此级别的外部联系人"
            app:ev_icon="@drawable/ic_empty_cooper"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="14dp"
            android:paddingTop="4dp"
            android:paddingEnd="14dp"
            android:paddingBottom="4dp"
            android:textColor="#ff666666"
            android:textSize="@dimen/sp_13"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/selectLevelLayout" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/num" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--默认空白页面-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:ev_content="没有添加的外部联系人"
        app:ev_icon="@drawable/ic_empty_cooper"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>