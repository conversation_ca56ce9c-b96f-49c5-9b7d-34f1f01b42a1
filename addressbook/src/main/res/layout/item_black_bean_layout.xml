<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="53dp"
    android:background="@color/white">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical">

        <com.joinutech.ddbeslibrary.widget.CircleImageView
            android:id="@+id/black_header"
            android:layout_width="38dp"
            android:layout_height="38dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="14dp"
            android:src="@drawable/default_heading" />

        <TextView
            android:id="@+id/tv_black_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@+id/black_header"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="2"
            android:text="张三李四"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_14" />
        <!--移出-->
        <TextView
            android:id="@+id/tv_black_remove"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@color/main_blue"
            android:gravity="center"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:layout_marginRight="25dp"
            android:paddingLeft="7dp"
            android:paddingTop="3dp"
            android:paddingRight="7dp"
            android:paddingBottom="3dp"
            android:text="移出"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_13" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_alignParentBottom="true"
        android:background="@color/line_grey" />
</RelativeLayout>