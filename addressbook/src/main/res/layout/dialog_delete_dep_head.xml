<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="280dp"
    android:layout_height="143dp">

    <TextView
        android:id="@+id/topText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:ellipsize="end"
        android:gravity="center"
        android:lines="2"
        android:text="你确认要删除此部门的\n【研发部副部长】职位吗？"
        android:textColor="@color/color393939"
        android:textSize="@dimen/sp_15"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:text="如果该职位当前有人任职，则该员工将变更为\n本部门的一位普通员工"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topText" />
</androidx.constraintlayout.widget.ConstraintLayout>