<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/srl_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="visible">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_friend_apply"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="13dp"
                    android:text="好友申请"
                    android:textColor="#323232"
                    android:textSize="15sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_friend_apply_tip"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="12dp"
                    android:text="请求添加我为好友的已处理和未处理的请求"
                    android:textColor="#999999"
                    android:textSize="11sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_friend_apply" />

                <!--不套这层RelativeLayout的话，有时会出现列表显示不全的问题-->
                <RelativeLayout
                    android:id="@+id/friend_list_contain_rl"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    app:layout_constraintTop_toBottomOf="@id/tv_friend_apply_tip">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_friend_apply_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:overScrollMode="never" />

                </RelativeLayout>


                <View
                    android:id="@+id/line_friend_apply"
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="#E2E2E2"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@id/friend_list_contain_rl" />

                <TextView
                    android:id="@+id/tv_friend_more_apply"
                    android:layout_width="match_parent"
                    android:layout_height="41dp"
                    android:background="@color/white"
                    android:gravity="center"
                    android:text="查看更多 >>"
                    android:textColor="#999999"
                    android:textSize="12sp"
                    android:visibility="visible"
                    app:layout_constraintTop_toBottomOf="@id/line_friend_apply" />

                <TextView
                    android:id="@+id/tv_org_apply_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="13dp"
                    android:text="团队加入申请"
                    android:textColor="#323232"
                    android:textSize="15sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_friend_more_apply" />

                <TextView
                    android:id="@+id/text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="12dp"
                    android:text="我创建或我管理的团队中已处理、未处理的请求。点击可查看详情"
                    android:textColor="#999999"
                    android:textSize="11sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_org_apply_title" />

                <!--不套这层RelativeLayout的话，有时会出现列表显示不全的问题-->
                <RelativeLayout
                    android:id="@+id/company_list_contain_rl"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    app:layout_constraintTop_toBottomOf="@id/text">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_org_apply_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:overScrollMode="never" />

                </RelativeLayout>


                <View
                    android:id="@+id/line_org_apply"
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="#E2E2E2"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@id/company_list_contain_rl" />

                <TextView
                    android:id="@+id/apply_external_title_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="13dp"
                    android:text="外部协作人邀请"
                    android:textColor="#323232"
                    android:textSize="15sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/line_org_apply" />

                <TextView
                    android:id="@+id/apply_external_des_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="12dp"
                    android:text="邀请我为外部协作人的未处理申请，点击可查看详情"
                    android:textColor="#999999"
                    android:textSize="11sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/apply_external_title_tv" />
                <!--不套这层RelativeLayout的话，有时会出现列表显示不全的问题-->
                <RelativeLayout
                    android:id="@+id/external_list_contain_rl"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    app:layout_constraintTop_toBottomOf="@id/apply_external_des_tv">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/apply_external_recycler"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:background="@color/white"
                        android:overScrollMode="never" />

                </RelativeLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>
        </ScrollView>
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <!--默认空白页面-->
    <com.joinutech.common.widget.PageEmptyView
        android:id="@+id/layout_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:ev_content="暂无待处理的申请"
        app:ev_icon="@drawable/ic_empty_friend"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>