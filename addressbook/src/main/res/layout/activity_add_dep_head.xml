<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/depHeadNameLayout"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginTop="9dp"
        android:background="@color/white"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:paddingStart="12dp"
            android:text="名称"
            android:textColor="#ff333333"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/depHeadNameEdit"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:background="@color/white"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLength="10"
            android:paddingEnd="19dp"
            android:singleLine="true"
            android:text="部门负责人"
            android:textColor="#ff333333"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/hintText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:paddingEnd="19dp"
            android:text="部门负责人"
            android:textColor="#ffaaaaaa"
            android:textSize="14sp" />
    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/depHeadLayout"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginTop="10dp"
        android:background="@color/white"
        android:visibility="visible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/depHeadNameLayout">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="12dp"
            android:text="人员"
            android:textColor="#ff333333"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/depHeadNext"
            android:layout_width="33dp"
            android:layout_height="14dp"
            android:paddingStart="9dp"
            android:paddingEnd="16dp"
            android:src="@drawable/arraw_right"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_dept_head_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="未设置人员"
            android:textColor="#ffaaaaaa"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@id/depHeadNext"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>