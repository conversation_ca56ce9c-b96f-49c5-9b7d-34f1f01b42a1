<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/dp_14">

    <TextView
        android:id="@+id/tv_friend_apply"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="好友申请"
        android:textColor="@color/colorFF333333"
        android:textSize="@dimen/sp_15"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_friend_apply_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:text="请求添加我为好友的已处理和未处理的请求"
        android:textColor="@color/color999999"
        android:textSize="@dimen/sp_12"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_friend_apply" />
</androidx.constraintlayout.widget.ConstraintLayout>