<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:orientation="vertical">

<!--上面内容-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_title_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingBottom="15dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_al_set"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:paddingStart="@dimen/dp_12"
            android:text="你已被设置为管理员"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_17"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_manage_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20"
            android:paddingStart="@dimen/dp_12"
            android:text="管理员名称："
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_14"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_al_set" />

        <ImageView
            android:id="@+id/iv_per_icon"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginStart="6dp"
            android:src="@drawable/icon_super_permission_tag"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_manage_title"
            app:layout_constraintStart_toEndOf="@id/tv_manage_title"
            app:layout_constraintTop_toTopOf="@id/tv_manage_title"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_manage_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20"
            android:paddingStart="@dimen/dp_10"
            android:paddingEnd="@dimen/dp_12"
            android:text="世界上近三年"
            android:textColor="#ff333333"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_per_icon"
            app:layout_constraintTop_toBottomOf="@id/tv_al_set"
            app:layout_goneMarginStart="0dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>

<!--权限列表和空显示-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toBottomOf="@id/cl_title_layout">

        <TextView
            android:id="@+id/tv_per_title"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_34"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_14"
            android:text="权限"
            android:textColor="@color/colorFF333333"
            android:textSize="@dimen/sp_14"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_per_title" />

        <ImageView
            android:id="@+id/iv_no_per"
            android:layout_width="40dp"
            android:layout_height="46dp"
            android:layout_marginTop="@dimen/dp_55"
            android:src="@drawable/icon_no_permission_notice"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_no_per"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:text="管理员暂未分配权限"
            android:textColor="#ffa3b2bf"
            android:textSize="@dimen/sp_14"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_no_per" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>