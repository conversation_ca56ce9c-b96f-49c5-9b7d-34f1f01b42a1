<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:ignore="MissingDefaultResource">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_search_friend"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/search"
            android:layout_width="match_parent"
            android:layout_height="29dp"
            android:layout_marginStart="14dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="14dp"
            android:background="@drawable/rounded_membersearch_edit_bg"
            android:drawableLeft="@drawable/iconsmallsearch"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:paddingStart="10dp"
            android:text="搜索担当好友"
            android:textColor="#CBCBCB"
            android:textSize="12sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/scanLayout"
        android:layout_width="match_parent"
        android:layout_height="42dp"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@id/cl_search_friend">

        <ImageView
            android:id="@+id/scanIv"
            android:layout_width="21dp"
            android:layout_height="21dp"
            android:layout_marginStart="26dp"
            android:src="@drawable/icon_phonescan"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:text="扫一扫"
            android:textColor="#ff000000"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/scanIv"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/phoneAddressLayout"
        android:layout_width="match_parent"
        android:layout_height="42dp"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@id/scanLayout">

        <ImageView
            android:id="@+id/phoneAddressIv"
            android:layout_width="21dp"
            android:layout_height="21dp"
            android:layout_marginStart="26dp"
            android:src="@drawable/icon_phoneaddress"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:layout_marginTop="3dp"
            android:text="通过手机通讯录添加"
            android:textColor="#ff000000"
            android:textSize="14sp"
            app:layout_constraintLeft_toRightOf="@id/phoneAddressIv"
            app:layout_constraintTop_toTopOf="@id/phoneAddressIv" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>