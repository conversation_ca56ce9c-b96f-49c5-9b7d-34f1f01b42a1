<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    >
    <!-- &lt;!&ndash; 获取位置 &ndash;&gt; -->
    <!-- <permission-group android:name="android.permission-group.LOCATION" /> -->
    <!-- <uses-permission android:name="android.permission.BAIDU_LOCATION_SERVICE" /> -->
    <!-- android 9.0上使用前台服务，需要添加权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- 这个权限用于进行网络定位 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- 这个权限用于访问GPS定位 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- 用于访问wifi网络信息，wifi信息会用于进行网络定位 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 获取运营商信息，用于支持提供运营商信息相关的接口 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 这个权限用于获取wifi的获取权限，wifi信息会用来进行网络定位 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- 写入扩展存储，向扩展卡写入数据，用于写入离线定位数据 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- 访问网络，网络定位需要上网 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- android10开始不能通过此权限获取IMEI -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission
        android:name="android.permission.READ_LOGS"
        tools:ignore="ProtectedPermissions" /> <!-- 读取系统信息，包含系统版本等信息，用作统计 -->
    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" /> <!-- 扫码添加好友等摄像头功能 -->
    <uses-permission android:name="android.permission.CAMERA" /> <!-- 录音 -->
    <uses-permission android:name="android.permission.FLASHLIGHT" /> <!-- 闪光灯 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" /> <!-- 控制振动器 -->
    <uses-permission android:name="android.permission.VIBRATE" /> <!-- 防止设备休眠 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" /> <!-- 获取联系人信息，demo中演示发送通讯录消息 -->
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- 如果是安卓8.0，应用编译配置的targetSdkVersion>=26，请务必添加以下权限 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />



    <!--<uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>-->

    <permission
        android:name="com.joinutech.ddbeslibrary.permission.C2D_MESSAGE"
        android:protectionLevel="signature" />
    <uses-permission android:name="com.joinutech.ddbeslibrary.permission.C2D_MESSAGE" />


    <queries>
        <package android:name="com.autonavi.minimap" />
        <package android:name="com.baidu.BaiduMap" />
        <package android:name="com.tencent.map" />
    </queries>


    <application
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:resizeableActivity="false"
        android:usesCleartextTraffic="true"
        tools:targetApi="n">

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />



        <service
            android:name="com.baidu.location.f"
            android:enabled="true"
            android:process=":remote" /> <!-- 支持高宽高比屏幕 -->
        <meta-data
            android:name="android.max_aspect"
            android:value="2.4" />
        <!--
 如果您项目中的所有页面都只需要以高或宽中的一个作为基准进行适配的话,
        那就只需要填写高或宽中的一个设计图尺寸即可
        -->
        <meta-data
            android:name="design_width_in_dp"
            android:value="375" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="812" /> <!-- 适配华为（huawei）刘海屏 -->
        <meta-data
            android:name="android.notch_support"
            android:value="true" /> <!-- 支持小米刘海屏 -->
        <meta-data
            android:name="notch.config"
            android:value="portrait" />
        <meta-data
            android:name="BUGRPT_APPID"
            android:value="A001712217" /> <!-- 图片缓存配置 -->
        <!-- <meta-data -->
        <!-- android:name="com.joinutech.ddbeslibrary.utils.JoinUGlideAppModule" -->
        <!-- android:value="GlideModule" /> -->
        <activity
            android:name=".widget.activity.WithToolBarImagePreviewActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" /> <!-- 任务图片大图浏览 -->
        <activity
            android:name=".widget.activity.TaskImagePreviewActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" /> <!-- <receiver android:name=".receiver.DDBGIMReceiver"> -->
        <!-- <intent-filter> -->
        <!-- <action android:name="ddbgIMMsgReceive" /> -->
        <!-- </intent-filter> -->
        <!-- </receiver> -->
        <!-- <receiver android:name=".receiver.OnclickMessageReceiver"> -->
        <!-- <intent-filter> -->
        <!-- <action android:name="clickMessage" /> -->
        <!-- </intent-filter> -->
        <!-- </receiver> -->
        <service
            android:name=".utils.DownloadService"
            android:enabled="true"
            android:process=":DownloadService" /> <!-- android:process=".utils.DownloadService" /> -->
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <activity
            android:name=".base.NoticeWebActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.joinutech.common.base.EnvChangeActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".base.FilePreviewTestActivity"
            android:configChanges="orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".base.CommonWebActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustPan|stateHidden" />

<!--        &lt;!&ndash;小米推送&ndash;&gt;-->
<!--        <receiver-->
<!--            android:name="com.joinutech.common.util.pushReceivers.MiPushReceiver"-->
<!--            android:exported="true">-->
<!--            &lt;!&ndash;这里com.xiaomi.mipushdemo.DemoMessageRreceiver改成app中定义的完整类名&ndash;&gt;-->
<!--            <intent-filter>-->
<!--                <action android:name="com.xiaomi.mipush.RECEIVE_MESSAGE" />-->
<!--            </intent-filter>-->
<!--            <intent-filter>-->
<!--                <action android:name="com.xiaomi.mipush.MESSAGE_ARRIVED" />-->
<!--            </intent-filter>-->
<!--            <intent-filter>-->
<!--                <action android:name="com.xiaomi.mipush.ERROR" />-->
<!--            </intent-filter>-->
<!--        </receiver>-->

<!--        &lt;!&ndash;华为推送接收消息的服务，华为推送&ndash;&gt;-->
<!--        <service-->
<!--            android:name="com.joinutech.factory_push.pushReceivers.HuaWeiPushService"-->
<!--            android:directBootAware="true"-->
<!--            android:exported="false">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />-->
<!--            </intent-filter>-->
<!--        </service>-->

<!--        &lt;!&ndash;oppo推送1&ndash;&gt;-->
<!--        <service-->
<!--            android:name="com.joinutech.common.util.pushReceivers.AppPushMessageService"-->
<!--            android:exported="true"-->
<!--            android:permission="com.heytap.mcs.permission.SEND_PUSH_MESSAGE">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.heytap.mcs.action.RECEIVE_MCS_MESSAGE" />-->
<!--                <action android:name="com.heytap.msp.push.RECEIVE_MCS_MESSAGE" />-->
<!--            </intent-filter>-->
<!--        </service>-->

<!--        &lt;!&ndash;oppo推送2&ndash;&gt;-->
<!--        <service-->
<!--            android:name="com.joinutech.common.util.pushReceivers.PushMessageService"-->
<!--            android:exported="true"-->
<!--            android:permission="com.coloros.mcs.permission.SEND_MCS_MESSAGE">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.coloros.mcs.action.RECEIVE_MCS_MESSAGE" />-->
<!--            </intent-filter>-->
<!--        </service>-->




<!--        &lt;!&ndash; 魅族推送&ndash;&gt;-->
<!--        <receiver android:name="com.joinutech.common.util.pushReceivers.MeiZuPushReceiver">-->
<!--            <intent-filter>-->
<!--                &lt;!&ndash; 接收 push 消息 &ndash;&gt;-->
<!--                <action android:name="com.meizu.flyme.push.intent.MESSAGE" />-->
<!--                &lt;!&ndash; 接收 register 消息 &ndash;&gt;-->
<!--                <action android:name="com.meizu.flyme.push.intent.REGISTER.FEEDBACK" />-->
<!--                &lt;!&ndash; 接收 unregister 消息&ndash;&gt;-->
<!--                <action android:name="com.meizu.flyme.push.intent.UNREGISTER.FEEDBACK" />-->
<!--                &lt;!&ndash; 兼容低版本 Flyme3 推送服务配置 &ndash;&gt;-->
<!--                <action android:name="com.meizu.c2dm.intent.REGISTRATION" />-->
<!--                <action android:name="com.meizu.c2dm.intent.RECEIVE" />-->

<!--                <category android:name="com.joinutech.ddbeslibrary" />-->
<!--            </intent-filter>-->
<!--        </receiver>-->

    </application>

</manifest>