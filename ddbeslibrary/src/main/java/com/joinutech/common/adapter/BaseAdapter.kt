package com.joinutech.common.adapter

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView

/**
 * Description RecycleView的BaseAdapter
 * Author HJR36
 * Date 2018/6/13 16:26
 */
abstract class BaseAdapter<T>(list: List<T>?, context: Context)
    : RecyclerView.Adapter<BaseAdapter.BaseViewHolder>() {

    private var onItemClickListener: OnItemClickListener? = null

    private var onItemLongClickListener: OnItemLongClickListener? = null

    var mList: List<T>? = list

    var mContext = context

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        return BaseViewHolder(creatView(parent))
    }

    override fun getItemCount(): Int {
        return if (mList == null) 0 else mList!!.size
    }

    override fun onBindViewHolder(holder: <PERSON>ViewHolder, position: Int) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener { v ->
                onItemClickListener?.onItemClick(v, holder.layoutPosition)
            }
        }
        if (onItemLongClickListener != null) {
            holder.itemView.setOnLongClickListener { v ->
                onItemLongClickListener!!.onItemLongClick(v, holder.layoutPosition)
            }
        }
        if (mList?.size!! > 0)
            updateView(holder, position)
    }

    abstract fun setDataSourceList(list: List<T>?)

    abstract fun addMoreData(t: T)

    abstract fun creatView(parent: ViewGroup): View?

    abstract fun updateView(holder: BaseViewHolder, position: Int)


    class BaseViewHolder(itemView: View?) : RecyclerView.ViewHolder(itemView!!)

    interface OnItemClickListener {
        fun onItemClick(view: View, position: Int)
    }

    interface OnItemLongClickListener {
        fun onItemLongClick(view: View, position: Int): Boolean
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        this.onItemClickListener = onItemClickListener
    }

    fun setOnItemLongClickListener(onItemLongClickListener: OnItemLongClickListener) {
        this.onItemLongClickListener = onItemLongClickListener
    }
}