package com.joinutech.common.adapter.impl

import android.content.Context
import android.widget.TextView
import com.joinutech.ddbeslibrary.bean.Branch
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.common.adapter.support.ViewHolder
import com.joinutech.common.adapter.CommonAdapter
import com.joinutech.ddbeslibrary.R

class DepLevelListAdapter(context: Context, data: ArrayList<Branch>)
    : CommonAdapter<Branch>(context, data, R.layout.item_level_hon_list) {

    private var listner: ItemClickListener? = null

    override fun bindData(holder: ViewHolder, data: Branch, position: Int) {
        val view = holder.getView<TextView>(R.id.tv_level_name)
        if (mData.isNotEmpty()) {
            if (position == mData.size - 1) {
                view.setTextColor(CommonUtils.getColor(mContext, R.color.colorFF333333))
            } else {
                view.setTextColor(CommonUtils.getColor(mContext, R.color.color1E87F0))
            }
            if (position == 0) {
                view.text = data.name
            } else {
                view.text = " >${data.name}"
            }
        }
        if (position < mData.lastIndex) {
            holder.setOnItemClickListener {
                if (listner != null) listner?.onItemClick(holder.adapterPosition)
            }
        }
    }

    interface ItemClickListener {
        fun onItemClick(position: Int)
    }

    fun setClickListener(listner: ItemClickListener) {
        this.listner = listner
    }
}