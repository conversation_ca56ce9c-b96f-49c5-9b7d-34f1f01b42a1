package com.joinutech.common.adapter

import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.ddbeslibrary.utils.OnNoDoubleClickListener
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils


/**
 * @className: RvAdapter
 * @desc: RecyclerView adapter 基类
 * @author: zyy
 * @date: 2019/8/1 10:43
 * @company: joinUTech
 * @leader: ke
 */
class MyHolder<T>(itemView: View) : RecyclerView.ViewHolder(itemView) {
    fun bindData(data: T) {
    }

    fun setText(viewId: Int, info: String): MyHolder<T> {
        itemView.findViewById<TextView>(viewId).text = info
        return this
    }

    fun setText(viewId: Int, resId: Int): MyHolder<T> {
        itemView.findViewById<TextView>(viewId).setText(resId)
        return this
    }

    fun setImage(viewId: Int, resId: Int): MyHolder<T> {
        itemView.findViewById<ImageView>(viewId).setImageResource(resId)
        return this
    }

    fun setImage(viewId: Int, url: String): MyHolder<T> {
        ImageLoaderUtils.loadImage(itemView.context, itemView.findViewById(viewId), url)
        return this
    }

    fun showView(vararg viewIds: Int): MyHolder<T> {
        if (viewIds.isNotEmpty()) {
            viewIds.forEach {
                itemView.findViewById<View>(it).visibility = View.VISIBLE
            }
        }
        return this
    }

    fun unShowView(vararg viewIds: Int): MyHolder<T> {
        if (viewIds.isNotEmpty()) {
            viewIds.forEach {
                itemView.findViewById<View>(it).visibility = View.INVISIBLE
            }
        }
        return this
    }

    fun hideView(vararg viewIds: Int): MyHolder<T> {
        if (viewIds.isNotEmpty()) {
            viewIds.forEach {
                itemView.findViewById<View>(it).visibility = View.GONE
            }
        }
        return this
    }
}

/**拖动监听*/
interface OnListDragCheckListener {
    fun isSwapable(fromPosition: Int, targetPosition: Int): Boolean
}

/**拖动监听*/
interface OnListDragListener {
    fun onStartDrag(viewHolder: RecyclerView.ViewHolder)
    fun onItemSwap(fromPosition: Int, targetPosition: Int)
    fun onItemMoved(position: Int)
}

/**拖动回调*/
class ItemCallBack(val listener: OnListDragListener) : ItemTouchHelper.Callback() {
    override fun getMovementFlags(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder): Int {
        val dragFlag: Int
        val swipeFlags: Int
        //如果是表格布局，则可以上下左右的拖动，但是不能滑动
        if (recyclerView.layoutManager is GridLayoutManager) {
            dragFlag = ItemTouchHelper.UP or
                    ItemTouchHelper.DOWN or
                    ItemTouchHelper.LEFT or
                    ItemTouchHelper.RIGHT or
                    ItemTouchHelper.START or
                    ItemTouchHelper.END
            swipeFlags = 0
        } else {
            //如果是线性布局，那么只能上下拖动，只能左右滑动
            dragFlag = ItemTouchHelper.UP or ItemTouchHelper.DOWN
//            swipeFlags = ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT
            swipeFlags = 0
        }
        //通过makeMovementFlags生成最终结果
        return makeMovementFlags(dragFlag, swipeFlags)
    }

    override fun onMove(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder): Boolean {
        //被拖动的item位置
        val fromPosition = viewHolder.layoutPosition
        //他的目标位置
        val targetPosition = target.layoutPosition
        //为了降低耦合，使用接口让Adapter去实现交换功能
        listener.onItemSwap(fromPosition, targetPosition)
        return true
    }

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
        listener.onItemMoved(viewHolder.layoutPosition)
    }

    override fun isLongPressDragEnabled(): Boolean {
        return false
    }

    override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
        super.onSelectedChanged(viewHolder, actionState)
        /**拖拽开始时，设置背景色*/
        if (actionState != ItemTouchHelper.ACTION_STATE_IDLE) {
            viewHolder?.itemView?.setBackgroundColor(Color.WHITE)
        }
    }

    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        /**松开手指时，恢复背景色*/
        viewHolder.itemView.setBackgroundColor(Color.WHITE)
        super.clearView(recyclerView, viewHolder)
    }
}

class MyDragAdapter<T>(
        val context: Context,
        private val layoutId: Int,
        private val dragViewId: Int,
        private val data: MutableList<T>,
        val onBindItem: (holder: MyHolder<T>, position: Int, data: T, itemView: View) -> Unit,
        val onItemClick: (position: Int, data: T, itemView: View) -> Unit,
        val listener: OnListDragCheckListener? = null
) : RecyclerView.Adapter<MyHolder<T>>(), OnListDragListener {
    private lateinit var itemTouchHelper: ItemTouchHelper
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyHolder<T> {
//        return MyHolder(LayoutInflater.from(context).inflate(layoutId, parent, false))
        val itemView = LayoutInflater.from(context).inflate(layoutId, parent, false)
        return MyHolder(itemView)
    }

    override fun getItemCount(): Int {
        return data.size
    }

    override fun onBindViewHolder(holder: MyHolder<T>, position: Int) {
        holder.itemView.tag = position
        holder.itemView.setOnClickListener {
            if (it.tag != null && it.tag is Int && (it.tag as Int) in data.indices) {
                val index = it.tag as Int
                onItemClick(index, data[index], holder.itemView)
            } else {
                if (position in data.indices) {
                    onItemClick(position, data[position], holder.itemView)
                }
            }
        }
        holder.bindData(data[position])
        onBindItem(holder, position, data[position], holder.itemView)
        holder.itemView.findViewById<View>(dragViewId)?.let {
            if (data.size > 1) {
                it.visibility = View.VISIBLE
                it.setOnTouchListener { _, event ->
                    if (event.action == MotionEvent.ACTION_DOWN) {
                        onStartDrag(holder)
                    }
                    true
                }
            } else {
                it.visibility = View.GONE
            }
        }
    }

    fun getData(position: Int): T? {
        return if (position in data.indices) {
            data[position]
        } else {
            null
        }
    }

    fun attachDragEvent(rvList: RecyclerView) {
        itemTouchHelper = ItemTouchHelper(ItemCallBack(this))
        itemTouchHelper.attachToRecyclerView(rvList)
    }

    override fun onStartDrag(viewHolder: RecyclerView.ViewHolder) {
        itemTouchHelper.startDrag(viewHolder)
    }

    override fun onItemSwap(fromPosition: Int, targetPosition: Int) {
        if (listener == null || (listener.isSwapable(fromPosition, targetPosition))) {
            val temp = data[targetPosition]
            data[targetPosition] = data[fromPosition]
            data[fromPosition] = temp
            notifyItemMoved(fromPosition, targetPosition)
        }
    }

    override fun onItemMoved(position: Int) {
        /**滑动删除项*/
    }
}

open class MyAdapter<T>(
        val context: Context,
        private val layoutId: Int,
        private val mData: MutableList<T>,
        val onBindItem: (position: Int, data: T, itemView: View) -> Unit,
        val onItemClick: (position: Int, data: T, itemView: View) -> Unit
) : RecyclerView.Adapter<MyHolder<T>>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyHolder<T> {
//        return MyHolder(LayoutInflater.from(context).inflate(layoutId, parent, false))
        val itemView = LayoutInflater.from(context).inflate(layoutId, parent, false)
        val holder = MyHolder<T>(itemView)
        return holder
    }

    override fun getItemCount(): Int {
        return mData.size
    }

    override fun onBindViewHolder(holder: MyHolder<T>, position: Int) {
        holder.itemView.setOnClickListener { onItemClick(position, mData[position], holder.itemView) }
        holder.bindData(mData[position])
        onBindItem(position, mData[position], holder.itemView)
    }
}

open class MyAdapter2<T>(
        val context: Context,
        private val layoutId: Int,
        private val mData: MutableList<T>,
        val onBindItem: (position: Int, data: T, holder: MyHolder<T>) -> Unit,
        val onItemClick: (position: Int, data: T, itemView: View) -> Unit
) : RecyclerView.Adapter<MyHolder<T>>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyHolder<T> {
//        return MyHolder(LayoutInflater.from(context).inflate(layoutId, parent, false))
        val itemView = LayoutInflater.from(context).inflate(layoutId, parent, false)
        return MyHolder<T>(itemView)
    }

    override fun getItemCount(): Int {
        return mData.size
    }

    override fun onBindViewHolder(holder: MyHolder<T>, position: Int) {
        holder.itemView.setOnClickListener { onItemClick(position, mData[position], holder.itemView) }
        holder.bindData(mData[position])
        onBindItem(position, mData[position], holder)
    }

}

open class MultiTypeAdapter<T>(
    val activity: Activity,
    private val data: MutableList<T>,
    val generateViewType: (position: Int, data: T) -> Int,//根据position和数据计算得出ViewType
    val generateLayoutId: (viewType: Int) -> Int,//根据上面的ViewType得出一个布局的id
    val onBindItem: (position: Int, data: T, itemView: View) -> Unit,
    val onItemClick: (position: Int, data: T, itemView: View) -> Unit
) : RecyclerView.Adapter<MyHolder<T>>() {

    override fun getItemViewType(position: Int): Int {
        return generateViewType(position, data[position])
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyHolder<T> {
        val itemView = LayoutInflater.from(activity).inflate(generateLayoutId(viewType), parent, false)
        itemView.tag = viewType
        return MyHolder(itemView)
    }

    override fun getItemCount(): Int {
        return data.size
    }

    override fun onBindViewHolder(holder: MyHolder<T>, position: Int) {
        holder.itemView.setOnClickListener(object : OnNoDoubleClickListener {
            override fun onNoDoubleClick(v: View) {
                onItemClick(position, data[position], holder.itemView)
            }
        })
        holder.bindData(data[position])
        onBindItem(position, data[position], holder.itemView)
    }

}

//不复用的recycleView解决包含edittext无法复制问题
open class MultiTypeAdapterNoReuse<T>(
    val activity: Activity,
    private val data: MutableList<T>,
    val generateViewType: (position: Int, data: T) -> Int,
    val generateLayoutId: (viewType: Int) -> Int,
    val onBindItem: (position: Int, data: T, itemView: View) -> Unit,
    val onItemClick: (position: Int, data: T, itemView: View) -> Unit
) : RecyclerView.Adapter<MyHolder<T>>() {

    override fun getItemViewType(position: Int): Int {
        return generateViewType(position, data[position])
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyHolder<T> {
        val itemView = LayoutInflater.from(activity).inflate(generateLayoutId(viewType), parent, false)
        val holder = MyHolder<T>(itemView)
        return holder
    }

    override fun getItemCount(): Int {
        return data.size
    }

    override fun onBindViewHolder(holder: MyHolder<T>, position: Int) {
        /* 强制关闭复用，以解决EditText被复用后长按无法弹出ContextMenu的问题 */
        holder.setIsRecyclable(false)
        holder.itemView.setOnClickListener { onItemClick(position, data[position], holder.itemView) }
        holder.bindData(data[position])
        onBindItem(position, data[position], holder.itemView)
    }

}

