package com.joinutech.common.util

import android.animation.Animator
import android.content.Context
import android.os.Build
import android.view.Gravity
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatSeekBar
import androidx.constraintlayout.widget.ConstraintLayout
import com.bumptech.glide.request.RequestOptions
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.utils.BottomDialogUtil
import com.joinutech.ddbeslibrary.utils.OnNoDoubleClickListener
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.marktoo.lib.cachedweb.LogUtil

/**
 * @PackageName: com.joinutech.common.util
 * @ClassName: ImageVerifyDialog
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/10/12 16:31
 * @Desc: //TODO 图片验证码 工具类
 */

interface ImageVerifyTouchListener {
    fun onRefreshImage()
    fun onCheckImage(backId: String, topId: String, scale: Double)
}

class ImageVerifyDialog private constructor(val context: Context) {

    class Builder {
        private var mContext: Context? = null
        fun with(context: Context): Builder {
            mContext = context
            return this
        }

        private var touchListener: ImageVerifyTouchListener? = null

        fun addTouchListener(touchListener: ImageVerifyTouchListener): Builder {
            this.touchListener = touchListener
            return this
        }

        fun build(): ImageVerifyDialog? {
            if (mContext != null) {
                val dialog = ImageVerifyDialog(mContext!!)
                dialog.touchListener = touchListener
                return dialog
            }
            return null
        }
    }

    private var touchListener: ImageVerifyTouchListener? = null
    private var dialog: AlertDialog? = null
    private lateinit var bigIv: ImageView
    private lateinit var smallIv: ImageView
    private lateinit var seekBar: AppCompatSeekBar
    private lateinit var verifySuccessIv: ImageView
    private lateinit var fileText: TextView
    private lateinit var maskIng: View
    private var layoutParams: ConstraintLayout.LayoutParams? = null

    private var isDismiss = false

    fun show(backUrl: String, topUrl: String) {
        if (dialog == null) {
            val view = View.inflate(context, R.layout.dialog_image_verify, null)
            dialog = BottomDialogUtil.showBottomDialog(context, view, Gravity.CENTER)
            dialog?.setOnDismissListener {
                isDismiss = true
            }
            bigIv = view.findViewById(R.id.bigIv)
            smallIv = view.findViewById(R.id.smallIv)
            seekBar = view.findViewById(R.id.seekBar)
            fileText = view.findViewById(R.id.fileText)
            maskIng = view.findViewById(R.id.maskIng)
            verifySuccessIv = view.findViewById(R.id.verifySuccessIv)
            val refresh = view.findViewById<ImageView>(R.id.refresh)
            refresh.setOnClickListener(object : OnNoDoubleClickListener {
                override fun onNoDoubleClick(v: View) {
                    //点击刷新按钮，启动动画
                    v.animate().rotationBy(360f).setDuration(500)
                            .setInterpolator(AccelerateDecelerateInterpolator())
                            .setListener(object : Animator.AnimatorListener {
                                override fun onAnimationStart(animation: Animator) {}
                                override fun onAnimationEnd(animation: Animator) {
                                    touchListener?.onRefreshImage()
                                }

                                override fun onAnimationCancel(animation: Animator) {}
                                override fun onAnimationRepeat(animation: Animator) {}
                            })
                }
            })
            seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                    layoutParams = smallIv.layoutParams as ConstraintLayout.LayoutParams
                    val width: Int = bigIv.width
                    val smallWidth: Int = smallIv.width
                    val differenceValue = progress * 0.01 * width
                    val value: Int = if (differenceValue > width - smallWidth) {
                        width - smallWidth
                    } else {
                        differenceValue.toInt()
                    }
                    layoutParams?.leftMargin = value
                    smallIv.layoutParams = layoutParams
                }

                override fun onStartTrackingTouch(seekBar: SeekBar?) {
                }

                override fun onStopTrackingTouch(seekBar: SeekBar?) {
                    val progress = seekBar!!.progress
                    LogUtil.showLog("图片验证码拖动进度：：$progress")
                    val width: Int = bigIv.width
                    val smallWidth: Int = smallIv.width
                    val differenceValue = progress * 0.01 * width
                    val scale: Double = if (differenceValue > width - smallWidth) {
                        (width - smallWidth) * 1.00 / width
                    } else {
                        differenceValue / width
                    }
                    val backId = if (StringUtils.isNotBlankAndEmpty(backUrl)) {
                        val dotIndex = backUrl.lastIndexOf(".")
                        val lineIndex = backUrl.lastIndexOf("/")
                        backUrl.substring(lineIndex + 1, dotIndex)
                    } else {
                        ""
                    }
                    val topId = if (StringUtils.isNotBlankAndEmpty(topUrl)) {
                        val dotIndex = topUrl.lastIndexOf(".")
                        val lineIndex = topUrl.lastIndexOf("/")
                        topUrl.substring(lineIndex + 1, dotIndex)
                    } else {
                        ""
                    }
                    touchListener?.onCheckImage(backId, topId, scale)
                }
            })
        } else {
            if (isDismiss) {
                isDismiss = true
                dialog?.show()
            }
        }
        loadImageVerifyIv(context, backUrl, topUrl, bigIv, smallIv)
        reset()
    }

    fun onVerifySuccess(result: () -> Unit) {
        maskIng.visibility = View.VISIBLE
        verifySuccessIv.visibility = View.VISIBLE
        maskIng.postDelayed({
            result.invoke()
        }, 1500)
    }

    fun onVerifyFailed() {
        fileText.visibility = View.VISIBLE
        fileText.postDelayed({
            fileText.visibility = View.GONE
        }, 1000)
    }

    private fun loadImageVerifyIv(context: Context, backUrl: String, topUrl: String,
                                  bigIv: ImageView, smallIv: ImageView) {
        if (StringUtils.isNotBlankAndEmpty(backUrl)) {
            val options = RequestOptions
                    .placeholderOf(R.drawable.image_placeholder_im)
                    .error(R.drawable.image_placeholder_im)
                    .centerCrop()
            ImageLoaderUtils.showImgWithOption(context, backUrl, bigIv, options)
        }
        if (StringUtils.isNotBlankAndEmpty(topUrl)) {
            ImageLoaderUtils.loadImage(context, smallIv, topUrl)
        }
    }

    fun reset() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            seekBar.setProgress(0, true)
        } else {
            seekBar.progress = 0
        }
        if (layoutParams == null) {
            layoutParams = smallIv.layoutParams as ConstraintLayout.LayoutParams
        }
        layoutParams?.leftMargin = 0
        smallIv.layoutParams = layoutParams
    }

    fun hide() {
        if (dialog != null) {
            dialog?.dismiss()
            dialog = null
        }
    }
}