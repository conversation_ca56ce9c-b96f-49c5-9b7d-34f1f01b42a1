package com.joinutech.common.util

import android.annotation.SuppressLint
import android.os.CountDownTimer

/**
 * <AUTHOR>
 * @date   2019/4/25 11:20
 * @className: CountDownTimerUtils
 *@Description: 类作用描述
 * 倒计时工具类通用化，增加计时过程和计时结束回调
 */
class CountDownTimerUtils(millisInFuture: Long, countDownInterval: Long,
                          val onTick: (tick: Long) -> Unit,
                          val onFinish: () -> Unit)
    : CountDownTimer(millisInFuture, countDownInterval) {

    override fun onFinish() {
        onFinish.invoke()
    }

    @SuppressLint("SetTextI18n")
    override fun onTick(millisUntilFinished: Long) {
        onTick.invoke(millisUntilFinished)
    }
}