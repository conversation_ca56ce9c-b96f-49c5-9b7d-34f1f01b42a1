package com.joinutech.common.util

import android.content.Context

/**
 * @Description: TODO
 * @Author: zhaoyy
 * @Time:  -
 * @packageName:
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */

fun getResourceId(context: Context, resName: String, packageName: String, defResId: Int): Int {
    return try {
        context.resources.getIdentifier(resName, "mipmap", packageName)
    } catch (e: Exception) {
        defResId
    }
}