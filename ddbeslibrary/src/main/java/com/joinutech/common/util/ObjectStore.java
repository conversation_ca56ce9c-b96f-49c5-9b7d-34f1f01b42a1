package com.joinutech.common.util;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;


/**
 * 使用场景，页面间传大对象值，防止bundle超载异常
 */
public final class ObjectStore {
    private final static Map<String, Object> mObjects = new HashMap<>();

    private ObjectStore() {}

    public static String add(Object obj) {
        String key = UUID.randomUUID().toString();
        synchronized (mObjects) {
            mObjects.put(key, obj);
        }
        return key;
    }

    public static void add(String key, Object obj) {
        synchronized (mObjects) {
            mObjects.put(key, obj);
        }
    }

    public static Object get(String key) {
        Object obj = null;
        synchronized (mObjects) {
            obj = mObjects.get(key);
        }
        return obj;
    }

    public static Object remove(String key) {
        Object obj = null;
        synchronized (mObjects) {
            obj = mObjects.remove(key);
        }
        return obj;
    }
}
