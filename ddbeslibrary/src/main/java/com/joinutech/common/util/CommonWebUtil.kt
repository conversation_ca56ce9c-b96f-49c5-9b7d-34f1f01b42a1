package com.joinutech.common.util

import android.content.Context
import android.net.Uri
import android.net.http.SslError
import android.os.Build
import android.view.View
import android.webkit.*
import androidx.core.content.FileProvider
import com.joinutech.common.base.isWebDebug
import com.marktoo.lib.cachedweb.LogUtil
import com.marktoo.lib.cachedweb.CommonWebConfig
import com.marktoo.lib.cachedweb.WebListener
import java.io.File

/**
 * @Description: TODO
 * @Author: zhaoyy
 * @Time: 2021/1/26 13:16
 * @packageName: com.joinutech.common.util
 * @Company: Copyright (C),2019- 2021,JoinuTech
 */
class CommonWebUtil(val context: Context) {

    private lateinit var commWeb: CommonWebConfig
    private var canGoBack = 0
    private var fileChooseCallback: ValueCallback<Array<Uri>>? = null
    var listener: LoadListener? = null

    init {
        if (context is LoadListener) {
            listener = context
        }
    }

    fun initWeb(webView: WebView) {
        commWeb = CommonWebConfig(context, webView)
        commWeb.debug = isWebDebug
        commWeb.cacheable = false
        commWeb.autoWide = true
        commWeb.zoomable = true
        commWeb.multiWindow = false
        commWeb.defaultEncoding = "utf-8"
        commWeb.jsBridge = true
        commWeb.applyWebSettings()
        commWeb.addInterceptor()
        commWeb.addDefaultClient()
        commWeb.webListener = object : WebListener() {
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                showLog("load page finished,$url")
                if (canGoBack == 0) {
                    listener?.loadState(1)
                }
            }

            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                listener?.loadProgress(newProgress)
                super.onProgressChanged(view, newProgress)
            }

            override fun onHttpError(view: WebView?, request: WebResourceRequest?,
                                     errorResponse: WebResourceResponse?) {
//                showLog("Http Error: ${errorResponse?.statusCode}")
                showLog("receive Http Error: ${request?.url} -- ${errorResponse?.statusCode}--${errorResponse?.reasonPhrase}")
                canGoBack = -1
                listener?.loadState(-1)
                super.onHttpError(view, request, errorResponse)
            }

            override fun onReceivedError(view: WebView?, errorCode: Int?, desc:String?, url: String?) {
                showLog("receive Http Error: $errorCode : $url")
                canGoBack = -1
                listener?.loadState(-1)
            }

            override fun onReceivedTitle(view: WebView?, title: String?, hasError: Boolean) {
                showLog("receive title $title")
                title?.let {
                    if (it.contains("404") || it.contains("500")
                            || it.contains("error", true)
                            || it.contains("网页无法打开")
                            || it.contains("failed", true)) {
                        canGoBack = -1
                        listener?.loadState(-1)
                    }
                }
            }

            override fun onSslError(view: WebView?, handler: SslErrorHandler?, error: SslError?) {
//                super.onSslError(view, handler, error)
                handler?.proceed()
            }

            override fun onShowFileChooser(webView: WebView?, filePathCallback: ValueCallback<Array<Uri>>?,
                                           fileChooserParams: WebChromeClient.FileChooserParams?): Boolean {
                fileChooseCallback = filePathCallback
                if (fileChooserParams != null) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                        if (fileChooserParams.mode == WebChromeClient.FileChooserParams.MODE_OPEN_MULTIPLE) {
                            listener?.onSelectData("pics", 9)

                        } else {
                            listener?.onSelectData("pics", 1)
                        }
                    } else {
                        listener?.onSelectData("pics", 1)
                    }
                } else {
                    listener?.onSelectData("pics", 1)
                }
                return true
            }

            override fun onJsAlert(view: WebView?, url: String?, message: String?, result: JsResult?): Boolean {
                // 允许js弹窗，默认禁止
                return true
            }
        }
        commWeb.useCached(false)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webView.settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        }
        webView.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
    }

    fun addChannel(channelObj: Any, channelName: String) {
        commWeb.addMutualInterface(channelObj, channelName)
    }

    fun onTransPicToWeb(selectList: ArrayList<String>) {
        if (fileChooseCallback != null) {
            val fileUris = arrayListOf<Uri>()
            if (selectList.isNotEmpty()) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    selectList.forEach { filePath ->
                        fileUris.add(
                                FileProvider.getUriForFile(context, context.packageName + ".fileProvider", File(filePath))
                        )
                    }
                } else {
                    selectList.forEach { filePath ->
                        fileUris.add(Uri.fromFile(File(filePath)))
                    }
                }
            }
            showLog("返回选择文件信息")
            fileChooseCallback?.onReceiveValue(fileUris.toTypedArray())// web 直选文件回调
        }
        fileChooseCallback = null // 不释放不能下次选取图片
        showLog("返回选择文件信息 之后")
    }

    fun showLog(info: String) {
        LogUtil.showLog(info)
    }

    interface LoadListener {
        fun loadProgress(progress: Int)
        fun loadState(state: Int)
        fun onSelectData(name: String, data: Any)
    }
}