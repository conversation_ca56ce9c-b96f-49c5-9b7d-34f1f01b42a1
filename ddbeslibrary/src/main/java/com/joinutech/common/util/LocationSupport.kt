package com.joinutech.common.util

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.location.LocationManager
import androidx.core.location.LocationManagerCompat

/**
 * @Group: Copyright (C),2017- 2020,JoinuTech
 * @PackageName: com.joinutech.common.util
 * @ClassName: LocationSupport
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2021/1/16 17:34
 * @Desc: //TODO 定位服务支持类
 */
class LocationSupport {

    /**获取gps开关状态*/
    @SuppressLint("ServiceCast")
    fun checkGpsOpenState(activity: Activity): Boolean {
        val lm = activity.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        return LocationManagerCompat.isLocationEnabled(lm)
    }
}