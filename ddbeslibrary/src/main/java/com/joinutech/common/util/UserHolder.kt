package com.joinutech.common.util

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.joinutech.common.base.isDebug
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.bean.*
import com.joinutech.ddbeslibrary.imbean.ImTokenBean
import com.joinutech.ddbeslibrary.org.GlobalCompanyHolder
import com.joinutech.ddbeslibrary.utils.*
import com.joinutech.ddbeslibrary.utils.CommonKeys.TCP_APPROVAL_PUSH_ID
import com.joinutech.ddbeslibrary.utils.CommonKeys.TCP_GENER_MANAGEMENT_PUSH_ID
import com.joinutech.ddbeslibrary.utils.CommonKeys.TCP_KINGDEE_PUSH_ID
import com.joinutech.ddbeslibrary.utils.CommonKeys.TCP_MATTER_PUSH_ID
import com.joinutech.ddbeslibrary.utils.CommonKeys.TCP_SYSTEM_PUSH_ID
import com.joinutech.ddbeslibrary.utils.CommonKeys.TCP_TICKET_PUSH_ID
import com.joinutech.ddbeslibrary.utils.CommonKeys.TCP_TRAIN_PUSH_ID
import com.marktoo.lib.cachedweb.LogUtil
import timber.log.Timber
import java.util.*
import kotlin.collections.ArrayList
import kotlin.collections.HashMap

/**
 * @Description: TODO
 * @Author: zhaoyy
 * @Time: 2021/2/8 14:08
 * @packageName: com.joinutech.common.util
 * @Company: Copyright (C),2019- 2021,JoinuTech
 */
/**
 * 当前用户信息
 * token缓存
 * 登录、登出缓存清理
 * 用户信息查询
 * */
interface CacheHolder {
    fun clearCache(key: String) {
        if (MMKVUtil.containKey(key)) {
            MMKVUtil.clearByKey(key)
        }
    }

    fun showLog(msg: String) {
        LogUtil.showLog(msg, "cache__>>")
    }

    /**初始化缓存*/
    fun init()

    /**用户退出登录，清理之前用户数据*/
    fun onLogout()

}

object CacheHelper : CacheHolder {
    override fun init() {
    }

    var globalBackListenerTime = 0L

    fun onCache(key: String, any: Any) {
        MMKVUtil.saveString(key, GsonUtil.toJson(any))
    }

    fun getCache(key: String): java.util.ArrayList<AttendanceHolidayBean>? {
        val json = MMKVUtil.getString(key)
        if (!json.isNullOrBlank()) {
            try {
                return GsonUtil.getList2(json, AttendanceHolidayBean::class.java)
            } catch (e: Exception) {
            }
        }
        return arrayListOf()
    }

    override fun onLogout() {
    }

}

object UserHolder : CacheHolder {

    private val lock = Object()

    fun updateRefreshToken(accessToken: String , refreshToken: String , expireIn: Int) {
        synchronized(lock){
            try {
                val tokenCache = MMKVUtil.getString(ConsKeys.TOKEN_CACHE)
                if (tokenCache.isNotBlank()) {
                    tokenBean = GsonUtil.fromJson(tokenCache, TokenBean::class.java)
                }
                tokenBean?.let {
                    it.access_token = accessToken
                    it.refresh_token = refreshToken
                    it.expires_in = expireIn.toLong()
                    it.updateTime = System.currentTimeMillis()

                    val json = GsonUtil.toJson(tokenBean)
                    MMKVUtil.saveString(ConsKeys.TOKEN_CACHE, json)
                    Timber.i("updateRefreshToken。。。最后更新了 $json")
                }
            }catch (e: Exception){
                e.printStackTrace()
            }
        }
    }

    //    @Volatile
    private var loginUser: PersonInfoBean? = null


    private var ddLongConnectUrl: String? = null
    private var ddImToken: String? = null

    fun storeFlutterSocketParam(imtoken: String?, url: String?) {
        if (imtoken == null || url == null) return
        MMKVUtil.saveString("dd_imToken", imtoken)
        MMKVUtil.saveString("dd_socket", url)
        ddImToken = imtoken
        ddLongConnectUrl = url
    }

    fun getSocketImTokne() : String {
        if (ddImToken == null) {
            ddImToken = MMKVUtil.getString("dd_imToken") ?:""
        }
        return ddImToken ?:""
    }

    fun getSocketUrl() : String {
        if (ddLongConnectUrl == null) {
            ddLongConnectUrl = MMKVUtil.getString("dd_socket") ?:""
        }
        return ddLongConnectUrl ?:""
    }


    override fun init() {
        getCurrentUser()
        getAccessToken()
    }

    /*-------------登录相关-------------*/

    fun getCurrentUser(): PersonInfoBean? {
        return synchronized(lock) {
            if (loginUser != null) {
                loginUser
            } else {
                val userInfo = MMKVUtil.getString(ConsKeys.USER_INFO)
                if (userInfo.isNotBlank()) {
                    loginUser = GsonUtil.fromJson(userInfo, PersonInfoBean::class.java)
                    loginUser
                } else {
                    null
                }
            }
        }
    }

    fun isLogin(): Boolean {
        return synchronized(lock) {
            if (loginUser?.userId != null) return true
            return !(getAccessToken().isNullOrEmpty())
        }
    }



    fun getUserId(): String? {
        try {
            if (loginUser != null) {
                return loginUser!!.userId ?:""
            } else {
                val userInfo = MMKVUtil.getString(ConsKeys.USER_INFO)
                if (userInfo.isNotBlank()) {
                    loginUser = GsonUtil.fromJson(userInfo, PersonInfoBean::class.java)
                    return loginUser!!.userId
                } else {
                    return null
                }
            }
        }catch (e: Exception){
            return ""
        }
    }

    /**当前用户登录或者当前用户信息更新时需要通过这个方法更新数据*/
    fun onLogin(user: PersonInfoBean, update: Boolean = false) {
        CompanyHolder.showLog("更新用户信息 ${GsonUtil.toJson(user)} ,isUpdate = $update")
        synchronized(lock) {
            MMKVUtil.saveString(ConsKeys.USER_INFO, GsonUtil.toJson(user))
            MMKVUtil.saveString(ConsKeys.PHONE, user.mobile)
            loginUser = user
            if (update) {
                showLog("用户信息更新成功")
            } else {
                showLog("用户登录成功")
            }
            lock.notifyAll()
        }
        saveUser(//缓存个人信息，nick为绑定微信名，解绑或重新绑定需要更新nick
                UserInfo(userId = user.userId, userIcon = user.avatar,
                        userName = user.name, userNick = user.nickName))
    }

    override fun onLogout() {
        synchronized(lock) {
            clearCache(ConsKeys.USER_INFO)// 用户信息
            clearCache(ConsKeys.PUSHTOKEN)// 清空推送token
            clearCache(ConsKeys.TOKEN_CACHE)// 清空token
            loginUser = null
            tokenBean = null
        }
    }
    /*-------------登录相关-------------*/

    /*-------------token相关-------------*/
    private var tokenBean: TokenBean? = null

    fun saveToken(tokenBean: TokenBean) {
        showLog("存储token ${GsonUtil.toJson(tokenBean)}")
        UserHolder.tokenBean = tokenBean
        /**存储token获取时间，用于计算当前token有效期是否过期*/
        tokenBean.updateTime = System.currentTimeMillis()
        MMKVUtil.saveString(ConsKeys.TOKEN_CACHE, GsonUtil.toJson(tokenBean))
    }

    fun getUserToken() : String {
        return if (!StringUtils.isEmpty(tokenBean?.access_token ?:"")) {
            "${tokenBean?.access_token}"
        } else {
            val tokenCache = MMKVUtil.getString(ConsKeys.TOKEN_CACHE)
            if (tokenCache.isNotBlank()) {
                tokenBean = GsonUtil.fromJson(tokenCache, TokenBean::class.java)
                if (tokenBean != null) {
                    "${tokenBean!!.access_token}"
                } else {
                    ""
                }
            } else {
                ""
            }
        }
    }

    fun getAccessToken(): String {
        return if (tokenBean != null) {
            "Bearer ${tokenBean!!.access_token}"
        } else {
            val tokenCache = MMKVUtil.getString(ConsKeys.TOKEN_CACHE)
            if (tokenCache.isNotBlank()) {
                tokenBean = GsonUtil.fromJson(tokenCache, TokenBean::class.java)
                if (tokenBean != null) {
                    "Bearer ${tokenBean!!.access_token}"
                } else {
                    ""
                }
            } else {
                ""
            }
        }
    }

    fun getRefreshToken(): String {
        return if (tokenBean != null && !tokenBean!!.refresh_token.isNullOrBlank()) {
            tokenBean!!.refresh_token!!
        } else {
            val tokenCache = MMKVUtil.getString(ConsKeys.TOKEN_CACHE)
            if (tokenCache.isNotBlank()) {
                tokenBean = GsonUtil.fromJson(tokenCache, TokenBean::class.java)
                tokenBean?.refresh_token ?: ""
            } else {
                ""
            }
        }
    }

    /**token未过期检测*/
    fun isTokenExpired(): Boolean {
        // 原生端去掉 过期逻辑 ， token 一直有效
        return true


        /*return if (tokenBean != null) {
            // 更新阈值 测试3分钟 正式提前三十分钟
            val effectiveTime = if (isDebug) {
                // token过期测试使用，三分钟过期
//                300 * 60 * 1000
                30000 * 60 * 1000
            } else {
                // token过期提前30分钟
                (tokenBean!!.expires_in - 3 * 10 * 60) * 1000
            }

            // 自从上次更新后，使用的时间，大于等于有效期时需要更新，小于有效期，说明还没有过期
            val sinceUpdate = System.currentTimeMillis() - tokenBean!!.updateTime
            return sinceUpdate < effectiveTime
        } else {
            false
        }*/
    }

    fun setCurrentPushToken(pushToken: String?) {
        if (pushToken == null || pushToken == "") return
        val oldToken = MMKVUtil.getString(ConsKeys.PUSHTOKEN)
        if (oldToken != pushToken) {
            MMKVUtil.saveString(ConsKeys.PUSHTOKEN, pushToken)
        }
    }

    fun getCurrentPushToken(): String = MMKVUtil.getString(ConsKeys.PUSHTOKEN)
/*-------------token相关-------------*/

    /*-------------用户缓存相关-------------*/
    fun saveUser(user: UserInfo) {
        if (user.userId == getUserId()) { // 当前用户
            MMKVUtil.saveString("C_${user.userId}", GsonUtil.toJson(user))
        } else { // 其他用户
            MMKVUtil.saveString("U_${user.userId}", GsonUtil.toJson(user))
        }
        // CHANGE_HISTORY: 2021/2/8 14:10
        CacheDataHolder.setUserInfo(user.userId, user)

        EventBusUtils.sendEvent(EventBusEvent(ConsKeys.USER_INFO_UPDATE, user.userId))
    }

    fun saveOffLineNotReadCount(sessionId: String, notReadCount: Int) {
        MMKVUtil.saveInt(getUserId() + sessionId + "notReadCount", notReadCount)
    }

    fun getOffLineNotReadCount(sessionId: String): Int {
        return MMKVUtil.getInt(getUserId() + sessionId + "notReadCount")
    }


    fun saveSessionId(appChatId: String, sessionId: String?) {
        MMKVUtil.saveString(getUserId() + appChatId + "sessionId", sessionId ?: "")
    }

    fun saveComplainTipMsgBySessionId(tipMsg: String = "", sessionId: String?) {
        MMKVUtil.saveString(getUserId() + sessionId + "complainTipMsg", tipMsg)
    }

    fun saveApproCache() {
        val currentUserId = getUserId()
        BaseApplication.approCacheMap[currentUserId + "timeTag"] = System.currentTimeMillis().toString()
        MMKVUtil.saveString(currentUserId + "approCache", GsonUtil.toJson(BaseApplication.approCacheMap))
        BaseApplication.approCacheMap.clear()
    }

    fun getApproCache(): HashMap<String, String>? {
        val jsonCache = MMKVUtil.getString(getUserId() + "approCache")
        return Gson().fromJson<HashMap<String, String>>(jsonCache, object : TypeToken<HashMap<String, String>>() {}.type)
                ?: null
    }

    fun saveUserName(appChatId: String, userName: String) {
        MMKVUtil.saveString(getUserId() + appChatId + "name", userName)
    }

    fun saveUserLogo(appChatId: String, userLogo: String) {
        MMKVUtil.saveString(getUserId() + appChatId + "logo", userLogo)
    }

    fun getUserName(appChatId: String): String {
        return MMKVUtil.getString(getUserId() + appChatId + "name")
    }

    fun getUserLogo(appChatId: String): String {
        return MMKVUtil.getString(getUserId() + appChatId + "logo")
    }

    fun saveImTokenBean(imTokenBean: ImTokenBean) {
        MMKVUtil.saveString("tcpImTokenBean" + getUserId(), GsonUtil.toJson(imTokenBean))
    }

    fun saveScanKey(key:String?){
        MMKVUtil.saveString("scan_key" + getUserId(), key?:"")
    }

    fun saveGrateDataLastTime(map:HashMap<String,String>?){
        MMKVUtil.saveString("entroy_last_time" + getUserId(), GsonUtil.toJson(map))
    }

    fun getScanKey():String{
        return MMKVUtil.getString("scan_key" + getUserId())
    }

    fun getGrateDataLastTimeMap():HashMap<String,String>{
        val mapString= MMKVUtil.getString("entroy_last_time" + getUserId())

        if (StringUtils.isEmpty(mapString)) {
            return hashMapOf()
        }
        return Gson().fromJson<HashMap<String,String>>(mapString, object : TypeToken<HashMap<String,String>>() {}.type)
    }

    fun saveKingdeeEntroyBean(bean: EntroyBean?) {
        MMKVUtil.saveString("kingdee_bean"+ getUserId(),GsonUtil.toJson(bean))
    }
    fun getKingdeeEntroyBean(): EntroyBean? {
        val string= MMKVUtil.getString("kingdee_bean"+ getUserId())
        return GsonUtil.fromJson(string,EntroyBean::class.java)
    }

    //点击超链接时的警告语=============================================
    fun saveLinkWarn(warnText: String?) {
        MMKVUtil.saveString("warnText", warnText?:"")
    }

    fun getLinkWarn():String {
      return  MMKVUtil.getString("warnText")
    }

    fun saveEntroyListMap(map: HashMap<String,ArrayList<EntroyBean>>?) {
        MMKVUtil.saveString("entroyList${getUserId()}", GsonUtil.toJson(map))
    }

    fun getEntroyListMap():HashMap<String,ArrayList<EntroyBean>> {
        val mapString=  MMKVUtil.getString("entroyList${getUserId()}")
        if (!StringUtils.isNotBlankAndEmpty(mapString)) {
            return hashMapOf()
        }
        return Gson().fromJson<HashMap<String,ArrayList<EntroyBean>>>(mapString, object : TypeToken<HashMap<String,ArrayList<EntroyBean>>>() {}.type)
    }

    //点击超链接时，是否直接跳转的白名单
    fun saveLinkWhiteList(list: ArrayList<String>?) {
        MMKVUtil.saveString("whiteList", GsonUtil.toJson(list))
    }

    fun getLinkWhiteList():ArrayList<String> {
        val listString=  MMKVUtil.getString("whiteList")
        if (!StringUtils.isNotBlankAndEmpty(listString)) {
            return arrayListOf()
        }
        return Gson().fromJson<ArrayList<String>>(listString, object : TypeToken<ArrayList<String>>() {}.type)
    }

    //========================
    fun saveEntryImageUrl(type:String,imageUrl: String) {
        MMKVUtil.saveString("entryImage" + getUserId()+type,imageUrl)
    }

    fun getEntryImageUrl(type:String): String {
        return MMKVUtil.getString("entryImage" + getUserId()+type)
    }

    fun saveOpenTopIdList(idList: ArrayList<String>?) {
        if (idList == null) return
        try {
            MMKVUtil.saveString("tcpOpenTopList" + getUserId(), GsonUtil.toJson(idList))
        }catch (e: Exception){}
    }

    fun saveHideSessionIdList(idList: ArrayList<String>?) {
        if (idList == null) return
        MMKVUtil.saveString("tcpHideSessionIdList" + getUserId(), GsonUtil.toJson(idList))
    }

    fun getHideSessionIdList(): ArrayList<String>? {
        val hideSessionIdJson = MMKVUtil.getString("tcpHideSessionIdList" + getUserId())
        if (!hideSessionIdJson.isNullOrBlank()) {
            return Gson().fromJson<ArrayList<String>>(hideSessionIdJson, object : TypeToken<ArrayList<String>>() {}.type)//tcp----json转集合
        }
        return null
    }

    fun getOpenTopIdList(): ArrayList<String>? {
        val openTopJson = MMKVUtil.getString("tcpOpenTopList" + getUserId())
        if (!openTopJson.isNullOrBlank()) {
            return Gson().fromJson<ArrayList<String>>(openTopJson, object : TypeToken<ArrayList<String>>() {}.type)//tcp----json转集合
        }
        return null
    }

    fun saveSingleDisturbIdList(idList: ArrayList<String>) {
        MMKVUtil.saveString("tcpSingleDisturbList" + getUserId(), GsonUtil.toJson(idList))
    }

    fun getSingleDisturbIdList(): ArrayList<String>? {
        val disturbSingleJson = MMKVUtil.getString("tcpSingleDisturbList" + getUserId())
        if (!disturbSingleJson.isNullOrBlank()) {
            return Gson().fromJson<ArrayList<String>>(disturbSingleJson, object : TypeToken<ArrayList<String>>() {}.type)
        }
        return null
    }

    fun saveGroupDisturbIdList(idList: ArrayList<String>) {
        MMKVUtil.saveString("tcpGroupDisturbList" + getUserId(), GsonUtil.toJson(idList))
    }

    fun getGroupDisturbIdList(): ArrayList<String>? {
        val disturbGroupJson = MMKVUtil.getString("tcpGroupDisturbList" + getUserId())
        if (!disturbGroupJson.isNullOrBlank()) {
            return Gson().fromJson<ArrayList<String>>(disturbGroupJson, object : TypeToken<ArrayList<String>>() {}.type)
        }
        return null
    }

    fun clearImTokenBean() {
        MMKVUtil.saveString("tcpImTokenBean" + getUserId(), "")
        // CHANGE_HISTORY: 2021/2/8 15:02
    }

    fun getImTokenBean(): ImTokenBean? {
        val imTokenJson = MMKVUtil.getString("tcpImTokenBean" + getUserId())
        if (!imTokenJson.isNullOrBlank()) {
            return GsonUtil.fromJson(imTokenJson, ImTokenBean::class.java)
        }
        return null
    }


    fun getImToken(): String {
        val imTokenJson = MMKVUtil.getString("tcpImTokenBean" + getUserId())
        if (!imTokenJson.isNullOrBlank()) {
            val i = GsonUtil.fromJson(imTokenJson, ImTokenBean::class.java)
            return i?.token ?:""
        }
        return ""
    }


    fun getSessionIdByChatId(appChatId: String): String {
        return MMKVUtil.getString(getUserId() + appChatId + "sessionId")
    }

    fun getComplainTipMsgBySessionId(sessionId: String?): String {
        if (sessionId == null) {
            return ""
        }
        return MMKVUtil.getString(getUserId() + sessionId + "complainTipMsg", defaultValue = "")
    }

    fun saveUuid(uuid: String) {
        MMKVUtil.saveString("cmdId", uuid)
    }

    fun getUuid(): String {
        return MMKVUtil.getString("cmdId")
    }

    fun getUser(userId: String): UserInfo? {
        val info = if (userId == getUserId()) {
            MMKVUtil.getString("C_$userId")
        } else {
            MMKVUtil.getString("U_$userId")
        }
        if (info.isNotBlank()) {
            return GsonUtil.fromJson(info, UserInfo::class.java)
        }
        return null
    }
/*-------------用户缓存相关-------------*/

}

/**
 * 团队信息查询
 * 当前团队查询
 * 主要团队查询
 * 所有团队查询
 * 所有协作团队查询
 * */
object CompanyHolder : CacheHolder {

    private var mainOrgId: String? = null

    /**当前团队*/
    private var currentOrg: WorkStationBean? = null

    /**当前所有团队*/
    private var allNormalOrg: ArrayList<WorkStationBean> = arrayListOf()

    /**当前协作团队*/
    private var allCooperation: ArrayList<WorkStationBean> = arrayListOf()

    fun getCurrentOrg(): WorkStationBean? {
        if (currentOrg == null) {
            currentOrg = GsonUtil.fromJson(MMKVUtil.getString(CURRENTCOMPANY),
                    WorkStationBean::class.java)
        }
        return currentOrg
    }

    fun saveCurrentOrg(work: WorkStationBean?) {
        currentOrg = work
        if (work != null) {
            MMKVUtil.saveString(CURRENTCOMPANY, GsonUtil.toJson(work))
        } else {
            MMKVUtil.saveString(CURRENTCOMPANY, "")
        }
    }

    fun setCurrentOrg(companyId: String?) {
        val companys = getTotalCompanies()
        if (companys.isEmpty()) {
            Timber.i("当前没有任何公司")
            return
        }
        val company = companys.filter { it.companyId == companyId }?.first()
        if(company == null){
            Timber.i("没有对应的公司")
            return
        }
        saveCurrentOrg(company)
        GlobalCompanyHolder.setCurrentCompany(company)
    }

    fun getAllNormalOrg(): ArrayList<WorkStationBean> {
        if (allNormalOrg.isNullOrEmpty()) {
            allNormalOrg = GsonUtil.getList2(MMKVUtil.getString(COMPANIES),
                    WorkStationBean::class.java) ?: arrayListOf()
        }
        return allNormalOrg
    }

    /**获取所有团队信息*/
    //tcp获取所有团队的数据（包含合作团队）
    fun getTotalCompanies(): ArrayList<WorkStationBean> {
        val list = arrayListOf<WorkStationBean>()
        list.addAll(getAllNormalOrg())
        list.addAll(getCooperationOrg())
        return list
    }

    /**获取所有团队信息*/
    fun getTotalCompanyIdMap(): Map<String, WorkStationBean> {
        if (getTotalCompanies().isNotEmpty()) {
            return getTotalCompanies().map { it.companyId to it }.toMap()
        }
        return hashMapOf()
    }

    fun getCompanyByCompanyId(companyId: String): WorkStationBean? {
        return getTotalCompanyIdMap()[companyId]
    }

    fun findUseridAndTotalCompanys(onResult: (String, ArrayList<WorkStationBean>, ArrayList<WorkStationBean>) -> Unit) {
        val currentUserId = UserHolder.getUserId() ?: return
        val allNormalOrgs = getAllNormalOrg()
        val allCooperationOrgs = getCooperationOrg()
        showLog("获取当前所有团队和协作团队-->")
        onResult.invoke(currentUserId, allNormalOrgs, allCooperationOrgs)
    }

    //haveApprove字段存储的是当前账号公司列表对应的所有未处理审批的数量，，，，不包含金蝶的
    fun getTotalUnReadApproCount(): Int {
        val allCompanies = getTotalCompanies()
        var totalCount = 0
        allCompanies.forEach {
            totalCount = totalCount + it.haveApprove
        }
        return totalCount
    }



    fun clearUnReadApproCount(companyId: String, result: () -> Unit) {
        showLog("清理审批未读数缓存---《》《《》《")
        val allNormalOrg1 = getAllNormalOrg()
        if (!allNormalOrg1.isNullOrEmpty()) {
            val find = allNormalOrg1.find { it.companyId == companyId }
            if (find != null) {
                find.haveApprove = 0
                saveAllNormalOrg(allNormalOrg1, result) // 清理审批未读数
            } else {
                val cooperationOrg = getCooperationOrg()
                if (!cooperationOrg.isNullOrEmpty()) {
                    val find1 = cooperationOrg.find { it.companyId == companyId }
                    if (find1 != null) {
                        find1.haveApprove = 0
                        saveCooperationOrg(cooperationOrg, result)
                    }
                }
            }
        }
    }

    fun upOneUnReadApproCount(companyId: String, result: () -> Unit) {
        val allOrg1 = getAllNormalOrg()
        val find = allOrg1.find { it.companyId == companyId }
        if (find != null) {
            find.haveApprove = find.haveApprove + 1
            saveAllNormalOrg(allOrg1, result) //审批未读输处理
        } else {
            val cooperationOrg = getCooperationOrg()
            val find1 = cooperationOrg.find { it.companyId == companyId }
            if (find1 != null) {
                find1.haveApprove = find1.haveApprove + 1
                saveCooperationOrg(cooperationOrg, result)
            }
        }
    }

    /**获取团队信息*/
    fun getOrgInfo(companyId: String): UserInfo? {
        return GsonUtil.fromJson(MMKVUtil.getString("O_${companyId}"), UserInfo::class.java)
    }

    /**保存团队信息*/
    fun setOrgInfo(userInfo: UserInfo) {
        MMKVUtil.saveString("O_${userInfo.userId}", GsonUtil.toJson(userInfo))
//        CacheDataHolder.setUserInfo(userInfo.userId, userInfo)
    }

    /**保存所有团队并更新主要团队信息，调用完需要刷新一下用主要团队的位置*/
    fun saveAllNormalOrg(allOrg: ArrayList<WorkStationBean>) {
        saveAllNormalOrg(allOrg) {}
    }

    fun saveAllNormalOrg(allNormalOrg: ArrayList<WorkStationBean>, result: () -> Unit) {
        showLog("保存全部团队列表-->${GsonUtil.toJson(allNormalOrg)}")
        CompanyHolder.allNormalOrg = allNormalOrg
        MMKVUtil.saveString(COMPANIES, GsonUtil.toJson(allNormalOrg))

        fun updateCurrent() {
            saveCurrentOrg(when {
                StringUtils.isNotBlankAndEmpty(mainOrgId) -> {
                    allNormalOrg.find { it.companyId == mainOrgId }
                }
                allNormalOrg.isNotEmpty() -> {
                    allNormalOrg[0]
                }
                else -> {
                    null
                }
            })
        }

        fun saveNormalOrgInfo() {
            allNormalOrg.forEach {
                setOrgInfo(UserInfo(it.companyId, it.logo, it.name))
            }
        }

        if (allNormalOrg.isNotEmpty()) {
            // 团队信息保存为userInfo，方便之后im列表或相关位置使用
            saveNormalOrgInfo()
            val currentOrg = getCurrentOrg()
            if (currentOrg != null) {
                val result = allNormalOrg.find { it.companyId == currentOrg.companyId }
                if (result == null) {// 未找到当前团队信息，说明当前团队已不存在，需要重新设置当前团队
                    updateCurrent()
                }
            } else {
                updateCurrent()
            }
        } else {
            saveCurrentOrg(null)
            saveMainOrgId(null)
            showLog("清空主要团队id $mainOrgId")
            UserHolder.getCurrentUser()?.let {
                it.companyId = "" // 更新所有团队信息时，无团队信息时，清空用户默认公司id
                UserHolder.onLogin(it, true)// change main org
            }
        }
        result.invoke()
    }

    fun getCooperationOrg(): ArrayList<WorkStationBean> {
        if (allCooperation.isNullOrEmpty()) {
            allCooperation = GsonUtil.getList2(MMKVUtil.getString(COOPERATION_ORG_ALL),
                    WorkStationBean::class.java) ?: arrayListOf()
        }
        return allCooperation
    }

    fun saveCooperationOrg(allCooperation: ArrayList<WorkStationBean>) {
        saveCooperationOrg(allCooperation) {}
    }

    fun saveCooperationOrg(allCooperation: ArrayList<WorkStationBean>, result: () -> Unit) {
        showLog("保存全部协作团队列表-->${GsonUtil.toJson(allCooperation)}")
        CompanyHolder.allCooperation = allCooperation
        MMKVUtil.saveString(COOPERATION_ORG_ALL, GsonUtil.toJson(allCooperation))
        if (!allCooperation.isNullOrEmpty()) {
            allCooperation.forEach {
                setOrgInfo(UserInfo(it.companyId, it.logo, it.name))
            }
        }
        result.invoke()
    }

    fun saveMainOrgId(mainOrgId: String?) {
        showLog("保存主要团队id oldId = ${CompanyHolder.mainOrgId}, newId = $mainOrgId")
        CompanyHolder.mainOrgId = mainOrgId
        UserHolder.getCurrentUser()?.let {
            it.companyId = mainOrgId ?: ""
            UserHolder.onLogin(UserHolder.getCurrentUser()!!, true)// change main org
        }
        EventBusUtils.sendEvent(EventBusEvent(ConsKeys.ORG_MAIN_CHANGE, mainOrgId))
    }

    fun getMainOrg(): WorkStationBean? {
        showLog("获取主要团队 当前主要团队id = $mainOrgId")
        if (mainOrgId.isNullOrBlank() || mainOrgId == "0") {
//            mainOrgId = MMKVUtil.getString(MAIN_COMPANY_ID)
            mainOrgId = UserHolder.getCurrentUser()?.companyId ?: ""
        }
        showLog("获取主要团队 实际读取到主要团队id = $mainOrgId")
        if (!mainOrgId.isNullOrBlank() && mainOrgId != "0" && !getAllNormalOrg().isNullOrEmpty()) {
            val result = allNormalOrg.find { it.companyId == mainOrgId }
            return result ?: getAllNormalOrg()[0]
        }
        return null
    }

    /**
     * 设置免打扰的团队id缓存
     * app 启动从后台获取设置免打扰 团队 id数组
     * 修改团队 免打扰后 设置本地缓存
     *
     * 个人的配置信息，退出登录时需要清理
     */
    private val noticeMsgDisturbSettingSet = hashSetOf<String>()

    fun savePushSetting(setting: PushSettingData) {
        if (UserHolder.isLogin()) {
            MMKVUtil.saveString("push_setting_${UserHolder.getUserId()}", GsonUtil.toJson(setting))

            noticeMsgDisturbSettingSet.clear()
            noticeMsgDisturbSettingSet.addAll(setting.ids)
            if (setting.systemPushSwitch == 1) {
                noticeMsgDisturbSettingSet.add(TCP_SYSTEM_PUSH_ID)
            }
            if (setting.approvalPushSwitch == 1) {
                noticeMsgDisturbSettingSet.add(TCP_APPROVAL_PUSH_ID)
            }
            if (setting.ticketPushSwitch == 1) {
                noticeMsgDisturbSettingSet.add(TCP_TICKET_PUSH_ID)
            }
            if (setting.inventorySwitch == 1) {
                noticeMsgDisturbSettingSet.add(TCP_MATTER_PUSH_ID)
            }
            if (setting.kingdeePushSwitch == 1) {
                noticeMsgDisturbSettingSet.add(TCP_KINGDEE_PUSH_ID)
            }
            if (setting.trainingSwitch == 1) {
                noticeMsgDisturbSettingSet.add(TCP_TRAIN_PUSH_ID)
            }
            if (setting.managementSwitch == 1){
                noticeMsgDisturbSettingSet.add(TCP_GENER_MANAGEMENT_PUSH_ID)
            }
        }
        EventBusUtils.sendEvent(EventBusEvent(MSG_NOTIFY_CHANGE, ""))
    }

    fun hasPushSetting() : Boolean {
        if (!UserHolder.isLogin()) return false
        val json = MMKVUtil.getString("push_setting_${UserHolder.getUserId()}")
        if (json.isNotBlank() && json.startsWith("{") && json.endsWith("}")) return true
        return false
    }

    fun getPushSetting(): PushSettingData {
        if (UserHolder.isLogin()) {
            val json = MMKVUtil.getString("push_setting_${UserHolder.getUserId()}")
            if (json.isNotBlank() && json.startsWith("{") && json.endsWith("}")) {
                return GsonUtil.fromJson<PushSettingData>(json)!!
            }
        }
        return PushSettingData()
    }

    fun getPushSettingSet(): HashSet<String> {
        if (noticeMsgDisturbSettingSet.isNotEmpty()) {
            return noticeMsgDisturbSettingSet
        }
        val pushSetting = getPushSetting()
        noticeMsgDisturbSettingSet.addAll(pushSetting.ids)
        if (pushSetting.systemPushSwitch == 1) {
            noticeMsgDisturbSettingSet.add(TCP_SYSTEM_PUSH_ID)
        }
        if (pushSetting.approvalPushSwitch == 1) {
            noticeMsgDisturbSettingSet.add(TCP_APPROVAL_PUSH_ID)
        }

        if (pushSetting.ticketPushSwitch == 1) {
            noticeMsgDisturbSettingSet.add(TCP_TICKET_PUSH_ID)
        }
        if (pushSetting.inventorySwitch == 1) {
            noticeMsgDisturbSettingSet.add(TCP_MATTER_PUSH_ID)
        }
        if (pushSetting.kingdeePushSwitch == 1) {
            noticeMsgDisturbSettingSet.add(TCP_KINGDEE_PUSH_ID)
        }
        if (pushSetting.trainingSwitch == 1) {
            noticeMsgDisturbSettingSet.add(TCP_TRAIN_PUSH_ID)
        }
        if (pushSetting.managementSwitch == 1){
            noticeMsgDisturbSettingSet.add(TCP_GENER_MANAGEMENT_PUSH_ID)
        }
        return noticeMsgDisturbSettingSet
    }


    fun checkNoticeIsDisturb(noticeId : String?) : Boolean {
        // 检测对应通知类型是否开启了免打扰
        val pushSettingSet = getPushSettingSet()
        if (pushSettingSet.isEmpty()) return false
        return pushSettingSet.contains(noticeId)
    }


    //tcp通知消息免打扰
//检查系统消息是否打开免打扰，
    fun checkSystemDisturb(): Boolean {//检查系统通知是否开启免打扰
        val pushSettingSet = getPushSettingSet()
        if (pushSettingSet.contains(TCP_SYSTEM_PUSH_ID)) {
            return true
        } else {
            return false
        }
    }

    //检查审批消息是否打开免打扰
    fun checkApprocvalDisturb(): Boolean {//检查审批通知是否开启免打扰
        val pushSettingSet = getPushSettingSet()
        if (pushSettingSet.contains(TCP_APPROVAL_PUSH_ID)) {
            return true
        } else {
            return false
        }
    }

    fun checkGenerManagementDisturb(): Boolean {//检查【综合管理平台】是否开启免打扰
        val pushSettingSet = getPushSettingSet()
        return pushSettingSet.contains(TCP_GENER_MANAGEMENT_PUSH_ID)
    }


    //检查消息助手审批消息是否打开免打扰
    fun checkKingdeeDisturb(): Boolean {//检查消息助手审批通知是否开启免打扰
        val pushSettingSet = getPushSettingSet()
        if (pushSettingSet.contains(TCP_KINGDEE_PUSH_ID)) {
            return true
        } else {
            return false
        }
    }

    fun checkGenerateManageDisturb(): Boolean {
        val pushSettingSet = getPushSettingSet()
        if (pushSettingSet.contains(TCP_GENER_MANAGEMENT_PUSH_ID)) {
            return true
        } else {
            return false
        }
    }

    //检查工单消息是否打开免打扰
    fun checkTicketDisturb(): Boolean {
        val pushSettingSet = getPushSettingSet()
        if (pushSettingSet.contains(TCP_TICKET_PUSH_ID)) {
            return true
        } else {
            return false
        }
    }

    fun checkTrainDisturb(): Boolean {
        val pushSettingSet = getPushSettingSet()
        if (pushSettingSet.contains(TCP_TRAIN_PUSH_ID)) {
            return true
        } else {
            return false
        }
    }

    //检查库存消息是否打开免打扰
    fun checkMatterDisturb(): Boolean {
        val pushSettingSet = getPushSettingSet()
        if (pushSettingSet.contains(TCP_MATTER_PUSH_ID)) {
            return true
        } else {
            return false
        }
    }

    //检查公司消息是否打开免打扰
    fun checkWorkCompanyIdDisturb(companyId: String): Boolean {//检查工作通知是否开启免打扰
        val pushSettingSet = getPushSettingSet()
        if (pushSettingSet.contains(companyId)) {
            return true
        } else {
            return false
        }
    }



//    private var unReadApr: HashMap<String, Int>? = null
//
//    fun getUnReadApr(): HashMap<String, Int> {
//        if (unReadApr == null) {
//            MMKVUtil.getString("push_setting_${UserHolder.getUserId()}", GsonUtil.toJson(setting))
//            unReadApr = hashMapOf()
//        }
//        return unReadApr!!
//    }
//
//    fun saveUnReadApr(targetId: String, num: Int) {
//        if (unReadApr!!.containsKey(targetId)) {
//            unReadApr!![targetId] = unReadApr!![targetId] + num
//        } else {
//            unReadApr!![targetId] = num
//        }
//    }

    override fun init() {
    }

    override fun onLogout() {
        clearCache(CURRENTCOMPANY)// 当前组织
        clearCache(COOPERATION_ORG_ALL)// 所有协作组织
        clearCache(COMPANIES)// 所有组织
        allCooperation.clear()
        allNormalOrg.clear()
        currentOrg = null
    }
}

/**
 * 内存缓存用户信息，最多三十人
 * 如果不存在，先获取好友信息，存在则返回
 * 如果好友信息不存在，则获取用户信息，存在则返回
 * 不存在，则接口请求用户信息，请求成功后，保存用户缓存，事件更新已打开页面中存在当前用户信息的位置UI
 *
 * */
/**
 * @PackageName: com.joinutech.common.helper
 * @ClassName: CacheDataHolder
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/5/13 13:10
 * @Desc: 缓存数据工具类
 */
/**好友和群成员相关信息缓存*/
object CacheDataHolder : CacheHolder {
    private val maxSize = 40
    private var userInfoCache: LinkedHashMap<String, UserInfo> = LinkedHashMap(40, 0.75f, true)

    @Volatile
    var lock = Object()

    fun cacheInfo(targetId: String, userInfo: UserInfo) {
        synchronized(lock) {
            if (!userInfoCache.containsKey(targetId)) {
                if (userInfoCache.size >= maxSize) {
                    showLog("需要清理消息 ${userInfoCache.size}")
                    userInfoCache.remove(userInfoCache.keys.toList()[0])
                    userInfoCache.remove(userInfoCache.keys.toList()[0])
                    userInfoCache.remove(userInfoCache.keys.toList()[0])
                    userInfoCache.remove(userInfoCache.keys.toList()[0])
                    userInfoCache.remove(userInfoCache.keys.toList()[0])
                    userInfoCache.remove(userInfoCache.keys.toList()[0])
                    userInfoCache.remove(userInfoCache.keys.toList()[0])
                    userInfoCache.remove(userInfoCache.keys.toList()[0])
                    userInfoCache.remove(userInfoCache.keys.toList()[0])
                    userInfoCache.remove(userInfoCache.keys.toList()[0])
                }
            }
            lock.notifyAll()
        }
        userInfoCache[targetId] = userInfo
    }

    fun getCache(targetId: String): UserInfo? {
        return userInfoCache[targetId]
    }

    fun setGroupInfo(targetId: String, userInfo: UserInfo) {
        cacheInfo(targetId, userInfo)
    }

    /**获取公司信息*/
    fun getCompanyInfo(targetId: String): UserInfo {
        val user = getCache(targetId)
        if (user != null && !user.userName.isNullOrBlank() && !user.userIcon.isNullOrBlank()) {
            return user
        } else {
            val companyInfo = CompanyHolder.getOrgInfo(targetId)
            if (companyInfo != null && !companyInfo.userName.isNullOrBlank()) {
                return companyInfo
            } else {
                val temp = CompanyHolder.getTotalCompanyIdMap()
                if (temp.containsKey(targetId)) {
                    val workstation = temp[targetId] ?: return UserInfo(targetId)
                    val user = UserInfo(targetId, workstation.logo, workstation.name)
                    CompanyHolder.setOrgInfo(user)
                    cacheInfo(targetId, user)
                    return user
                }
            }
            return UserInfo(targetId, "", "")
        }
    }

    /**获取用户信息
     * 先查询缓存信息
     * 在查询好友信息 --- 不再查询好友数据，仅查询用户数据
     * 再查询用户信息*/
    fun getUserInfo(targetId: String): UserInfo {
        val user = getCache(targetId)
        if (user != null && !user.userName.isNullOrBlank()) {
            return user
        } else {
//            val friend = FriendCacheHolder.getFriend(targetId)
            val friend = GsonUtil.fromJson(MMKVUtil.getString(
                    "F_${UserHolder.getUserId()}_$targetId"),
                    UserInfo::class.java)
            if (friend != null) {
                cacheInfo(targetId, friend)
                return friend
            } else {
                val userInfo = UserHolder.getUser(targetId)
                if (userInfo != null) {
                    cacheInfo(targetId, userInfo)
                    return userInfo
                } else {
                    loadUserInfo()
                }
            }
        }
        return UserInfo(targetId, "", "")
    }

    private fun loadUserInfo() {
        // TODO: 2020/7/13 9:29 加载用户信息数据 请求成功后，写缓存，事件同步更新到UI，需要更新的位置

    }

    fun setUserInfo(targetId: String, userInfo: UserInfo) {
        cacheInfo(targetId, userInfo)
    }

    fun savePushStateLastCheck(time: Long) {
        MMKVUtil.saveLong(MSG_PUSH_CHECK_LAST_TIME, time)
    }

    fun needCheckPushState(): Boolean {
        val last = MMKVUtil.getLong(MSG_PUSH_CHECK_LAST_TIME)
        if (last == 0L) return true
        val lastCalendar = Calendar.getInstance()
        lastCalendar.timeInMillis = last
        val currentCalendar = Calendar.getInstance()
        return if (currentCalendar.after(lastCalendar)) {
            val day1 = lastCalendar.get(Calendar.DAY_OF_YEAR)
            val day2 = currentCalendar.get(Calendar.DAY_OF_YEAR)
            day2 > day1
        } else {
            false
        }
    }

    fun sysPushEnable(context: Context): Boolean {
        return NotificationUtil.isNotificationEnabled(context)
    }

    /**获取系统消息免打扰设置*/
    fun msgPushEnable(): Boolean {
        return MMKVUtil.getInt(MSG_NOTIFICATION_ACTION, 1) > 0
    }

    fun msgPushVibEnable(): Boolean {
        return MMKVUtil.getInt(MSG_NOTIFICATION_VIBRATES, 1) > 0
    }

    fun msgPushSoundEnable(): Boolean {
        return MMKVUtil.getInt(MSG_NOTIFICATION_SOUND, 1) > 0
    }

    fun saveMsgPushEnable(state: Int) {
        MMKVUtil.saveInt(MSG_NOTIFICATION_ACTION, state)
        EventBusUtils.sendEvent(EventBusEvent(MSG_NOTIFY_CHANGE, ""))
    }

    fun saveMsgPushVibEnable(state: Int) {
        MMKVUtil.saveInt(MSG_NOTIFICATION_VIBRATES, state)
    }

    fun saveMsgPushSoundEnable(state: Int) {
        MMKVUtil.saveInt(MSG_NOTIFICATION_SOUND, state)
    }

    /**
     * 缓存用户信息
     * */
    fun refreshUserInfo(info: UserInfo) {
        try {
            MMKVUtil.saveString(info.userId, GsonUtil.toJson(info))
            LogUtil.showLog("IM ::  缓存IM用户信息${info.userName} 成功", "user__")
        } catch (e: Exception) {
            LogUtil.showLog("IM ::  缓存IM用户信息 失败", "user__")
        }
    }

    override fun init() {
    }

    override fun onLogout() {
        clearCache(MSG_PUSH_CHECK_LAST_TIME)
    }

}
