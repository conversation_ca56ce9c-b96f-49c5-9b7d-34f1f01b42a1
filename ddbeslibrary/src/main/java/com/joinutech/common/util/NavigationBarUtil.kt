package com.joinutech.common.util

import android.app.Activity
import android.content.Context
import android.content.res.Resources
import android.graphics.Point
import android.graphics.Rect
import android.os.Build
import android.provider.Settings
import android.util.DisplayMetrics
import android.view.WindowManager
import com.gyf.immersionbar.OSUtils
import com.marktoo.lib.cachedweb.LogUtil

/**
 * @Description: TODO
 * @Author: zhaoyy
 * @Time:  -
 * @packageName:
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
class NavigationBarUtil {

    companion object {
        var wm: WindowManager? = null

        @Volatile
        var lock = Object()
        fun getScreenRealHeight(context: Context): Int {
            if (wm == null) {
                synchronized(lock) {
                    if (wm == null) {
                        wm = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
                    }
                    lock.notifyAll()
                }
            }
            val point = Point()
            wm!!.defaultDisplay.getRealSize(point)
            return point.y
        }

        fun getDecorViewHeight(activity: Activity): Int {
            val rect = Rect()
            activity.window.decorView.getWindowVisibleDisplayFrame(rect)
            return rect.height()
        }

        fun getStatusBarHeight(context: Context): Int {
            var result = 0
            val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
            if (resourceId > 0) {
                result = context.resources.getDimensionPixelSize(resourceId)
            }
            return result
        }

        fun getNavigationBarHeight(context: Context): Int {
            var result = 0
            val resourceId = context.resources.getIdentifier("navigation_bar_height", "dimen", "android")
            if (resourceId > 0) {
                result = context.resources.getDimensionPixelSize(resourceId)
            }
            return result
        }

        fun getNavigationHeight(activity: Activity): Int {
            val realHeight = getScreenRealHeight(activity)
            val decorHeight = getDecorViewHeight(activity)
            val statusHeight = getStatusBarHeight(activity)
            val navigationBarHeight = getNavigationBarHeight(activity)
            LogUtil.showLog("--->>>realHeight=$realHeight,decorHeight=$decorHeight,statusHeight=$statusHeight,navigationBarHeight=$navigationBarHeight")
            LogUtil.showLog("--->>>realHeight=$realHeight,topHeight=${decorHeight + statusHeight}")
            //  360 N7 Lite 1803-A01
//            >>>realHeight=2160,decorHeight=2160,statusHeight=72,navigationBarHeight=144
//            >>>realHeight=2160,topHeight=2232
//            >>>realHeight=2160,decorHeight=2016,statusHeight=72,navigationBarHeight=144
//            >>>realHeight=2160,topHeight=2088

            return realHeight - (decorHeight + statusHeight)
        }

        fun isShowNavigationBar(activity: Activity): Boolean {
            return getNavigationHeight(activity) > 0
        }

    }


    /**
     * 导航栏竖屏高度标识位
     */
    val IMMERSION_NAVIGATION_BAR_HEIGHT = "navigation_bar_height"

    /**
     * MIUI导航栏显示隐藏标识位
     */
    val IMMERSION_MIUI_NAVIGATION_BAR_HIDE_SHOW = "force_fsg_nav_bar"

    /**
     * EMUI导航栏显示隐藏标识位
     */
    val IMMERSION_EMUI_NAVIGATION_BAR_HIDE_SHOW = "navigationbar_is_min"

    fun getNavigationBarHeight(context: Context): Int {
        val result = 0
        if (hasNavBar(context as Activity)) {
            val key = IMMERSION_NAVIGATION_BAR_HEIGHT
            return getInternalDimensionSize(context, key)
        }
        return result
    }

    private fun hasNavBar(activity: Activity): Boolean {
        //判断小米手机是否开启了全面屏，开启了，直接返回false
        if (Settings.Global.getInt(activity.contentResolver, IMMERSION_MIUI_NAVIGATION_BAR_HIDE_SHOW, 0) != 0) {
            return false
        }
        //判断华为手机是否隐藏了导航栏，隐藏了，直接返回false
        if (OSUtils.isEMUI()) {
            if (OSUtils.isEMUI3_x() || Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
                if (Settings.System.getInt(activity.contentResolver, IMMERSION_EMUI_NAVIGATION_BAR_HIDE_SHOW, 0) != 0) {
                    return false
                }
            } else {
                if (Settings.Global.getInt(activity.contentResolver, IMMERSION_EMUI_NAVIGATION_BAR_HIDE_SHOW, 0) != 0) {
                    return false
                }
            }
        }

        //其他手机根据屏幕真实高度与显示高度是否相同来判断
        val windowManager = activity.windowManager
        val d = windowManager.defaultDisplay
        val realDisplayMetrics = DisplayMetrics()
        d.getRealMetrics(realDisplayMetrics)
        val realHeight = realDisplayMetrics.heightPixels
        val realWidth = realDisplayMetrics.widthPixels
        val displayMetrics = DisplayMetrics()
        d.getMetrics(displayMetrics)
        val displayHeight = displayMetrics.heightPixels
        val displayWidth = displayMetrics.widthPixels
        return realWidth - displayWidth > 0 || realHeight - displayHeight > 0
    }

    private fun getInternalDimensionSize(context: Context, key: String): Int {
        val result = 0
        try {
            val resourceId = Resources.getSystem().getIdentifier(key, "dimen", "android")
            if (resourceId > 0) {
                val sizeOne = context.resources.getDimensionPixelSize(resourceId)
                val sizeTwo = Resources.getSystem().getDimensionPixelSize(resourceId)
                return if (sizeTwo >= sizeOne) {
                    sizeTwo
                } else {
                    val densityOne = context.resources.displayMetrics.density
                    val densityTwo = Resources.getSystem().displayMetrics.density
                    val f = sizeOne * densityTwo / densityOne
                    (if (f >= 0) f + 0.5f else f - 0.5f).toInt()
                }
            }
        } catch (ignored: Resources.NotFoundException) {
            return 0
        }
        return result
    }
}