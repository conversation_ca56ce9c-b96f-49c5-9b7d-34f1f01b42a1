package com.joinutech.common.util

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.graphics.BitmapFactory
import android.os.Build
import androidx.core.app.NotificationCompat
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.utils.PROPERTY_DETAIL_TITLE_MODEL
import com.marktoo.lib.cachedweb.LogUtil
import java.io.Serializable

/**
 * @Description: TODO
 * @Author: zhaoyy
 * @Time:  -
 * @packageName:
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */

class NotifyUtil private constructor() {

    companion object {
        val instance: NotifyUtil by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            NotifyUtil()
        }
    }

    private val notificationManager: NotificationManager by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        BaseApplication.joinuTechContext.getSystemService(Context.NOTIFICATION_SERVICE)
                as NotificationManager
    }

    @Volatile
    private var currentTarget: String? = null

    fun isCurrent(targetId: String): Boolean {
        return targetId == currentTarget
    }

    fun setCurrentTarget(targetId: String?) {
        currentTarget = targetId
        //旧push,位置5
       /* if (!targetId.isNullOrBlank()) {
            clearNotify(targetId)
            PushCenter.clearNotify(BaseApplication.joinuTechContext)
        }*/
    }

    /*fun showNotify(context: Context?, channelId: String, notifyId: String,
                   channelName: String = "担当办公IM消息",
                   title: String, content: String, intentAction: PendingIntent? = null,
                   sound: Boolean, vibrator: Boolean) {
        if (context == null) return
        val titleInfo = if (title.isNotBlank()) {
            title
        } else {
            PROPERTY_DETAIL_TITLE_MODEL
        }
        if (notifyId.isNullOrBlank() || notifyId == currentTarget) return
        if (!sound && !vibrator) return
        val id = notifyId.hashCode()

        val notifyContent = if (content.length <= 20) {
            content
        } else {
            content.substring(0, 19).plus("...")
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val builder: Notification.Builder = Notification.Builder(BaseApplication.joinuTechContext, channelId)
            builder.setOnlyAlertOnce(false)
                    //must set
                    .setSmallIcon(R.mipmap.icon)
                    //must set
                    .setLargeIcon(BitmapFactory.decodeResource(context.resources, R.mipmap.icon))
                    //must set
                    .setContentTitle(titleInfo)
                    //must set
                    .setContentText(notifyContent)
                    .setAutoCancel(true)
                    .setWhen(System.currentTimeMillis())
            if (intentAction != null) {
                builder.setContentIntent(intentAction)
            }
            //创建 通知通道  channelid和channelname是必须的（自己命名就好）
            val channel = NotificationChannel(channelId, channelName, NotificationManager.IMPORTANCE_HIGH)
            channel.enableLights(true)
            channel.setShowBadge(true) //是否显示角标
            if (sound && vibrator) {
                channel.enableVibration(true)
                channel.vibrationPattern = longArrayOf(100, 200, 300)
            } else if (sound) {
                channel.enableVibration(false)
                channel.vibrationPattern = longArrayOf(0)
            } else if (vibrator) {
                channel.enableVibration(true)
                channel.vibrationPattern = longArrayOf(100, 200, 300)
            } else {
                channel.enableLights(false)
                //统一消除声音和震动
                channel.setSound(null, null)
                channel.enableVibration(false)
                channel.vibrationPattern = longArrayOf(0)
                //是否显示角标
                channel.setShowBadge(false)
            }
            notificationManager.createNotificationChannel(channel)
            notificationManager.notify(id, builder.build())
        } else {
            val builder = NotificationCompat.Builder(BaseApplication.joinuTechContext, channelId)

            builder.setOnlyAlertOnce(true)
                    .setSmallIcon(R.mipmap.icon)
                    .setLargeIcon(BitmapFactory.decodeResource(context.resources, R.mipmap.icon))
                    //must set
                    .setContentTitle(title)
                    .setContentText(notifyContent)
                    .setAutoCancel(true)
                    .setWhen(System.currentTimeMillis())
            //must set
            if (intentAction != null) {
                builder.setContentIntent(intentAction)
            }
            if (sound && vibrator) {
                //默认全部
                builder.setDefaults(Notification.DEFAULT_ALL)
            } else if (sound) {
                builder.setDefaults(Notification.DEFAULT_SOUND)
            } else if (vibrator) {
                builder.setDefaults(Notification.DEFAULT_VIBRATE)
            } else {
                //统一消除声音和震动
                builder.setDefaults(NotificationCompat.FLAG_ONLY_ALERT_ONCE)
            }
            notificationManager.notify(id, builder.build())
        }
    }*/

    fun clearNotify(targetId: String) {
        LogUtil.showLog("移除通知栏消息 notify id is $targetId")
        //移除通知栏
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            //关闭通知通道
//            notificationManager.deleteNotificationChannel(targetId)
            notificationManager.cancel(targetId.hashCode())
        } else {
            notificationManager.cancel(targetId.hashCode())
        }
//        notificationManager.cancel(id.hashCode())

    }

    fun clearAllNotify() {
        notificationManager.cancelAll()
    }
}
