package com.joinutech.common.util

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.location.Geocoder
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.provider.Settings
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import cn.addapp.pickers.util.LogUtils
import com.joinutech.common.widget.CommonDialog
import com.joinutech.ddbeslibrary.base.customlivedata.SuperLiveDataManager
import com.joinutech.ddbeslibrary.base.customlivedata.base.postOneOff
import com.joinutech.ddbeslibrary.utils.AppManager
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.*

/**
 *@desc:
 *@author: wangchao
 *@createTime: 2022/8/31
 */
object LocationUtils {

    private  var firstListener: MyLocationListener? = null
    private  var secondListener: MyLocationListener? = null
    private lateinit var locationManager: LocationManager
    /**
     * 获取所在国家
     */
    fun getLocation(context: Context){
        //权限判断
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED
            && ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_BACKGROUND_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            Log.d("test","记录位置-获取经纬度没有位置权限")
            return
        }
        locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        val locationProviders = locationManager.getProviders(true)
        //默认位置提供者为网络
        var locationProvider  = if (locationProviders.contains(LocationManager.NETWORK_PROVIDER)){
            //网络定位
            LocationManager.NETWORK_PROVIDER
        }else if (locationProviders.contains(LocationManager.GPS_PROVIDER)){
            //GPS定位
            LocationManager.GPS_PROVIDER
        }else{
            //没有可用位置提供者  跳转系统gps设置
            Log.d("test","记录位置-获取经纬度没有打开gps")

            val dialog = CommonDialog(context, false)
            dialog.setTitle("需开启系统定位服务开关")
                .setContent("用于确认中国号码用户来电功能适用范围")
                .setCancelable(true)
                .setRightButtonListener(
                ) {
                    Log.d("test","记录位置-gps提醒弹窗选择去设置")

                    context.startActivity(Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS))
                    dialog.dismiss()
                }
                .setLeftButtonListener(){
                    dialog.dismiss()
                    Log.d("test","记录位置-gps提醒弹窗选择拒绝")
                }
                .show()

            return
        }
        //获取上次位置  第一次一般为空
        var location = locationManager.getLastKnownLocation(locationProvider)
        if (location != null){
            Log.d("记录位置-获取位置","上次位置==${location}")
            getLocationAddress(context, location)
       }else{
            //监听位置变化
           if (firstListener != null){
               locationManager.removeUpdates(firstListener!!)
               firstListener = null
           }
            firstListener = MyLocationListener()
            locationManager.requestLocationUpdates(locationProvider, 3000, 1f, firstListener!!)
            Log.d("test","记录位置-首次获取位置提供者，没有上次位置，开启位置监听 位置提供者==${locationProvider}")

            //使用另外一种provider
            if(locationProvider == LocationManager.NETWORK_PROVIDER && locationProviders.contains(LocationManager.GPS_PROVIDER)){
                locationProvider = LocationManager.GPS_PROVIDER
            }else if (locationProvider == LocationManager.GPS_PROVIDER && locationProviders.contains(LocationManager.NETWORK_PROVIDER)){
                locationProvider = LocationManager.NETWORK_PROVIDER
            }
            location = locationManager.getLastKnownLocation(locationProvider)
            if (location != null){
                Log.d("记录位置-获取位置","使用另一个provider获取上次位置==${location}")

                getLocationAddress(context, location)
            }else{
                //监听位置变化
                if (secondListener != null){
                    locationManager.removeUpdates(secondListener!!)
                    secondListener = null
                }
                secondListener = MyLocationListener()
                locationManager.requestLocationUpdates(locationProvider, 3000, 1f, secondListener!!)
                Log.d("test","记录位置-首次获取位置提供者，没有上次位置，开启位置监听 位置提供者==${locationProvider}")

            }
       }

    }

    /**
     * 根据经纬度解析具体地址
     */
    fun getLocationAddress(context: Context, location: Location) {
        GlobalScope.launch {
            val geocoder = Geocoder(context, Locale.CHINA)
            try {
                val addressList = geocoder.getFromLocation(location.latitude,location.longitude,1)
                if (addressList != null) {
                    for (address in addressList){
                        Log.d("记录位置-获取到详细位置信息","详细数据  国家==${address.countryName}  数据==${address}")
                        SuperLiveDataManager.getLocationResult.postOneOff("${location.latitude},${location.longitude},${address.featureName}")
                    }
                }
                if(addressList?.size == 0){
                    Log.d("test","记录位置-没有解析到位置信息")
                    SuperLiveDataManager.getLocationResult.postOneOff("")
                }
            }catch (exception:Exception){
                Log.d("test","记录位置-解析位置出错")
                SuperLiveDataManager.getLocationResult.postOneOff("")
            }
        }
    }

    /**
     * 获取当前位置
     * 获取成功后会解析为国家通过SuperLiveDataManager.getLocation发送
     */
    fun getCurrentLocation(context: Context){
        //权限判断
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED
            && ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_BACKGROUND_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            Log.d("test","记录位置-获取经纬度没有位置权限")
            return
        }
        locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        val locationProviders = locationManager.getProviders(true)
        //默认位置提供者为网络
        var locationProvider  = if (locationProviders.contains(LocationManager.NETWORK_PROVIDER)){
            //GPS定位
            LocationManager.NETWORK_PROVIDER
        }else if (locationProviders.contains(LocationManager.GPS_PROVIDER)){
            //网络定位
            LocationManager.GPS_PROVIDER
        }else{
            //没有可用位置提供者  跳转系统gps设置
            Log.d("test","录音获取当前位置-获取经纬度没有打开gps")

            val dialog = CommonDialog(context, false)
            dialog.setTitle("需开启系统定位服务开关")
                .setContent("考勤打卡需要打卡定位功能")
                .setCancelable(true)
                .setRightButtonListener(
                ) {
                    Log.d("test","记录位置-gps提醒弹窗选择去设置")
                    context.startActivity(Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS))
                    dialog.dismiss()
                }
                .setLeftButtonListener(){
                    dialog.dismiss()
                    Log.d("test","记录位置-gps提醒弹窗选择拒绝")
                }
                .show()

            return
        }
        //监听位置变化
        if (firstListener != null){
            locationManager.removeUpdates(firstListener!!)
            firstListener = null
        }
        firstListener = MyLocationListener()
        locationManager.requestLocationUpdates(locationProvider, 3000, 1f, firstListener!!)
        //使用另外一种provider
        if(locationProvider == LocationManager.NETWORK_PROVIDER && locationProviders.contains(LocationManager.GPS_PROVIDER)){
            locationProvider = LocationManager.GPS_PROVIDER
        }else if (locationProvider == LocationManager.GPS_PROVIDER && locationProviders.contains(LocationManager.NETWORK_PROVIDER)){
            locationProvider = LocationManager.NETWORK_PROVIDER
        }
        //监听位置变化
        if (secondListener != null){
            locationManager.removeUpdates(secondListener!!)
            secondListener = null
        }
        secondListener = MyLocationListener()
        locationManager.requestLocationUpdates(locationProvider, 3000, 1f, secondListener!!)

    }
    fun getCurrentLocation(context: Context, listener:LocationListener){
        //权限判断
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED
            && ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_BACKGROUND_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            Log.d("test","记录位置-获取经纬度没有位置权限")
            return
        }
        locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        val locationProviders = locationManager.getProviders(true)
        //默认位置提供者为网络
        var locationProvider  = if (locationProviders.contains(LocationManager.NETWORK_PROVIDER)){
            //GPS定位
            LocationManager.NETWORK_PROVIDER
        }else if (locationProviders.contains(LocationManager.GPS_PROVIDER)){
            //网络定位
            LocationManager.GPS_PROVIDER
        }else{
            //没有可用位置提供者  跳转系统gps设置
            Log.d("test","录音获取当前位置-获取经纬度没有打开gps")

            val dialog = CommonDialog(context, false)
            dialog.setTitle("需开启系统定位服务开关")
                .setContent("考勤打卡需要打卡定位功能")
                .setCancelable(true)
                .setRightButtonListener(
                ) {
                    Log.d("test","记录位置-gps提醒弹窗选择去设置")
                    context.startActivity(Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS))
                    dialog.dismiss()
                }
                .setLeftButtonListener(){
                    dialog.dismiss()
                    Log.d("test","记录位置-gps提醒弹窗选择拒绝")
                }
                .show()

            return
        }
        //监听位置变化
        locationManager.requestLocationUpdates(locationProvider, 3000, 1f, listener)
    }

    fun removeListener(listener:LocationListener){
        locationManager.removeUpdates(listener)
    }

    class MyLocationListener :LocationListener{
        override fun onLocationChanged(location: Location) {
            Log.d("test","记录位置-监听到位置变化===provider${location.provider}")
            //避免重复发送
            if(this == firstListener && secondListener !=null){
                locationManager.removeUpdates(secondListener!!)
            }else if(this == secondListener && firstListener !=null){
                locationManager.removeUpdates(firstListener!!)
            }
            getLocationAddress(AppManager.single_instance.currentActivity()!!,location)
            locationManager.removeUpdates(this)
        }

    }
}