package com.joinutech.common.util

import java.io.BufferedReader
import java.io.InputStreamReader

/**
 * @Description: TODO
 * @Author: zhaoyy
 * @Time: 2021/2/2 15:05
 * @packageName: com.joinutech.common.util
 * @Company: Copyright (C),2019- 2021,JoinuTech
 */
class NetUtil {
    /**
     * 推断Ping 网址是否返回成功
     *
     * @param netAddress
     * @return
     */
    fun isPingSuccess(pingNum: Int, m_strForNetAddress: String): String? {
        val pingInfo = StringBuffer()
        try {
            val p = Runtime.getRuntime().exec("/system/bin/ping -c $pingNum $m_strForNetAddress") // ************
            // m_strForNetAddress
            val status = p.waitFor()
            var result = ""
            result = if (status == 0) {
                "success"
            } else {
                "failed"
            }
            val lost = String()
            val delay = String()
            val buf = BufferedReader(InputStreamReader(p.inputStream))
            var str = String()
            // 读出全部信息并显示
            while (buf.readLine().also({ str = it }) != null) {
                str = """
                    $str
                    
                    """.trimIndent()
                pingInfo.append(str)
            }
            return pingInfo.toString()
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
        return ""
    }
}