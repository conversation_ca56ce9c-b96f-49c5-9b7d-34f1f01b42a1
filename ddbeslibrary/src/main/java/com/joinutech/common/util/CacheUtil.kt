package com.joinutech.common.util


import com.joinutech.ddbeslibrary.utils.MMKVUtil

object CacheUtil {
    //-----------------验证码的有效开始时间----------开始----------
    fun saveVerifyCodeStartTime(startTime: Long) {
        MMKVUtil.saveLong("verify_code_time" + UserHolder.getUserId(), startTime)
    }

    fun getVerifyCodeStartTime(): Long {
        val startTime = MMKVUtil.getLong("verify_code_time" + UserHolder.getUserId())
        return startTime
    }

    //--------------------------------------------结束--------------------
}