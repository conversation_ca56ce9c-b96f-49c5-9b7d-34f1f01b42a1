package com.joinutech.common.util

/**
 * @PackageName: com.joinutech.common.util
 * @ClassName: ScheduleTask
 * @Author: zhao<PERSON>y
 * @Leader: Ke
 * @CreateTime: 2020/8/4 10:12
 * @Desc: //TODO 定时任务工具类
 */
class ScheduleTask {
//    private var timer: Timer? = null
//    private var timerTask: TimerTask? = null
//
//    private var handler: MyHandler? = null
//
//    /**
//     * 初始化计时器用来更新倒计时
//     */
//    fun init(onHandle: () -> Unit) {
//        handler = MyHandler(onHandle)
//        timer = Timer()
//        timerTask = object : TimerTask() {
//            override fun run() {
//                handler?.sendEmptyMessage(1)
//            }
//        }
//    }
//
//    fun start(period: Long = 1000) {
//        timer?.schedule(timerTask, 0, period)
//    }
//
//    fun stop() {
//        timer?.cancel()
//        handler = null
//        timerTask = null
//        timer = null
//    }
}

//class MyHandler(val onHandle: () -> Unit) : Handler() {
//
//    override fun handleMessage(msg: Message?) {
//        if (msg != null) {
//            onHandle()
//        }
//        super.handleMessage(msg)
//    }
//}