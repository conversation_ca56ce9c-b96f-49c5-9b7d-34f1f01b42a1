package com.joinutech.common.util

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.PopupWindow
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.utils.DeviceUtil
import com.joinutech.common.adapter.MyAdapter
import com.joinutech.ddbeslibrary.utils.PopUtil
import com.joinutech.ddbeslibrary.utils.ScreenUtils

/**
 * @PackageName: com.joinutech.common.util
 * @ClassName: CommonPop
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/5/14 14:21
 * @Desc: //TODO
 */
open class CommonPop(val context: Context, val contentLayout: Int = R.layout.popwindow_home_address_new) {
    protected var pop: PopupWindow? = null

    protected lateinit var contentView: View
    fun init(onBind: (view: View) -> Unit) {
        contentView = LayoutInflater.from(context).inflate(contentLayout, null)
        onBind(contentView)
        pop = PopUtil.buildPopWrap(contentView)
        pop!!.animationStyle = R.style.pop_anim_style
    }

    fun show(targetView: View) {
        if (pop == null) {
            pop = PopUtil.buildPop(contentView)
            pop!!.animationStyle = R.style.pop_anim_style
        }

        PopUtil.showAtPosition(pop!!, targetView,
                gravity = Gravity.TOP or Gravity.END,
//                x = -DeviceUtil.dip2px(context, 138f),
                x = DeviceUtil.dip2px(context, 9f),
                y = ScreenUtils.getStatusBarHeight(context) + DeviceUtil.dip2px(context, 28f))
    }

    fun hidePop() {
        pop!!.dismiss()
    }

    fun finish() {
        if (pop != null && pop!!.isShowing) {
            pop!!.dismiss()
        }
        pop = null
    }
}

class CommonListPop<T>(context: Context, val list: List<T>) : CommonPop(context, R.layout.popwindow_home_address_new) {

    fun initView(itemLayout: Int = R.layout.item_common_pop_layout,
                 onBind: (position: Int, data: T, view: View) -> Unit, onItemClick: (position: Int, data: T) -> Unit) {
        contentView = LayoutInflater.from(context).inflate(contentLayout, null)
        contentView.findViewById<View>(R.id.ll_content).visibility = View.GONE
        val recyclerView = contentView.findViewById<RecyclerView>(R.id.rv_list)
        recyclerView.visibility = View.VISIBLE
        val temp = mutableListOf<T>()
        temp.addAll(list)
        val adapter = MyAdapter(context, itemLayout, temp,
                onBindItem = { position: Int, data: T, view: View ->
                    view.findViewById<View>(R.id.item_line)?.let {
                        if (position != list.lastIndex) {
                            it.visibility = View.VISIBLE
                        } else {
                            it.visibility = View.GONE
                        }
                    }
                    onBind.invoke(position, data, view)
                },
                onItemClick = { position: Int, data: T, view: View ->
                    onItemClick.invoke(position, data)
                })
        recyclerView.layoutManager = LinearLayoutManager(context)
        recyclerView.adapter = adapter
        pop = PopUtil.buildPopWrap(contentView)
        pop!!.animationStyle = R.style.pop_anim_style
    }
}