package com.joinutech.common.util

import android.content.Context
import android.graphics.Color
import android.view.View
import android.widget.TextView
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.utils.XUtil
import com.joinutech.ddbeslibrary.widget.wheel.builder.TimePickerBuilder
import com.joinutech.ddbeslibrary.widget.wheel.listener.OnTimeSelectListener
import com.joinutech.ddbeslibrary.widget.wheel.view.TimePickerView
import com.marktoo.lib.cachedweb.LogUtil
import java.util.*

/**
 * @PackageName: com.joinutech.common.util
 * @ClassName: TimePickerUtil
 * @Desc: 时间选择器工具类
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/4/23 10:02
 */
class TimePickerUtil(val context: Context) {
    private var pvCustomTime: TimePickerView? = null
    private var timePickerBuilder: TimePickerBuilder? = null
    private var selectDateType: Int = 0

    private val timeSelectDialogTitles = arrayOfNulls<TextView>(2)
    private val timeSelectDialogTitleIndicators = arrayOfNulls<View>(2)

    private fun showFirstTitle(showIndicator: Boolean = true) {
        timeSelectDialogTitles[0]?.setTextColor(Color.parseColor("#1E87F0"))
        timeSelectDialogTitles[1]?.setTextColor(Color.parseColor("#333333"))
        if (showIndicator) {
            timeSelectDialogTitleIndicators[0]?.visibility = View.VISIBLE
            timeSelectDialogTitleIndicators[1]?.visibility = View.GONE
        } else {
            timeSelectDialogTitleIndicators[0]?.visibility = View.GONE
            timeSelectDialogTitleIndicators[1]?.visibility = View.GONE
        }

        if (types.isNotEmpty()) {
            selectDateType = types[0]
        }
    }

    private fun showSecondTitle(showIndicator: Boolean = true) {
        timeSelectDialogTitles[0]?.setTextColor(Color.parseColor("#333333"))
        timeSelectDialogTitles[1]?.setTextColor(Color.parseColor("#1E87F0"))
        if (showIndicator) {
            timeSelectDialogTitleIndicators[1]?.visibility = View.VISIBLE
            timeSelectDialogTitleIndicators[0]?.visibility = View.GONE
        } else {
            timeSelectDialogTitleIndicators[0]?.visibility = View.GONE
            timeSelectDialogTitleIndicators[1]?.visibility = View.GONE
        }
        if (types.isNotEmpty()) {
            selectDateType = if (mode == 0) {
                types[1]
            } else {
                types[0]
            }
        }
    }

    //0：年月日时分，1：年月日，2：时分
    var mode = 0

    fun setPickerMode(mode: Int) {
        this.mode = mode
    }

    var types = arrayOf<Int>()
    fun setPickerTypes(types: Array<Int>) {
        this.types = types
    }

    private fun initTimePickerBuilder(titles: ArrayList<String>? = null, calendars: Array<Calendar>, mode: Int = 0) {
        setPickerMode(mode)
        /**
         * @description
         *
         * 注意事项：
         * 1.自定义布局中，id为 optionspicker 或者 timepicker 的布局以及其子控件必须要有，否则会报空指针.
         * 具体可参考demo 里面的两个自定义layout布局。
         * 2.因为系统Calendar的月份是从0-11的,所以如果是调用Calendar的set方法来设置时间,月份的范围也要是从0-11
         * setRangDate方法控制起始终止时间(如果不设置范围，则使用默认时间1900-2100年，此段代码可注释)
         */
        //时间选择器 ，自定义布局
        if (timePickerBuilder == null) {
            timePickerBuilder = TimePickerBuilder(context,
                    OnTimeSelectListener { date, _ ->
                        //选中事件回调
                        //关闭弹窗时回调结果到页面
                        LogUtil.showLog("关闭弹窗时回调结果到页面 $date")
                    })
                    .setTimeSelectChangeListener { date ->
                        //时间选择后回调
                        LogUtil.showLog("时间选择后回调 $date")
                        if (titles == null) {
                            timeSelectDialogTitles[0]?.text = XUtil.getMyDate(date)
                            timeSelectDialogTitles[1]?.text = XUtil.getMyTime(date)
                        }
                    }
                    .setDate(calendars[0])
                    .setRangDate(calendars[1], calendars[2])
                    .setLayoutRes(R.layout.pickerview_custom_time) { v ->
                        val tvSubmit = v.findViewById<View>(R.id.tv_finish)
                        val ivCancel = v.findViewById<View>(R.id.tv_cancel)
                        timeSelectDialogTitles[0] = v.findViewById(R.id.tv_date_title)
                        timeSelectDialogTitles[1] = v.findViewById(R.id.tv_time_title)
                        timeSelectDialogTitleIndicators[0] = v.findViewById(R.id.indicator_date)
                        timeSelectDialogTitleIndicators[1] = v.findViewById(R.id.indicator_time)

                        if (titles != null) {
                            timeSelectDialogTitles[0]?.text = titles[0]
                            timeSelectDialogTitles[1]?.text = titles[1]
                        } else {
                            timeSelectDialogTitles[0]?.text = XUtil.getMyDate(Date())
                            timeSelectDialogTitles[1]?.text = XUtil.getMyTime(Date())
                        }
                        timeSelectDialogTitles[1]?.isSelected = true
//                        timeSelectDialogTitles[0]?.setSelected(true);
//                        timeSelectDialogTitles[0]?.setTextColor(Color.parseColor("#1E87F0"))
                        timeSelectDialogTitleIndicators[1]?.visibility = View.GONE
                        tvSubmit.setOnClickListener {
                            pvCustomTime?.returnData()
                            pvCustomTime?.dismiss()
                        }
                        ivCancel.setOnClickListener { pvCustomTime?.dismiss() }

                        updateMode()
                    }
                    .setContentTextSize(18)
//                切换控制显示年月日时分秒
                    .setType(booleanArrayOf(true, true, true, false, false, false))
//                .setLabel("年", "月", "日", "时", "分", "秒")
                    .setLabel("年", "月", "日", ":", "", "")
                    .setLineSpacingMultiplier(1.2f)
                    .setTextXOffset(30, 0, -30, 30, -30, 0)
//               是否只显示中间选中项的label文字，false则每项item全部都带有label。
                    .isCenterLabel(true)
        } else {
            timePickerBuilder!!.setRangDate(calendars[1], calendars[2]).setDate(calendars[0])
        }
    }

    private fun updateMode() {
        when (mode) {
            1 -> {//只显示第一组 mode=1
                timeSelectDialogTitles[0]?.setOnClickListener(null)
                timeSelectDialogTitles[1]?.setOnClickListener(null)
                showFirstTitle(false)
                timeSelectDialogTitles[0]?.visibility = View.VISIBLE
                timeSelectDialogTitles[1]?.visibility = View.GONE
            }
            2 -> {//只显示第二组 mode=2
                timeSelectDialogTitles[0]?.setOnClickListener(null)
                timeSelectDialogTitles[1]?.setOnClickListener(null)
                timeSelectDialogTitles[0]?.visibility = View.GONE
                timeSelectDialogTitles[1]?.visibility = View.VISIBLE
                showSecondTitle(false)
            }
            else -> {//显示两组选择 mode=0
                timeSelectDialogTitles[0]?.visibility = View.VISIBLE
                timeSelectDialogTitles[1]?.visibility = View.VISIBLE
                timeSelectDialogTitles[0]?.setOnClickListener {
                    showFirstTitle()
                    pvCustomTime?.setUpdate(selectDateType)
                }
                timeSelectDialogTitles[1]?.setOnClickListener {
                    showSecondTitle()
                    pvCustomTime?.setUpdate(selectDateType)
                }
                showFirstTitle()
            }
        }
    }

    fun getCalendars(startTime: Long? = null, endTime: Long? = null): Array<Calendar> {
        val selectedDate = Calendar.getInstance()

        val startDate = Calendar.getInstance()
        val startDateArray = if (startTime != null && startTime > 0L) {
            XUtil.getYMDValue(startTime)
        } else {
            arrayOf(2017, 1, 1)
        }
        startDate.set(startDateArray[0], startDateArray[1], startDateArray[2])

        val endDate = Calendar.getInstance()
        val endDateArray=if (endTime != null && endTime > 0L) {
         XUtil.getYMDValue(endTime)
        } else {
         arrayOf(2047,2,28)
        }
        endDate.set(endDateArray[0],endDateArray[1],endDateArray[2])

        return arrayOf(selectedDate, startDate, endDate)
    }

    fun initCustomTimePicker(titles: ArrayList<String>? = null, calendars: Array<Calendar>, mode: Int = 0) {
        initTimePickerBuilder(titles, calendars, mode)
        pvCustomTime = timePickerBuilder?.build()
    }

    fun updatePickerRange(titles: ArrayList<String>, calendars: Array<Calendar>, mode: Int = 0) {
        LogUtil.showLog("得到最晚时间，更新时间选择器")
        initTimePickerBuilder(titles, calendars, mode)
        pvCustomTime = timePickerBuilder?.build()
    }

    fun showCustomPicker(mode: Int = 0, result: (type: Int, date: Date) -> Unit) {
        setPickerMode(mode)
        updateMode()
        pvCustomTime?.setUpdate(selectDateType)
        pvCustomTime?.selectListener = OnTimeSelectListener { date, _ ->
            result.invoke(selectDateType, date)
        }
//      弹出自定义时间选择器
        pvCustomTime?.show()
    }


    companion object{
        //限制起始时间时使用，获取当月的第一天和最后一天的时间-----开始-----
        fun getStartTime(): Long {
            val cal=  Calendar.getInstance()
            // DAY_OF_MONTH表示月份内的天数值，从1开始，0会迭代到上个自然月的最后一天。
            cal.set(Calendar.DAY_OF_MONTH,1)
            val mDate=cal.time
            return mDate.time
        }

        fun getEndTime(): Long {
            val cal=  Calendar.getInstance()
            // DAY_OF_MONTH表示月份内的天数值，从1开始，0会迭代到上个自然月的最后一天。
            cal.set(Calendar.DAY_OF_MONTH,1)

            // 下面是直接获取月份的结束如期。
            // 这里利用DAY_OF_MONTH的-1位来获取上月的最后一天。
            // 基于上面的日期已经是月头的第一天，在这里直接在当前的月份基础上增加一个整月。
            cal.add(Calendar.MONTH,1)
            // 然后在这里利用DAY_OF_MONTH的-1位来获取上月的最后一天，如此就能获取到第一天和最后一天。
            cal.set(Calendar.DAY_OF_MONTH,0)

            val mDate=cal.time
            return mDate.time
            // 那么如果要获取指定月份的第一天和最后一天，可以使用cal.add(Calendar.MONTH, 1);来设定你需要的月份。
            // 然后在其基础上进行需要的时间计算。
        }
        //限制起始时间时使用，获取当月的第一天和最后一天的时间-----结束-----
    }


}