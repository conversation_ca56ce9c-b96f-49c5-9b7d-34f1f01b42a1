package com.joinutech.common.provider

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import com.alibaba.android.arouter.facade.template.IProvider
import com.marktoo.lib.cachedweb.LogUtil

/**
 * @PackageName: com.joinutech.common.helper
 * @ClassName: RouteServiceProvider
 * @Desc: 测试路由服务
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/4/29 15:13
 */
interface RouteServiceProvider : IProvider {

    fun openPage(path: String, params: Bundle)

    fun service(path: String, params: Bundle, result: (data: String) -> Unit)

    fun openPageWithResult(activity: FragmentActivity, path: String, params: Bundle, result: (data: String) -> Unit)

    fun openPageWithResult1(activity: FragmentActivity, path: String, params: Bundle, result: (data: String) -> Unit)

    fun showLog(msg: String) {
        LogUtil.showLog(msg, "rsp__")
    }
}