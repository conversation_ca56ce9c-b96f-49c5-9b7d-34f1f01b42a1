package com.joinutech.common.provider

import android.content.Context
import com.alibaba.android.arouter.facade.Postcard
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.utils.filePreviewWebActivity

/**文件预览统一处理方式*/
object FilePreviewProvider {

    /**
     * 文件预览 使用wps两种方式
     * 一种是直接加载预览地址
     * 一种是访问页面，通过参数回调给页面，页面里跳转预览页面
     * */
    fun getPreviewPost(fileName: String, fileSize: Long, filePath: String, ext: String = ""): Postcard {
        return ARouter.getInstance()
                .build(filePreviewWebActivity)
                .withString("fileName", fileName)
                .withLong("fileSize", fileSize)
                .withString("filePath", filePath)
                .withString("ext", ext)
                .withBoolean("useWps", true)
    }

    fun checkPreviewFile(context: Context, suffix: String, fileSize: Long): Boolean {

        val suffixList = arrayListOf<String>()
        suffixList.addAll(context.resources.getStringArray(R.array.rc_text_file_suffix))
        suffixList.addAll(context.resources.getStringArray(R.array.rc_word_file_suffix))
        suffixList.addAll(context.resources.getStringArray(R.array.rc_excel_file_suffix))
        suffixList.addAll(context.resources.getStringArray(R.array.rc_pdf_file_suffix))
        suffixList.addAll(context.resources.getStringArray(R.array.rc_ppt_file_suffix))

        fun isMoreThanLimit(fileSize: Long): Boolean {
            return fileSize < 20 * 1024 * 1024
        }

        return suffixList.contains(suffix) && isMoreThanLimit(fileSize)
    }
}