package com.joinutech.common.provider

import android.content.Context
import android.os.Bundle
import com.alibaba.android.arouter.facade.Postcard
import com.alibaba.android.arouter.facade.annotation.Interceptor
import com.alibaba.android.arouter.facade.callback.InterceptorCallback
import com.alibaba.android.arouter.facade.template.IInterceptor
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.common.storage.FileStorage
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.utils.*
import org.json.JSONArray

/**不再需要调用接口查询，只需要接收通知触发消息后跳转，在这里拦截，处理是否要跳转到列表页面*/
//拦截器使用https://cloud.tencent.com/developer/article/1334985
//这个拦截器的作用是在使用Arouter来界面跳转过程中的拦截，用来处理一些操作；tcp
@Interceptor(priority = 8, name = "测试用拦截器")
class RouteInterceptor : IInterceptor {

    var mContext: Context? = null

    override fun init(context: Context?) {
        mContext = context?.applicationContext
    }

    override fun process(postcard: Postcard?, callback: InterceptorCallback?) {
        when (postcard?.path) {
            RouteVc.VC_START_RUNNING_CONF -> {
                val content = postcard.extras.getString("im_meeting_content") ?: ""
                val jsonArray = JSONArray(content)
                when {
                    jsonArray.length() > 1 -> {
                        postcard.withString("startConfDataJson", content)
                        callback?.onContinue(postcard)
                    }
                    jsonArray.length() == 1 -> {
                        val jsonObj = jsonArray.getJSONObject(0)
                        val meetingId = jsonObj.optString("meetingId")
                        if (meetingId.isNullOrBlank()) {
                            callback?.onInterrupt(IllegalAccessException("0"))
                        } else {
                            postcard.withString("meetingId", meetingId)
                            callback?.onInterrupt(IllegalAccessException("1"))
                        }
                    }
                    else -> callback?.onInterrupt(IllegalAccessException("0"))
                }
            }
            filePreviewWebActivity -> {
                val fileName = postcard.extras.getString("fileName") ?: ""
                val filePath = postcard.extras.getString("filePath") ?: ""
                val fileSize = postcard.extras.getLong("fileSize", 0L)
                val onlyShow = postcard.extras.getBoolean("onlyShow", false)
                val ext = postcard.extras.getString("ext", "")
                FileStorage.showLog("文件在线预览 $fileName ,$filePath")
                if (onlyShow) { //  fileName filePath ext fileSize==0L ，不需要本地验证文件大小
                    postcard.withString("title", fileName)
                    postcard.withString("targetUrl", filePath)
                    postcard.withString("ext", ext)
                    callback?.onContinue(postcard)
                } else { //  fileName filePath fileSize ,需要本地验证文件大小
                    if (fileName.isNullOrBlank() || !fileName.contains(".") || filePath.isNullOrBlank()) {
                        callback?.onInterrupt(IllegalAccessException("0"))
                    } else {
                        val suffix = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase()
                        if (FilePreviewProvider.checkPreviewFile(mContext!!, ".".plus(suffix), fileSize)) {
                            postcard.withString("title", fileName)
                            postcard.withString("targetUrl", filePath)
                            postcard.withString("ext", suffix)
                            callback?.onContinue(postcard)
                        } else {
                            callback?.onInterrupt(IllegalAccessException("1"))
                        }
                    }
                }
            }
            COMMON_WEB_ACTIVITY -> {
                Loggerr.i("路由跳转", "===路由拦截器执行===")
                val url = postcard.extras.getString(ConsKeys.PARAMS_URL)
                Loggerr.i("路由跳转", "===路由拦截器拦截的地址URL：${url}")
                val isPlusToken=postcard.extras.getString("plusToken")//如果为"no"就不请求token了
                val isPlusPhone=postcard.extras.getString("plusPhone")//如果为"Yes"就拼接手机号
                Loggerr.i("路由跳转", "==isPlusToken=${isPlusToken}===isPlusPhone=${isPlusPhone}===")
                if (!url.isNullOrBlank()&&isPlusToken=="yes") {
                    Loggerr.i("路由跳转", "===金蝶请求token执行===")
                    val bundle = Bundle()
                    bundle.putString(ConsKeys.COMPANY_ID, CompanyHolder.getCurrentOrg()?.companyId ?: "")
                    bundle.putString(ConsKeys.USER_ID, UserHolder.getUserId() ?: "")
                    (ARouter.getInstance().build("/addressbook/service/org").navigation() as RouteServiceProvider)
                            .service("getKingDeeToken", bundle) { accessToken ->
                                if (accessToken.isNullOrBlank()||accessToken.startsWith("err:")) {
                                    mContext?.toastShort(accessToken.substring("err:".length))
                                    callback?.onInterrupt(IllegalAccessException("1"))
                                } else {
                                    postcard.withString(ConsKeys.PARAMS_URL, url.plus(accessToken).plus("&platform=1"))
                                    callback?.onContinue(postcard)
                                }
                            }
                } else if(!url.isNullOrBlank()&&isPlusPhone=="yes"){
                    val phone=UserHolder.getCurrentUser()?.mobile
                    Loggerr.i("路由跳转", "===拼接手机号执行==phone=${phone}===")
//                    if (phone!=null) {
//
//                        Loggerr.i("路由跳转", "===拼接手机号执行==")
//                        postcard.withString(ConsKeys.PARAMS_URL, url?.plus(phone))
//                        callback?.onContinue(postcard)
//                    }


                    callback?.onContinue(postcard)

                } else{
                    callback?.onContinue(postcard)
                }
            }
            else -> {
                callback?.onContinue(postcard)
            }
        }
    }

}