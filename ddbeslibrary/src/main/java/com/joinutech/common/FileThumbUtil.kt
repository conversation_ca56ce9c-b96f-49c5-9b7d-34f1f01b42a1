package com.joinutech.common

import android.content.Context
import android.os.Environment
import android.os.Environment.MEDIA_MOUNTED
import com.joinutech.ddbeslibrary.utils.GsonUtil
import timber.log.Timber
import java.io.File
import java.io.IOException
import java.io.Serializable

/**
 * @Des：
 * @author: moon
 * @date: 1/3/25
 */

// 新增 图片缩略图目录
const val IMAGE_THUMB_FOLDER = "image_thumb_folder"

// 新增 图片预览图目录
const val IMAGE_PREVIEW_FOLDER = "image_preview_folder"

// 新增 图片原图 目录
const val IMAGE_CACHE_FOLDER = "image_cache_folder"

object FileThumbUtil {

    val thumb = "imageMogr2/thumbnail/!30p"
    val preview = "imageMogr2/thumbnail/!80p"  // todo 预览图暂时改为 100%

    /**
     * https://im-resources-dev-1304188286.cos.ap-chongqing.myqcloud.com/2ASZFJy5uLR
     * ?imageMogr2/thumbnail/!10p&sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKID0oNgiTYBdrob6n8RU7TYYHPJfYAYtpqp%26q-sign-time%3D1735884907%3B1735888507%26q-key-time%3D1735884907%3B1735888507%26q-header-list%3D%26q-url-param-list%3Dresponse-cache-control%3Bresponse-content-disposition%3Bresponse-content-language%3Bresponse-expires%26q-signature%3D240e10bfc67f262e2a75e41e106caa005674d619&response-cache-control=no-cache&response-content-disposition=inline&response-content-language=zh-CN&response-expires=Sat%2C%2004%20Jan%202025%2006%3A15%3A07%20GMT
     */
    fun createSignThumbImageUrl(url: String?) :String {
        if (url.isNullOrEmpty()) return ""
        if (!url.contains("?")) return url
        val url_start = url.split("?")[0]
        val url_end = url.split("?")[1]
        val signUrl = "$url_start?$thumb&${url_end}"
        return signUrl
    }

    fun createSignPreviewImageUrl(url: String?) :String {
        // TODO
        return createSignOriginImageUrl(url)


        if (url.isNullOrEmpty()) return ""
        if (!url.contains("?")) return url
        val url_start = url.split("?")[0]
        val url_end = url.split("?")[1]
        val signUrl = "$url_start?$preview&${url_end}"
        return signUrl
    }

    fun createSignOriginImageUrl(url: String?) :String {
        if (url.isNullOrEmpty()) return ""
        return url
    }

    // glide 加载 ，url 头部添加 file://
    fun getThumbFileUrl(context: Context , fileId: String) : String {
        if (fileId.isNullOrEmpty()) return ""
        val tempLocalUrl = getFileCachePath(context, IMAGE_THUMB_FOLDER) + "/" + fileId + ".png"
        return "file://$tempLocalUrl"
    }

    fun getLocalPreviewUrl(context: Context , fileId: String) : String {
        if (fileId.isNullOrEmpty()) return ""
        val tempLocalUrl = getFileCachePath(context, IMAGE_PREVIEW_FOLDER) + "/" + fileId + ".png"
        return "file://$tempLocalUrl"
    }

    fun getLocalOriginUrl(context: Context , fileId: String) : String {
        val tempLocalUrl = getFileCachePath(context, IMAGE_CACHE_FOLDER) + "/" + fileId + ".png"
        return "file://$tempLocalUrl"
    }

    fun thumbIsExist(context: Context ,fileId :String): Boolean {
        val tempLocalUrl = getFileCachePath(context, IMAGE_THUMB_FOLDER) + "/" + fileId + ".png"
        return File(tempLocalUrl).exists()
    }

    fun previewExist(context: Context ,fileId :String): Boolean {
        val tempLocalUrl = getFileCachePath(context, IMAGE_PREVIEW_FOLDER) + "/" + fileId + ".png"
        return File(tempLocalUrl).exists()
    }

    fun originExist(context: Context ,fileId :String): Boolean {
        val tempLocalUrl = getFileCachePath(context, IMAGE_CACHE_FOLDER) + "/" + fileId + ".png"
        return File(tempLocalUrl).exists()
    }

    fun saveThumb(context: Context , file: File?, fileId: String) : String{
        if (file == null) return ""
        val tempDir = getFileCachePath(context, IMAGE_THUMB_FOLDER) + "/"
        val destination = File(tempDir)
        if (!destination.exists()) destination.mkdirs()
        try {
            val tF = File(tempDir , fileId + ".png")
            file.inputStream().use { input ->
                tF.outputStream().use { output ->
                    input.copyTo(output)
            } }
            Timber.i("下载缩略图到本地 : ${tF.path} , 是否存在： ${tF.exists()}")
            return tF.path
        } catch (e: IOException) {
            e.printStackTrace()
            return ""
        }
    }

    fun savePreviewThumb(context: Context , file: File, fileId: String) {
        val tempDir = getFileCachePath(context, IMAGE_PREVIEW_FOLDER) + "/"
        try {
            val tF = File(tempDir , fileId + ".png")
            file.inputStream().use { input ->
                tF.outputStream().use { output ->
                    input.copyTo(output)
                } }
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    fun previewImgIsExist(context: Context , fileId:String): Boolean {
        val tempLocalUrl = getFileCachePath(context, IMAGE_PREVIEW_FOLDER) + "/" + fileId + ".png"
        return File(tempLocalUrl).exists()
    }

    fun originImgIsExist(context: Context , fileId:String): Boolean {
        val tempLocalUrl = getFileCachePath(context, IMAGE_CACHE_FOLDER) + "/" + fileId + ".png"
        return File(tempLocalUrl).exists()
    }



    //获取缓存的文件夹路径
    fun getFileCachePath(context: Context, dir: String): String {
        var directoryPath = ""
        //判断SD卡是否可用
        directoryPath = if (MEDIA_MOUNTED == Environment.getExternalStorageState()) {
            context.getExternalFilesDir(dir)?.absolutePath ?: context.cacheDir.absolutePath
        } else {
            //没内存卡就存机身内存
            context.filesDir.absolutePath + File.separator + dir
        }
        val file = File(directoryPath)
        if (!file.exists()) { //判断文件目录是否存在
            file.mkdirs()
        }
        return directoryPath
    }

    fun isOriginImg(extOne: String?) : Boolean {
        try {
            val imgExt = GsonUtil.fromJson<ImSendImgExtEntity>(extOne ?:"")
            return imgExt?.is_pic_info == 1
        }catch (e: Exception){
            return false
        }
    }

}

data class ImSendImgExtEntity(
    var is_pic_info: Int? = 0
) : Serializable