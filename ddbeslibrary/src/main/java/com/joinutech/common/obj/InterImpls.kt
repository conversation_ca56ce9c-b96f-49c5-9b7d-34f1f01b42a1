package com.joinutech.common.obj

import com.alibaba.android.arouter.facade.Postcard
import com.alibaba.android.arouter.facade.callback.NavigationCallback

/**
 * @PackageName: com.joinutech.common.obj
 * @Desc: //TODO 预定义一些接口实现类，方便使用
 * @Author: z<PERSON><PERSON><PERSON>
 * @Leader: Ke
 * @CreateTime: 2020/4/22 9:14
 */
@Deprecated("no used")
class RouteCallback : NavigationCallback {
    override fun onLost(postcard: Postcard?) {
    }

    override fun onFound(postcard: Postcard?) {
    }

    override fun onInterrupt(postcard: Postcard?) {
    }

    override fun onArrival(postcard: Postcard?) {
    }

}