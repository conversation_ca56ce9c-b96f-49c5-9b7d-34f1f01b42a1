import android.app.Activity
import android.content.Context
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.common.base.LinkBuilder
import com.joinutech.ddbeslibrary.utils.COMMON_WEB_ACTIVITY
import com.joinutech.ddbeslibrary.utils.ConsKeys
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.ThridMapUtil
import com.joinutech.ddbeslibrary.utils.toast
import timber.log.Timber
import kotlin.math.abs
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt

/**
 * {
 *     "address": "京津冀大数据创新应用中心",
 *     "town": "耀华道街道",
 *     "nation": "中国",
 *     "city": "廊坊市",
 *     "county_code": "156131003",
 *     "poi_position": "西南",
 *     "county": "广阳区",
 *     "city_code": "156131000",
 *     "address_position": "西南",
 *     "poi": "京津冀大数据创新应用中心",
 *     "province_code": "156130000",
 *     "town_code": "156131003007",
 *     "province": "河北省",
 *     "road": "润泽道",
 *     "road_distance": 250,
 *     "address_distance": 89,
 *     "poi_distance": 89,
 *     "lon": 116.72155,
 *     "lat": 39.5975
 * }
 */
data class TiandituBody (
	val address : String?,
	val town : String?,
	val nation : String?,
	val city : String?,
	val county_code : Int?,
	val poi_position : String?,
	val county : String?,
	val city_code : Int?,
	val address_position : String?,
	val poi : String?,
	val province_code : Int?,
	val town_code : String?,
	val province : String?,
	val road : String?,
	val road_distance : Int,
	val address_distance : Int,
	val poi_distance : Int?,
	val lon : Double?,
	val lat : Double?,
	var uri: String? = null
)

data class NavigateBody(
	var type:Int?,
	var lat: Double?,
	var lon: Double?,
	var poiName: String?,
	var poiAddress: String?,
)

const val TIANDITU_URL = "https://mobile.ddbes.com/test-map/index.html#/"
enum class MapRouterType(var type: Int) {
	GET_LOCATION(1),
	TO_NAVIGATE(2),
	AUTO_LOCATION(3),
}

// 去地图获取位置，返回值
fun toShareLocation(context: Activity , requestCode: Int) {
	val url = "${TIANDITU_URL}?mapViewPageType=${MapRouterType.GET_LOCATION.type}"
	val mapUrl = LinkBuilder.generate().getResultUrl(url)
	ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
		.withString(ConsKeys.PARAMS_URL, mapUrl)
		.withString("type", "selectMapLocation")
		.withInt("backspaceKey",1)
		.withString("titleName", "请选择位置")
		.navigation(context ,requestCode)
}

// 去导航
fun toNavigate(lat: Double? ,lng:Double? , poiName:String? , poiAddress: String?) {
	val url = "${TIANDITU_URL}?mapViewPageType=${MapRouterType.TO_NAVIGATE.type}&lat=${lat}&lon=${lng}&poiName=${poiName}&poiAddress=${poiAddress}"
	val mapUrl = LinkBuilder.generate().getResultUrl(url)
	ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
		.withString(ConsKeys.PARAMS_URL, mapUrl)
		.withString("type", "selectMapLocation")
		.withString("titleName", "请选择位置")
		.withInt("backspaceKey", 1)
		.navigation()
}

// 自动定位（无界面）
fun autoLocation() {
	val url = "${TIANDITU_URL}?mapViewPageType=${MapRouterType.AUTO_LOCATION.type}"
	val mapUrl = LinkBuilder.generate().getResultUrl(url)
	ARouter.getInstance().build(COMMON_WEB_ACTIVITY)
		.withString(ConsKeys.PARAMS_URL, mapUrl)
		.withString("type", "selectMapLocation")
		.withString("titleName", "请选择位置")
		.withInt("backspaceKey", 1)
		.navigation()
}


fun navigate2Map(context: Context ,json: String) {
	try {
		val navigateBody = GsonUtil.fromJson<NavigateBody>(json)
		if (navigateBody == null){
			toast("未获取到位置")
			return
		}
		val lat = navigateBody.lat ?:0.0
		val lon = navigateBody.lon ?: 0.0
		val address = navigateBody.poiAddress ?:""
		if (navigateBody.type == 1){
			val (alon ,alat) = wgs84convertCj02(lon , lat)
			ThridMapUtil.goToTencentMap(context , alat!!, alon!! , address )
		}else if (navigateBody.type == 2){
			val (alon ,alat) = wgs84convertCj02(lon , lat)
			ThridMapUtil.startAmap(context , alat!!, alon!! , address )
		}else if (navigateBody.type == 3){
			val (alon ,alat) = gcj02ToBd09(lat , lon)
			ThridMapUtil.startBaiduMap(context , alat!!, alon!! , address )
		}
	}catch (e:Exception){
		toast("未获取到位置")
		e.printStackTrace()
	}
}


var X_PI = 3.14159265358979324 * 3000.0 / 180.0
var PI = 3.1415926535897932384626
var a = 6378245.0
var ee = 0.00669342162296594323

// 天地图 -> 高德/腾讯
fun wgs84convertCj02(lon: Double? , lat: Double?) : Coordinate {
	Timber.i("天地图 -> 高德 转换前: $lon , $lat")
	if(outOfChina(lon ,lat)) return Coordinate(lon ,lat)
	var dlat = transformlat(lon!! - 105.0, lat!! - 35.0)
	var dlng = transformlng(lon - 105.0, lat - 35.0)
	val radlat = lat / 180.0 * PI
	var magic = sin(radlat)
	magic = 1 - ee * magic * magic
	val sqrtmagic = sqrt(magic)
	dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI)
	dlng = (dlng * 180.0) / (a / sqrtmagic * cos(radlat) * PI)
	val mglat = lat + dlat
	val mglng = lon + dlng
	Timber.i("天地图 -> 高德 转换后: $mglng , $mglat")
	return  Coordinate(mglng, mglat)
}

// 天地图 -> 百度体育
fun gcj02ToBd09(lat: Double, lon: Double): Coordinate {
	val x = lon
	val y = lat
	val z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * X_PI)
	val theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * X_PI)
	val bdLon = z * Math.cos(theta) + 0.0065
	val bdLat = z * Math.sin(theta) + 0.006
	return Coordinate(bdLon, bdLat)
}

private fun transformlat(lng: Double, lat: Double): Double {
	var ret =
		-100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * sqrt(
			abs(lng)
		)
	ret += (20.0 * sin(6.0 * lng * PI) + 20.0 * sin(2.0 * lng * PI)) * 2.0 / 3.0
	ret += (20.0 * sin(lat * PI) + 40.0 * sin(lat / 3.0 * PI)) * 2.0 / 3.0
	ret += (160.0 * sin(lat / 12.0 * PI) + 320 * sin(lat * PI / 30.0)) * 2.0 / 3.0
	return ret
}

private fun transformlng(lng: Double, lat: Double): Double {
	var ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * sqrt(
		abs(lng)
	)
	ret += (20.0 * sin(6.0 * lng * PI) + 20.0 * sin(2.0 * lng * PI)) * 2.0 / 3.0
	ret += (20.0 * sin(lng * PI) + 40.0 * sin(lng / 3.0 * PI)) * 2.0 / 3.0
	ret += (150.0 * sin(lng / 12.0 * PI) + 300.0 * sin(lng / 30.0 * PI)) * 2.0 / 3.0
	return ret
}

// 是否在国内
private fun outOfChina(lon: Double?, lat: Double?): Boolean {
	if (lon == null || lat == null) return false
	return (lon < 72.004 || lon > 137.8347) || ((lat < 0.8293 || lat > 55.8271) || false)
}


data class Coordinate(var lon: Double?, var lat: Double?)