package com.joinutech.common.helper

import android.content.Context
import com.joinutech.ddbeslibrary.bean.PersonInfoBean
import com.joinutech.ddbeslibrary.utils.ConsKeys
import com.joinutech.ddbeslibrary.utils.MMKVKEY
import com.joinutech.ddbeslibrary.utils.MMKVUtil
import com.joinutech.ddbeslibrary.utils.USER

/**
 * @PackageName: com.joinutech.common.helper
 * @ClassName: LoginHelper
 * @Desc: 登录相关辅助操作处理
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/4/30 12:46
 */
object LoginHelper {

    fun isReadPrivacy(context: Context? = null, userId: String? = null): Boolean {
        return MMKVUtil.getBoolean(MMKVKEY.IS_READ_PRIVACY, false)
    }

    fun onPrivacyRead(userId: String? = null) {
        MMKVUtil.saveBoolean(MMKVKEY.IS_READ_PRIVACY, true)
    }

    fun onNewUserLogin(newAccount: String) {
        val oldAccount = MMKVUtil.getString(ConsKeys.PHONE)
        if (newAccount != oldAccount) {
            /**第一次使用验证码判断*/
            val decodeStringSet = if (MMKVUtil.containKey(MMKVKEY.FIRST_VERIFY_CODE_PHONES)) {
                MMKVUtil.getStringSet(MMKVKEY.FIRST_VERIFY_CODE_PHONES)
            } else {
                hashSetOf()
            }
            /**隐私政策提示 未看过隐私设置的用户要显示，需要记录已看过隐私设置的用户信息*/
            val privacyRead = MMKVUtil.getBoolean(MMKVKEY.IS_READ_PRIVACY, false)
            // 清理用户缓存信息
//            MMKVUtil.clearAll()
            MMKVUtil.saveString(ConsKeys.PHONE, newAccount)

            //只是设备第一次
            if (privacyRead) {
                MMKVUtil.saveBoolean(MMKVKEY.IS_READ_PRIVACY, privacyRead)
            }

            if (!decodeStringSet.isNullOrEmpty()) {
                MMKVUtil.saveStringSet(MMKVKEY.FIRST_VERIFY_CODE_PHONES, decodeStringSet)
            }
            // 清理数据库信息
//            try {
//                DDMessageDaoOpe.getInstance().deleteAllData(mContext)
//            } catch (e: Exception) {
//
//            }
        }
    }

    fun onLoginSuccess(personInfo: PersonInfoBean) {

    }
}