package com.joinutech.common.helper
//
//import android.content.Context
//import com.google.gson.Gson
//import com.google.gson.reflect.TypeToken
//import com.joinutech.common.base.isDebug
//import com.joinutech.ddbeslibrary.api.FriendCacheData
//import com.joinutech.ddbeslibrary.base.BaseApplication
//import com.joinutech.ddbeslibrary.bean.*
//import com.joinutech.ddbeslibrary.imtcp.ConstantTcpUtil
//import com.joinutech.ddbeslibrary.imtcp.Logger
//import com.joinutech.ddbeslibrary.imtcp.NoticeUtil.NoticeUtil
//import com.joinutech.ddbeslibrary.imtcp.dbbean.FriendBean
//import com.joinutech.ddbeslibrary.imtcp.dbbean.GroupInfoDbBean
//import com.joinutech.ddbeslibrary.imtcp.dbope.ApprovalNotificationDaoOpe
//import com.joinutech.ddbeslibrary.imtcp.dbope.FriendDaoOpe
//import com.joinutech.ddbeslibrary.imtcp.dbope.GroupDaoOpe
//import com.joinutech.ddbeslibrary.imtcp.imbean.ImTokenBean
//import com.joinutech.ddbeslibrary.imtcp.imservice.netutil.netbean.OffLineUnReadApproCountBean
//import com.joinutech.ddbeslibrary.request.CommonResult
//import com.joinutech.ddbeslibrary.request.RxScheduleUtil
//import com.joinutech.ddbeslibrary.request.exception.ApiException
//import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
//import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
//import com.joinutech.ddbeslibrary.service.UserService
//import com.joinutech.ddbeslibrary.utils.*
//import com.marktoo.lib.cachedweb.LogUtil
//import com.trello.rxlifecycle3.LifecycleTransformer
//import io.reactivex.Observable
//import io.reactivex.ObservableOnSubscribe
//import io.reactivex.Observer
//import io.reactivex.android.schedulers.AndroidSchedulers
//import io.reactivex.disposables.Disposable
//import io.reactivex.schedulers.Schedulers
//import java.util.*
//import kotlin.collections.LinkedHashMap
//
///**
// * 内存缓存用户信息，最多三十人
// * 如果不存在，先获取好友信息，存在则返回
// * 如果好友信息不存在，则获取用户信息，存在则返回
// * 不存在，则接口请求用户信息，请求成功后，保存用户缓存，事件更新已打开页面中存在当前用户信息的位置UI
// *
// * */
///**
// * @PackageName: com.joinutech.common.helper
// * @ClassName: CacheDataHolder
// * @Author: zhaoyy
// * @Leader: Ke
// * @CreateTime: 2020/5/13 13:10
// * @Desc: 缓存数据工具类
// */
///**好友和群成员相关信息缓存*/
//object CacheDataHolder : CacheHolder {
//    private val maxSize = 40
//    private var userInfoCache: LinkedHashMap<String, UserInfo> = LinkedHashMap(40, 0.75f, true)
//
//    @Volatile
//    var lock = Object()
//
//    private fun cacheInfo(targetId: String, userInfo: UserInfo) {
//        synchronized(lock) {
//            if (!userInfoCache.containsKey(targetId)) {
//                if (userInfoCache.size >= maxSize) {
//                    showLog("需要清理消息 ${userInfoCache.size}")
//                    userInfoCache.remove(userInfoCache.keys.toList()[0])
//                    userInfoCache.remove(userInfoCache.keys.toList()[0])
//                    userInfoCache.remove(userInfoCache.keys.toList()[0])
//                    userInfoCache.remove(userInfoCache.keys.toList()[0])
//                    userInfoCache.remove(userInfoCache.keys.toList()[0])
//                    userInfoCache.remove(userInfoCache.keys.toList()[0])
//                    userInfoCache.remove(userInfoCache.keys.toList()[0])
//                    userInfoCache.remove(userInfoCache.keys.toList()[0])
//                    userInfoCache.remove(userInfoCache.keys.toList()[0])
//                    userInfoCache.remove(userInfoCache.keys.toList()[0])
//                }
//            }
//            lock.notifyAll()
//        }
//        userInfoCache[targetId] = userInfo
//    }
//
//    private fun getCache(targetId: String): UserInfo? {
//        return userInfoCache[targetId]
//    }
//
//    /**获取群组信息*/
//    fun getGroupInfo(context: Context, targetId: String): UserInfo {
//        val user = getCache(targetId)
//        if (user != null && !user.userName.isNullOrBlank()) {
//            return user
//        } else {
//            val userInfo = GroupCacheHolder.getGroupInfo(targetId)
//            return if (userInfo != null && !userInfo.userName.isNullOrBlank()) {
//                cacheInfo(targetId, userInfo)
//                userInfo
//            } else {
//                val bean = GroupDaoOpe.instance
//                        .queryGroupInfoByGroupId(context, targetId)
//                if (bean != null) {
//                    val user = UserInfo(targetId, bean.logo ?: "", bean.name ?: "")
//                    GroupCacheHolder.setGroupInfo(user)
//                    user
//                } else {
//                    UserInfo(targetId, "", "")
//                }
//            }
//        }
//    }
//
//    fun setGroupInfo(targetId: String, userInfo: UserInfo) {
//        cacheInfo(targetId, userInfo)
//    }
//
//    /**获取公司信息*/
//    fun getCompanyInfo(targetId: String): UserInfo {
//        val user = getCache(targetId)
//        if (user != null && !user.userName.isNullOrBlank() && !user.userIcon.isNullOrBlank()) {
//            return user
//        } else {
//            val companyInfo = CompanyHolder.getOrgInfo(targetId)
//            if (companyInfo != null && !companyInfo.userName.isNullOrBlank()) {
//                return companyInfo
//            } else {
//                val temp = CompanyHolder.getAllCompanyMap()
//                if (temp.containsKey(targetId)) {
//                    val workstation = temp[targetId] ?: return UserInfo(targetId)
//                    val user = UserInfo(targetId, workstation.logo, workstation.name)
//                    CompanyHolder.setOrgInfo(user)
//                    cacheInfo(targetId, user)
//                    return user
//                }
//            }
//            return UserInfo(targetId, "", "")
//        }
//    }
//
//    /**获取用户信息
//     * 先查询缓存信息
//     * 在查询好友信息 --- 不再查询好友数据，仅查询用户数据
//     * 再查询用户信息*/
//    fun getUserInfo(targetId: String): UserInfo {
//        val user = getCache(targetId)
//        if (user != null && !user.userName.isNullOrBlank()) {
//            return user
//        } else {
//            val friend = FriendCacheHolder.getFriend(targetId)
//            if (friend != null) {
//                cacheInfo(targetId, friend)
//                return friend
//            } else {
//                val userInfo = UserHolder.getUser(targetId)
//                if (userInfo != null) {
//                    cacheInfo(targetId, userInfo)
//                    return userInfo
//                } else {
//                    loadUserInfo()
//                }
//            }
//        }
//        return UserInfo(targetId, "", "")
//    }
//
//    private fun loadUserInfo() {
//        // TODO: 2020/7/13 9:29 加载用户信息数据 请求成功后，写缓存，事件同步更新到UI，需要更新的位置
//
//    }
//
//    fun setUserInfo(targetId: String, userInfo: UserInfo) {
//        cacheInfo(targetId, userInfo)
//    }
//
//    fun savePushStateLastCheck(time: Long) {
//        MMKVUtil.saveLong(MSG_PUSH_CHECK_LAST_TIME, time)
//    }
//
//    fun needCheckPushState(): Boolean {
//        val last = MMKVUtil.getLong(MSG_PUSH_CHECK_LAST_TIME)
//        if (last == 0L) return true
//        val lastCalendar = Calendar.getInstance()
//        lastCalendar.timeInMillis = last
//        val currentCalendar = Calendar.getInstance()
//        return if (currentCalendar.after(lastCalendar)) {
//            val day1 = lastCalendar.get(Calendar.DAY_OF_YEAR)
//            val day2 = currentCalendar.get(Calendar.DAY_OF_YEAR)
//            day2 > day1
//        } else {
//            false
//        }
//    }
//
//    fun sysPushEnable(context: Context): Boolean {
//        return NotificationUtil.isNotificationEnabled(context)
//    }
//
//    /**获取系统消息免打扰设置*/
//    fun msgPushEnable(): Boolean {
//        return MMKVUtil.getInt(MSG_NOTIFICATION_ACTION, 1) > 0
//    }
//
//    fun msgPushVibEnable(): Boolean {
//        return MMKVUtil.getInt(MSG_NOTIFICATION_VIBRATES, 1) > 0
//    }
//
//    fun msgPushSoundEnable(): Boolean {
//        return MMKVUtil.getInt(MSG_NOTIFICATION_SOUND, 1) > 0
//    }
//
//    fun saveMsgPushEnable(state: Int) {
//        MMKVUtil.saveInt(MSG_NOTIFICATION_ACTION, state)
//        EventBusUtils.sendEvent(EventBusEvent(MSG_NOTIFY_CHANGE, ""))
//    }
//
//    fun saveMsgPushVibEnable(state: Int) {
//        MMKVUtil.saveInt(MSG_NOTIFICATION_VIBRATES, state)
//    }
//
//    fun saveMsgPushSoundEnable(state: Int) {
//        MMKVUtil.saveInt(MSG_NOTIFICATION_SOUND, state)
//    }
//
//    /**
//     * 缓存用户信息
//     * */
//    fun refreshUserInfo(info: UserInfo) {
//        try {
//            MMKVUtil.saveString(info.userId, GsonUtil.toJson(info))
//            LogUtil.showLog("IM ::  缓存IM用户信息${info.userName} 成功", "user__")
//        } catch (e: Exception) {
//            LogUtil.showLog("IM ::  缓存IM用户信息 失败", "user__")
//        }
//    }
//
//    override fun init() {
//    }
//
//    override fun onLogout() {
//        clearCache(MSG_PUSH_CHECK_LAST_TIME)
//    }
//
//}
//
///**
// * 好友数据查询
// * 好友数据处理
// * */
//object FriendCacheHolder {
//
//    fun showLog(msg: String) {
//        LogUtil.showLog(msg, "friend__")
//    }
//
//    /**获取好友数据版本信息*/
//    fun checkFriendDataVersion(context: Context) {
//        RxScheduleUtil.rxSchedulerHelper(UserService.getFriendListVersion())
//                .compose(ErrorTransformer.getInstance())
//                .subscribe(object : BaseSubscriber<String>() {
//                    override fun onError(ex: ApiException) {
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(newVersion: String?) {
//                        if (newVersion.isNullOrBlank() || checkFriendVersion(newVersion)) {
//                            showLog("需要更新好友数据")
//                            getFriendCacheList(context)
//                        } else {
//                            showLog("不需要更新好友数据")
//                        }
//                    }
//                })
//    }
//
//    /**获取好友数据版本*/
//    private fun getFriendDataVersion(): String {
//        val friendVersionKey = "${UserHolder.getUserId()}_${ConsKeys.FRIEND_CACHE_VERSION}"
//        showLog("versionKey is $friendVersionKey")
//        return MMKVUtil.getString(friendVersionKey)
//    }
//
//    /**保存好友数据版本*/
//    fun saveFriendDataVersion(newVersion: String?) {
//        val friendVersionKey = "${UserHolder.getUserId()}_${ConsKeys.FRIEND_CACHE_VERSION}"
//        MMKVUtil.saveString(friendVersionKey, newVersion ?: "")
//    }
//
//    /**比较好友数据版本*/
//    fun checkFriendVersion(version: String): Boolean {
//        val oldVersion = getFriendDataVersion()
//        showLog("oldVersion is $oldVersion ，newVersion is $version")
//        return oldVersion != version
//    }
//
//    /**获取好友数据*/
//    fun getFriendCacheList(context: Context) {
//        getFriendCacheListWithCallback(context, onSuccess = {}, onError = {})
//    }
//
//    /**
//     * 获取好友数据
//     * ApplicationDetailActivity 添加好友
//     * FriendInfoActivity 用户详情页面，添加好友时，如果已经是好友或添加好友成功，则刷新好友列表
//     * OrgExternalContactDetailActivity 外部联系人详情页面，添加好友时，如果已经是好友或添加好友成功，则刷新好友列表
//     * */
//    fun getFriendCacheListWithCallback(context: Context,
//                                       onSuccess: (list: List<FriendBean>) -> Unit,
//                                       onError: (msg: String) -> Unit) {
//        RxScheduleUtil.rxSchedulerHelper(UserService.getFriendListCache())
//                .compose(ErrorTransformer.getInstance())
//                .subscribe(object : BaseSubscriber<FriendCacheData>() {
//                    override fun onError(ex: ApiException) {
//                        onError.invoke(ex.message)
//                    }
//
//                    override fun onComplete() {
//
//                    }
//
//                    override fun onNext(cache: FriendCacheData?) {
//                        if (cache != null) {
//                            saveFriendDataVersion(cache.friendVersion)
//                            if (!cache.friendVersion.isNullOrBlank() && !cache.friendList.isNullOrEmpty()) {
//                                saveFriendCacheData(context, cache.friendList)
//                                onSuccess.invoke(cache.friendList)
//                            } else {
//                                saveFriendCacheData(context, arrayListOf())
//                                onSuccess.invoke(arrayListOf())
//                            }
//                        }
//                    }
//                })
//    }
//
//    /**保存好友数据*/
//    fun saveFriendCacheData(context: Context, friendList: List<FriendBean>) {
//        friendList.forEach {
//            saveFriend(// 更新好友列表数据时，更新缓存好友信息
//                    UserInfo(it.userId ?: "", it.avatar ?: "", it.name ?: "", it.remark ?: ""))
//        }
//        FriendDaoOpe.instance.deleteAllData(context)
//        FriendDaoOpe.instance.saveDataList(context, friendList)
//        EventBusUtils.sendEvent(EventBusEvent(IMEvent.UPDATE_FRIEND_VERSION, ""))
////        EventBusUtils.sendEvent(EventBusEvent(IMEvent.CHANGE_FRIEND,""))
//    }
//
//    //tcp保存好友数据
//    fun saveAppFriendCacheData(context: Context, friendList: List<FriendBean>) {
//        BaseApplication.getWebSocketInstance()?.cleanFriendIdSet()
//        val logoutIds = getMyLogoutFriendIds()
//        logoutIds.clear()
//        friendList.forEach {
//            saveFriend(// 将简介缓存到mkv    用的key是“F_当前用户id_目标用户id”
//                    UserInfo(it.userId ?: "", it.avatar ?: "", it.name ?: "", it.remark
//                            ?: "", logout = it.logout))
//            if (it.logout == 1) {
//                logoutIds.add(it.userId)
//            } else {
//                //将好友的id放到服务中的一个集合
//                BaseApplication.getWebSocketInstance()?.addFriendIdIntoSet(it.userId)
//            }
//        }
//        updateLogoutFriendList(logoutIds)
//        //存数据库
//        FriendDaoOpe.instance.deleteAllData(context)
//
//        FriendDaoOpe.instance.saveDataList(context, friendList)
////        EventBusUtils.sendEvent(EventBusEvent(IMEvent.CHANGE_FRIEND,""))
//    }
//
//    fun updateLogoutFriendList(logoutFriendIds: HashSet<String>) {
//        MMKVUtil.saveStringSet("F_${UserHolder.getUserId()}_logout", logoutFriendIds.toSet())
//    }
//
//    fun getMyLogoutFriendIds(): HashSet<String> {
//        return MMKVUtil.getStringSet("F_${UserHolder.getUserId()}_logout").toHashSet()
//    }
//
//    /**加载好友数据缓存*/
//    fun loadFriend(context: Context, lifecycle: LifecycleTransformer<List<FriendBean>>,
//                   onResult: (list: List<FriendBean>) -> Unit,
//                   onError: (msg: String) -> Unit) {
//        showLog("加载所有好友信息")
//        Observable.create(ObservableOnSubscribe<List<FriendBean>> {
//            val temp = FriendDaoOpe.instance.getAllFriendList(context)
//            Logger.i("---执行---从数据库取出好友列表---", "--temp?.size--" + temp?.size)
//            if (!temp.isNullOrEmpty()) {
//                it.onNext(temp)
//            } else {
//                it.onError(IllegalStateException("暂无好友信息"))
//            }
//        }).compose(lifecycle)
//                .subscribeOn(Schedulers.io())
//                .observeOn(AndroidSchedulers.mainThread())
//                .subscribe(object : Observer<List<FriendBean>> {
//                    override fun onComplete() {
//                    }
//
//                    override fun onSubscribe(d: Disposable) {
//                    }
//
//                    override fun onNext(list: List<FriendBean>) {
//                        showLog("加载所有好友信息 成功${list.size}")
//                        onResult.invoke(list)
//                    }
//
//                    override fun onError(e: Throwable) {
//                        showLog("加载所有好友信息 失败")
//                        onError.invoke(e.message ?: "")
//                    }
//                })
//    }
//
//    /**
//     * 加载好友数据缓存
//     * 根据好友id获取好友信息，主要用于获取好友关系信息，发送im消息时判定是否可以发送消息
//     */
//    fun loadFriendByIds(context: Context, ids: List<String>,
//                        onResult: (list: List<FriendBean>) -> Unit,
//                        onError: (msg: String) -> Unit) {
//        val tag = "根据好友id获取好友信息"
//        showLog(tag)
//        Observable.create(ObservableOnSubscribe<List<FriendBean>> {
//            val temp = FriendDaoOpe.instance.queryFriendByUserIds(context, ids)
//            if (!temp.isNullOrEmpty()) {
//                it.onNext(temp)
//            } else {
//                it.onError(IllegalStateException("暂无好友信息"))
//            }
//        }).subscribeOn(Schedulers.computation())
//                .observeOn(AndroidSchedulers.mainThread())
//                .subscribe(object : Observer<List<FriendBean>> {
//                    override fun onComplete() {
//                    }
//
//                    override fun onSubscribe(d: Disposable) {
//                    }
//
//                    override fun onNext(list: List<FriendBean>) {
//                        showLog("$tag 成功${list.size}")
//                        onResult.invoke(list)
//                    }
//
//                    override fun onError(e: Throwable) {
//                        showLog("$tag 失败")
//                        onError.invoke(e.message ?: "")
//                    }
//                })
//    }
//
//    /**搜索好友缓存数据*/
//    fun searchFriend(context: Context, lifecycle: LifecycleTransformer<List<FriendBean>>,
//                     keyWord: String, onResult: (list: List<FriendBean>) -> Unit,
//                     onError: (msg: String) -> Unit) {
//        Observable.create(ObservableOnSubscribe<List<FriendBean>> {
//            val temp = FriendDaoOpe.instance.queryFriendListByKeyWord(context, keyWord)
//            if (!temp.isNullOrEmpty()) {
//                it.onNext(temp)
//            } else {
//                it.onError(IllegalStateException("暂无好友信息"))
//            }
//        }).compose(lifecycle)
//                .subscribeOn(Schedulers.io())
//                .observeOn(AndroidSchedulers.mainThread())
//                .subscribe(object : Observer<List<FriendBean>> {
//                    override fun onComplete() {
//                    }
//
//                    override fun onSubscribe(d: Disposable) {
//                    }
//
//                    override fun onNext(list: List<FriendBean>) {
//                        onResult.invoke(list)
//                    }
//
//                    override fun onError(e: Throwable) {
//                        onError.invoke(e.message ?: "")
//                    }
//                })
//    }
//
//    /**更新好友信息到缓存 好友详情页面加载好友信息时更新*/
//    fun updateFriendInfo(context: Context, friend: FriendBean, onResult: (result: String) -> Unit,
//                         onError: (msg: String) -> Unit) {
//        Observable.create(ObservableOnSubscribe<String> {
//            val temp = FriendDaoOpe.instance.queryFriendByUserId(context, friend.userId)
//            if (temp != null) {
//                temp.avatar = friend.avatar ?: ""
//                temp.remark = friend.remark
//                temp.name = friend.name ?: ""
////                temp.relation = friend.relation// 好友关系
//                temp.logout = friend.logout// 好友注册状态
//                FriendDaoOpe.instance.updateFriend(context, temp)
//                it.onNext("1")
////            } else {
////                FriendBeanDaoOpe.instance.insertData(context, user)
//            } else {
//                FriendDaoOpe.instance.saveFriend(context, friend)
//            }
//        }).subscribeOn(Schedulers.io())
//                .observeOn(AndroidSchedulers.mainThread())
//                .subscribe(object : Observer<String> {
//                    override fun onComplete() {
//                    }
//
//                    override fun onSubscribe(d: Disposable) {
//                    }
//
//                    override fun onNext(data: String) {
//                        onResult.invoke(data)
//                    }
//
//                    override fun onError(e: Throwable) {
//                        onError.invoke(e.message ?: "")
//                    }
//                })
//    }
//
//    /**移除好友信息*/
//    fun removeFriendInfo(context: Context, friendUserId: String,
//                         onResult: (result: String) -> Unit,
//                         onError: (msg: String) -> Unit) {
//        Observable.create(ObservableOnSubscribe<String> {
//            val temp = FriendDaoOpe.instance.queryFriendByUserId(context, friendUserId)
//            if (temp != null) {
//                FriendDaoOpe.instance.deleteData(context, temp.id)
//                it.onNext("1")
//            }
//        }).subscribeOn(Schedulers.io())
//                .observeOn(AndroidSchedulers.mainThread())
//                .subscribe(object : Observer<String> {
//                    override fun onComplete() {
//                    }
//
//                    override fun onSubscribe(d: Disposable) {
//                    }
//
//                    override fun onNext(data: String) {
//                        onResult.invoke(data)
//                    }
//
//                    override fun onError(e: Throwable) {
//                        onError.invoke(e.message ?: "")
//                    }
//                })
//    }
//
//    /**
//     * F_当前用户id_好友userId*/
//    /**保存好友信息
//     * 保存好友关系简略缓存信息
//     *
//     * 添加好友后更新
//     * 加载好友信息后更新
//     * 修改备注信息后更新
//     * */
//    fun saveFriend(user: UserInfo) {
//        MMKVUtil.saveString("F_${UserHolder.getUserId()}_${user.userId}", GsonUtil.toJson(user))
//        // 更新与用户缓存信息中好友信息系
//        CacheDataHolder.setUserInfo(user.userId, user)
//    }
//
//    /**移除好友信息*/
//    fun removeFriend(userId: String) {
//        MMKVUtil.clearByKey("F_${UserHolder.getUserId()}_$userId")
//        // -- 获取缓存中数据并清理好友别名信息后保存到用户信息缓存中
//        val info = CacheDataHolder.getUserInfo(userId)
//        info.userNick = null
//        CacheDataHolder.setUserInfo(userId, info)
//        FriendDaoOpe.instance.deleteFriendByUserId(BaseApplication.joinuTechContext,userId)
//        // -- 获取缓存中数据并清理好友别名信息后保存到用户信息缓存中
//        EventBusUtils.sendEvent(EventBusEvent(ConsKeys.FRIEND_INFO_UPDATE, userId))
//    }
//
//    /**获取好友信息简介*/
//    fun getFriend(userId: String): UserInfo? {
//        return GsonUtil.fromJson(MMKVUtil.getString("F_${UserHolder.getUserId()}_$userId"), UserInfo::class.java)
//    }
//}
//
///**
// * 群组信息查询
// * */
//object GroupCacheHolder : CacheHolder {
//
//    /**@param type 2 私有群组 1 团队群组*/
//    fun getAllGroup(life: LifecycleTransformer<Result<List<GroupListBean>>>, type: Int = 2,
//                    onResult: (CommonResult<List<GroupListBean>>) -> Unit) {
//        /*  GroupService.getGroupList(type)
//                  .compose(life).compose(ErrorTransformer.getInstance())
//                  .subscribe(object : BaseSubscriber<List<GroupListBean>>() {
//                      override fun onError(ex: ApiException) {
//                          onResult.invoke(CommonResult(errorCode = 0, extra = ex.message))
//                      }
//
//                      override fun onComplete() {}
//
//                      override fun onNext(list: List<GroupListBean>?) {
//                          onResult.invoke(CommonResult(success = list))
//                      }
//                  })*/
//    }
//
//    /**保存群组列表，更新群成员信息，会查询好友列表*/
//    fun saveGroupList(list: List<GroupInfoBean>?) {
//        showLog("保存群组列表")
//        Observable.create(ObservableOnSubscribe<String> {
//            try {
//                val dbList = arrayListOf<GroupInfoDbBean>()
//                list?.forEach { groupInfo ->
//                    run {
//                        // 保存群组信息
//                        dbList.add(turnGroupToDb(groupInfo))
//                    }
//                }
//                GroupDaoOpe.instance.saveDataList(BaseApplication.joinuTechContext, dbList)
//                it.onNext("1")
//            } catch (e: Exception) {
//                it.onError(Throwable("0"))
//            }
//        })
//                .subscribeOn(Schedulers.io())
//                .observeOn(AndroidSchedulers.mainThread())
//                .subscribe(object : Observer<String> {
//                    override fun onComplete() {
//                    }
//
//                    override fun onSubscribe(d: Disposable) {
//                    }
//
//                    override fun onNext(data: String) {
//                    }
//
//                    override fun onError(e: Throwable) {
//                    }
//                })
//    }
//
//    //tcp保存群组列表
//    fun saveAppGroupList(lifecycle: LifecycleTransformer<String>, list: List<GroupInfoDbBean>?) {
//        showLog("保存群组列表")
//        Observable.create(ObservableOnSubscribe<String> {
//            try {
//                GroupDaoOpe.instance.deleteAllData(BaseApplication.joinuTechContext)
//                GroupDaoOpe.instance.insertGroupList(BaseApplication.joinuTechContext, list)
//                list?.forEach { groupInfoDbBean ->
//                    setGroupInfo(UserInfo(groupInfoDbBean.groupId, groupInfoDbBean.logo, groupInfoDbBean.name,
//                            companyId = groupInfoDbBean.orgId), needRefresh = false)
//                }
//                it.onNext("1")
//            } catch (e: Exception) {
//                it.onError(Throwable("0"))
//            }
//        })
//                .subscribeOn(Schedulers.io())
//                .observeOn(AndroidSchedulers.mainThread())
//                .subscribe(object : Observer<String> {
//                    override fun onComplete() {
//                    }
//
//                    override fun onSubscribe(d: Disposable) {
//                    }
//
//                    override fun onNext(data: String) {
//                    }
//
//                    override fun onError(e: Throwable) {
//                    }
//                })
//    }
//
//    fun saveGroupDataInDB(lifecycle: LifecycleTransformer<String>, groupInfo: GroupInfoBean) {
//        Observable.create(ObservableOnSubscribe<String> {
//            // 保存群组信息
//            try {
//                GroupDaoOpe.instance.updateGroup(BaseApplication.joinuTechContext, turnGroupToDb(groupInfo))
//                it.onNext("1")
//            } catch (e: Exception) {
//                it.onError(Throwable("0"))
//            }
//        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
//                .subscribe(object : Observer<String> {
//                    override fun onComplete() {
//                    }
//
//                    override fun onSubscribe(d: Disposable) {
//                    }
//
//                    override fun onNext(data: String) {
//                    }
//
//                    override fun onError(e: Throwable) {
//                    }
//                })
//    }
//
//    private fun turnGroupToDb(groupInfo: GroupInfoBean): GroupInfoDbBean {
//        if (isDebug) {
//            showLog("转换群组信息 ${GsonUtil.toJson(groupInfo)}")
//        }
//        setGroupInfo(UserInfo(groupInfo.groupId, groupInfo.logo, groupInfo.name))
//
//        val bean = GroupInfoDbBean()
//        bean.createTime = groupInfo.createTime
//        bean.createUserId = groupInfo.createUserId
//        bean.logo = groupInfo.logo
//        bean.name = groupInfo.name
//        bean.orgId = groupInfo.orgId
//        bean.groupId = groupInfo.groupId
//        bean.receiveMode = groupInfo.receiveMode
//        bean.type = groupInfo.type
//        bean.initial = groupInfo.initial
//        if (!groupInfo.users.isNullOrEmpty()) {
//            groupInfo.users.forEach { groupMemberBean ->
//                try {
//                    val daoBean = FriendDaoOpe.instance
//                            .queryFriendByUserId(BaseApplication.joinuTechContext, groupMemberBean.id)
//                    if (daoBean != null && StringUtils.isNotBlankAndEmpty(daoBean.remark)) {
//                        groupMemberBean.remarkName = daoBean.remark
//                        FriendCacheHolder.saveFriend( // 更新群成员信息
//                                UserInfo(userId = groupMemberBean.id, userIcon = groupMemberBean.headimg,
//                                        userName = groupMemberBean.name, userNick = daoBean.remark))
//                    } else {
//                        UserHolder.saveUser(// 更新群成员信息
//                                UserInfo(groupMemberBean.id, groupMemberBean.headimg,
//                                        groupMemberBean.name))
//                    }
//                } catch (e: Exception) {
//                }
//            }
//            bean.users = GsonUtil.toJson(groupInfo.users)
//        } else {
//            bean.users = GsonUtil.toJson(arrayListOf<GroupMemberBean>())
//        }
//        return bean
//    }
//
//    fun getGroupInfo(groupId: String): UserInfo? {
//        return GsonUtil.fromJson(MMKVUtil.getString("G_${groupId}"), UserInfo::class.java)
//    }
//
//    /**
//     * 更新群信息
//     *
//     * 加载群信息后调用
//     * 修改群名称后调用
//     * 读取数据库中群信息后调用
//     * 收到群通知相关信息中携带群信息时调用
//     * */
//    fun setGroupInfo(userInfo: UserInfo, needRefresh: Boolean = true) {
//        MMKVUtil.saveString("G_${userInfo.userId}", GsonUtil.toJson(userInfo))
//        CacheDataHolder.setGroupInfo(userInfo.userId, userInfo)// 内存缓存
//        if (needRefresh == true) {
//            EventBusUtils.sendEvent(EventBusEvent(ConsKeys.GROUP_INFO_UPDATE, userInfo.userId))
//        }
//
//    }
//
//    /**设置免打扰的群id缓存*/
//    fun saveDisturbSetting(disturbGroups: MutableSet<String>?) {
//        if (UserHolder.isLogin()) {
//            if (disturbGroups.isNullOrEmpty()) {
//                clearCache(GROUP_NO_DISTURB + "_${UserHolder.getUserId()}")
//            } else {
//                MMKVUtil.saveStringSet(GROUP_NO_DISTURB + "_${UserHolder.getUserId()}", disturbGroups)
//            }
//            EventBusUtils.sendEvent(EventBusEvent(MSG_NOTIFY_CHANGE, ""))
//        }
//    }
//
//    /**获取免打扰的群id缓存*/
//    fun getDisturbSetting(): MutableSet<String> {
//        return if (UserHolder.isLogin()) {
//            MMKVUtil.getStringSet(GROUP_NO_DISTURB + "_${UserHolder.getUserId()}")
//        } else {
//            hashSetOf()
//        }
//    }
//
//    /**
//     * 保存离线前最后一条消息，msgId以便于
//     * //        MMKVUtil.saveString(key = "${msgBean.groupId}OFFLINE", value = msgBean.msgId)// 存储对应群组的最后一条消息的消息id
//     * 个人的配置信息，退出登录时需要清理
//     */
//    fun saveGroupOffMsg(groupId: String, messageId: String) {
//        if (UserHolder.isLogin()) {
//            saveGroupOffSetting(groupId)
//            MMKVUtil.saveString("OFF_${UserHolder.getUserId()}_$groupId", messageId)
//        }
//    }
//
//    private fun saveGroupOffSetting(groupId: String) {
//        val groupOffList = MMKVUtil.getStringSet("GOFF_${UserHolder.getUserId()}")
//        if (!groupOffList.contains(groupId)) {
//            groupOffList.add(groupId)
//            MMKVUtil.saveStringSet("GOFF_${UserHolder.getUserId()}", groupOffList)
//        }
//    }
//
//    private fun clearGroupOffSetting(groupId: String) {
//        val groupOffList = MMKVUtil.getStringSet("GOFF_${UserHolder.getUserId()}")
//        if (groupOffList.contains(groupId)) {
//            groupOffList.remove(groupId)
//            MMKVUtil.saveStringSet("GOFF_${UserHolder.getUserId()}", groupOffList)
//        }
//    }
//
//    fun clearGroupOffMsg(groupId: String) {
//        if (UserHolder.isLogin()) {
//            clearGroupOffSetting(groupId)
//            clearCache("OFF_${UserHolder.getUserId()}_$groupId")
//        }
//    }
//
//    /**群组最后一条消息msg*/
//    fun getGroupOffMsg(groupId: String): String {
//        return if (UserHolder.isLogin()) {
//            MMKVUtil.getString("OFF_${UserHolder.getUserId()}_$groupId")
//        } else {
//            ""
//        }
//    }
//
//    /**
//     * 新用户登录后，清理旧的免打扰信息配置
//     * */
//    override fun init() {
//    }
//
//    override fun onLogout() {
//        if (UserHolder.isLogin()) {
//            val groupOffList = MMKVUtil.getStringSet("GOFF_${UserHolder.getUserId()}")
//            groupOffList.forEach {
//                clearCache("OFF_${UserHolder.getUserId()}_$it")
//            }
//            groupOffList.clear()
//            clearCache("GOFF_${UserHolder.getUserId()}")
//        }
//    }
//}
//
//class MyObServer<T>(val onSuccess: (data: T) -> Unit, val onError: (msg: String) -> Unit) : Observer<T> {
//    override fun onComplete() {
//    }
//
//    override fun onSubscribe(d: Disposable) {
//    }
//
//    override fun onNext(t: T) {
//    }
//
//    override fun onError(e: Throwable) {
//    }
//
//}
//
//object AppVersionHelper {
//    fun needUpdate(): Boolean {
//        return false
//    }
//}
//
///**
// * 团队信息查询
// * 当前团队查询
// * 主要团队查询
// * 所有团队查询
// * 所有协作团队查询
// * */
//object CompanyHolder : CacheHolder {
//
//    private var mainOrgId: String? = null
//
//    /**当前团队*/
//    private var currentOrg: WorkStationBean? = null
//
//    /**当前所有团队*/
//    private var allOrg: ArrayList<WorkStationBean> = arrayListOf()
//
//    /**当前协作团队*/
//    private var allCooperation: ArrayList<WorkStationBean> = arrayListOf()
//
//    fun getCurrentOrg(): WorkStationBean? {
//        if (currentOrg == null) {
//            currentOrg = GsonUtil.fromJson(MMKVUtil.getString(CURRENTCOMPANY),
//                    WorkStationBean::class.java)
//        }
//        return currentOrg
//    }
//
//    fun saveCurrentOrg(work: WorkStationBean?) {
//        currentOrg = work
//        if (work != null) {
//            MMKVUtil.saveString(CURRENTCOMPANY, GsonUtil.toJson(work))
//        } else {
//            MMKVUtil.saveString(CURRENTCOMPANY, "")
//        }
//    }
//
//    fun getAllOrg(): ArrayList<WorkStationBean> {
//        if (allOrg.isNullOrEmpty()) {
//            allOrg = GsonUtil.getList2(MMKVUtil.getString(COMPANIES),
//                    WorkStationBean::class.java) ?: arrayListOf()
//        }
//        return allOrg
//    }
//
//    /**获取所有团队信息*/
//    //tcp获取所有团队的数据（包含合作团队）
//    fun getAllCompanies(): ArrayList<WorkStationBean> {
//        val list = arrayListOf<WorkStationBean>()
//        list.addAll(getAllOrg())
//        list.addAll(getCooperationOrg())
//        return list
//    }
//
//    /**获取所有团队信息*/
//    fun getAllCompanyMap(): Map<String, WorkStationBean> {
//        if (getAllCompanies().isNotEmpty()) {
//            return getAllCompanies().map { it.companyId to it }.toMap()
//        }
//        return hashMapOf()
//    }
//
//    fun updataAllCompanysUnReadCount(context: Context, mList: List<OffLineUnReadApproCountBean>) {
//        val currentUserId = UserHolder.getUserId()
//        val faulTime = System.currentTimeMillis()
//        val allOrgs = getAllOrg()
//        val allCooperationOrgs = getCooperationOrg()
//        allOrgs.forEach { it2 ->
//            val offlineFind = mList.find { it.tag == it2.companyId }
//            if (offlineFind != null) {
//                it2.haveApprove += offlineFind.count
//                //如果该公司的审批有离线未读数，就插入断点（审批表插断点）
//                if (offlineFind.count > 0) {
//                    ApprovalNotificationDaoOpe.instance.saveOffLineFaultMessage(context, currentUserId, it2.companyId, 0, faulTime)
//                }
//            }
//        }
//        allCooperationOrgs.forEach { it2 ->
//            val offlineFind = mList.find { it.tag == it2.companyId }
//            if (offlineFind != null) {
//                it2.haveApprove += offlineFind.count
//                //如果该公司的审批有离线未读数，就插入断点（审批表插断点）
//                if (offlineFind.count > 0) {
//                    ApprovalNotificationDaoOpe.instance.saveOffLineFaultMessage(context, currentUserId, it2.companyId, 0, faulTime)
//                }
//            }
//        }
//        saveAllOrg(allOrgs)
//        saveCooperationOrg(allCooperationOrgs)
//
//    }
//
//    fun getTotalUnReadApproCount(): Int {
//        val allCompanies = getAllCompanies()
//        var totalCount = 0
//        allCompanies.forEach {
//            totalCount = totalCount + it.haveApprove
//        }
//        return totalCount
//    }
//
//    fun clearUnReadApproCount(companyId: String) {
//        val allOrg1 = getAllOrg()
//        val find = allOrg1.find { it.companyId == companyId }
//        if (find != null) {
//            find.haveApprove = 0
//            saveAllOrg(allOrg1)
//        } else {
//            val cooperationOrg = getCooperationOrg()
//            val find1 = cooperationOrg.find { it.companyId == companyId }
//            if (find1 != null) {
//                find1.haveApprove = 0
//                saveCooperationOrg(cooperationOrg)
//            }
//        }
//    }
//
//    fun upOneUnReadApproCount(companyId: String) {
//        val allOrg1 = getAllOrg()
//        val find = allOrg1.find { it.companyId == companyId }
//        if (find != null) {
//            find.haveApprove = find.haveApprove + 1
//            saveAllOrg(allOrg1)
//        } else {
//            val cooperationOrg = getCooperationOrg()
//            val find1 = cooperationOrg.find { it.companyId == companyId }
//            if (find1 != null) {
//                find1.haveApprove = find1.haveApprove + 1
//                saveCooperationOrg(cooperationOrg)
//            }
//        }
//    }
//
//    /**获取团队信息*/
//    fun getOrgInfo(companyId: String): UserInfo? {
//        return GsonUtil.fromJson(MMKVUtil.getString("O_${companyId}"), UserInfo::class.java)
//    }
//
//    /**保存团队信息*/
//    fun setOrgInfo(userInfo: UserInfo) {
//        MMKVUtil.saveString("O_${userInfo.userId}", GsonUtil.toJson(userInfo))
//        CacheDataHolder.setUserInfo(userInfo.userId, userInfo)
//    }
//
//    /**保存所有团队并更新主要团队信息，调用完需要刷新一下用主要团队的位置*/
//    fun saveAllOrg(allOrg: ArrayList<WorkStationBean>) {
//        this.allOrg = allOrg
//        MMKVUtil.saveString(COMPANIES, GsonUtil.toJson(allOrg))
//
//        fun updateCurrent() {
//            saveCurrentOrg(when {
//                StringUtils.isNotBlankAndEmpty(mainOrgId) -> {
//                    allOrg.find { it.companyId == mainOrgId }
//                }
//                allOrg.isNotEmpty() -> {
//                    allOrg[0]
//                }
//                else -> {
//                    null
//                }
//            })
//        }
//
//        fun saveOrgInfo() {
//            allOrg.forEach {
//                setOrgInfo(UserInfo(it.companyId, it.logo, it.name))
//            }
//        }
//
//        if (allOrg.isNotEmpty()) {
//            // 团队信息保存为userInfo，方便之后im列表或相关位置使用
//            saveOrgInfo()
//            val currentOrg = getCurrentOrg()
//            if (currentOrg != null) {
//                val result = allOrg.find { it.companyId == currentOrg.companyId }
//                if (result == null) {// 未找到当前团队信息，说明当前团队已不存在，需要重新设置当前团队
//                    updateCurrent()
//                }
//            } else {
//                updateCurrent()
//            }
//        } else {
//            saveCurrentOrg(null)
//            saveMainOrgId(null)
//            showLog("清空主要团队id $mainOrgId")
//            UserHolder.getCurrentUser()?.let {
//                it.companyId = ""
//                UserHolder.onLogin(it, true)
//            }
//        }
//
//        // 创建相关公司的通知id，分别创建审批和工作通知的
//        var workTagIdChange = 20001
//        var approvalTagIdChange = 30001
//        if (allOrg.isNotEmpty()) {//所有公司，不包含合作团队
//            allOrg.forEach {
//                NoticeUtil.saveWorkTagId(it.companyId, workTagIdChange)
//                workTagIdChange++
//                NoticeUtil.saveApprovalTagId(it.companyId, approvalTagIdChange)
//                approvalTagIdChange++
//            }
//        }
//    }
//
//    fun getCooperationOrg(): ArrayList<WorkStationBean> {
//        if (allCooperation.isNullOrEmpty()) {
//            allCooperation = GsonUtil.getList2(MMKVUtil.getString(COOPERATION_ORG_ALL),
//                    WorkStationBean::class.java) ?: arrayListOf()
//        }
//        return allCooperation
//    }
//
//    fun saveCooperationOrg(allCooperation: ArrayList<WorkStationBean>) {
//        this.allCooperation = allCooperation
//        MMKVUtil.saveString(COOPERATION_ORG_ALL, GsonUtil.toJson(allCooperation))
//        if (!allCooperation.isNullOrEmpty()) {
//            allCooperation.forEach {
//                setOrgInfo(UserInfo(it.companyId, it.logo, it.name))
//            }
//        }
//
//        // 创建相关公司的通知id，分别创建审批和工作通知的
//        var workTagIdChange = 22001
//        var approvalTagIdChange = 32001
//        if (allCooperation.isNotEmpty()) {//所有公司，不包含合作团队
//            allCooperation.forEach {
//                NoticeUtil.saveWorkTagId(it.companyId, workTagIdChange)
//                workTagIdChange++
//                NoticeUtil.saveApprovalTagId(it.companyId, approvalTagIdChange)
//                approvalTagIdChange++
//            }
//        }
//
//    }
//
//    fun saveMainOrgId(mainOrgId: String?) {
//        showLog("保存主要团队id oldId = ${this.mainOrgId}, newId = $mainOrgId")
//        this.mainOrgId = mainOrgId
//        UserHolder.getCurrentUser()?.companyId = mainOrgId ?: ""
//        if (UserHolder.getCurrentUser() != null) {
//            UserHolder.onLogin(UserHolder.getCurrentUser()!!, true)
//        }
//        EventBusUtils.sendEvent(EventBusEvent(ConsKeys.ORG_MAIN_CHANGE, mainOrgId))
//    }
//
//    fun getMainOrg(): WorkStationBean? {
//        showLog("获取主要团队 当前主要团队id = $mainOrgId")
//        if (mainOrgId.isNullOrBlank() || mainOrgId == "0") {
////            mainOrgId = MMKVUtil.getString(MAIN_COMPANY_ID)
//            mainOrgId = UserHolder.getCurrentUser()?.companyId ?: ""
//        }
//        showLog("获取主要团队 实际读取到主要团队id = $mainOrgId")
//        if (!mainOrgId.isNullOrBlank() && mainOrgId != "0" && !getAllOrg().isNullOrEmpty()) {
//            val result = allOrg.find { it.companyId == mainOrgId }
//            return result ?: getAllOrg()[0]
//        }
//        return null
//    }
//
//    /**
//     * 设置免打扰的团队id缓存
//     * app 启动从后台获取设置免打扰 团队 id数组
//     * 修改团队 免打扰后 设置本地缓存
//     *
//     * 个人的配置信息，退出登录时需要清理
//     */
//    private val noticeMsgDisturbSettingSet = hashSetOf<String>()
//
//    fun savePushSetting(setting: PushSettingData) {
//        if (UserHolder.isLogin()) {
//            MMKVUtil.saveString("push_setting_${UserHolder.getUserId()}", GsonUtil.toJson(setting))
//
//            noticeMsgDisturbSettingSet.clear()
//            noticeMsgDisturbSettingSet.addAll(setting.ids)
//            if (setting.systemPushSwitch == 1) {
//                noticeMsgDisturbSettingSet.add(ConstantTcpUtil.TCP_SYSTEM_PUSH_ID)
//            }
//            if (setting.approvalPushSwitch == 1) {
//                noticeMsgDisturbSettingSet.add(ConstantTcpUtil.TCP_APPROVAL_PUSH_ID)
//            }
//        }
//        EventBusUtils.sendEvent(EventBusEvent(MSG_NOTIFY_CHANGE, ""))
//    }
//
//    fun getPushSetting(): PushSettingData {
//        if (UserHolder.isLogin()) {
//            val json = MMKVUtil.getString("push_setting_${UserHolder.getUserId()}")
//            if (json.isNotBlank() && json.startsWith("{") && json.endsWith("}")) {
//                return GsonUtil.fromJson<PushSettingData>(json)!!
//            }
//        }
//        return PushSettingData()
//    }
//
//    //tcp通知消息免打扰
////检查系统消息是否打开免打扰，
//    fun checkSystemDisturb(): Boolean {//检查系统通知是否开启免打扰
//        val pushSettingSet = getPushSettingSet()
//        if (pushSettingSet.contains(ConstantTcpUtil.TCP_SYSTEM_PUSH_ID)) {
//            return true
//        } else {
//            return false
//        }
//    }
//
//    //检查审批消息是否打开免打扰
//    fun checkApprocvalDisturb(): Boolean {//检查审批通知是否开启免打扰
//        val pushSettingSet = getPushSettingSet()
//        if (pushSettingSet.contains(ConstantTcpUtil.TCP_APPROVAL_PUSH_ID)) {
//            return true
//        } else {
//            return false
//        }
//    }
//
//    //检查公司消息是否打开免打扰
//    fun checkWorkCompanyIdDisturb(companyId: String): Boolean {//检查工作通知是否开启免打扰
//        val pushSettingSet = getPushSettingSet()
//        if (pushSettingSet.contains(companyId)) {
//            return true
//        } else {
//            return false
//        }
//    }
//
//    fun getPushSettingSet(): HashSet<String> {
//        if (noticeMsgDisturbSettingSet.isNotEmpty()) {
//            return noticeMsgDisturbSettingSet
//        }
//        val pushSetting = getPushSetting()
//        noticeMsgDisturbSettingSet.addAll(pushSetting.ids)
//        if (pushSetting.systemPushSwitch == 1) {
//            noticeMsgDisturbSettingSet.add(ConstantTcpUtil.TCP_SYSTEM_PUSH_ID)
//        }
//        if (pushSetting.approvalPushSwitch == 1) {
//            noticeMsgDisturbSettingSet.add(ConstantTcpUtil.TCP_APPROVAL_PUSH_ID)
//        }
//        return noticeMsgDisturbSettingSet
//    }
//
////    private var unReadApr: HashMap<String, Int>? = null
////
////    fun getUnReadApr(): HashMap<String, Int> {
////        if (unReadApr == null) {
////            MMKVUtil.getString("push_setting_${UserHolder.getUserId()}", GsonUtil.toJson(setting))
////            unReadApr = hashMapOf()
////        }
////        return unReadApr!!
////    }
////
////    fun saveUnReadApr(targetId: String, num: Int) {
////        if (unReadApr!!.containsKey(targetId)) {
////            unReadApr!![targetId] = unReadApr!![targetId] + num
////        } else {
////            unReadApr!![targetId] = num
////        }
////    }
//
//    override fun init() {
//    }
//
//    override fun onLogout() {
//        clearCache(CURRENTCOMPANY)// 当前组织
//        clearCache(COOPERATION_ORG_ALL)// 所有协作组织
//        clearCache(COMPANIES)// 所有组织
//        allCooperation.clear()
//        allOrg.clear()
//        currentOrg = null
//    }
//}
//
///**
// * 当前用户信息
// * token缓存
// * 登录、登出缓存清理
// * 用户信息查询
// * */
//object UserHolder : CacheHolder {
//
//    private val lock = Object()
//
//    //    @Volatile
//    private var loginUser: PersonInfoBean? = null
//
//    override fun init() {
//        getCurrentUser()
//        getAccessToken()
//    }
//
//    /*-------------登录相关-------------*/
//
//    fun getCurrentUser(): PersonInfoBean? {
//        return synchronized(lock) {
//            if (loginUser != null) {
//                loginUser
//            } else {
//                val userInfo = MMKVUtil.getString(ConsKeys.USER_INFO)
//                if (userInfo.isNotBlank()) {
//                    loginUser = GsonUtil.fromJson(userInfo, PersonInfoBean::class.java)
//                    loginUser
//                } else {
//                    null
//                }
//            }
//        }
//    }
//
//    fun isLogin(): Boolean {
//        return synchronized(lock) {
//            showLog("判断用户是否登录 ${Thread.currentThread().id}-${System.currentTimeMillis()}")
//            loginUser != null
//        }
//    }
//
//    fun getUserId(): String? {
//        return loginUser?.userId
//    }
//
//    fun onLogin(user: PersonInfoBean, update: Boolean = false) {
//        CompanyHolder.showLog("更新用户信息 ${GsonUtil.toJson(user)} ,isUpdate = $update")
//        synchronized(lock) {
//            MMKVUtil.saveString(ConsKeys.USER_INFO, GsonUtil.toJson(user))
//            MMKVUtil.saveString(ConsKeys.PHONE, user.mobile)
//            saveUser(//缓存个人信息，nick为绑定微信名，解绑或重新绑定需要更新nick
//                    UserInfo(userId = user.userId, userIcon = user.avatar,
//                            userName = user.name, userNick = user.nickName))
//            loginUser = user
//            if (update) {
//                showLog("用户信息更新成功")
//            } else {
//                showLog("用户登录成功")
//            }
//            lock.notifyAll()
//        }
//    }
//
//    override fun onLogout() {
//        synchronized(lock) {
//            clearCache(ConsKeys.USER_INFO)// 用户信息
//            clearCache(ConsKeys.PUSHTOKEN)// 清空推送token
//            clearCache(ConsKeys.TOKEN_CACHE)// 清空token
//            loginUser = null
//            tokenBean = null
//        }
//    }
//    /*-------------登录相关-------------*/
//
//    /*-------------token相关-------------*/
//    private var tokenBean: TokenBean? = null
//
//    fun saveToken(tokenBean: TokenBean) {
//        showLog("存储token ${GsonUtil.toJson(tokenBean)}")
//        this.tokenBean = tokenBean
//        /**存储token获取时间，用于计算当前token有效期是否过期*/
//        tokenBean.updateTime = System.currentTimeMillis()
//        MMKVUtil.saveString(ConsKeys.TOKEN_CACHE, GsonUtil.toJson(tokenBean))
//    }
//
//    fun getAccessToken(): String {
//        return if (tokenBean != null) {
//            "Bearer ${tokenBean!!.access_token}"
//        } else {
//            val tokenCache = MMKVUtil.getString(ConsKeys.TOKEN_CACHE)
//            if (tokenCache.isNotBlank()) {
//                tokenBean = GsonUtil.fromJson(tokenCache, TokenBean::class.java)
//                if (tokenBean != null) {
//                    "Bearer ${tokenBean!!.access_token}"
//                } else {
//                    ""
//                }
//            } else {
//                ""
//            }
//        }
//    }
//
//    fun getRefreshToken(): String {
//        return if (tokenBean != null && !tokenBean!!.refresh_token.isNullOrBlank()) {
//            tokenBean!!.refresh_token!!
//        } else {
//            val tokenCache = MMKVUtil.getString(ConsKeys.TOKEN_CACHE)
//            if (tokenCache.isNotBlank()) {
//                tokenBean = GsonUtil.fromJson(tokenCache, TokenBean::class.java)
//                tokenBean?.refresh_token ?: ""
//            } else {
//                ""
//            }
//        }
//    }
//
//    /**token未过期检测*/
//    fun isTokenExpired(): Boolean {
//        return if (tokenBean != null) {
//            // 更新阈值 测试3分钟 正式提前三十分钟
//            val effectiveTime = if (isDebug) {
//                // oken过期测试使用，三分钟过期
//                300 * 60 * 1000
//            } else {
//                // token过期提前30分钟
//                (tokenBean!!.expires_in - 3 * 10 * 60) * 1000
//            }
//
//            // 自从上次更新后，使用的时间，大于等于有效期时需要更新，小于有效期，说明还没有过期
//            val sinceUpdate = System.currentTimeMillis() - tokenBean!!.updateTime
//            return sinceUpdate < effectiveTime
//        } else {
//            false
//        }
//    }
//
//    fun setCurrentPushToken(pushToken: String) {
//        val oldToken = MMKVUtil.getString(ConsKeys.PUSHTOKEN)
//        if (oldToken != pushToken) {
//            MMKVUtil.saveString(ConsKeys.PUSHTOKEN, pushToken)
//        }
//    }
//
//    fun getCurrentPushToken(): String = MMKVUtil.getString(ConsKeys.PUSHTOKEN)
///*-------------token相关-------------*/
//
//    /*-------------用户缓存相关-------------*/
//    fun saveUser(user: UserInfo) {
//        if (user.userId == getUserId()) { // 当前用户
//            MMKVUtil.saveString("C_${user.userId}", GsonUtil.toJson(user))
//        } else { // 其他用户
//            MMKVUtil.saveString("U_${user.userId}", GsonUtil.toJson(user))
//        }
//        CacheDataHolder.setUserInfo(user.userId, user)
//        EventBusUtils.sendEvent(EventBusEvent(ConsKeys.USER_INFO_UPDATE, user.userId))
//    }
//
//    fun saveOffLineNotReadCount(sessionId: String, notReadCount: Int) {
//        MMKVUtil.saveInt(getUserId() + sessionId + "notReadCount", notReadCount)
//    }
//
//    fun getOffLineNotReadCount(sessionId: String): Int {
//        return MMKVUtil.getInt(getUserId() + sessionId + "notReadCount")
//    }
//
//
//    fun saveSessionId(appChatId: String, sessionId: String?) {
//        MMKVUtil.saveString(getUserId() + appChatId + "sessionId", sessionId ?: "")
//    }
//
//    fun saveUserName(appChatId: String, userName: String) {
//        MMKVUtil.saveString(getUserId() + appChatId + "name", userName)
//    }
//
//    fun saveUserLogo(appChatId: String, userLogo: String) {
//        MMKVUtil.saveString(getUserId() + appChatId + "logo", userLogo)
//    }
//
//    fun getUserName(appChatId: String): String {
//        return MMKVUtil.getString(getUserId() + appChatId + "name")
//    }
//
//    fun getUserLogo(appChatId: String): String {
//        return MMKVUtil.getString(getUserId() + appChatId + "logo")
//    }
//
//    fun saveImTokenBean(imTokenBean: ImTokenBean) {
//        MMKVUtil.saveString("tcpImTokenBean" + getUserId(), GsonUtil.toJson(imTokenBean))
//    }
//
//    fun saveOpenTopIdList(idList: ArrayList<String>) {
//        MMKVUtil.saveString("tcpOpenTopList" + getUserId(), GsonUtil.toJson(idList))
//    }
//
//    fun saveHideSessionIdList(idList: ArrayList<String>) {
//        MMKVUtil.saveString("tcpHideSessionIdList" + getUserId(), GsonUtil.toJson(idList))
//    }
//
//    fun getHideSessionIdList(): ArrayList<String>? {
//        val hideSessionIdJson = MMKVUtil.getString("tcpHideSessionIdList" + getUserId())
//        if (!hideSessionIdJson.isNullOrBlank()) {
//            return Gson().fromJson<ArrayList<String>>(hideSessionIdJson, object : TypeToken<ArrayList<String>>() {}.type)//tcp----json转集合
//        }
//        return null
//    }
//
//    fun getOpenTopIdList(): ArrayList<String>? {
//        val openTopJson = MMKVUtil.getString("tcpOpenTopList" + getUserId())
//        if (!openTopJson.isNullOrBlank()) {
//            return Gson().fromJson<ArrayList<String>>(openTopJson, object : TypeToken<ArrayList<String>>() {}.type)//tcp----json转集合
//        }
//        return null
//    }
//
//    fun saveSingleDisturbIdList(idList: ArrayList<String>) {
//        MMKVUtil.saveString("tcpSingleDisturbList" + getUserId(), GsonUtil.toJson(idList))
//    }
//
//    fun getSingleDisturbIdList(): ArrayList<String>? {
//        val disturbSingleJson = MMKVUtil.getString("tcpSingleDisturbList" + getUserId())
//        if (!disturbSingleJson.isNullOrBlank()) {
//            return Gson().fromJson<ArrayList<String>>(disturbSingleJson, object : TypeToken<ArrayList<String>>() {}.type)
//        }
//        return null
//    }
//
//    fun saveGroupDisturbIdList(idList: ArrayList<String>) {
//        MMKVUtil.saveString("tcpGroupDisturbList" + getUserId(), GsonUtil.toJson(idList))
//    }
//
//    fun getGroupDisturbIdList(): ArrayList<String>? {
//        val disturbGroupJson = MMKVUtil.getString("tcpGroupDisturbList" + getUserId())
//        if (!disturbGroupJson.isNullOrBlank()) {
//            return Gson().fromJson<ArrayList<String>>(disturbGroupJson, object : TypeToken<ArrayList<String>>() {}.type)
//        }
//        return null
//    }
//
//    fun clearImTokenBean() {
//        MMKVUtil.saveString("tcpImTokenBean" + getUserId(), "")
//        val allFriendList = FriendDaoOpe.instance.getAllFriendList(BaseApplication.joinuTechContext)
//        if (allFriendList != null && allFriendList.isNotEmpty()) {
//            allFriendList.forEach {
//                UserHolder.saveSessionId(it.userId, "")
//            }
//        }
//    }
//
//    fun getImTokenBean(): ImTokenBean? {
//        val imTokenJson = MMKVUtil.getString("tcpImTokenBean" + getUserId())
//        if (!imTokenJson.isNullOrBlank()) {
//            return GsonUtil.fromJson(imTokenJson, ImTokenBean::class.java)
//        }
//        return null
//
//    }
//
//    fun getSessionIdByChatId(appChatId: String): String {
//        return MMKVUtil.getString(getUserId() + appChatId + "sessionId")
//    }
//
//    fun saveUuid(uuid: String) {
//        MMKVUtil.saveString("cmdId", uuid)
//    }
//
//    fun getUuid(): String {
//        return MMKVUtil.getString("cmdId")
//    }
//
//    fun getUser(userId: String): UserInfo? {
//        val info = if (userId == getUserId()) {
//            MMKVUtil.getString("C_$userId")
//        } else {
//            MMKVUtil.getString("U_$userId")
//        }
//        if (info.isNotBlank()) {
//            return GsonUtil.fromJson(info, UserInfo::class.java)
//        }
//        return null
//    }
///*-------------用户缓存相关-------------*/
//
//}
//
//object CacheHelper : CacheHolder {
//    override fun init() {
//    }
//
//    fun onCache(key: String, any: Any) {
//        MMKVUtil.saveString(key, GsonUtil.toJson(any))
//    }
//
//    fun getCache(key: String): java.util.ArrayList<AttendanceHolidayBean>? {
//        val json = MMKVUtil.getString(key)
//        if (!json.isNullOrBlank()) {
//            try {
//                return GsonUtil.getList2(json, AttendanceHolidayBean::class.java)
//            } catch (e: Exception) {
//            }
//        }
//        return arrayListOf()
//    }
//
//    override fun onLogout() {
//    }
//
//}
//
//interface CacheHolder {
//    fun clearCache(key: String) {
//        if (MMKVUtil.containKey(key)) {
//            MMKVUtil.clearByKey(key)
//        }
//    }
//
//    fun showLog(msg: String) {
//        LogUtil.showLog(msg)
//    }
//
//    /**初始化缓存*/
//    fun init()
//
//    /**用户退出登录，清理之前用户数据*/
//    fun onLogout()
//
//}