package com.joinutech.common.helper

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import androidx.core.content.FileProvider
import com.joinutech.ddbeslibrary.base.MiniProgramBean
import com.joinutech.ddbeslibrary.utils.APP_ID_WX
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.JavaUtils.base64ToByteArray
import com.joinutech.ddbeslibrary.utils.JavaUtils.bmpToByteArray
import com.joinutech.ddbeslibrary.utils.Loggerr
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.WxBitmapUtil.buildTransaction
import com.marktoo.lib.cachedweb.LogUtil
import com.tencent.mm.opensdk.constants.Build
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelmsg.*
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import java.io.File


/**微信分享工具类*/
class WechatHelper {
    //WX
    private lateinit var api: IWXAPI
    private lateinit var context: Context

    private fun showLog(msg: String) {
        LogUtil.showLog(msg, "wx___")
    }

    fun initApi(context: Context, onResult: () -> Unit) {
        showLog("初始化微信sdk")
        this.context = context
        api = WXAPIFactory.createWXAPI(context, APP_ID_WX, true)
//        if (api.isWXAppInstalled()) {
        if (CommonUtils.isHaveWeChat(context)) {
            showLog("注册到微信")
            api.registerApp(APP_ID_WX)
            context.registerReceiver(object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    showLog("微信广播监听完成，开始注册App到微信 ${intent?.action}")
                    // 将该app注册到微信
//                onResult()
                }
            }, IntentFilter(ConstantsAPI.ACTION_HANDLE_APP_REGISTER))
//            }, IntentFilter(ConstantsAPI.ACTION_REFRESH_WXAPP))
//        // 将该app注册到微信
//        api.registerApp(APP_ID_WX)
            showLog("注册微信后，回调后续操作")
            onResult()
        } else {
            showLog("请先安装微信")
            ToastUtil.show(context, "请先安装微信")
        }
    }

    // 判断微信版本是否为7.0.13及以上
    private fun checkVersionValid(context: Context): Boolean {
        return api.wxAppSupportAPI >= 0x27000D00;
    }

    fun shareFileToWx(file: File, fileIcon: Int, scene: Int = 0) {
        val filePath =
            if (checkVersionValid(context) && android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                //转化为uri
                val uri =
                    FileProvider.getUriForFile(context, context.packageName + ".fileProvider", file)
                context.grantUriPermission(
                    "com.tencent.mm",
                    uri,
                    Intent.FLAG_GRANT_READ_URI_PERMISSION
                )
                uri.toString()
            } else {
                file.absolutePath
            }

        try {
            val fileObj = WXFileObject(filePath)
            val msg = WXMediaMessage()
            msg.mediaObject = fileObj
            msg.title = file.name
            msg.description = file.name
            val iconBitmap = BitmapFactory.decodeResource(context.resources, fileIcon)
            msg.thumbData = bmpToByteArray(iconBitmap, true)

            val req = SendMessageToWX.Req()
            //对应该请求的事务 ID，通常由 Req 发起
            req.transaction = buildTransaction("file")
            req.message = msg
            req.scene = scene
//调用api接口，发送数据到微信
            api.sendReq(req)
        } catch (e: Exception) {
        }
    }

    /**
     * @param content 长度需大于 0 且不超过 10KB
     * @param scene WXSceneSession WXSceneTimeline WXSceneFavorite
     * */
    fun sendTextToWx(content: String, desc: String, scene: Int = 0) {
        showLog("分享文字到微信 $content")
        //初始化一个 WXTextObject 对象，填写分享的文本内容
        val textObj = WXTextObject()
        textObj.text = content

//用 WXTextObject 对象初始化一个 WXMediaMessage 对象

        val msg = WXMediaMessage()
//        msg.title = ""// 限制长度不超过 512Bytes
//        msg.thumbData = byte[]// 缩略图的二进制数据,限制内容大小不超过 32KB
        msg.mediaObject = textObj
        msg.description = desc // 限制长度不超过 1KB

        val req = SendMessageToWX.Req()
        //对应该请求的事务 ID，通常由 Req 发起
        req.transaction = buildTransaction("text")
        req.message = msg
        req.scene = scene
//调用api接口，发送数据到微信
        api.sendReq(req)
    }

    /**
     * @param icon  分享item的图片
     * @param scene 0表示为分享到微信好友  1表示为分享到朋友圈 3 收藏
     */
    fun sendImgToWx(icon: Bitmap, scene: Int = 0) {
        showLog("分享图片到微信")
        if (scene == 0) {
            //初始化 WXImageObject 和 WXMediaMessage 对象
            val imgObj = WXImageObject(icon)
            val msg = WXMediaMessage()
            msg.mediaObject = imgObj
            val thumbBmp = Bitmap.createScaledBitmap(icon, 150, 150, true)
            msg.thumbData = bmpToByteArray(thumbBmp, true)
            icon.recycle()
            //构建一个Req
            val req = SendMessageToWX.Req()
            req.transaction = buildTransaction("img")
            req.message = msg
            req.scene = scene
            api.sendReq(req)
        } else if (scene == 1 && api.wxAppSupportAPI >= Build.TIMELINE_SUPPORTED_SDK_INT) {
            //初始化 WXImageObject 和 WXMediaMessage 对象
            val imgObj = WXImageObject(icon)
            val msg = WXMediaMessage()
            msg.mediaObject = imgObj
            val thumbBmp = Bitmap.createScaledBitmap(icon, 150, 150, true)
            msg.thumbData = bmpToByteArray(thumbBmp, true)
            icon.recycle()
            //构建一个Req
            val req = SendMessageToWX.Req()
            req.transaction = buildTransaction("img")
            req.message = msg
            req.scene = scene
            api.sendReq(req)
        } else if (scene == 3 && api.wxAppSupportAPI >= Build.FAVORITE_SUPPPORTED_SDK_INT) {
            //初始化 WXImageObject 和 WXMediaMessage 对象
            val imgObj = WXImageObject(icon)
            val msg = WXMediaMessage()
            msg.mediaObject = imgObj
            val thumbBmp = Bitmap.createScaledBitmap(icon, 150, 150, true)
            msg.thumbData = bmpToByteArray(thumbBmp, true)
            icon.recycle()
            //构建一个Req
            val req = SendMessageToWX.Req()
            req.transaction = buildTransaction("img")
            req.message = msg
            req.scene = scene
            api.sendReq(req)
        }
//        toastShort("发送微信分享成功")
    }

    //分享小程序到微信
    fun sendMiniProgramToWx(miniBean: MiniProgramBean) {

        //==============使用以下的代码======
        val miniProgramObj = WXMiniProgramObject()
        miniProgramObj.webpageUrl = miniBean.webpageUrl// 兼容低版本的网页链接

        miniProgramObj.miniprogramType = miniBean.miniProgramType// 正式版:0，测试版:1，体验版:2

        miniProgramObj.userName = miniBean.userName // 小程序原始id

        miniProgramObj.path = miniBean.programPath //小程序页面路径；对于小游戏，可以只传入 query 部分，来实现传参效果，如：传入 "?foo=bar"

        val msg = WXMediaMessage(miniProgramObj)
        msg.title = miniBean.title // 小程序消息title

        msg.description = miniBean.description // 小程序消息desc

        msg.thumbData = base64ToByteArray(miniBean.hdImageData) // 小程序消息封面图片，小于128k


        val req = SendMessageToWX.Req()
        req.transaction = buildTransaction("miniProgram")
        req.message = msg
        req.scene = SendMessageToWX.Req.WXSceneSession // 目前只支持会话

        api.sendReq(req)
    }


    /**
     * @param url 限制长度不超过 10KB
     * @param scene 0 session 1 cycle 2 favorite
     * */
    fun sendLinkToWx(icon: ByteArray, title: String, desc: String, url: String, scene: Int = 0) {
        showLog("分享链接到微信")
        //初始化一个WXWebpageObject，填写url
        val webpage = WXWebpageObject()
        webpage.webpageUrl = url // 限制长度不超过 10KB

        //用 WXWebpageObject 对象初始化一个 WXMediaMessage 对象
        val msg = WXMediaMessage(webpage)
        msg.title = title
        msg.description = desc
//        if (icon > 0) {
//            val thumbBmp = BitmapFactory.decodeResource(context.resources, icon)
//            msg.thumbData = bmpToByteArray(thumbBmp, true)
//        }
        msg.thumbData = icon
        //构造一个Req
        val req = SendMessageToWX.Req()
        req.transaction = buildTransaction("webpage")
        req.message = msg
        req.scene = scene

//调用api接口，发送数据到微信
        api.sendReq(req)
    }

    fun initApiWithLogin(context: Context, onResult: () -> Unit, onError: (Int) -> Unit) {
        if (CommonUtils.isHaveWeChat(context)) {
            showLog("初始化微信sdk")
            api = WXAPIFactory.createWXAPI(context, APP_ID_WX, true)
            showLog("注册到微信")
            api.registerApp(APP_ID_WX)
            context.registerReceiver(object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    Loggerr.i("微信登录", "===广播中注册到微信=成功，，当前线程=${Thread.currentThread().name}==")
                    api.registerApp(APP_ID_WX)
                }
            }, IntentFilter(ConstantsAPI.ACTION_REFRESH_WXAPP))
//            }, IntentFilter(ConstantsAPI.ACTION_REFRESH_WXAPP))
            showLog("注册微信后，回调后续操作")
            onResult()
        } else {
            ToastUtil.show(context, "请先安装微信")
            onError.invoke(1)
        }
    }

    fun tryLogin() {
        val req = SendAuth.Req()
        req.scope = "snsapi_userinfo"
        req.state = "wechat_sdk_微信登录"
        api.sendReq(req)
    }
}
