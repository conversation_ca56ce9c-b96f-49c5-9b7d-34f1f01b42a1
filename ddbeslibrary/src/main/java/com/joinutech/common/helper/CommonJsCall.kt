package com.joinutech.common.helper

import android.webkit.JavascriptInterface
import androidx.appcompat.app.AppCompatActivity
import com.joinutech.common.storage.FileStorage
import com.luck.picture.lib.config.PictureConfig

/**
 * @PackageName: com.joinutech.common.helper
 * @ClassName:
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/8/21 9:20
 * @Desc: //TODO js 交互对象 公共
 */
class CommonJsCall(private val activity: AppCompatActivity,
                   private val listener: JsInvokeImpl,
                   private val tag: String = "云文档页面") {

    @JavascriptInterface
    fun client_goBack() {
        FileStorage.showLog("$tag---  点击返回键")
        activity.runOnUiThread {
            listener.onWebGoBack()
        }
    }

    @JavascriptInterface
    fun isAlready() {
        FileStorage.showLog("$tag---  加载完毕回调")
        activity.runOnUiThread {
            listener.onWebInitFinish()
        }
    }

    @JavascriptInterface
    fun changeCompany() {
        FileStorage.showLog("$tag---  接收公司切换后信息")
        activity.runOnUiThread {
            listener.onChangeCompany()
        }
    }

    @JavascriptInterface
    fun getToken() {
        FileStorage.showLog("$tag---  token过期后主动请求token")
        activity.runOnUiThread {
            listener.webGetToken()
        }
    }

    @JavascriptInterface
    fun pan_CopyLink(data: String) {
        FileStorage.showLog("$tag---  复制内容到剪贴板 $data")
        activity.runOnUiThread {
            listener.onCopy(data)
        }
    }

    @JavascriptInterface
    fun pan_SetStorage(data: String) {
        FileStorage.showLog("$tag---  存储内容到移动端 $data")
        activity.runOnUiThread {
            listener.onSaveData(data)
        }
    }

    @JavascriptInterface
    fun pan_GetStorage() {
        FileStorage.showLog("$tag---  获取存储到移动端的内容")
        activity.runOnUiThread {
            listener.onGetData()
        }
    }

    @JavascriptInterface
    fun client_take_photo(parentId: String) {
        FileStorage.showLog("$tag---  拍照 $parentId")
        activity.runOnUiThread {
            listener.takePhoto(parentId)
        }
    }

    @JavascriptInterface
    fun client_take_video(parentId: String) {
        FileStorage.showLog("$tag---  视频选择 $parentId")
        activity.runOnUiThread {
            listener.selectPic(parentId, PictureConfig.TYPE_VIDEO)
        }
    }

    @JavascriptInterface
    fun client_take_picture(parentId: String) {
        FileStorage.showLog("$tag---  选择图片 $parentId")
        activity.runOnUiThread {
            listener.selectPic(parentId, PictureConfig.TYPE_IMAGE)
        }
    }

    /**
     * 分享文件给好友
     * fileType:0带后缀1文件夹2多图
     *
     * {
     * "fileName":"PictureSelector_20200804_182917.JPEG.JPEG",
     * "fileType":"0",
     * "textarea":"Julia云资产的赵阳阳，分享给您一个文件【PictureSelector_20200804_182917.JPEG.JPEG】",
     * "link":"https://pan.ddbes.com/pan.html#/share/2Ylt0JZi33n",
     * "password":"6511",
     * "ids":["2488484430818051069"]
     * }
     * */
    @JavascriptInterface
    fun pan_shareFileToFriend(json: String) {
        FileStorage.showLog("$tag---  分享文件给好友--$json")
        activity.runOnUiThread {
            listener.shareToFriend(json)
        }
    }

    /**
     * 分享文件到微信
     * {
     * "fileName":"1596000353179113.jpg",
     * "fileType":0,
     * "textarea":"云服务团队的金刚，分享给您一个文件【1596000353179113.jpg】",
     * "link":"https://pan.ddbes.com/pan.html#/share/2YlVwoQG8Xz",
     * "password":"8878",
     * "ids":[]
     * }*/
    @JavascriptInterface
    fun pan_shareFileToWX(json: String) {
        FileStorage.showLog("$tag---  分享文件到微信--$json")
        activity.runOnUiThread {
            listener.shareToWx(json)
        }
    }

    /**分享文件给同事*/
    @JavascriptInterface
    fun pan_shareFileToOrgMember(json: String) {
        FileStorage.showLog("$tag---  分享文件给同事--$json")
        activity.runOnUiThread {
            listener.shareToOrgMember(json)
        }
    }

    /**保存图片到相册
     * url 下载地址
     * name 文件名
     * {"url":"https://ddbes-pan-test-1257239906.cos.ap-beijing.myqcloud.com/2495532203715331069?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLRJwTlAa8QUIqCi8j4C4g5HDuLWVnjy1%26q-sign-time%3D1596771180%3B1596772080%26q-key-time%3D1596771180%3B1596772080%26q-header-list%3D%26q-url-param-list%3Dresponse-cache-control%3Bresponse-content-disposition%3Bresponse-content-language%3Bresponse-expires%26q-signature%3Da12a6eddecc7841fc2d8c2a31c1c674920391242&response-cache-control=no-cache&response-content-disposition=attachment%3B%20filename%3D%221596000352931419.jpg%22&response-content-language=zh-CN&response-expires=Sat%2C%2008%20Aug%202020%2003%3A33%3A00%20GMT",
     * "name":"1596000352931419.jpg","
     * id":"2495532204789072893"
     * }
     * {"url":"https://ddbes-pan-test-1257239906.cos.ap-beijing.myqcloud.com/2495502770304451581?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLRJwTlAa8QUIqCi8j4C4g5HDuLWVnjy1%26q-sign-time%3D1597221422%3B1597222322%26q-key-time%3D1597221422%3B1597222322%26q-header-list%3D%26q-url-param-list%3Dresponse-cache-control%3Bresponse-content-disposition%3Bresponse-content-language%3Bresponse-expires%26q-signature%3D6600d3c9c0a0f9bbf56b73660f04581aaa526f7c&response-cache-control=no-cache&response-content-disposition=attachment%3B%20filename%3D%22EVA%25E5%25A4%25A7%25E5%25B1%2595%25E4%25B8%25AD%25E5%259B%25BD%25E9%25A3%258E.jpg%22&response-content-language=zh-CN&response-expires=Thu%2C%2013%20Aug%202020%2008%3A37%3A02%20GMT","name":"EVA大展中国风.jpg","id":"2495503539103597565"}
     * */
    @JavascriptInterface
    fun client_saveToGallery(json: String) {
        FileStorage.showLog("$tag---  保存图片到相册 $json")
        activity.runOnUiThread {
            listener.saveToGallery(json)
        }
    }

    /**
     * 预览文件 type 1 图片 2 文件
     *          data url
     *          name 文件名
     */
    @JavascriptInterface
    fun client_previewFile(json: String) {
        FileStorage.showLog("$tag---  预览文件 $json")
        activity.runOnUiThread {
            listener.previewFile(json)
        }
    }

    /**json{\"link\":\"文件访问地址\", \"ext\": \"文件后缀\"，\"name\":\"文件名\"}*/
    @JavascriptInterface
    fun client_previewFileOnLine(json: String) {
        activity.runOnUiThread {
            listener.previewFileOnLine(json)
        }
    }

//    /**info{\"link\":\"文件访问地址\", \"ext\": \"文件后缀\"}*/
//    @JavascriptInterface
//    fun getPreviewFileInfo() {
//        FileStorage.showLog("web 调用 获取预览文件信息方法 ----")
//        activity.runOnUiThread {
//            listener.onGetPreviewFileInfo()
//        }
//    }

}

open class JsInvokeImpl : JsCommonListener, JsBaseListener, JsPreviewListener, JsDataListener, JsShareListener {

    override fun onWebGoBack() {
        FileStorage.showLog("点击返回键")
    }

    override fun onBackPress() {

    }

    override fun onWebInitFinish() {
        FileStorage.showLog("网页初始化完成")
    }

    override fun webSaveUserInfo() {

    }

    override fun webGetToken() {

    }

    override fun webSaveToken() {

    }

    override fun webSaveCurrentCompany() {

    }

    override fun onChangeCompany() {

    }

    override fun previewFile(data: String) {

    }

    override fun previewFileOnLine(data: String) {

    }

    override fun onCopy(data: String) {

    }

    override fun onSaveData(data: String) {

    }

    override fun onGetData() {

    }

    override fun webGetData(data: String) {

    }

    override fun selectPic(parentId: String, type: Int) {

    }

    override fun takePhoto(parentId: String) {

    }

    override fun saveToGallery(json: String) {

    }

    override fun shareToFriend(msg: String) {

    }

    override fun shareToWx(msg: String) {

    }

    override fun shareToOrgMember(msg: String) {

    }

}

interface JsCommonListener {

    /**web不再处理返回事件时调用*/
    fun onWebGoBack()

    /**
     * 系统返回键按下时，调用web，触发页面回退
     * */
    fun onBackPress()

}

interface JsBaseListener {

    /**web内容初始化完成*/
    fun onWebInitFinish()

    /**web获取用户信息
     *
     * js method is :pan_getUserBaseInfo
     * */
    fun webSaveUserInfo()

    /**web获取用户信息
     *
     * js method is :ddbes_pan.pan_h5SaveToken(token:String)
     * */
    fun webGetToken()

    /**
     * web 接收token
     * js method is :ddbes_pan.pan_h5SaveToken(token:String)*/
    fun webSaveToken()

    /**
     * web获取当前公司信息
     * 页面初始化完和切换公司后要通过这个方法返回给web json
     *
     * js method is :pan_getCurrentCompanyInfo
     * */
    fun webSaveCurrentCompany()

    /**web触发公司切换，弹出侧边栏*/
    fun onChangeCompany()

}

interface JsShareListener {

    /**分享文件给好友*/
    fun shareToFriend(msg: String)

    /**分享文件到微信*/
    fun shareToWx(msg: String)

    /**分享文件给同事*/
    fun shareToOrgMember(msg: String)
}

interface JsDataListener {

    /**web触发 复制功能，增加内容到剪贴板*/
    fun onCopy(data: String)

    /**web触发 存储数据到缓存*/
    fun onSaveData(data: String)

    /**web触发 获取设置的缓存信息*/
    fun onGetData()

    /**
     * 获取web调用原生缓存的内容，传输给web
     * js method is :pan_getStorage
     */
    fun webGetData(data: String)

    /**选择照片 @param type 1 图片 2 视频*/
    fun selectPic(parentId: String, type: Int)

    /**拍照*/
    fun takePhoto(parentId: String)

    /**保存图片到相册
     * {"url":"",
     * "name":"1596000352931419.jpg","
     * id":"2495532204789072893"
     * }
     * */
    fun saveToGallery(json: String)
}

interface JsPreviewListener {
    /**预览文件 1 图片 2 文件*/
    fun previewFile(data: String)

    /**在线预览文件*/
    fun previewFileOnLine(data: String)
//
//    /**ddbes_common.getPreviewFileInfo*/
//    fun onGetPreviewFileInfo()
}