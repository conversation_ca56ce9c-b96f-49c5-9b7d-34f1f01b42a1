package com.joinutech.common.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.viewpager.widget.ViewPager

/**
 * @className: NoScrollPager
 * @desc: 不可滑动ViewPager
 * @author: zyy
 * @date: 2019/8/6 10:02
 * @company: joinUTech
 * @leader: ke
 */
class NoScrollPager(context: Context, attrs: AttributeSet) : ViewPager(context, attrs) {

    var noScroll = true

    override fun onTouchEvent(ev: MotionEvent?): <PERSON><PERSON>an {
        return if (noScroll) {
            false
        } else {
            super.onTouchEvent(ev)
        }
    }

    override fun onInterceptTouchEvent(ev: MotionEvent?): <PERSON><PERSON><PERSON> {
        return if (noScroll) {
            false
        } else {
            super.onInterceptTouchEvent(ev)
        }
    }
}