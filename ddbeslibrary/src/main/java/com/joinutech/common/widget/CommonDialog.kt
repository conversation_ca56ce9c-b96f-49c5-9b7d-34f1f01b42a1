package com.joinutech.common.widget

import android.app.Dialog
import android.content.Context
import android.graphics.Typeface
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.joinutech.ddbeslibrary.R

class CommonDialog(private val context: Context) {
    var tvTitle: TextView? = null
    var tvContent: TextView? = null

    var tvLeft: TextView? = null
    var tvRight: TextView? = null

    var llBottomAction: LinearLayout? = null
    var ivClose: ImageView? = null

    private var dialog: Dialog? = null


    constructor(context: Context, bigImage: Boolean) : this(context) {
        dialog = Dialog(context, R.style.my_dialog)
        val view = View.inflate(
            context,
            R.layout.dialog_common ,
            null
        )
        tvTitle = view.findViewById(R.id.title1_common)
        tvContent = view.findViewById(R.id.desc_common)
        tvLeft = view.findViewById(R.id.cancle_common)
        tvRight = view.findViewById(R.id.confirm_common)

        dialog!!.setContentView(view)
    }

    fun showMatchParentWidth(): CommonDialog {
        val window = dialog!!.window
        dialog!!.window!!.setLayout(-1, -2)
        dialog!!.show()
        return this
    }

    fun show(): CommonDialog {
        val displayMetrics = context.resources.displayMetrics
        val widthPixel = (displayMetrics.widthPixels * 0.75).toInt()
        val window = dialog?.window
        window?.setLayout(widthPixel, -2)
        dialog?.show()
        return this
    }

    val isShowing: Boolean
        get() = dialog!!.isShowing

    fun setContent(content: String?): CommonDialog {
        tvContent?.apply {
            visibility = View.VISIBLE
            text = content
        }
        return this
    }



    fun setIvCloseVisible(): CommonDialog {
        ivClose?.visibility = View.VISIBLE
        return this
    }



    fun setContentGravity(dstGravity: Int): CommonDialog {
        tvContent?.gravity = dstGravity
        return this
    }

    fun setContent(content: CharSequence?): CommonDialog {
        tvContent?.apply {
            visibility = View.VISIBLE
            text = content
        }
        return this
    }



    fun setTitle(title: String?): CommonDialog {
        tvTitle?.apply {
            if (!TextUtils.isEmpty(title)) {
                visibility = View.VISIBLE
                text = title
            } else {
                visibility = View.GONE
            }
        }
        return this
    }

    fun setTitleSize(titleSize: Float): CommonDialog {
        tvTitle?.textSize = titleSize
        return this
    }

    fun setTitleBold(): CommonDialog {
        tvTitle?.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        return this
    }

    fun setTextViewBold(tv: TextView?): CommonDialog {
        tv?.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        return this
    }

    fun setCancelable(isBackKeyAvaiable: Boolean): CommonDialog {
        dialog?.setCancelable(isBackKeyAvaiable)
        return this
    }



    fun setLeftButtonListener(listener: View.OnClickListener?): CommonDialog {
        if (tvLeft == null) {
            throw NullPointerException("should call createDialog before")
        }
        if (listener != null) {
            tvLeft?.setOnClickListener(listener)
        }
        return this
    }

    fun setRightButtonListener(listener: View.OnClickListener?): CommonDialog {
        if (tvRight == null) {
            throw NullPointerException("should call createDialog before")
        }
        if (listener != null) {
            tvRight?.setOnClickListener(listener)
        }
        return this
    }







    private fun setBottomIconCloseListener(listener: View.OnClickListener?): CommonDialog {
        if (ivClose == null) {
            throw NullPointerException("should call createDialog before")
        }
        if (listener != null) {
            ivClose?.setOnClickListener(listener)
        }
        return this
    }

    fun setLeftTextAndListener(
        text: String?,
        listener: View.OnClickListener?
    ): CommonDialog {
        setLeftButtonListener(listener)
        if (!TextUtils.isEmpty(text)) tvLeft!!.text = text
        return this
    }

    fun setRightTextAndListener(
        text: String?,
        listener: View.OnClickListener?
    ): CommonDialog {
        setRightButtonListener(listener)
        if (!TextUtils.isEmpty(text)) tvRight!!.text = text
        return this
    }



    fun setBottomIvCloseListener(
        listener: View.OnClickListener?
    ): CommonDialog {
        setBottomIconCloseListener(listener)
        return this
    }


    fun setTitleTvVisible(visible: Boolean): CommonDialog {
        tvTitle!!.visibility = if (visible) View.VISIBLE else View.GONE
        return this
    }

    fun setRightTextGone(): CommonDialog {
        tvRight!!.visibility = View.GONE
        return this
    }

    fun setLeftTextGone(): CommonDialog {
        tvLeft!!.visibility = View.GONE
        return this
    }

    fun setContentTextClor(resId: Int): CommonDialog {
        tvContent!!.setTextColor(context.resources.getColor(resId))
        return this
    }



    fun setContentTextSize(fontSize: Int): CommonDialog {
        tvContent!!.textSize = fontSize.toFloat()
        return this
    }


    fun setLeftTextBackground(resId: Int): CommonDialog {
        tvLeft!!.background = context.resources.getDrawable(resId)
        return this
    }

    fun setLeftTextClor(resId: Int): CommonDialog {
        tvLeft!!.setTextColor(context.resources.getColor(resId))
        return this
    }

    fun setRightTextClor(resId: Int): CommonDialog {
        tvRight!!.setTextColor(context.resources.getColor(resId))
        return this
    }

    fun setRightTextBackground(resId: Int): CommonDialog {
        tvRight!!.background = context.resources.getDrawable(resId)
        return this
    }

    fun setOnDialogListener(listener: onDialogListening?): CommonDialog {
        if (listener != null) {
            tvLeft!!.setOnClickListener { listener.onLeftButton() }
            tvRight!!.setOnClickListener { listener.onRightButton() }
        }
        return this
    }

    fun dismiss(): CommonDialog {
        if (dialog != null && dialog!!.isShowing) {
            // 当activity已关闭时调用dismiss会提示 not attached to window manager
            // 可以使用[dialog.setOwnActivity()] 初始化
            // 在dismiss前判断，dialog.getOwnActivity != null && it.isNotFinish
            try {
                dialog!!.dismiss()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return this
    }

    interface onDialogListening {
        fun onLeftButton()
        fun onRightButton()
//        fun onCenterButton()
//        fun onIvClose()
    }

}