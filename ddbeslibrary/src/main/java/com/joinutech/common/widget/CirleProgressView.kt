package com.joinutech.common.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.utils.DeviceUtil


/**
 * @PackageName: com.joinutech.common.widget
 * @ClassName:
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/7/30 13:10
 * @Desc: //TODO 圆形进度条控件
 */
class CircleProgressView @JvmOverloads constructor(context: Context,
                                                   attributeSet: AttributeSet? = null,
                                                   defAttrStyle: Int = 0) : View(context, attributeSet, defAttrStyle) {
    private var bgColor: Int = resources.getColor(R.color.line_grey)
    private var lineColor: Int = resources.getColor(R.color.main_blue)
    private var lineWidth = 2f
    var startAngle: Int = -90
    var process: Int = 0
    /**
     * 0 无状态
     * 1 上传
     * 2 下载
     * 3 进行中
     * 4 暂停
     * */
    /**状态
     * -4 空间不足
     * -3 unknown
     * -2 failed
     * -1 cancel
     * 0 waiting
     * 1 process
     * 2 pause
     * 3 completed
     * */
    private var state: Int = 2

    /**进度值，最大100*/
    private var progress: Int = 60

    init {
        val ta = context.obtainStyledAttributes(attributeSet, R.styleable.CircleProgressView, defAttrStyle, 0)
        bgColor = ta.getColor(R.styleable.CircleProgressView_cpv_bg_color, bgColor)
        lineColor = ta.getColor(R.styleable.CircleProgressView_cpv_line_color, lineColor)
        lineWidth = ta.getDimension(R.styleable.CircleProgressView_cpv_line_width, lineWidth)
        startAngle = ta.getInteger(R.styleable.CircleProgressView_cpv_start_angle, startAngle)
        state = ta.getInteger(R.styleable.CircleProgressView_cpv_state, startAngle)
        process = ta.getInteger(R.styleable.CircleProgressView_cpv_process, startAngle)
        ta.recycle()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
    }

    @SuppressLint("ResourceAsColor")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        when (state) {
            0 -> {
                // 绘制进度条
                val radius = width / 2f
                val lineWidthf = DeviceUtil.dip2pxf(context, lineWidth)
                val padding = lineWidthf * 2 / 3
                drawBg(canvas, radius, lineWidthf, padding)
                drawFront(canvas, lineWidthf, padding)
            }
            1 -> {
                // 绘制进度条，绘制暂停图标
                val radius = width / 2f
                val lineWidthf = DeviceUtil.dip2pxf(context, lineWidth)
                val padding = lineWidthf * 2 / 3
                drawBg(canvas, radius, lineWidthf, padding)
                drawFront(canvas, lineWidthf, padding)
                drawPause(canvas, lineWidthf)
            }
            else -> {
                // 不绘制任何信息
            }
        }

//            if (progress == 100) {
//                val paint = Paint(Paint.ANTI_ALIAS_FLAG)
//                paint.color = resources.getColor(R.color.main_blue)
//                canvas?.drawText("finish", width / 2f, height / 2f, paint)
//            }
    }

    private fun drawPause(canvas: Canvas?, lineWidth: Float) {
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.style = Paint.Style.FILL
        paint.color = bgColor
        paint.strokeCap = Paint.Cap.ROUND
        paint.strokeWidth = DeviceUtil.dip2pxf(context, 2f)

        val firstLineX = width / 2 - lineWidth
        val secondLineX = width / 2 + lineWidth
        val startY = height / 2f - height / 8f
        val endY = height / 2f + height / 8f

        canvas?.drawLine(firstLineX, startY, firstLineX, endY, paint)
        canvas?.drawLine(secondLineX, startY, secondLineX, endY, paint)
    }

    private fun drawBg(canvas: Canvas?, radius: Float, circleWidth: Float, padding: Float) {
        val centerX = width / 2f
        val centerY = height / 2f
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.style = Paint.Style.STROKE
        paint.color = bgColor
        paint.strokeWidth = circleWidth
        canvas?.drawCircle(centerX, centerY, radius - padding, paint)
    }

    private fun drawFront(canvas: Canvas?, circleWidth: Float, padding: Float) {
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.style = Paint.Style.STROKE
        paint.color = lineColor
        paint.strokeCap = Paint.Cap.ROUND
        paint.strokeWidth = circleWidth
        val rectf = RectF(padding, padding, width - padding, height - padding)
//        val tempPaint = Paint()
//        tempPaint.style = Paint.Style.STROKE
//        tempPaint.strokeWidth = DeviceUtil.dip2pxf(context, 2f)
//        tempPaint.color = resources.getColor(R.color.red)
//        canvas?.drawRect(rectf, tempPaint)
        val sweepAngle = progress * 3.6f
        canvas?.drawArc(rectf, startAngle.toFloat(), sweepAngle, false, paint)
    }

    fun updateState(state: Int) {
        this.state = state
        invalidate()
    }

    fun updateProgress(progress: Int) {
        this.progress = progress
        invalidate()
    }
}