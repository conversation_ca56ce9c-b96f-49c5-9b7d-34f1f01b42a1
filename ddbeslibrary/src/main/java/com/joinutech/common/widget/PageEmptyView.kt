package com.joinutech.common.widget

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.joinutech.ddbeslibrary.R

/**
 * 默认空白页面组件
 */
class PageEmptyView : LinearLayout {

    private var _default_content: String = "暂无数据"
    private var _default_desc: String = ""
    private var _default_positive: String = ""
    private var _default_negative: String = ""

    private var _default_content_color = Color.parseColor("#ff333333")
    private var _default_desc_color = Color.parseColor("#999999")
    private var _default_icon: Int = R.drawable.ic_empty_friend

    constructor(context: Context) : super(context) {
        init(null, 0)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        init(attrs, 0)
    }

    constructor(context: Context, attrs: AttributeSet, defStyle: Int) : super(context, attrs, defStyle) {
        init(attrs, defStyle)
    }

    private fun init(attrs: AttributeSet?, defStyle: Int) {
        // Load attributes
        val a = context.obtainStyledAttributes(attrs, R.styleable.PageEmptyView, defStyle, 0)

        _default_icon = a.getResourceId(R.styleable.PageEmptyView_ev_icon, _default_icon)

        _default_content = a.getString(R.styleable.PageEmptyView_ev_content) ?: _default_content
        _default_content_color = a.getColor(R.styleable.PageEmptyView_ev_content_color, _default_content_color)
        _default_desc = a.getString(R.styleable.PageEmptyView_ev_desc) ?: _default_desc
        _default_desc_color = a.getColor(R.styleable.PageEmptyView_ev_desc_color, _default_desc_color)
        _default_positive = a.getString(R.styleable.PageEmptyView_ev_positive) ?: _default_positive
        _default_negative = a.getString(R.styleable.PageEmptyView_ev_negative) ?: _default_negative
//        _default_icon = a.getDrawable(R.styleable.PageEmptyView_ev_icon) ?: _default_icon
        a.recycle()
        loadView()
    }

    var clickListener: OnEmptyClickListener? = null

    private var ivIcon: ImageView? = null
    private var tvContent: TextView? = null
    private var tvDesc: TextView? = null
    private var btnPositive: TextView? = null
    private var btnNegative: TextView? = null

    private fun loadView() {
//        LayoutInflater.from(context).inflate(R.layout.include_empty_layout, this)
        val view = LayoutInflater.from(context).inflate(R.layout.common_empty_layout, null)
        addView(view, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
        ivIcon = view.findViewById(R.id.iv_empty_icon)
        tvContent = view.findViewById(R.id.tv_empty_info)
        tvDesc = view.findViewById(R.id.tv_empty_desc)
        btnPositive = view.findViewById(R.id.btn_positive)
        btnNegative = view.findViewById(R.id.btn_negative)

        setEmptyIcon(_default_icon)
        setContent(_default_content, _default_desc)
        setPositiveContent(_default_positive)
        setNegativeContent(_default_negative)
    }

    fun setEmptyIcon(iconId: Int) {
        ivIcon?.let {
            it.setImageResource(iconId)
            it.setOnClickListener {
                clickListener?.onAction(0)
            }
        }
    }

    fun setContent(content: String, desc: String = "") {
        tvContent?.let {
            it.text = content
        }

        tvDesc?.let {
            if (desc.isNotBlank()) {
                it.text = desc
                it.visibility = View.VISIBLE
            } else {
                it.visibility = View.GONE
            }
        }
    }

    fun setPositiveContent(content: String) {
        btnPositive?.let {
            if (content.isNotBlank()) {
                it.visibility = View.VISIBLE
                it.text = content
                it.setOnClickListener { clickListener?.onAction(1) }
            } else {
                it.visibility = View.GONE
            }
        }
    }

    fun setNegativeContent(content: String) {
        btnNegative?.let {
            if (content.isNotBlank()) {
                it.visibility = View.VISIBLE
                it.text = content
                it.setOnClickListener { clickListener?.onAction(2) }
            } else {
                it.visibility = View.GONE
            }
        }
    }

    fun show() {
        this.visibility = View.VISIBLE
    }

    fun hide() {
        this.visibility = View.GONE
    }
}

interface OnEmptyClickListener {
    /**
     * @param actionCode 0 图标点击
     *                   1 主动按钮
     *                   2 被动按钮
     */
    fun onAction(actionCode: Int)
}
