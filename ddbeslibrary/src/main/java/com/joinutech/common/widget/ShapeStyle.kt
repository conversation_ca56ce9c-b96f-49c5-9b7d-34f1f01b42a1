package com.joinutech.common.widget

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.View


/**
 * @Des：
 * @author: moon
 * @date: 6/11/24
 */
class ShapeStyle {

    companion object {

        private fun dp2px(context: Context, dp: Float): Int {
            val scale = context.resources.displayMetrics.density
            return (dp * scale + 0.5f).toInt()
        }

        fun setShapeStyle (view: View , bgColor: String ,  strokeWidth: Int = 1,  roundRadius: Float = 5f) {
            val roundRadius = dp2px(view.context, roundRadius).toFloat()
            val strokeColor = Color.parseColor("#E2E2E2") //边框颜色
            val fillColor = Color.parseColor(bgColor) //内部填充颜色
            val colors = intArrayOf(0xFFFFFF, 0xFFFFFF, 0xFFFFFF) //分别为开始颜色，中间夜色，结束颜色

            val gd = GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, colors) //创建drawable
            gd.setColor(fillColor)
//            gd.cornerRadius = roundRadius.toFloat()
            val corners = floatArrayOf(roundRadius, roundRadius, roundRadius, roundRadius ,0f,0f,0f,0f)
            gd.cornerRadii = corners
            gd.setStroke(strokeWidth, strokeColor)
            view.setBackgroundDrawable(gd)
        }

    }

}