package com.joinutech.common.storage

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.view.View
import android.widget.TextView
import com.joinutech.common.base.isDebug
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.bean.*
import com.joinutech.ddbeslibrary.request.DdbesApiUtil
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.request.RxScheduleUtil
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.UploadFileService
import com.joinutech.ddbeslibrary.utils.*
import com.marktoo.lib.cachedweb.LogUtil
import com.tencent.cos.xml.CosXmlServiceConfig
import com.tencent.cos.xml.CosXmlSimpleService
import com.tencent.cos.xml.transfer.TransferConfig
import com.tencent.cos.xml.transfer.TransferManager
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import io.reactivex.FlowableEmitter
import io.reactivex.FlowableOnSubscribe
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import okhttp3.ResponseBody
import timber.log.Timber
import java.io.*
import kotlin.random.Random

/**
 * @Description: TODO
 * @Author: zhaoyy
 * @Time:  -
 * @packageName:
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
class FileStorage {

    companion object {

        fun showLog(msg: String) {
            LogUtil.showLog(msg, "file_up__")
        }

        // TODO: 2020/12/31 11:06 修改为动态获取，切换存储区域  appId和存储区域动态获取
        /**存储应用id，bucket-appid拼接使用*/
//        var COS_APPID = "1257239906" // 北京 已废弃
        var COS_APPID = "1304188286" // 重庆

        /**获取存储位置*/
        @Volatile
        var COS_REGION = "ap-chongqing"
            @SuppressLint("CheckResult")
            set(value) {
                // 六合一获取的存储配置region数据为空，或者更新时需要重置
                when {
                    value.isNullOrBlank() -> {
                        getBucketConfig().subscribe {
                            updateCosConfig(it)
                        }
                        return
                    }
                    field != value -> {
                        normalBucketConfig?.region = value
                        field = value
//                        CosWrapper.reInitSdk()
                        CosWrapperV2.reInitSdk()
                        return
                    }
                    else -> {
                        field = value
                    }
                }
            }

//        /**担当通用文件存储 IM，个人头像，公司logo */
//        val tos_root_bucket = "ddbes-repository-$COS_APPID"

        /**担当云盘文件存储 文档 任务 审批 汇报*/
        var tos_pan_bucket: String = ""

        // TODO: 2020/12/31 11:06 修改为动态获取，切换存储区域  appId和存储区域动态获取 云存储默认路径前缀
//        val WEB_IMAGE_BASE_URL = "https://${tos_root_bucket}.cos.${cos_region}.myqcloud.com/"
        var TOS_DEFAULT_URL_PRE = "https://cdn.ddbes.com"


        /**项目logo 固定路径*/
//        val PROGRAM_LOGO_URL = "https://ddbes-task-${COS_APPID}.cos.${cos_region}.myqcloud.com/"
//        val PROGRAM_LOGO_URL = "https://cdn.task.ddbes.com/"

        private const val RANDOM_SOURCE_DATA = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"

        fun buildRandomString(source: String = RANDOM_SOURCE_DATA, length: Int = 18): String {
            val random = Random.Default
            val charArray = CharArray(length)
            for (i in 0 until length) {
                val index = random.nextInt(source.length)
                charArray[i] = source[index]
            }

            for (index in length - 1 downTo 0) {
                val rIndex = random.nextInt(index + 1)
                val temp = charArray[rIndex]
                charArray[rIndex] = charArray[index]
                charArray[index] = temp
            }

            return String(charArray)
        }

        /**对象在存储桶中的位置标识符，即对象键。*/
        fun buildCosPath(type: TosFileType): String {
            val tag = UserHolder.getUserId() ?: ""

            val suffix = when (type) {
                TosFileType.VOICE -> {
                    ".amr"
                }
                TosFileType.VIDEO -> {
                    ".mp4"
                }
                else -> {
                    ".png"
                }
            }
            val path = if (type.path.contains("pan", true)) {
                ""
            } else {
                type.path
            }
            val fileName = "${path}${tag}_${buildRandomString()}_${System.currentTimeMillis()}$suffix"
            FileUploadUtil.showLog("上传文件名为：$fileName")
            return fileName
        }

        fun getTencentCloudConfig(): CosXmlServiceConfig? {
            if (COS_REGION.isNullOrBlank()) return null
            return CosXmlServiceConfig.Builder()
                    .isHttps(true)
                    .setRegion(COS_REGION)
                    .setDebuggable(isDebug)
                    .builder()
        }

        fun getTransferManager(service: CosService): TransferManager {
            return TransferManager(service, TransferConfig.Builder().build())
        }

        fun isValidKey(expiredTime: Long): Boolean {
            if (cosSession != null){
                Timber.i("cosSession!!.expiredTime = ${cosSession!!.expiredTime}")
            }
            val current = (System.currentTimeMillis() / 1000)
            Timber.i("current = $current")
            return (expiredTime - current) >= 90
        }

        private var cosServerConfig: CosBucketConfig? = null

        @SuppressLint("CheckResult")
        fun getPanBucketConfig(): Flowable<CosBucketConfig> {
            return if (cosServerConfig == null) {
                FileStorage().getPanBucketConfig()
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
            } else {
//                Flowable.create({ emitter: FlowableEmitter<CosBucketConfig> ->
//                    emitter.onNext(cosServerConfig!!)
//                }, BackpressureStrategy.BUFFER)
                Flowable.just(cosServerConfig!!)
            }
        }

        fun updatePanBucketConfig(config: CosBucketConfig) {
            cosServerConfig = config
            tos_pan_bucket = config.bucket
        }

        private var normalBucketConfig: CosBucketConfig? = null

        @SuppressLint("CheckResult")
        fun getBucketConfig(): Flowable<CosBucketConfig> {
            showLog("启动接口返回region数据为空，重新获取开访问存储配置-->")
            return if (normalBucketConfig == null) {
                showLog("获取开访问存储配置--> 在线获取")
                FileStorage().getBucketConfig()
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
            } else {
                showLog("获取开访问存储配置--> 获取已经获得的配置")
                Flowable.create({ emitter: FlowableEmitter<CosBucketConfig> ->
                    emitter.onNext(normalBucketConfig!!)
                }, BackpressureStrategy.BUFFER)
            }
        }

        private fun updateCosConfig(config: CosBucketConfig) {
            normalBucketConfig = config
            COS_APPID = config.appId
            if (!config.region.isNullOrBlank() && COS_REGION != config.region) {
                COS_REGION = config.region
//                CosWrapper.reInitSdk()
//                CosWrapperV2.reInitSdk()
            }
            TOS_DEFAULT_URL_PRE = config.pre
        }

        var cosSession: TencentSessionBean? = null

        fun isSessionValid(): Boolean {
            return cosSession != null && isValidKey(cosSession!!.expiredTime)
        }

        /**请求tencent cos session 秘钥相关数据*/
        fun sessionRequest(): Flowable<TencentSessionBean> {
//            return if (isNewVersionAPI) {
            // 新版本直接获取通用token即可上传下载文件
            showLog("获取聚合秘钥信息秘钥")
            return UploadFileService.getPanTencentSessionV2()
                    .compose(ErrorTransformer.getInstance<TencentSessionBean>())
//            } else {
//                // 旧版本先获取云盘配置，再根据配置中bucket获取token即可上传下载文件
//                getPanBucketConfig().flatMap { config ->
//                    updatePanBucketConfig(config)
//                    UploadFileService.getPanTencentSession(config.bucket)
//                            .compose(ErrorTransformer.getInstance<TencentSessionBean>())
//                            .subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
//                }
//            }
        }
    }

    private fun saveToFile(file: File, response: ResponseBody, progress: (complete: Long, total: Long, percent: Int) -> Unit): Flowable<Int> {
        return Flowable.create({ emitter: FlowableEmitter<Int> ->
            if (!FileIOUtils.createOrExistsFile(file)) {
                emitter.onNext(-1)
            } else {
                var state = -1
                var fos: BufferedOutputStream? = null
                var completed = 0L
                var stream: InputStream? = null
                try {
                    val total = response.contentLength()
                    stream = response.byteStream()
                    fos = BufferedOutputStream(FileOutputStream(file, false))
                    val buffer = ByteArray(FileIOUtils.sBufferSize)
                    var len: Int
                    while (stream.read(buffer, 0, FileIOUtils.sBufferSize).also { len = it } != -1) {
                        fos.write(buffer, 0, len)
                        completed += len
                        if (total > 0) {
                            progress.invoke(completed, total, (completed * 100 / total).toInt())
                        }
                    }
                    state = 100
                } catch (e: IOException) {
                    e.printStackTrace()
                } finally {
                    try {
                        stream?.close()
                    } catch (e: IOException) {
                        e.printStackTrace()
                    }
                    try {
                        fos?.close()
                    } catch (e: IOException) {
                        e.printStackTrace()
                    }
                }
                emitter.onNext(state)
            }
        }, BackpressureStrategy.BUFFER)
    }

    /**
     * 检查云存储逻辑：
     * 获取文件hash信息
     * 判断文件是否存在云空间中
     * 获取云空间使用量*/
    fun checkCapacity(activity: MyUseBindingActivity<*>, companyId: String, uploadFiles: List<AddFileBean>, onResult: (files: List<AddFileBean>?, msg: String) -> Unit) {

        /**显示存储空间不足时提示*/
        fun showNoCapacityTip() {
            val dialog = BaseCenterDialogHelper(
                    activity = activity,
                    onBindView = { view ->
                        run {
                            val cancel = view.findViewById<View>(R.id.cancel)
                            val lineV = view.findViewById<View>(R.id.line_v)
                            val tvContent = view.findViewById<TextView>(R.id.tv_content)
                            tvContent.text = "团队云盘存储空间已满，无法上传更多图片/附件。" +
                                    "请登录pan.ddbes.com了解更多存储详情"
                            val tvTitle = view.findViewById<View>(R.id.tv_title)
                            val tvHint = view.findViewById<View>(R.id.tv_hint)
                            tvTitle.visibility = View.GONE
                            tvHint.visibility = View.GONE
                            cancel.visibility = View.GONE
                            lineV.visibility = View.GONE
                        }
                    },
                    onConfirm = {
                        onResult.invoke(uploadFiles, "-1")
                    },
                    onCancel = {
                        onResult.invoke(uploadFiles, "-1")
                    })
            dialog.initView()
            dialog.show(true)
        }

        /**获取云空间使用量*/
        fun getCloudCapacity() {
            showLog("需要上传文件，判断文件存储空间是否充足")
            val tag = "检查存储空间"
            RxScheduleUtil.rxSchedulerHelper(DdbesApiUtil.getTaskService().searchCompanyCapacity(
                    UserHolder.getAccessToken(), companyId)
            ).compose(ErrorTransformer.getInstance())
                    .subscribe(object : BaseSubscriber<CapacityBean>() {
                        override fun onError(ex: ApiException) {
                            RequestHelper.showLogLine(msg = ">>>$tag 返回错误<<<")
                            onResult.invoke(null, "获取团队存储空间失败")
                        }

                        override fun onComplete() {
                            RequestHelper.showLogLine(msg = ">>>$tag 获取结束<<<")
                        }

                        override fun onNext(result: CapacityBean?) {
                            if (result != null) {
                                RequestHelper.showLogLine(msg = ">>>$tag 返回数据<<<")
                                var computerFileTotalSize = 0L
                                uploadFiles.forEach { file ->
                                    if (!file.inCloud) {// 文件在云空间不存在时，才累计要上传的文件容量
                                        computerFileTotalSize += file.fileSize
                                    }
                                }
                                val canUsed = result.capacity - result.used
                                //判断是否容量可用
                                if (computerFileTotalSize <= canUsed) {
                                    onResult.invoke(uploadFiles, "0")
                                } else {
                                    showNoCapacityTip()
                                }
                            } else {
                                RequestHelper.showLogLine(msg = ">>>$tag 未返回数据<<<")
                                onResult.invoke(null, "未获取到团队存储空间信息")
                            }
                        }
                    })
        }

        /**判断存储空间是否可用*/
        fun checkCloudFileInfo(hashValues: HashMap<String, String>) {
            showLog("通过文件hash获取云盘文件存储信息，判断文件是否存在")
            val hashList = hashValues.values.toList()
            val map = hashMapOf<String, Any>()
            map["hash"] = hashList
            // 获取文件存储id相关信息
            UploadFileService.getPanTencentId(UserHolder.getAccessToken(), map)
                    .compose(activity.bindToLifecycle())
                    .compose(ErrorTransformer.getInstance())
                    .subscribe(object : BaseSubscriber<List<PanFileBean>>() {
                        override fun onError(ex: ApiException) {
                            activity.dismissDialog()
                            ToastUtil.show(BaseApplication.joinuTechContext, ex.message)
                        }

                        override fun onComplete() {

                        }

                        override fun onNext(fileKeyList: List<PanFileBean>?) {
                            showLog("获取文件存储fileId并判断是否存在该文件 ${GsonUtil.toJson(fileKeyList)} ----")
                            if (!fileKeyList.isNullOrEmpty()) {
                                var needUploadFile = false
                                uploadFiles.forEach { file ->
                                    val hash = hashValues[file.fileUrl]
                                    if (!hash.isNullOrBlank()) {
                                        val data = fileKeyList[hashList.indexOf(hash)]
                                        file.fileId = data.key
                                        file.inCloud = data.exist
                                        // CHANGE_HISTORY: 2021/1/25 11:14  云端已存在文件，bucket参数如何传
                                        file.bucket = data.bucket ?: ""
                                        file.fileHash = hash // 所有文件hash值复制
                                        if (!file.inCloud) {
                                            needUploadFile = true
                                        }
                                    }
                                }
                                if (needUploadFile) {
                                    getCloudCapacity()
                                } else {
                                    showLog("没有文件需要上传，云盘中已存在")
                                    onResult(uploadFiles, "1")
                                }
                            }
                        }
                    })
        }

        /**获取文件的md5，再通过后台获取上传文件的id值并判断文件是否在云端存在，确定需要上传文件列表后，再判断存储空间问题*/
        fun getFileSizeAndHash() {
            showLog("获取文件大小和hash值信息")

            val perms = arrayOf(
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
            val tips = "选取图片需要您授权读写"
            PermissionUtils.requestPermissionActivity(activity, perms, tips,
                    onSuccess = {
                        Flowable.create(FlowableOnSubscribe<HashMap<String, String>> { emitter ->
                            try {
                                val hashMap = hashMapOf<String, String>()
                                var hasBigFile = false
                                for (bean in uploadFiles) {
                                    // 获取文件体积
                                    bean.fileSize = File(bean.fileUrl).length()
                                    if (bean.fileSize > 4L * 1024 * 1024 * 1024) {
                                        hasBigFile = true
                                        break
                                    } else {
                                        // 获取文件hash md5
                                        if (hashMap.isEmpty() || !hashMap.containsKey(bean.fileUrl)) {
                                            val hashValue = StringUtils.getFileMD5(File(bean.fileUrl))
                                            if (StringUtils.isNotBlankAndEmpty(hashValue)) {
                                                hashMap[bean.fileUrl] = hashValue
                                            }
                                        }
                                    }
                                }

                                when {
                                    hasBigFile -> {
                                        emitter.onNext(hashMapOf("hasBig" to ""))
                                    }
                                    hashMap.isNotEmpty() -> {
                                        emitter.onNext(hashMap)
                                    }
                                    else -> {
                                        emitter.onNext(hashMapOf())
                                    }
                                }
                            } catch (e: Exception) {
                                emitter.onNext(hashMapOf("hasError" to ""))
                            }
                        }, BackpressureStrategy.BUFFER)
                                .subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribe { hashValues ->
                                    if (hashValues.isNullOrEmpty()) {
                                        onResult.invoke(null, "文件暂时无法上传")
                                    } else {
                                        if (hashValues.containsKey("hasBig") || hashValues.containsKey("hasError")) {
                                            onResult.invoke(null, "文件暂时无法上传")
                                        } else {
                                            showLog("获取所有要上传文件的hash完成，开始请求文件id ${GsonUtil.toJson(hashValues)}")
                                            checkCloudFileInfo(hashValues)
                                        }
                                    }
                                }
                    },
                    onError = {
                        ToastUtil.show(BaseApplication.joinuTechContext, tips)
                    })
        }

        getFileSizeAndHash()
    }

    fun submitFileId(uploadFile: PanUploadBean, onResult: () -> Unit) {
        associateToPan(uploadFile)
                .subscribe(object : BaseSubscriber<PanUploadResult>() {
                    override fun onError(ex: ApiException) {
                        ToastUtil.show(BaseApplication.joinuTechContext, ex.message)
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(fileKeyList: PanUploadResult?) {
                        showLog("文件上传后绑定给到公司目录信息上， 结果回调 ${GsonUtil.toJson(fileKeyList)} ----")
                        onResult.invoke()
                    }
                })
    }

    fun associateToPan(uploadFile: PanUploadBean): Flowable<PanUploadResult> {
        return UploadFileService.uploadFile(uploadFile)
                .compose(ErrorTransformer.getInstance())
    }

    fun getBucketConfig(): Flowable<CosBucketConfig> {
        return UploadFileService.getBucketConfig().compose(ErrorTransformer.getInstance())
    }

    fun getPanBucketConfig(): Flowable<CosBucketConfig> {
        return UploadFileService.getPanBucketConfig().compose(ErrorTransformer.getInstance())
    }

}

class CosService(context: Context, configuration: CosXmlServiceConfig,
                 var provider: MyCredentialProvider)
    : CosXmlSimpleService(context.applicationContext, configuration) {
    init {
        credentialProvider = provider
    }

    fun updateProvider(provider: MyCredentialProvider) {
        this.credentialProvider = provider
    }

    /**秘钥更新时调用*/
    fun updateProvider(session: TencentSessionBean) {
        this.credentialProvider = MyCredentialProvider(session.credentials.tmpSecretId,
                session.credentials.tmpSecretKey,
                session.credentials.sessionToken, session.expiredTime)
    }

    /**存储区域更新时调用*/
    fun updateConfig(config: CosXmlServiceConfig?) {
        if (config != null) {
            setNetworkClient(config)
        }
    }
}

/**添加文件传输*/
data class AddFileBean(
        /**下载时，为远程可下载文件地址*/
        val fileUrl: String,
        /**下载时，为远程可下载文件名称，包含后缀*/
        val fileName: String,
        var fileSize: Long = 0L,
        var fileHash: String = "",
        var inCloud: Boolean = false,
        /**下载时，为远程可下载文件存储id*/
        var fileId: String = "",
        val createTime: Long = 0L,
        /**添加后不下载标记，默认添加后即开始下载*/
        val pauseAdd: Boolean = false,
        /**文件的存储桶信息*/
        var bucket: String = "",
        /**媒体文件类型*/
        var mimeType: String = "image/jpeg"
)