package com.joinutech.common.storage

import android.annotation.SuppressLint
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.common.base.isDebug
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.bean.PanUploadBean
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.utils.*
import com.marktoo.lib.cachedweb.LogUtil
import com.tencent.cos.xml.exception.CosXmlClientException
import com.tencent.cos.xml.exception.CosXmlServiceException
import com.tencent.cos.xml.listener.CosXmlResultListener
import com.tencent.cos.xml.model.CosXmlRequest
import com.tencent.cos.xml.model.CosXmlResult
import com.tencent.cos.xml.transfer.COSXMLTask
import com.tencent.cos.xml.transfer.COSXMLUploadTask
import com.tencent.cos.xml.transfer.TransferState
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import io.reactivex.FlowableEmitter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import java.util.*

/**文件上传传输
 *
 * 组织相关文件上传
 *
 * 文件传输：
 * 添加文件、下载文件
 * 重新开始或重试下载
 *
 * 网络变化后检查网络状态，如果WiFi则继续下载，如果流量则提示 全部暂停或提示
 *
 * 网络断开时先全部暂停并保存进度信息
 *
 */
object FileUpTransferManager : TransListener {

    var isLoaded = false

    fun onLoad(): List<String> {
        FileStorage.showLog("获取团队上传记录信息")
        val result = MMKVUtil.getString("${UserHolder.getUserId()}_transfer_company_ids")
        val waitTransOrgList = arrayListOf<String>()
        if (result.isNotBlank() && result.startsWith("[") && result.endsWith("]")) {
            val list = GsonUtil.getList2(result, String::class.java)
            if (!list.isNullOrEmpty()) {
                list.forEach {
                    if (getTransfer(it).onLoad() > 0) {
                        waitTransOrgList.add(it)
                    }
                }
            }
        }
        isLoaded = true
        return waitTransOrgList
    }

    fun onRetry(companyId: String, fileIndies: List<String>) {
        getTransfer(companyId).onRetry(fileIndies)
    }

    fun onResume(companyId: String, fileIndies: List<String>) {
        getTransfer(companyId).onResume(fileIndies)
    }

    fun onPause(companyId: String, fileIndies: List<String>) {
        getTransfer(companyId).onPause(fileIndies)
    }

    fun onCancel(companyId: String, fileIndies: List<String>) {
        getTransfer(companyId).onCancel(fileIndies)
    }

    fun onResumeAll() {
        if (transManager.isNotEmpty()) {
            transManager.keys.forEach {
                getTransfer(it).onResumeAll()
            }
        }
    }

    fun onPauseAll() {
        if (transManager.isNotEmpty()) {
            transManager.keys.forEach {
                getTransfer(it).onPauseAll()
            }
        }
    }

    fun onSave() {
        if (transManager.isNotEmpty()) {
            transManager.keys.forEach {
                getTransfer(it).onSave()
            }
            MMKVUtil.saveString("${UserHolder.getUserId()}_transfer_company_ids", GsonUtil.toJson(transManager.keys))
        } else {
            MMKVUtil.clearByKey("${UserHolder.getUserId()}_transfer_company_ids")
        }
    }

    @JvmField
    @Volatile
    var onTransListener: TransListener? = null

    @JvmField
    @Volatile
    var transManager = hashMapOf<String, CompanyUploadTransfer>()

    fun getTransOrgList(): List<String> {
        if (transManager.isEmpty()) return arrayListOf()
        return transManager.keys.toList()
    }

    /**批量上传文件*/
    fun addUploadFiles(companyId: String, parentId: String, files: List<AddFileBean>, state: String) {
        getTransfer(companyId).addUploadFiles(parentId, files, state)
    }

    fun getTransHistory(companyId: String): List<FileTransferInfo> {
        val list = arrayListOf<FileTransferInfo>()

        val processList = getTransfer(companyId).transList
        list.add(FileTransferInfo(1, progress = processList.size))
        list.addAll(processList)

        val successList = getTransfer(companyId).transFinishList
        list.add(FileTransferInfo(2, progress = successList.size))
        list.addAll(successList)
        return list
    }

    private fun getTransfer(companyId: String): CompanyUploadTransfer {
        if (!transManager.containsKey(companyId) || transManager[companyId] == null) {
            transManager[companyId] = CompanyUploadTransfer(companyId, this)
        }
        return transManager[companyId]!!
    }

    var instanceCosService: CosService? = null

    private fun getCosService(): CosService? {
        if (instanceCosService != null) return instanceCosService
        FileStorage.showLog("PanService 初始化")
        val t = FileStorage.cosSession ?: return null
        val secretId = t.credentials.tmpSecretId
        val secretKey = t.credentials.tmpSecretKey
        val sessionToken = t.credentials.sessionToken
        val expiredTime: Long = t.expiredTime//临时密钥有效截止时间戳
        //初始化 config
        val serviceConfig = FileStorage.getTencentCloudConfig() ?: return null
        //使用临时密钥初始化QCloudCredentialProvider
        val qCloudCredentialProvider = MyCredentialProvider(secretId, secretKey, sessionToken, expiredTime)
//            not necessary
//            qCloudCredentialProvider.refresh()
        //初始化CosXmlService
        instanceCosService = CosService(BaseApplication.joinuTechContext, serviceConfig, qCloudCredentialProvider)
        return instanceCosService
    }

    /**
     * 单文件上传 带进度回调
     * onSuccess onError String is fileId
     */
    fun uploadSingleFile(fileBean: FileTransferInfo,
                         onSuccess: (fileInfo: FileTransferInfo) -> Unit,
                         onError: (fileIndex: String) -> Unit,
                         onCreateTask: (TransTaskHolder<COSXMLUploadTask>) -> Unit,
                         onTransfer: (FileTransferInfo) -> Unit) {

        FileStorage.showLog("单文件上传：$fileBean")
        val uploadWithCustomName = createUploadTask(fileBean, onCreateTask, onTransfer)

        checkUploadSession(uploadWithCustomName)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(object : BaseSubscriber<FileTransferInfo>() {
                    override fun onError(ex: ApiException) {
                        FileStorage.showLog("上传文件失败： ${ex.code} - ${ex.message}")
                        onError(ex.message)
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(result: FileTransferInfo) {
                        FileStorage.showLog("上传文件回调： $result")
                        onSuccess.invoke(result)
//                                if (result.state == 3) {
//                                } else {
//                                    onError.invoke(result.index)
//                                }
                    }
                })
    }

    /**
     * 真实上传操作，会自动判断秘钥是否过期，是否需要请求秘钥
     */
    private fun checkUploadSession(upload: Flowable<FileTransferInfo>): Flowable<FileTransferInfo> {
        FileStorage.showLog("文件上传-->检查session是否过期（请求秘钥后存储），执行上传文件")
        return if (FileStorage.isSessionValid()) {
            FileStorage.showLog("秘钥有效，直接上传文件")
            upload
        } else {
            FileStorage.sessionRequest().flatMap {
                FileStorage.showLog("请求秘钥后转型,执行上传文件")
                FileStorage.cosSession = it
                getCosService()?.updateProvider(it)
                upload
            }
        }
    }

    /*上传文件在异步线程中，所以切换到主线程再回调*/
    private fun createUploadTask(fileBean: FileTransferInfo,
                                 onCreateTask: (TransTaskHolder<COSXMLUploadTask>) -> Unit,
                                 onTransfer: (FileTransferInfo) -> Unit): Flowable<FileTransferInfo> {
        return Flowable.create({ emitter: FlowableEmitter<FileTransferInfo> ->
            createUploadFileTask(fileBean,
                    onCreateTask = onCreateTask,
                    onTransfer = onTransfer,
                    onResult = {
                        emitter.onNext(it)
                    })
        }, BackpressureStrategy.BUFFER)
    }

    private fun createUploadFileTask(fileBean: FileTransferInfo,
                                     /**创建任务回调*/
                                     onCreateTask: (TransTaskHolder<COSXMLUploadTask>) -> Unit,
                                     /**进度和状态回调*/
                                     onTransfer: (FileTransferInfo) -> Unit,
                                     /**结果成功和失败回调*/
                                     onResult: (FileTransferInfo) -> Unit) {
        try {
            val bucket = if (FileStorage.cosSession?.panBucket.isNullOrBlank()) {
                FileStorage.tos_pan_bucket
            } else {
                if (FileStorage.cosSession?.panBucket?.contains(FileStorage.COS_APPID) == false) {
                    FileStorage.cosSession?.panBucket?.plus("-")?.plus(FileStorage.COS_APPID)
                } else {
                    FileStorage.cosSession?.panBucket
                }
            }
            if (getCosService() == null || bucket.isNullOrBlank()) {
                fileBean.state = -2
                onResult.invoke(fileBean)
            } else {
                val transManager = FileStorage.getTransferManager(getCosService()!!)
                val uploadId = null //若存在初始化分块上传的 UploadId，则赋值对应的 uploadId 值用于续传；否则，赋值 null
//            // TODO: 2020/12/31 13:53 本地拼接文件路径，访问上传的文件 ？？
//            fileBean.cosPath = "https://$bucket.cos.${FileStorage.getRegion()}.myqcloud.com/${fileBean.fileId}"
                //上传对象
                val upTask: COSXMLUploadTask = transManager.upload(bucket, fileBean.fileId, fileBean.srcPath, uploadId)
                upTask.setCosXmlProgressListener { completed, total ->
                    val percent = (completed * 100 / total).toInt()
//                    FileStorage.showLog("path:${fileBean.srcPath}, over:$completed, total:$total, per:$percent")
                    if (percent % 5 == 0) {
                        fileBean.completed = completed
                        fileBean.progress = percent
                        fileBean.state = 1
                        onTransfer.invoke(fileBean)// 上传进度回调
                    }
                }
                //设置返回结果回调                    val cosPath = result!!["accessUrl"]
                upTask.setCosXmlResultListener(object : CosXmlResultListener {
                    override fun onSuccess(request: CosXmlRequest?, result: CosXmlResult?) {
                        fileBean.cosPath = result!!.accessUrl// 拼接的文件路径地址，不可用，需要后端拼接权限信息后才可用
                        val mutableList = result!!.headers["ETag"]
                        val hash = if (!mutableList.isNullOrEmpty()) {
                            mutableList[0].replace("\"", "")
                        } else {
                            ""
                        }
                        if (hash != fileBean.hash) {
                            FileStorage.showLog("本地和腾讯云接收文件hash不一致 remote=$hash local=${fileBean.hash}")
                        }
                        fileBean.completed = fileBean.total
                        fileBean.progress = 100
                        fileBean.state = 3
                        fileBean.bucket = bucket
                        onResult.invoke(fileBean) // 上传成功回调
                    }

                    override fun onFail(request: CosXmlRequest?,
                                        exception: CosXmlClientException?,
                                        serviceException: CosXmlServiceException?) {
                        fileBean.state = -2
                        onResult.invoke(fileBean) // 上传失败回调
                    }
                })

                upTask.setTransferStateListener { transState ->
                    // 文件传输状态回调
                    /**
                     * -3 unknown
                     * -2 failed
                     * -1 cancel
                     * 0 waiting
                     * 1 process
                     * 2 pause
                     * 3 completed
                     * */
                    val state = when (transState) {
                        TransferState.CONSTRAINED -> {
                            -3
                        }
                        TransferState.RESUMED_WAITING -> {
                            // resumed,queue fo execution,no start to transfer data
                            0
                        }
                        TransferState.WAITING -> {
                            // in queue, no started
                            0
                        }
                        TransferState.IN_PROGRESS -> {
                            // 处理中
                            1
                        }
                        TransferState.PAUSED -> {
                            // pause manual
                            2
                        }
                        TransferState.COMPLETED -> {
                            // 已完成
                            3
                        }
                        TransferState.CANCELED -> {
                            // cancel transfer data
                            -1
                        }
                        TransferState.FAILED -> {
                            // transfer with exception
                            -2
                        }
                        TransferState.UNKNOWN -> {
                            // unknown state
                            -3
                        }
                    }
                    fileBean.state = state
                    onTransfer.invoke(fileBean)// 上传状态回调
                }

                onCreateTask.invoke(TransTaskHolder(fileBean.index, upTask))// 上传任务创建成功回调
            }
        } catch (e: CosXmlClientException) {
            e.printStackTrace()
            fileBean.state = -2
            onResult.invoke(fileBean) // 上传失败回调
        } catch (e: CosXmlServiceException) {
            e.printStackTrace()
            fileBean.state = -2
            onResult.invoke(fileBean) // 上传失败回调
        } catch (e: Throwable) {
            e.printStackTrace()
            fileBean.state = -2
            onResult.invoke(fileBean) // 上传失败回调
        } catch (e: Exception) {
            e.printStackTrace()
            fileBean.state = -2
            onResult.invoke(fileBean) // 上传失败回调
        }
    }

    override fun onNoTask(companyId: String) {
        onTransListener?.onNoTask(companyId)
    }

    override fun onTransResult(companyId: String, file: FileTransferInfo) {
        onTransListener?.onTransResult(companyId, file)
    }

    override fun onTransProcess(companyId: String, file: FileTransferInfo) {
        onTransListener?.onTransProcess(companyId, file)
    }
}

class CompanyUploadTransfer(val companyId: String, val listener: TransListener) {

    /**
     * 进行中记录
     * 团队文件传输记录
     * 用于记录添加的传输文件信息
     * 成功 失败 暂停 删除都需要更新这个集合
     * */
    val transList = arrayListOf<FileTransferInfo>()

    /**已完成记录*/
    val transFinishList = arrayListOf<FileTransferInfo>()

    /**
     * 团队传输队列 需要上传文件的记录集合
     * key 为在传输记录中的索引
     * value 为文件信息记录，
     * 成功、失败、暂停后更新这个记录信息到传输列表中key对应位置
     * 更新信息时，可以筛选队列中是否存在相同的文件上传信息，如果存在，则置为完成状态
     * 成功的文件要调用绑定parentId接口关联到具体目录中
     * */
    private val transQueue = LinkedList<FileTransferInfo>()

    private var currentUploadFile: FileTransferInfo? = null

    private var currentUploadTask: COSXMLTask? = null

//    fun addUploadFile(parentId: String, file: UploadFileBean, state: String) {
//        addUploadFiles(parentId, arrayListOf(file), state)
//    }

    fun addUploadFiles(parentId: String, uploadFiles: List<AddFileBean>, state: String) {
        translateFileData(parentId, uploadFiles, state)
    }

    /**
     * 多文件上传 类型转换，存储缓存相关
     * @param state -1 存储空间不足 0 未上传 1 已上传
     */
    private fun translateFileData(parentId: String, hashList: List<AddFileBean>, state: String) {
        FileStorage.showLog("转换数据类型，构造文件传输数据类型")
        val transList = hashList.map { file ->
            val fileState = if (state == "-1") -4 else if (file.inCloud) 3 else 0
            FileTransferInfo(name = file.fileName, srcPath = file.fileUrl,// 文件本地地址
                    fileId = file.fileId, hash = file.fileHash, total = file.fileSize,
                    parentId = parentId,
                    startTime = System.currentTimeMillis(),
                    completed = if (file.inCloud) file.fileSize else 0L,
                    progress = if (file.inCloud) 100 else 0,
                    updateTime = if (file.inCloud) System.currentTimeMillis() else 0L,
                    pauseAdd = file.pauseAdd,
                    state = fileState,
                    bucket = file.bucket,
                    mimeType = file.mimeType)
        }.toMutableList() as ArrayList
        addUploadFiles(transList)
        updateQueue()
    }

    /**
     * 新选择的文件中，如果云盘中已存在，则添加到完成队列中，同时，关联到正式空间，
     * 不在云盘中文件，才加入上传处理队列中，等待上传
     * 添加到传输列表和恢复缓存到传输列表
     * 返回待处理文件数量
     * */
    private fun addUploadFiles(files: List<FileTransferInfo>): Int {
        val time = System.currentTimeMillis()
        var index = 1
        val successList = arrayListOf<FileTransferInfo>()
        files.forEach { file ->
            if (file.state == 3) {// 已经上传完成的文件记录
                transFinishList.add(file)
                successList.add(file)
            } else {// 需要上传的文件才会加入到队列中
                showList = true
                file.state = if (file.pauseAdd) 2 else 0
                file.index = "${time}_${index++}"// 文件索引，记录当前上传文件顺序信息
                transList.add(file)
                if (!file.pauseAdd) {
                    transQueue.addLast(file)
                }
            }
        }
        if (successList.isNotEmpty()) {
            batchAssociateToPan(successList)
        }
        return transQueue.size
    }

    /**添加文件到传输队列时，过滤已上传文件，通知到网盘*/
    private fun batchAssociateToPan(successList: ArrayList<FileTransferInfo>) {
        FileStorage.showLog("遇到添加任务时，发现文件已经在云盘存在，直接进行关联操作")
        var associateCount = 0
        FileStorage.getPanBucketConfig().flatMap { config ->
            FileStorage.updatePanBucketConfig(config)
            Flowable.fromIterable(successList).flatMap { source ->
                val bucket = if (source.bucket.isNullOrBlank()) config.bucket else source.bucket
                FileStorage().associateToPan(
                        PanUploadBean(source.hash, source.name, source.parentId,
                                source.fileId, bucket = bucket)
                )
            }
        }.subscribe(
                object : BaseSubscriber<Any>() {
                    override fun onError(ex: ApiException) {
                        FileStorage.showLog("批量关联文件 失败一个")
                        associateCount++
                        if (associateCount == successList.size) {
                            onComplete()
                        }
                    }

                    override fun onComplete() {
                        FileStorage.showLog("批量关联文件完成后，发送上传完成事件")
                        EventBusUtils.sendEvent(EventBusEvent("file_transfer_success", ""))
                    }

                    override fun onNext(result: Any?) {
                        associateCount++
                        FileStorage.showLog("批量关联文件 成功一个")
                        if (associateCount == successList.size) {
                            onComplete()
                        }
                    }
                })
    }

    @SuppressLint("CheckResult")
    private fun associatedToPan(source: FileTransferInfo) {
        FileStorage.getPanBucketConfig().subscribe { config ->
            FileStorage.updatePanBucketConfig(config)
            val bucket = if (source.bucket.isNullOrBlank()) config.bucket else source.bucket
            FileStorage().submitFileId(PanUploadBean(source.hash, source.name, source.parentId,
                    source.fileId, bucket = bucket)) {
                EventBusUtils.sendEvent(EventBusEvent("file_transfer_success", source.hash))
            }
        }
    }

//    fun addUploadFile(file: FileTransferInfo) {
//        if (file.state == 3) {// 已经上传完成的文件记录
//            transFinishList.add(file)
//        } else {
//            showList = true
//            file.index = System.currentTimeMillis().toString()
//            transList.add(file)
//            transQueue.addLast(file)
//        }
//        updateQueue()
//    }

    private var showList = false

    private fun updateQueue() {
        FileStorage.showLog("更新上传队列。。。")
        if (currentUploadTask == null) {
            if (transQueue.isNotEmpty()) {
                FileStorage.showLog("上传队列存在待处理任务")
                val file = transQueue.pop()
                if (file.progress == 0) {
                    file.startTime = System.currentTimeMillis()// 更新上传文件开始时间信息
                    if (file.index.isNotBlank()) {
                        transList.find { it.index == file.index }?.let {
                            transList[transList.indexOf(it)] = file
                        }
                    }
                }
                currentUploadFile = file// 当前上传文件
                transferUpload(file)
                if (isDebug && showList) {// TODO: 2020/8/5 17:24 测试代码，发版清除
                    showList = false
                    ARouter.getInstance().build("/clouddoc/trans_list")
                            .withInt("targetIndex", 1)
                            .navigation()
                }
            } else {
                FileStorage.showLog("上传队列不存在待处理任务")
                onSave()
                listener.onNoTask(companyId)
            }
        } else {
            FileStorage.showLog("上传任务进行中")
        }
    }

    private fun transferUpload(file: FileTransferInfo) {
        FileStorage.showLog("处理文件传输 ${GsonUtil.toJson(file)}")
        FileUpTransferManager.uploadSingleFile(file,
                onSuccess = {
                    if (it.index == currentUploadFile?.index) {
                        currentUploadFile?.let { source ->
                            if (file.index.isNotBlank()) {
                                if (it.state == 3) {
                                    source.completed = source.total
                                    source.progress = 100
                                    source.updateTime = System.currentTimeMillis()
                                    source.state = 3
                                    source.bucket = it.bucket
                                    onResult(source, true)
                                } else {
                                    source.completed = 0
                                    source.progress = 0
                                    source.updateTime = System.currentTimeMillis()
                                    source.state = -2
                                    source.bucket = it.bucket
                                    onResult(source)
                                }
                            }
                        }
                    }
                },
                onError = {
                    LogUtil.showLog(it)
                },
                onCreateTask = {
                    if (it.fileIndex == currentUploadFile?.index) {
                        currentUploadTask = it.task
                    }
                },
                onTransfer = {
                    FileStorage.showLog("文件上传进度回调线程：${Thread.currentThread().id} :: ${it.state}::${it.progress}::${it.completed}/${it.total}")
                    if (it.index == currentUploadFile?.index) {
                        currentUploadFile?.cosPath = it.cosPath
                        currentUploadFile?.completed = it.completed
                        currentUploadFile?.progress = it.progress
                        currentUploadFile?.state = it.state
                        updateSource(currentUploadFile)
                    }
                })
    }

    /**下载中更新数据源，需要UI现实的位置需要设置监听器接收信息*/
    private fun updateSource(source: FileTransferInfo?) {
        FileStorage.showLog("更新文件上传源数据")
        if (source != null && source.index.isNotBlank()) {
            val index = transList.indexOfFirst { it.index == source.index }
            if (index >= 0) {
                transList[index] = source
            }
            listener.onTransProcess(companyId, source)
        }
    }

    /**文件上传成功后关联到云盘正式空间*/
    private fun onResult(targetUpload: FileTransferInfo, success: Boolean = false) {
        if (success) {
            if (targetUpload.index.isNotBlank()) {
                val target = transList.find { it.index == targetUpload.index }
                if (target != null) {
                    transList.remove(target)
                }
            }
            transFinishList.add(targetUpload)
            associatedToPan(targetUpload)// 本次上传的成功的文件关联
            onSaveComplete()
        } else {
            if (targetUpload.index.isNotBlank()) {
                val index = transList.indexOfFirst { it.index == targetUpload.index }
                if (index >= 0) {
                    transList[index] = targetUpload
                }
            }
        }
        FileStorage.showLog("重置当前下载信息，回调结果给监听器，触发队列下载")
        currentUploadFile = null
        currentUploadTask = null
        listener.onTransResult(companyId, targetUpload)
        updateQueue()
    }

    fun onRetry(fileIndies: List<String>) {
        FileStorage.showLog("重试下载")
        if (transList.isNotEmpty()) {
            transList.forEach {
                if (it.index in fileIndies) {
                    it.state = 0
                    transQueue.addLast(it)
                }
            }
        }
        if (currentUploadFile == null) {
            FileStorage.showLog("当前没有文件在上传")
            updateQueue()
        } else {
            if (currentUploadFile?.index in fileIndies) {
                FileStorage.showLog("重试当前文件上传")
                currentUploadFile!!.completed = 0
                transferUpload(currentUploadFile!!)
            } else {
                FileStorage.showLog("加入队列后等待上传...")
            }
        }
    }

    /**上传恢复时，通过断点上传开始恢复上传任务，应该增加token是否过期检查后再执行上传*/
    fun onResume(fileIndies: List<String>) {
        FileStorage.showLog("恢复上传")
        if (transList.isNotEmpty()) {
            transList.forEach {
                if (it.index in fileIndies) {
                    it.state = 0
                    transQueue.addLast(it)
                }
            }
        }
        if (currentUploadTask != null) {
            currentUploadTask?.resume()
        }
//        if (currentUploadFile != null && currentUploadFile?.index in fileIndies) {
//            currentUploadTask?.resume()
//        }
        updateQueue()
    }

    fun onPause(fileIndies: List<String>) {
        FileStorage.showLog("暂停上传")
        if (transList.isNotEmpty()) {
            transList.forEach {
                if (it.index in fileIndies) {
                    it.state = 2
                }
            }
        }
        val queList = transQueue.filter { it.index in fileIndies }
        if (queList.isNotEmpty()) {
            queList.forEach {
                transQueue.remove(it)
            }
        }
        currentUploadTask?.pause()
//        if (currentUploadFile != null && currentUploadFile?.index in fileIndies) {
//            currentUploadFile = null
//        }
        updateQueue()
    }

    fun onResumeAll() {
        FileStorage.showLog("恢复上传")
        transQueue.clear()
        if (transList.isNotEmpty()) {
            transList.forEach {
                if (it.state == 2) {
                    it.state = 0
                    transQueue.addLast(it)
                }
            }
        }
        if (currentUploadTask != null) {
            currentUploadTask?.resume()
        } else {
            updateQueue()
        }
    }

    fun onPauseAll() {
        FileStorage.showLog("暂停上传")
        transQueue.clear()
        if (transList.isNotEmpty()) {
            transList.forEach {
                if (it.state in 0..1) {
                    it.state = 2
                }
            }
        }
        currentUploadTask?.pause()
        onSave()
    }

    fun onCancel(fileIndies: List<String>) {
        val cancelList = transList.filter { it.index in fileIndies }
        cancelList.forEach {
            transList.remove(it)
        }
        val cancelFinishList = transFinishList.filter { it.index in fileIndies }
        cancelFinishList.forEach {
            transFinishList.remove(it)
        }
        val cancelQueueList = transQueue.filter { it.index in fileIndies }
        cancelQueueList.forEach {
            transQueue.remove(it)
        }
        if (currentUploadFile?.index in fileIndies) {
            currentUploadTask?.cancel()
            currentUploadFile = null
            currentUploadTask = null
            updateQueue()
        }
    }

    fun onLoad(): Int {
        FileStorage.showLog("获取团队${companyId}上传记录信息")

        val successList = MMKVUtil.getString("${companyId}_transfer_success")
        if (successList.isNotBlank() && successList.startsWith("[") && successList.endsWith("]")) {
            val result = GsonUtil.getList2(successList, FileTransferInfo::class.java)
            if (!result.isNullOrEmpty()) {
                transFinishList.addAll(result)
            }
        }
        val processList = MMKVUtil.getString("${companyId}_transfer_process")
        if (processList.isNotBlank() && processList.startsWith("[") && processList.endsWith("]")) {
            val result = GsonUtil.getList2(processList, FileTransferInfo::class.java)
            if (!result.isNullOrEmpty()) {
                return addUploadFiles(result)
            }
        }
        return 0
    }

    fun onSave() {
        onSaveTransfer()
        onSaveComplete()
    }

    private fun onSaveTransfer() {
        if (transList.isNotEmpty()) {
            MMKVUtil.saveString("${companyId}_transfer_process", GsonUtil.toJson(transList))
        } else {
            MMKVUtil.clearByKey("${companyId}_transfer_process")
        }
    }

    private fun onSaveComplete() {
        if (transFinishList.isNotEmpty()) {
            MMKVUtil.saveString("${companyId}_transfer_success", GsonUtil.toJson(transFinishList))
        } else {
            MMKVUtil.clearByKey("${companyId}_transfer_success")
        }
    }

}