package com.joinutech.common.storage

import android.content.Context
import android.net.Uri
import android.os.Build
import android.os.Environment
import androidx.core.content.FileProvider
import com.google.gson.reflect.TypeToken
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.utils.GsonUtil
import java.io.File

/**
 * PackageName: com.joinutech.common.storage
 * ClassName: FileUtil
 * Author: zhaoyy
 * Leader: Ke
 * CreateTime: 2020/7/31 14:08
 * Desc: //TODO 文件管理
 */
object FileUtil {

    private var authority: String? = null

    /**
     * <provider
    android:name="android.support.v4.content.FileProvider"
    android:authorities="com.marktoo.test.fileProvider"
    android:exported="false"
    android:grantUriPermissions="true">
    <meta-data
    android:name="android.support.FILE_PROVIDER_PATHS"
    android:resource="@xml/file_path" />
    </provider>
     * */
    fun init(authority: String = "com.marktoo.test.fileProvider") {
        this.authority = authority
    }

    fun getContentUri(context: Context, file: File?): Uri? {
        return if (file == null || authority.isNullOrEmpty()) {
            null
        } else
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                FileProvider.getUriForFile(context, authority!!, file)
            } else {
                Uri.fromFile(file)
            }
    }

    fun getFilePath(context: Context, pathName: String = "temp"): String {
        val rootDir = if (Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED) {
            context.getExternalFilesDir(null)
        } else {
            context.filesDir
        }
        val parentDir = File(rootDir, pathName)
        if (!parentDir.parentFile.exists()) {
            parentDir.parentFile.mkdirs()
        }
        if (!parentDir.exists()) {
            parentDir.mkdir()
        }
        return parentDir.absolutePath
    }

    fun buildFile(context: Context, parent: String, fileName: String): File {
        val rootDir = if (Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED) {
            context.getExternalFilesDir(null)
        } else {
            context.filesDir
        }
        val parentDir = File(rootDir, parent)
        if (!parentDir.exists()) {
            parentDir.mkdirs()
        }
        return File(parentDir, fileName)
    }

//    fun getFileName(filePath: String): String {
//        return File(filePath).name
//    }

    private val fileTypeHolder = hashMapOf<String, Int>()

    fun getFileTypeIcon(fileName: String, fileType: String = "", force: Boolean = false): Int {
//        val mContext: WeakReference<Context> = WeakReference(context)
        if (fileType == "1") {
            return R.mipmap.ic_file_type_wjj
        }
        if (fileName.isNotBlank() && fileName.contains(".")) {
            val extra = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase()
            if (extra.length > 5) return R.mipmap.ic_file_type_wu
            if (!force && fileTypeHolder.containsKey(extra) && fileTypeHolder[extra] != null) {
                return fileTypeHolder[extra]!!
            } else {
                val json = "{\"ai\":[\"ai\"],\"doc\":[\"doc\",\"docx\"],\"dwf\":[\"dwf\"],\"dwg\":[\"dwg\"],\"excel\":[\"xls\",\"xlsx\"],\"gif\":[\"gif\"],\"html\":[\"html\"],\"jpg\":[\"jpg\",\"png\",\"jpeg\",\"webp\",\"tif\",\"bmp\",\"pcx\",\"emf\",\"tga\"],\"js\":[\"js\"],\"mp3\":[\"mp3\",\"wma\",\"rm\",\"wav\",\"midi\"],\"mp4\":[\"mp4\",\"avi\",\"rmbvb\",\"mov\",\"flv\",\"3gp\"],\"pdf\":[\"pdf\"],\"ppt\":[\"ppt\",\"pptx\",\"pps\",\"pot\",\"ppa\"],\"psd\":[\"psd\",\"psb\"],\"rp\":[\"rp\"],\"skp\":[\"skp\"],\"stl\":[\"stl\"],\"swf\":[\"swf\"],\"txt\":[\"txt\"],\"vsd\":[\"vsd\"],\"xmind\":[\"xmind\"],\"zip\":[\"zip\",\"rar\",\"tar\",\"cab\",\"7z\"]}"

                val fileTypeMap: HashMap<String, List<String>> = GsonUtil.fromJson2(json, object : TypeToken<HashMap<String, List<String>>>() {}.type)
                        ?: return R.mipmap.ic_file_type_wu

                val targetKeys = fileTypeMap.filterValues { it.contains(extra) }.toMap()
                return if (targetKeys.size == 1) {
                    val extraIcons = getExtraIconMap()
                    extraIcons[targetKeys.keys.toList()[0]] ?: R.mipmap.ic_file_type_wu
                } else {
                    R.mipmap.ic_file_type_wu
                }
//                val fileTypes = fileTypeMap.flatMap { entry ->
//                    entry.value.map { it to entry.key }
//                }.toMap()
//                val resTag = fileTypes[extra] ?: "wu"
//                var result = mContext.get()?.resources?.getIdentifier("ic_file_type_$resTag",
//                        "mipmap", mContext.get()?.packageName) ?: R.mipmap.ic_file_type_wu
//                if (result > 0) {
//                    fileTypeHolder[extra] = result
//                } else {
//                    result = R.mipmap.ic_file_type_wu
//                }
//                return result
            }
        } else {
            return R.mipmap.ic_file_type_wjj
        }
    }

    private fun getExtraIconMap(): HashMap<String, Int> {
        val map = hashMapOf<String, Int>()
        map["ai"] = R.mipmap.ic_file_type_ai
        map["doc"] = R.mipmap.ic_file_type_doc
        map["dwf"] = R.mipmap.ic_file_type_dwf
        map["dwg"] = R.mipmap.ic_file_type_dwg
        map["excel"] = R.mipmap.ic_file_type_excel
        map["gif"] = R.mipmap.ic_file_type_gif
        map["html"] = R.mipmap.ic_file_type_html
        map["jpg"] = R.mipmap.ic_file_type_jpg
        map["js"] = R.mipmap.ic_file_type_js
        map["mp3"] = R.mipmap.ic_file_type_mp3
        map["mp4"] = R.mipmap.ic_file_type_mp4
        map["pdf"] = R.mipmap.ic_file_type_pdf
        map["ppt"] = R.mipmap.ic_file_type_ppt
        map["psd"] = R.mipmap.ic_file_type_psd
        map["rp"] = R.mipmap.ic_file_type_rp
        map["skp"] = R.mipmap.ic_file_type_skp
        map["stl"] = R.mipmap.ic_file_type_stl
        map["swf"] = R.mipmap.ic_file_type_swf
        map["txt"] = R.mipmap.ic_file_type_txt
        map["vsd"] = R.mipmap.ic_file_type_vsd
        map["xmind"] = R.mipmap.ic_file_type_xmind
        map["zip"] = R.mipmap.ic_file_type_zip
        return map
    }

}