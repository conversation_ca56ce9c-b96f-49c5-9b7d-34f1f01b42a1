package com.joinutech.common.storage

import android.content.Context
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.common.base.isDebug
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.bean.DownloadFileSession
import com.joinutech.ddbeslibrary.bean.TencentSessionBean
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.UploadFileService
import com.joinutech.ddbeslibrary.utils.*
import com.tencent.cos.xml.exception.CosXmlClientException
import com.tencent.cos.xml.exception.CosXmlServiceException
import com.tencent.cos.xml.listener.CosXmlProgressListener
import com.tencent.cos.xml.listener.CosXmlResultListener
import com.tencent.cos.xml.model.CosXmlRequest
import com.tencent.cos.xml.model.CosXmlResult
import com.tencent.cos.xml.model.`object`.GetObjectRequest
import com.tencent.cos.xml.transfer.COSXMLDownloadTask
import com.tencent.cos.xml.transfer.TransferState
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import io.reactivex.FlowableEmitter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import java.io.File
import java.util.*

object FileDownTransferManager : TransListener {

    var isLoaded = false

    fun onLoad(): List<String> {
        FileStorage.showLog("获取团队下载记录信息")
        val result = MMKVUtil.getString("${UserHolder.getUserId()}_down_transfer_company_ids")
        val waitTransOrgList = arrayListOf<String>()
        if (result.isNotBlank() && result.startsWith("[") && result.endsWith("]")) {
            val list = GsonUtil.getList2(result, String::class.java)
            if (!list.isNullOrEmpty()) {
                list.forEach {
                    if (getTransfer(it).onLoad() > 0) {
                        waitTransOrgList.add(it)
                    }
                }
            }
        }
        isLoaded = true
        return waitTransOrgList
    }

    fun onSave() {
        if (transManager.isNotEmpty()) {
            transManager.keys.forEach {
                getTransfer(it).onSave()
            }
            MMKVUtil.saveString("${UserHolder.getUserId()}_down_transfer_company_ids", GsonUtil.toJson(transManager.keys))
        } else {
            MMKVUtil.clearByKey("${UserHolder.getUserId()}_down_transfer_company_ids")
        }
    }

    @JvmField
    @Volatile
    var onTransListener: TransListener? = null

    @JvmField
    @Volatile
    var transManager = hashMapOf<String, CompanyDownloadTransfer>()

    fun getTransOrgList(): List<String> {
        if (transManager.isEmpty()) return arrayListOf()
        return transManager.keys.toList()
    }

    fun addDownLoadFile(companyId: String, file: AddFileBean) {
        getTransfer(companyId).addDownFile(file)
    }

    fun addDownLoadFiles(companyId: String, files: List<AddFileBean>) {
        getTransfer(companyId).addDownFiles(files)
    }

    fun getTransHistory(companyId: String): List<FileTransferInfo> {
        val list = arrayListOf<FileTransferInfo>()

        val processList = getTransfer(companyId).transList
        list.add(FileTransferInfo(1, progress = processList.size))
        list.addAll(processList)

        val successList = getTransfer(companyId).transFinishList
        list.add(FileTransferInfo(2, progress = successList.size))
        list.addAll(successList)
        return list
    }

    private fun getTransfer(companyId: String): CompanyDownloadTransfer {
        if (!transManager.containsKey(companyId) || transManager[companyId] == null) {
            transManager[companyId] = CompanyDownloadTransfer(companyId, this)
        }
        return transManager[companyId]!!
    }

    fun onRetry(companyId: String, fileIndies: List<String>) {
        getTransfer(companyId).onRetry(fileIndies)
    }

    fun onResume(companyId: String, fileIndies: List<String>) {
        getTransfer(companyId).onResume(fileIndies)
    }

    fun onPause(companyId: String, fileIndies: List<String>) {
        getTransfer(companyId).onPause(fileIndies)
    }

    fun onResumeAll() {
        if (transManager.isNotEmpty()) {
            transManager.keys.forEach {
                getTransfer(it).onResumeAll()
            }
        }
    }

    fun onPauseAll() {
        if (transManager.isNotEmpty()) {
            transManager.keys.forEach {
                getTransfer(it).onPauseAll()
            }
        }
    }

    fun onCancel(companyId: String, fileIndies: List<String>) {
        getTransfer(companyId).onCancel(fileIndies)
    }

    /**请求tencent cos session 秘钥相关数据*/
    private fun getDownFileInfo(fileId: String): Flowable<DownloadFileSession> {
        FileStorage.showLog("文件下载-->检查session是否过期（请求秘钥后存储），执行下载文件")
//        val getSession = if (isNewVersionAPI) {
        val getSession = UploadFileService.getPanDownloadSessionV2(fileId)
                .compose(ErrorTransformer.getInstance<DownloadFileSession>())
//        } else {
//            UploadFileService.getPanDownloadSession(fileId)
//                    .compose(ErrorTransformer.getInstance<DownloadFileSession>())
//        }
        return if (FileStorage.isSessionValid()) {
            FileStorage.showLog("秘钥有效，直接上传文件")
            getSession
        } else {
            FileStorage.showLog("请求秘钥后转型保存到 FileStorage.cosSession,执行上传文件")
            FileStorage.sessionRequest()
                    .flatMap {
                        //  更新秘钥后执行上传
                        FileStorage.cosSession = it
                        getSession
                    }.subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
        }
    }

    private fun getService(session: TencentSessionBean?): CosService? {
        if (session == null) return null
        val secretId = session.credentials.tmpSecretId
        val secretKey = session.credentials.tmpSecretKey
        val sessionToken = session.credentials.sessionToken
        val expiredTime: Long = session.expiredTime//临时密钥有效截止时间戳
        //初始化 config
        val serviceConfig = FileStorage.getTencentCloudConfig() ?: return null
        //使用临时密钥初始化QCloudCredentialProvider
        val qCloudCredentialProvider = MyCredentialProvider(secretId, secretKey, sessionToken, expiredTime)
//            not necessary
//            qCloudCredentialProvider.refresh()
        //初始化CosXmlService
        return CosService(BaseApplication.joinuTechContext, serviceConfig, qCloudCredentialProvider)
    }

    /**
     * 单文件下载 带进度回调
     * onSuccess onError String is fileId
     * 根据文件fileId获取下载token，获取token后根据token中bucket 和 uri等信息下载文件
     */
    fun downloadSingleFile(fileBean: FileTransferInfo,
                           onSuccess: (FileTransferInfo) -> Unit, onError: (String) -> Unit,
                           onCreateTask: (TransTaskHolder<GetObjectRequest>) -> Unit,
                           onTransfer: (FileTransferInfo) -> Unit) {

        FileStorage.showLog("下载文件：$fileBean")

        getDownFileInfo(fileBean.fileId).flatMap { downloadSession ->
            FileStorage.showLog("请求秘钥后转型,执行下载文件")
            createDownloadTask(downloadSession, fileBean, onCreateTask, onTransfer)
        }.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                        object : BaseSubscriber<FileTransferInfo>() {
                            override fun onError(ex: ApiException) {
                                FileStorage.showLog("下载文件失败： ${ex.code} - ${ex.message}")
                                onError.invoke(fileBean.index)
                            }

                            override fun onComplete() {
                            }

                            override fun onNext(result: FileTransferInfo?) {
                                FileStorage.showLog("下载文件成功： $result")
                                onSuccess.invoke(result!!)
                            }
                        })
    }

    fun downloadSingleFile2(context: Context, fileBean: FileTransferInfo,
                            onSuccess: (String) -> Unit, onError: (String) -> Unit,
                            onCreateTask: (TransTaskHolder<COSXMLDownloadTask>) -> Unit,
                            onTransfer: (FileTransferInfo) -> Unit) {

        FileStorage.showLog("下载文件：$fileBean")

        getDownFileInfo(fileBean.fileId).flatMap { downloadSession ->
            FileStorage.showLog("请求秘钥后转型,执行下载文件")
            createDownloadTask2(context, downloadSession, fileBean, onCreateTask, onTransfer)
        }.subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe(
                object : BaseSubscriber<String>() {
                    override fun onError(ex: ApiException) {
                        FileStorage.showLog("下载文件失败： ${ex.code} - ${ex.message}")
                        onError.invoke(fileBean.index)
                    }

                    override fun onComplete() {
                    }

                    override fun onNext(result: String?) {
                        FileStorage.showLog("下载文件成功： $result")
                        onSuccess.invoke(result!!)
                    }
                })
    }

    /*上传文件在异步线程中，所以切换到主线程再回调*/
    private fun createDownloadTask(downloadFileSession: DownloadFileSession,
                                   fileBean: FileTransferInfo,
                                   onCreateTask: (TransTaskHolder<GetObjectRequest>) -> Unit,
                                   onTransfer: (FileTransferInfo) -> Unit, type: Int = 0): Flowable<FileTransferInfo> {
        return Flowable.create({ emitter: FlowableEmitter<FileTransferInfo> ->
            createDownloadFileTask(downloadFileSession, type, fileBean,
                    onCreateTask = onCreateTask,
                    onTransfer = {
                        it.index = fileBean.index
                        onTransfer.invoke(it)
                    },
                    onResult = {
                        FileStorage.showLog("下载结果回调1 ${GsonUtil.toJson(it)}")
                        if (it.state == 3) {
                            emitter.onNext(fileBean)
                        } else {
                            emitter.onError(Throwable(fileBean.index))
                        }
                    })
//            FileStorage.showLog("直接返回异常，不处理下载 ${GsonUtil.toJson(fileBean)}")
//            emitter.onError(Throwable(fileBean.index))
        }, BackpressureStrategy.BUFFER)
    }

    private fun createDownloadTask2(context: Context, downloadFileSession: DownloadFileSession,
                                    fileBean: FileTransferInfo,
                                    onCreateTask: (TransTaskHolder<COSXMLDownloadTask>) -> Unit,
                                    onTransfer: (FileTransferInfo) -> Unit): Flowable<String> {
        return Flowable.create({ emitter: FlowableEmitter<String> ->
            createDownloadFileTask2(context, downloadFileSession, fileBean,
                    onCreateTask = onCreateTask,
                    onTransfer = {
                        it.index = fileBean.index
                        onTransfer.invoke(it)
                    },
                    onResult = {
                        FileStorage.showLog("下载结果回调1 ${GsonUtil.toJson(it)}")
                        if (it.state == 3) {
                            emitter.onNext(fileBean.index)
                        } else {
                            emitter.onError(Throwable(fileBean.index))
                        }
                    })
//            FileStorage.showLog("直接返回异常，不处理下载 ${GsonUtil.toJson(fileBean)}")
//            emitter.onError(Throwable(fileBean.index))
        }, BackpressureStrategy.BUFFER)
    }

    /**download task，不能暂停，只能结束，开始*/
    private fun createDownloadFileTask(downloadFileSession: DownloadFileSession,
                                       type: Int, fileBean: FileTransferInfo,
                                       /**创建任务回调*/
                                       onCreateTask: (TransTaskHolder<GetObjectRequest>) -> Unit,
                                       /**进度和状态回调*/
                                       onTransfer: (FileTransferInfo) -> Unit,
                                       /**结果成功和失败回调*/
                                       onResult: (FileTransferInfo) -> Unit) {
        try {
            fileBean.total = downloadFileSession.size

            val fileTotal = fileBean.total
            val fileCompleted = fileBean.completed

            val bucket = if (type == 0 && !downloadFileSession.bucket.isNullOrBlank()) {
                downloadFileSession.bucket
            } else {
                if (type == 11) {
                    FileStorage.cosSession?.perBucket
                } else {
                    FileStorage.tos_pan_bucket
                }
            }

            FileStorage.showLog("使用通用秘钥下载文件 ${FileStorage.cosSession} \n初始化cos服务后执行下载文件")
            val token = FileStorage.cosSession
            getService(token)?.let { service ->
                FileStorage.showLog("下载文件存储桶：${bucket}")
                FileStorage.showLog("下载文件地址：${downloadFileSession.uri}")
                FileStorage.showLog("下载文件保存地址：${fileBean.srcPath}/${fileBean.name}")
                val objectRequest = GetObjectRequest(bucket, downloadFileSession.uri, fileBean.srcPath, fileBean.name)
                if (fileBean.completed > 0) {
                    objectRequest.setRange(fileBean.completed)
                }

                val listener = CosXmlProgressListener { complete, _ ->
                    val completed = fileCompleted + complete
                    if (fileTotal > 0) {
                        val percent = (completed * 100 / fileTotal).toInt()
                        if (percent % 5 == 0) {
                            val fileInfo = FileTransferInfo(total = fileTotal, completed = completed, progress = percent, state = 1)
                            FileStorage.showLog("获取对象进度回调 ${GsonUtil.toJson(fileInfo)}")
                            onTransfer.invoke(fileInfo)
                        }
                    }
                }

                objectRequest.progressListener = listener

                service.getObjectAsync(objectRequest, object : CosXmlResultListener {
                    override fun onSuccess(request: CosXmlRequest?, result: CosXmlResult?) {
                        FileStorage.showLog("get object result is ${GsonUtil.toJson(result)}")
                        if (!File(fileBean.srcPath, fileBean.name).exists()) {
                            FileStorage.showLog("----》》》下载文件不存在！！！")
                        }
                        val fileInfo = FileTransferInfo(state = 3)
                        onResult.invoke(fileInfo)
                    }

                    override fun onFail(request: CosXmlRequest?, exception: CosXmlClientException?, serviceException: CosXmlServiceException?) {
                        val fileInfo = FileTransferInfo(state = -2)
                        onResult.invoke(fileInfo)
                    }
                })

                onCreateTask(TransTaskHolder(fileBean.index, objectRequest))
            } ?: onResult.invoke(FileTransferInfo(state = -2))

        } catch (e: CosXmlClientException) {
            e.printStackTrace()
            fileBean.state = -2
            onResult.invoke(fileBean)
        } catch (e: CosXmlServiceException) {
            e.printStackTrace()
            fileBean.state = -2
            onResult.invoke(fileBean)
        } catch (e: Throwable) {
            e.printStackTrace()
            fileBean.state = -2
            onResult.invoke(fileBean)
        } catch (e: Exception) {
            e.printStackTrace()
            fileBean.state = -2
            onResult.invoke(fileBean)
        }
    }

    /**download task ,task 可以控制下载状态，可以暂停，恢复*/
    private fun createDownloadFileTask2(context: Context, downloadFileSession: DownloadFileSession,
                                        fileBean: FileTransferInfo,
                                        /**创建任务回调*/
                                        onCreateTask: (TransTaskHolder<COSXMLDownloadTask>) -> Unit,
                                        /**进度和状态回调*/
                                        onTransfer: (FileTransferInfo) -> Unit,
                                        /**结果成功和失败回调*/
                                        onResult: (FileTransferInfo) -> Unit) {
        try {
            fileBean.total = downloadFileSession.size

            val bucket = if (downloadFileSession.bucket.isNullOrBlank()) {
                FileStorage.tos_pan_bucket
            } else {
                downloadFileSession.bucket
            }

            fun downLoadHigh(cosService: CosService) {

                val transManager = FileStorage.getTransferManager(cosService)
                FileStorage.showLog("下载文件存储桶：${bucket}")
                FileStorage.showLog("下载文件地址：${downloadFileSession.uri}")
                FileStorage.showLog("下载文件保存地址：${fileBean.srcPath}/${fileBean.name}")
                // 下载对象
                val downTask = transManager.download(
                        context,
                        bucket,
                        downloadFileSession.uri,
                        fileBean.srcPath,
                        fileBean.name)

                downTask.setCosXmlProgressListener { completed, total ->
                    val percent = (completed * 100 / total).toInt()
                    FileStorage.showLog("path:${fileBean.srcPath + File.separator + fileBean.name}, over:$completed, total:$total, per:$percent")
                    fileBean.completed = completed
                    fileBean.progress = percent
                    fileBean.state = 1
                    onTransfer.invoke(fileBean)
                }

                downTask.setCosXmlResultListener(object : CosXmlResultListener {
                    override fun onSuccess(request: CosXmlRequest?, result: CosXmlResult?) {
                        FileStorage.showLog("下载成功")
                        val mutableList = result!!.headers["ETag"]
                        val hash = if (!mutableList.isNullOrEmpty()) {
                            mutableList[0].replace("\"", "")
                        } else {
                            ""
                        }
                        fileBean.hash = hash
                        fileBean.completed = fileBean.total
                        fileBean.progress = 100
                        fileBean.state = 3
                        onResult.invoke(fileBean)
                    }

                    override fun onFail(request: CosXmlRequest?, exception: CosXmlClientException?,
                                        serviceException: CosXmlServiceException?) {
                        FileStorage.showLog("下载失败")
                        fileBean.state = -2
                        onResult.invoke(fileBean)
                    }
                })

                downTask.setTransferStateListener { transState ->
                    FileStorage.showLog("下载状态回调 ${transState.name} :: ${transState.ordinal}")
                    // 文件传输状态回调
                    /**
                     * -3 unknown
                     * -2 failed
                     * -1 cancel
                     * 0 waiting
                     * 1 process
                     * 2 pause
                     * 3 completed
                     */
                    val state = when (transState) {
                        TransferState.CONSTRAINED -> {
                            -3
                        }
                        TransferState.RESUMED_WAITING -> {
                            // resumed,queue fo execution,no start to transfer data
                            0
                        }
                        TransferState.WAITING -> {
                            // in queue, no started
                            0
                        }
                        TransferState.IN_PROGRESS -> {
                            // 处理中
                            1
                        }
                        TransferState.PAUSED -> {
                            // pause manual
                            2
                        }
                        TransferState.COMPLETED -> {
                            // 已完成
                            3
                        }
                        TransferState.CANCELED -> {
                            // cancel transfer data
                            -1
                        }
                        TransferState.FAILED -> {
                            // transfer with exception
                            -2
                        }
                        TransferState.UNKNOWN -> {
                            // unknown state
                            -3
                        }
                    }
                    fileBean.state = state
                    onTransfer.invoke(fileBean)
                }

                onCreateTask.invoke(TransTaskHolder(fileBean.index, downTask))
            }
            getService(FileStorage.cosSession)?.let { downLoadHigh(it) }
                    ?: onResult.invoke(FileTransferInfo(state = -2))
        } catch (e: CosXmlClientException) {
            e.printStackTrace()
            fileBean.state = -2
            onResult.invoke(fileBean)
        } catch (e: CosXmlServiceException) {
            e.printStackTrace()
            fileBean.state = -2
            onResult.invoke(fileBean)
        } catch (e: Throwable) {
            e.printStackTrace()
            fileBean.state = -2
            onResult.invoke(fileBean)
        } catch (e: Exception) {
            e.printStackTrace()
            fileBean.state = -2
            onResult.invoke(fileBean)
        }
    }

    override fun onNoTask(companyId: String) {
        onTransListener?.onNoTask(companyId)
    }

    override fun onTransResult(companyId: String, file: FileTransferInfo) {
        onTransListener?.onTransResult(companyId, file)
    }

    override fun onTransProcess(companyId: String, file: FileTransferInfo) {
        onTransListener?.onTransProcess(companyId, file)
    }

    fun singleFileDownload(type: Int, fileBean: FileTransferInfo,
                           onSuccess: (FileTransferInfo) -> Unit, onError: (String) -> Unit,
                           onCreateTask: (TransTaskHolder<GetObjectRequest>) -> Unit,
                           onTransfer: (FileTransferInfo) -> Unit) {

        FileStorage.showLog("单个文件下载：$fileBean")

        getDownFileInfo(fileBean.fileId).flatMap { downloadSession ->
            FileStorage.showLog("请求秘钥后转型,执行下载文件")
            createDownloadTask(downloadSession, fileBean, onCreateTask, onTransfer, type)
        }.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                        object : BaseSubscriber<FileTransferInfo>() {
                            override fun onError(ex: ApiException) {
                                FileStorage.showLog("下载文件失败： ${ex.code} - ${ex.message}")
                                onError.invoke(fileBean.index)
                            }

                            override fun onComplete() {
                            }

                            override fun onNext(result: FileTransferInfo?) {
                                FileStorage.showLog("下载文件成功： $result")
                                onSuccess.invoke(result!!)
                            }
                        })
    }
}

/**
 * useTask 使用任务上传方式
 * 默认为普通下载方式
 */
class CompanyDownloadTransfer(val companyId: String, val listener: TransListener, private val useTask: Boolean = false) {

    private val fileDir = FileUtil.getFilePath(BaseApplication.joinuTechContext, "download")

    /**
     * 进行中记录
     * 团队文件传输记录
     * 用于记录添加的传输文件信息
     * 成功 失败 暂停 删除都需要更新这个集合
     * */
    val transList = arrayListOf<FileTransferInfo>()

    /**已完成记录*/
    val transFinishList = arrayListOf<FileTransferInfo>()

    /**
     * 团队传输队列 需要上传文件的记录集合
     * key 为在传输记录中的索引
     * value 为文件信息记录，
     * 成功、失败、暂停后更新这个记录信息到传输列表中key对应位置
     * 更新信息时，可以筛选队列中是否存在相同的文件上传信息，如果存在，则置为完成状态
     * 成功的文件要调用绑定parentId接口关联到具体目录中
     * */
    private val transQueue = LinkedList<FileTransferInfo>()

    /**当前下载文件*/
    private var currentDownloadFile: FileTransferInfo? = null

    /**当前下载请求*/
    private var objRequest: GetObjectRequest? = null

    /**当前下载任务*/
    private var downTask: COSXMLDownloadTask? = null

    fun addDownFile(file: AddFileBean) {
        addDownFiles(arrayListOf(file))
    }

    fun addDownFiles(uploadFiles: List<AddFileBean>) {
        translateDownFileData(uploadFiles)
    }

    /**
     * 多文件下载 类型转换，存储缓存相关
     * @param state -1 存储空间不足 0 未上传 1 已上传
     * // TODO: 2020/8/7 14:04 转换下载文件数据类型 本地路径
     *
     */
    private fun translateDownFileData(hashList: List<AddFileBean>) {
        FileStorage.showLog("转换数据类型，构造文件下载数据类型")
        val transList = hashList.map { file ->
            val fileName = if (file.fileName.contains(".")) {
                "cld_" + file.fileId + file.fileName.substring(file.fileName.lastIndexOf("."))
            } else {
                "cld_" + file.fileId + ".png"
            }

            val temp = File(fileDir, fileName)
            if (temp.exists()) {
                temp.delete()
            }

            FileTransferInfo(name = fileName, srcPath = fileDir,
                    cosPath = file.fileUrl, fileId = file.fileId, hash = file.fileHash,
                    total = 0L,
                    startTime = System.currentTimeMillis(),
                    completed = 0L,
                    progress = 0,
                    updateTime = System.currentTimeMillis(),
                    pauseAdd = file.pauseAdd,
                    isDownload = true,
                    state = 0)
        }.toMutableList() as ArrayList
        addDownloadFiles(transList)
        updateQueue()
    }

    private fun addDownloadFiles(files: List<FileTransferInfo>): Int {
        showList = true
        val time = System.currentTimeMillis()
        var index = 0
        files.forEach { file ->
            file.index = "${time}_${index++}"
            file.state = if (file.pauseAdd) 2 else 0
            transList.add(file)
            if (!file.pauseAdd) {
                transQueue.addLast(file)
            }
        }
        return transQueue.size
    }

    private var showList = false

    /**触发队列更新*/
    private fun updateQueue() {
        FileStorage.showLog("更新下载队列。。。")
        if (downTask == null && currentDownloadFile == null) { // 当前下载任务为空，在队列中获取一个下载任务
            if (transQueue.isNotEmpty()) {
                FileStorage.showLog("下载队列存在待处理任务")
                val file = transQueue.pop()
                if (file.progress == 0) {
                    file.startTime = System.currentTimeMillis()
                    if (file.index.isNotBlank()) {
                        transList.find { it.index == file.index }?.let {
                            transList[transList.indexOf(it)] = file
                        }
                    }
                }
                this.currentDownloadFile = file
                if (useTask) {
                    transferDownload2(currentDownloadFile!!)
                } else {
                    transferDownload(currentDownloadFile!!)
                }
//                downloadFileWithOKHttp2(file)
                if (isDebug && showList) { // TODO: 2020/8/5 17:24 测试代码，发版清除
                    showList = false
                    ARouter.getInstance().build("/clouddoc/trans_list")
                            .withInt("targetIndex", 0)
                            .navigation()
                }
            } else {
                FileStorage.showLog("下载队列不存在待处理任务")
                onSave()
                listener.onNoTask(companyId)
            }
        } else {
            FileStorage.showLog("下载任务正在进行中")
        }
    }

    /**恢复下载后，会重新打开文件下载，重新根据fileid获取token信息后再执行上传下载*/
    private fun transferDownload(file: FileTransferInfo) {
        FileStorage.showLog("处理文件传输 ${GsonUtil.toJson(file)}")
        FileDownTransferManager.downloadSingleFile(
                file,
                onSuccess = {
                    FileStorage.showLog("进度结果回调线程：${Thread.currentThread().id}")
                    if (it.index == currentDownloadFile?.index) {
                        currentDownloadFile?.let { source ->
                            if (file.index.isNotBlank()) {
                                source.progress = 100
                                source.updateTime = System.currentTimeMillis()
                                source.state = 3
                                FileStorage.showLog("文件下载完成回调, 更新传输数据集合")
                                onResult(source, true)
                            }
                        }
                    }
                },
                onError = {
                    FileStorage.showLog("进度结果回调线程：${Thread.currentThread().id}")
                    if (it == currentDownloadFile?.index) {
                        currentDownloadFile?.let { source ->
                            if (file.index.isNotBlank()) {
                                source.progress = 0
                                source.updateTime = System.currentTimeMillis()
                                if (source.state != 2) {// 当前文件非暂停情况处理
                                    source.state = -2
                                }
                                FileStorage.showLog("文件下载失败回调, 更新传输数据集合")
                                onResult(source)
                            }
                        }
                    }
                },
                onCreateTask = {
                    if (it.fileIndex == currentDownloadFile?.index) {
                        objRequest = it.task
                    }
                },
                onTransfer = {
                    FileStorage.showLog("文件下载进度回调线程：${Thread.currentThread().id} :: ${it.state}::${it.progress}::${it.completed}/${it.total}")
                    if (it.index == currentDownloadFile?.index) {
                        FileStorage.showLog("文件下载进度回调并更新传输数据集合")
                        currentDownloadFile?.total = it.total
                        currentDownloadFile?.completed = it.completed
                        currentDownloadFile?.progress = it.progress
                        currentDownloadFile?.state = it.state
                        updateSource(currentDownloadFile)
                    }
                })
    }

    private fun transferDownload2(file: FileTransferInfo) {
        FileStorage.showLog("处理文件传输 ${GsonUtil.toJson(file)}")
        FileDownTransferManager.downloadSingleFile2(BaseApplication.joinuTechContext,
                file,
                onSuccess = {
                    FileStorage.showLog("进度结果回调线程：${Thread.currentThread().id}")
                    if (it == currentDownloadFile?.index) {
                        currentDownloadFile?.let { source ->
                            if (file.index.isNotBlank()) {
                                source.progress = 100
                                source.updateTime = System.currentTimeMillis()
                                source.state = 3
                                FileStorage.showLog("文件下载完成回调, 更新传输数据集合")
                                onResult(source, true)
                            }
                        }
                    }
                },
                onError = {
                    FileStorage.showLog("进度结果回调线程：${Thread.currentThread().id}")
                    if (it == currentDownloadFile?.index) {
                        currentDownloadFile?.let { source ->
                            if (file.index.isNotBlank()) {
                                source.progress = 0
                                source.updateTime = System.currentTimeMillis()
                                if (source.state != 2) {// 当前文件非暂停情况处理
                                    source.state = -2
                                }
                                FileStorage.showLog("文件下载失败回调, 更新传输数据集合")
                                onResult(source)
                            }
                        }
                    }
                },
                onCreateTask = {
                    if (it.fileIndex == currentDownloadFile?.index) {
                        downTask = it.task
                    }
                },
                onTransfer = {
                    FileStorage.showLog("文件下载进度回调线程：${Thread.currentThread().id} :: ${it.state}::${it.progress}::${it.completed}/${it.total}")
                    if (it.index == currentDownloadFile?.index) {
                        FileStorage.showLog("文件下载进度回调并更新传输数据集合")
                        currentDownloadFile?.total = it.total
                        currentDownloadFile?.completed = it.completed
                        currentDownloadFile?.progress = it.progress
                        currentDownloadFile?.state = it.state
                        updateSource(currentDownloadFile)
                    }
                })
    }

    /**下载中更新数据源，需要UI现实的位置需要设置监听器接收信息*/
    private fun updateSource(source: FileTransferInfo?) {
        FileStorage.showLog("更新文件下载源数据")
        if (source != null && source.index.isNotBlank()) {
            val index = transList.indexOfFirst { it.index == source.index }
            if (index >= 0) {
                transList[index] = source
            }
            listener.onTransProcess(companyId, source)
        }
    }

    private fun notifyDownload(source: FileTransferInfo) {
        EventBusUtils.sendEvent(EventBusEvent("file_download_transfer_success", source.index))
    }

    private fun onResult(downLoadTarget: FileTransferInfo, success: Boolean = false) {
        FileStorage.showLog("下载结果回调2 ${GsonUtil.toJson(downLoadTarget)}")
        if (success) {
            if (downLoadTarget.index.isNotBlank()) {
                val target = transList.find { it.index == downLoadTarget.index }
                if (target != null) {
                    transList.remove(target)
                }
            }
            transFinishList.add(downLoadTarget)
            notifyDownload(downLoadTarget)
            onSaveComplete()
            CommonUtils.addToGallery(BaseApplication.joinuTechContext, downLoadTarget.srcPath, downLoadTarget.name)
        } else {
            if (downLoadTarget.index.isNotBlank()) {
                val target = transList.find { it.index == downLoadTarget.index }
                if (target != null) {
                    transList[transList.indexOf(target)] = downLoadTarget
                }
            }
        }

        FileStorage.showLog("重置当前下载信息，回调结果给监听器，触发队列下载")
//        objRequest = null
        downTask = null
        currentDownloadFile = null
        /**结果数据回调*/
        listener.onTransResult(companyId, downLoadTarget)
        updateQueue()
    }

    fun onRetry(fileIndies: List<String>) {
        FileStorage.showLog("重试下载")
        transList.forEach {
            if (it.index in fileIndies) {
                it.state = 0
                transQueue.addLast(it)
            }
        }
        if (currentDownloadFile == null) {
            FileStorage.showLog("当前没有文件在下载")
            updateQueue()
        } else {
            if (currentDownloadFile?.index in fileIndies) {
                FileStorage.showLog("重试当前文件下载")
                currentDownloadFile!!.completed = 0
                if (useTask) {
                    transferDownload2(currentDownloadFile!!)
                } else {
                    transferDownload(currentDownloadFile!!)
                }
            } else {
                FileStorage.showLog("加入队列后等待下载...")
            }
        }
    }

    /**恢复时，是重新开始下载，没有用断点续传*/
    fun onResume(fileIndies: List<String>) {
        FileStorage.showLog("恢复下载")
        if (transList.isNotEmpty()) {
            transList.forEach {
                if (it.index in fileIndies) {
                    it.state = 0
                    transQueue.addLast(it)
                }
            }
        }
        if (useTask) {
            if (downTask != null) {
                // todo after request session then try resume
                downTask?.resume()
            }
        } else {
            if (currentDownloadFile != null && currentDownloadFile!!.index in fileIndies) {
                transferDownload(currentDownloadFile!!)
            }
        }
        updateQueue()
    }

    fun onResumeAll() {
        FileStorage.showLog("全部恢复下载")
        transQueue.clear()
        if (transList.isNotEmpty()) {
            transList.forEach {
                if (it.state == 2) {
                    it.state = 0
                    transQueue.addLast(it)
                }
            }
        }
        if (useTask) {
            if (downTask != null) {
                // todo after request session then try resume
                downTask?.resume()
            }
        } else {
            if (currentDownloadFile != null) {
                transferDownload(currentDownloadFile!!)
            }
        }
        updateQueue()
    }

    fun onPause(fileIndies: List<String>) {
        FileStorage.showLog("暂停下载")
        if (transList.isNotEmpty()) {
            transList.forEach {
                if (it.index in fileIndies) {
                    it.state = 2
                }
            }
        }
        val queList = transQueue.filter { it.index in fileIndies }
        if (queList.isNotEmpty()) {
            queList.forEach {
                transQueue.remove(it)
            }
        }

        if (useTask) {
            if (downTask != null) {
                downTask?.pause()
            }
        } else {
            if (currentDownloadFile != null && currentDownloadFile!!.index in fileIndies && objRequest != null) {
                objRequest?.httpTask?.cancel()
                objRequest = null
            }
        }
        updateQueue()
    }

    fun onPauseAll() {
        FileStorage.showLog("全部暂停下载")
        transQueue.clear()
        if (transList.isNotEmpty()) {
            transList.forEach {
                if (it.state in 0..1) {
                    it.state = 2
                }
            }
        }
        if (useTask) {
            if (downTask != null) {
                downTask?.pause()
            }
        } else {
            if (currentDownloadFile != null && objRequest != null) {
                objRequest?.httpTask?.cancel()
                objRequest = null
            }
        }
        updateQueue()
        onSave()
    }

    fun onCancel(fileIndies: List<String>) {
        FileStorage.showLog("取消下载")
        val cancelList = transList.filter { it.index in fileIndies }
        cancelList.forEach {
            transList.remove(it)
        }
        val cancelFinishList = transFinishList.filter { it.index in fileIndies }
        cancelFinishList.forEach {
            transFinishList.remove(it)
        }
        val cancelQueueList = transQueue.filter { it.index in fileIndies }
        cancelQueueList.forEach {
            transQueue.remove(it)
        }
        if (currentDownloadFile?.index in fileIndies) {
            downTask?.cancel()
            downTask = null
            currentDownloadFile = null
//            objRequest?.httpTask?.cancel()
//            objRequest = null
            updateQueue()
        }
    }

    fun onLoad(): Int {
        FileStorage.showLog("获取团队${companyId}下载记录信息")
        val successList = MMKVUtil.getString("${companyId}_download_transfer_success")
        if (successList.isNotBlank() && successList.startsWith("[") && successList.endsWith("]")) {
            val result = GsonUtil.getList2(successList, FileTransferInfo::class.java)
            if (!result.isNullOrEmpty()) {
                transFinishList.addAll(result)
            }
        }
        val processList = MMKVUtil.getString("${companyId}_download_transfer_process")
        if (processList.isNotBlank() && processList.startsWith("[") && processList.endsWith("]")) {
            val result = GsonUtil.getList2(processList, FileTransferInfo::class.java)
            if (!result.isNullOrEmpty()) {
                return addDownloadFiles(result)
            }
        }
        return 0
    }

    fun onSave() {
        onSaveTransfer()
        onSaveComplete()
    }

    private fun onSaveTransfer() {
        if (transList.isNotEmpty()) {
            MMKVUtil.saveString("${companyId}_download_transfer_process", GsonUtil.toJson(transList))
        } else {
            MMKVUtil.clearByKey("${companyId}_download_transfer_process")
        }
    }

    private fun onSaveComplete() {
        if (transFinishList.isNotEmpty()) {
            MMKVUtil.saveString("${companyId}_download_transfer_success", GsonUtil.toJson(transFinishList))
        } else {
            MMKVUtil.clearByKey("${companyId}_download_transfer_success")
        }
    }
}