package com.joinutech.common.storage

interface TransListener {
    fun onNoTask(companyId: String)
    fun onTransResult(companyId: String, file: FileTransferInfo)
    fun onTransProcess(companyId: String, file: FileTransferInfo)
}

data class FileTransferInfo(
        /**
         * 数据类型
         * 0：文件数据项
         * 1：进行中 分组数据
         * 2：已完成 分组数据
         */
        val type: Int = 0,
        /**文件关联目录*/
        val parentId: String = "",
        /**文件名*/
        val name: String = "",
        /**文件本地路径*/
        val srcPath: String = "",
        /**文件存储id*/
        var fileId: String = "",
        /**文件hash*/
        var hash: String = "",
        /**文件总长度*/
        var total: Long = 0L,
        /**文件上传秘钥中 bucket参数*/
        var bucket: String = "",
        /**媒体文件类型*/
        var mimeType: String = "image/jpeg",
        //  --------------------传输相关会变动数据-----------------
        /**文件在集合中索引标记*/
        var index: String = "",
        /**开始时间 开始上传*/
        var startTime: Long = 0L,
        /**文件远程路径*/
        var cosPath: String = "",
        /**传输完成数据量*/
        var completed: Long = 0L,
        /**传输进度，百分比 过滤类型时为文件数*/
        var progress: Int = 0,
        /**更新时间 状态更新时间*/
        var updateTime: Long = 0L,
        /**状态
         * -4 空间不足
         * -3 unknown
         * -2 failed
         * -1 cancel
         * 0 waiting
         * 1 process
         * 2 pause
         * 3 completed
         * */
        var state: Int = 0,
        /**添加后是否立即执行*/
        val pauseAdd: Boolean = true,
        /**是否为下载任务*/
        val isDownload: Boolean = false,
        /**是否选中*/
        var isSelect: Boolean = false)

class TransTaskHolder<T>(val fileIndex: String, val task: T)