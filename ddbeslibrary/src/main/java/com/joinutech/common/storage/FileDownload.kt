package com.joinutech.common.storage


//class FileDownload {
//
//    private val threads = hashMapOf<String, Thread>()
//
//    fun downloadFileWithRetrofit(companyId: String, fileInfo: FileTransferInfo, listener: TransListener) {
//        val file = File(fileInfo.srcPath, fileInfo.name)
//        if (!file.parentFile.exists()) {
//            file.parentFile.mkdirs()
//        }
//        val call = DdbesApiUtil.getDownLoadService().downloadFileCall(fileInfo.cosPath)
//        call.enqueue(object : retrofit2.Callback<ResponseBody> {
//
//            override fun onResponse(call: retrofit2.Call<ResponseBody>, response: Response<ResponseBody>) {
//                val thread = Thread {
//                    saveFileToStorage(file, response, listener)
//                }
//                threads[fileInfo.index] = thread
//                thread.start()
//            }
//
//            override fun onFailure(call: retrofit2.Call<ResponseBody>, t: Throwable) {
//                fileInfo.state = -2
//                listener.onTransResult(companyId, fileInfo)
//            }
//        })
//    }
//
//    private fun saveFileToStorage(file: File, response: Response<ResponseBody>, listener: TransListener) {
//
//    }
//}

interface DownloadListener<T> {
    fun init(data: T)
    fun onStart(tag: String)
    fun onProgress(complete: Long, total: Long, percent: Int)
    fun onSuccess(tag: String)
    fun onFailed(tag: String)
}