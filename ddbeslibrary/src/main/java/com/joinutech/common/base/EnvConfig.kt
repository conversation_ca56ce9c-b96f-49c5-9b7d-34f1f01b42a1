package com.joinutech.common.base

import com.joinutech.ddbeslibrary.BuildConfig
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.MMKVUtil
import com.marktoo.lib.cachedweb.LogUtil

const val tokenKey = "Authorization"
const val DEFAULT_TOKEN = "Basic YXBwOmFwcA=="

const val REQUEST_TIMEOUT_READ = 30L
const val REQUEST_TIMEOUT_CONNECT = 30L
const val REQUEST_TIMEOUT_WRITE = 30L

//切换环境，发版修改位置1
//var local = ServerEnv.PRO   // 线上环境
var local = ServerEnv.DEV  // 开发环境

fun resetEnv() {
//    val envIndex = MMKVUtil.getInt("env_index") ?: -1
//    if (envIndex < 0){
//    }else{
//        // 根据缓存进行设置
//        local = ServerEnv.values().get(envIndex)
//    }
    version_mode = local
//    loadEnv()
}

var version_mode: ServerEnv = local
    set(value) {
        LogUtil.showLog("设置当前环境变量  ${GsonUtil.toJson(value)}")
        BASE_URL = value.server
        WEB_URL_HOST = value.web
        IM_SERVER = value.im_host
        IM_OFFLINE_SERVER = value.im_offline_host
        LogUtil.debug = value != ServerEnv.PRO
        field = value
    }

/**开发模式判定*/
var isDebug = BuildConfig.DEBUG || version_mode != ServerEnv.PRO //&& version_mode != ServerEnv.PRE

/**网页调试，原生拦截输出log*/
var isWebDebug = false

// TODO: 2021/5/13 联系方式获取到再显示，班次中日期选择有问题，当日9：00-当日8:00情况处理
var isFuture = true//如果为false，则考勤和审批都是使用旧版
var isAttendance = false//false是使用旧版，true是使用新版，前提是isFuture = true
var isApproval = true//false是使用旧版，true是使用新版，前提是isFuture = true

/***新需求2标记位置 排班考勤组相关 排班考勤组view控制*/
var isFuture2 = isFuture && isAttendance//切换排班

var BASE_URL: String = version_mode.server

var WEB_URL_HOST: String = version_mode.web

var IM_SERVER: String = version_mode.im_host

var IM_OFFLINE_SERVER: String = version_mode.im_offline_host

var MEETING_SOCKET_URL: String = version_mode.meetingSocketUrl

//切换开发(0)、测试(1)、正式(2)环境使用 统一网关(3)
enum class ServerEnv(
    var tokenServer: String, var server: String, var web: String,
    var im_host: String, var im_offline_host: String, var note: String = "",

    var meetingSocketUrl: String = "",
) {
    PRO(
        tokenServer = "https://passport.ddbes.com/",//刷新token，退出登录
        server = "https://api.ddbes.com/",//接口域名
        web = "https://mobile.ddbes.com",//通用web页
//        im_host = "https://router.im.ddbes.com/",// 2024.5.13 停用
        im_host = "https://router-im.ddbes.com/",//
//        im_offline_host = "https://offline.im.ddbes.com/", // 2024.5.13 停用
        im_offline_host = "https://offline-im.ddbes.com/",
        note = "线上环境",
        meetingSocketUrl = "wss://meeting.ddbes.com/"
    ),

    DEV(
        // 域名
//        tokenServer = "http://passport.joinu.ltd/",
//        server = "http://api.joinu.ltd/",
//        web = "http://mobile.inddbes.com",
//        im_host = "http://router-im.joinu.ltd/",
//        im_offline_host = "http://offline-im.joinu.ltd/",
//        note = "内网 开发环境 域名",
//        meetingSocketUrl = "ws://*********:8165/",
        // 域名

        tokenServer = "http://*********:9098/",
        server = "http://*********:8768/",
        web = "http://mobile.inddbes.com",
        im_host = "http://*********:8078/",
        im_offline_host = "http://*********:8089/",
        note = "内网 开发环境 域名",
        meetingSocketUrl = "ws://*********:8165/"
    ),

    CUSTOM(
        tokenServer = "http://auth.test.ddbes.com/",
        server = "http://api.test.ddbes.com/",
        web = "http://************:8080",
        im_host = "http://test.router.ddbes.com/",
        im_offline_host = "http://test.offline.ddbes.com/",
        note = "手动自定义环境",
        meetingSocketUrl = "ws://**********:8165/"
    ),

    // 4.0 专用
    FAST(
//        tokenServer = "http://fastauth.joinu.ltd/",
        tokenServer = "http://*********:9098/",
//        server = "http://fastapi.joinu.ltd/",   // api
        server = "http://*********:8768/",
        web = "http://mobile.inddbes.com",
//        im_host = "http://fastim.joinu.ltd/",
        im_host = "http://*********:8088/",
//        im_offline_host = "http://fastoffline.joinu.ltd/",
        im_offline_host = "http://*********:8089/",
        note = "内网 fast环境 域名",
        meetingSocketUrl = "ws://**********:8165/"
    ),

    /**外网测试环境1*/
    TEST1(
        tokenServer = "https://auth.test.ddbes.com/",//刷新token，退出登录
        server = "https://api.test.ddbes.com/",//接口域名
        web = "http://test.mobile.ddbes.com",//通用web页
//        web = "http://************:8086",//通用web页//调试云盘
        im_host = "http://test.router.ddbes.com:8080/",//imw
        im_offline_host = "http://test.offline.ddbes.com:8080/",//im
        note = "外网测试环境1",
        meetingSocketUrl = "ws://**********:8165/"
    ),

    /**正式环境 带log*/
    PRE(
        tokenServer = "https://passport.ddbes.com/",
        server = "https://api.ddbes.com/",//接口域名
//            web = "http://************:9000",
        web = "https://mobile.ddbes.com",//通用web页
//            web = "http://************:8080",
        im_host = "https://router.im.ddbes.com/",//im
        im_offline_host = "https://offline.im.ddbes.com/",//im
        note = "预发布环境（和线上一致，可以看日志）",
        meetingSocketUrl = "ws://**********:8165/"
    ),

}