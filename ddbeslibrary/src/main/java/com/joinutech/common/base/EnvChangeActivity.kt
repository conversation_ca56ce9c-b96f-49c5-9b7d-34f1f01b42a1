package com.joinutech.common.base

import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.widget.EditText
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.common.adapter.MyAdapter
import com.joinutech.common.adapter.support.ListItemDecoration
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.base.SimpleBaseActivity
import com.joinutech.ddbeslibrary.databinding.CommonListLayoutBinding
import com.joinutech.ddbeslibrary.request.RetrofitClient
import com.joinutech.ddbeslibrary.utils.*

//手动切换环境页面
@Route(path = ENV_CHANGE)
class EnvChangeActivity(override val contentViewResId: Int = R.layout.common_list_layout) :
    SimpleBaseActivity<CommonListLayoutBinding>() {

    lateinit var rvList: RecyclerView
    lateinit var adapter: MyAdapter<ServerEnv>
    lateinit var list: MutableList<ServerEnv>

    override fun initView() {
        setPageTitle("切换接口环境")
        list = ServerEnv.values().toMutableList()
        list.find { it.name.equals("CUSTOM", true) }?.let { newEnv ->
            newEnv.tokenServer = MMKVUtil.getString("env_config_token", newEnv.tokenServer)
            newEnv.server = MMKVUtil.getString("env_config_server", newEnv.server)
            newEnv.web = MMKVUtil.getString("env_config_web", newEnv.web)
            newEnv.im_host = MMKVUtil.getString("env_config_im", newEnv.im_host)
            newEnv.im_offline_host = MMKVUtil.getString("env_config_im_off", newEnv.im_offline_host)
        }

//        list = mutableListOf(ServerEnv.DEVELOP, ServerEnv.PRE, ServerEnv.PRO)
        rvList = findViewById(R.id.rv_list)
//        list = ServerEnv.values()
        adapter = MyAdapter(this,
            R.layout.item_env_layout, list,
            onBindItem = { _: Int, serverEnv: ServerEnv, view: View ->
                if (version_mode == serverEnv) {
                    view.findViewById<View>(R.id.name_note_tv).setBackgroundColor(Color.GREEN)
                } else {
                    view.findViewById<View>(R.id.name_note_tv).setBackgroundColor(Color.RED)
                }
                XUtil.setText(
                    view,
                    R.id.name_note_tv,
                    if (StringUtils.isNotBlankAndEmpty(serverEnv.note)) serverEnv.note else serverEnv.name
                )
                XUtil.setText(view, R.id.name_token_tv, "tokenServer = ")
                XUtil.setText(view, R.id.content_token_tv, serverEnv.tokenServer)

                XUtil.setText(view, R.id.name_server_tv, "server = ")
                XUtil.setText(view, R.id.content_server_tv, serverEnv.server)

                XUtil.setText(view, R.id.name_web_tv, "web = ")
                XUtil.setText(view, R.id.content_web_tv, serverEnv.web)

                XUtil.setText(view, R.id.name_im_host_tv, "im_host = ")
                XUtil.setText(view, R.id.content_im_host_tv, serverEnv.im_host)

                XUtil.setText(view, R.id.name_im_offline_tv, "im_offline_host = ")
                XUtil.setText(view, R.id.content_im_offline_tv, serverEnv.im_offline_host)
            },
            onItemClick = { _: Int, serverEnv: ServerEnv, view: View ->
                showTip(serverEnv)
            }
        )
        rvList.layoutManager = LinearLayoutManager(this)
        rvList.addItemDecoration(ListItemDecoration(DeviceUtil.dip2px(this, 10f)))
        rvList.adapter = adapter
//        layout_empty_layout.visibility = View.GONE
    }

    override fun inflateViewBinding(layoutInflater: LayoutInflater): CommonListLayoutBinding {
        return CommonListLayoutBinding.inflate(layoutInflater)
    }

    private fun showTip(newEnv: ServerEnv) {
        val content = "您确认要切换到 ${newEnv.name} 环境吗？"
        val startIndex = content.indexOf(newEnv.name)
        val endIndex = startIndex + newEnv.name.length
        val spannableString = SpannableString(content)
        val colorSpan = ForegroundColorSpan(Color.parseColor("#1E87F0"))
        spannableString.setSpan(colorSpan, startIndex, endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        if (newEnv == ServerEnv.CUSTOM) {
            val dialog = BaseCenterDialogHelper(
                this,
                layoutId = R.layout.dialog_env_edit,
                onBindView = { dialogView ->
                    dialogView.findViewById<EditText>(R.id.content_token_et)?.let {
                        it.setText(newEnv.tokenServer)
                        it.addTextChangedListener(object : TextWatcher {
                            override fun afterTextChanged(s: Editable?) {
                            }

                            override fun beforeTextChanged(
                                s: CharSequence?,
                                start: Int,
                                count: Int,
                                after: Int
                            ) {
                            }

                            override fun onTextChanged(
                                s: CharSequence?,
                                start: Int,
                                before: Int,
                                count: Int
                            ) {
                                newEnv.tokenServer = s?.toString() ?: ""
                            }
                        })
                    }
                    dialogView.findViewById<EditText>(R.id.content_server_et)?.let {
                        it.setText(newEnv.server)
                        it.addTextChangedListener(object : TextWatcher {
                            override fun afterTextChanged(s: Editable?) {
                            }

                            override fun beforeTextChanged(
                                s: CharSequence?,
                                start: Int,
                                count: Int,
                                after: Int
                            ) {
                            }

                            override fun onTextChanged(
                                s: CharSequence?,
                                start: Int,
                                before: Int,
                                count: Int
                            ) {
                                newEnv.server = s?.toString() ?: ""
                            }
                        })
                    }
                    dialogView.findViewById<EditText>(R.id.content_web_et)?.let {
                        it.setText(newEnv.web)
                        it.addTextChangedListener(object : TextWatcher {
                            override fun afterTextChanged(s: Editable?) {
                            }

                            override fun beforeTextChanged(
                                s: CharSequence?,
                                start: Int,
                                count: Int,
                                after: Int
                            ) {
                            }

                            override fun onTextChanged(
                                s: CharSequence?,
                                start: Int,
                                before: Int,
                                count: Int
                            ) {
                                newEnv.web = s?.toString() ?: ""
                            }
                        })
                    }
                    dialogView.findViewById<EditText>(R.id.content_im_host_et)?.let {
                        it.setText(newEnv.im_host)
                        it.addTextChangedListener(object : TextWatcher {
                            override fun afterTextChanged(s: Editable?) {
                            }

                            override fun beforeTextChanged(
                                s: CharSequence?,
                                start: Int,
                                count: Int,
                                after: Int
                            ) {
                            }

                            override fun onTextChanged(
                                s: CharSequence?,
                                start: Int,
                                before: Int,
                                count: Int
                            ) {
                                newEnv.im_host = s?.toString() ?: ""
                            }
                        })
                    }
                    dialogView.findViewById<EditText>(R.id.content_im_offline_et)?.let {
                        it.setText(newEnv.im_offline_host)
                        it.addTextChangedListener(object : TextWatcher {
                            override fun afterTextChanged(s: Editable?) {
                            }

                            override fun beforeTextChanged(
                                s: CharSequence?,
                                start: Int,
                                count: Int,
                                after: Int
                            ) {
                            }

                            override fun onTextChanged(
                                s: CharSequence?,
                                start: Int,
                                before: Int,
                                count: Int
                            ) {
                                newEnv.im_offline_host = s?.toString() ?: ""
                            }
                        })
                    }
                },
                onConfirm = {
                    local = newEnv
                    version_mode = local
                    MMKVUtil.saveString("env", local.name)
                    MMKVUtil.saveString("env_config", GsonUtil.toJson(local))

                    MMKVUtil.saveString("env_config_token", newEnv.tokenServer)
                    MMKVUtil.saveString("env_config_server", newEnv.server)
                    MMKVUtil.saveString("env_config_web", newEnv.web)
                    MMKVUtil.saveString("env_config_im", newEnv.im_host)
                    MMKVUtil.saveString("env_config_im_off", newEnv.im_offline_host)
                    RetrofitClient.single_intance.mRetrofit = null
                    (ARouter.getInstance().build(RouteProvider.IM_PROVIDER)
                        .navigation() as RouteServiceProvider)
                        .openPage("clearRetrofitClient", Bundle())
//                    DownLoadRetrofitClient.single_intance.mRetrofit = null
                    onUserLogout()
                }, onCancel = {

                })
            dialog.show()
        } else {
            val helper = object : CenterDialogHelper(
                this,
                onConfirm = {
                    if(version_mode!=newEnv){
                        local = newEnv
                        version_mode = local
                        MMKVUtil.saveString("env", local.name)
                        MMKVUtil.saveString("env_config", GsonUtil.toJson(local))
                        RetrofitClient.single_intance.mRetrofit = null
                        (ARouter.getInstance().build(RouteProvider.IM_PROVIDER)
                            .navigation() as RouteServiceProvider)
                            .openPage("clearRetrofitClient", Bundle())
//                    DownLoadRetrofitClient.single_intance.mRetrofit = null
                        onUserLogout()
                    }
                },
                onCancel = {
                }) {
                override fun bindView(dialogView: View) {
                    super.bindView(dialogView)
                    if(version_mode!=newEnv){
                        dialogView.findViewById<TextView>(R.id.tv_content).text =
                            "$spannableString \n $spannableString \n $spannableString\n$spannableString\n"
                        dialogView.findViewById<View>(R.id.tv_hint).visibility = View.GONE
                    }else{
                        dialogView.findViewById<TextView>(R.id.tv_content).text =
                            "绿色标记的为当前环境"
                        dialogView.findViewById<View>(R.id.tv_hint).visibility = View.GONE
                    }

                }
            }
            helper.initView()
            helper.onConfig(DialogConfig(useDefault = true))
            helper.show()
        }
    }
}
