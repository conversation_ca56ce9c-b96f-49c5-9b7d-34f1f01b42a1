package com.joinutech.common.base

import android.content.Context
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.BuildConfig
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.utils.ConsValue
import com.joinutech.ddbeslibrary.utils.MMKVUtil
import com.marktoo.lib.cachedweb.LogUtil

/**
 * @className: LinkBuilder
 * @desc: 链接集中控制类
 * @author: marktoo
 * @date: 2020-05-19 18:46
 * @company: Copyright (C),2017- 2020
 * @leader: self
 */
class LinkBuilder private constructor(private val context: Context?,
                                      private val domain: String,
                                      private val token: String,
                                      private val platform: String = "1",
                                      private val version: String = BuildConfig.VERSION_NAME,
                                      private val appVersion: String = BuildConfig.innerCode,
                                      private val statusBarHeight: Int = 0) {

    companion object {
        fun generate(context: Context? = null,
                     domain: String = WEB_URL_HOST,
                     token: String = UserHolder.getAccessToken(),
                     statusBarHeight: Int = 0): LinkBuilder {
            val height = if (statusBarHeight == 0) {
                ConsValue.getStatusBarHeight(BaseApplication.joinuTechContext)
            } else {
                statusBarHeight
            }
            return LinkBuilder(context, domain, token, statusBarHeight = height)
        }
    }

    private fun String.appendNormalTag(): String {
//        return this.plus("token=$token&platform=$platform&version=$version&appVersion=$appVersion&height=$statusBarHeight")
        return this.plus("platform=$platform&version=$version&appVersion=$appVersion&height=$statusBarHeight")
    }

    //全部审批，我发起的，我审批的，抄送我的，审批详情
    private fun String.appendApproveTag(): String {
       return this.plus("platform=$platform&version=$version&height=$statusBarHeight")
   }


    private fun String.appendNormalTag2(): String {
        return this.plus("platform=$platform&version=$version&appVersion=$appVersion&height=$statusBarHeight")
    }

    /**公告详情web地址*/   // http://oss.joinu.ltd:9000/ddbes-h5/notice.html#/
    fun buildNoticeDetailUrl(noticeId: String, comId: String, isEdit: String , userId : String , token: String? = null): String {
        if (local == ServerEnv.DEV){
//            return "http://oss.joinu.ltd:9000".plus("/ddbes-h5").plus(
            return approveDomain.plus("/ddbes-h5").plus(
                context?.resources?.getString(R.string.notice_detail, noticeId, comId, isEdit))
                .plus("&userId=${userId}")
                .plus("&token=${token}")
                .plus("&").appendNormalTag()
        }

        return domain.plus("/h5").plus(
                context?.resources?.getString(R.string.notice_detail, noticeId, comId, isEdit))
                .plus("&userId=${userId}")
                .plus("&token=${token}")
                .plus("&").appendNormalTag()
    }

    /**公告发布web地址*/
    fun buildPublishUrl(comId: String , userId: String , token: String): String {
        if (local == ServerEnv.DEV){
//            return "http://oss.joinu.ltd:9000".plus("/ddbes-h5").plus(
            return approveDomain.plus("/ddbes-h5").plus(
                context?.resources?.getString(R.string.notice_publish, comId))
                .plus("&userId=${userId}")
                .plus("&token=${token}")
                .plus("&").appendNormalTag()
        }

        return domain.plus("/h5").plus(
                context?.resources?.getString(R.string.notice_publish, comId))
                .plus("&userId=${userId}")
                .plus("&token=${token}")
                .plus("&").appendNormalTag()
    }

    /**云文档web页面地址*/
    fun buildCloudDocUrl(): String {
        return domain.plus("/pan/")
                .plus("index.html")
                .plus("#/?token=${UserHolder.getAccessToken()}&")
                .appendNormalTag()
//        return domain.plus("/pan/#/?token=$token&platform=$platform&height=$statusBarHeight")
//        return "http://192.168.0.62:8085".plus("/pan/index.html#/?token=$token&platform=$platform&height=$statusBarHeight")
    }

    /**刷新token接口*/
    fun getTokenUrl(): String {
        return version_mode.tokenServer + "oauth/token"
    }

    /**获取登录token信息*/
    fun getLoginTokenUrl(): String {
        return version_mode.tokenServer + "oauth/mobile/token"
    }

    /**退出登录接口*/
    fun getLogoutUrl(): String {
        return version_mode.tokenServer + "oauth/remove/token"
    }

    /**用户服务协议web地址*/
    fun getServiceUrl(): String {
        return "$domain/h5/service.html"
    }

    /**隐私政策接口或者地址*/
    fun getPrivacyUrl(): String {
        return "$domain/h5/privacy_android.html"
    }

    /**创建新模板内容web页面*/
    fun getResolveUrl(): String {
        return "$domain/h5/resolve.html"
    }

    /**汇报统计规则列表*/
    fun getReportRuleList(reportPeriod: String, companyId: String, statisticsId: String): String {
        LogUtil.showLog("----->>>>getReportRuleList")
        return domain.plus("/h5").plus("/report.html#/${reportPeriod}?" +
                "companyId=${companyId}&statisticsId=${statisticsId}&").appendNormalTag()
    }

    /**汇报统计规则列表 查看汇报统计详情*/
    fun getReportRuleDetail(reportPeriod: String, companyId: String, statisticsId: String,
                            startTime: String, endTime: String): String {
        LogUtil.showLog("----->>>>getReportRuleDetail")
        return domain.plus("/h5").plus("/report.html#/${reportPeriod}?id=${statisticsId}" +
                "&companyId=${companyId}" + "&startTime=${startTime}" + "&endTime=${endTime}" +
                "&").appendNormalTag()
    }

    /**汇报详情web地址*/
    fun getReportDetail(companyId: String?, reportId: String?, modelId: String?): String {
        LogUtil.showLog("----->>>>getReportDetail")
        val url = domain.plus("/h5").plus("/report.html#/dailyDetail?" +
                "companyId=${companyId}" + "&reportId=${reportId}&").appendNormalTag()
        if (!modelId.isNullOrBlank()) {
            return url.plus("&modelId=${modelId}")
        }
        return url
    }

    /**编辑汇报web地址*/
    fun getReportEditPage(companyId: String?, reportId: String?, reportType: String): String {
        LogUtil.showLog("----->>>>getReportEditPage")
        return domain.plus("/h5").plus("/report.html#/writeDaily?" +
                "companyId=${companyId}&reportType=${reportType}" +
                "&reportId=${reportId}&isEdit=1&").appendNormalTag()
    }

    /**通知 带接收人的 创建汇报地址*/
    fun getReportSubmitPage(companyId: String?, reportType: String, reportId: String = "0"): String {
        LogUtil.showLog("----->>>>getReportSubmitPage")
        return domain.plus("/h5").plus("/report.html#/writeDaily?" +
                "companyId=${companyId}&reportType=${reportType}" +
                "&reportId=${reportId}" + "&recipient=1&").appendNormalTag()
    }

    /**创建汇报*/
    fun getReportCreatePage(companyId: String?, reportType: String, reportId: String = "0" , token: String = ""): String {
        LogUtil.showLog("----->>>>getReportCreatePage")
        return domain.plus("/h5").plus("/report.html#/writeDaily?" +
                "companyId=${companyId}&reportType=${reportType}" +
                "&token=${token}" +
                "&reportId=${reportId}&").appendNormalTag()
    }

    /**投票分享web地址*/
    fun getVoteShareUrl(id: String): String {
        return domain.plus("/h5").plus("/vote.html#/?w=1&vid=${id}")
    }

    /**通知分享web地址*/
    fun getNoticeShareUrl(id: String): String {
        return domain.plus("/h5").plus("/vote.html#/?w=2&nid=${id}")
    }

    /**数字报告web地址*/
    fun getDigitalReportUrl(companyId: String?, date: String): String {
        return domain.plus("/h5").plus("/numbers.html#/?companyId=$companyId" +
                "&date=$date&").appendNormalTag()
    }

    /**数字报告web地址*/
    fun getDigitalReportUrl2(companyId: String?): String {
        return domain.plus("/h5").plus("/numbers.html#/?companyId=$companyId&")
                .appendNormalTag2()
    }

    /**数字报告周报web地址*/
//    @Deprecated("not used")
    fun getDigitalReportByWeekUrl(companyId: String?, date: String, type: Int): String {
        return domain.plus("/h5").plus("/numbers.html#/md?companyId=$companyId" +
                "&date=${date}".plus(if (type != 0) "&week=$type" else ""))
                .plus("&").appendNormalTag()
    }

    fun getDigitalReportUrl3(companyId: String): String {
        return domain.plus("/h5").plus("/numbers.html#/new?companyId=$companyId&")
                .appendNormalTag2()
    }

    fun getDigitalReportUrl3(date: String = "", type: Int = 0): String {
        return domain.plus("/h5").plus("/numbers.html#/new?date=$date&type=$type&")
                .appendNormalTag2()
    }

    // http://oss.joinu.ltd:9000/ddbes-approve-mobile/index.html#/all/3
    val approvePre = "ddbes-approve-mobile"
//    val approveDomain = "http://oss.joinu.ltd:9000"
    val approveDomain = "http://10.0.1.10:9000"

    val proApprovePre = "approve"

    //全部审批
    fun getTotalApproveListUrl(): String {
        if (local == ServerEnv.DEV){
            return approveDomain.plus("/$approvePre").plus("/index.html#/all/3?").appendApproveTag()
        }
        return domain.plus("/$proApprovePre").plus("/index.html#/all/3?").appendApproveTag()
    }
    /**审批 我发起的 web地址*/
    fun getStartApproveListUrl(): String {
        if (local == ServerEnv.DEV){
            return approveDomain.plus("/$approvePre").plus("/index.html#/start/3?").appendApproveTag()
        }
        return domain.plus("/$proApprovePre").plus("/index.html#/start/3?").appendApproveTag()
    }

    /**审批 我审批的 web地址*/
    fun getHandleApproveListUrl(): String {
        if (local == ServerEnv.DEV){
            return approveDomain.plus("/$approvePre").plus("/index.html#/approve/1?").appendApproveTag()
        }
        return domain.plus("/$proApprovePre").plus("/index.html#/approve/1?").appendApproveTag()
    }

    /**审批 抄送我的 web地址*/
    fun getCopyApproveListUrl(): String {
        if (local == ServerEnv.DEV){
            return approveDomain.plus("/$approvePre").plus("/index.html#/copy?").appendApproveTag()
        }
        return domain.plus("/$proApprovePre").plus("/index.html#/copy?").appendApproveTag()
    }

    /**审批 详情页面 web地址*/
    fun getApproveDetail(approveId: String, approveType: Int): String {
        return domain.plus("/approve").plus("/index.html#/detail/${approveId}/?type=${approveType}&").appendApproveTag()
    }

    //访客信息详情
    //参数status为0时，代表未完成，1代表已完成
    fun getVisitorDetail(status:Int,visitorId:String):String{
        return domain.plus("/visitor").plus("/index.html#/applyPage/?status=${status}&visitorId=${visitorId}&").appendNormalTag()
    }

    //访客待处理列表
    fun getVisitorList():String{
        return domain.plus("/visitor").plus("/index.html#/applyList?").appendNormalTag()
    }

    /**
     * 文件预览地址
     * @param filePath 文件云盘路径
     * @param suffix 文件后缀
     */
    fun getFilePreviewUrl(filePath: String, suffix: String): String {
        return "https://api.ddbes.com/preview/onlinePreview?url=${filePath}&ext=${suffix}"
    }

    /**
     * 文件预览页面地址
     */
    fun getFilePreviewPageUrl(): String {
        return "$domain/preview/index.html#/?platform=$platform"
//        return "http://192.168.0.62:8080/preview/index.html#/?platform=$platform"
    }

    /**健康上报web页面地址*/
    fun getHealthReportUrl(): String {
//        return "http://192.168.0.63:8088/healthReport/#/?platform=$platform"
//        return domain.plus("/?").appendNormalTag()//web测试使用，发版修改位置7
        return domain.plus("/healthReport/index.html#/?").appendNormalTag()//正式使用
    }


    /**商城的web页面地址*/
    fun getShoppingUrl(): String {
//        return "http://192.168.0.63:8088/healthReport/#/?platform=$platform"
        return domain.plus("/?").appendNormalTag()
    }

    /**
     * 考勤打卡eb页面地址
     * @params 通知调用时使用
     * */
    fun getAttendRecord(date: String = ""): String {
        return domain.plus("/h5/attence.html#/?date=$date&").appendNormalTag2()
    }

    /**
     * 考勤打卡eb页面地址
     * @params 通知调用时使用
     * */
    fun getAttendOrderSetting(companyId: String, groupId: String, time: String = "2021-07"): String {
        return domain.plus("/h5/attence.html#/dutyList?id=${groupId}&cid=${companyId}&time=${time}&").appendNormalTag()
    }

    // TODO: 2021/8/14 金蝶相关web页面

    /**金蝶关联功能 web页面地址*/
    fun getKingDeeUrl(companyId: String): String {
        // 需要先获取token再加载页面
//        return "http://kingdee.ddbes.cn/ierp/accessTokenLogin.do?redirect=http://kingdee.ddbes.cn/ierp/mobile.html?form=wf_mobilelist_mob&access_token="
        return MMKVUtil.getString("kingDeeListUrl_$companyId")
    }

    fun getInteGrateUrl(webUrl: String,needAddEndTag:Boolean=true): String {
        if (needAddEndTag) {
            val temp= if (webUrl.contains("?")) {
                "&"
            }else{
                "?"
            }
            return webUrl.plus(temp).appendNormalTag()
        } else {
            return webUrl
        }
    }

    /**扫描访客二维码后地址*/
    fun getResultUrl(addressUrl: String): String {
        // 需要先获取token再加载页面
//        return "http://kingdee.ddbes.cn/ierp/accessTokenLogin.do?redirect=http://kingdee.ddbes.cn/ierp/mobile.html?form=wf_mobilelist_mob&access_token="
        val temp= if (addressUrl.contains("?")) {
            "&"
        }else{
            "?"
        }
        return addressUrl.plus(temp).appendNormalTag()
    }

    /**金蝶关联功能 web页面地址*/
    fun getKingDeeDetailUrl(previewUrl: String): String {
        // 需要先获取token再加载页面
        return previewUrl
    }

}