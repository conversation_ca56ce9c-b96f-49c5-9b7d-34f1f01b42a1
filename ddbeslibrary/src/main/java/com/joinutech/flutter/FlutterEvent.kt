package com.joinutech.flutter

import com.joinutech.ddbeslibrary.bean.AppStartResult
import com.joinutech.ddbeslibrary.bean.OutMsgPersonBean
import com.joinutech.ddbeslibrary.imbean.ImTokenBean
import okio.ByteString
import java.io.File
import java.io.Serializable

/**
 * @Des：
 * @author: moon
 * @date: 9/12/23
 */

data class EventHomeReady(val msg:String = "")
data class EventLoginOut(val type: Int = 1)
data class EventResetLoginIM(val type: Int = 1)
data class EventToFlutterCreateFriend(val type: Int = 1)


data class EventUpdateUserInfo(val type: Int = 1)

data class EventStartUserInfoPage(val userId:String , val type: Int = 1 , val companyId : String = "")   // //1手机号搜索2群组3组织4外部联系人
data class EventBackHandSignFile(val approveId: String , val file : File)
data class EventApplyDetailPage(
                                val recordId : String ="",
                                val companyId: String ="",
                                val type: Int =0,   // 0 好友，2 公司，1 外部联系人
)

data class EventDoneWith(val route:String , val orgId:String , val approveId:String)

data class EventRefreshOrg(val id:Int)  // 更新组织
data class EventRefreshOfflineData(val id:Int = 0)

data class EventApplyInfoData(val routeName: String, val argument: Object)
data class EventRefreshApprove(val orgId: String? = "" , var map: HashMap<String,Any> = hashMapOf())
data class EventRefreshWorkRed(val orgId: String? = "")
data class EventCheckTimeUpdate(val id: String? = "")
data class EventAboutCheckTimeUpdate(val id: String? = "")
data class EventRefreshGroupInfo(val id: String? = "")
data class EventStartGroupChat(val id: String? = "")  // 发起群聊后关闭单聊页面，单聊信息页面

data class EventCallVoiceBean(var time: Long = 0 , var meetingId:String = "" , var sendName:String = "")
data class EventGroupAddFriend(
    var selectList: List<String> = arrayListOf() , var type: Int = 1 , var groupId :String = "",
    val userList: List<EventGroupAddFriendUser> = arrayListOf(),
    var index: Int = 0
    )  // type: 0创建群组1群组添加成员

data class EventGroupAddFriendUser(var name: String = "") : Serializable

data class EventRefreshApproveRedDot(var msgType: Int , val data: String)
data class EventRefreshContactPendingDataRedDot(var id: String? = "")


data class EventToApproveDetail(var approveId: String? = "" , var orgId: String)
data class EventToApproveList(var listType: Int? = 2 , var orgId: String )
data class EventToRtcMeetingDetail(var meetingId: String? = "" , var type: Int )


data class RtcEventStartMeeting(val meetingId: String)

data class EventOpenFlutterForward(val type: Int = 2 , val forwardText: String? = "" , var isMerge: Boolean = false)


data class TranslateData(val members: ArrayList<OutMsgPersonBean>? = arrayListOf(),
    var message: String? = ""
)


data class RefreshMsgTop(val id: String? = "" , val isTop: Boolean = false)

data class EventAcceptImData(var body: ImTokenBean?)
data class EventAccept6in1Data(var body: AppStartResult?)
data class EventClearFlutterCache(var body: Int? = 0)
data class EventImLoginSuccess(var body: Int? = 0)
data class EventShowLogoutDialog(var body: String? = "")
data class EventGroupNameAction(var groupId: String?, var groupName: String?, var groupLogo: String?)
data class EventForceRefreshWorkbench(var id: String? = "")

data class EventToRecord(val sessionId: String?)


data class EventFinishPage(var id: String? = "");
data class EventUpdateSession(var id: String? = "" , var avatar: String? = "" , var name: String? = "");
data class EventRequestPermission(var body: String? = "");

// 向flutter 发送数据
data class EventFlutterIM(var data : String);
// 发送多端登录指令
data class EventSyncFlutter(var data : Map<String, Any>);
// 刷新 Flutter 顶部条的会议状态
data class EventFlutterRefreshMeetingTopbar(var sessionId: String)
data class EventFluttertranslateLocalPath(var localPath: String)


data class EventSelectMember(var members: List<String>? = arrayListOf())
// appChatId,
//                                                targetLogo,
//                                                targetName,
data class EventCloseWeb(var appChatId: String? = "")
data class EventReOpenWeb(var appChatId: HashMap<String,Any>? = hashMapOf())


data class EventTiandituParam(var json: String? = "");


data class EventApproveDeleteKingdee(var name: String? = "");
data class EventToQrCode(var name: String? = "");
data class EventCloseFlutterEngine(var name: String? = "");
data class EventWebViewGoBack(var name: String? = "");
data class EventSocketStatus(var status: Int? = 0);   //0 失败 ，  1 成功，  -1 断开
data class EventByte(var status: ByteString?);
data class EventReConnect(var status: Int? = 0);
data class EventRouteFlutter(var json: String? = "");
data class EventTakeMealSuccess(var json: String? = "");
data class EventBindWeChat(var json: String? = "");
data class EventRefreshNotice(var type: Int = 1,var orgId: String? = "");
