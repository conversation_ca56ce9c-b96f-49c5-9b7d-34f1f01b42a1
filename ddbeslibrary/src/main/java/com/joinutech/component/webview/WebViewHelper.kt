package com.joinutech.component.webview

import android.annotation.SuppressLint
import android.content.Context
import android.view.View
import com.tencent.smtt.sdk.WebChromeClient
import com.tencent.smtt.sdk.WebSettings
import com.tencent.smtt.sdk.WebView
import com.tencent.smtt.sdk.WebViewClient

/**
 * @Des：综合管理webview，配置及其属性
 * @author: moon
 * @date: 8/29/23
 */
class WebViewHelper {

    companion object {

        @SuppressLint("SetJavaScriptEnabled")
        fun createWebView(context: Context ,
                          webViewClient: WebViewClient? = null,
                          webChromeClient: WebChromeClient? = null ): WebView {
            val webview = WebView(context)
            webview.setLayerType(View.LAYER_TYPE_SOFTWARE, null)

            webview.settings.apply {
                javaScriptEnabled = true
                pluginsEnabled = true
                useWideViewPort = true
                loadWithOverviewMode = true
                setSupportZoom(true)
                builtInZoomControls = true
                displayZoomControls = false

                cacheMode = WebSettings.LOAD_DEFAULT
                allowFileAccess = true
                javaScriptCanOpenWindowsAutomatically = true
                loadsImagesAutomatically = true
                defaultTextEncodingName = "utf-8"

                layoutAlgorithm = WebSettings.LayoutAlgorithm.SINGLE_COLUMN
                setSupportMultipleWindows(false)


                val hasStoragePerm = true
                domStorageEnabled = hasStoragePerm
                databaseEnabled = hasStoragePerm
            }

            webViewClient?.let {
                webview.webViewClient = it
            }
            webChromeClient?.let {
                webview.webChromeClient = it
            }
            return webview;
        }
    }

}