package com.joinutech.ddbeslibrary.api

import com.joinutech.common.base.tokenKey
import com.joinutech.ddbeslibrary.bean.ConfCreate
import com.joinutech.ddbeslibrary.bean.ConversationDetailBean
import com.joinutech.ddbeslibrary.bean.MeetingMember
import com.joinutech.ddbeslibrary.request.Result
import io.reactivex.Flowable
import retrofit2.http.*

/**
 * @Description: 视频会议API
 * @Author: hjr
 * @Time: 2020/2/14 16:16
 * @packageName: com.joinutech.ddbeslibrary.api
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
interface VcApi {

    @POST("meeting/meeting")
    fun launchVideoConversation(@Header(tokenKey) token: String, @Body data: Any)
            : Flowable<Result<ConfCreate>>

    @GET("meeting/mettingView/{id}")
    fun getVideoConversationDetail(@Header(tokenKey) token: String, @Path("id") id: String)
            : Flowable<Result<ConversationDetailBean>>

    @PUT("meeting/meeting")
    fun updateConversationStatus(@Header(tokenKey) token: String, @Body data: Any)
            : Flowable<Result<String>>

    @POST("meeting/v1/meeting/userAdd")
    fun inviteJoinConversation(@Header(tokenKey) token: String, @Body data: Any)
            : Flowable<Result<String>>

    @PUT("meeting/v1/meeting/userRemove/mettingId/{mettingId}/userId/{userId}")
    fun shiftOutConversation(@Header(tokenKey) token: String,
                             @Path("mettingId") mettingId: String,
                             @Path("userId") userId: String): Flowable<Result<String>>

    @GET("meeting/v1/meeting/allUsers/{meetingId}/{status}")
    fun queryJoinConversationPerson(@Header(tokenKey) token: String,
                                    @Path("meetingId") mettingId: String,
                                    @Path("status") status: String): Flowable<Result<List<MeetingMember>>>

    @PUT("meeting/v1/meeting/userJoin/mettingId/{mettingId}/userId/{userId}")
    fun joinConversation(@Header(tokenKey) token: String,
                         @Path("mettingId") mettingId: String,
                         @Path("userId") userId: String): Flowable<Result<String>>
}