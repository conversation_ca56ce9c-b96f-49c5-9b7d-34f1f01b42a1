package com.joinutech.ddbeslibrary.api

import com.joinutech.common.base.tokenKey
import com.joinutech.ddbeslibrary.bean.*
import com.joinutech.ddbeslibrary.request.Result
import io.reactivex.Flowable
import retrofit2.http.*

/**
 * 消息接口类
 */
interface FileUploadApi {

//    //获取腾讯云ID
//    @GET
//    fun getTencentId(@Header(tokenKey) token: String, @Url url: String): Flowable<Result<List<String>>>

    /**获取腾讯云 公共 文件上传 配置信息
     * // CHANGE_HISTORY: 2021/1/25 16:24 新增接口
     */
    @GET("user/upload/config")
    fun getBucketConfig(): Flowable<Result<CosBucketConfig>>

    /**获取腾讯云 公共 文件上传 权限*/
    @GET("user/upload/auth")
    fun getTencentSession(@Header(tokenKey) token: String): Flowable<Result<TencentSessionBean>>

    /**获取腾讯云 公共 文件上传 权限
     * // CHANGE_HISTORY: 2021/1/14 9:16 更新
     * */
    @GET("user/upload/auth/v2")
    fun getTencentSessionV2(@Header(tokenKey) token: String): Flowable<Result<TencentSessionBean>>

    /**
     * 获取云盘文件上传 bucket 配置
     * // CHANGE_HISTORY: 2021/1/25 16:24 新增接口
     */
    @GET("pan/file/upload/bucket/v1")
    fun getPanBucketConfig(): Flowable<Result<CosBucketConfig>>

    //    /**
//     * 获取腾讯云 网盘 文件上传 秘钥
//     * @param token 个人验证信息
//     * @see #getPanBucketConfig(String) 获取bucket后 再调用 该接口
//     * // CHANGE_HISTORY: 2021/1/25 16:24 入参 出参更新
//     */
    @GET("pan/file/token/v1")
    fun getPanTencentSession(@Query("bucket") bucket: String): Flowable<Result<TencentSessionBean>>

    /**新版聚合token返回，开放，网盘，im，cdn四种bucket，和共同秘钥
     * // CHANGE_HISTORY: 2021/3/30 16:53 废弃v1，更新版本v2，返回聚合的bucket和共用的session
     */
    @GET("pan/file/token/v2")
    fun getPanTencentSessionV2(): Flowable<Result<TencentSessionBean>>

    /**
     * 云文档使用 上传文件成功后，同步数据到云盘后台
     * // CHANGE_HISTORY: 2021/1/25 16:24 入参 更新
     */
    @PUT("pan/file/v1")
    fun uploadFile(@Body map: Any): Flowable<Result<PanUploadResult>>

    /**--------------------下载---------------------*/
    /**
     * 获取腾讯云网盘 文件下载 秘钥
     * // CHANGE_HISTORY: 2021/3/30 16:55 废弃，更新版本V2
     */
    @GET("pan/file/download/v1/{fileId}")
    fun getPanDownloadSession(@Path("fileId") fileId: String): Flowable<Result<DownloadFileSession>>

    @GET("pan/file/download/v2/{fileId}")
    fun getPanDownloadSessionV2(@Path("fileId") fileId: String): Flowable<Result<DownloadFileSession>>

    /**
     * 获取腾讯云网盘 文件预览 临时url 给web用
     * // CHANGE_HISTORY: 2021/1/25 16:24 入参 出参更新
     */
    @POST("pan/file/download/url/keys/v2")
    fun getPanFileTempUrl(@Body download: PanLinkRequest): Flowable<Result<List<PanLinkResult>>>

//    /**批量获取网盘腾讯云 fileID*/
//    @GET("pan/file/id/v2/{num}")
//    fun getPanTencentIdNoHash(@Header(tokenKey) token: String, @Path("num") num: Int)
//            : Flowable<Result<List<String>>>

    /**批量获取网盘腾讯云 fileID 返回hash*/
    @POST("pan/file/id/v1")
    fun getPanTencentId(@Header(tokenKey) token: String, @Body map: Any): Flowable<Result<List<PanFileBean>>>

}