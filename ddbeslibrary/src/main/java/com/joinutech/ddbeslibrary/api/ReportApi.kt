package com.joinutech.ddbeslibrary.api

/**
 * @Description: 汇报api
 * @Author: hjr
 * @Time: 2020/3/9 14:09
 * @packageName: com.joinutech.ddbeslibrary.api
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
//@Deprecated("no used")
//interface ReportApi {
//    //写日报模板列表
//    @GET("report/model/list/{companyId}")
//    fun getWriteModelList(@Header(tokenKey) token: String,
//                          @Path("companyId") companyId: String)
//            : Flowable<Result<List<ReportWriteModelBean>>>
//
//    //查看汇报列表
//    @POST("report/report/list")
//    fun getWatchReportList(@Header(tokenKey) token: String, @Body data: Any):
//            Flowable<Result<WatchReportListBean>>
//
//    //得到评论列表
//    @GET("report/comment/{page}/{pageCount}/{companyId}")
//    fun getReportCommentList(@Header(tokenKey) token: String,
//                             @Path("companyId") companyId: String,
//                             @Path("page") page: Int,
//                             @Path("pageCount") pageCount: Int)
//            : Flowable<Result<List<ReportCommentListBean>>>
//
//    //查看汇报未读数
//    @GET("report/report/unRead/{companyId}")
//    fun getWatchReportNum(@Header(tokenKey) token: String,
//                          @Path("companyId") companyId: String): Flowable<Result<Int>>
//
//    //写汇报评论
//    @POST("report/comment")
//    fun writeReportComment(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>
//
//    //添加汇报规则
//    @POST("report/statistics")
//    fun addReportCommitRule(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>
//
//    //得到汇报统计列表
//    @GET("report/statistics/{companyId}/{page}/{pageCount}/{type}")
//    fun getReportStatisticsList(@Header(tokenKey) token: String,
//                                @Path("companyId") companyId: String,
//                                @Path("page") page: Int,
//                                @Path("pageCount") pageCount: Int,
//                                @Path("type") type: Int)
//            : Flowable<Result<ReportRuleStatisticsListBean>>
//
//    //得到汇报模板列表
//    @GET("report/model/{companyId}")
//    fun getReportModelList(@Header(tokenKey) token: String,
//                           @Path("companyId") companyId: String)
//            : Flowable<Result<List<ReportModelBean>>>
//
//    //编辑汇报规则
//    @PUT("report/statistics")
//    fun editReportCommitRule(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>
//
//    //获取统计规则详情
//    @GET("report/statistics/{statisticsId}")
//    fun getReportRuleDetail(@Header(tokenKey) token: String,
//                            @Path("statisticsId") statisticsId: String)
//            : Flowable<Result<ReportRuleDetailBean>>
//
//    //删除统计规则
//    @DELETE("report/statistics/{statisticsId}")
//    fun deleteReportRule(@Header(tokenKey) token: String,
//                         @Path("statisticsId") statisticsId: String): Flowable<Result<Any>>
//
//}