package com.joinutech.ddbeslibrary.api

import com.joinutech.common.base.tokenKey
import com.joinutech.ddbeslibrary.bean.TokenBean
import com.joinutech.ddbeslibrary.bean.VerifyImageBean
import com.joinutech.ddbeslibrary.request.Result
import io.reactivex.Flowable
import retrofit2.Call
import retrofit2.http.*


/**
 * 登录接口类
 */
interface LoginApi {

    /**todo 注销账号接口*/
    @POST("user/user/logout/v2")
    fun unRegisterAccount(@Body data: Any): Flowable<Result<Any>>

    //登录
    @POST
    fun getLoginToken(@Header(tokenKey) token: String, @Url url: String, @Body data: Any): Flowable<Any>

    /**线程内刷新token*/
    @POST
    fun refreshTokenCall(@Header(tokenKey) token: String,
                         @Url url: String,
                         @Query("refresh_token") refreshToken: String,
                         @Query("grant_type") grant_type: String = "refresh_token"): Call<TokenBean>
    //退出登录
    @GET
    fun loginOut(@Header(tokenKey) token: String, @Url url: String): Flowable<Result<String>>

    //验证码登录
    @POST
    fun getVerifyLoginToken(@Header(tokenKey) token: String, @Url url: String, @Body data: Any): Flowable<Any>

    //查询微信用户是否绑定担当
    @POST
    fun getOpenIdToken(@Header(tokenKey) token: String, @Url url: String, @Body data: Any): Flowable<Any>

    /**完善用户信息接口*/
    @POST("user/user/register/edit")
    fun completeUserInfo(@Body userInfo: Any): Flowable<Result<Any>>

    //注册
    @POST("user/user/register")
    fun register(@Body data: Any): Flowable<Result<Any>>

    //获取隐私协议政策内容
    @GET
    fun getPrivacy(@Url url:String): Flowable<Result<String>>

    //验证手机号是否注册或登录，验证手机号验证码是否达到上限，验证是否为新设备
    @GET("verify/api/v2/sms/verify/phone/{phone}/type/{type}")
    fun confirmPhone(@Path("phone") phone: String,
                     @Path("type") type: Int): Flowable<Result<Any>>

    //手机号是否绑定担当账号
    @GET("user/wx/volidate/{phone}")
    fun wxPhone(@Path("phone") phone: String): Flowable<Result<Any>>

    //发送验证码
    @GET("verify/api/v2/sms/{phone}/{type}")
    fun sendSms(@Path("phone") phone: String, @Path("type") type: Int):
            Flowable<Result<Any>>

    //修改密码
    @POST("user/user/set/forgetPwd")
    fun updatePassword(@Body data: Any): Flowable<Result<Any>>

    //微信绑定担当
    @POST("user/wx/bind")
    fun wxBind(@Body data: Any): Flowable<Result<Any>>

    //微信绑定担当(设置中)
    @POST("user/user/wx/bind")
    fun wxBindSafe(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>

    //解除绑定微信
    @PUT("user/plat/open/wx/unbind")
    fun wxUnBind(@Header(tokenKey) token: String): Flowable<Result<Any>>

    //担当app扫描二维码
    @GET
    fun scanCodeGetInfo(@Url url: String): Flowable<Result<Any>>

    //担当二维码确定/取消
    @GET
    fun dealWebLogin(@Url url: String): Flowable<Result<Any>>

    /**
     * 获取图片验证码
     */
    @GET("verify/api/v2/verify/getVerifyImg/phone/{phone}")
    fun getVerifyImage(@Path("phone") phone: String): Flowable<Result<VerifyImageBean>>

    /**
     * 校验图片验证码
     */
    @POST("verify/api/v2/verify/verifyImage")
    fun verifyImage(@Body dataMap: Any): Flowable<Result<Any>>

    /**
     * 验证图片验证码带短信验证码
     */
    @POST("verify/api/v2/verify/verify/img/send/code")
    fun verifyImageWithMsg(@Body dataMap: Any): Flowable<Result<Any>>

    /**
     * 校验验证码是否正确，以及验证码是否过期
     */
    @GET("verify/api/v2/sms/verifyCode/{mobile}/{code}/{type}")
    fun verifyUserMsgIsValid(@Path("code") code: String,
                             @Path("mobile") mobile: String,
                             @Path("type") type: Int): Flowable<Result<Any>>

    /**
     * todo 校验验证码是否正确 并返回业务码，用以通过验证码验证后 业务认证
     */
    @POST("verify/api/v2/sms/verifyCode")
    fun verifyCode(@Body data: Any): Flowable<Result<String>>
}