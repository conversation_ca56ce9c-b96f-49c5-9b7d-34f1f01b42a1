package com.joinutech.ddbeslibrary.api

import com.joinutech.common.base.tokenKey
import com.joinutech.ddbeslibrary.bean.*
import com.joinutech.ddbeslibrary.request.Result
import io.reactivex.Flowable
import retrofit2.http.*

/**
 * <AUTHOR>
 * @date   2019/4/22 9:25
 * @className: TaskApi
 *@Description: 任务API接口类
 */
interface TaskApi {

    /**
     * 获取日志列表
     * @param taskId 任务id
     * @param type 查询日志类型 type：1.全部动态；2.日志 3.留言
     */
    @GET("task/log/getLogs/v2")
    fun getLogList(@Header(tokenKey) token: String,
                   @Query("taskId") taskId:
                   String, @Query("type") type: Int = 1)
            : Flowable<Result<List<TaskLogListBean>>>

    //创建项目
    @POST("task/project")
    fun creatProgram(@Header(tokenKey) accessToken: String, @Body map: Any)
            : Flowable<Result<String>>

    //编辑项目
    @PUT("task/project")
    fun editProgram(@Header(tokenKey) accessToken: String, @Body map: Any)
            : Flowable<Result<Any>>

    //修改项目状态
    @DELETE
    fun returnRecycleProject(@Header(tokenKey) token: String, @Url url: String):
            Flowable<Result<Any>>

    //获取项目信息
    @GET("task/project/{projectId}")
    fun projectDetail(@Header(tokenKey) token: String, @Path("projectId") projectId: String):
            Flowable<Result<ProgramBean>>

    // 添加项目成员
    @POST("task/project/addProNum")
    fun addProjectJoiner(@Header(tokenKey) token: String,
                         @Body map: HashMap<String, Any>): Flowable<Result<Any>>

    // 修改项目管理员
    @PUT("task/project/auth")
    fun changeProjectManager(@Header(tokenKey) token: String,
                             @Body map: HashMap<String, Any>): Flowable<Result<Any>>

    //判断人员在团队中是否有创建项目
    @GET("task/project/checkPro/{userId}/{organizationId}")
    fun getOrgCreateProgramList(@Header(tokenKey) accessToken: String,
                                @Path("userId") userId: String,
                                @Path("organizationId") companyId: String)
            : Flowable<Result<List<ProgramBean>>>

    //人事移交和删除项目操作
    @POST("task/project/dealPro")
    fun handOverProgramList(@Header(tokenKey) token: String, @Body list: Any):
            Flowable<Result<Any>>

    //项目创建者移交项目
    @POST("task/project/deliver")
    fun transfer(@Header(tokenKey) accessToken: String, @Body map: Any):
            Flowable<Result<Any>>

    //获取人均任务完成情况图
    @GET("task/project/getChart/{projectId}")
    fun getTaskChart(@Header(tokenKey) accessToken: String,
                     @Path("projectId") projectId: String):
            Flowable<Result<List<TaskChartBean>>>

    //获取项目列表
    @POST("task/project/projectList")
    fun getProgramList(@Header(tokenKey) accessToken: String, @Body map: Any)
            : Flowable<Result<ProgramListBean>>

    //得到团队参与人
    @POST("task/project/projectMember")
    fun getProjectJoinerList(@Header(tokenKey) accessToken: String, @Body map: Any)
            : Flowable<Result<List<ProjectJoinerBean>>>

    //删除项目成员
    @DELETE
    fun deleteProjectJoiner(@Header(tokenKey) token: String,
                            @Url url: String): Flowable<Result<Any>>

    //项目成员创建任务
    @POST("task/task/v2")
    fun createTaskV2(@Header(tokenKey) token: String,
                     @Body updateTaskModel: Any): Flowable<Result<String>>

    @POST("task/task/v3")
    fun createTask(@Header(tokenKey) token: String, @Body updateTaskModel: Any): Flowable<Result<String>>

    //修改任务
    @PUT("task/task/v3")
    fun updateTaskV3(@Header(tokenKey) token: String, @Body updateTaskModel: Any): Flowable<Result<Any>>

    //修改任务
    @PUT("task/task/v4")
    fun updateTask(@Header(tokenKey) token: String, @Body updateTaskModel: Any): Flowable<Result<Any>>

    //修改任务状态
    @PUT("task/task/status")
    fun updateTaskStatus(@Header(tokenKey) token: String, @Body status: Any): Flowable<Result<Any>>

    //获取任务详情
    @GET("task/task/v2/{taskId}")
    fun getTaskDetail(@Header(tokenKey) token: String,
                      @Path("taskId") taskId: String): Flowable<Result<TaskDetailBean>>

    //删除任务
    @DELETE("task/task/{taskId}")
    fun deleteTask(@Header(tokenKey) token: String,
                   @Path("taskId") taskId: String): Flowable<Result<Any>>

    //任务mobile高级搜索
    @POST("task/task/highTaskSearch")
    fun highTaskSearch(@Body map: Any): Flowable<Result<MulTaskListBean>>

    //得到任务回收站列表
    @GET("task/task/recycleTask/{projectId}/{status}/{page}/{relate}")
    fun getTaskRecycleList(@Header(tokenKey) token: String,
                           @Path("status") status: Int, @Path("page") page: Int,
                           @Path("projectId") projectId: String,
                           @Path("relate") relate: Int):
            Flowable<Result<TaskRecycleListBean>>

    //彻底删除任务
    @DELETE("task/task/recycleTask/{taskId}")
    fun deleteRecycleTask(@Header(tokenKey) token: String,
                          @Path("taskId") taskId: String): Flowable<Result<Any>>

    //恢复删除任务
    @GET("task/task/resumeTask/{taskId}")
    fun returnRecycleTask(@Header(tokenKey) token: String,
                          @Path("taskId") taskId: String): Flowable<Result<Any>>

    //得到未完成的个数
    @GET
    fun getUnCompleteTask(@Header(tokenKey) token: String,
                          @Url url: String): Flowable<Result<UnCompleteListBean>>

    //新增任务评论
    @POST("task/trends/v2")
    fun createTrend(@Header(tokenKey) token: String, @Body map: TaskTendBean):
            Flowable<Result<Any>>

    //新增任务评论 im相关更新
    @POST("task/trends/v2")
    fun createTrendV3(@Header(tokenKey) token: String, @Body map: TaskTendBean):
            Flowable<Result<Any>>

    //增加标签
    @POST("task/tags")
    fun addTaskLabel(@Header(tokenKey) token: String, @Body map: HashMap<String, Any>):
            Flowable<Result<Any>>

    //修改标签
    @PUT("task/tags")
    fun updateTaskLabel(@Header(tokenKey) token: String, @Body map: HashMap<String, Any>):
            Flowable<Result<Any>>

    //标签列表
    @GET("task/tags/{projectId}")
    fun taskLabelList(@Header(tokenKey) token: String, @Path("projectId") projectId: String):
            Flowable<Result<List<TaskLabelCreateBean>>>

    //删除标签
    @DELETE("task/tags/{tagId}")
    fun deleteTaskLabel(@Header(tokenKey) token: String, @Path("tagId") tagId: String):
            Flowable<Result<Any>>

    //获取任务看板任务列表
    @POST("task/task/taskList")
    fun getSingleTaskListData(@Header(tokenKey) accessToken: String, @Body map: HashMap<String, Any>)
            : Flowable<Result<TaskBoardListBean>>

    //设置任务量化参数
    @PUT("task/project/quantization/{projectId}/{status}")
    fun setProjectQuantizationParam(@Header(tokenKey) token: String,
                                    @Path("projectId") projectId: String,
                                    @Path("status") status: Int)
            : Flowable<Result<Any>>

    //项目公告编辑
    @POST("task/project/notice")
    fun projectNoticeAdd(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>

    //项目公告
    @GET("task/project/notice/{projectId}")
    fun getProjectNotice(@Header(tokenKey) token: String, @Path("projectId") projectId: String)
            : Flowable<Result<ProjectNoticeBean>>

    //判断是否创建自由任务的权限
    @GET("task/project/claim/{projectId}")
    fun dealIsHaveFreeClaimPermission(@Header(tokenKey) token: String, @Path("projectId") projectId: String)
            : Flowable<Result<ProjectFreeClaimPermissionBean>>

    //修改人员的自由认领权限
    @PUT("task/project/claim/{projectId}/{userId}/{status}")
    fun changClaimPermission(@Header(tokenKey) token: String,
                             @Path("projectId") projectId: String,
                             @Path("userId") userId: String,
                             @Path("status") status: Int)
            : Flowable<Result<Any>>

    //创建自由认领任务
    @POST("task/task/rewardTask/v2")
    fun createFreeClaimTask(@Header(tokenKey) token: String, @Body taskModel: Any): Flowable<Result<String>>

    /**
     * 转换任务为常规任务
     */
    @PUT("task/task/trans")
    fun changTaskToNormalType(@Header(tokenKey) token: String, @Body updateTaskModel: Any): Flowable<Result<Any>>

    /**
     * 认领自由任务
     */
    @GET("task/task/claimTask/{taskId}")
    fun claimTask(@Header(tokenKey) token: String, @Path("taskId") taskId: String)
            : Flowable<Result<Any>>

    /**
     * 添加项目成员
     */
    @PUT("task/task/taskMember")
    fun addTaskMember(@Header(tokenKey) token: String, @Body updateTaskModel: Any): Flowable<Result<Any>>

    /**
     * 获取关联被关联任务列表
     * @param relation: 0关联 1被关联
     */
    @GET("task/task/relation/{taskId}/{relation}")
    fun relationTaskList(@Header(tokenKey) token: String, @Path("taskId") taskId: String,
                         @Path("relation") relation: Int): Flowable<Result<List<MulTaskBean>>>

    /**
     * 查询团队剩余容量
     */
    @GET("task/task/capacity/{companyId}")
    fun searchCompanyCapacity(@Header(tokenKey) token: String, @Path("companyId") companyId: String)
            : Flowable<Result<CapacityBean>>
}