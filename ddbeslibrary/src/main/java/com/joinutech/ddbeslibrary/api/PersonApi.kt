package com.joinutech.ddbeslibrary.api

import com.joinutech.common.base.tokenKey
import com.joinutech.ddbeslibrary.bean.*
import com.joinutech.ddbeslibrary.request.Result
import io.reactivex.Flowable
import retrofit2.http.*

/**
 * Description 用户资料API
 * Author HJR36
 * Date 2018/6/21 10:32
 */
interface PersonApi {

    companion object {
        const val GATE = "user/user"
    }

    //获取职业信息
    @GET("user/user/set/profession")
    fun getJobChoiceData(@Header(tokenKey) token: String): Flowable<Result<List<JobChoiceBean>>>

    //身份验证
    @POST("user/user/set/checkPwd")
    fun verifyPersonal(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>

    //更换手机号
    @POST("user/user/set/upMobile")
    fun upMobile(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>

    //修改密码
    @POST("user/user/set/upPwd")
    fun upupPwd(@Header(tokenKey) token: String, @Body data: Any)
            : Flowable<Result<Any>>

    //获取个人设置
    @GET("user/user/set/settings/v2")//
    fun getPersonSettingData(@Header(tokenKey) token: String)
            : Flowable<Result<PersonSettingBean>>



    //修改个人设置
    @POST("user/user/set/settings")
    fun updatePersonSetting(@Header(tokenKey) token: String, @Body data: Any)
            : Flowable<Result<Any>>

    //修改陌生人发送消息设置
    @POST("user/user/set/settings/stranger")
    fun updateStrangerSetting(@Header(tokenKey) token: String, @Body data: Any)
            : Flowable<Result<Any>>

    //检查targetID是否设置了陌生人发消息设置
    @GET("user/user/set/settings/stranger/{targetId}")
    fun checkStrangerSetting(@Header(tokenKey) token: String, @Path("targetId") targetId: String)
            : Flowable<Result<CheckStrangerBean>>

    //修改通知推送免打扰
    // ids 免打扰的companyIds
    // systemPushSwitch 系统通知设置 系统开关 0关闭 1开启
    // approvalPushSwitch 审批免打扰 0关闭 1开启
    // 审批通知、系统消息免打扰的设置
    @POST("user/user/set/push/settings/v2")
    fun changeWorkNoticeSet(@Body data: PushSettingData): Flowable<Result<Any>>

    //获取群消息免打扰数组
    //获取通知推送免打扰数组
    @Deprecated("接口合并 user/user/start")
    @GET("user/user/set/push/settings")
    fun getWorkNoticeSet(): Flowable<Result<List<String>>>

    /**提交个人信息*/
    @POST("user/user/upUser")
    fun commitPersonInfo(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<PersonInfoBean>>

    //获取用户信息
    @GET("/user/user/remark/{userId}")
    fun getRemarkUserInfo(@Path("userId") userId: String): Flowable<Result<WithRemarkUserInfo>>

    //获取个人信息
    @GET("user/user/info")
    fun getPersonInfo(@Header(tokenKey) token: String): Flowable<Result<PersonInfoBean>>

    //搜索手机号添加好友
    @GET("user/user/info/search/{phone}")
    fun searchPhone(@Header(tokenKey) token: String, @Path("phone") phone: String)
            : Flowable<Result<PhoneSearchFriendBean>>

    //个人---获取好友信息tcp
    @GET("user/user/info/v2/{userId}")
    fun getUserInfo(@Header(tokenKey) token: String, @Path("userId") userId: String)
            : Flowable<Result<Any>>

    //团队---获取好友信息tcp
    @GET("user/user/info/v2/companyId/{companyId}/userId/{userId}")
    fun getCompanyUserInfo(@Header(tokenKey) token: String,
                           @Path("companyId") companyId: String,
                           @Path("userId") userId: String): Flowable<Result<Any>>

    //通讯录匹配用户是否注册，是否好友
    @POST("user/user/phone/matching")
    fun getPhoneContactStatus(@Header(tokenKey) token: String, @Body data: Any)
            : Flowable<Result<List<PhoneContactDataBean>>>

    //获取banner
    @Deprecated("接口合并 user/user/start")
    @GET("user/banner")
    fun getBanner(): Flowable<Result<List<HomeBannerBean>>>

    //app升级
    @GET("user/app/version/volidate")
    fun validateVersion(@Query("version") version: String,
                        @Query("versionCode") versionCode: Int):
            Flowable<Result<AppVersionBean>>

    //更新IMToken
    @Deprecated("接口合并 user/user/start")
    @POST("user/user/im/token")
    fun setImPushToken(@Body data: Any): Flowable<Result<Any>>

    //用户打开app次数统计
    @Deprecated("接口合并 user/user/start")
    @POST("user/app/version/statistics")
    fun statisticsData(): Flowable<Result<Any>>

    /**
     * 用户登录后调用一次接口
     *  返回
     *  群组离线消息
     *  公司id数组
     *  banner信息
     *  版本更新信息
     *  待审核信息
     *
     * */
    @POST("user/user/start")
    fun userStartApp(@Header("clientCode") clientCode: Long,
                       @Body data: Any): Flowable<Result<AppStartResult>>

    @POST("user/user/start/v2")//六合一tcp
    fun userStartAppV2(@Header("clientCode") clientCode: Int,
                     @Body data: Any): Flowable<Result<AppStartResult>>

    // TODO: 2021/7/1 获取用户imId
    @POST("user/user/imId")
    fun getUserImId(@Body data: List<String>): Flowable<Result<List<String>>>

}