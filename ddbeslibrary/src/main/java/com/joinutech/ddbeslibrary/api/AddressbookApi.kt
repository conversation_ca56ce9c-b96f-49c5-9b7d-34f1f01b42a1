package com.joinutech.ddbeslibrary.api

import com.joinutech.common.base.tokenKey
import com.joinutech.ddbeslibrary.bean.*
import com.joinutech.ddbeslibrary.request.Result
import io.reactivex.Flowable
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.HTTP
import retrofit2.http.Header
import retrofit2.http.PATCH
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query
import retrofit2.http.Url

/**
 *<AUTHOR>
 *@date 2018/11/19
 */
interface AddressbookApi {

    //验证是否有考勤组
    @POST("att/validate")
    fun validateAttendanceGroup(@Header(tokenKey) token: String, @Body data: Any)
            : Flowable<Result<ValidateAttendanceBean>>

    //验证是否有考勤组 删除部门时判断关联考勤组
    @POST("att/validate/dept")
    fun validateAttendanceGroupV2(@Body data: Any): Flowable<Result<ValidateAttendanceBean>>

    //更新考勤组
    @POST("att/update")
    fun updateAttendanceGroup(@Header(tokenKey) token: String,
                              @Body updateUserGroupModel: Any): Flowable<Result<Any>>

    //获取公司列表
    @GET("org/company/app/v3/user")
    fun getCompaniesV3(): Flowable<Result<CompanyListBean>>

    @GET("org/company/app/v2/user/{userId}")
    fun getCompaniesV2(@Path("userId") userId: String): Flowable<Result<CompanyListBean>>

    //获取公司行业
    @GET("org/industry/company")
    fun getIndustry(@Header(tokenKey) token: String): Flowable<Result<List<JobChoiceBean>>>

    //获取职业信息
    @GET("user/user/set/profession")
    fun getJobChoiceData(@Header(tokenKey) token: String): Flowable<Result<List<JobChoiceBean>>>

    //获取公司类型
    @GET("org/company/type")
    fun getCompanyTypes(@Header(tokenKey) token: String): Flowable<Result<List<CompanyTypeBean>>>

    //创建公司
    @POST("org/company")
    fun createCompany(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>

    //修改公司信息
    @PUT("org/company/admin")
    fun modifyCompany(@Header(tokenKey) token: String,
                      @Body data: Any): Flowable<Result<Any>>

    //转让团队创建者
    @PUT("org/company/attorn")
    fun changeOrgCreator(@Header(tokenKey) token: String,
                         @Body data: Any): Flowable<Result<Any>>

    //转让团队创建者
    @PUT("org/company/v2/attorn")
    fun changeOrgCreator(@Body data: Any): Flowable<Result<Any>>

    //根据Id查询公司简略信息
    @GET("org/company/companyInfo/{companyId}")
    fun queryCompanySimpleInfoById(@Header(tokenKey) token: String,
                                   @Path("companyId") companyId: String)
            : Flowable<Result<CompanySampleInfoBean>>

    //根据Id查询公司信息
    @GET("org/company/{companyId}")
    fun queryCompanyById(@Header(tokenKey) token: String,
                         @Path("companyId") companyId: String): Flowable<Result<CompanyBean>>

    //查看用户是否是创建者
    @GET("org/company/validate/{companyId}")
    fun queryValidate(@Header(tokenKey) token: String,
                      @Path("companyId") companyId: String): Flowable<Result<Any>>

    //获取主要团队
    @Deprecated("no used, use all company and cache")
    @GET("org/company/main")
    fun getMainCompany(@Header(tokenKey) token: String): Flowable<Result<CompanyBean>>

    //解散团队
    @DELETE("org/company/v2/{companyId}/{phone}/{code}")
    fun disbandCompany(@Header(tokenKey) token: String,
                       @Path("companyId") companyId: String, @Path("phone") phone: String,
                       @Path("code") code: String): Flowable<Result<Any>>

    //团队同意个人加入
    @PUT("org/personnel/v2/schedule/agree")//tcp已修改
    fun orgAgreeUserJoin(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>

    //团队拒绝个人加入
    @PUT("org/personnel/schedule/refuse")
    fun rejectUserJoin(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>

    //请离人员
    @HTTP(method = "DELETE", path = "org/personnel/v2/admin/schedule/leave", hasBody = true)//tcp已修改
    fun leaveUser(@Header(tokenKey)
                  token: String, @Body map: Any): Flowable<Result<Any>>

    // todo 通讯录首页回调
    //得到未处理消息列表
//    @GET("org/personnel/v3/schedule/wait/{status}/{page}/{size}")//tcp已修改
    @GET("org/personnel/v2/schedule/wait/{status}/{page}/{size}") //tcp已修改
    fun getUndoList(@Path("status") status: Int, @Path("page") page: Int,
                    @Path("size") size: Int): Flowable<Result<UndoBean>>

    //得到公司未处理消息列表
    @GET("org/personnel/schedule/{companyId}/{page}/{size}")
    fun getCompanyUndoList(@Header(tokenKey) token: String,
                           @Path("page") page: Int, @Path("size") size: Int,
                           @Path("companyId") companyId: String)
            : Flowable<Result<CompanyUndoBean>>

    //个人申请团队
    @PUT("org/personnel/v2/schedule/apply")//tcp已修改
    fun verifyOrgApplication(@Header(tokenKey) token: String, @Body data: Any)
            : Flowable<Result<Int>>

    //个人申请团队次数校验
    @PUT("org/personnel/schedule/apply/volidate")
    fun volidate(@Header(tokenKey) token: String, @Body data: Any)
            : Flowable<Result<Int>>

    //搜索团队
    @GET
    fun searchResult(@Header(tokenKey)
                     token: String, @Url data: String)
            : Flowable<Result<List<CompanyBean>>>

    //搜索团队成员
//    @GET("org/personnel/schedule/member/sort/{companyId}")
//    fun searchDepMember2(@Header(tokenKey) token: String,
//                         @Path("companyId") companyId: String,

//                         @Query("keyword") keyWord: String)
//            : Flowable<Result<List<SearchMemberBean>>>

    // TODO: 2020/10/13 13:40 url优化，不再拼接后传参，改为参数
    @GET("org/personnel/schedule/member/sort/{companyId}")
    fun searchDepMember(@Path("companyId") companyId: String,
                        @Query("keyword") keyword: String)
            : Flowable<Result<List<SearchMemberBean>>>

    //退出团队
    @DELETE("org/personnel/schedule/{companyId}")
    fun quitCompanyV2(@Header(tokenKey) token: String,
                      @Path("companyId") companyId: String): Flowable<Result<Any>>

    //退出团队
    @DELETE("org/personnel/v2/schedule/{companyId}")//tcp已修改
    fun quitCompany(@Header(tokenKey) token: String,
                    @Path("companyId") companyId: String): Flowable<Result<Any>>

    //查询架构部门
    @GET("org/company/dept/{companyId}/{deptId}")
    fun quitChartDep(@Header(tokenKey) token: String,
                     @Path("companyId") companyId: String, @Path("deptId") deptId: String)
            : Flowable<Result<OrgChartDepBean>>

    // TODO: 2021/6/4 10:45 组织架构部门排序
    @POST("org/company/dept/rank")
    fun orgDeptSort(@Body deptRankDTO: OrgDeptSortBean): Flowable<Result<String>>

    //添加子部门
    @POST("org/company/dept/admin/{deptId}")
    fun addChildDep(@Path("deptId") deptId: String, @Body body: Any): Flowable<Result<Any>>

    /**改变团队部门*/
    @POST("org/personnel/v2/admin/schedule/deptement")//tcp已修改
    fun changeMemberDep(@Header(tokenKey) token: String, @Body body: Any): Flowable<Result<Any>>

    //更改上级部门
    @PUT("org/company/dept/{companyId}")
    fun changeAboveDep(@Header(tokenKey) token: String,
                       @Path("companyId") companyId: String,
                       @Body body: Any): Flowable<Result<Any>>

    //新增部门负责人
    /*  @POST("org/personnel/principal")
      fun addDepHead(@Header(tokenKey) token: String,
                     @Body body: Any)
              : Flowable<Result<Any>>*/
    @POST("org/personnel/principal/v2")//tcp已修改
    fun addDepHead(@Header(tokenKey) token: String,
                   @Body body: Any)
            : Flowable<Result<Any>>

    //更改部门负责人
    /* @PUT("org/personnel/principal")
     fun changeDepHead(@Header(tokenKey) token: String,
                       @Body body: Any)
             : Flowable<Result<Any>>*/
    @PUT("org/personnel/principal/v2")//tcp已修改
    fun changeDepHead(@Header(tokenKey) token: String,
                      @Body body: Any)
            : Flowable<Result<Any>>

    //请离人员
    /* @HTTP(method = "DELETE", path = "org/personnel/principal", hasBody = true)
     fun deleteDepHeadPosition(@Header(tokenKey)
                               token: String, @Body positionId: HashMap<String, String>): Flowable<Result<Any>>*/

    @HTTP(method = "DELETE", path = "org/personnel/principal/v2", hasBody = true)//tcp已修改
    fun deleteDepHeadPosition(@Header(tokenKey)
                              token: String, @Body positionId: HashMap<String, String>): Flowable<Result<Any>>

    //更改团队部门
    @PUT("org/company/dept/{companyId}/{deptId}")
    fun changeDepName(@Header(tokenKey) token: String,
                      @Path("companyId") companyId: String,
                      @Path("deptId") deptId: String,
                      @Body body: Any)
            : Flowable<Result<Any>>

    //更改团队部门
    @DELETE("org/company/dept/{companyId}/{deptId}")
    fun deleteDep(@Header(tokenKey) token: String, @Path("companyId") companyId: String,
                  @Path("deptId") deptId: String): Flowable<Result<Any>>

    //创建群组
    @POST("user/group")
    fun creatGroup(@Header(tokenKey) token: String, @Body body: Any): Flowable<Result<Any>>

    //查询群组
    @GET("user/group")
    fun getGroup(@Header(tokenKey) token: String): Flowable<Result<List<GroupBean>>>

    // 加入团队，获取申请详情tcp
    @GET("org/personnel/schedule/wait/{status}/{recordId}")
    fun getApplicationDetailData(@Header(tokenKey) token: String,
                                 @Path("status") status: Int,
                                 @Path("recordId") page: String)
            : Flowable<Result<Any>>

    //加好友，获取申请详情tcp
    @GET("relation/apply/v1/record/info/{applyId}")
    fun getApplicationDetailData(@Header(tokenKey) token: String,
                                 @Path("applyId") handleId: String)
            : Flowable<Result<Any>>

    //查询公告列表
    @GET("org/company/notice/{companyId}/{page}/{count}")
    fun getBulleList(@Header(tokenKey) token: String,
                     @Path("companyId") companyId: String, @Path("page") page: Int,
                     @Path("count") count: Int): Flowable<Result<List<BulleListBean>>>

    //得到公司权限列表
    @GET("org/company/manager/{companyId}")
    fun orgPermissionList(@Header(tokenKey) token: String,
                          @Path("companyId") companyId: String):
            Flowable<Result<List<OrgPermissionListBean>>>

    //编辑管理员
    @PUT("org/company/manager/v2/{companyId}")//tcp已修改
    fun editManager(@Header(tokenKey) token: String,
                    @Path("companyId") companyId: String,
                    @Body editCompanyManager: Any): Flowable<Result<Any>>

    //添加管理员
    @POST("org/company/manager/v2/{companyId}")//tcp已修改
    fun addManager(@Header(tokenKey) token: String,
                   @Path("companyId") companyId: String,
                   @Body addManagerModel: Any): Flowable<Result<Any>>

    //删除管理员
    @DELETE("org/company/manager/v2/{companyId}/{managerId}")//tcp已修改
    fun deleteManager(@Header(tokenKey) token: String,
                      @Path("companyId") companyId: String,
                      @Path("managerId") managerId: String): Flowable<Result<Any>>

    //获取团队权限和公告
    @GET("org/company/power/{companyId}")
    fun getWorkNoticeAndPower(@Header(tokenKey) token: String,
                              @Path("companyId") companyId: String): Flowable<Result<OrgNoticeAndPowerBean>>

    //获取短信短链接
    @GET
    fun getMsgShortUrl(@Header(tokenKey) token: String, @Url url: String):
            Flowable<Result<String>>

    //团队下负责人
    @GET("org/personnel/principal/{companyId}/{deptId}")
    fun showDeaHeadDetail(@Header(tokenKey) token: String,
                          @Path("companyId") companyId: String,
                          @Path("deptId") deptId: String): Flowable<Result<OrgDepHeadBean>>

    //请离员工校验
    @POST("task/project/checkUser")
    fun leaveUserValidate(@Header(tokenKey) token: String,
                          @Body users: HashMap<String, Any>): Flowable<Result<Any>>

    //批量待处理列表
    @GET("org/personnel/schedule/{companyId}/records")
    fun batchProcessUndoMsgList(@Header(tokenKey) token: String,
                                @Path("companyId") companyId: String): Flowable<Result<BatchProcessUndoBean>>

    //批量待处理列表,团队批量同意个人加入
    @PUT("org/personnel/v2/schedule/records")//tcp已修改
    fun batchProcessUndoMsg(@Header(tokenKey) token: String, @Body body: Any): Flowable<Result<Any>>

    //团队导入员工获取部门下的员工
    // TODO: 2021/6/24 接口数据接近  一个字段之差 level deptLevel
    /**一次查询所有部门和成员*/
//    @GET("org/personnel/schedule/{companyId}/{deptId}/members")
//    fun queryOrgDepMember(@Header(tokenKey) token: String,
//                          @Path("companyId") companyId: String,
//                          @Path("deptId") deptId: String): Flowable<Result<OrgImportDeptBean>>

    //团队获取部门下
    @GET("org/personnel/schedule/{companyId}/{deptId}/members")
    fun queryOrgDept(@Path("companyId") companyId: String,
                     @Path("deptId") deptId: String): Flowable<Result<OrgImportDeptBean>>
    // TODO: 2021/6/24 接口数据接近

    // TODO: 2021/7/9 14:35  按部门查询子部门和当前部门成员 ，使用下边的
    @GET("org/personnel/schedule/{companyId}/{deptId}")
    fun quitChartMember(@Path("companyId") companyId: String,
                        @Path("deptId") deptId: String): Flowable<Result<OrgChartBean>>

//    @GET("att/group/dept/{companyId}/{deptId}")
    @GET("att/group/dept/{deptId}/{companyId}")
    fun quitChartMemberNew(@Path("companyId") companyId: String,
                        @Path("deptId") deptId: String): Flowable<Result<TimeDeptSetingBean>>

    //得到考勤结果分享的图片数组
    @GET("att/picture")
    fun getAttendanceResultPicList(@Header(tokenKey) token: String): Flowable<Result<List<String>>>

    //添加外部联系人
    @POST("org/company/admin/external/contacts")
    fun addExternalContact(@Header(tokenKey) token: String, @Body body: Any): Flowable<Result<Any>>

    //修改外部联系人
    @PUT("org/company/admin/external/contacts")
    fun updateExternalContact(@Header(tokenKey) token: String, @Body body: Any)
            : Flowable<Result<Any>>

    //删除外部联系人
    @PATCH("org/company/admin/external/contacts")
    fun deleteExternalContact(@Header(tokenKey) token: String, @Body data: Any)
            : Flowable<Result<Any>>

    //查询外部联系人详情
    @GET("org/company/external/contacts/{companyId}/{userId}")
    fun getExternalContactDetail(@Header(tokenKey) token: String,
                                 @Path("companyId") companyId: String,
                                 @Path("userId") userId: String)
            : Flowable<Result<ExternalContactDetailBean>>

    //得到外部联系人列表
    @Deprecated("no used")
    @GET("org/company/external/contacts/info/{companyId}")
    fun getExternalContactList(@Header(tokenKey) token: String,
                               @Path("companyId") companyId: String,
                               @Query("keyword") keyword: String,
                               @Query("level") level: String,
                               @Query("type") type: String)
            : Flowable<Result<List<ExternalContactListBean>>>

    //修改合作联系人邮箱
    @PUT("org/company/external/contacts/company")
    fun commitCooperationOwnEmail(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>

    //退出合作团队
    @POST("org/company/external/contacts/company/exit")
    fun quitCooperationCompany(@Header(tokenKey) token: String, @Body body: Any)
            : Flowable<Result<Any>>

    //根据id查询合作申请详情
    @GET("org/company/external/contacts/info/show/{id}")
    fun getCooperationApplicationDetail(@Header(tokenKey) token: String, @Path("id") id: String):
            Flowable<Result<CooperationApplicationDetailBean>>

    //处理合作申请
    @POST("org/company/external/contacts/hand")
    fun dealCooperationApplication(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>

    //搜索外部联系人列表
    @GET("org/company/external/contacts/list/active/{companyId}")
    fun searchDepExternalMember(@Path("companyId") companyId: String):
            Flowable<Result<List<CompanyExternalMemberSearchBean>>>

    //发送激活邀请
    @POST("org/company/admin/external/contacts/message")
    fun sendApplicationMsg(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>

    //得到合作团队详情
    @GET("org/company/external/contacts/company/info/{companyId}")
    fun getCooperationCompanyDetail(@Header(tokenKey) token: String, @Path("companyId") companyId: String)
            : Flowable<Result<CooperationCopmanyDetailBean>>

    /**
     * workflow/approve/count  post请求   传个map对象，对象里面的key：organizationIds 是string数组 @iOS王园 @赵阳阳 安卓
     */
    @POST("workflow/approve/count")
    fun updateAprCount(@Body data: HashMap<String, Any>): Flowable<Result<List<Int>>>

    @GET("approve/kingdee/finance/token")
//    fun getKingDeeAccessToken(): Flowable<Result<KingDeeTokenBean>>
    fun getKingDeeAccessToken(): Flowable<Result<String>>

    @GET("kingdee/kingdee/finance/count")
    fun updateKingDeeTipCount(): Flowable<Result<Int>>

    //废弃了
    @GET("kingdee/kingdee/finance/listurl/{companyId}")
    fun getKingDeeListUrl(@Path("companyId") companyId: String): Flowable<Result<KingDeeListUrl>>

    //新接口，用来整合金蝶和交接班等功能org/company/group/{companyId}
    @GET("org/company/v3/group/{companyId}")
    fun getInteGrateData(@Path("companyId") companyId: String): Flowable<Result<InteGrateBean>>

    //拉取所有入口的未读数
    @GET("org/company/v2/group/message/{companyId}")
    fun getEntryListUnRead(@Path("companyId") companyId:String): Flowable<Result<InteGrateBean>>

    @GET("org/company/v2/group/{companyId}")
    fun getGrateDataLastTime(@Path("companyId") companyId:String): Flowable<Result<Long>>

    //测试环境时，商城用单独的接口，后期会整合到上面那个接口
    @GET("org/company/group/joinu/{companyId}")
    fun getShopingData(@Path("companyId") companyId: String): Flowable<Result<InteGrateBean>>
}