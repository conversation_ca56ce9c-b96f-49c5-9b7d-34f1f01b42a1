package com.joinutech.ddbeslibrary.api

import com.joinutech.common.base.tokenKey
import com.joinutech.ddbeslibrary.imbean.FriendApplyBean
import com.joinutech.ddbeslibrary.request.Result
import io.reactivex.Flowable
import retrofit2.http.*

interface UserApi {

//    //个人申请好友
//    @POST("relation/apply/v1")
//    fun verifyFriendApplication(@Header(tokenKey) token: String, @Body data: Any)
//            : Flowable<Result<Any>>
//
//    //获取好友申请列表
//    @GET("relation/apply/v1")
//    fun getFriendApplyList(@Header(tokenKey) token: String)
//            : Flowable<Result<ArrayList<FriendApplyBean>>>
//    @POST("user/friend/apply")
//    fun verifyFriendApplication(@Header(tokenKey)
//                                token: String, @Body data: Any)
//            : Flowable<Result<Any>>

//    //tcp同意好友申请api
////    @POST("user/friend")
////    fun agreeAddFriend(@Header(tokenKey)
////                       token: String, @Body data: Any)
////            : Flowable<Result<Any>>
//    @GET("relation/apply/v1/{applyId}/{opt}")
//    fun agreeAddFriend(@Header(tokenKey)
//                       token: String, @Path("applyId") handleId: String, @Path("opt") opt: Int)
//            : Flowable<Result<Any>>
//
//    //tcp拒绝好友申请api
////    @PUT("user/friend")
////    fun rejectAddFriendJoin(@Header(tokenKey)
////                            token: String, @Body data: Any)
////            : Flowable<Result<Any>>
//    @GET("relation/apply/v1/{applyId}/{opt}")
//    fun rejectAddFriendJoin(@Header(tokenKey)
//                            token: String, @Path("applyId") handleId: String, @Path("opt") opt: Int)
//            : Flowable<Result<Any>>

    //得到未处理好友消息列表
//    @GET("org/personnel/schedule/wait/{status}/{page}/{size}")
//    @Deprecated("merge to addressbook api getUndoList")
//    fun getFriendUndoList(@Header(tokenKey)
//                          token: String, @Path("status") status: Int,
//                          @Path("page") page: Int, @Path("size") size: Int)
//            : Flowable<Result<UndoBean>>

//    //得到好友列表
//    @Deprecated("改为通过好友版本获取，搜索使用本地数据库搜索")
//    @GET("user/friend/sort")
//    fun getFriendList(@Query("keyword") keyword: String): Flowable<Result<List<FriendBean>>>

//    /**获取好友数据版本信息*/
//    @GET("user/friend/version")
//    fun getFriendListVersion(): Flowable<Result<String>>

//    /**获取好友数据版本信息和好友列表*/
//    @GET("user/friend/cache")
//    fun getFriendListCacheV2(): Flowable<Result<FriendCacheData>>
//
//    /**todo 2.4.5 合并后废弃 获取外部联系人需要的简要好友列表 ->>user/friend/cache*/
//    @GET("user/friend/external/contacts")
//    fun searchExternalFriendList(@Header(tokenKey) token: String, @Query("keyword") content: String)
//            : Flowable<Result<List<FriendSimpleBean>>>

//    /**
//     * todo 获取好友数据版本信息和好友列表 合并以上两个接口
//     *  user/friend/cache
//     *  user/friend/external/contacts
//     */
//    @GET("user/friend/cache/v2")
//    fun getFriendListCache(): Flowable<Result<FriendCacheData>>

//    //删除好友
//    @HTTP(method = "DELETE", path = "relation/friends/v1", hasBody = true)
//    fun deleteFriend(@Header(tokenKey)
//                     token: String, @Body map: Any): Flowable<Result<Any>>

//    @PUT("relation/friends/v1")
//    fun remarkFriend(@Header(tokenKey)
//                     token: String, @Body data: Any)
//            : Flowable<Result<Any>>

//    /**个人申请添加好友次数校验*/
//    @POST("user/friend/volidate")
//    fun volidate(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>
}

data class GroupNoticeSet(
        val ids: List<String>,
        val userId: String,
        val version: String
)