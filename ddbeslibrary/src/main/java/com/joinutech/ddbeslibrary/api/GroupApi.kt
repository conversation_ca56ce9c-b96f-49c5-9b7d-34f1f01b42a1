package com.joinutech.ddbeslibrary.api

import com.joinutech.common.base.tokenKey
import com.joinutech.ddbeslibrary.bean.*
import com.joinutech.ddbeslibrary.request.Result
import io.reactivex.Flowable
import retrofit2.http.*

/**
 * @Description: 群组服务API
 * @Author: hjr
 * @Time: 2020/3/4 9:23
 * @packageName: com.joinutech.ddbeslibrary.api
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
interface GroupApi {
    //创建群组
//    @POST("group/group")
//    fun createGroup(@Header(tokenKey) accessToken: String, @Body body: Any)
//            : Flowable<Result<GroupCreateBean>>
//    @POST("relation/groups/v1")
//    fun createGroup(@Header(tokenKey) accessToken: String, @Body body: Any)
//            : Flowable<Result<GroupCreateBean>>
//
//    //修改群名
//    @PUT("relation/groups/v1")
//    fun updateGroupName(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>
//    @PUT("group/group")
//    fun updateGroupName(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>


//    //获取群组详情
////    @GET("group/group/{groupId}")
////    fun getGroupInfo(@Header(tokenKey) token: String, @Path("groupId") groupId: String)
////            : Flowable<Result<List<GroupInfoBean>>>
//    @GET("relation/groups/v1/{groupId}")
//    fun getGroupInfo(@Header(tokenKey) token: String, @Path("groupId") groupId: String)
//            : Flowable<Result<List<GroupInfoBean>>>

//    //分组获取群组列表
//    @GET("group/group/type/{type}")
//    fun getGroupList(@Path("type") type: Int): Flowable<Result<List<GroupListBean>>>
//
//    //搜索获取全部群组列表
//    @GET("group/group")
//    fun getAllGroupList(@Header(tokenKey) token: String): Flowable<Result<List<GroupInfoBean>>>

//    //修改群消息免打扰开关
//    @PUT("group/group/disturb")
//    fun changeGroupDisturb(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>

//    //邀请好友进群
//    @POST("relation/groups/v1/member")
//    fun inviteFriends(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>
//    @POST("group/group/member")
//    fun inviteFriends(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>

//    //删除群组成员
//    @HTTP(method = "DELETE", path = "relation/groups/v1/member", hasBody = true)
//    fun deleteGroupMembers(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>
//    @POST("group/group/member/delete")
//    fun deleteGroupMembers(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>

//    //转让群组
//    @PUT("relation/groups/v1")
//    fun transformCreate(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>
////    @PUT("group/group/assignment")
////    fun transformCreate(@Header(tokenKey) token: String, @Body data: Any): Flowable<Result<Any>>
//
//    //退出群组
//    @DELETE("relation/groups/v1/quit/{groupId}")
//    fun exitGroup(@Header(tokenKey) token: String, @Path("groupId") groupId: String)
//            : Flowable<Result<Any>>
//
//    //    @DELETE("group/group/quit/{groupId}")
////    fun exitGroup(@Header(tokenKey) token: String, @Path("groupId") groupId: String)
////            : Flowable<Result<Any>>
////解散群组
//    @DELETE("relation/groups/v1/{groupId}")
//    fun dissolveGroup(@Header(tokenKey) token: String, @Path("groupId") groupId: String)
//            : Flowable<Result<Any>>

//    //离线消息查询简略查询
//    @Deprecated("接口合并 user/user/start")
//    @GET("group/group/off/line")
//    fun getGroupOfflineMsg(): Flowable<Result<List<GroupOffLineMsgBean>>>

//    //获取群组具体的离线群组消息
//    @GET("group/group/off/line/info/{groupId}/{msgId}")
//    fun getGroupOfflineMsgList(@Path("groupId") groupId: String,
//                               @Path("msgId") msgId: String):
//            Flowable<Result<List<GroupMsgOffLineDetailBean>>>
}