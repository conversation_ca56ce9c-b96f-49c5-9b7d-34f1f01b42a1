package com.joinutech.ddbeslibrary.api

import io.reactivex.Flowable
import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.http.GET
import retrofit2.http.Streaming
import retrofit2.http.Url

//文件下载
interface DownLoadApi {
//    @Streaming
//    @GET
//    fun downloadFileCall(@Url fileUrl: String): Call<ResponseBody>

    @Streaming
    @GET
    fun downloadFile(@Url fileUrl: String): Flowable<ResponseBody>

    /**
     * app更新下载
     */
    @Streaming
    @GET
    fun downloadApk(@Url url: String?): Flowable<ResponseBody>
}