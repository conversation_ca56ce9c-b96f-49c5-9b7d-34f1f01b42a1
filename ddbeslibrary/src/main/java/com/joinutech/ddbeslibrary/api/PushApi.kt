package com.joinutech.ddbeslibrary.api

/**
 * @className: PushApi
 * @desc: TODO
 * @author: zyy
 * @date: 2019/7/29 10:38
 * @company: joinUTech
 * @leader: ke
 */
interface Push<PERSON><PERSON> {

//    @PUT("http://10.3.80.90:8085/huawei/token")
//    fun uploadPushToken(@Header("accessToken") token: String, @Body data: Any): Flowable<Result<Any>>
//
//    @DELETE("http://10.3.80.90:8085/huawei/token")
//    fun deletePushToken(@Header("accessToken") token: String, @Query("userId") userId : String): Flowable<Result<Any>>

}

data class PushToken(val userId : String, val token: String)

object PushServiceApi {
//    fun uploadPushToken(pushToken: String, userId : String, userToken: String): Flowable<Result<Any>> {
//        return RxScheduleUtil.rxSchedulerHelper(
//                DdbesApiUtil.getPushService().uploadPushToken(userToken, PushToken(userId, pushToken))
//        )
//    }
//
//    fun deletePushToken(userId : String, userToken: String): Flowable<Result<Any>> {
//        return RxScheduleUtil.rxSchedulerHelper(
//                DdbesApiUtil.getPushService().deletePushToken(userToken, userId)
//        )
//    }
}