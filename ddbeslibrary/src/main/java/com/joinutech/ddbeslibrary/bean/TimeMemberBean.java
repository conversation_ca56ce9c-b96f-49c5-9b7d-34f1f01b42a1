package com.joinutech.ddbeslibrary.bean;

import androidx.annotation.NonNull;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/3/12.
 */

public class TimeMemberBean implements Serializable {
    public List<PeopleBean> checkMembers;
    public  DeptMemBean deptList;
    public class PeopleBean implements Serializable{
        public String headimg;
        public String name;
        public String userId;
        public String position;
        public String positionId;
        public int status;//0：可添加，1：在其它考勤组,2：在此考勤组
        public int navstatus;//原始状态

        @Override
        public boolean equals(Object o) {//set集合能去重的原因
            if (o == this) return true;
            if (!(o instanceof PeopleBean)) {
                return false;
            }
            PeopleBean user = (PeopleBean) o;
            return user.userId.equals(userId);
        }

        @Override
        public int hashCode() {//17和31散列码重写hashcode用于去重
            int result=17;
            result=31*result+userId.hashCode();
            return result;
        }

        @Override
        public String toString() {
            return "PeopleBean{" +
                    "headimg='" + headimg + '\'' +
                    ", name='" + name + '\'' +
                    ", userId='" + userId + '\'' +
                    ", position='" + position + '\'' +
                    ", positionId='" + positionId + '\'' +
                    ", status=" + status +
                    ", navstatus=" + navstatus +
                    '}';
        }
    }

    public class DeptMemBean implements Serializable {
        public String name;
        public String deptId;
        public int status;
        public String parentId;
        public List<DeptMemBean> child;// 子部门信息
        public List<PeopleBean> members;// 当前部门成员
        public List<PeopleBean> allMembers;//所有成员
        public int navstatus;//原始状态
        public int  needSelAll;//0:原始状态 1：子集页面已经全选过 2:需要全选状态
    }
}
