package com.joinutech.ddbeslibrary.bean

import java.io.Serializable

data class ConversationDetailBean(
        val companyId: String,
        /**邀请码 会议码*/
        val code: String?,
        /*房间号*/
        val roomId: Int,
        val createTime: Long,
        val createUserId: String,
        val desc: String,
        val duration: String,
        val endTime: Long,
        val id: String,
        val meetingMember: List<MeetingMember>,
        val members: String,
        val startTime: String,
        //0进行中  1未开启 2已结束 3已取消
        val status: String,
        /**会议类型 1 语音 2 视频*/
        var type: String,
        val title: String
) : Serializable