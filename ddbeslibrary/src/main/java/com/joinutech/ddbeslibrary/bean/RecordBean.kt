package com.joinutech.ddbeslibrary.bean

/**
 * 打卡
 *<AUTHOR>
 *@date 2019/3/21
 * @param clockStatus 1代表当前时间不能打卡 2可以更新打卡 3缺卡
 * status:打卡状态 0未打卡 1正常 2迟到 3早退 4外勤
 *
 * 非工作日：
 * 未打卡：{"note":"","wifi":"","location":"","clockStatus":0}
 * 打卡后：{"note":"","wifi":"i-LFDZ","location":"银河公寓","clockStatus":2,
 * //增加字段："createTime":"10:01","clockId":"","sortNum":0,"id":"2500237623855940604","status":4}
 * 工作日：
 * 打卡前：{"ateId":"2500240298546824189","clockPre":1,"note":"","wifi":"","clockId":"2500240299620568060","location":"","clockStatus":0,【"deviceUnusual":0,？？】"clock":"09:00","per":"上班","status":2}
 * 打卡后：{"ateId":"2500238462448305149","clockPre":1,"note":"也是他","wifi":"","clockId":"2500238462448309244","location":"银河公寓","clockStatus":2,"clock":"09:00","per":"上班","status":2,
 * // 增加字段："createTime":"10:18","sortNum":0,"id":"2500238722293826556",【"sortS":"25002384624483051490" ？？】}
 *
 *
 * groupStatus：1 可以查看考勤组规则 0 不可查看，考勤组有更新
 * ateSchedule：考勤组类型0 固定 1 自由 2 排班
 * status：0：休息日 1：工作日
 * clockStatus：0 可以打卡 1 不可以打卡 2 可以更新打卡 3 缺卡
 * recordList.status：0 未打卡(clockStatus==1 or 3) 服务端返回当前打卡类型：1 正常 2 迟到 3 早退 4 外勤（本地判断后生成）
 * clockStatus==0 返回1,2,3; clockStatus==2 返回1,2,3,4 clockStatus==1 or 3 返回0，表示没有打卡
 *
 * 不能打卡：
 * {"status":1,"ateSchedule":0,"recordList":[{"clockStatus":1,"status":0},{"clockStatus":1,"status":0}]}
 * 可以打卡：
 * {"status":1,"ateSchedule":0,"recordList":[{"clockStatus":0,"status":1},{"clockStatus":1,"status":0}]}
 * 正常打卡后
 * {"status":1,"ateSchedule":0,"recordList":[{"clockStatus":2,"status":1},{"clockStatus":0,"status":3}]}
 *
 * 当前已迟到
 * {"status":1,"ateSchedule":0,"recordList":[{"clockStatus":0,"status":2},{"clockStatus":1,"status":0}]}
 * 迟到打卡后：
 * {"status":1,"ateSchedule":0,"recordList":[{"clockStatus":2,"status":2},{"clockStatus":0,"status":3}]}
 *
 * 早退打卡后：
 * {"status":1,"ateSchedule":0,"recordList":[{"clockStatus":1,"status":1},{"clockStatus":2,"status":3}]}
 *
 * // 上班卡缺卡，下班卡正常
 * {"status":1,"ateSchedule":0,"recordList":[{"clockStatus":3,"status":0},{"clockStatus":0,"status":1}]}
 *
 * 休息日：
 * {"status":0,"ateSchedule":0,"recordList":[{"clockStatus":0}]}
 * {"status":0,"ateSchedule":0,"recordList":[{"clockStatus":2,"status":4},{"clockStatus":0}]}
 *
 */
data class RecordBean(
        /**  99 休息日title
         * 未打卡时：1代表当前时间不能打卡 0可以打卡
         * 已打卡时：1 已打过卡或者不支持更新卡的结果 2 打卡后可以更新打卡 3缺卡*/
        var clockStatus: Int,
        /**考勤备注*/
        var note: String?,
        /**打卡位置信息*/
        var location: String,
        /**打卡WiFi信息*/
        var wifi: String,
        // ------------非工作日未打卡仅返回以上字段数据---------------
        /**考勤组ID*/
        var ateId: String,
        /** 1 表示当天 2表示次日*/
        var clockPre: Int,
        /**考勤时间 HH:mm 休息日不返回*/
        var clock: String,
        /**上班或下班*/
        var per: String,
        /**
         * 打卡前：工作日打卡时返回，非工作日不返回
         * 打卡后：都返回
         * 打卡状态 0未打卡,不能打卡 1正常 2迟到 3早退 4外勤，提交的打卡状态数据保存在此
         * {"code":1,"msg":"请求成功","data":{"ateId":"2468607709792961532","groupName":"加优考勤","scope":300,"status":0,"ateSchedule":0,"scopeList":[{"address":"经济技术开发区楼庄路9号","lng":116.734375,"location":"京津冀大数据创新应用中心","lat":39.60465}],"wifiList":[{"wifiName":"joinutech_5g-2","wifiId":"e8:fc:af:f6:0f:c6"},{"wifiName":"joinutech_5g-1","wifiId":"e8:fc:af:f6:0f:c7"},{"wifiName":"joinutech_2.4g","wifiId":"e8:fc:af:f6:0f:c5"},{"wifiName":"sspaas","wifiId":"ac:64:dd:56:18:15"},{"wifiName":"bieshu3#1-1","wifiId":"ac:64:dd:56:1b:98"}],"earlyClock":"20:30:00","lateClock":"05:30:00","groupStatus":1,"currentTime":"2020-09-26 10:32:54","clockDate":"2020-09-26","recordList":[{"note":"","wifi":"i-LFDZ","createTime":"10:01","clockId":"","sortNum":0,"location":"银河公寓","clockStatus":2,"id":"2500237623855940604","status":4},{"note":"","wifi":"","location":"","clockStatus":0}],"deptName":"","oldVersion":23,"oldAteId":"2468607709792961532","workTime":0,"isFind":true}}
         * {"code":1,"msg":"请求成功","data":{"ateId":"2500238462448305149","groupName":"工作日打卡测试","scope":500,"status":1,"ateSchedule":0,"scopeList":[{"address":"河北省廊坊市广阳区银河公寓(橙桔路西100米)","lng":116.6826239999999,"location":"银河公寓","lat":39.5963890973133}],"wifiList":[],"earlyClock":"08:00:00","lateClock":"21:00:00","groupStatus":1,"currentTime":"2020-09-26 10:35:30","clockDate":"2020-09-26","recordList":[{"ateId":"2500238462448305149","note":"也是他","wifi":"","clock":"09:00","clockPre":1,"createTime":"10:18","clockId":"2500238462448309244","sortNum":0,"location":"银河公寓","clockStatus":2,"id":"2500238722293826556","per":"上班","sortS":"25002384624483051490","status":2},{"ateId":"2500238462448305149","clockPre":1,"note":"","wifi":"","clockId":"2500238462448309244","location":"","clockStatus":0,"deviceUnusual":0,"clock":"18:00","per":"下班","status":3}],"deptName":"","oldVersion":1,"oldAteId":"2500238462448305149","workTime":0,"isFind":true}}
         *
         * status
         * 打卡前：非工作日不返回状态，即都允许打卡
         * 打卡前：工作日返回 0,1,2,3 三个状态，4 需要自己判断
         * 打卡后：返回上次打卡状态，可以更新打卡，更新打卡时需要根据当前状态判断新的考勤状态提交到服务端。
         *
         * clockStatus
         * 未打卡时：1 代表当前时间不能打卡 0 可以打卡
         * 已打卡时：1 已打过卡或者不支持更新卡的结果 2 打卡后可以更新打卡 3 缺卡
         */
        var status: Int,
        /**打卡id 非工作日打卡后才返回*/
        var clockId: String?,
        // ------------工作日未打卡返回以上字段数据---------------
        /**打卡后会存在此字段*/
        var createTime: String,
        /**打卡还是更新 判断字段，打过卡的记录才有这个字段*/
        var id: String = "",
        var sortNum: Int = 0,
        // ------------打过卡后返回以上字段数据---------------

        var needOutclockReviewAck : Int = 0,  // 0 不显示外勤标记， 1显示标记
        var outclockReviewStatus : Int = -1,  // 0审核中1已同意2已拒绝

)