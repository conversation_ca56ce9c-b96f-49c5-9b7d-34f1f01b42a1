package com.joinutech.ddbeslibrary.bean

/**
 *<AUTHOR>
 *@date 2019/3/20
 * @param status 0为休息日
 * groupStatus 0删除 1存在
 */
data class AttenHomeBean(
        /**k考勤组状态*/
        var groupStatus: Int,
        /**k考勤组id*/
        var ateId: String,
        /**2020-09-24 14:20:25*/
        var currentTime: String,
        var deptName: String,
        var groupName: String,
        var scope: Int,
        var earlyClock: String,
        var lateClock: String,
        /** 标识考勤组类型：0 固定 1 自由 2 排班*/
        val ateSchedule: Int,
        /**标识考勤日类型：0：休息日 1：工作日*/
        var status: Int,
        /**考勤打卡信息，包含打卡 缺卡 待打卡*/
        var recordList: List<RecordBean>,
        /**考勤WiFi数组*/
        var wifiList: List<WifiBean>,
        /**考勤位置范围数组*/
        var scopeList: List<ScopeBean>,
        /**当前打卡日期 2020-09-24 */
        var clockDate: String,
        var needPicture:Int , //1是拍照打卡，0不是拍照打卡

        var reviewAuth : Boolean = false  // 有权限， true 显示 审核tab /  没权限，不显示 审核tab
        )