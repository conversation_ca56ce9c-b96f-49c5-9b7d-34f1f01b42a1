package com.joinutech.ddbeslibrary.bean

import java.io.Serializable

data class SearchMemberBean(
        /**审批相关人员选择后，详情只返回前三个字段*/
        val headimg: String = "",//接口中字段
        val name: String = "",//接口中字段
        val userId: String = "",//接口中字段
        /**加审创建人*/
        val retrialCreator: String? = "",
        /**审批相关人员选择后，详情只返回前三个字段*/
        val createTime: Long = 0L,
        val mail: String = "",
        val phone: String = "",
        val positionLevel: Int = 0,//接口中字段
        val positionName: String = "",//接口中字段
        val initial: String = "",//接口中字段
        val serialVersionUID: String = "",//接口中字段
        /**---自定义字段---*/
        /**审批节点id*/
        val approveAssigneeId: String = "",
        /**审批详情中 节点审批意见，审批创建人不包含此字段*/
        val content: String = "",
        /**审批详情中审批节点操作结果  0未通过, 1通过, 2待审核, 3审批中, 4已退回, 999发起审批*/
        val opinion: Int = 3,
        /**节点包含人员*/
        var assignee: List<SearchMemberBean>? = null,
        /**节点类型：1：普通类型：2：会审；3：或审*/
        var approveType: Int = 1,
        /**标记是否已选择*/
        var select: Boolean = false,
        /**选择人员时，拼音分组被选中*/
        var isCurrentIndex: Boolean = false,
        /** 选择的人员是否是外部联系人 0 不是 1 是外部联系人*/
        var isOuter :Int = 0
) : Serializable

data class OrgPersonBean(
        var headimg: String = "",//接口中字段
        var initial: String = "",//接口中字段,名字全拼的首字母
        var name: String = "",//接口中字段
        var positionLevel: Int = 0,//接口中字段
        var positionName: String = "",//接口中字段
        var serialVersionUID: String = "",//接口中字段
        var userId: String = "",//接口中字段
        /**---自定义字段---*/
        var isSelect:Boolean=false//是否被选中的标记

) : Serializable
