package com.joinutech.ddbeslibrary.bean

import java.io.Serializable

/**
 * <AUTHOR>
 * @date   2019/5/8 14:47
 * @className: AppVersionBean
 *@Description: 类作用描述
 */
data class AppVersionBean(val downUrl: String, val versionName: String, val desc: String,
                          val currentDesc: String,
                          val status: Int,   /** 更新类型 1 必须 2 非必须*/
                          val auditing: Int? = 0  /**审核状态 0 审核中 1 审核通过*/
) {
        fun needUpdate() = status > 0

        fun underReviewing() = auditing == 0

}

/**app 首次提交信息后返回结果*/
data class AppStartResult(
        /**是否通过审核 0 审核中 1 通过*/
        val auditing: Int,
        /**当前版本描述*/
        val currentDesc: String,
        /**描述*/
        val desc: String,
        /**下载地址*/
        val downUrl: String,
        /**版本名称*/
        val versionName: String,
        /**更新类型 1 必须 2 非必须*/
        val status: Int,

//        /**群组离线消息*/
//        val group: List<GroupOffLineMsgBean>,
        /**开启免打扰 公司id数组*/
        val ids: HashSet<String>,
        /**系统通知免打扰开关 0 关闭 1 开启 */
        val systemPushSwitch: Int = 0,
        /**审批通知免打扰开关 0 关闭 1 开启 */
        val approvalPushSwitch: Int = 0,
        /**工单通知免打扰开关 0 关闭 1 开启 */
        val ticketPushSwitch: Int = 0,
        /**库存通知免打扰开关 0 关闭 1 开启 */
        val inventorySwitch: Int = 0,
        var trainingSwitch: Int = 0,
        var kingdeePushSwitch: Int = 0,
        /** 综合管理平台开关  0 关闭 1 开启 */
        var managementSwitch: Int = 0,

        val bannerList: List<Banner>,
        /**更新push token是否成功 1 成功 else 失败*/
        val updateToken: Int,
        /**
         * // TODO: 2021/1/13 17:44 2.4.6 之后版本增加，用于动态获取云存储区域和AppId
         */
        val region: String?,
        val appId: String?,
        /**网络图片拼接路径前缀 cdn 地址*/
        val pre: String?,
        /**
         * // TODO: 2021/4/26 14:45 新增客服电话字段，为空则不显示
         */
        val tel: String? = "",
        val linkWarn:String? ="",//点击IM消息的超链接时的警告提示语
        val linkWhiteList:ArrayList<String>? = arrayListOf()//点击IM超链接可以直接跳转的白名单

) : Serializable

data class Banner(
        /** bannerId 非 团队id companyId*/
        val id: String,
        /**图片地址*/
        val picture: String,
        /**banner名称*/
        val name: String? = "",
        /**图片链接*/
        val url: String
)

