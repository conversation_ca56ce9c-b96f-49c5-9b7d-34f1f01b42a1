package com.joinutech.ddbeslibrary.bean

import java.io.Serializable

/**
 * @Description: 上传文件Bean
 * @Author: hjr
 * @Time: 2020/1/15 16:39
 * @packageName: com.joinutech.ddbeslibrary.bean
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
data class UploadFileBean(var fileId: String = "", var hash: String = "",
                          var fileName: String = "", var fileUrl: String = "",
                          var isUploadFlag: Boolean = false,
                          var remoteUrl: String = "",
                          /**文件大小*/
                          var fileLength: Long = 0L,
                          /**上传文件创建时间*/
                          val time: Long = 0L,
                          var bucket: String = "",
                          // 进度
                          var process: Int = 0) : Serializable