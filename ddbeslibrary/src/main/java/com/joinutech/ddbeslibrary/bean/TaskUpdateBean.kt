package com.joinutech.ddbeslibrary.bean

/**
 * <AUTHOR>
 * @date   2019/4/24 14:27
 * @className: TaskUpdateBean
 *@Description: 类作用描述
 */
data class TaskUpdateBean(val endTime: String,
                          val level: Int,
                          val status: Int,
                          val taskDec: String,
                          val tagsId: List<String>,
                          val taskId: String,
                          var newOperator: String,
                          var taskMembers: List<String>,
                          val urls: List<Any>,
        /*编辑时，如果删除了所有的图片传1，其他情况传0*/
                          val isDel: Int = 0,
                          val difficult: Int,
                          val reward: Int,
                          var delUrls: ArrayList<String> = arrayListOf()
)