package com.joinutech.ddbeslibrary.bean

import java.io.Serializable

/**
 *<AUTHOR>
 *@date 2018/11/19
 */
data class CompanyBean(
        //多页面共用，暂时用于外部联系人的phone
        var address: String,
        //多页面共用，暂时用于外部联系人的email
        var area: String,
        var companyId: String,
        var deptId: String,
        var fax: String,
        var industry: String,
        var linkManMail: String,
        var linkManName: String,
        var linkManPhone: String,
        var logo: String?,
        var name: String,
        var officialWebsite: String,
        var postalcode: String,
        var profile: String,
        // 1 拒绝了申请
        var rejectJoin: Int,
        //1 拒绝邀请 0可以邀请
        var rejectInvitation: Int,
        var scale: String,
        var startTime: String,
        var type: String,
        // 1 同意了申请
        var isJoin: Int,
        var power: String,
        var count: Int,
        var createTime: String,
        var isOuter: Boolean = false, //adapter判断内外团队字段
        val logout: Int = 0// 0 正常 1 已注销
) : Serializable