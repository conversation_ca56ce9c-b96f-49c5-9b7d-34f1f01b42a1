package com.joinutech.ddbeslibrary.bean

import java.io.Serializable

data class WorkStationBean(
        /**团队id*/
        val companyId: String,
        /**团队部门id
         * 0    标识创建的团队
         * 其他 标识所在部门信息
         * */
        var deptId: String = "",
        /**团队logo*/
        val logo: String = "",
        /**团队名*/
        val name: String,
        /**公告内容*/
        var content: String = "",
        /**公告id*/
        var noticeId: String = "",
        /**团队权限*/
        var power: String = "",
        val rejectInvitation: Int = 0,
        val rejectJoin: Int = 0,
        var isSelected: Boolean = false,
        /*标记审批数量字段*/
        var haveApprove: Int = 0,
        var isOuter: Boolean = false
) : Serializable