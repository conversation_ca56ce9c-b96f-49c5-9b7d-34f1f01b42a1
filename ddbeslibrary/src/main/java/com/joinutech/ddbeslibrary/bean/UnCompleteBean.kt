package com.joinutech.ddbeslibrary.bean

data class UnCompleteBean(
        val taskDec: String,
        /**任务级别:0:普通，1:紧急 2: 非常紧急*/
        val level: Int,
        val tags: List<TaskLabelCreateBean>,
        val closeTime:String,
        val endTime:String,
        val avatar:String,
        val userName:String,
        val userId:String,
        val taskId:String,
        val status:Int,
        val projectLogo:String,
        val projectId:String,
        val proName:String,
        val parentTask:String,
        val reward:Int,
        val quantizationParameter:Int
)