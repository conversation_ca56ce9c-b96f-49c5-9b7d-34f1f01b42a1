package com.joinutech.ddbeslibrary.bean;

import java.io.Serializable;

/**
 * description ： 导入人员Bean类
 * author: 黄洁如
 * date : 2019/10/23
 */
public class OrgImportPeopleBean implements Serializable {
    public String userId;
    public String name;
    public String headimg;
    public String mobile;
    public String positionName;

    public int status;//0：可添加，1：在其它考勤组,2：在此考勤组
    public int navstatus;//原始状态

    @Override
    public boolean equals(Object o) {//set集合能去重的原因
        if (o == this) return true;
        if (!(o instanceof OrgImportPeopleBean)) {
            return false;
        }
        OrgImportPeopleBean user = (OrgImportPeopleBean) o;
        return user.userId.equals(userId);
    }

    @Override
    public int hashCode() {//17和31散列码重写hashcode用于去重
        int result=17;
        result=31*result+userId.hashCode();
        return result;
    }
}
