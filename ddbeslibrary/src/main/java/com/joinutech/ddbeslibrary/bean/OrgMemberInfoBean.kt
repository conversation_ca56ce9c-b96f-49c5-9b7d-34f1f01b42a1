package com.joinutech.ddbeslibrary.bean

data class OrgMemberInfoBean(
        val address: String,
        val birthday: String,
        val email: String,
        val gender: Int,
        val mobile: String,
        val profession: String,
        val companyId: String,
        val commonNames: String,
        val companyName: String?,
        val count: Int,
        val isShow: Boolean,
        val headimg: String?,
        val name: String?,
        /**TODO 0 不是好友 1 单向好友 2 双向好友 本人时为2*/
        /**是否为好友*/
        val isFriend: Boolean = false,
        /**当前审批状态*/
        val approveType: Int = 0,
        val remark: String?,
        val imId: String,  //新版im新加字段
        val logout: Int = 0, //用户是否注销字段 0正常 1注销
        val userStatus: Int = 0//用户是否被投诉了，0代表正常，1代表被投诉三次及以上，2代表被封禁
)