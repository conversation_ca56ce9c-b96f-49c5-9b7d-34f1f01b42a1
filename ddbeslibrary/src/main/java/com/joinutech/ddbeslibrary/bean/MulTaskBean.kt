package com.joinutech.ddbeslibrary.bean

/**
 *<AUTHOR>
 *@date 2019/4/24
任务状态status：任务状态：1.未开始，2.进行中，3.已完成
任务del_status：任务删除状态：1.正常，2.回收站，3.彻底删除
任务优先级level：优先级别：0.普通；1.紧急；2.严重
 */
data class MulTaskBean(val taskId: String,
                       val taskDec: String,
                       val parentTask: String,
                       val completeSonTask: Int,
                       val tags: List<TaskLabelCreateBean>,
                       val projectId: String,
                       val sonTask: Int = 0,
                       val parentTaskId: String,
                       val startTime: String,
                       val endTime: String,
                       val closeTime: String,
                       val creatorId: String,
                       val managerId: String,
                       val userMembers: List<TaskDetailMemberBean>,
                       val userName: String,
                       val avatar: String,
                       val level: Int = 0,
                       val status: Int,
                       val updateStatusTime: String,
                       val delStatus: Int,
                       val reward: Int = 0,
                       val delTime: String,
        //关联任务状态type:0前置 1后置 2并行 3其他
                       val relationType: Int = 0)