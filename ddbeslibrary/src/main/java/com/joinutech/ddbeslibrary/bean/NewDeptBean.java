package com.joinutech.ddbeslibrary.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Created by l<PERSON><PERSON><PERSON> on 2018/3/30.
 */
public class NewDeptBean implements Serializable {
    public String parentId;
    public List<NewDeptBean> sublevel;
    public String deptId;
    public String name;
    /**
     * 0 未选中
     * 1 保持选中状态
     * 2 被选中
     */
    public int status;
    public String companyId;
    public int deptlevel;
    public int navstatus;//保存原始status
    public int choose;//0:原始状态  1:点击取消 2:点击选择
    public boolean option;//保存用户在子集页面的操作

    @Override
    public boolean equals(Object o) {//set集合能去重的原因
        if (o == this) {
            return true;
        }
        if (!(o instanceof NewDeptBean)) {
            return false;
        }
        NewDeptBean bean = (NewDeptBean) o;
        return bean.deptId.equals(deptId);
    }

    @Override
    public int hashCode() {//17和31散列码重写hashcode用于去重
        int result = 17;
        result = 31 * result + deptId.hashCode();
        return result;
    }
}
