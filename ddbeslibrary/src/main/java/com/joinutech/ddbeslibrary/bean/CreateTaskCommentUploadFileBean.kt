package com.joinutech.ddbeslibrary.bean

/**
 * @Description: 上传文件Bean
 * @Author: hjr
 * @Time: 2020/1/15 16:39
 * @packageName: com.joinutech.ddbeslibrary.bean
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
/**
 * @Group: Copyright (C),2017- 2020,JoinuTech
 * @PackageName: com.joinutech.ddbeslibrary.bean
 * @ClassName: CreateTaskCommentUploadFileBean
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2021/1/25 8:43
 * @Desc: //TODO 上传文件到云盘时，文件提交参数：审批图片，审批评论图片，创建任务图片，任务评论图片
 */
data class CreateTaskCommentUploadFileBean(var fileId: String,
                                           val hash: String,
                                           var fileName: String,
//                                           /**存储桶*/
//                                           val bucket: String,
                                           /**文件类型*/
                                           var fileType: Int = 0) {

    fun getTaskData(): Map<String, String> {
        return hashMapOf("fileId" to fileId, "hash" to hash, "fileName" to fileName)
    }
}