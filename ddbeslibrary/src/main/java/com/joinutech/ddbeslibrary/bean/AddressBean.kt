package com.joinutech.ddbeslibrary.bean

/**金蝶审批列表url*/
data class KingDeeListUrl(
        val webUrl: String?
)

/**金蝶访问服务token*/
data class KingDeeTokenBean(
        val token: String,
        val expireTime: Long
)


//金蝶和交接班整合新接口，访客管理也整合进来了
data class InteGrateBean(
        val toolsList: List<EntroyBean> = arrayListOf()
)

data class EntroyBean(
        var icon:String="",
        var name:String="",
        var showInt:Int=0,//0代表不显示new标签，1代表显示new标签
        var typeInt:Int=2,
        var unreadInt:Int=0,
        var webUrl:String="",
        var backspaceKey:Int=0,//移动端是否显示返回键，，1是展示，0是不展示；
        var key:String="",//首页整合接口中的模块对应的key
        var refreshTime:Long=0L,
        var kingdeeTokenType:Int=0//1是不拼接token，2是拼接token
)