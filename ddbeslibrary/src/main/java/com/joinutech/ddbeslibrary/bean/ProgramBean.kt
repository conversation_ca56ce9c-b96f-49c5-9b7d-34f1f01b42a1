package com.joinutech.ddbeslibrary.bean

import java.io.Serializable

/**
 *<AUTHOR>
 *@date 2019/4/22
 */
data class ProgramBean(var createTime: String,
                       /**0.项目创建者；1.项目管理人；2.项目参与人*/
                       var identity: Int,
                       var num: Int,
                       var projectDec: String, var projectId: String, var projectLogo: String,
                       var projectName: String, var status: Int, var total: Int, var complete: Int,
                       var nostart: Int, var lastTaskTime: String, var running: Int,
                       var isSelected: Boolean = false, var creatorName: String, var managerName: String,
                       var creatorId: String, var managerAvatar: String, var delTime: String,
                       var managerId: String, var isDeletedSelect: Boolean = false,
                       var selectMemberName: String = "", var selectMemberUserId: String = "",
                       var creatorAvatar: String,
                       /**量化参数开关*/var quantizationParameter:Int =0,
                       /**创建自由认领任务权限*/var isClaim:Int =1
) : Serializable