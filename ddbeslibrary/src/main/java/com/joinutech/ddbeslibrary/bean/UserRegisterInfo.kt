package com.joinutech.ddbeslibrary.bean

/**
 * @className: UserRegisterInfo
 * @desc: 用户完善信息提交
 * @author: zyy
 * @date: 2019/9/9 18:51
 * @company: joinUTech
 * @leader: ke
 */

//                @{@"name":_nameTextField.text,
//                @"password":_pwdTextField.text,
//                @"mobile":self.telStr,
//                @"wxAuthId":openidDic[@"unionid"],
//                @"wxOppenId":openidDic[@"openid"],
//                @"type":@(1),
//                @"nickName":openidDic[@"nickname"]};
class UserRegisterInfo(
        val mobile: String,
        val name: String,
        val password: String,
        val nickName: String = "",
        val wxAuthId: String = "",
        val wxOppenId: String = "",
        /*1担当 2云资产*/
        val type: Int = 1
) {
    fun getData(withWx: Boolean = false): Map<String, Any> {
        val map = hashMapOf<String, Any>()
        map["name"] = name
        map["mobile"] = mobile
        map["password"] = password
        //type:1担当 2云资产
        map["type"] = type
        if (withWx) {
            map["nickName"] = nickName
            map["wxAuthId"] = wxAuthId
            map["wxOppenId"] = wxOppenId
        }
        return map
    }
}