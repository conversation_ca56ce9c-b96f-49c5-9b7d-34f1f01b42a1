package com.joinutech.ddbeslibrary.bean

/**
 * @Description: 网盘文件Bean
 * @Author: hjr
 * @Time: 2020/1/16 10:00
 * @packageName: com.joinutech.ddbeslibrary.bean
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
data class PanFileBean(
        /**文件id，网盘中唯一标识*/
        val id: String,
        /**文件唯一key*/
        val key: String,
        /**生成的文件的hash值，方便匹配本地文件*/
        val hash: String,
        /** 文件是否已存在*/
        val exist: Boolean,
        /**文件的存储桶信息*/
        val bucket: String? = "")

/** 云文档 文件上传后 更新数据到后台*/
data class PanUploadBean(
        /**文件hash*/
        val hash: String = "",
        /**文件名*/
        val name: String = "",
        /**文件父目录id*/
        val parentId: String = "",
        /**文件的id or key
         * @see  PanFileBean.id
         * */
        val tmpName: String = "",
        /**关联云文档*/
        val bucket: String = ""
)

/**云盘文件上传后回调结果*/
data class PanUploadResult(
        val createTime: Long,
        val deletable: Boolean,
        val deleteTime: Int,
        val deptId: String,
        val fileId: String,
        val fileType: Int,
        val focus: Boolean,
        val name: String,
        val orgId: String,
        val parentId: String,
        val `property`: Int,
        val route: List<Any>,
        val size: Int,
        val tags: List<Any>,
        val updateTime: Long,
        val url: String,
        val user: String,
        val userId: String
)

/**云存储 云盘 存储桶配置 */
data class CosBucketConfig(
        val appId: String,
        val bucket: String,
        var region: String,
        /**拼接文件路径前缀*/
        val pre: String
)