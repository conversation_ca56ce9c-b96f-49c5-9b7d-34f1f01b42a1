package com.joinutech.ddbeslibrary.bean

import java.io.Serializable

/**
 * @Description: 好友选择bean
 * @Author: hjr
 * @Time: 2020/2/24 11:53
 * @packageName: com.joinutech.addressbook.adapter.bean
 * @Company: Copyright (C),2017- 2020,JoinuTech
 * @see GroupCallActivity
 * @see GroupChatMessageActivity
 *
 * @see FriendSelectViewModel
 * @see SelectWithSearchListWithBottomShowActivity2
 *
 * @see FriendSelectSearchAdapter
 * @see FriendSelectWithSearchListActivity2
 * @see SelectWithSearchListWithBottomShowActivity
 * @see WithDataListSelectActivity
 */
data class FriendSelectBean(

        var userId: String = "",
        var name: String = "",
        var remark: String = "",
        var avatar: String = "",
        /**
         * 索引字符
         */
        var initial: String? = null,
        /**
         * 排序名
         */
        var sortName: String? = null,
        var birthday: String? = null,
        var gender: String? = null,
//        /**
//         * 标识当前用户，存储用户的好友信息
//         */
//        var ownUserId: String? = null,
//        /**
//         * 邮箱
//         */
//        var email: String? = null,
        /**
         * 手机号
         */
        var phone: String? = null,
        /**
         * 好友是否注销 0 正常 1 已注销
         */
        var logout: Int? = null,
        /**标记是否已选择*/
        var select: Boolean = false,
        /**选择人员时，拼音分组被选中*/
        var isCurrentIndex: Boolean = false
) : Serializable