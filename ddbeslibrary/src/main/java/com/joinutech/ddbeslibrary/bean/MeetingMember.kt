package com.joinutech.ddbeslibrary.bean

import java.io.Serializable

data class MeetingMember(
        val userId: String,
        val name: String,
        val avatar: String,
        /**参会人员所有团队信息*/
        val comapnies: List<String>,
        val meetingId: String,
        /**1 已参会 0 未参会*/
        val status: String,
        /**是否允许邀请*/
        var invitable: Boolean,
        /**图标显示0 无 1 音频 2 视频 */
        var flag: Int = 0
) : Serializable