package com.joinutech.ddbeslibrary.bean

data class TaskBoardBean(
        val avatar: String,
        val closeTime: String,
        val completeSonTask: Int,
        val creatorId: String,
        val endTime: String,
        /**任务级别:0:普通，1:紧急 2: 非常紧急*/
        val level: Int,
        val managerId: String,
        val parentTask: String,
        val parentTaskId: String,
        val projectId: String,
        val sonTask: Int,
        val startTime: String,
        /**完成状态: 1:未开始 2:进行中 3:已完成*/
        val status: Int,
        val tags: List<TaskLabelCreateBean>,
        val taskDec: String,
        val taskId: String,
        val updateStatusTime: String,
        val userName: String,
        val reward:Int =0
)