package com.joinutech.ddbeslibrary.bean

import java.io.Serializable

data class ProjectJoinerBean(
        val avatar: String,
        val doingTaskNum: Int,
        val doneTaskNum: Int,
        /**权限标记 0 创建 1 负责人 2 参与人*/
        var identity: Int,
        val status: Int,
        val todoTaskNum: Int,
        val userId: String,
        var userName: String,
        /**职位信息*/
        val positionName: String,
        var isSelected: Boolean = false,
        var isClaim: Int = 0
) : Serializable