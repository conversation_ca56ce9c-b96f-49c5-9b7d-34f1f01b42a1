package com.joinutech.ddbeslibrary.bean

import java.io.Serializable

data class OrgChartBean(
        /**公司部门层级*/
        val deptLevel: Int,//当前部门等级
        /**TODO: 2021/7/9 15:22  公司创建者id*/
        val userId: String,//用户id
        /**公司名*/
        val deptName: String,//当前部门

        //当前部门下所有总人数，包含子部门
        val count:Int=0,
        /**当前部门子部门*/
        val branchList: List<Branch> = arrayListOf(),
        /**当前部们成员*/
        val memberList: List<Member> = arrayListOf()
) : Serializable