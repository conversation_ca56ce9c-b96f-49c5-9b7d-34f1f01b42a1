package com.joinutech.ddbeslibrary.bean

import java.io.Serializable

open class ImageMsgBean(val w: Int = 0, val h: Int = 0, val url: String = "", val cmdId: String = ""
                        , val msgId: String = "",
    ) : Serializable {
    open var filedId: String? = ""
    open var isOrigin: Boolean? = false
    open var size: Long? = 0
    }

class ImageTransMsgBean(w: Int = 0, h: Int = 0, url: String = "",
                        val messageId: String = "", val targetId: String = ""
                        , val uuid: String = "",
) : ImageMsgBean(w, h, url ), Serializable {

}