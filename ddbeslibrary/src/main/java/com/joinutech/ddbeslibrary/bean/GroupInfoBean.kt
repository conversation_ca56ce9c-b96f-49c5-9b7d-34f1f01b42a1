package com.joinutech.ddbeslibrary.bean

import com.joinutech.common.util.UserHolder

data class GroupInfoBean(
    var createTime: Long = 0L,
    var createUserId: String = "",
    var groupId: String = "",
    var logo: String = "",
    var name: String = "",
    var orgId: String = "",
    /**消息免打扰 1 未开启 2 开启*/
    var receiveMode: Int = 0,
    var type: Int = 0,  //群类型 1公司群组， 2私有群组
    var status: Int = 0,//0代表群组正常，1代表群组已解散
    var initial: String = "",
    var searchMatchUsers: List<GroupMemberBean> = arrayListOf(),
    var users: List<GroupMemberBean> = arrayListOf(),//用这个字段判断当前用户是否在群组中
    var banned: Int = 0//代表群组的被投诉状态，0代表正常，1代表被投诉3次及以上，2代表被封禁
    , var addMember: Int = 0 // 仅群主和管理可以添加群成员 0不开启 1开启
    , var hintMember: Int = 0 // 仅群主和管理可以@所有人 0不开启 1开启
    , var voice: Int = 0 // 仅群主和管理开启语音通话 0不开启 1开启
) {

    fun ownGroupManagePermission(): Boolean {
        if (isCompanyGroup()) return false
        if (users.isEmpty()) return false
        val uid = UserHolder.getUserId() ?: ""
        val selfs = users.filter { it.id == uid }
        if (selfs.isEmpty()) return false
        val self = selfs.first()
        return self.identity == 1  // 只有群主可以看到群管理入口
    }

    fun isCompanyGroup(): Boolean = type == 1

    // 是否有删成员的权限
    fun canDelMember(): Boolean {
        if (isCompanyGroup()) return false
        if (users.isEmpty()) return false
        val uid = UserHolder.getUserId() ?: ""
        val selfs = users.filter { it.id == uid }
        if (selfs.isEmpty()) return false
        val self = selfs.first()
        return self.identity == 1 || self.identity == 2
    }

}

fun GroupInfoBean?.canAddMember(): Boolean {
    if (this == null) return false
    val uid = UserHolder.getUserId() ?: ""
    val u = users?.filter { it.id == uid }?.firstOrNull()
    if (u == null) return false
    if (u.identity > 0) return true
    return addMember != 1
}

