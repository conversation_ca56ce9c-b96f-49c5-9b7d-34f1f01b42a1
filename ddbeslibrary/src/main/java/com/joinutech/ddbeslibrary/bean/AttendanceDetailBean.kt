package com.joinutech.ddbeslibrary.bean

data class AttendanceDetailBean(
        var ateId : String,
        //考勤规则 0固定排班
        var ateSchedule: Int,
        //考勤日期状态 0休息1工作
        var clockDateStatus: String,
        //考勤时间概览
        var content: String,
        //参与部门
        var ateDept: List<AttendanceDepBean>,
        //考勤组名
        var ateGroupName: String,
        //自定义考勤日期
        var ateHolidaySelves:List<AteHolidaySelf>,
        //考勤人员
        var ateMember:List<AttendanceMemberBean>,
        //考勤地点
        var atePlace: List<AttendanceLocationBean>,
        //考勤wifi
        var ateWifi: List<WifiBean>,
        //公司ID
        var companyId : String,
        //部门是否为空
        var deptIsNull:Boolean,
        //考勤时间
        var dutyClock:List<AteClockBean>,
        //提前时间(显示使用)
        var  early:String,
        //延迟时间(显示使用)
        var  late:String,
        //提前打卡时间
        var earlyClock: String,
        //延后打卡时间
        var lateClock: String,
        //最早打卡 1当日 2前日
        var earlyPre: Int,
        //是否开启节假日 0否1是
        var isOpenHoliday:Int,
        //是否开启外勤打卡 0否1是
        var isOutClock:Int,
        //是否开启打卡提醒 0否1是
        var isRmd:Int,
        //最晚打卡 1当日 2次日
        var latePre: Int,
        //指定成员是否为空 false否,true是
        var memberIsNull: Boolean,
        //提醒时间
        var rmdTime:Int,
        //定义打卡范围
        var scope:Int,
        //是否是拍照打卡,1是，0不是
        var needPicture:Int,
        //是否开启外部审核，1是开启，0是关闭
        var needOutclockAck:Int,//
        var ateOutclockReviewList :List<AttendApproverReq>?  ,//外部审批人列表  添加新增的时候使用，

        var outClockReviewInfoList :ArrayList<AttendApprover>?  //外部审批人列表   请求的时候返回的



)