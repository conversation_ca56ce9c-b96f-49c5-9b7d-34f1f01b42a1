package com.joinutech.ddbeslibrary.bean

data class TaskLogListBean(
        /*日志json*/
//{"urls":["https://ddbes-task-1257239906.cos.ap-beijing.myqcloud.com/4073025347995417__1561450929217_1.png"],
// "trendsId":4466539809276928,
// "createTime":"2019-06-25 16:22:09","name":"上官海棠好看",
// "commentId":0,
// "avatar":"http://ddbes.cdn.joinutech.com/img_155738004962844133.png",
// "type":1,"userId":4073025347995417,
// "taskId":4466539806245888,
// "content":"上官海棠好看创建了任务【aaa】"},

        /*评论json*/
// {"urls":[],
// "trendsId":4466642666554368,
// "createTime":"2019-06-25 18:06:47",
// "name":"上官海棠好看",
// "commentId":4466642667635712,
// "avatar":"http://ddbes.cdn.joinutech.com/img_155738004962844133.png",
// "type":0,
// "userId":4073025347995417,
// "taskId":4466539806245888,
// "content":"评论1"}

        /**任务id*/
        val taskId: String,
        /**日志id*/
        val trendsId: String,
        /**评论id*/
        val commentId: Long,
        /**人员id*/
        val userId: String,
        /**姓名*/
        val name: String,
        /**头像*/
        val avatar: String,
        /**日志内容*/
        val content: String,
        /**创建时间*/
        val createTime: String,
        /**type:0->评论，1->日志*/
        val type: Int,
        /**文件数组*/
        val urls: ArrayList<FileCustomBean?>
)

/**
 * 发表评论
 */
data class TaskTendBean(val parentCommentId: String,
                        val taskId: String,
                        val content: String,
                        val urls: List<CreateTaskCommentUploadFileBean>)