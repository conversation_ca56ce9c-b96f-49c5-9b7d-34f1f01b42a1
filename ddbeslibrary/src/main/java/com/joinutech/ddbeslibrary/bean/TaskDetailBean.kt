package com.joinutech.ddbeslibrary.bean

import java.io.Serializable

data class TaskDetailBean(
        val avatar: String,
        val creatorId: String,
        val endTime: String,
        val level: Int,
        val tags:List<TaskLabelCreateBean>,
        /**1：正常 2：回收站，3：彻底删除*/
        val delStatus: Int,
        val taskDec: String,
        val taskId : String,
        val updateStatusTime: String,
        val userMembers: List<TaskDetailMemberBean>,
        val userName: String,
        //0 项目创建者 1 项目负责人 2 项目参与人
        val proIdentity: Int,
        //1 任务创建者 3 任务负责人 2 任务参与人 4 任务无关人员
        val taskIdentity: Int,
        val urls: ArrayList<UploadFileBean>,
        val sonTaskByType :List<ChildTaskBean>,
        val parentTask:String,
        val projectStatus:Int,
        val reward:Int,
        val type:Int,
        val difficult:String,
        val isClaim:Int,
        val quantizationParameter:Int,
        //关联的任务
        val relationCount:Int,
        //被关联任务
        val associateCount:Int,
        val companyId:String =""
) : TaskJumpBean(), Serializable

open class TaskJumpBean(
        var status: Int = 1,
        var projectId: String = "",
        var projectName: String? = "",
        //是否可以创建自由认领任务
        var freeClaimBoolean: Boolean = false,
        //是否开启量化参数
        var quantizationParameterBoolean: Boolean = false,
        var taskCurrentCompanyId: String = "",
        var taskType: String = "create"//type:create,update,quickCreate,recycle
) : Serializable