package com.joinutech.ddbeslibrary.bean

import java.io.Serializable

/**
 * Created by Administrator on 2016/10/25 0025.
 */
/**
 * name : 餐饮
 * industryId : 41
 * son : [{"id":42,"name":"服务员","parentId":41,"type":1},
 * {"id":43,"name":"送餐员","parentId":41,"type":1},
 * {"id":44,"name":"厨师/厨师长","parentId":41,"type":1}]
 * parentId : 0
 * type:类型//是否被选中
 */
/**
 * id : 42
 * name : 服务员
 * parentId : 41
 * type : 1
 */
data class JobChoiceBean(var name: String? = null,
                         var industryId: Int = 0,
                         var type: Int = 0,
                         var parentId: Int = 0,
                         var son: List<IndustrysBean>? = null) : Serializable {


    data class IndustrysBean (var id: Int = 0,
                              var name: String? = null,
                              var parentId: Int = 0,
                              var type: Int = 0): Serializable
}
