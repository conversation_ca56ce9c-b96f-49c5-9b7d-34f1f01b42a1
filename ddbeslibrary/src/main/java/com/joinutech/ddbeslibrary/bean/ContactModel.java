package com.joinutech.ddbeslibrary.bean;


import android.graphics.Bitmap;

import com.joinutech.ddbeslibrary.widget.wavesidebar.FirstLetterUtil;

import java.io.Serializable;

/**
 * 联系人model实体类
 */

public class ContactModel implements Serializable {
    private String index;
    private String name;
    private String logo;
    private Boolean isSelect;
    private String phoneNum;
    private int status;
    private String userId;
    private Bitmap bitmap;
    private int relation;
    /**
     * 选中状态
     */
    private Boolean isCheck;
    /**
     * 不可选状态
     */
    private Boolean noSelect = false;
    private int sessionType = 1;//tcp新添加
    private String sessionId = "";//tcp新添加
    private int chatStatus = 0;//tcp新添加

    public void setChatStatus(int chatStatus) {
        this.chatStatus = chatStatus;
    }

    public int getChatStatus() {
        return chatStatus;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public int getSessionType() {
        return sessionType;
    }

    public void setSessionType(int sessionType) {
        this.sessionType = sessionType;
    }
//    private Boolean isCurrentIndex = false;

    public ContactModel(String name, String logo) {
        this.index = FirstLetterUtil.getFirstLetter(name);
        this.name = name;
        this.logo = logo;
        this.isSelect = false;
        this.status = 0;
        this.isCheck = false;
        this.noSelect = false;
    }

    public Boolean getNoSelect() {
        return noSelect;
    }

    public void setNoSelect(Boolean noSelect) {
        this.noSelect = noSelect;
    }

    public int getRelation() {
        return relation;
    }

    public void setRelation(int relation) {
        this.relation = relation;
    }

    public Boolean getCheck() {
        return isCheck;
    }

    public void setCheck(Boolean check) {
        isCheck = check;
    }

    public Bitmap getBitmap() {
        return bitmap;
    }

    public void setBitmap(Bitmap bitmap) {
        this.bitmap = bitmap;
    }

    public String getPhoneNum() {
        return phoneNum;
    }

    public void setPhoneNum(String phoneNum) {
        this.phoneNum = phoneNum;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getIndex() {
        return index;
    }

    public String getName() {
        return name;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public Boolean getSelect() {
        return isSelect;
    }

    public void setSelect(Boolean select) {
        isSelect = select;
    }

//    public Boolean getCurrentIndex() {
//        return isCurrentIndex;
//    }
//
//    public void setCurrentIndex(Boolean currentIndex) {
//        isCurrentIndex = currentIndex;
//    }
}
