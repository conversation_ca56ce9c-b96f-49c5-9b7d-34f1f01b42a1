package com.joinutech.ddbeslibrary.bean

import com.google.gson.annotations.SerializedName

data class CredentialsX(
        val sessionToken: String,
        val tmpSecretId: String,
        val tmpSecretKey: String
)

//data class TencentSessionBean(
//        /**存储桶 ddbes-pan-tmp-1257239906*/
//        val bucket: String = "",
//        val credentials: CredentialsX,
//        val expiredTime: Long //临时密钥有效截止时间戳，单位是秒
//)

data class TencentSessionBean(
        /**存储桶 ddbes-pan-tmp-1257239906*/
//"bucket":"im-resources-dev",
//"imBucket":"im-resources-dev",
//"userBucket":"ddbes-repository",
//"userEndpoint":"ddbes-repository",

        /**user 开放访问logo等存储 eg: ddbes-repository-1304188286*/
        @SerializedName("userBucket")
        val openBucket: String? = "",

        /**网盘存储 eg: ddbes-pan-tmp-1304188286*/
        @SerializedName("bucket")
        val panBucket: String = "",

        /**im相关 eg: im-resources-dev-1304188286*/
        val imBucket: String? = "",

        /**文件预览? eg: ddbes-pan-test-1304188286*/
        val perBucket: String? = "",

        /**cdn 存储 eg: cdn.ddbes.com*/
        @SerializedName("userEndpoint")
        val cdnBucket: String? = "",
        val imEndpoint: String? = "",
        val credentials: CredentialsX,
        val expiredTime: Long //临时密钥有效截止时间戳，单位是秒
)

/**下载文件token*/
data class DownloadFileSession(
        val bucket: String,
        val fileId: String,
        val name: String,
        val size: Long,
        /**fileId*/
        val uri: String,
        // CHANGE_HISTORY: 2021/3/30 17:06 废弃
        val token: TencentSessionBean?
)

/**下载文件token 整合多bucket共用token后使用*/
data class DownloadFileSessionV2(
        val bucket: String,
        val fileId: String,
        val name: String,
        val size: Long,
        /**fileId*/
        val uri: String
        // CHANGE_HISTORY: 2021/3/30 17:06 废弃
//        val token: TencentSessionBean?
)

data class PanLinkRequest(
        val files: List<HashMap<String, String>>,
        val bucket: String,
        /**临时链接过期时间，毫秒值，不传或0 默认为一小时*/
        val expire: Long = 0L
)

data class PanLinkResult(
        val fileId: String,
        val name: String,
        val size: Long,
        val uri: String,
        val bucket: String
)
