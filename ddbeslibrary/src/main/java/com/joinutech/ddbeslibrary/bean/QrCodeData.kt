package com.joinutech.ddbeslibrary.bean

import java.io.Serializable


/**
 * @className: QrCodeData
 * @desc: 二维码页面跳转数据定义
 * @author: zyy
 * @date: 2019/9/25 11:39
 * @company: joinUTech
 * @leader: ke
 */
class QrCodeData(
        val headImg: String,
        val name: String,
        val userId: String,
        /**
         * r.userId
         * o.companyId
         * */
        val content: String,
        val title: String = "个人二维码",
        val hint: String = "使用担当办公扫一扫，添加我为担当好友",
        val savable: Boolean = true) : Serializable