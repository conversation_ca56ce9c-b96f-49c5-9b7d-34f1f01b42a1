package com.joinutech.ddbeslibrary.bean

/**
 * 群组通知消息
 * 系统和工作消息的json解析bean类
 *
 * add 主动发送群组通知消息，只有创建群组成功，其他时候均为后端推送，其他群组普通消息为正常消息处理，类型不通而已
 */
data class GroupMessageBean(var groupId: String,
                            var msgId: String,
                            var groupLogo: String,
                            var groupName: String = "",
        // 群组时,type = 13 or 17 为meetingId
                            var title: String = "担当办公",
        // 13,14,15，group：13,14，
                            var type: String,
        // type = -303, content is InviteGroupData，type=13 or 17 时为邀请人的名字，14为操作人userId
                            var content: String,
        //type=13 or 17 为对象，其他为字符串 1 OR 2
                            var icon: String = "",
                            var createTime: Long,
        //群组时，type = 13,17 被邀请的用户userId拼接
                            val pushMsg: String = ""
)

data class InviteGroupData(
        val userNameAvatars: List<UserNameAvatar>,
        val username: String
)

data class UserNameAvatar(
        val headimg: String,
        val id: String,
        var name: String
)