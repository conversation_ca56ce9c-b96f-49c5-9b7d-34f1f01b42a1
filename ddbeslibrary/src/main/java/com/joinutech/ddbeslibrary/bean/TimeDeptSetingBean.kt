package com.joinutech.ddbeslibrary.bean

import java.io.Serializable

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/3/15.
 */
data class TimeDeptSetingBean(
        /**
         * 子部门信息 层级关系 这个字段中deptList为部门层级关系
         * deptList中 sublevel 为 子部门中 二级部门信息，依次类推
         */
        val secDeptList: TimeDeptSetingBean? = null,

        /**部门id关系，部门id和父部门id数据 目前返回数据都是空*/
        val checkDeptIds: List<DeptInfo>? = null,
        /**
         * 部门信息 当前部门信息，
         * sublevel 包含的所有子部门集合，以及部门间关系 parentId
         */
        val deptList: List<NewDeptBean> = arrayListOf()
) : Serializable

/**部门信息*/
data class DeptInfo(
        /**部门id，根部门为0*/
        val deptId: String,
        /**父部门id，根部门为0*/
        val parentId: String,
        //----------checkDeptIds 时没有以下字段
        /**部门名称*/
        val name: String? = null,
        /**部门状态*/
        val status: Int? = null,
        val sublevel: List<DeptInfo>? = null
) : Serializable