package com.joinutech.ddbeslibrary.base

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.graphics.Point
import android.graphics.Rect
import android.os.Build
import android.os.Bundle
import android.os.StrictMode
import android.os.StrictMode.VmPolicy
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewTreeObserver
import android.view.Window
import android.view.WindowManager
import android.widget.FrameLayout
import com.alibaba.android.arouter.facade.annotation.Route
import com.gyf.immersionbar.ImmersionBar
import com.joinutech.common.util.CacheHelper
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.databinding.ActivityCommonWebLayoutBinding
import com.joinutech.ddbeslibrary.utils.COMMON_WEB_ACTIVITY
import com.joinutech.ddbeslibrary.utils.ConsKeys
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.Loggerr
import com.joinutech.ddbeslibrary.utils.ScreenUtils
import com.marktoo.lib.cachedweb.LogUtil
//import kotlinx.android.synthetic.main.activity_common_web_layout.fl_container



/**
 * @PackageName: com.joinutech.ddbeslibrary.base
 * @ClassName: CommonWebActivity
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2021/1/11 16:54
 * @Desc: //TODO 通用webview页面
 */
//通用web页面,主要内容在一个fragment中了
@Route(path = COMMON_WEB_ACTIVITY)
//class CommonWebActivity : MyUseBaseActivity(), ViewTreeObserver.OnGlobalLayoutListener {
class CommonWebActivity : MyUseBindingActivity<ActivityCommonWebLayoutBinding>(), ViewTreeObserver.OnGlobalLayoutListener {

    override val contentViewResId: Int = R.layout.activity_common_web_layout
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityCommonWebLayoutBinding {
        return ActivityCommonWebLayoutBinding.inflate(layoutInflater)
    }

    override fun showToolBar(): Boolean = false
    override fun openArouterReceive(): Boolean = false
    override fun openEventBus(): Boolean = true

    override fun initImmersion() {
        val type = intent.getIntExtra(ConsKeys.MODEL_TYPE, 0)
        requestedOrientation = if (type == 1) {
            if (ImmersionBar.hasNotchScreen(this)) {
                showLog("存在刘海")
                var notchHeight = ImmersionBar.getNotchHeight(this)
                if (notchHeight > 0) {
                    showLog("存在刘海，需要设置padding $notchHeight")
                } else {
                    showLog("存在刘海，未获取到刘海高度")
                    notchHeight = 100
                }
                findViewById<FrameLayout>(R.id.fl_container).setPadding(notchHeight, 0, 0, 0)
            }
            ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        } else {
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
    }

    lateinit var mChildOfContent: View
    lateinit var frameLayoutParams: FrameLayout.LayoutParams

    private var statusHeight: Int = 0

    var isShowBack=0
    var companyId=""
    private var commonWebFragment: CommonWebFragment? = null

    companion object{
        var locationType = ""
    }
    override fun initView() {
        //webview调用系统相机时用到的
        val builder = VmPolicy.Builder()
        StrictMode.setVmPolicy(builder.build())
//        whiteStatusBarBlackFont()
        statusHeight = ScreenUtils.px2dip(this, ScreenUtils.getStatusBarHeight2(this))

        val url = intent.getStringExtra(ConsKeys.PARAMS_URL) ?: ""
        companyId = intent.getStringExtra(ConsKeys.COMPANY_ID) ?: ""
        locationType = intent.getStringExtra("type") ?: ""
        isShowBack=intent.getIntExtra("backspaceKey",0)
        val titleName=intent.getStringExtra("titleName")
        val pageInfo = intent.getStringExtra("pageInfo")?:""
        val isNoticeWeb = intent.getBooleanExtra("isNoticeWeb",false)
        commonWebFragment = CommonWebFragment.newInstance(url, companyId,isShowBack,titleName?:"",pageInfo,isNoticeWeb)
        backListener = commonWebFragment
        val transaction = supportFragmentManager.beginTransaction()
        transaction.replace(R.id.fl_container, commonWebFragment!!, "cwf")
        transaction.commitAllowingStateLoss()

        val content = findViewById<FrameLayout>(android.R.id.content)
        mChildOfContent = content.getChildAt(0)

//        autoUpdateView()

        autoUpdateView2()
    }

    fun autoUpdateView() {
        mChildOfContent.viewTreeObserver.addOnGlobalLayoutListener { possiblyResizeChildOfContent() }
        frameLayoutParams = mChildOfContent.layoutParams as FrameLayout.LayoutParams
    }

    fun autoUpdateView2() {
//        mChildOfContent.viewTreeObserver.addOnGlobalLayoutListener { possiblyResizeChildOfContent() }
        frameLayoutParams = mChildOfContent.layoutParams as FrameLayout.LayoutParams
    }

    override fun onResume() {
        super.onResume()
        window.decorView.viewTreeObserver.addOnGlobalLayoutListener(this)
    }

    override fun onGlobalLayout() {
        possiblyResizeChildOfContent2()
    }

    override fun onPause() {
        window.decorView.viewTreeObserver.removeOnGlobalLayoutListener(this)
        super.onPause()
    }

    private var usableHeightPrevious = 0

    private fun possiblyResizeChildOfContent() {
        val usableHeightNow = computeUsableHeight()
        if (usableHeightNow != usableHeightPrevious) {
            val usableHeightSansKeyboard = mChildOfContent.rootView
                    .height
            val heightDifference = usableHeightSansKeyboard - usableHeightNow
            if (heightDifference > usableHeightSansKeyboard / 4) {
// keyboard probably just became visible
                frameLayoutParams.height = (usableHeightSansKeyboard
                        - heightDifference)
            } else {
// keyboard probably just became hidden
                frameLayoutParams.height = usableHeightSansKeyboard
            }
            mChildOfContent.requestLayout()
            usableHeightPrevious = usableHeightNow
        }
    }

    private fun computeUsableHeight(): Int {
        val r = Rect()
        mChildOfContent.getWindowVisibleDisplayFrame(r)
        return r.bottom - r.top + statusHeight
    }

    private fun possiblyResizeChildOfContent2() {
        // 实际可以显示高度
        val paddingBottom = computeUsableHeight2()
        showLog("now =$paddingBottom ,pre = $usableHeightPrevious")
        if (paddingBottom != usableHeightPrevious) {
            // 总高度
            val usableHeightSansKeyboard = mChildOfContent.rootView.height
            // 总高度与可显示区域高度 差值，如果大于了总高度的1/4，则重设显示区域高度
            showLog("容器父布局高度为 $usableHeightSansKeyboard 虚拟部分占据高度为 $paddingBottom")
            val newHeight = if (paddingBottom > usableHeightSansKeyboard / 4) {
                // keyboard probably just became visible
                (usableHeightSansKeyboard - paddingBottom)
            } else {
                // 设置高度为FrameLayout content的布局高度
                // keyboard probably just became hidden
//                usableHeightNow
                FrameLayout.LayoutParams.MATCH_PARENT
            }
            showLog("容器布局新的高度为=$newHeight")
            frameLayoutParams.height = newHeight
            mChildOfContent.requestLayout()
            usableHeightPrevious = paddingBottom
        }
    }

    private fun computeUsableHeight2(): Int {
        val r = Rect()
        mChildOfContent.getWindowVisibleDisplayFrame(r)
        ScreenUtil.getScreenSize(this)
        ScreenUtil.getDisplayScreenSize(this)
        val realHeight = ScreenUtil.getScreenRealHeight(this)
//        val displayHeight = ScreenUtil.getDisplayScreenHeight(this)
        val visualBarHeight = realHeight - r.bottom
//        if (ScreenUtil.navigationGestureEnabled(this)) {
//            showLog("开启了全面屏手势")
//            showLog("全面屏下 虚拟按键高度 ${ScreenUtil.getCurrentNavigationBarHeight(this)}")
//        } else {
//            showLog("关闭了全面屏手势")
//            showLog("非全面屏下 虚拟按键高度 ${ScreenUtil.getCurrentNavigationBarHeight(this)}")
//        }
//        val statusBarHeight = r.top
//        showLog("visualBarHeight=$visualBarHeight," +
//                "statusBarHeight=$statusBarHeight,displayHeight=$displayHeight," +
//                "realHeight = $realHeight == ${visualBarHeight + statusBarHeight + displayHeight}")
//        val useableHeight = if (ScreenUtil.getNavigationBarEnable(this)) {
//            showLog("虚拟导航键 显示 bottom = ${r.bottom} ,top = ${r.top}")
////            r.bottom - r.top - visualBarHeight
//            r.bottom - visualBarHeight
//        } else {
//            showLog("虚拟导航键 未显示")
//            r.bottom - r.top
//        }
//
//        showLog("内容区域高度为$useableHeight")
        return visualBarHeight
    }

    private var backListener: OnCommonWebCallback? = null

    private var startTime = 0L

    /**当前点击回退时间，少于两秒则直接关闭页面*/
    override fun onBackPressed() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - startTime > 2000) {
            startTime = currentTime
            backListener?.goBack()
            CacheHelper.globalBackListenerTime = System.currentTimeMillis()
        } else {
            super.onBackPressed()
        }
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (!hasFocus){
            commonWebFragment?.notifyWebOnPause()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        val fragment = supportFragmentManager.findFragmentByTag("cwf")
        fragment?.onActivityResult(requestCode, resultCode, data)
        super.onActivityResult(requestCode, resultCode, data)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        showLog("------>onConfigurationChanged<------------")
        super.onConfigurationChanged(newConfig)

    }

    override fun onDestroy() {
        supportFragmentManager.findFragmentByTag("cwf")?.let {
            val transaction = supportFragmentManager.beginTransaction()
            transaction.remove(it)
            transaction.commitAllowingStateLoss()
        }
        super.onDestroy()
        //刷新金蝶的审批未读数
        if (isShowBack == 1) {
            Loggerr.i("切换公司测试", "=金蝶相关=发送了刷新入口未读数指令===")
            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.refresh_entroy_unread_count, companyId))
        }else{
            //刷新审批未处理数--触发事件,,说明，web页的审批操作是在approveWebFeagment中完成的
//            EventBusUtils.sendEvent(EventBusEvent(EventBusAction.refresh_all_appro_undo, 0))
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        if (outState != null && clearFragmentTag()) {
            outState.remove("android:support:fragments")
        }
    }

    private fun clearFragmentTag(): Boolean {
        return true
    }
}

object ScreenUtil {

    private fun log(info: String) {
//        LogUtil.addDebugTags("screen_util")
        LogUtil.showLog(info)
    }

    fun getScreenSize(context: Context) {
        val wm = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = wm.defaultDisplay
        val point = Point()
//        display.getSize(point)
//        log("可显示区域 = ${point.x}x${point.y}")
        display.getRealSize(point)
        log("全部屏幕尺寸 = ${point.x}x${point.y}")
    }

    fun getDisplayScreenSize(context: Context) {
        val height = context.resources.displayMetrics.heightPixels
        val width = context.resources.displayMetrics.widthPixels
        log("可显示区域2  = ${width}x${height}")
    }

    /**
     * 获取屏幕高度
     * 第一种，读取DisplayMetrics的heightPixels参数
     */
    fun getDisplayScreenHeight(context: Context): Int {
        return context.resources?.displayMetrics?.heightPixels ?: 0
    }

    /**
     * 获取屏幕Real高度
     * 第二种，读取windowManager里面的defaultDisplay参数
     */
    @Volatile
    private var sRealSizes = arrayOfNulls<Point>(2)
    fun getScreenRealHeight(context: Context): Int {
        var orientation = context.resources?.configuration?.orientation
        orientation = if (orientation == 1) 0 else 1
        if (sRealSizes[orientation] == null) {
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val display = windowManager.defaultDisplay
            val point = Point()
            display.getRealSize(point)
            sRealSizes[orientation] = point
        }
        return sRealSizes[orientation]?.y ?: getDisplayScreenHeight(context)
    }

    fun getScreenWidth(context: Context): Int {
        return context.resources?.displayMetrics?.widthPixels ?: 0
    }

    fun getScreenRealWidth(context: Context): Int {
        var orientation = context.resources?.configuration?.orientation
        orientation = if (orientation == 1) 0 else 1
        if (sRealSizes[orientation] == null) {
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val display = windowManager.defaultDisplay
            val point = Point()
            display.getRealSize(point)
            sRealSizes[orientation] = point
        }
        return sRealSizes[orientation]?.x ?: getScreenWidth(context)
    }

    fun getStatusBarHeight(window: Window, context: Context): Int {
        val localRect = Rect()
        window.decorView.getWindowVisibleDisplayFrame(localRect)
        var mStatusBarHeight = localRect.top
        if (0 == mStatusBarHeight) {
            try {
                val localClass = Class.forName("com.android.internal.R\$dimen")
                val localObject = localClass.newInstance()
                val i5 =
                        localClass.getField("status_bar_height")[localObject].toString().toInt()
                mStatusBarHeight = context.resources.getDimensionPixelSize(i5)
            } catch (var6: ClassNotFoundException) {
                var6.printStackTrace()
            } catch (var7: IllegalAccessException) {
                var7.printStackTrace()
            } catch (var8: InstantiationException) {
                var8.printStackTrace()
            } catch (var9: NumberFormatException) {
                var9.printStackTrace()
            } catch (var10: IllegalArgumentException) {
                var10.printStackTrace()
            } catch (var11: SecurityException) {
                var11.printStackTrace()
            } catch (var12: NoSuchFieldException) {
                var12.printStackTrace()
            }
        }
        if (0 == mStatusBarHeight) {
            val resourceId: Int =
                    context.resources.getIdentifier("status_bar_height", "dimen", "android")
            if (resourceId > 0) {
                mStatusBarHeight = context.resources.getDimensionPixelSize(resourceId)
            }
        }
        return mStatusBarHeight
    }

    fun getNavigationBarEnable(context: Context): Boolean {
//        val navBarOverride: String =
//            com.sun.javafx.runtime.SystemProperties.get("qemu.hw.mainkeys")
//        if ("1" == navBarOverride) {
//            mHasNavigationBar = false
//        } else if ("0" == navBarOverride) {
//            mHasNavigationBar = true
//        }
//        val config = context.resources.getIdentifier("config_showNavigationBar", "bool", "android")
//        return config != 0
        return getScreenRealHeight(context) > getDisplayScreenHeight(context)
    }

    fun isAllScreenDevice(context: Context): Boolean {
        if (Build.VERSION.SDK_INT < 21) {
            return false
        } else {
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val display = windowManager.defaultDisplay
            val point = Point()
            display.getRealSize(point)
            val width: Float
            val height: Float
            if (point.x < point.y) {
                width = point.x.toFloat()
                height = point.y.toFloat()
            } else {
                width = point.y.toFloat()
                height = point.x.toFloat()
            }
            if (height / width >= 1.97f) {
                return true
            }
            return false
        }
    }

    /**
     * 全面屏（是否开启全面屏开关 0 关闭  1 开启）
     *
     * @param context
     * @return
     */
    fun navigationGestureEnabled(context: Context): Boolean {
        val fullScreen: Int = Settings.Global.getInt(context.contentResolver, getDeviceInfo(), 0)
        return fullScreen != 0
    }

    /**
     * 获取设备信息（目前支持几大主流的全面屏手机，亲测华为、小米、oppo、魅族、vivo都可以）
     *
     * @return
     */
    private fun getDeviceInfo(): String? {
        val brand = Build.BRAND
        if (brand.isNullOrBlank()) return "navigationbar_is_min"
        return when {
            brand.equals("HUAWEI", ignoreCase = true) -> {
                "navigationbar_is_min"
            }
            brand.equals("XIAOMI", ignoreCase = true) -> {
                "force_fsg_nav_bar"
            }
            brand.equals("VIVO", ignoreCase = true) -> {
                "navigation_gesture_on"
            }
            brand.equals("OPPO", ignoreCase = true) -> {
                "navigation_gesture_on"
            }
            else -> {
                "navigationbar_is_min"
            }
        }
    }

    /**
     * 非全面屏下 虚拟键实际高度(隐藏后高度为0)
     * @param activity
     * @return
     */
    fun getCurrentNavigationBarHeight(activity: Activity): Int {
        return if (isNavigationBarShown(activity)) {
            getNavigationBarHeight2(activity)
        } else {
            0
        }
    }

    /**
     * 非全面屏下 虚拟按键是否打开
     * @param activity
     * @return
     */
    private fun isNavigationBarShown(activity: Activity): Boolean {
        //虚拟键的view,为空或者不可见时是隐藏状态
        val view = activity.findViewById<View>(android.R.id.navigationBarBackground) ?: return false
        return view.visibility == View.VISIBLE
    }

    /**
     * 非全面屏下 虚拟键高度(无论是否隐藏)
     * @param context
     * @return
     */
    fun getNavigationBarHeight2(context: Context): Int {
        var result = 0
        val resourceId = context.resources.getIdentifier("navigation_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = context.resources.getDimensionPixelSize(resourceId)
        }
        return result
    }

}
