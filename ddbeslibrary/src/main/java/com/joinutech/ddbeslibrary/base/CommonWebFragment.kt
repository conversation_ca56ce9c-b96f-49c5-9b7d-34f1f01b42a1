package com.joinutech.ddbeslibrary.base

import TIANDITU_URL
import TiandituBody
import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.ContentUris
import android.content.Context
import android.content.Intent
import android.database.Cursor
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.location.Geocoder
import android.location.Location
import android.location.LocationListener
import android.net.Uri
import android.net.wifi.WifiManager
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.provider.DocumentsContract
import android.provider.MediaStore
import android.provider.Settings
import android.text.Html
import android.util.Base64
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.webkit.JavascriptInterface
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.FileProvider
import androidx.loader.content.CursorLoader
import cn.addapp.pickers.util.LogUtils
import com.alibaba.android.arouter.launcher.ARouter
import com.google.gson.reflect.TypeToken
import com.joinutech.common.base.isWebDebug
import com.joinutech.common.helper.WechatHelper
import com.joinutech.common.provider.FilePreviewProvider
import com.joinutech.common.provider.RouteServiceProvider
import com.joinutech.common.storage.FileDownTransferManager
import com.joinutech.common.storage.FileStorage
import com.joinutech.common.storage.FileTransferInfo
import com.joinutech.common.storage.FileUtil
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.LocationSupport
import com.joinutech.common.util.LocationUtils
import com.joinutech.common.util.ObjectStore
import com.joinutech.common.util.TimePickerUtil
import com.joinutech.common.util.UserHolder
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.api.PersonApi
import com.joinutech.ddbeslibrary.base.loading.LoadingManager
import com.joinutech.ddbeslibrary.bean.OrgImportPeopleBean
import com.joinutech.ddbeslibrary.bean.OrgPersonBean
import com.joinutech.ddbeslibrary.bean.UserInfo
import com.joinutech.ddbeslibrary.request.RetrofitClient
import com.joinutech.ddbeslibrary.request.RxScheduleUtil
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.request.interceptor.RequestCache
import com.joinutech.ddbeslibrary.service.LocationCallback
import com.joinutech.ddbeslibrary.service.LocationResult
import com.joinutech.ddbeslibrary.service.LocationService
import com.joinutech.ddbeslibrary.service.WifiService
import com.joinutech.ddbeslibrary.utils.APP_ID_WX
import com.joinutech.ddbeslibrary.utils.CommonKeys
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.ConsKeys
import com.joinutech.ddbeslibrary.utils.ConsValue
import com.joinutech.ddbeslibrary.utils.DeviceUtil
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.IMAGE_CUT_CODE
import com.joinutech.ddbeslibrary.utils.JavaUtils
import com.joinutech.ddbeslibrary.utils.Loggerr
import com.joinutech.ddbeslibrary.utils.MMKVKEY
import com.joinutech.ddbeslibrary.utils.MMKVUtil
import com.joinutech.ddbeslibrary.utils.PermissionUtils
import com.joinutech.ddbeslibrary.utils.PictureNewHelper
import com.joinutech.ddbeslibrary.utils.PushEvent
import com.joinutech.ddbeslibrary.utils.RouteOrg
import com.joinutech.ddbeslibrary.utils.RouteProvider
import com.joinutech.ddbeslibrary.utils.ScreenUtils
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.XUtil
import com.joinutech.ddbeslibrary.utils.convertJsPermission
import com.joinutech.ddbeslibrary.utils.toast
import com.joinutech.ddbeslibrary.utils.toastShort
import com.joinutech.ddbeslibrary.widget.activity.TaskImagePreviewActivity
import com.joinutech.flutter.EventApproveDeleteKingdee
import com.joinutech.flutter.EventCloseWeb
import com.joinutech.flutter.EventReOpenWeb
import com.joinutech.flutter.EventRouteFlutter
import com.joinutech.flutter.EventTakeMealSuccess
import com.joinutech.flutter.EventTiandituParam
import com.joinutech.flutter.EventToQrCode
import com.joinutech.flutter.EventWebViewGoBack
import com.marktoo.lib.cachedweb.LogUtil
import com.tencent.mm.opensdk.modelpay.PayReq
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import com.tencent.smtt.export.external.interfaces.GeolocationPermissionsCallback
import com.tencent.smtt.export.external.interfaces.IX5WebChromeClient
import com.tencent.smtt.export.external.interfaces.JsPromptResult
import com.tencent.smtt.export.external.interfaces.JsResult
import com.tencent.smtt.export.external.interfaces.PermissionRequest
import com.tencent.smtt.export.external.interfaces.SslError
import com.tencent.smtt.export.external.interfaces.SslErrorHandler
import com.tencent.smtt.export.external.interfaces.WebResourceRequest
import com.tencent.smtt.export.external.interfaces.WebResourceResponse
import com.tencent.smtt.sdk.ValueCallback
import com.tencent.smtt.sdk.WebChromeClient
import com.tencent.smtt.sdk.WebSettings
import com.tencent.smtt.sdk.WebView
import com.tencent.smtt.sdk.WebViewClient
import com.trello.rxlifecycle3.components.support.RxAppCompatActivity.RESULT_OK
import com.zhy.http.okhttp.OkHttpUtils
import com.zhy.http.okhttp.callback.FileCallBack
import createMapImage
import navigate2Map
import okhttp3.Call
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.Serializable
import java.util.Date
import java.util.Locale
import java.util.concurrent.atomic.AtomicBoolean



//通用web页面tcp，fragment，，，审批相关的web页面是另一个通用web页面ApproveWebFragment
class CommonWebFragment : WebBaseFragment(), OnCommonWebCallback {
    override val layoutRes: Int = R.layout.fragment_tbs_web_layout

    private lateinit var api: IWXAPI

    /** 公共标题栏 */
    lateinit var llTitleBar: View
    lateinit var ivLeft: ImageView
    lateinit var tvLeft: TextView
    lateinit var tvTitle: TextView
    lateinit var tvSubTitle: TextView
    lateinit var ivRight: ImageView
    lateinit var ivLeftClose: ImageView
    lateinit var tvRight: TextView

    private var webview: WebView? =null

    private lateinit var emptyView: PageEmptyView

    lateinit var loadingLayout: View
    private lateinit var progressBar: ProgressBar

    var targetUrl: String = ""
    var companyId: String = ""

    /**web挂载原生回调方法对象名*/
    var objName: String = "ddbes_web"

    var statusHeight: Int = 0
    var isShowBack=0
    var titleName = ""

    var customViewCallBack :IX5WebChromeClient.CustomViewCallback? = null
    var customView: View? = null

    var canBackPageByJs = true

    companion object {
        fun newInstance(targetUrl: String, companyId: String,isShowBack:Int,titleName:String): CommonWebFragment {
            return CommonWebFragment().apply {
                arguments = Bundle().apply {
                    putString(ConsKeys.PARAMS_URL, targetUrl)
                    putString(ConsKeys.COMPANY_ID, companyId)
                    putString("titleName", titleName)
                    putInt("isShowBack",isShowBack)
                }
            }
        }
    }

    override fun onSaved(savedInstanceState: Bundle?) {
        super.onSaved(savedInstanceState)
        if (savedInstanceState != null){
            webview?.restoreState(savedInstanceState)
        }
    }

    override fun initView(rootView: View) {
        updateStatusBar(true)
        immersionBar?.apply {
//            keyboardEnable(true).keyboardMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        }
        val topPx = ScreenUtils.getStatusBarHeight2(requireContext())
        statusHeight = ScreenUtils.px2dip(requireContext(), topPx)

        llTitleBar = rootView.findViewById(R.id.ll_title_bar)

        ivLeft = rootView.findViewById(R.id.iv_left)
        ivLeft.setOnClickListener(this)

        tvLeft = rootView.findViewById(R.id.tv_left)

        tvTitle = rootView.findViewById(R.id.tv_title)
        tvSubTitle = rootView.findViewById(R.id.tv_sub_title)

        ivRight = rootView.findViewById(R.id.iv_right)
        ivLeftClose = rootView.findViewById(R.id.iv_left_close)
        tvRight = rootView.findViewById(R.id.tv_right)

        ivRight.setOnClickListener(this)
        ivLeftClose.setOnClickListener(this)
        tvRight.setOnClickListener(this)

        ivRight.setImageResource(R.drawable.icon_image_verify_refresh)
        ivRight.visibility = View.VISIBLE

//        webview = rootView.findViewById(R.id.lfw_webview)
        emptyView = rootView.findViewById(R.id.empty_layout)
        loadingLayout = rootView.findViewById(R.id.loadingLayout)
        progressBar = rootView.findViewById(R.id.pb_web_load_progress)

        targetUrl = arguments?.getString(ConsKeys.PARAMS_URL, "") ?: return
        Loggerr.i("webview调试", "web地址： ${targetUrl}")
        companyId = arguments?.getString(ConsKeys.COMPANY_ID, "") ?: return
//        handler = MyStaticHandler(WeakReference(mActivity))


        webview=WebView(mActivity)
        val params=FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,ViewGroup.LayoutParams.MATCH_PARENT)
        webview?.layoutParams=params
        rootView.findViewById<FrameLayout>(R.id.web_contain_fl).addView(webview)

        //是否显示原生的返回按键
        isShowBack=arguments?.getInt("isShowBack",0)?:0
        titleName= arguments?.getString("titleName","") ?:""
        if (isShowBack == 0 ||titleName=="金蝶审批") {
            //展示返回键,用我自己的titleBar
            updateTitleBar(true,titleName?:"","")
        }else{
            //不展示返回键
            llTitleBar.visibility=View.GONE
        }
        PermissionUtils.requestPermissionFragment(this,
            arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),"",
            onSuccess = {
                LocationUtils.getLocation(requireContext())
            },
            onError = {
                toast("授权失败")
            }, preTips = "需要您授权对应权限")

    }

    @SuppressLint("SetJavaScriptEnabled", "JavascriptInterface")
    override fun initLogic() {
        initWx()
        val perms = arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )

        val hasPermission = PermissionUtils.checkPermissions(requireContext() , perms)
        val text = "需要您的同意,才能使用存储权限,便于查看对应内容"

        PermissionUtils.requestPermissionActivity(mActivity, perms, "存储文件权限",
            onSuccess = {
                initInterceptor(true)
//                showLoading()
                loadPage(true)
            },
            onError = {
                initInterceptor(false)
                loadPage(false)
            } , preTips = text)
    }

    private fun initWx() {
        api = WXAPIFactory.createWXAPI(mActivity, APP_ID_WX, true)
        // 将该app注册到微信
        api.registerApp(APP_ID_WX)
    }


    private fun clearWebCache() {
        showLog("清理webview缓存内容")
        try {
            val cacheDir = requireContext().externalCacheDir ?: requireContext().cacheDir
            webview?.settings?.setAppCachePath(cacheDir.absolutePath)
            webview?.settings?.setAppCacheEnabled(true)
            requireActivity().cacheDir?.delete()
        } catch (e: Exception) {
        }
//        requireActivity().externalCacheDir?.delete()
        try {
            //缓存模式
            webview?.settings?.cacheMode = WebSettings.LOAD_NO_CACHE
        } catch (e: Exception) {
        }
        try {
            webview?.clearCache(true)
        } catch (e: Exception) {
        }
        try {
            webview?.clearHistory()
        } catch (e: Exception) {
        }
        try {
            webview?.clearFormData()
        } catch (e: Exception) {
        }
    }

    //    private var canGoBack = -1
    private var fileChooseCallback: ValueCallback<Array<Uri>>? = null

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        webview?.saveState(outState)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        webview?.saveState(savedInstanceState)
    }

    override fun onResume() {
        super.onResume()
        webview?.onResume()
//        webview?.resumeTimers()
    }

    override fun onPause() {
        super.onPause()

        notifyWebOnPause()

        webview?.onPause()
//        webview?.pauseTimers()
    }

    fun notifyWebOnPause() {
        invokeJs("applicationWillResignActive")
    }


    val request_code_file_chooser_for_web=20003
    val request_code_file_capture_for_web=20005
    val request_code_permission=20004
    var imageUri:Uri?=null
    //使用x5内核webview时的相关配置如下
    @SuppressLint("SetJavaScriptEnabled")
    private fun initInterceptor(hasStoragePerm: Boolean) {
        val webviewSettings = webview?.settings
        webviewSettings?.apply {
            javaScriptEnabled=true
            pluginsEnabled=true
            useWideViewPort=true
            loadWithOverviewMode=true
            setSupportZoom(true)
            builtInZoomControls=true
            displayZoomControls=false

            cacheMode=WebSettings.LOAD_DEFAULT
            allowFileAccess=true
            javaScriptCanOpenWindowsAutomatically=true
            loadsImagesAutomatically=true
            defaultTextEncodingName="utf-8"

            layoutAlgorithm=WebSettings.LayoutAlgorithm.SINGLE_COLUMN
            setSupportMultipleWindows(false)

            domStorageEnabled=hasStoragePerm
            databaseEnabled=hasStoragePerm

            setGeolocationEnabled(true)
            mediaPlaybackRequiresUserGesture = false
            mixedContentMode = android.webkit.WebSettings.MIXED_CONTENT_COMPATIBILITY_MODE
        }

        //对象映射
        webview?.addJavascriptInterface(WebMutualInterface(mActivity, this), ConsKeys.JS_TRANS_PORT)
        webview?.setOnLongClickListener { false }// 禁用长按
        webview?.setLayerType(View.LAYER_TYPE_HARDWARE,null)
        clearWebCache()

        webview?.webViewClient=object:WebViewClient(){
            override fun onPageStarted(p0: WebView?, p1: String?, p2: Bitmap?) {
                super.onPageStarted(p0, p1, p2)
            }

            override fun onPageFinished(p0: WebView?, p1: String?) {
                if (!loadFinished) {
                    showLog("--->加载页面 ")
                    loadFinished = true
                    if (delayed.get()) {
                        handler.removeCallbacks(runnable)
                    } else {
                        delayed.compareAndSet(false, true)
                    }
                    checkLoadError()
                }
            }

            override fun onReceivedError(view: WebView?, errorCode: Int, description: String?, url: String?) {
                showLog("--->加载页面 onReceivedError 错误码：$errorCode -- $description")
                val noError =
                    description?.contains("net::ERR_FAILED") == true// || (targetUrl.contains("attence.html") && description?.contains("ERR_CONNECTION_REFUSED") == true)
                val err =
                    url?.contains(targetUrl) == true && description?.startsWith("ERR_") ?: false
                val code = errorCode ?: 0 < 0
                if (!loadFinished && !loadPageError && ((err && !noError) || code)) {
                    loadPageError = true
                    pageErrorInfo = "$url--$errorCode--$description"
                }
            }

            override fun onReceivedSslError(
                view: WebView?,
                handler: SslErrorHandler?,
                error: SslError?
            ) {
                handler?.proceed()
            }

            override fun onReceivedHttpError(
                view: WebView?,
                request: WebResourceRequest?,
                errorResponse: WebResourceResponse?
            ) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    showLog("--->加载页面 onHttpError ${request?.url} -- ${errorResponse?.statusCode}")
                    if (!loadFinished && !loadAssetsError && errorResponse?.statusCode!! >= 400) {
                        loadAssetsError = true
                    }
                }
            }

        }


        webview?.webChromeClient=object:WebChromeClient(){
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                if (newProgress < 100) {
                    if (progressBar.visibility == View.VISIBLE && progressBar.progress < newProgress) {
                        progressBar.progress = newProgress
                    }
                } else {
                    if (!loadFinished) {
                        if (delayed.compareAndSet(false, true)) {
                            showLog("--->加载页面 进度达到百分之百后，发送一次延时处理结果情况，避免不回调 finished方法")
                            handler.postDelayed(runnable, 20000)
                        }
                    }
                }
            }

            override fun onReceivedTitle(view: WebView?, title: String?) {
                val titleError = title?.contains("网页无法打开") ?: false
                        || title?.contains("error", true) ?: false
                        || title?.contains("failed", true) ?: false
                if (!loadFinished && !loadTitleError && (titleError)) {
                    loadTitleError = true
                    titleErrorInfo = "$targetUrl--$title"
                }
            }

            override fun onGeolocationPermissionsShowPrompt(
                origin: String?,
                callback: GeolocationPermissionsCallback?
            ) {
                super.onGeolocationPermissionsShowPrompt(origin, callback)
                callback?.invoke(origin , true, false)
            }

            override fun onJsAlert(view: WebView?, url: String?, message: String?, result: JsResult?): Boolean {
                    //默认禁止
                return true
//                return super.onJsAlert(view,url,message,result)
            }

            override fun onJsPrompt(
                p0: WebView?,
                p1: String?,
                p2: String?,
                p3: String?,
                p4: JsPromptResult?
            ): Boolean {
                return super.onJsPrompt(p0, p1, p2, p3, p4)
            }

            override fun onJsConfirm(
                p0: WebView?,
                p1: String?,
                p2: String?,
                p3: JsResult?
            ): Boolean {
                return super.onJsConfirm(p0, p1, p2, p3)

            }

            override fun onShowFileChooser(
                webView: WebView?,
                filePathCallback: ValueCallback<Array<Uri>>?,
                fileChooserParams: FileChooserParams?
            ): Boolean {
                //以下是webview调用系统相机的步骤-----------------------------------------------------------
                val mode=fileChooserParams?.mode?:""
                Loggerr.i("调用模式", "===传递的mode=${mode}===")
                Loggerr.i("调用模式", "===预定义的mode=${FileChooserParams.MODE_OPEN}===")
                Loggerr.i("调用模式", "===预定义的mode=${FileChooserParams.MODE_OPEN_FOLDER}===")
                Loggerr.i("调用模式", "===预定义的mode=${FileChooserParams.MODE_SAVE}===")
                Loggerr.i("调用模式", "===预定义的mode=${FileChooserParams.MODE_OPEN_MULTIPLE}===")
                if (mode == FileChooserParams.MODE_OPEN_MULTIPLE) {
                 //此时是调用拍照
                    val perms = arrayOf(
                        Manifest.permission.READ_EXTERNAL_STORAGE,
                        Manifest.permission.CAMERA,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    val tips = "选取图片需要您授权读写"
                    PermissionUtils.requestPermissionActivity(mActivity, perms,
                        hint = "照相机权限",
                        onSuccess = {
                            fileChooseCallback = filePathCallback
                            val fileFolder=CommonUtils.getFileCachePath(mActivity,"webPhoto")
                            val format=java.text.SimpleDateFormat("yyyy-MM-DD").format(Date())
                            val fileName="JDEG_"+format+"_"
                            val imageFile=File.createTempFile(fileName,".jpg",File(fileFolder))
//                            val imageFile=File(fileFolder+File.separator+fileName+System.currentTimeMillis()+".jpg")//也可以
                            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                                //转化为uri
                                imageUri=FileProvider.getUriForFile(mActivity,
                                    mActivity.packageName+".fileProvider",imageFile)

                            }else{
                                imageUri=Uri.fromFile(imageFile)
                            }
                            Loggerr.i("web文件选择", "=拍摄之前==imageUri=${imageUri}===")
                            val captureIntent=Intent(MediaStore.ACTION_IMAGE_CAPTURE)
                            //下面这句代码表示，提前指定拍摄后的文件名称，这个文件就算是最终的返回结果；
                            // 注释掉的话，就是代表不指定文件名称，那拍摄之后将在OnActivityForResult中返回bitmap结果，且是缩略图；
                            captureIntent.putExtra(MediaStore.EXTRA_OUTPUT, imageUri)

                            captureIntent.putExtra(MediaStore.Images.Media.ORIENTATION,0)

                            val photo=Intent(Intent.ACTION_PICK,MediaStore.Images.Media.EXTERNAL_CONTENT_URI)

                            /*val chooserIntent=Intent.createChooser(photo,"选择上传方式")
                            chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, arrayOf(captureIntent));*/
                            val chooserIntent=Intent(Intent.ACTION_CHOOSER)
                            chooserIntent.putExtra(Intent.EXTRA_INTENT,photo)
                            chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, arrayOf(captureIntent))

                            startActivityForResult(chooserIntent, request_code_file_capture_for_web);
                        },
                        onError = {
                            Toast.makeText(context, tips, Toast.LENGTH_SHORT).show()
                        } , preTips = tips)

                    return true
                }


                //以下是webview调用系统文件选择器的步骤========================================================
                Loggerr.i("web文件选择", "===filePathCallback=${filePathCallback}===")
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    //Android版本大于等于11时,申请全部文件的权限，文件选择，选择文件，，，调用系统的文件选择器===========
                    if (!Environment.isExternalStorageManager()) {
                        val intent=Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                        intent.setData(Uri.parse("package:" + mActivity.getPackageName()));
                        startActivityForResult(intent, request_code_permission);
                        return false
                    }
                }
                fileChooseCallback = filePathCallback
//                val intent=fileChooserParams?.createIntent()
                val intent=Intent(Intent.ACTION_GET_CONTENT)
                intent.addCategory(Intent.CATEGORY_OPENABLE)
                intent.setType("*/*")
                try {
//                    startActivityForResult(intent,request_code_file_chooser_for_web)
                    startActivityForResult(Intent.createChooser(intent,"File Chooser"),request_code_file_chooser_for_web)
                }catch (e:Exception){
                    fileChooseCallback=null
                    ToastUtil.show(mActivity,"打开文件选择器失败")
                    return false
                }

                return true
            }

            override fun onPermissionRequest(request: PermissionRequest?) {
                if (request == null) return
                if (request.resources.isEmpty()) return
                val perms = request.resources.toList()
                var ps = convertJsPermission(perms).toTypedArray()
                val tips = "需要您授权对应权限"
                PermissionUtils.requestPermissionFragment(this@CommonWebFragment , ps , "", onSuccess = {
                    request.grant(request.resources)
                } , onError = {
                    toast("授权失败")
                } , preTips = tips)
            }

            override fun onShowCustomView(view: View?, callback: IX5WebChromeClient.CustomViewCallback?) {
                if (customView == null) {
                    callback?.onCustomViewHidden()
                    return
                }
                customView = view
                customView?.setBackgroundColor(requireActivity().resources.getColor(R.color.black))
                val r = rootView?.findViewById<FrameLayout>(R.id.web_contain_fl)
                ( webview?.parent as? ViewGroup)?.removeView(webview)
                r?.addView(customView)
                r?.visibility = View.VISIBLE
            }

            override fun onHideCustomView() {
                if (customView == null) return
                val r = rootView?.findViewById<FrameLayout>(R.id.web_contain_fl)
                customView?.visibility = View.GONE;
                r?.removeView(customView);
                r?.visibility = View.GONE;
                customView = null;
                customViewCallBack?.onCustomViewHidden();
                r?.addView(webview)
            }
        }
    }

    private var loadTitleError = false
    private var titleErrorInfo = ""
    private var loadPageError = false
    private var pageErrorInfo = ""
    private var loadAssetsError = false
    private var loadFinished = false
    val cache = RequestCache()

    val handler = Handler()
    private val delayed = AtomicBoolean(false)
    val runnable = {
        if (!loadFinished) {
            loadFinished = true
            checkLoadError()
        }
    }

    private fun showLoadView() {
        loadingLayout.visibility = View.VISIBLE
        progressBar.visibility = View.VISIBLE
        emptyView.hide()
        delayed.compareAndSet(true, false)
        loadTitleError = false
        titleErrorInfo = ""
        loadPageError = false
        pageErrorInfo = ""
        loadAssetsError = false
        loadFinished = false
    }

    private fun checkLoadError() {
        showLog("加载完成，检查页面是否包含错误")
        loadingLayout.visibility = View.GONE
        if (loadTitleError || loadPageError) {
            emptyView.setContent("加载失败，请稍后重试！")
            emptyView.setPositiveContent("")
            emptyView.setNegativeContent("")
            emptyView.show()
            // TODO: 2021/8/24 记录网页加载异常信息
            val info = if (!titleErrorInfo.isNullOrBlank() && !pageErrorInfo.isNullOrBlank()) {
                titleErrorInfo.plus(";").plus(pageErrorInfo)
            } else if (!titleErrorInfo.isNullOrBlank()) {
                titleErrorInfo
            } else if (!pageErrorInfo.isNullOrBlank()) {
                pageErrorInfo
            } else {
                ""
            }
            showLog("-->>加载出现异常，需要记录日志信息")
            cache.log(0L, info, 6, start, System.currentTimeMillis(), 1)
        } else {
            showLog("-->>加载正常，隐藏loading和失败时显示view")
            emptyView.hide()
        }
    }

    private var start = 0L

    private fun loadPage(hasStoragePerm: Boolean) {
        initLocate()
//        loadUrl("http://192.168.0.76:8080/#/")
        start = System.currentTimeMillis()
        showLog("通用web页面加载url：hasStoragePerm=$hasStoragePerm -$targetUrl $start")
        loadUrl(targetUrl)
    }

    private fun loadUrl(realUrl: String, isRefresh: Boolean = false) {
//        webview.loadUrl("http://www.baidu.com".plus(realUrl), ConsValue.getWebHeaders())
        showLog("--->开始加载页面 $realUrl")
        showLoadView()
        if (isRefresh) {
            webview?.reload()
        } else {
            webview?.loadUrl(realUrl, ConsValue.getWebHeaders())
        }
    }

    //=========================================以上==============是webview配置=============================
    //=========================================以下==============是业务功能=============================

    override fun onNoDoubleClick(v: View) {
        when (v.id) {
            R.id.iv_left -> {
                if (webview?.canGoBack()?:false) {
                    webview?.goBack()
                } else {
                    onFinish()
                }
            }
            R.id.tv_right -> {
            }
            R.id.iv_right -> {
                loadUrl(targetUrl, true)
            }
            R.id.iv_left_close->{
                onFinish()
            }
        }
    }

    //js交互对象tcp,js调用android
    @SuppressLint("JavascriptInterface")
    class WebMutualInterface constructor(
        private val mActivity: Activity,
        private val callback: OnCommonWebCallback
    ) {
        //调起微信支付
        @JavascriptInterface
        fun client_goWXPay(params: String) {
            val jsonObject = JSONObject(params)
            val appid = jsonObject.getString("appid")
            val nonceStr = jsonObject.getString("nonceStr")
            val package_value = jsonObject.getString("package")
            val partnerid = jsonObject.getString("partnerid")
            val paySign = jsonObject.getString("paySign")
            val prepay_id = jsonObject.getString("prepay_id")
            val prepayid = jsonObject.getString("prepayid")
            val signType = jsonObject.getString("signType")
            val timeStamp = jsonObject.getString("timeStamp")

            val request = PayReq()
            request.appId = appid
            request.partnerId = partnerid
            request.prepayId = prepayid
            request.packageValue = package_value
            request.nonceStr = nonceStr
            request.timeStamp = timeStamp
            request.sign = paySign
            //callback用来调用自己写的其他的本地的方法
            callback.onWxPayStart(request)

        }

        /**
         * web页面初始化完成回调
         * 页面名称，调用web方法的web对象名称，是否使用原生的标题栏
         * 原生标题栏 左侧返回键，右侧关闭键
         * {"title":"","subTitle":"","objName":"原生调用web对象名","useTitleBar":true}
         * 原生通过 objName.updateClientInfo() 回调json数据 {"company":{},"user":{},"token":""}
         */
        @JavascriptInterface
        fun client_isAlready(json: String) {
            mActivity.runOnUiThread {
                try {
                    val jsonObj = JSONObject(json)
                    //说明，这个callback调用的方法，会执行这个fragment中实现的子类override fun onWebReady。。。；下同
                    Loggerr.i("webview调试", "===准备执行client_isAlready=== $json")
                    callback.onWebReady(
                        jsonObj.optString("title"),
                        jsonObj.optString("subTitle"),
                        jsonObj.optString("objName"),
                        jsonObj.optBoolean("useTitleBar")
                    )
                }catch (e: Exception){
                    e.printStackTrace()
                }
            }
        }

        // js 跳转 二维码
        @JavascriptInterface
        fun client_jumpQr() {
            mActivity.runOnUiThread {
                try {
                    EventBus.getDefault().post(EventToQrCode())
                }catch (e: Exception){
                    e.printStackTrace()
                }
            }
        }

        /**获取状态栏高度 通过xxx.updateStatusBarHeight(num)方法回调给web*/
        @JavascriptInterface
        fun client_getStatusBarHeight() {
            mActivity.runOnUiThread {
                callback.getStatusBarHeight()
            }
        }

        /**
         * 深色状态栏
         * isDark：web实现时状态栏是深色时传true，浅色时传false
         * 原生处理深色状态栏文字变浅色效果
         */
        @JavascriptInterface
        fun client_darkStatusBar(isDark: Boolean) {
            mActivity.runOnUiThread {
                callback.darkStatusBar(isDark)
            }
        }

        /**
         * 调用原生，refresh为true主动刷新token，false获取token
         * 获取token后，通过xxx.updateToken(String)通知到web页面
         * 本地请求中如果涉及到token更新，也是通过此方式通知到web页面
         */
        @JavascriptInterface
        fun client_getToken(refresh: Boolean) {
            mActivity.runOnUiThread {
                callback.getToken(refresh)
            }
        }

        /**
         * 获取当前公司信息 通过xxx.updateCompanyInfo(String)通知到web页面
         */
        @JavascriptInterface
        fun client_getCompanyInfo() {
            mActivity.runOnUiThread {
                callback.getCompanyInfo()
            }
        }

        /**获取当前人员信息 通过xxx.updateUserInfo(String)通知到web页面*/
        @JavascriptInterface
        fun client_getUserInfo() {
            mActivity.runOnUiThread {
                callback.getUserInfo()
            }
        }

        /**
         * 选择图片 最少1张，最多9张，单选图片传1，多张图片时，如果有最多图片限制，需要计算好可以再选择图片张数传参
         * 选择结果压缩后，生成base64，通过xxx.getBase64String(String)返回给web页面
         */
        @JavascriptInterface
        fun client_choosePic(json: String) {
            mActivity.runOnUiThread {
                callback.choosePic(json)
            }
        }

        /**拍照 同上*/
        @JavascriptInterface
        fun client_openCamera(json: String) {
            mActivity.runOnUiThread {
                callback.takeCamera(json)
            }
        }

        /**
         * 请求定位信息，原生需要获取定位权限成功后，通过xxx.getLocationInfo(String)返回给web页面
         * 包含四个属性：lat,lng,address,poiName
         */
        @JavascriptInterface
        fun client_requestLocation(once: Boolean) {
            mActivity.runOnUiThread {
                callback.requestLocation(once)
            }
        }

        @JavascriptInterface
        fun client_stopLocation() {
            mActivity.runOnUiThread {
                callback.stopLocation()
            }
        }

        /**
         * web 调用原生 获取当前设备信息
         * web接收方法，findDeviceInfo(json:String)
         * 返回json格式：{"device":"设备唯一标识","deviceModel":"设备型号信息"}
         */
        @JavascriptInterface
        fun client_getDeviceInfo() {
            mActivity.runOnUiThread {
                callback.getDeviceInfo()
            }
        }

        /**
         * web 调用原生 获取当前设备连接wifi信息
         * web接收方法，findWifiInfo(json:String)
         * 返回json格式：{"bssid":"wifi设备地址","ssid":"wifi名称","wifiState":0} 未获取到wifi信息时，ssid 为空字符串
         * wifiState： 1 获取到wifi信息 0 未获取到 -1 无权限
         */
        @JavascriptInterface
        fun client_getWifiInfo() {
            mActivity.runOnUiThread {
                callback.getWifiInfo()
            }
        }

        /**
         * 预览图片 json 内容格式：{"index":Int,"list":["图片链接",""]}
         *          list  文件链接图片
         *          index 多张图片时，指定图片数组中预览哪一张图片
         */
        @JavascriptInterface
        fun client_previewPics(json: String) {
            mActivity.runOnUiThread {
                callback.previewPics(json)
            }
        }

        /**
         * 在线预览文件 json 内容格式：{"link":"","name":"","ext":""}
         *          link 文件链接url
         *          name 文件名
         *          ext  文件后缀 文件在线预览服务需要
         */
        @JavascriptInterface
        fun client_previewFileOnLine(json: String) {
            mActivity.runOnUiThread {
                callback.previewFile(json)
            }
        }

        /**选择时间 支持年月日时分
         * timeMode int 0：年月日时分，1：年月日，2：时分
         * 通过objName.updateTimeSelect("时间戳")
         */
        @JavascriptInterface
        fun client_selectTime(timeMode: Int) {
            mActivity.runOnUiThread {
                callback.selectTime(timeMode)
            }
        }

        //访客管理选择时间
        //参数sign，web页用来做标记，直接连同时间戳传回去就行；
        @JavascriptInterface
        fun client_visitorChooseDate(sign: String) {
            mActivity.runOnUiThread {
                Loggerr.i("选择时间", "==传递的值=sign=${sign}")
                callback.visitorChooseData(sign)
            }
        }

        /**
         * web获取云存储相关配置信息，通过objName.updateCosInfo(data)通知到web页面
         * 返回json数据格式:{"cosAppId":"","cosRegion":"","urlPre":"拼接图片公共cdn地址"}
         */
        @JavascriptInterface
        fun client_getCosInfo() {
            mActivity.runOnUiThread {
                callback.getCosInfo()
            }
        }

        /**
         * 系统返回键 会通过objName.onBackPress()通知到web页面，web页面确定后退按键显示，
         * 如果确定不再显示其他页面，交还给系统处理（关闭页面）时，调用 client_goBack() 即可
         */
        @JavascriptInterface
        fun client_goBack() {
            mActivity.runOnUiThread {
                callback.onFinish()
            }
        }

        @JavascriptInterface
        fun client_popBack(){
            mActivity.runOnUiThread {
                callback.popBack()
            }
        }

        @JavascriptInterface
        fun client_jumpPage(params: String) {
            mActivity.runOnUiThread {
                Loggerr.i("跳转参数", "params=${params}")
                val json = JSONObject(params)
                when (json.optString("path")) {
                    "1" -> { //ok
                        callback.onLaunchApproval(json.optString("modelId"))
                    }
                    "2" -> { //ok
                        callback.toAteRule(json.optString("ateId"), json.optString("version"))
                    }
                    "3" -> {
                        val array = json.getJSONArray("ateId")
                        val deptList = arrayListOf<String>()
                        for (i in 0 until array.length()) {
                            deptList.add(array.optString(i))
                        }
                        callback.onSelectDept(deptList, json.optString("date"))
                    }
                    "4" -> {
                        callback.toAteRemind()
                    }
                    "5" -> { //ok
                        callback.toAteResultShare(
                            json.optString("url"),
                            json.optString("content"),
                            json.optString("name"),
                            json.optString("time")
                        )
                    }
                    "6" -> {
                        callback.openAppSetting()
                    }
                    "7" -> {
                        callback.openGpsSetting()
                    }
                    "8" -> {
                        callback.openWifiSetting()
                    }
                    "9",
                    "10",
                    "11" -> {
                        GsonUtil.fromJson<ShareFileBean>(params)?.let {
                            callback.shareFile(it)
                        }
                    }
                    "12" -> {//分享链接到微信
                        GsonUtil.fromJson<ShareLinkBean>(params)?.let {
                            callback.shareLink(it)
                        }
                    }
                    "13" -> {//跳转到选择组织成员界面
                        GsonUtil.fromJson<ChoosePersonBean>(params)?.let {
                            callback.choosePerosn(it)
                        }
                    }
                    "14" -> {//分享小程序到微信
                        Loggerr.i("分享到微信", "==${params}====")
                        GsonUtil.fromJson<MiniProgramBean>(params)?.let {
                            callback.shareMiniProgramToWx(it)
                        }
                    }

                }
            }
        }

        @JavascriptInterface
        fun client_cache(key: String, data: String) {
            mActivity.runOnUiThread {
                callback.cacheData(key, data)
            }
        }

        @JavascriptInterface
        fun client_findCache(method: String, key: String) {
            mActivity.runOnUiThread {
                callback.findCacheData(method, key)
            }
        }

        //复制文字
        @JavascriptInterface
        fun client_copy(data: String) {
            mActivity.runOnUiThread {
                callback.copyData(data)
            }
        }

        //js截屏保存当前页面以及是否分享
        @JavascriptInterface
        fun client_saveImageWithScreen(data: String) {
            mActivity.runOnUiThread {
                callback.saveImageToShare(data)
            }
        }

        //web调用移动端播放视频，去掉所有视频控制功能，只播放（可以暂停）
        //body:{"url":"","second":"","field":""}
        @JavascriptInterface
        fun client_playVideo(params: String) {
            //
        }

        // wps附件预览
        @JavascriptInterface
        fun client_fileInfo(params: String) {
            Timber.i("webview: client_fileInfo $params")
            mActivity.runOnUiThread {
                callback.callJsWps()
            }
        }

        @JavascriptInterface
        fun kingdee_goBack(params: String){
            mActivity.runOnUiThread{
                callback.kingdee_goBack(params)
            }
        }


        @JavascriptInterface
        fun client_setCurrentLocation(params: String) {
            mActivity.runOnUiThread {
                callback.client_setCurrentLocation(params)
            }
        }

        @JavascriptInterface
        fun client_goNavigationSoftware(json: String) {
            mActivity.runOnUiThread {
                callback.client_goNavigationSoftware(json)
            }
        }

        @JavascriptInterface
        fun client_setAutoPosition(params: String) {
            mActivity.runOnUiThread {
                callback.client_setAutoPosition(params)
            }
        }

        @JavascriptInterface
        fun client_allowsGestures(param: String) {
            mActivity.runOnUiThread {
                callback.client_allowsGestures(param)
            }
        }

        @JavascriptInterface
        fun client_route_web2app(params: String) {
            mActivity.runOnUiThread {
                callback.routeFlutter(params)
            }
        }


        // js 获取 亮度调节
        @JavascriptInterface
        fun client_getBrightness(params:String) {
            Timber.i("client_getBrightness ==> params")
        }

        // js 设置屏幕亮度
        @JavascriptInterface
        fun client_settBrightness(params:String) {
            Timber.i("client_settBrightness ==> params")
        }

    }

    override fun shareMiniProgramToWx(miniBean: MiniProgramBean) {
        val helper = WechatHelper()
        helper.initApi(requireContext()) {
            helper.sendMiniProgramToWx(miniBean)
        }
    }

    //截屏并分享
    override fun saveImageToShare(data: String) {
        val saveImageBean = GsonUtil.fromJson(data, SaveImageBean::class.java)
        when (saveImageBean?.type) {
            "0" -> {
                //保存图片tcp
                XUtil.catchScreenWithPerssion(mActivity, onSuccess = { screenFile ->
                    if (saveImageBean.list.isNullOrEmpty()) {//不需要裁剪
                        XUtil.saveImageToAlbum(mActivity, screenFile, result = {})
                    } else {//需要裁剪
                        val tailBean = saveImageBean.list.get(0)
                        val tailResultFile =
                            XUtil.tailorImageFile(mActivity, screenFile, tailerBean = tailBean)
                        if (tailResultFile != null) {
                            XUtil.saveImageToAlbum(mActivity, tailResultFile, result = {})
                        }
                    }
                })
            }
            "1" -> {
                val helper = WechatHelper()
                //分享
                XUtil.catchScreenWithPerssion(mActivity, onSuccess = { screenFile ->
                    if (saveImageBean.list.isNullOrEmpty()) {//不需要裁剪
                        //分享原图
                        // 分享图片到微信
                        helper.initApi(requireContext()) {
                            val bitmap = BitmapFactory.decodeFile(screenFile.absolutePath)
                            helper.sendImgToWx(bitmap)
                        }
                    } else {//需要裁剪
                        val tailBean = saveImageBean.list.get(0)
                        val tailResultFile =
                            XUtil.tailorImageFile(mActivity, screenFile, tailerBean = tailBean)
                        if (tailResultFile != null) {
                            //分享裁剪后的图片
                            helper.initApi(requireContext()) {
                                val bitmap = BitmapFactory.decodeFile(tailResultFile.absolutePath)
                                helper.sendImgToWx(bitmap)
                            }
                        }
                    }
                })
            }
        }
    }

    override fun kingdee_goBack(data: String) {
        // 关闭当前web页，将参数传递给flutter
        onFinish()
        EventBus.getDefault().post(EventApproveDeleteKingdee(name = data))
    }

    override fun client_setAutoPosition(json: String) {
        try {
            Timber.i("client_setAutoPosition --> $json")
            val tiandiBody = GsonUtil.fromJson<TiandituBody>(json)
            if (tiandiBody == null){
                return
            }

            val param = mapOf<String, Any?>(
                "address" to (tiandiBody.address ?:""),
                "positioning" to (tiandiBody?.poi ?:""),
                "latitude" to (tiandiBody?.lat ?: 0.0),
                "longitude" to (tiandiBody.lon ?: 0.0),
            )
            //todo 传递Flutter 页面
            Timber.i("client_setAutoPosition==> $param");

        }catch (e: Exception){
            e.printStackTrace()
        }
    }

    override fun client_goNavigationSoftware(json: String){
        navigate2Map(requireContext() ,json)
    }

    override fun client_setCurrentLocation(json: String) {
        // web call native 调用
        try {
            Timber.i("client_setCurrentLocation--> $json")
//            EventBus.getDefault().post(EventTiandituParam(json))
            sendLocation(json)
        }catch (e: Exception){
            toast("未获取到位置")
            e.printStackTrace()
        }
    }

    private fun sendLocation(json: String) {
        val jsonObject = JSONObject(json)
        val lat = jsonObject.getDouble("lat")
        val lng = jsonObject.getDouble("lon")

        val intent = Intent()
        intent.putExtra("latitude", lat)
        intent.putExtra("longitude", lng)
        intent.putExtra("address", jsonObject.getString("address"))
        intent.putExtra("city", jsonObject.getString("city"))
        intent.putExtra("title", jsonObject.getString("poi"))

        val uri = createMapImage(lng,lat)

        when (CommonWebActivity.locationType) {
            "selectMapLocation" -> {
                intent.putExtra("locuri", uri)
                requireActivity().setResult(RESULT_OK, intent)
            }
            "addAttendanceLocation", //添加考勤位置
                //选择位置
            "selectLocation" -> {
                intent.putExtra("uri", uri)
                requireActivity().setResult(Activity.RESULT_OK, intent)
            }
        }
        requireActivity().finish()
    }

    override fun client_allowsGestures(json: String) {
        try {
            Timber.i("client_allowsGestures => $json")
            val body = GsonUtil.fromJson<ClientCanBack>(json)
            canBackPageByJs = body?.allow == 1
        }catch (e: Exception){
            e.printStackTrace()
        }
    }

    override fun routeFlutter(json: String) {
        try {
            Timber.i("routeFlutter--> $json")
            EventBus.getDefault().post(EventRouteFlutter(json))
        }catch (e: Exception){}
    }

    override fun callJsWps() {
        val wpsFileInfo = ObjectStore.remove("wpsfileInfo") as? Map<String,String>
        wpsFileInfo?.let {
            invokeJs("getFileInfo", GsonUtil.toJson(it))
        }
    }

    //调起微信支付
    override fun onWxPayStart(req: PayReq) {
        if (this::api.isInitialized) {
            api.sendReq(req)
        }
    }

    //支付结果通知js,“1”代表成功，“0”代表失败,微信支付结果通知web
    private fun notictPayToJs(resultCode: String) {
        invokeJs("backWXPayResult", resultCode)
    }

    override fun onWebReady(
        title: String,
        subTitle: String,
        objName: String,
        useTitleBar: Boolean
    ) {
        if (!objName.isNullOrBlank()) {
            this.objName = objName
        }
        Loggerr.i("webview调试", "===h5正在调用client_isAlready==原生通过updateClientInfo方法将token等信息返回给web=")
        updateTitleBar(useTitleBar, title, subTitle)
        // web初始化完成后，回调token，用户信息，当前团队信息到web页面
        updateClientInfo()
    }

    private fun updateTitleBar(useTitleBar: Boolean, title: String, subTitle: String = "") {
        if (useTitleBar) {
            immersionBar?.titleBar(llTitleBar)?.init()
            tvTitle.text = title
            llTitleBar.visibility = View.VISIBLE
            ivLeft.visibility = View.VISIBLE
            tvTitle.visibility = View.VISIBLE
            ivLeftClose.visibility=View.VISIBLE
            if (!subTitle.isNullOrBlank()) {
                tvSubTitle.visibility = View.VISIBLE
                tvSubTitle.text = subTitle
            } else {
                tvSubTitle.visibility = View.GONE
            }
        } else {
            immersionBar?.titleBar(null)?.init()
            llTitleBar.visibility = View.GONE
            ivLeft.visibility = View.GONE
            tvTitle.visibility = View.GONE
            updateStatusBarHeight()
        }
    }

    override fun darkStatusBar(isDark: Boolean) {
        showLog("web 调用 动态设置状态栏字体颜色 $isDark")
        updateStatusBar(!isDark)
//        updateStatusBarAndColor(!isDark, R.color.white)
    }

    private fun updateClientInfo() {
        val map = hashMapOf<String, Any>()
        map["token"] = UserHolder.getAccessToken()
        Loggerr.i("webview调试", "==返回的token=${UserHolder.getAccessToken()}=")
        if (UserHolder.isLogin()) {
            map["user"] = UserHolder.getCurrentUser()!!
        }
        if (CompanyHolder.getCurrentOrg() != null) {
            map["company"] = CompanyHolder.getCurrentOrg()!!
        }
        if (statusHeight == 0) {
            statusHeight = ScreenUtils.px2dip(
                requireContext(),
                ScreenUtils.getStatusBarHeight2(requireContext())
            )
        }
        map["statusHeight"] = statusHeight
        invokeJs("updateClientInfo", GsonUtil.toJson(map))
    }

    override fun getStatusBarHeight() {
        showLog("web 调用 获取状态栏高度")
        updateStatusBarHeight()
    }

    /**web实现标题栏时，通知web状态栏高度*/
    private fun updateStatusBarHeight() {
        if (statusHeight == 0) {
            statusHeight = ScreenUtils.px2dip(
                requireContext(),
                ScreenUtils.getStatusBarHeight2(requireContext())
            )
        }
        invokeJs("updateStatusBarHeight", statusHeight.toString())
    }

    override fun getToken(refresh: Boolean) {
        Loggerr.i("webview调试", "===web正在请原生的token===")
        if (refresh) {
//            Flowable.create({ emitter: FlowableEmitter<String> ->
//                val call = LoginService.refreshToken(UserHolder.getRefreshToken())
//                val tokenBean = call.execute().body()
//                if (tokenBean != null) {
//                    UserHolder.saveToken(tokenBean)
//                    emitter.onNext(UserHolder.getAccessToken())
//                } else {
//                    emitter.onNext("-2")
//                }
//            }, BackpressureStrategy.BUFFER)
//                .subscribeOn(Schedulers.io())
//                .observeOn(AndroidSchedulers.mainThread())
//                .subscribe(object : BaseSubscriber<String>() {
//                    override fun onError(ex: ApiException) {
////                        EventBusUtils.sendEvent(EventBusEvent(IMEvent.MULTIUSER_LOGIN, "请重新登录"))
//                    }
//
//                    override fun onComplete() {
//                    }
//
//                    override fun onNext(token: String?) {
//                        if (!token.isNullOrBlank() && token != "-2") {
//                            Loggerr.i("webview调试", "===原生将token返回给web=token=${token}==")
//                            val json = JSONObject()
//                            json.put("token", token)
//                            updateWebToken()
//                        } else {
////                            EventBusUtils.sendEvent(EventBusEvent(IMEvent.MULTIUSER_LOGIN, "请重新登录"))
//                        }
//                    }
//                })
        } else {
            updateWebToken()
        }
    }

    private fun updateWebToken() {
        val token = UserHolder.getAccessToken()
        if (!token.isNullOrBlank()) {
//            invokeJs("updateToken", "12345")
//            invokeJs("updateToken", URLEncoder.encode(token, "UTF-8"), true)
//            invokeJs("updateToken", token.substring(token.indexOf(" ") + 1), true)
            Loggerr.i("webview调试", "===原生返回token时，真正的值=token=${token}==")
            invokeJs("updateToken", "\"" + token + "\"")
        } else {
            showLog("token不存在")
        }
    }

    override fun getCompanyInfo() {
        showLog("web 调用 获取公司信息")
        updateCompanyInfo()
    }

    private fun updateCompanyInfo() {
        var org = CompanyHolder.getCurrentOrg()
        if (!companyId.isNullOrBlank() && CompanyHolder.getTotalCompanyIdMap().containsKey(companyId)) {
            if (org == null || companyId != org.companyId) {
                org = CompanyHolder.getTotalCompanyIdMap()[companyId]
            }
        }
        companyId = org?.companyId ?: ""
        invokeJs("updateCompanyInfo", GsonUtil.toJson(org))
    }

    override fun getUserInfo() {
        showLog("web 调用 获取用户信息")
        updateUserInfo()
    }

    private fun updateUserInfo() {
        if (UserHolder.isLogin()) {
            invokeJs("updateUserInfo", GsonUtil.toJson(UserHolder.getCurrentUser()))
        }
    }

    private var currentImageRequest = ""

    override fun choosePic(json: String) {
        showLog("web 调用 choosePic 获取选择图片 ${json}")
        currentImageRequest = json
        choosePic(JSONObject(json).optInt("count", 1))
    }

//    private val selector = PicSelector2(this)

    override fun takeCamera(json: String) {
        currentImageRequest = json
        PictureNewHelper.beforeSelectPhoto(this, ConsKeys.TAKE_PHOTO)
//        selector.onSelect(ConsKeys.TAKE_PHOTO)
    }

    fun choosePic(count: Int) {
        when {
            count < 0 -> {
                PictureNewHelper.beforeSelectPhoto(this, maxSelectNum = 1)
//                selector.onSelect2(limit = 1)
            }
            count > 9 -> {
                PictureNewHelper.beforeSelectPhoto(this, maxSelectNum = 9)
//                selector.onSelect2(limit = 9)
            }
            else -> {
                PictureNewHelper.beforeSelectPhoto(this, maxSelectNum = count)
//                selector.onSelect2(limit = count)
            }
        }
    }

    private fun afterFileChooseGoing(resultCode: Int, data: Intent?) {
        Loggerr.i("web文件选择", "==选择文件的=回调执行了===")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            if (fileChooseCallback == null) {
                return
            }
            val uris = WebChromeClient.FileChooserParams.parseResult(resultCode, data)
            fileChooseCallback?.onReceiveValue(uris)
        }
        fileChooseCallback = null
    }

    private fun afterFileTakeGoing(resultCode: Int, data: Intent?) {
        Loggerr.i("web文件选择", "==拍照=回调执行了===")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            updatePhotos()
            if (fileChooseCallback == null) {
                return
            }
            if (data != null) {
                Loggerr.i("web文件选择", "===调用系统相机===")
                val uri:Uri?=data.data
                if (uri==null) {
                    //拍摄
                    val resultBitmap=data.extras?.get("data") as Bitmap?
                    Loggerr.i("web文件选择", "===调用系统相机=拍摄====不指定文件名称且是缩略图=====")
                   val resultFile= CommonUtils.saveBitmap(mActivity,
                       resultBitmap,"${System.currentTimeMillis()}.jpg","webPhoto")
                    if (resultFile!=null) {
                        val resultUri=  if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                            FileProvider.getUriForFile(mActivity,mActivity.packageName+".fileProvider",resultFile)
                        }else{
                            Uri.fromFile(resultFile)
                        }
                        if (resultUri != null) {
                            val result= arrayOf(resultUri)
                            fileChooseCallback?.onReceiveValue(result)
                        }else{
                            fileChooseCallback?.onReceiveValue(null)
                        }
                    }else{
                        fileChooseCallback?.onReceiveValue(null)
                    }
                }else{
                    //选择
                    Loggerr.i("web文件选择", "===调用系统相机==选择======")
                    if (uri != null) {
                        val result= arrayOf(uri)
                        fileChooseCallback?.onReceiveValue(result)
                    }else{
                        fileChooseCallback?.onReceiveValue(null)
                    }
                }
            }else{
                //指定拍照的文件名时才会用到
                Loggerr.i("web文件选择", "===调用系统相机==拍摄==指定文件名称===是原图===")
                imageUri?.let {
                    val resultBitmap=BitmapFactory.decodeStream(mActivity.contentResolver.openInputStream(it))
                    val resultFile= CommonUtils.saveBitmap(mActivity,
                        resultBitmap,"${System.currentTimeMillis()}.jpg","webPhoto")
                    if (resultFile!=null) {
                        val resultUri=  if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                            FileProvider.getUriForFile(mActivity,mActivity.packageName+".fileProvider",resultFile)
                        }else{
                            Uri.fromFile(resultFile)
                        }
                        if (resultUri != null) {
                            val result= arrayOf(resultUri)
                            fileChooseCallback?.onReceiveValue(result)
                        }else{
                            fileChooseCallback?.onReceiveValue(null)
                        }
                    }else{
                        fileChooseCallback?.onReceiveValue(null)
                    }
                }
            }

        }
        fileChooseCallback = null
        imageUri=null
    }

   private fun updatePhotos() {
// 该广播即使多发（即选取照片成功时也发送）也没有关系，只是唤醒系统刷新媒体文件
       val intent = Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
       intent.setData(imageUri);
       mActivity.sendBroadcast(intent);
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        Timber.i("webfragment onActivityResult = ")
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == IMAGE_CUT_CODE) {
                if (data != null) {
//                    val selectList = PictureSelector.obtainMultipleResult(data)
                    if (fileChooseCallback != null) {
                        val selectList = PictureNewHelper.afterSelectPhotoUris(mActivity, data)
                        showLog("返回选择文件信息")
                        fileChooseCallback?.onReceiveValue(selectList.toTypedArray())// web 直选文件回调
                    } else {
                        val selectList = PictureNewHelper.afterSelectPhotoPaths(data)
                        if (selectList.isNotEmpty()) {
                            val temp = arrayListOf<String>()
                            for (picPath in selectList) {
                                val bm: Bitmap = BitmapFactory.decodeFile(picPath)
                                val baos = ByteArrayOutputStream()
                                //1.5M的压缩后在100Kb以内，测试得值,压缩后的大小=94486字节,压缩后的大小=74473字节
                                //这里的JPEG 如果换成PNG，那么压缩的就有600kB这样
                                //图片处理，图片转成base64，其他人写的
                                bm.compress(Bitmap.CompressFormat.JPEG, 40, baos)
                                temp.add(Base64.encodeToString(baos.toByteArray(), Base64.DEFAULT))
                            }
                            transPicToWeb(temp)
                        }
                    }
                    fileChooseCallback = null // 不释放不能下次选取图片
                    showLog("返回选择文件信息 之后")
                } else {
                    fileChooseCallback?.onReceiveValue(arrayOf())
                    fileChooseCallback = null // 不释放不能下次选取图片
                }
            } else if (requestCode == request_code_file_chooser_for_web) {
                Loggerr.i("web文件选择", "=RESULT_OK==回调给web执行===")
               afterFileChooseGoing(resultCode,data)
            }else if (requestCode==request_code_file_capture_for_web) {
                Loggerr.i("web文件选择", "=RESULT_OK==回调给web执行===")
                afterFileTakeGoing(resultCode,data)
            } else if (requestCode == 10004) {
                invokeJs("refreshApproval")
            }
        } else if(resultCode==Activity.RESULT_CANCELED){
            Loggerr.i("web文件选择", "===取消文件选择器=requestCode=${requestCode}==")
            if (requestCode == request_code_file_chooser_for_web) {
                afterFileChooseGoing(resultCode, data)
            }else if(requestCode == request_code_file_capture_for_web){
                afterFileTakeGoing(resultCode,data)
            }
        }else{
            Loggerr.i("web文件选择", "===其他回调===")
            if (fileChooseCallback != null) {
                fileChooseCallback?.onReceiveValue(arrayOf())
            }
            fileChooseCallback = null // 不释放不能下次选取图片
            imageUri=null
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

    private fun transPicToWeb(temp: List<String>) {
        val json = JSONObject(currentImageRequest)
        json.put("images", GsonUtil.toJson(temp))

        invokeJs("getBase64String", json.toString())
        currentImageRequest = ""

    }

    /**
     * true 单次定位
     * false 持续定位
     */
    private var locationOnce = true
    override fun requestLocation(once: Boolean) {
        showLog("web请求${if (once) "单次" else "持续"}定位服务")
        locationOnce = once
        if (!locationOnce || LocationSupport().checkGpsOpenState(mActivity)) {
            val perms = arrayOf(
                Manifest.permission.ACCESS_COARSE_LOCATION,
                Manifest.permission.ACCESS_FINE_LOCATION
            )
            PermissionUtils.requestPermissionFragment(
                this, perms,
                "获取当前位置需要使用位置权限",
                onSuccess = {
                    showLog("开启地图定位图层")
                    locationListener = LocationListener { location ->
                        val geocoder = Geocoder(requireContext(), Locale.CHINA)
                        try {
                            val addressList = geocoder.getFromLocation(location.latitude,location.longitude,1)
                            if (addressList != null) {
                                for (address in addressList){
                                    Log.d("test","requestLocation详细数据  国家==${address.countryName}  数据==${address}")
                                    val locationInfo = LocationResult(
                                        lat = location.latitude,
                                        lng = location.longitude,
                                        address = address.featureName ?: "",
                                        name = address.featureName ?: ""
                                    )
                                    onLocationInfo(locationInfo, 1)

                                }
                            }
                            if(addressList?.size == 0){
                                Log.d("test","getNativeLocation-没有解析到位置信息")
                                val locationInfo = LocationResult(
                                    lat = location.latitude,
                                    lng = location.longitude,
                                    address = "",
                                    name =  ""
                                )
                                onLocationInfo(locationInfo, 0)
                            }
                        }catch (exception:Exception){
                            Log.d("test","getNativeLocation-解析位置出错")
                            val locationInfo = LocationResult(
                                lat = location.latitude,
                                lng = location.longitude,
                                address = "",
                                name =  ""
                            )
                            onLocationInfo(locationInfo, 0)
                        }
                    }
                    LocationUtils.getCurrentLocation(requireContext(),locationListener!!)
                },
                onError = {
                    showLog("获取当前位置需要使用位置权限")
                    try {
                        ToastUtil.show(requireContext(), "获取当前位置需要授予位置权限")
                    } catch (e: Exception) {
                        try {
                            ToastUtil.show(mActivity, "获取当前位置需要授予位置权限")
                        } catch (e: Exception) {
                        }
                    }
                    onLocationInfo(LocationResult(), -2) // 暂无定位权限时回调
                },
                selfControl = true
            )
        } else {
            onLocationInfo(LocationResult(), -1) // 未开启GPS时回调定位失败
        }
    }

    override fun stopLocation() {
//        if (locationService != null) {
//            locationService?.apply {
//                showLog("注销定位回调和停止定位服务")
////                unregisterListener(locateListener)
////                stop()
//                LocationUtils.removeListener(this)
//            }
//        }
        if (locationListener != null){
            LocationUtils.removeListener(locationListener!!)
            locationListener = null
        }
    }

    //预览图片
    override fun previewPics(json: String) {
        if (!json.isNullOrBlank() && json.startsWith("{") && json.endsWith("}")) {
            val data = JSONObject(json)
            val temp = arrayListOf<String>()
            val array = data.optJSONArray("list") ?: return
            if (array.length() > 0) {
                for (i in 0 until array.length()) {
                    temp.add(array[i] as String)
                }
                val index = if (temp.size > 1) {
                    data.optInt("index")
                } else {
                    0
                }
                TaskImagePreviewActivity.openImageList(requireActivity() , temp ,index)
            }
        }
    }

    override fun previewFile(json: String) {
        if (!json.isNullOrBlank() && json.startsWith("{") && json.endsWith("}")) {
            val data = JSONObject(json)
            val name = data.optString("name")
            FilePreviewProvider.getPreviewPost(
                name, 0L, data.optString("link"), data.optString("ext")
            ).withBoolean("onlyShow", true).navigation(mActivity)
        }
    }

    //选择时间示例，timeMode int 0：年月日时分，1：年月日，2：时分
    override fun selectTime(timeMode: Int) {
        if (timePickerUtil == null) {
            initCustomTimePicker()
        }
        showCustomPicker(timeMode) {
            //获取到时间
            invokeJs("updateTimeSelect", it.time.toString())
        }
    }

    //访客管理选择时间
    override fun visitorChooseData(sign: String) {
        if (timePickerUtil == null) {
            initCustomTimePicker()
        }
        showCustomPicker(0) {
            //获取到时间
            val map = linkedMapOf<String, Any>()
            map.put("sign", sign)
            map.put("timeStamp", it.time.toString())
            val resultJson = GsonUtil.toJson(map)
            Loggerr.i("选择时间", "==传递的值=result=${resultJson}")
            invokeJs("getTime", resultJson)
        }
    }

    private var timePickerUtil: TimePickerUtil? = null

    private fun initCustomTimePicker() {
        timePickerUtil = TimePickerUtil(mActivity)
        timePickerUtil!!.initCustomTimePicker(mode = 0, calendars = timePickerUtil!!.getCalendars())
        timePickerUtil!!.setPickerTypes(arrayOf(0, 2))
    }

    private fun showCustomPicker(dateType: Int = 0, result: (date: Date) -> Unit) {
//        0：年月日时分，1：年月日，2：时分
        timePickerUtil?.showCustomPicker(dateType) { _, date ->
            result.invoke(date)
        }
    }

    override fun getCosInfo() {
        showLog("web 调用 获取COS信息")
        invokeJs(
            "updateCosInfo", GsonUtil.toJson(
                hashMapOf(
                    "cosAppId" to FileStorage.COS_APPID,
                    "cosRegion" to FileStorage.COS_REGION,
                    "urlPre" to FileStorage.TOS_DEFAULT_URL_PRE
                )
            )
        )
    }

    /**
     * @param locationState
     * 1 定位成功
     * 0 未获取到定位
     * -1 未开启定位 gps开关未打开
     * -2 未授权
     */
    fun onLocationInfo(result: LocationResult, locationState: Int) {
        val map = hashMapOf<String, Any>()
        map["address"] = result.address
        map["poiName"] = result.name
        map["lat"] = result.lat
        map["lng"] = result.lng
        map["locationState"] = locationState
        invokeJs("getLocationInfo", GsonUtil.toJson(map))
        if (locationOnce) {
            showLog("当前单次定位服务即将结束了")
            stopLocation()
            if (!result.locationSuccess) {
                showToast("获取当前位置信息失败，请打开GPS后重试")
            }
        }
    }

    private var locationService: LocationService? = null
    private var locationListener: LocationListener? = null

    /**定位回调*/
    private var locateListener: MyLocationListener = MyLocationListener()

    inner class MyLocationListener : LocationCallback {

        override fun getTag(): String = "common_web"

        override fun onLocationResult(locationInfo: LocationResult) {
            if (!locationInfo.locationSuccess) {
                onLocationInfo(LocationResult(), 0)// 定位失败时回调
            } else {
                onLocationInfo(locationInfo, 1)
            }
        }

        override fun onPoiLocationResult(locationInfo: LocationResult) {
            if (!locationInfo.locationSuccess) {
                onLocationInfo(LocationResult(), 0)// 定位失败时回调
            } else {
                onLocationInfo(locationInfo, 1)
            }
        }
    }

    private fun initLocate() {
//        if (locationService == null) {
//            locationService = (requireActivity().application as BaseApplication).locationService
//        }
//        locationService?.initLocation()
//        if (!locationService!!.isRegister(locateListener)) {
//            locationService?.apply {
//                val option = getOption()
//                option.setIsNeedLocationPoiList(true)
//                setLocationOption(option)
//            }
//        }
    }

    override fun goBack() {
        if (isShowBack==1) {
            Loggerr.i("点击返回", "====原生的goBack()===")

            if (!canBackPageByJs) return

            if (webview?.canGoBack()?:false) {
                webview?.goBack()
            } else {
                onFinish()
            }
        } else {
            Loggerr.i("点击返回", "====调用web的返回方法===")

            if (titleName=="金蝶审批") {
                if (webview?.canGoBack()?:false) {
                    webview?.goBack()
                } else {
                    onFinish()
                }
            }else{
                invokeJs("onBackPress")
            }
        }
    }

    @Subscribe
    fun flutterJsGoBack(b: EventWebViewGoBack) {
        goBack()
    }
    @Subscribe
    fun flutterJsGoBack(event: EventTakeMealSuccess) {
        showLog("=================调用js===================：${event.json}")
        invokeJs("gopickupsuccess",event.json)
    }

    override fun onFinish() {
        showLog("web 调用原生 或原生主动  结束页面")
//        onDestroyView()
//        EventBus.getDefault().post(EventCloseWeb())

        if (isKingdee() || isLocation()){
            mActivity.finish()
        }else{
            onDestroyView()
            EventBus.getDefault().post(EventCloseWeb())
        }
    }


    override fun popBack() {
        goBack()
    }

    fun isKingdee() : Boolean {
       return targetUrl.contains("kingdee.ddbes.com") || targetUrl.contains("mbos.kdeascloud.com")
    }
    fun isLocation() : Boolean {
        return targetUrl.contains(TIANDITU_URL)
    }




    override fun getDeviceInfo() {
        showLog("web 调用 获取设备信息")
        invokeJs(
            "findDeviceInfo",
            "{\"device\":\"${MMKVUtil.getString(MMKVKEY.IMEI)}\",\"deviceModel\":\"${
                DeviceUtil.getValueEncoded(
                    ConsValue.deviceModel
                )
            }\"}"
        )

//        val map = hashMapOf("device" to MMKVUtil.getString(MMKVKEY.IMEI), "deviceModel" to DeviceUtil.getValueEncoded(ConsValue.deviceModel))
//        invokeJs("findDeviceInfo", GsonUtil.toJson(map))
    }

    @SuppressLint("WifiManagerLeak")
    override fun getWifiInfo() {
        showLog("web 调用 获取wifi信息")
        //获取当前连接的WiFi信息
        fun getCurrentWifiInfo() {
            WifiService().registerWifiStateReceiver(mActivity)
            val mWifiManager =
                mActivity.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            //当前的WiFi连接信息
            val currentConnectionInfo = mWifiManager.connectionInfo
            //当前的WiFi的名称与bssid地址
            val mCurrentBSSID = currentConnectionInfo.bssid
            var mCurrentSSID = currentConnectionInfo.ssid
            if (mCurrentSSID != null && mCurrentBSSID != null && mCurrentBSSID != "00:00:00:00:00:00" && "<unknown ssid>" != mCurrentSSID) {
                if (mCurrentSSID!!.contains("\"")) {
                    mCurrentSSID = mCurrentSSID!!.substring(1, mCurrentSSID!!.lastIndexOf("\""))
                }
                findWifiInfo(mCurrentBSSID, mCurrentSSID, 1)
            } else {
                findWifiInfo(wifiState = 0)
            }
        }

        val perms = arrayOf(Manifest.permission.ACCESS_WIFI_STATE)
        val tip = "需要您同意使用获取当前WIFI，才能继续查看内容"
        PermissionUtils.requestPermissionFragment(mFragment, perms,
            "获取当前WIFI需要WIFI权限", {
                getCurrentWifiInfo()
            }, {
                ToastUtil.show(requireActivity(), "获取当前WIFI需要WIFI权限")
                findWifiInfo(wifiState = -1)
            }, preTips = tip)
    }

    /**@param wifiState： 1 获取到wifi信息 0 未获取到 -1 无权限*/
    private fun findWifiInfo(bssid: String = "", ssid: String = "", wifiState: Int = 0) {
//        invokeJs("findWifiInfo", "{\"bssid\":\"$bssid\",\"ssid\":\"$ssid\",}")
        invokeJs("findWifiInfo", """{"bssid":"$bssid","ssid":"$ssid","wifiState":$wifiState}""")
//        val map = hashMapOf("bssid" to bssid, "ssid" to ssid)
//        invokeJs("findWifiInfo", GsonUtil.toJson(map))
    }

    override fun onLaunchApproval(modelId: String) {
        showLog("web 调用 跳转审批 $modelId")
        if (StringUtils.isNotBlankAndEmpty(modelId) && StringUtils.isNotBlankAndEmpty(companyId)) {
            val bundle = Bundle()
            bundle.putString(ConsKeys.MODEL_ID, modelId)
            bundle.putString(ConsKeys.COMPANY_ID, companyId)
            ARouter.getInstance().build("/approval/create")
                .with(bundle).navigation(mActivity, 10004)
        }
    }

    override fun onSelectDept(dept: ArrayList<String>, date: String) {
        showLog("web 调用 跳转部门选择")
        ARouter.getInstance().build(RouteOrg.ORG_SECRET_VISIBLE_RANGE)
            .withInt(CommonKeys.PAGE_TYPE, 1)
            .withString(CommonKeys.DATE, date)// 根据时间获取团队历史架构
            .withInt(CommonKeys.POSITION, if (dept.isEmpty()) 0 else 1)
            .withString(ConsKeys.COMPANY_ID, companyId)
            .withString(CommonKeys.TITLE_NAME, "查看范围")
            .withString(CommonKeys.RIGHT_TITLE, "完成")
            .withStringArrayList(CommonKeys.DATA, dept)
            .withStringArrayList("source", arrayListOf("整个团队", "选择部门"))
            .navigation(mActivity, RouteOrg.CODE_CHECK_ORG_RANGE)
    }

    override fun toAteResultShare(url: String, content: String, name: String, time: String) {
        val imageUrl = if (StringUtils.isNotBlankAndEmpty(url)) {
            url
        } else {
            "http://cdn.ddbes.com/LOGO/recordShare/%E5%9B%BE%E5%B1%82%201.png"
        }
        ARouter.getInstance().build("/work/attendanceShareActivity")
            .withString("resultImage", imageUrl)
            .withString("resultContent", content)
            .withString("resultName", name)
            .withString("attendanceResultTime", time).navigation()
    }

    override fun openAppSetting() {
        val intent = Intent()
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
        intent.data = Uri.fromParts("package", mActivity.packageName, null)
        startActivity(intent)
    }

    override fun openGpsSetting() {
//        LocationSupport().checkGpsOpenState(mActivity)
//        val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
//        startActivityForResult(intent, openGpsRequest)
        startActivity(Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS))
    }

    override fun openWifiSetting() {
        startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
    }

    /**type 9 分享给担当 11 分享给微信好友 10 邮箱*/
    private var shareInfo: ShareFileBean? = null
    //工单详情，点击导出，点击分享，再点击担当、微信或者邮箱后执行；
    override fun shareFile(fileBean: ShareFileBean) {
        showLog("$fileBean")
        when (fileBean.path) {
            "9" -> {
//                showToast("分享到担当")
                if (fileBean.fileId.isNullOrBlank() || fileBean.fileUrl.isNullOrBlank()) return

                fun selectMember() {
                    shareInfo = fileBean

                    ARouter.getInstance().build(RouteOrg.selectMemberShareActivity)
                        .withString("pageFlag", "select_share_target")
                        .navigation()
                }

                if (fileBean.fileSize >= 1024 * 1024 * 100) {
                    fileBean.msgType = 0
                    // 分享链接到担当
                    selectMember()
                } else {
                    fileBean.msgType = 1
                    // TODO: 2021/7/1 15:05  转存文件到Im存储桶，选择目标成员，查询对应的imId，最后发送im消息
                    fun transFileToImBucket() {
                        val bundle = Bundle()
                        bundle.putString("fileId", fileBean.fileId)
                        bundle.putString("fileName", fileBean.fileName)
                       if(StringUtils.isNotBlankAndEmpty(fileBean.approvePdfId)){
                           bundle.putString("typePdf", "approve")
                       }else if(StringUtils.isNotBlankAndEmpty(fileBean.ticketPdfId)){
                           bundle.putString("typePdf", "ticket")
                       }
                        (ARouter.getInstance().build(RouteProvider.APR_FUN_PROVIDER)
                            .navigation() as RouteServiceProvider)
                            .service("transFileToImBucket", bundle) {
                                if (!it.isNullOrBlank() && !it.startsWith("error")) {
                                    fileBean.fileId = it // 更新为转存到im中的fileId
                                    selectMember()
                                } else {
                                    showToast("转存到im存储失败")
                                }
                            }
                    }
                    transFileToImBucket()
                }
            }
            "11" -> {
                //分享到微信
                if (fileBean.fileId.isNullOrBlank() || fileBean.fileUrl.isNullOrBlank()) return
                val helper = WechatHelper()
                if (fileBean.fileSize >= 1024 * 1024 * 10) {
                    // 分享链接到微信
//                    val fileIcon = FileUtil.getFileTypeIcon(fileBean.fileName)
//                    val thumbBmp = BitmapFactory.decodeResource(mActivity.resources, fileIcon)
//                    val icon = JavaUtils.bmpToByteArray(thumbBmp, true)
//                    if (icon != null && icon.isNotEmpty()) {
                    helper.initApi(requireContext()) {
//                            helper.sendLinkToWx(icon, fileBean.fileName, CommonUtils.convertFileSize(fileBean.fileSize), fileBean.fileUrl)
                        helper.sendTextToWx(
                            "分享审批附件《${fileBean.fileName}》有效时间3天：\n${Html.fromHtml(fileBean.fileUrl)}",
                            fileBean.fileName
                        )
                    }
//                    }
                } else {
                    val localPath =
                        requireContext().cacheDir.absolutePath + File.separator + "share_file" + File.separator + fileBean.fileName

                    // 下载文件后分享到微信
                    fun getDownLoadUrl3() {
//                    val targetDir = DOWNLOAD_DIR
                        val targetDir =
                            requireContext().cacheDir.absolutePath + File.separator + "share_file"
                        val targetName = fileBean.fileName
                        showLog("downloadUrl = [${fileBean.fileUrl}]")
                        showLog("downloadUrl2 = [${Html.fromHtml(fileBean.fileUrl)}]")

                        LoadingManager.showLoadingDialog(requireContext())


                        OkHttpUtils.get().url(fileBean.fileUrl).build()
                            .execute(object : FileCallBack(
                                targetDir, targetName
                            ) {

                                override fun onResponse(file: File?, p1: Int) {
                                    //当文件下载完成后回调
                                    LogUtil.showLog("文件下载 完成 ${file?.absolutePath}")

                                    LoadingManager.showLoadingDialog(requireContext())
                                    val fileIcon = FileUtil.getFileTypeIcon(fileBean.fileName)
                                    if (file != null && file.exists()) {
                                        helper.initApi(requireContext()) {
                                            helper.shareFileToWx(file, fileIcon)
                                        }
                                    }
                                }

                                override fun onError(p0: Call?, e: Exception?, code: Int) {
                                    LoadingManager.dismissLoadingDialog()
                                    LogUtil.showLog("文件下载 失败 $code -- ${e?.message}")
                                    showToast("分享失败")
                                }

                                override fun inProgress(progress: Float, total: Long, id: Int) {
                                    //progress*100为当前文件下载进度，total为文件大小
                                    LogUtil.showLog("文件下载 进行中 $progress / $total")
                                    if ((progress * 100).toInt() % 10 == 0) {
                                        //避免频繁刷新View，这里设置每下载10%提醒更新一次进度
                                    }
                                }
                            })
                    }

                    fun getDownLoadUrl2() {
                        if (fileBean.approvePdfId.isNullOrBlank()) return
                        val bundle = Bundle()
                        bundle.putString("approvePdfId", fileBean.approvePdfId)
                        (ARouter.getInstance().build(RouteProvider.APR_FUN_PROVIDER)
                            .navigation() as RouteServiceProvider)
                            .service("getExportFileUrl", bundle) {
                                showLog("下载地址为：${it}")
                                // {"code":1,"msg":"请求成功","data":{"approvePdfId":"2526516646470750205","exportName":"2021-07-06 16:25:39导出小四-请假申请","approveId":"2522239838296474621","userId":"2488484430818051069","createTime":1625559939464,"fileId":"2526516646470749181","type":1,"organizationId":"2518723558234915837","fileName":"2021-07-06 16:25:39导出小四-请假申请.pdf","url":"https://ddbes-pan-test-**********.cos.ap-chongqing.myqcloud.com/2526516645397005309?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKID0oNgiTYBdrob6n8RU7TYYHPJfYAYtpqp%26q-sign-time%3D1625621318%3B1625623118%26q-key-time%3D1625621318%3B1625623118%26q-header-list%3D%26q-url-param-list%3Dresponse-cache-control%3Bresponse-content-disposition%3Bresponse-content-language%3Bresponse-expires%26q-signature%3D3c8de3d19cac434d840d02baed63622355270129&response-cache-control=no-cache&response-content-disposition=attachment%3B%20filename%3D%222021-07-06%2B16%253A25%253A39%25E5%25AF%25BC%25E5%2587%25BA%25E5%25B0%258F%25E5%259B%259B-%25E8%25AF%25B7%25E5%2581%2587%25E7%2594%25B3%25E8%25AF%25B7.pdf%22&response-content-language=zh-CN&response-expires=Thu%2C%2008%20Jul%202021%2001%3A28%3A38%20GMT","fileSize":0}}

                            }
                    }

                    fun getDownLoadUrl1() {
                        FileDownTransferManager.singleFileDownload(
                            11,
                            FileTransferInfo(
                                fileId = fileBean.fileId,
                                srcPath = localPath,
                                total = fileBean.fileSize
                            ),
                            onSuccess = {
                                val file = File(localPath)
                                if (file.exists()) {
                                    helper.initApi(requireContext()) {
                                        helper.shareFileToWx(
                                            file,
                                            com.joinutech.ddbeslibrary.R.mipmap.ic_file_type_pdf
                                        )
                                    }
                                }
                            },
                            onError = {
                                requireContext().toastShort("分享失败")
                            },
                            onCreateTask = {},
                            onTransfer = {})
                    }

                    getDownLoadUrl3()
                }
            }
            "10" -> {
//                showToast("分享到邮箱")
                if (fileBean.fileId.isNullOrBlank()) return
                val bundle = Bundle()
                bundle.putString("type", "email")
                bundle.putString("name", UserHolder.getCurrentUser()?.email)
                bundle.putInt("requestCode", 1009)
                (ARouter.getInstance().build(RouteProvider.WORK_PROVIDER)
                    .navigation() as RouteServiceProvider)
                    .openPageWithResult(requireActivity(), "checkEmail", bundle) {
                        if (it == "success") {
                            // // TODO: 2021/7/1 15:07  调用接口分享到邮箱
                            val bundle = Bundle()
                            if(StringUtils.isNotBlankAndEmpty(fileBean.approvePdfId)){
                                bundle.putString("approvePdfId", fileBean.approvePdfId)
                            }else if(StringUtils.isNotBlankAndEmpty(fileBean.ticketPdfId)){
                                bundle.putString("ticketPdfId", fileBean.ticketPdfId)
                            }
                            (ARouter.getInstance().build(RouteProvider.APR_FUN_PROVIDER)
                                .navigation() as RouteServiceProvider)
                                .service("transFileToEmail", bundle) { result ->
                                    if ("success" == result) {
                                        showToast("已发送到邮箱，请注意查收！")
                                    }
                                }
                        }
                    }
            }
            else -> {

            }
        }
    }

    override fun shareLink(linkBean: ShareLinkBean) {
        when (linkBean.path) {
            "12" -> {
                if (linkBean.fileUrl.isNullOrBlank() || linkBean.thumImage.isNullOrBlank()) return
                val helper = WechatHelper()
                val base64ByteArray = JavaUtils.base64ToByteArray(linkBean.thumImage)
                // 分享链接到微信
                helper.initApi(requireContext()) {
                    helper.sendLinkToWx(
                        base64ByteArray,
                        linkBean.fileName,
                        linkBean.content,
                        linkBean.fileUrl
                    )
                }
            }
            else -> {

            }
        }
    }

    override fun choosePerosn(chooseBean: ChoosePersonBean) {
//        Loggerr.i("返回的users","users=${chooseBean.users}")
        /* val tempList = GsonUtil.fromJson2<ArrayList<Any>>(
             chooseBean.users,
             object : TypeToken<ArrayList<Any>>() {}.type
         )*/
        /* val resul= Gson().fromJson<ArrayList<Any>>(chooseBean.users,object:TypeToken<ArrayList<Any>>(){}.type)
          Loggerr.i("返回的users","users==2=${GsonUtil.toJson(resul)}")*/
//跳转到选择组织成员界面，，，组织人员列表，审核人列表
        ARouter.getInstance()
            .build(RouteOrg.searchOrgPersonActivity)
            .withSerializable("userChoosedList", chooseBean.users)//已经选择好的人的集合
            .withString("isMulty", chooseBean.type)//1是单选，2是多选
            .withString("isOwner", chooseBean.owner)//1是可以选择自己，0是不可以选择自己
            .withString("title", "请选择人员")
            .withString("companyId", companyId)
            .withString("eventCode", "webPage")//获取到结果后会通过这个字段发送eventBus事件
            .navigation()
    }

    @Subscribe
    fun closeFlutterCurrentPage(body: EventReOpenWeb) {

        val map = body.appChatId
        if (map == null) return
        val url = map.get("url") as String

        Timber.i("url =>>>>>>>>>> $url")

        loadUrl(url , isRefresh = false)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSelectMemberResult(event: EventBusEvent<List<OrgImportPeopleBean>>) {
        if (event.code == "select_share_target" && !event.data.isNullOrEmpty() && event.data is List<OrgImportPeopleBean>) {
//            requireContext().toastShort("分享给 ${event.data?.joinToString { it.name }}")

            val bundle = Bundle()
            bundle.putString("userId", userId)
            val method = if (1 == shareInfo?.msgType) {
                bundle.putString("fileId", shareInfo?.fileId)
                bundle.putString("fileName", shareInfo?.fileName)
                bundle.putLong("fileSize", shareInfo?.fileSize ?: 0L)
                "sendFileMsg"
            } else {
                bundle.putString(
                    "content",
                    "分享审批附件《${shareInfo?.fileName}》有效时间3天：\n${Html.fromHtml(shareInfo?.fileUrl)}"
                )
                "sendTextMsg"
            }

            /**向用户发送im消息 文件消息或链接（文本）消息*/
            fun sendMessage(bean: OrgImportPeopleBean) {
                bundle.putSerializable("userInfo", UserInfo(bean.userId, bean.headimg, bean.name))
                (ARouter.getInstance().build(RouteProvider.IM_PROVIDER)
                    .navigation() as RouteServiceProvider)
                    .service(method, bundle) {
                        if ("success" == it) {
                            LogUtil.showLog("分享到担当成功 $it")
                            showToast("分享到担当成功")
                        } else {
                            LogUtil.showLog("分享到担当失败 $it")
                            showToast("分享到担当失败")
                        }
                    }
            }

            /**获取用户的imId*/
            fun findUserImId(userIds: List<String>) {
                // TODO: 2021/7/2 9:12 获取imId 后 调用im发送消息
                val api =
                    RetrofitClient.single_intance.getRxRetrofit().create(PersonApi::class.java)
                RxScheduleUtil.rxSchedulerHelper(api.getUserImId(userIds))
                    .compose(ErrorTransformer.getInstance())
                    .subscribe(object : BaseSubscriber<Any>() {
                        override fun onError(ex: ApiException) {
                        }

                        override fun onComplete() {

                        }

                        override fun onNext(t: Any?) {
//                                if (1 == shareInfo["msgType"]) {
//                                    bundle.putString("fileId", shareInfo["fileId"] as String)
//                                    bundle.putString("fileName", shareInfo["fileId"] as String)
//                                    bundle.putLong("fileSize", shareInfo["fileSize"] as Long)
//                                } else {
//                                    bundle.putString("content", shareInfo["fileUrl"] as String)
//                                }
//                                sendMessage()
                        }
                    })
            }
//
//            findUserImId(event.data!!.map { it.userId }.toList())

            event.data?.forEach {
                sendMessage(it)
            }
        }
    }

    //接收到选择组织成员的结果
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun receiveStringEvent(event: EventBusEvent<String>) {
        if (event.data != null && event.data is String) {
            when (event.code) {
                "webPage" -> {
                    val personsString = event.data
                    Loggerr.i("选择成员返回", "personsString=${personsString}")
                    invokeJs("getMembers", personsString)
                }
            }
        }
    }

    //接收微信支付结果的回调
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun receivePayResultEvent(event: EventBusEvent<String>) {
        if (event.data != null && event.data is String) {
            when (event.code) {
                PushEvent.TCP_WX_PAY_RESULT_CODE -> {//接收到微信支付结果
                    val resultCode = event.data
                    if (resultCode != null) {
                        if (resultCode == "0") {
                            notictPayToJs("1")
                        } else {
                            notictPayToJs("0")
                        }

                    }
                }
            }
        }
    }

    override fun cacheData(key: String, data: String) {
        if (UserHolder.isLogin()) {
            val realKey = "${UserHolder.getUserId()}_web_$key"
            val oldValue = MMKVUtil.getString(realKey)
            val map = if (oldValue.isNotBlank()) {
                GsonUtil.fromJson2(oldValue, object : TypeToken<HashMap<String, Any>>() {}.type)
                    ?: hashMapOf()
            } else {
                hashMapOf<String, Any>()
            }
            val subMap: HashMap<String, Any>? =
                GsonUtil.fromJson2(data, object : TypeToken<HashMap<String, Any>>() {}.type)
            if (!subMap.isNullOrEmpty()) {
                map.putAll(subMap)
                MMKVUtil.saveString(realKey, GsonUtil.toJson(map))
            }
        }
    }

    override fun findCacheData(receiveMethod: String, key: String) {
        if (UserHolder.isLogin()) {
            val realKey = "${UserHolder.getUserId()}_web_$key"
            invokeJs(receiveMethod, MMKVUtil.getString(realKey))
        }
    }

    //复制到剪贴板
    override fun copyData(data: String) {
        XUtil.toClipboard(mActivity, data) {
            showToast("复制成功")
        }
    }

    private val memberIdList = arrayListOf<String>()

    fun selectMember(companyId: String, companyName: String) {
        ARouter.getInstance()
            .build(RouteOrg.orgSelectMemberActivity)
            .withBoolean("needLoad", true)
            .withString("companyId", companyId)
            .withString("companyName", companyName)
//                .withString("depId", "0")
//                .withString("depName", depName)
            .withStringArrayList("depMember", memberIdList)
            .navigation()
    }

    override fun toAteRule(ateId: String, version: String?) {
        showLog("web 调用 跳转考勤规则 $ateId")
//        val postcard = ARouter.getInstance().build("/work/AttendanceRuleActivity")
        val postcard = ARouter.getInstance().build("/work/AteOrderRuleActivity")
            .withString(ConsKeys.MODEL_ID, ateId)
        if (!version.isNullOrBlank()) {
            postcard.withString("version", version)
        }
        postcard.navigation()
    }

    override fun toAteRemind() {
        showLog("web 调用 跳转考勤提醒")
        ARouter.getInstance().build("/work/ateRemindActivity").navigation()
    }

    override fun onStop() {
        showLog("页面关闭，即将关闭定位服务")
        stopLocation()
        super.onStop()
    }

    override fun onDestroyView() {
        Timber.i("native webview onDestroyView....")
        immersionBar?.apply {
            keyboardEnable(false).keyboardMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        }

        if (webview != null) {
            webview?.loadDataWithBaseURL(null, "", "text/html", "utf-8", null)
            webview?.clearHistory()
            val parentView=webview?.parent as ViewGroup
            parentView.removeView(webview)
            webview?.destroy()
            webview=null
        }

        super.onDestroyView()
    }

    //交互调用js
    private fun invokeJs(method: String, data: String? = null) {
        if (isWebDebug) {
            if (objName.isBlank()) {
                showToast("client_isAlready 未调用")
                return
            }
        }
        Loggerr.i("invokeJs", "===执行了=objName=${objName}===")
        val jsCode = if (data.isNullOrBlank()) {
            "javascript:window.${objName}.${method}()"
        } else {
            "javascript:window.${objName}.${method}($data)"
        }
        showLog("调用js：$jsCode")
        webview?.loadUrl(jsCode)
    }

    override fun openEventBus(): Boolean = true
}

interface OnCommonWebCallback {

    fun onWxPayStart(req: PayReq)

    fun onWebReady(title: String, subTitle: String, objName: String, useTitleBar: Boolean)

    /**深色状态栏触发，默认白色状态栏，黑色字体*/
    fun darkStatusBar(isDark: Boolean)

    /**获取状态栏高度*/
    fun getStatusBarHeight()

    /**
     * 调用原生，主动刷新token，获取token后，通过xxx.updateToken(String)通知到web页面
     * 本地请求中如果涉及到token更新，也需要通过此方式通知到web页面
     */
    fun getToken(refresh: Boolean)

    /**获取当前公司信息*/
    fun getCompanyInfo()

    /**获取当前人员信息*/
    fun getUserInfo()

    /**选择图片，选择结果压缩后，生成base64，通过xxxx.getBase64String(String)返回给web页面*/
    fun choosePic(json: String = "")

    /**拍照 同上*/
    fun takeCamera(json: String)

    /**
     * 请求定位信息，原生需要获取定位权限成功后，通过xxxx.getLocationInfo(String)返回给web页面
     * 包含四个属性：{"lat":0.0,"lng":0.0,"address":"","poiName":"","locationState":0} lat 和 lng 都为0.0 表示定位失败
     * 持续定位 once = false
     * 单次定位 once = true
     * locationState: 取值
     * 1 定位成功
     * 0 未获取到定位
     * -1 未开启定位
     * -2 未授权
     */
    fun requestLocation(once: Boolean)

    /**停止定位*/
    fun stopLocation()

    /**
     * web 调用原生 获取当前设备信息
     * web接收方法，findDeviceInfo(json:String)
     * 返回json格式：{"device":"设备唯一标识","deviceModel":"设备型号信息"}
     */
    fun getDeviceInfo()

    /**
     * web 调用原生 获取当前设备连接wifi信息
     * web接收方法，findWifiInfo(json:String)
     * 返回json格式：{"bssid":"wifi设备地址","ssid":"wifi名称"} 未获取到wifi信息时，ssid 为空字符串
     */
    fun getWifiInfo()

    /**预览图片*/
    fun previewPics(json: String)

    /**预览文件*/
    fun previewFile(json: String)

    /**选择时间*/
    fun selectTime(timeMode: Int)

    //访客管理选择时间
    fun visitorChooseData(sign: String)

    /**web 调用原生 获取云存储appId和区域公共数据*/
    fun getCosInfo()

    /**系统返回键 发送交互到web页面*/
    fun goBack()

    fun popBack()

    /**web 调用原生 返回键 关闭当前页面*/
    fun onFinish()

    /**
     * web 调用原生 跳转审批发起
     *  modelId: 审批模板id
     * 审批成功后，web接收方法：refreshApproval()
     */
    fun onLaunchApproval(modelId: String)

    /**跳转考勤规则页面*/
    fun toAteRule(ateId: String, version: String?)

    /**考勤提醒设置*/
    fun toAteRemind()

    /**
     * web 调用原生 跳转审批发起
     *  modelId: 审批模板id
     * 审批成功后，web接收方法：refreshApproval()
     */
    fun onSelectDept(dept: ArrayList<String>, date: String)

    /**
     * 跳转考勤结果分享页面
     */
    fun toAteResultShare(url: String, content: String, name: String, time: String)

    /**打开应用设置*/
    fun openAppSetting()

    /**打开GPS设置页面*/
    fun openGpsSetting()

    /**打开wifi设置*/
    fun openWifiSetting()

    /**type 9 分享给担当 11 分享给微信好友 10 邮箱*/
    fun shareFile(file: ShareFileBean)

    //分享链接到微信
    fun shareLink(link: ShareLinkBean)

    /**调用原生缓存数据*/
    fun cacheData(key: String, data: String)

    /**读取原生缓存数据*/
    fun findCacheData(receiveMethod: String, key: String)

    /**调用原生复制数据到剪贴板*/
    fun copyData(data: String)

    //选择组织成员
    fun choosePerosn(chooseBean: ChoosePersonBean)

    //分享小程序到微信
    fun shareMiniProgramToWx(miniBean: MiniProgramBean)

    //截屏当前页面并分享（根据参数决定是否分享）
    fun saveImageToShare(data: String)

    fun callJsWps()

    fun kingdee_goBack (data: String)

    /**web 调用原生 返回位置相关信息*/
    ///{"address":"京津冀大数据创新应用中心","town":"耀华道街道","nation":"中国","city":"廊坊市","county_code":"156131003","poi_position":"西南","county":"广阳区","city_code":"156131000","address_position":"西南","poi":"京津冀大数据创新应用中心","province_code":"156130000","town_code":"156131003007","province":"河北省","road":"润泽道","road_distance":250,"address_distance":89,"poi_distance":89,"lon":116.72155,"lat":39.5975}
    fun client_setCurrentLocation(locationJson: String)

    fun client_setAutoPosition(locationJson: String)

    /// 1 tx; 2 gaode ;3 baidu
    fun client_goNavigationSoftware(json: String)

    // js 控制 native 是否可以点击导航按钮
    fun client_allowsGestures(json: String)

    fun routeFlutter(json: String)
}

data class ShareFileBean(
    val path: String, var fileId: String, val fileName: String,
    val fileUrl: String, val fileSize: Long, val approvePdfId: String? = "", var msgType: Int = 0,
    var ticketPdfId: String? = ""
)

data class ShareLinkBean(
    val path: String, var fileUrl: String, var fileName: String,
    var content: String, var thumImage: String
)

data class ChoosePersonBean(
    val path: String, var type: String, var owner: String,
    var users: ArrayList<OrgPersonBean>
)

//分享小程序到微信专用
data class MiniProgramBean(
    val description: String,
    val hdImageData: String,
    val miniProgramType: Int,
    val path: String,
    val programPath: String,
    val title: String,
    val userName: String,
    val webpageUrl: String
)


data class SaveImageBean(
    val type: String,
    var list: ArrayList<TailerBean>
)

//图片裁剪数据
data class TailerBean(
    val top: String,
    val bottom: String,//是距离底部的距离
    val left: String,
    val right: String,
    val height: String//需要截取的高度，不为0，则以该值为准，为0，则以“总高度-top-bottom”为准；

)



data class ClientCanBack(var allow: Int = 0) : Serializable