package com.joinutech.ddbeslibrary.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewStub
import android.widget.FrameLayout
import androidx.annotation.LayoutRes
import androidx.viewbinding.ViewBinding
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.common.util.CacheHelper
import com.joinutech.common.widget.OnEmptyClickListener
import com.joinutech.common.widget.PageEmptyView
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.utils.AppManager
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.OnNoDoubleClickListener
import com.joinutech.ddbeslibrary.widget.EmptyView
import com.trello.rxlifecycle3.android.ActivityEvent
import com.trello.rxlifecycle3.components.support.RxAppCompatActivity
import io.reactivex.subjects.BehaviorSubject


/**
 * description: 最基本的baseActivity，不建议使用该Activity
 */
abstract class BaseBindingActivity<VB:ViewBinding> : RxAppCompatActivity(), OnNoDoubleClickListener, EmptyView.RefreshListener, OnEmptyClickListener {
    /**
     * 在使用自定义toolbar时候的根布局 =toolBarView+childView
     */
    var baseView: View? = null
    lateinit var binding :VB
    private var mEmptyView: PageEmptyView? = null

    /**
     * 获取contentView 资源id
     */
    abstract val contentViewResId: Int
    abstract fun inflateViewBinding(layoutInflater: LayoutInflater): VB

    //获取自定义toolbarview 资源id 默认为-1，showToolBar()方法必须返回true才有效
    abstract val toolBarResId: Int

    private val lifecycleSubject = BehaviorSubject.create<ActivityEvent>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        AppManager.single_instance.addActivity(this)
        if (openEventBus()) { // 是否接收event事件 需要@Sub
            EventBusUtils.register(this)
        }
        if (openArouterReceive()) {
            //ARouter inject 注入
            ARouter.getInstance().inject(this)
        }
        val extras = intent.extras
        if (null != extras) {
            getBundleExtras(extras)
        }
        beforeSetContent()
        setContentView(contentViewResId)
    }

    override fun setContentView(@LayoutRes layoutResID: Int) {

        baseView = LayoutInflater.from(this).inflate(R.layout.ac_base, null, false)
        if (baseView != null) {
            if (showToolBar() && toolBarResId != -1) {
                //如果需要显示自定义toolbar,并且资源id存在的情况下，实例化baseView;
                val mVs_toolbar = baseView!!.findViewById<View>(R.id.vs_toolbar) as ViewStub//toolbar容器
                mVs_toolbar.layoutResource = toolBarResId//toolbar资源id
                mVs_toolbar.inflate()//填充toolbar
            }
            val flContainer = baseView!!.findViewById<View>(R.id.fl_container) as FrameLayout
            mEmptyView = baseView!!.findViewById(R.id.empty_view) ?: null
//            mEmptyView!!.setListener(this)
            mEmptyView?.clickListener = this
            setContentView(baseView)
            binding = inflateViewBinding(layoutInflater)
            try {
//                LayoutInflater.from(this).inflate(layoutResID, flContainer, true)//子布局
                flContainer.addView(binding.root)
            } catch (e: Exception) {
            }
        }
    }

    override fun onNoDoubleClick(v: View) {
    }

    /**
     * 是否显示通用toolBar
     */
    abstract fun showToolBar(): Boolean

    /**
     * 是否开启eventBus
     */
    open fun openEventBus(): Boolean {
        return true
    }

    /**
     * 是否开启Arouter
     */
    abstract fun openArouterReceive(): Boolean

    /**
     * Bundle  传递数据
     *
     * @param extras
     */
    protected fun getBundleExtras(extras: Bundle) {

    }

    /**
     * NormalEmptyView的显示和隐藏
     *
     * @param isShow
     */
    protected fun setShowEmptyView(isShow: Boolean,content:String="无网络连接",des:String="请检查网络设置") {
        if (mEmptyView != null) {
            if (isShow) {
                mEmptyView?.visibility = View.VISIBLE
                mEmptyView?.setContent(content,des)
            } else {
                mEmptyView?.visibility = View.GONE
            }
        }
    }

    override fun onEmptyRefresh() {

    }

    override fun onDestroy() {
        super.onDestroy()
        AppManager.single_instance.finishActivity(this)

        baseView = null
        if (openEventBus()) {
            EventBusUtils.unregister(this)
        }
    }

    open fun beforeSetContent() {

    }

    override fun onResume() {
        super.onResume()
    }

    override fun onAction(actionCode: Int) {
        if (actionCode != 2) {
            onEmptyRefresh()
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()

        CacheHelper.globalBackListenerTime = System.currentTimeMillis()
    }

}
