package com.joinutech.ddbeslibrary.base.loading

import android.content.Context
import androidx.fragment.app.FragmentActivity


object LoadingManager {
    private const val LOADING_FRAGMENT_TAG = "loadingFragmentTag"
    private var isDlgShowing = false
    private var dialogFragment: LoadingDialog? = null

    @JvmOverloads
    fun showLoadingDialog(context: Context?, loadingText: String? = "") {
        if (context == null || isDlgShowing || context !is FragmentActivity || context.isFinishing) {
            return
        }
        if (dialogFragment == null) {
            dialogFragment = LoadingDialog.newInstance("")

        }
        dialogFragment?.onCloseListener = object : LoadingDialog.OnCloseListener {
            override fun onDisMiss() {
                isDlgShowing = false
            }
        }

        dialogFragment?.let {
            if (!it.isAdded){
                context.supportFragmentManager
                    .beginTransaction().add(it, LOADING_FRAGMENT_TAG).commitAllowingStateLoss()
            }else{
                context.supportFragmentManager
                    .beginTransaction().show(it).commitAllowingStateLoss()
            }
        }
        isDlgShowing = true
    }

    fun dismissLoadingDialog() {
        dialogFragment?.dismissAllowingStateLoss()
        dialogFragment = null
    }
}