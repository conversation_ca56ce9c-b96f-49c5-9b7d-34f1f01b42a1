package com.joinutech.ddbeslibrary.base

import android.net.http.SslError
import android.os.Build
import android.util.Log
import android.view.View
import android.webkit.*
import com.alibaba.android.arouter.facade.annotation.Route
import com.joinutech.common.base.LinkBuilder
import com.joinutech.common.base.isWebDebug
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.utils.ConsValue
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.filePreviewWebActivity
import com.joinutech.ddbeslibrary.utils.toastShort
import com.marktoo.lib.cachedweb.LogUtil
import com.marktoo.lib.cachedweb.CommonWebConfig
import com.marktoo.lib.cachedweb.WebListener
import org.json.JSONObject
import java.net.URLEncoder

@Route(path = filePreviewWebActivity)
class FilePreviewTestActivity : MyUseBaseActivity() {
    override val contentViewResId: Int = R.layout.activity_filepreview_test
    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = false

    private var targetUrl: String = ""
    private var ext: String = ""
    private var useWps: Boolean = false
    private var fileName = ""

    override fun initImmersion() {
        whiteStatusBarBlackFont()
    }

    override fun initView() {
        showBackButton(R.drawable.back_grey, View.OnClickListener { onBackPressed() })
        if (intent != null) {
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("title"))) {
                fileName = intent.getStringExtra("title")!!
                setPageTitle(fileName)
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("targetUrl"))) {
                targetUrl = intent.getStringExtra("targetUrl")!!
            }
            if (StringUtils.isNotBlankAndEmpty(intent.getStringExtra("ext"))) {
                ext = intent.getStringExtra("ext")!!
            }
            useWps = intent.getBooleanExtra("useWps", false)
        }
        if (targetUrl.isNullOrBlank() || ext.isNullOrBlank()) {
            toastShort("文件无法预览")
            finish()
        }
        initWebView(findViewById(R.id.web_view))
    }

    private var commWeb: CommonWebConfig? = null
    private var canGoBack = 0

    private fun initWebView(webView: WebView) {
        commWeb = CommonWebConfig(this, webView)
        commWeb?.let {
            it.debug = isWebDebug
            it.cacheable = false
            it.autoWide = true
            it.zoomable = true
            it.multiWindow = false
            it.defaultEncoding = "utf-8"
//            it.userAgent = "ddbes"
            it.jsBridge = true
            it.applyWebSettings()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                webView.settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            }
            it.addInterceptor()
            it.addDefaultClient()
            it.webListener = object : WebListener() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    showLog("load page finished,$url")
                    if (canGoBack == 0) {
                        canGoBack = 1
                    }
                }

                override fun onProgressChanged(view: WebView?, newProgress: Int) {
                    super.onProgressChanged(view, newProgress)
                }

                override fun onHttpError(view: WebView?,
                                         request: WebResourceRequest?,
                                         errorResponse: WebResourceResponse?) {
//                    showLog("Http Error: ${errorResponse?.statusCode}")
                    canGoBack = -1
                    super.onHttpError(view, request, errorResponse)
                }

                override fun onReceivedError(view: WebView?, errorCode: Int?, desc: String?, url: String?) {
                    showLog("receive Http Error: $errorCode : $url")
                    // 在访问失败的时候会首先回调onReceivedError，然后再回调onPageFinished
                    canGoBack = -1
                }

                override fun onReceivedTitle(view: WebView?, title: String?, hasError: Boolean) {
                    showLog("receive title $title")
                    title?.let {
                        if (it.contains("404") || it.contains("500")
                                || it.contains("error", true)
                                || it.contains("网页无法打开")
                                || it.contains("failed", true)) {
                            canGoBack = -1
                        }
                    }
                }

                override fun onSslError(
                        view: WebView?,
                        handler: SslErrorHandler?,
                        error: SslError?
                ) {
//                super.onSslError(view, handler, error)
                    handler?.proceed()
                }
            }
            it.useCached(false)
            webView.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
            it.addMutualInterface(CommonJsCall { data ->
                runOnUiThread {
//                    val json = JSONObject().put("link", targetUrl).put("ext", ext).toString()
                    Log.e("loggin===", targetUrl)
                    val formatData = URLEncoder.encode(targetUrl, "UTF-8").replace("\\+", "%20")
                    Log.e("loggin===", formatData)
                    val json = JSONObject().put("link", formatData).put("ext", ext).toString()
                    showLog("调用web方法，回调加载文件信息 $json")
                    invokeJs("getPreviewFileInfo", json)
                }
            }, "DDBESOFFICE")
        }
    }

    private fun loadUrl(realUrl: String) {
        Log.e("logging==", "加载链接 [ $realUrl ]")
        commWeb?.loadUrl(realUrl, ConsValue.getWebHeaders())
    }

    private fun invokeJs(method: String, data: String) {
        Log.e("logging==", "调用js：$method ：$data")
//        commWeb?.invokeJS("javascript:window.ddbes_common.$method(${data})")
        if (data.isNullOrBlank()) {
            commWeb?.invokeJS("javascript:$method()")
        } else {
            commWeb?.invokeJS("javascript:ddbes_common.$method(${data})")
        }
    }

    override fun initLogic() {
        val loadUrl = if (useWps) {
            LinkBuilder.generate().getFilePreviewPageUrl()
        } else {
            LinkBuilder.generate().getFilePreviewUrl(targetUrl, ext)
        }
        showLog("实际加载地址：$loadUrl")
        loadUrl(loadUrl)
//        loadUrl("https://mobile.ddbes.com/preview/index.html#/?platform=1")
    }

    override fun onBackPressed() {
        if (canGoBack <= 0) {
            finish()
        } else {
            super.onBackPressed()
        }
    }

}

class CommonJsCall(private val callBack: (data: String) -> Unit) {

    @JavascriptInterface
    fun isAlready() {
        LogUtil.showLog("----- 网页初始化完成")
        callBack.invoke("")
    }

    @JavascriptInterface
    fun showJsInfo() {
        callBack.invoke("")
    }

    @JavascriptInterface
    fun showJsInfo2(data: String) {
        callBack.invoke(data)
    }

}
