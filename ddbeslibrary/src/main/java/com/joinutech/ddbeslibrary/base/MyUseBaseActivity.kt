package com.joinutech.ddbeslibrary.base

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.joinutech.common.util.CompanyHolder
import com.joinutech.common.util.NotifyUtil
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.R.id.toolbar
import com.joinutech.ddbeslibrary.utils.BottomDialogUtil
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.IMEvent
import com.joinutech.ddbeslibrary.utils.MMKVKEY.IMEI
import com.joinutech.ddbeslibrary.utils.MMKVUtil
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.widget.LoadingView
import com.joinutech.flutter.EventLoginOut
import com.marktoo.lib.cachedweb.LogUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * description:  本项目运用的activity基类
 */

abstract class MyUseBaseActivity : MyBaseActivity() {

    var mContext: Context? = null

    var mToolbar: Toolbar? = null
    var tv_title: TextView? = null
    var subheadTv: TextView? = null
    var iv_left: ImageView? = null
    var tv_leftTitle: TextView? = null
    var tv_rightTitle: TextView? = null
    var iv_rightTitle: ImageView? = null
    var iv_right_dot: View? = null
    var lay_toolbar_right2: ImageView? = null
    var iv_rightTitle2: ImageView? = null
    var mLoadingDialog: LoadingView? = null
    var commonToolBarId: LinearLayout? = null
    var freeClaimTvToolbarRight: TextView? = null
    var toolbar_line: View? = null
    var savedInstanceState: Bundle? = null

    var titleBarLoadingText: View? = null

    var userId: String = UserHolder.getUserId() ?:""

    var accessToken: String = ""
        get() {
            return UserHolder.getAccessToken()
        }

    val userToken = UserHolder.getUserToken()

    var userName: String? = null
        get() {
            return UserHolder.getCurrentUser()?.name
        }

    //手机唯一标识
    var imei: String = ""

    var mImmersionBar: ImmersionBar? = null

    private var logoutDialog: AlertDialog? = null

   protected var toolBarMemberCount: TextView? = null
   protected var toolBarDistrubIcon: ImageView? = null

    override val toolBarResId: Int = R.layout.layout_common_toolbar

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initConfig()
        this.savedInstanceState = savedInstanceState
        if (mImmersionBar == null) {
            mImmersionBar = ImmersionBar.with(this)
        }

        if (mLoadingDialog == null) {
            mLoadingDialog = LoadingView(this, false)
        }
        if (showToolBar()) {
            mImmersionBar?.titleBar(R.id.toolbar)?.init()
            mToolbar = findViewById<View>(toolbar) as Toolbar
            toolbar_line = findViewById(R.id.toolbar_line)
            if (null != mToolbar) {
                setSupportActionBar(mToolbar)
                supportActionBar?.setDisplayShowTitleEnabled(false)
                initTitle()
            }
        }
        titleBarLoadingText = findViewById(R.id.title_loading_text)

        setToolBarColor(Color.WHITE)
//        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        mContext = this
        initImmersion()
        this.initView()
        this.initLogic()
    }

    @SuppressLint("DefaultLocale")
    private fun initConfig() {
        showLog("当前activity名字：" + this.javaClass.simpleName)
        if (StringUtils.isNotBlankAndEmpty(MMKVUtil.getString(IMEI))) {
            imei = MMKVUtil.getString(IMEI)
        }
    }

    private var isRunning = true
    override fun onResume() {
        super.onResume()
        isRunning = true
        BaseApplication.setCurrentActivity(this.javaClass.name)
//        mForegroundActivity = this
    }

    override fun onPause() {
        isRunning = false
        super.onPause()
    }

    /**登录成功后都进入到HomeActivity页面，在此初始化推送服务*/
    fun onLogin(onSuccess: () -> Unit) {
        // CHANGE_HISTORY: 2021/2/8 16:16  
//        GroupCacheHolder.init()
        onSuccess.invoke()
    }

    /**退出登录，被登出等情况时，需要退出推送服务*/
    fun onPushLogout() {

        // TODO: 解除推送
        //旧push,位置6
      /*  PushCenter.clearNotify(this)
        PushCenter.stopPush(this)*/
        //结束推送
    }

    /**清空用户登录数据，跳转到登录页面*/
    //跳转到登录页面
    open fun onUserLogout(type: Int = 1) {
        NotifyUtil.instance.clearAllNotify()
        BaseApplication.currentNetState = 0
        onPushLogout()
        onCacheLogout()
//        BaseApplication.CacheHolder.onLogout()

        EventBus.getDefault().post(EventLoginOut())
    }

    fun onCacheLogout() {
        UserHolder.onLogout()
        CompanyHolder.onLogout()
        // CHANGE_HISTORY: 2021/2/8 16:16  
//        GroupCacheHolder.onLogout()
    }

    /**
     * 初始化toolbar
     */
    private fun initTitle() {
        if (mToolbar == null) {
            return
        }
        commonToolBarId = findViewById(R.id.commonToolBarId)
        iv_left = mToolbar?.findViewById(R.id.iv_left)
        tv_leftTitle = mToolbar?.findViewById(R.id.toolbar_title_left)//最左边的TextView
        tv_title = mToolbar?.findViewById(R.id.toolbar_title)//中间标题的上标题TextView
        subheadTv = mToolbar?.findViewById(R.id.subheadTv)//中间标题的下标题TextView
        tv_rightTitle = mToolbar?.findViewById(R.id.tv_toolbar_right)//最右边（图标的右边）的TextView
        iv_rightTitle = mToolbar?.findViewById(R.id.iv_right)//最右边的图标imageview
        iv_right_dot = mToolbar?.findViewById(R.id.iv_dot_right)
        iv_rightTitle2 = mToolbar?.findViewById(R.id.iv_toolbar_right2)//从右往左第二个图标imageview
        freeClaimTvToolbarRight = mToolbar?.findViewById(R.id.free_claim_tv_toolbar_right)//从右往左第二个图标的左边的TextView

        toolBarMemberCount = mToolbar?.findViewById(R.id.toolbar_title_member_count)
        toolBarDistrubIcon = mToolbar?.findViewById(R.id.toolbar_title_distrub_icon)

        setCenterTitleNoClick()
    }

    //设置标题
    open fun setPageTitle(title: String) {
//        tv_title?.text = "${title}${envTarget()}"
        if (title.length > 12) {
            tv_title?.text = "${title.substring(0 ,11)}..."
        }else{
            tv_title?.text = "${title}"
        }

        tv_title?.visibility = View.VISIBLE
    }

    open fun setMemberCount(membersSize: Int) {
        toolBarMemberCount?.visibility = View.VISIBLE
        toolBarMemberCount?.text = "(${membersSize})"
    }

    open fun showDistrubStatus(opened: Boolean) {
        toolBarDistrubIcon?.visibility = if (opened) View.VISIBLE else View.GONE
    }

    //设置副标题
    open fun setSubhead(string: String) {
        subheadTv?.text = string
        subheadTv?.visibility = View.VISIBLE
    }

    //隐藏副标题
    open fun hideSubhead() {
        subheadTv?.visibility = View.GONE
    }

    fun setCenterTitleClickListener(listener: View.OnClickListener) {
        if (null != tv_title) {
            tv_title!!.isClickable = true
            tv_title!!.setOnClickListener(listener)
        }
    }

    fun setCenterTitleNoClick() {
        if (null != tv_title) {
            tv_title!!.isClickable = false
            tv_title!!.setOnClickListener(null)
        }
    }

    //设置标题
    fun setPageTitle(string: String, color: Int) {
        if (null != tv_title) {
            tv_title!!.text = string
            tv_title!!.setTextColor(color)
            tv_title!!.visibility = View.VISIBLE
        }
    }

    //设置左标题
    fun setLeftTitle(string: String, color: Int = R.color.color999999) {
        if (null != tv_leftTitle) {
            tv_leftTitle!!.also {
                it.text = string
                it.setTextColor(CommonUtils.getColor(this, color))
                it.visibility = View.VISIBLE
                it.setOnClickListener {
                    finish()
                }
            }
        }
    }

    //设置左标题
    fun setLeftTitle(string: String, listener: View.OnClickListener) {
        if (null != tv_leftTitle) {
            tv_leftTitle!!.text = string
            tv_leftTitle!!.visibility = View.VISIBLE
            tv_leftTitle!!.setOnClickListener(listener)
        }
    }

    fun getLoadingDialog(text: String, outSideCancelable: Boolean) {
        if (isRunning) {
            if (mLoadingDialog == null) {
                mLoadingDialog = LoadingView(mContext, outSideCancelable)
            }
            mLoadingDialog?.setCanceledOnTouchOutside(outSideCancelable)
            mLoadingDialog?.setCancelable(outSideCancelable)
            mLoadingDialog?.show()
        }
    }

    fun showTitleLoading() {
        titleBarLoadingText?.visibility = View.VISIBLE
    }

    fun hideTitleLoading() {
        titleBarLoadingText?.visibility = View.GONE
    }

    @SuppressLint("WrongConstant")
    fun showLogOutDialog(msg: String = "长时间不登录,过期了!" , context: Context? = mContext) {
        if (context == null) return
        if (isRunning) {
            if (logoutDialog == null) {
                val view1 = View.inflate(context, R.layout.dialog_base, null)
                logoutDialog = BottomDialogUtil.showBottomDialog(context!!, view1,
                        Gravity.CENTER, false)
                val confirm = view1.findViewById<TextView>(R.id.confirm_base)
                val cancel = view1.findViewById<TextView>(R.id.cancel_base)
                cancel.visibility = View.GONE
                val tv2 = view1.findViewById<TextView>(R.id.tv2)
                tv2.text = msg

                logoutDialog?.setOnDismissListener {
                    logoutDialog?.dismiss()
                    logoutDialog = null
                }
                confirm.setOnClickListener {
                    logoutDialog?.dismiss()
                    logoutDialog = null
                    onUserLogout()
                }
            } else {
                if (logoutDialog?.isShowing!!) {
                    logoutDialog?.dismiss()
                    logoutDialog?.show()
                }
            }
        }
    }

    fun showLoading(text: String = "", outSizeCancel: Boolean = false) {
        getLoadingDialog(text, outSizeCancel)
    }

    fun hideLoading() {
        dismissDialog()
    }

    /**
     * @Description: 关闭进度条
     */
    fun dismissDialog() {
        if (isRunning) {
            BaseApplication.mMainThreadHandler.postDelayed({
                if (mLoadingDialog != null && mLoadingDialog!!.isShowing && isValidContext(this)) {
                    mLoadingDialog!!.dismiss()
                    mLoadingDialog = null
                }
            }, 200)
        }
    }

    private fun isValidContext(a: Activity): Boolean {
        return if (a.isDestroyed || a.isFinishing) {
            showLog("YXH - Activity is invalid." + " isDestoryed-->" +
                    a.isDestroyed + " isFinishing-->" + a.isFinishing)
            false
        } else {
            true
        }
    }

    //设置左边标题字体尺寸
    fun setLeftTitleTextSize(string: String, listener: View.OnClickListener, color: Int) {
        if (null != tv_leftTitle) {
            tv_leftTitle!!.text = string
            tv_leftTitle!!.visibility = View.VISIBLE
            tv_leftTitle!!.setOnClickListener(listener)
            tv_leftTitle!!.setTextColor(color)
        }
    }

    //设置标题
    fun setPageTitle(id: Int) {
        if (null != tv_title) {
            tv_title!!.setText(id)
            tv_title!!.visibility = View.VISIBLE
        }
    }

    //设置标题颜色
    fun setMiddleTitleColor(color: Int) {
        if (null != tv_title) {
            tv_title!!.setTextColor(color)
        }
    }

    //设置标题颜色
    fun setMyTitleColor(color: Int) {
        if (null != tv_title) {
            tv_title!!.setTextColor(color)
        }
    }

    //显示线条
    fun showToolBarLine() {
        if (toolbar_line != null) {
            toolbar_line!!.visibility = View.VISIBLE
        }
    }

    //显示线条
    fun hideToolBarLine() {
        if (toolbar_line != null) {
            toolbar_line!!.visibility = View.GONE
        }
    }

    //设置toolbar颜色
    private fun setToolBarColor(color: Int) {
        if (null != mToolbar) {
            mToolbar!!.setBackgroundColor(color)
        }
    }

    //显示返回按钮
    fun showBackButton(@DrawableRes res: Int) {
//        if (mToolbar != null) {
//            mToolbar!!.setNavigationIcon(res)
//            mToolbar!!.setNavigationOnClickListener { finish() }
//        }
        iv_left?.let {
            it.visibility = View.VISIBLE
            it.setImageResource(res)
            it.setOnClickListener { finish() }
        }
    }

    fun showBackButtonNoClick(@DrawableRes res: Int) {
//        if (mToolbar != null) {
//            mToolbar!!.setNavigationIcon(res)
//            mToolbar!!.setNavigationOnClickListener(null)
//        }
        iv_left?.let {
            it.visibility = View.VISIBLE
            it.setImageResource(res)
            it.setOnClickListener(null)
        }
    }

    //显示返回按钮
    fun showBackButton(@DrawableRes res: Int, listener: View.OnClickListener?) {
//        if (mToolbar != null) {
//            mToolbar!!.setNavigationIcon(res)
//            if (listener != null) {
//                mToolbar!!.setNavigationOnClickListener(listener)
//            } else {
//                mToolbar!!.setNavigationOnClickListener { finish() }
//            }
//        }
        iv_left?.let {
            it.visibility = View.VISIBLE
            it.setImageResource(res)
            if (listener != null) {
                it.setOnClickListener(listener)
            } else {
                it.setOnClickListener { finish() }
            }
        }
    }

    fun hideBackButton() {
        if (iv_left != null) {
            iv_left?.visibility = View.GONE
        }
    }

    //设置自由认领的tag标记
    fun setFreeClaimShow() {
        if (null != freeClaimTvToolbarRight) {
            freeClaimTvToolbarRight!!.visibility = View.VISIBLE
        }
    }

    fun setFreeClaimHide() {
        if (null != freeClaimTvToolbarRight) {
            freeClaimTvToolbarRight!!.visibility = View.GONE
        }
    }

    fun hideRightText() {
        if (null != tv_rightTitle) {
            tv_rightTitle!!.visibility = View.GONE
        }
    }

    //显示标题栏右边标题
    fun setRightTitle(string: String, listener: View.OnClickListener) {
        if (null != tv_rightTitle) {
            if (!string.isNullOrBlank()) {
                tv_rightTitle!!.text = string
                tv_rightTitle!!.visibility = View.VISIBLE
            } else {
                tv_rightTitle!!.visibility = View.GONE
            }
            tv_rightTitle!!.setOnClickListener(listener)
        }
    }

    //显示标题栏右边标题
    fun setRightTitle(string: Int, listener: View.OnClickListener) {
        if (null != tv_rightTitle) {
            tv_rightTitle!!.setText(string)
            tv_rightTitle!!.visibility = View.VISIBLE
            tv_rightTitle!!.setOnClickListener(listener)
        }
    }

    //设置右边标题字体颜色
    fun setRightTitleColor(color: Int, string: String, listener: View.OnClickListener) {
        if (null != tv_rightTitle) {
            tv_rightTitle!!.visibility = View.VISIBLE
            tv_rightTitle!!.text = string
            tv_rightTitle!!.setTextColor(color)
            tv_rightTitle!!.setOnClickListener(listener)

        }
    }

    fun setRightTitleColor2(color: Int, string: String, listener: View.OnClickListener) {
        if (null != tv_rightTitle) {
            tv_rightTitle!!.visibility = View.VISIBLE
            tv_rightTitle!!.text = string
            tv_rightTitle!!.setTextColor(ContextCompat.getColor(this, color))
            tv_rightTitle!!.setOnClickListener(listener)
        }
    }

    //显示标题栏右边图片
    fun setRightImage(@DrawableRes res: Int, listener: View.OnClickListener) {
        if (null != iv_rightTitle) {
            iv_rightTitle!!.setImageResource(res)
            iv_rightTitle!!.visibility = View.VISIBLE
            iv_rightTitle!!.setOnClickListener(listener)
        }
    }

    fun setRightImage2(@DrawableRes res: Int, listener: View.OnClickListener) {
        if (null != iv_rightTitle2) {
            iv_rightTitle2?.setImageResource(res)
            iv_rightTitle2?.visibility = View.VISIBLE
            iv_rightTitle2?.setOnClickListener(listener)
        }
    }

    fun showRightRedDot() {
        if (null != iv_right_dot) {
            iv_right_dot!!.visibility = View.VISIBLE
        }
    }

    fun hideRightRedDot() {
        if (null != iv_right_dot) {
            iv_right_dot!!.visibility = View.GONE
        }
    }

    fun hideRightImage() {
        if (null != iv_rightTitle) {
            iv_rightTitle!!.visibility = View.GONE
        }
    }

    fun hideRightImage2() {
        if (null != iv_rightTitle2) {
            iv_rightTitle2!!.visibility = View.GONE
        }
    }

    //状态栏白底黑字
    fun whiteStatusBarBlackFont() {
        //原理：如果当前设备支持状态栏字体变色，会设置状态栏字体为黑色，
        // 如果当前设备不支持状态栏字体变色，会使当前状态栏加上透明度，否则不执行透明度
        mImmersionBar!!.statusBarDarkFont(true, 0.2f)
//                .navigationBarColor(R.color.white)
                ?.init()
    }

    fun updateStatusBar(isDarkFont: Boolean) {
        mImmersionBar?.statusBarDarkFont(isDarkFont, 0.2f)?.init()
    }

    /**
     * 初始化沉浸式
     */
    abstract fun initImmersion()

    /**
     * 初始化view
     */
    abstract fun initView()

    /**
     * 初始化逻辑
     */
    open fun initLogic() {
        if (!UserHolder.isLogin()) {
            return
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    open fun globalEvent(event: EventBusEvent<Any>) {//im 事件接收
        when (event.code) {

            IMEvent.MULTIUSER_LOGIN -> {
                // 账号异地登录后关闭房间页面
                showLog("监听到其他位置登录，需要弹窗提示了2")
                showLogOutDialog("您的账号已在其他地方登录，请重新登录")
            }
//            ConsKeys.APP_VERSION_UPDATE -> {
//                if (event.data != null && event.data is String) {
//                    val url = event.data as String
//                    showLog("App强制更新 $url")
//                    if (url.startsWith("http", true) && url.endsWith(".apk", true)) {
////                        if (downDialog == null) {
////                            downDialog = AppVersionCheckUpdateUtil.appDownloadDialog(this)
////                            OkHttpUtils.get().url(url).build().execute(object : FileCallBack(
////                                    DOWNLOAD_DIR, "/ddbes.apk") {
////
////                                override fun onResponse(p0: File?, p1: Int) {
////                                    //当文件下载完成后回调
////                                    showDownProgress("文件下载已完成", 100)
////                                }
////
////                                override fun onError(p0: okhttp3.Call?, p1: Exception?, p2: Int) {
////                                    showDownProgress("文件下载失败" + p1.toString(), -1)
////                                }
////
////                                override fun inProgress(progress: Float, total: Long, id: Int) {
////                                    //progress*100为当前文件下载进度，total为文件大小
////                                    if ((progress * 100).toInt() % 10 == 0) {
////                                        //避免频繁刷新View，这里设置每下载10%提醒更新一次进度
////                                        showDownProgress("文件正在下载..", (progress * 100).toInt())
////                                    }
////                                }
////
////                            })
////                        }
//                    }
//                }
//            }
        }
    }

    override fun onDestroy() {
        savedInstanceState = null
        mContext = null
        if (logoutDialog != null) {
            logoutDialog?.dismiss()
            logoutDialog = null
        }
        if (mLoadingDialog != null) {
            mLoadingDialog?.dismiss()
            mLoadingDialog = null
        }
        super.onDestroy()
    }

    fun showLog(msg: String, tag: String = "base_a_") {
        LogUtil.showLog(msg, tag)
    }

    fun showLog(data: Any, tag: String = "base_a_") {
        showLog(GsonUtil.toJson(data), tag)
    }

    fun <T : ViewModel> getModel(modelClass: Class<T>): T {
        return ViewModelProvider(this).get(modelClass)
    }

}
