package com.joinutech.ddbeslibrary.base

import android.os.Bundle
import android.view.View
import androidx.viewbinding.ViewBinding
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.ddbeslibrary.R

/**
 * @Description: 简单activity基类
 * @Author: zhaoyy
 * @Time:  2020-03-11 11:06
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */

abstract class SimpleBaseActivity<VB: ViewBinding> : MyUseBindingActivity<VB>() {
    override fun initImmersion() {
        whiteStatusBarBlackFont()
    }

    override fun initLogic() {
    }

    override fun setPageTitle(title: String) {
        super.setPageTitle(title)
        showToolBarLine()
        showBackButton(R.drawable.back_grey, View.OnClickListener { onBackPressed() })
    }

    fun jump(path: String, bundle: Bundle? = null, requestCode: Int = 0) {
        val postCard = ARouter.getInstance().build(path)
        if (bundle != null) {
            postCard.with(bundle)
        }
        if (requestCode > 0) {
            postCard.navigation(this, requestCode)
        } else {
            postCard.navigation()
        }
    }

    override fun showToolBar(): Boolean = true

    override fun openArouterReceive(): Boolean = true

}