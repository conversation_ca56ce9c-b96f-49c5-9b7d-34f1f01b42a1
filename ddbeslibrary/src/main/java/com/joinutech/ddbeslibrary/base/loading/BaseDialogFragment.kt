package com.joinutech.ddbeslibrary.base.loading

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.utils.ScreenUtils
import me.jessyan.autosize.internal.CancelAdapt


abstract class BaseDialogFragment : DialogFragment() , CancelAdapt{

    @JvmName("setDialogDisMissListener1")
    fun setDialogDisMissListener(dialogDisMissListener: DialogDisMissListener?) {
        this.dialogDisMissListener = dialogDisMissListener
    }

    protected var dialogDisMissListener: DialogDisMissListener? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
    }

    fun disMissDialogListener() {
        dialogDisMissListener?.disMiss()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnDismissListener { dialog1: DialogInterface? -> disMissDialogListener() }
        return dialog
    }

    override fun show(manager: FragmentManager, tag: String?) {
        try {
            super.show(manager, tag)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun show(fragmentActivity: FragmentActivity?, tag: String?) {
        if (fragmentActivity == null || fragmentActivity.isDestroyed) return
        fragmentActivity.supportFragmentManager.beginTransaction().add(this, tag).commitAllowingStateLoss()
    }

    interface DialogDisMissListener {
        fun disMiss()
    }

    override fun onCancel(dialog: DialogInterface) {
        super.onCancel(dialog)
        disMissDialogListener()
    }



    override fun onResume() {
        super.onResume()
    }

    override fun onPause() {
        super.onPause()
    }

}