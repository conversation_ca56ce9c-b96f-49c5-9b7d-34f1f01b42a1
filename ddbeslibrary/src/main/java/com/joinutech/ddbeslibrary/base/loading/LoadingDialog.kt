package com.joinutech.ddbeslibrary.base.loading

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import com.bumptech.glide.request.RequestOptions
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.utils.GlideApp


class LoadingDialog : BaseDialogFragment() {

    var onCloseListener: OnCloseListener? = null
    private var message: String? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        message = arguments?.getString("message")
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return Dialog(requireActivity(), R.style.base_myDialog1).also {
            it.setCanceledOnTouchOutside(false)
            it.setCancelable(true)
            val inflater = requireActivity().layoutInflater
            val view = inflater.inflate(com.joinutech.ddbeslibrary.R.layout.view_loading, null)
            it.setContentView(view)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val inflater = requireActivity().layoutInflater
        return inflater.inflate(com.joinutech.ddbeslibrary.R.layout.view_loading, null)
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val loadingView = view.findViewById<ImageView>(com.joinutech.ddbeslibrary.R.id.iv_loading_center)
        GlideApp.with(requireContext()).asGif()
            .apply(RequestOptions().skipMemoryCache(true))
            .load(com.joinutech.ddbeslibrary.R.drawable.ic_loading_new2)
            .into(loadingView)
    }
//    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//        launch(block = {
//            ObjectAnimator.ofFloat(binding.linqProgressLoading, "rotation", 0.0f, 360.0f).apply {
//                repeatCount = ValueAnimator.INFINITE
//                duration = 1000
//                start()
//            }
//        })
//    }

    override fun onDismiss(dialog: DialogInterface) {
        onCloseListener?.onDisMiss()
        super.onDismiss(dialog)
    }

    interface OnCloseListener {
        fun onDisMiss()
    }

    companion object {
        fun newInstance(message: String): LoadingDialog {
            return LoadingDialog().also {
                it.arguments = Bundle().also { it.putString("message", message) }
            }
        }
    }
}