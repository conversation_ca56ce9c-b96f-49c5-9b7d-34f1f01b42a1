package com.joinutech.ddbeslibrary.base.customlivedata.base

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer

class OneOffLiveData<T> : MutableLiveData<OneTimeEvent<T>>() {
    /**
     * 替代掉LiveData的observe方法
     */
    fun observeOneOff(owner: LifecycleOwner, func: (T) -> Unit) {
        observe(owner, Observer {
            it.call(func)
        })
    }
}