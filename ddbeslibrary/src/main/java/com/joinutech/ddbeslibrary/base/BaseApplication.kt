package com.joinutech.ddbeslibrary.base

//import io.flutter.view.FlutterMain
import android.annotation.SuppressLint
import android.app.ActivityManager
import android.content.Context
import android.os.Handler
import android.os.Process
import android.text.TextUtils
import androidx.camera.camera2.Camera2Config
import androidx.camera.core.CameraXConfig
import androidx.multidex.MultiDex
import androidx.multidex.MultiDexApplication
import cat.ereza.customactivityoncrash.CustomActivityOnCrash
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.common.base.isDebug
import com.joinutech.common.base.resetEnv
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.request.interceptor.FormatLogger
import com.joinutech.ddbeslibrary.request.interceptor.LoggerInterceptor
import com.joinutech.ddbeslibrary.request.interceptor.RequestEventListener
import com.joinutech.ddbeslibrary.request.interceptor.RequestHeaderInterceptor
import com.joinutech.ddbeslibrary.service.LocationService
import com.joinutech.ddbeslibrary.utils.DOWNLOAD_DIR
import com.joinutech.ddbeslibrary.utils.image.PictureSelectorEngineImp
import com.luck.picture.lib.app.IApp
import com.luck.picture.lib.app.PictureAppMaster
import com.luck.picture.lib.engine.PictureSelectorEngine
import com.marktoo.lib.cachedweb.LogUtil
import com.tencent.mmkv.MMKV
import com.zhy.http.okhttp.OkHttpUtils
import com.zhy.http.okhttp.https.HttpsUtils
import me.jessyan.autosize.AutoSize
import me.jessyan.autosize.AutoSizeConfig
import okhttp3.OkHttpClient
import timber.log.Timber
import java.io.BufferedReader
import java.io.FileReader
import java.io.IOException
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit


/**
 * Description TODO 公共的application
 * Author HJR36
 * Date 2018/4/13 10:31
 */
abstract class BaseApplication : MultiDexApplication(), IApp, CameraXConfig.Provider {

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(base)
        MultiDex.install(this)
    }

    lateinit var context: Context

    @SuppressLint("RestrictedApi")
    override fun onCreate() {
        super.onCreate()
        if (!this::context.isInitialized) {
            context = this
            joinuTechContext = applicationContext
        }
        DOWNLOAD_DIR = (externalCacheDir ?: cacheDir).absolutePath + "/ddbes/download"

        MMKV.initialize(joinuTechContext)
        resetEnv()
        // 获取当前包名
        val packageName = joinuTechContext.packageName
        // 获取当前进程名
        val processName = getProcessName(Process.myPid())
        if (processName != packageName) {
            LogUtil.showLog("不是主进程，不再执行初始化application")
            return
        }

        if (isDebug) {
            CustomActivityOnCrash.install(joinuTechContext)
        } else {

        }

        start = System.currentTimeMillis()

        LogUtil.debug = isDebug // 全局日志工具类配置
        FormatLogger.showLog = isDebug // 格式化日志log配置
        if (isDebug) {//修改log配置,设置log日志
            LogUtil.addDebugTags(
                    "base++",// log默认tag
                    "base_a_",// activity 相关log
                    "base_f_",// fragment 相关log
                    "IM__>>",//im相关log
                    "cache__>>",//缓存相关log
////                    "info->>",// 系统版本deviceModel log
//                    "wx___",
                    "file_up__",
                    "pushLib__",
//                    "ps__",
                    "rsp__",
//                    /*  "vc__", */
                    "msg__",// 消息相关log
                    "net__",// 网络请求打印log
//                    "request",
                    "requestTime__",// 请求时间统计日志
//                    "friend__", // 好友数据log
//                    "user__",// 用户相关数据log
                    "MkWebView",
                    "ct_log")
        }

        initAutoSize()
        initCache()// 用户数据加载
        init()
        //创建线程池
        singleThreadExecutor = Executors.newSingleThreadExecutor()
        initOkHttpUtils()
    }

    private fun initOkHttpUtils(){
        val sslParams = HttpsUtils.getSslSocketFactory(null, null, null)
        val mOkHttpClient = OkHttpClient.Builder()
          .sslSocketFactory(sslParams.sSLSocketFactory,sslParams.trustManager)
            .readTimeout(10, TimeUnit.SECONDS)//设置读取超时时间
            .writeTimeout(3, TimeUnit.SECONDS)//设置写的超时时间
            .connectTimeout(10, TimeUnit.SECONDS)//设置连接超时时间
            .addInterceptor(RequestHeaderInterceptor())//添加相关请求头，并判断token，其他人写的
            .eventListenerFactory(RequestEventListener.RequestEventFactory)
//            .addInterceptor(logging)
            .addInterceptor(LoggerInterceptor("担当办公-日志拦截", true))
            .build()

        OkHttpUtils.initClient(mOkHttpClient)
    }
    override fun getAppContext(): Context? {
        return this
    }

    override fun getPictureSelectorEngine(): PictureSelectorEngine? {
        return PictureSelectorEngineImp()
    }

    override fun getCameraXConfig(): CameraXConfig {
        return Camera2Config.defaultConfig()
    }

    //在首页有调用
    fun initConfig() {
        if (hasInitConfig) {
            return
        }

        val processName = getProcessName(Process.myPid())
        if (processName != packageName) {
            LogUtil.showLog("不是主进程，不再执行初始化application")
            return
        }

        initPush()

        // 初始化图片选择库中Application配置
        PictureAppMaster.getInstance().app = this

        hasInitConfig=true
    }

    fun configLog(tag: String) {
        LogUtil.addDebugTags(tag)
    }

    private fun initCache() {
        UserHolder.init()
    }

    //这个里边没有调用
    private fun initPush() {
        //旧push,位置1
      /*  PushCenter.init(this)
        PushCenter.listener = object : PushListener {
            override fun onNewToken(token: String) {
                LogUtil.showLog("${PushCenter.getSystemType()} 获取到token=$token")
                EventBusUtils.sendEvent(EventBusEvent(PushEvent.PUSH_TOKEN_UPDATE, token))
            }

            override fun onReceiveMsg(topic: String?, alias: String?, uriInfo: String?) {
                EventBusUtils.sendEvent(EventBusEvent(PushEvent.OFF_PUSH_RECEIVE, uriInfo))
            }
        }*/

    }

    val locationService: LocationService by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        Timber.i("locationService......start.....")
        LocationService(joinuTechContext)
    }


    /**初始化屏幕适配*/
    private fun initAutoSize() {
        AutoSizeConfig.getInstance().isCustomFragment = true
        //多进程
        AutoSize.initCompatMultiProcess(this)
    }

    override fun onTerminate() {
        super.onTerminate()
        ARouter.getInstance().destroy()
    }

     fun getProcessName(pid: Int): String? {
        var reader: BufferedReader? = null
        try {
            reader = BufferedReader(FileReader("/proc/$pid/cmdline"))
            var processName = reader.readLine()
            if (!TextUtils.isEmpty(processName)) {
                processName = processName.trim { it <= ' ' }
            }
            return processName
        } catch (throwable: Throwable) {
            throwable.printStackTrace()
        } finally {
            try {
                reader?.close()
            } catch (exception: IOException) {
                exception.printStackTrace()
            }

        }
        return null
    }

    /**
     * 各自可以实现sdk的初始化操作
     */
    abstract fun init()

    companion object {
        //线程池对象
        var singleThreadExecutor: ExecutorService? = null

        var preFileSdkInitIsOk=false

        /**当前会话列表中人员审批状态提示缓存*/
        val approveStatusMap = hashMapOf<String, Int>()

        fun getWorkTag(userId: String): Int {
            if (approveStatusMap.isNotEmpty()) {
                if (approveStatusMap.containsKey(userId)) {
                    val ivTag = approveStatusMap[userId]
                    if (ivTag != null && ivTag > 0) {
                        return getApproveTag(ivTag)
                    }
                }
            }
            return 0
        }

        /**
         * 审批状态
         * 0 正常
         * 1 请假
         * 2 出差
         * 3 外出
         */
        fun getApproveTag(tag: Int): Int {
            return when (tag) {
                1 -> R.drawable.ic_work_offline
                2 -> R.drawable.ic_work_out
                3 -> R.drawable.ic_work_goout
                else -> 0
            }
        }

        //审批申请详情的缓存
        val approCacheMap = hashMapOf<String, String>()

        //登录im长链接和im服务相关接口使用的token
        var imToken: String = ""

        //是否初始化bugly的标记
        var hasInitBugly=false
        var hasInitConfig=false

        //缓存当前人的权限
        var totalPermission:String=""

        /**
         * -1 未初始化，默认无网络状态
         * 0 无网络
         * 有网络
         * 1 mobile
         * 2 wifi
         * */
        var currentNetState: Int = -1
        var currentNetType: String = ""

        //断线重连时要重新拉取会话列表
        var reConnectedToGetOffLineSessionList = false

        //是否需要再次获取imToken
        var needGetImToken: Boolean = true


        //每次长链接登录成功时刷新的一个时间戳
        var longConnectLoginTime: Long = 0

        //标记权限是否发生变更
        var permissionHaveChanged: Boolean = false

        var tcpImServiceIsRunning = false

        var start = 0L

        // 获取到主线程的handler
        val mMainThreadHandler: Handler by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            Handler()
        }

//        // 获取到主线程
//        var mMainThread: Thread? = null
//
//        // 获取到主线程的id
//        var mMainThreadId: Int = 0
//
//        // 获取到主线程的looper
//        var mMainThreadLooper: Looper? = null

        // 获取到主线程的上下文
        @SuppressLint("StaticFieldLeak")
        lateinit var joinuTechContext: Context

//        // 对外暴露主线程的handler
//        fun getMainThreadHandler(): Handler? {
//            return mMainThreadHandler
//        }

//        // 对外暴露主线程
//        fun getMainThread(): Thread? {
//            return mMainThread
//        }
//
//        // 对外暴露主线程id
//        fun getMainThreadId(): Int {
//            return mMainThreadId
//        }

//        // 对外暴露主线程的looper
//        fun getMainThreadLooper(): Looper? {
//            return mMainThreadLooper
//        }

        /**
         * 获得当前进程的名字
         *
         * @param context
         * @return
         */
        fun getCurProcessName(context: Context): String? {

            val pid = android.os.Process.myPid()

            val activityManager = context
                    .getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager

            for (appProcess in activityManager
                    .runningAppProcesses) {

                if (appProcess.pid == pid) {

                    return appProcess.processName
                }
            }
            return null
        }

        private var currentActivity = ""
        fun setCurrentActivity(name: String) {
            currentActivity = name
        }

        fun getCurrentActivity(): String {
            return currentActivity
        }
    }





}