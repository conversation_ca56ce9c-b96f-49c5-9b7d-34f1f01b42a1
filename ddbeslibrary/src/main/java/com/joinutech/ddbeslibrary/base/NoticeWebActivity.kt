package com.joinutech.ddbeslibrary.base

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.os.Handler
import android.os.Message
import android.provider.MediaStore
import android.provider.Settings
import android.text.TextUtils
import android.util.Base64
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.webkit.JavascriptInterface
import android.widget.FrameLayout
import android.widget.ProgressBar
import android.widget.Toast
import androidx.core.content.FileProvider
import com.alibaba.android.arouter.facade.Postcard
import com.alibaba.android.arouter.facade.callback.NavCallback
import com.alibaba.android.arouter.launcher.ARouter
import com.joinutech.common.base.LinkBuilder
import com.joinutech.common.base.isWebDebug
import com.joinutech.common.helper.LoginHelper
import com.joinutech.common.util.UserHolder
import com.joinutech.component.webview.WebViewHelper
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.bean.PageInfoBean
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.databinding.ActivityWebNoticeWebviewBinding
import com.joinutech.ddbeslibrary.utils.COMPANIES
import com.joinutech.ddbeslibrary.utils.CommonUtils
import com.joinutech.ddbeslibrary.utils.ConsKeys
import com.joinutech.ddbeslibrary.utils.ConsValue
import com.joinutech.ddbeslibrary.utils.EventBusAction
import com.joinutech.ddbeslibrary.utils.EventBusEvent
import com.joinutech.ddbeslibrary.utils.EventBusUtils
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.IMAGE_CUT_CODE
import com.joinutech.ddbeslibrary.utils.Loggerr
import com.joinutech.ddbeslibrary.utils.MMKVUtil
import com.joinutech.ddbeslibrary.utils.ORG_PERMISS_TYPE
import com.joinutech.ddbeslibrary.utils.PermissionUtils
import com.joinutech.ddbeslibrary.utils.PictureNewHelper
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.joinutech.ddbeslibrary.utils.ToastUtil
import com.joinutech.ddbeslibrary.utils.home
import com.joinutech.ddbeslibrary.utils.login
import com.joinutech.ddbeslibrary.utils.toast
import com.joinutech.ddbeslibrary.utils.toastShort
import com.marktoo.lib.cachedweb.CommonWebConfig
import com.tencent.smtt.export.external.interfaces.JsPromptResult
import com.tencent.smtt.export.external.interfaces.JsResult
import com.tencent.smtt.sdk.ValueCallback
import com.tencent.smtt.sdk.WebView
import com.tencent.smtt.sdk.WebViewClient
import org.json.JSONObject
import java.io.ByteArrayOutputStream
import java.io.File
import java.lang.ref.WeakReference
import java.util.Date


/**
 * 通用webview页面
 * Created by zyy on 2019/4/16.
 */

class NoticeWebActivity : MyUseBindingActivity<ActivityWebNoticeWebviewBinding>() {


    private var fileChooseCallback: android.webkit.ValueCallback<Array<Uri>>? = null

    private fun afterFileChooseGoing(resultCode: Int, data: Intent?) {
        Loggerr.i("web文件选择", "==选择文件的=回调执行了===")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            if (fileChooseCallback == null) {
                return
            }
            val uris = com.tencent.smtt.sdk.WebChromeClient.FileChooserParams.parseResult(resultCode, data)
            fileChooseCallback?.onReceiveValue(uris)
        }
        fileChooseCallback = null
    }

    private var progressBar: ProgressBar? = null
    private var webview: WebView? = null
    private var isShowConfirmButton: Boolean = false
    private var handler = MyStaticHandler(WeakReference(this))

    override val contentViewResId: Int = R.layout.activity_web_notice_webview
    override fun inflateViewBinding(layoutInflater: LayoutInflater): ActivityWebNoticeWebviewBinding {
        return ActivityWebNoticeWebviewBinding.inflate(layoutInflater)
    }

    private var containerLayout : FrameLayout? = null

    var pageInfoBean: PageInfoBean? = null

    var targetUrl: String? = null

    private var timeValue = 3
    private var needApplyPermission = 1
    override fun initImmersion() {

        if (intent != null) {
            isShowConfirmButton = intent.getBooleanExtra("isShowConfirmButton", false)
            val pageInfo = intent.getStringExtra("pageInfo")
            needApplyPermission = intent.getIntExtra("enablePermission", 1)
            Log.i("moon" , "NoticeWebAct : pageInfo" + pageInfo)
            if (!TextUtils.isEmpty(pageInfo)) {
                pageInfoBean = GsonUtil.fromJson(pageInfo, PageInfoBean::class.java)
                if (pageInfoBean != null) {
                    setPageTitle(pageInfoBean!!.title!!)
                    holdTitles.add(pageInfoBean!!.title!!)
                    initUrl()
                }
            } else {
                targetUrl = intent.getStringExtra("targetUrl")
                val title = intent.getStringExtra("title")
                setPageTitle(title ?: "")
            }

        }
        showBackButton(R.drawable.back_grey, View.OnClickListener {
            onBackPressed()
        })
    }

    private fun initUrl() {
        when (pageInfoBean!!.pageType) {
            0 -> {
                /*创建*/
                if (!TextUtils.isEmpty(pageInfoBean!!.rightTitle)) {
                    setRightTitle(pageInfoBean!!.rightTitle!!, this)
                }
                targetUrl = LinkBuilder.generate(this).buildPublishUrl(pageInfoBean!!.companyId , userId ?:"",
                    token = accessToken)
            }
            1 -> {
                /*浏览*/
                val hasPower = hasPower()
                if (hasPower) {
                    setRightImage(R.drawable.icon_attendace_del, this)
                }
                try {

                    targetUrl = LinkBuilder.generate(this).buildNoticeDetailUrl(pageInfoBean!!.noticeId!!,
                        pageInfoBean!!.companyId, if (hasPower) "1" else "0" , userId ?:"" , token = accessToken)
                } catch (e: Exception) {
                    toastShort("公告参数缺失")
                }
            }
        }
    }

    private fun hasPower(): Boolean {
        val json = MMKVUtil.getString(COMPANIES, "")
        if (json.isNotEmpty()) {
            val companies = GsonUtil.fromJson(json, Array<WorkStationBean>::class.java)
            if (companies != null && companies.isNotEmpty()) {
                for (company in companies) {
                    if (company.companyId == pageInfoBean!!.companyId) {
                        if (company.deptId == "0" ||
                            ORG_PERMISS_TYPE.checkSuperPermission(company.power) ||
                            company.power.contains(ORG_PERMISS_TYPE.ORG_PERMISSION)) {
                            return true
                        }
                    }
                }
            }
        }
        return false
    }

    override fun initView() {
        whiteStatusBarBlackFont()
        initwebView()

        containerLayout = findViewById(R.id.notice_web_container)

        containerLayout?.addView(webview)
        //设置进度条的布局
        progressBar = findViewById(R.id.pb_web_load_progress)
    }

    val request_code_file_chooser_for_web=20003
    val request_code_file_capture_for_web=20005
    val request_code_permission=20004
    var imageUri:Uri?=null
    private var currentImageRequest = ""
    var objName: String = "ddbes_web"

    private fun initwebView() : WebView {
        webview = WebViewHelper.createWebView(this , webviewClient , webChromeClient)
        return webview!!
    }

    @SuppressLint("SetJavaScriptEnabled", "JavascriptInterface")
    override fun initLogic() {

        targetUrl?.let { url ->
            if (isShowConfirmButton) {
                binding.confirm.setOnClickListener(this)
                binding.cancel.setOnClickListener(this)
                binding.confirmLayout.visibility = View.VISIBLE

                webview?.setBackgroundColor(CommonUtils.getColor(mContext!!, R.color.transparent))
                loadUrl(url)
            } else {
                // 非必要页面 不申请权限
                if (needApplyPermission == 1){
                    val perms = arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    PermissionUtils.requestPermissionActivity(this, perms, "存储文件权限",
                        onSuccess = {
                            addJsScript()
                            loadUrl(url)
                        },
                        onError = {
                            webview?.setBackgroundColor(CommonUtils.getColor(mContext!!, R.color.transparent))
                            loadUrl(url)
                        })
                }else{
                    addJsScript()
                    loadUrl(url)
                }
            }
        }
    }


    private val webChromeClient = object: com.tencent.smtt.sdk.WebChromeClient(){
        override fun onProgressChanged(view: WebView?, newProgress: Int) {
            if (newProgress < 100) {
                progressBar!!.progress = newProgress
                if (progressBar!!.visibility == View.GONE) {
                    progressBar!!.visibility = View.VISIBLE //显示
                }
            } else {
                progressBar!!.visibility = View.GONE
                progressBar!!.progress = 0
            }
            super.onProgressChanged(view, newProgress)
        }

        override fun onReceivedTitle(view: WebView?, title: String?) {
            showLog("receive title $title")
            title?.let {
                if (it.contains("404") || it.contains("500")
                    || it.contains("error", true)
                    || it.contains("网页无法打开")
                    || it.contains("failed", true)) {
                    canGoBack = -1
                }
            }
        }

        override fun onJsAlert(view: WebView?, url: String?, message: String?, result: JsResult?): Boolean {
            //默认禁止
//                return true
            return super.onJsAlert(view,url,message,result)
        }

        override fun onJsPrompt(
            p0: WebView?,
            p1: String?,
            p2: String?,
            p3: String?,
            p4: JsPromptResult?
        ): Boolean {
            return super.onJsPrompt(p0, p1, p2, p3, p4)
        }

        override fun onJsConfirm(
            p0: WebView?,
            p1: String?,
            p2: String?,
            p3: JsResult?
        ): Boolean {
            return super.onJsConfirm(p0, p1, p2, p3)

        }

        override fun onShowFileChooser(
            webView: WebView?,
            filePathCallback: ValueCallback<Array<Uri>>?,
            fileChooserParams: FileChooserParams?
        ): Boolean {
            //以下是webview调用系统相机的步骤-----------------------------------------------------------
            val mode=fileChooserParams?.mode?:""
            Loggerr.i("调用模式", "===传递的mode=${mode}===")
            Loggerr.i("调用模式", "===预定义的mode=${FileChooserParams.MODE_OPEN}===")
            Loggerr.i("调用模式", "===预定义的mode=${FileChooserParams.MODE_OPEN_FOLDER}===")
            Loggerr.i("调用模式", "===预定义的mode=${FileChooserParams.MODE_SAVE}===")
            Loggerr.i("调用模式", "===预定义的mode=${FileChooserParams.MODE_OPEN_MULTIPLE}===")
            if (mode == FileChooserParams.MODE_OPEN_MULTIPLE) {
                //此时是调用拍照
                val perms = arrayOf(
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.CAMERA,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE)
                val tips = "选取图片需要您授权读写"
                PermissionUtils.requestPermissionActivity(this@NoticeWebActivity, perms,
                    hint = "照相机权限",
                    onSuccess = {
                        fileChooseCallback = filePathCallback
                        val fileFolder=CommonUtils.getFileCachePath(this@NoticeWebActivity,"webPhoto")
                        val format=java.text.SimpleDateFormat("yyyy-MM-DD").format(Date())
                        val fileName="JDEG_"+format+"_"
                        val imageFile=File.createTempFile(fileName,".jpg",File(fileFolder))
//                            val imageFile=File(fileFolder+File.separator+fileName+System.currentTimeMillis()+".jpg")//也可以
                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                            //转化为uri
                            imageUri=FileProvider.getUriForFile(this@NoticeWebActivity,
                                packageName+".fileProvider",imageFile)

                        }else{
                            imageUri=Uri.fromFile(imageFile)
                        }
                        Loggerr.i("web文件选择", "=拍摄之前==imageUri=${imageUri}===")
                        val captureIntent=Intent(MediaStore.ACTION_IMAGE_CAPTURE)
                        //下面这句代码表示，提前指定拍摄后的文件名称，这个文件就算是最终的返回结果；
                        // 注释掉的话，就是代表不指定文件名称，那拍摄之后将在OnActivityForResult中返回bitmap结果，且是缩略图；
                        captureIntent.putExtra(MediaStore.EXTRA_OUTPUT, imageUri)

                        captureIntent.putExtra(MediaStore.Images.Media.ORIENTATION,0)

                        val photo=Intent(Intent.ACTION_PICK,MediaStore.Images.Media.EXTERNAL_CONTENT_URI)

                        /*val chooserIntent=Intent.createChooser(photo,"选择上传方式")
                        chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, arrayOf(captureIntent));*/
                        val chooserIntent=Intent(Intent.ACTION_CHOOSER)
                        chooserIntent.putExtra(Intent.EXTRA_INTENT,photo)
                        chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, arrayOf(captureIntent))

                        startActivityForResult(chooserIntent, request_code_file_capture_for_web);
                    },
                    onError = {
                        Toast.makeText(this@NoticeWebActivity, tips, Toast.LENGTH_SHORT).show()
                    })

                return true
            }


            //以下是webview调用系统文件选择器的步骤========================================================
            Loggerr.i("web文件选择", "===filePathCallback=${filePathCallback}===")
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                //Android版本大于等于11时,申请全部文件的权限，文件选择，选择文件，，，调用系统的文件选择器===========
                if (!Environment.isExternalStorageManager()) {
                    val intent=Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                    intent.setData(Uri.parse("package:" + getPackageName()));
                    startActivityForResult(intent, request_code_permission);
                    return false
                }
            }
            fileChooseCallback = filePathCallback
//                val intent=fileChooserParams?.createIntent()
            val intent=Intent(Intent.ACTION_GET_CONTENT)
            intent.addCategory(Intent.CATEGORY_OPENABLE)
            intent.setType("*/*")
            try {
//                    startActivityForResult(intent,request_code_file_chooser_for_web)
                startActivityForResult(Intent.createChooser(intent,"File Chooser"),request_code_file_chooser_for_web)
            }catch (e:Exception){
                fileChooseCallback=null
                ToastUtil.show(this@NoticeWebActivity,"打开文件选择器失败")
                return false
            }

            return true
        }

    }

    private val webviewClient = object: WebViewClient(){
        override fun onPageStarted(p0: WebView?, p1: String?, p2: Bitmap?) {
            super.onPageStarted(p0, p1, p2)
        }

        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            showLog("load page finished,$url")
            if (canGoBack == 0) {
                canGoBack = 1
            }

            if (isShowConfirmButton && !LoginHelper.isReadPrivacy()) {
                handler.sendEmptyMessage(0)
            }
        }

        override fun onReceivedError(view: WebView?, errorCode: Int, description: String?, url: String?) {
            showLog("receive Http Error: $errorCode : $url")
            canGoBack = -1
        }

        override fun onReceivedSslError(
            view: WebView?,
            handler: com.tencent.smtt.export.external.interfaces.SslErrorHandler?,
            error: com.tencent.smtt.export.external.interfaces.SslError?
        ) {
            handler?.proceed()
        }
    }

    //核心代码

    @SuppressLint("SetTextI18n")
    fun webViewScrollChangeListener() {
        if (timeValue == 0) {
            binding.confirm.text = "我已阅读并同意本条款"
            binding.confirm.isClickable = true
            binding.confirm.setBackgroundResource(R.drawable.shape_2_main_blue)
        } else {
            binding.confirm.text = "我已阅读并同意本条款(${timeValue}s)"
            binding.confirm.isClickable = false
            binding.confirm.setBackgroundResource(R.drawable.shape_2_d5dfed)
            val msg = Message()
            msg.what = 0
            handler.sendMessageDelayed(msg, 1000)
        }
        timeValue--
    }

    private var canGoBack = 0

    @SuppressLint("SetJavaScriptEnabled")
    private fun addJsScript() {
        //对象映射
        webview?.addJavascriptInterface(WebMutualInterface(jsCallBackListener),
            ConsKeys.JS_TRANS_PORT)
        webview?.setOnLongClickListener { false }// 禁用长按
        clearWebCache()
    }

    private fun clearWebCache() {
        showLog("清理webview缓存内容")
        try {
            val cacheDir = externalCacheDir ?: cacheDir
            webview?.settings?.setAppCachePath(cacheDir.absolutePath)
            webview?.settings?.setAppCacheEnabled(true)
            cacheDir?.delete()
        } catch (e: Exception) {
        }
//        requireActivity().externalCacheDir?.delete()
        try {
            //缓存模式
            webview?.settings?.cacheMode = com.tencent.smtt.sdk.WebSettings.LOAD_NO_CACHE
        } catch (e: Exception) {
        }
        try {
            webview?.clearCache(true)
        } catch (e: Exception) {
        }
        try {
            webview?.clearHistory()
        } catch (e: Exception) {
        }
        try {
            webview?.clearFormData()
        } catch (e: Exception) {
        }
    }

    /**
     * 静态内部类handler
     */
    class MyStaticHandler(ref: WeakReference<NoticeWebActivity>) : Handler() {

        private var activity: NoticeWebActivity? = ref.get()

        override fun handleMessage(msg: Message) {
            if (activity != null) {
                when (msg.what) {
                    0 -> {
                        activity?.webViewScrollChangeListener()
                    }
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == IMAGE_CUT_CODE) {
                if (data != null) {
//                    val selectList = PictureSelector.obtainMultipleResult(data)
                    if (fileChooseCallback != null) {
                        val selectList = PictureNewHelper.afterSelectPhotoUris(this@NoticeWebActivity, data)
                        showLog("返回选择文件信息")
                        fileChooseCallback?.onReceiveValue(selectList.toTypedArray())// web 直选文件回调
                    } else {
                        val selectList = PictureNewHelper.afterSelectPhotoPaths(data)
                        if (selectList.isNotEmpty()) {
                            val temp = arrayListOf<String>()
                            for (picPath in selectList) {
                                val bm: Bitmap = BitmapFactory.decodeFile(picPath)
                                val baos = ByteArrayOutputStream()
                                //1.5M的压缩后在100Kb以内，测试得值,压缩后的大小=94486字节,压缩后的大小=74473字节
                                //这里的JPEG 如果换成PNG，那么压缩的就有600kB这样
                                //图片处理，图片转成base64，其他人写的
                                bm.compress(Bitmap.CompressFormat.JPEG, 40, baos)
                                temp.add(Base64.encodeToString(baos.toByteArray(), Base64.DEFAULT))
                            }
                            transPicToWeb(temp)
                        }
                    }
                    fileChooseCallback = null // 不释放不能下次选取图片
                    showLog("返回选择文件信息 之后")
                } else {
                    fileChooseCallback?.onReceiveValue(arrayOf())
                    fileChooseCallback = null // 不释放不能下次选取图片
                }
            } else if (requestCode == request_code_file_chooser_for_web) {
                Loggerr.i("web文件选择", "=RESULT_OK==回调给web执行===")
                afterFileChooseGoing(resultCode,data)
            }else if (requestCode==request_code_file_capture_for_web) {
                Loggerr.i("web文件选择", "=RESULT_OK==回调给web执行===")
                afterFileTakeGoing(resultCode,data)
            } else if (requestCode == 10004) {
                invokeJs("refreshApproval")
            }
        } else if(resultCode== Activity.RESULT_CANCELED){
            Loggerr.i("web文件选择", "===取消文件选择器=requestCode=${requestCode}==")
            if (requestCode == request_code_file_chooser_for_web) {
                afterFileChooseGoing(resultCode, data)
            }else if(requestCode == request_code_file_capture_for_web){
                afterFileTakeGoing(resultCode,data)
            }
        }else{
            Loggerr.i("web文件选择", "===其他回调===")
            if (fileChooseCallback != null) {
                fileChooseCallback?.onReceiveValue(arrayOf())
            }
            fileChooseCallback = null // 不释放不能下次选取图片
            imageUri=null
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

    private fun updatePhotos() {
        // 该广播即使多发（即选取照片成功时也发送）也没有关系，只是唤醒系统刷新媒体文件
        val intent = Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
        intent.setData(imageUri);
        sendBroadcast(intent);
    }

    private fun afterFileTakeGoing(resultCode: Int, data: Intent?) {
        Loggerr.i("web文件选择", "==拍照=回调执行了===")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            updatePhotos()
            if (fileChooseCallback == null) {
                return
            }
            if (data != null) {
                Loggerr.i("web文件选择", "===调用系统相机===")
                val uri:Uri?=data.data
                if (uri==null) {
                    //拍摄
                    val resultBitmap=data.extras?.get("data") as Bitmap?
                    Loggerr.i("web文件选择", "===调用系统相机=拍摄====不指定文件名称且是缩略图=====")
                    val resultFile= CommonUtils.saveBitmap(this@NoticeWebActivity,
                        resultBitmap,"${System.currentTimeMillis()}.jpg","webPhoto")
                    if (resultFile!=null) {
                        val resultUri=  if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                            FileProvider.getUriForFile(this@NoticeWebActivity,packageName+".fileProvider",resultFile)
                        }else{
                            Uri.fromFile(resultFile)
                        }
                        if (resultUri != null) {
                            val result= arrayOf(resultUri)
                            fileChooseCallback?.onReceiveValue(result)
                        }else{
                            fileChooseCallback?.onReceiveValue(null)
                        }
                    }else{
                        fileChooseCallback?.onReceiveValue(null)
                    }
                }else{
                    //选择
                    Loggerr.i("web文件选择", "===调用系统相机==选择======")
                    if (uri != null) {
                        val result= arrayOf(uri)
                        fileChooseCallback?.onReceiveValue(result)
                    }else{
                        fileChooseCallback?.onReceiveValue(null)
                    }
                }
            }else{
                //指定拍照的文件名时才会用到
                Loggerr.i("web文件选择", "===调用系统相机==拍摄==指定文件名称===是原图===")
                imageUri?.let {
                    val resultBitmap=BitmapFactory.decodeStream(contentResolver.openInputStream(it))
                    val resultFile= CommonUtils.saveBitmap(this@NoticeWebActivity,
                        resultBitmap,"${System.currentTimeMillis()}.jpg","webPhoto")
                    if (resultFile!=null) {
                        val resultUri=  if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                            FileProvider.getUriForFile(this@NoticeWebActivity,packageName+".fileProvider",resultFile)
                        }else{
                            Uri.fromFile(resultFile)
                        }
                        if (resultUri != null) {
                            val result= arrayOf(resultUri)
                            fileChooseCallback?.onReceiveValue(result)
                        }else{
                            fileChooseCallback?.onReceiveValue(null)
                        }
                    }else{
                        fileChooseCallback?.onReceiveValue(null)
                    }
                }
            }

        }
        fileChooseCallback = null
        imageUri=null
    }

    private fun transPicToWeb(temp: List<String>) {
        val json = JSONObject(currentImageRequest)
        json.put("images", GsonUtil.toJson(temp))

        invokeJs("getBase64String", json.toString())
        currentImageRequest = ""
    }

    //交互调用js
    private fun invokeJs(method: String, data: String? = null) {
        if (isWebDebug) {
            if (objName.isBlank()) {
                toast(this , "client_isAlready 未调用")
                return
            }
        }
        Loggerr.i("invokeJs", "===执行了=objName=${objName}===")
        val jsCode = if (data.isNullOrBlank()) {
            "javascript:window.${method}()"
        } else {
            "javascript:window.${method}($data)"
        }
        showLog("调用js：$jsCode")
        webview?.loadUrl(jsCode)
    }

    override fun onDestroy() {
        handler.removeCallbacksAndMessages(null)
        super.onDestroy()
    }

    @SuppressLint("JavascriptInterface")
    class WebMutualInterface constructor(private val noticeCallbackListener: OnNoticeCallbackListener?) {

        @JavascriptInterface
        fun dd_public_success() {
            noticeCallbackListener?.onNoticePublicSuccess()
        }

        @JavascriptInterface
        fun dd_delete_notice_success() {
            noticeCallbackListener?.onNoticeDeleteSuccess()
        }

        @JavascriptInterface
        fun dd_getNavigationTitle(subTitle: String) {
            noticeCallbackListener?.onTitleChanged(true, subTitle)
        }

        @JavascriptInterface
        fun dd_theAnnoncementDidDelete(toDoText: String) {
            noticeCallbackListener?.onNoticeDeleteClose(true, toDoText)
        }
    }

    private fun loadUrl(realUrl: String) {
        webview?.loadUrl(realUrl, ConsValue.getWebHeaders())
    }

    override fun onNoDoubleClick(v: View) {
        when (v) {
            iv_rightTitle -> {
                deleteNotice()
            }
            tv_rightTitle -> {
                publishNotice()
            }
            //点击拒绝
            binding.cancel -> {
                finish()
            }
            //点击同意
            binding.confirm -> {
                LoginHelper.onPrivacyRead()
                if (StringUtils.isNotBlankAndEmpty(UserHolder.getUserId())) {
                    ARouter.getInstance().build(home).navigation(BaseApplication.joinuTechContext,
                        object : NavCallback() {
                            override fun onArrival(postcard: Postcard?) {
                                finish()
                            }
                        })
                } else {
                    //启动登录页面
                    ARouter.getInstance().build(login).navigation(BaseApplication.joinuTechContext,
                        object : NavCallback() {
                            override fun onArrival(postcard: Postcard?) {
                                finish()
                            }
                        })
                }
            }
        }
    }

    private fun publishNotice() {
        invokeJs("dd_public")
    }

    private fun deleteNotice() {
        invokeJs("dd_delete_notice")
    }

    private val holdTitles: ArrayList<String> = arrayListOf()

    override fun showToolBar(): Boolean {
        return true
    }

    override fun openEventBus(): Boolean {
        // 区分不同链接，判断是否是用户登录后使用页面，再区分要不要接收登录事件
        return !userId.isNullOrBlank()
    }

    override fun openArouterReceive(): Boolean {
        return false
    }

    override fun onBackPressed() {
        if (canGoBack == 1 && webview != null && webview!!.canGoBack() && holdTitles.size > 1) {
            webview!!.goBack()
            jsCallBackListener.onTitleChanged(false)
        } else {
            super.onBackPressed()
        }
    }

    interface OnNoticeCallbackListener {
        fun onTitleChanged(isForward: Boolean, title: String? = null)
        fun onNoticePublicSuccess()
        fun onNoticeDeleteSuccess()
        fun onNoticeDeleteClose(b: Boolean, toDoText: String?)
    }

    private val jsCallBackListener = object : OnNoticeCallbackListener {
        override fun onNoticeDeleteClose(b: Boolean, toDoText: String?) {
            runOnUiThread {
                if (mContext != null && toDoText != null){
                    ToastUtil.show(mContext!!, toDoText)
                }
                BaseApplication.mMainThreadHandler.postDelayed({ finish() }, 1000)
            }
        }

        override fun onNoticePublicSuccess() {
            runOnUiThread {
                EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_MAIN_NOTICE))
                if (holdTitles.last() == resources.getString(R.string.notice_create_title)) {
                    ToastUtil.show(mContext!!, mContext?.getString(R.string.notice_create_success) ?:"")
                }
                finish()
            }
        }

        override fun onNoticeDeleteSuccess() {
            runOnUiThread {
                EventBusUtils.sendEvent(EventBusEvent(EventBusAction.REFRESH_MAIN_NOTICE))
                finish()
            }
        }

        override fun onTitleChanged(isForward: Boolean, title: String?) {
            runOnUiThread {
                if (isForward) {
                    holdTitles.add(title!!)
                    if (!TextUtils.isEmpty(title)) {
                        setPageTitle(title)
                        if (resources.getString(R.string.notice_edit_title).equals(title)) {
                            showLog("隐藏右侧删除按钮")
                            hideRightImage()
                        }
                    }
                } else {
                    holdTitles.removeAt(holdTitles.size - 1)
                    setPageTitle(holdTitles.last())
                }
            }
        }
    }

}

