package com.joinutech.ddbeslibrary.base

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.ColorRes
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.utils.*
import com.marktoo.lib.cachedweb.LogUtil
import com.trello.rxlifecycle3.components.support.RxFragment
import org.greenrobot.eventbus.EventBus


/**
 * description: Fragmetn基类
 * autour: lisong
 * date: 2017/8/14 0014 下午 2:28
 * version:
 */
abstract class BaseFragment : RxFragment(), OnNoDoubleClickListener {

    protected var rootView: View? = null

    /**
     * 获取布局文件
     */
    protected abstract val layoutRes: Int

    var userId: String? = null
    var token: String? = null
    lateinit var mActivity: MyUseBindingActivity<*>
    lateinit var mFragment: Fragment

    //双重判定，保证懒加载
//    private var isVisible =false //这个，标记，当前Fragment是否可见= false
    private var isPrepared = false //这个，标记当前Fragment是否已经执行了onCreateView

    //只有两个标记同时满足，才进行数据加载

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        userId = UserHolder.getUserId()
        token = UserHolder.getAccessToken()
        if (openEventBus()) {
            EventBus.getDefault().register(this)
        }
        if (rootView == null) {
            rootView = inflater.inflate(layoutRes, container, false)
        }
        // 状态栏
        initImmersion()
        // 初始化布局
        initView(rootView!!)
        // 初始化逻辑
        initLogic()
        return rootView
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        initActivityCreated(savedInstanceState)
    }

   open fun initActivityCreated(savedInstanceState: Bundle?){}

    protected var immersionBar: ImmersionBar? = null

    fun initImmersion() {
        if (immersionBar == null) {
            immersionBar = ImmersionBar.with(this)
        }
    }

    fun setStatusBarView(titleBar: View) {
        immersionBar?.titleBar(titleBar)?.init()
    }

    fun updateStatusBar(isDarkFont: Boolean) {
        immersionBar?.statusBarDarkFont(isDarkFont, 0.2f)?.init()
    }

    fun updateStatusBarAndColor(isDarkFont: Boolean, @ColorRes statusColor: Int) {
        immersionBar?.statusBarDarkFont(isDarkFont)?.statusBarColor(statusColor, 1.0f)?.init()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        mActivity = context as MyUseBindingActivity<*>
        mFragment = this
    }

    /**
     * 是否开启eventBus
     */
    open fun openEventBus(): Boolean {
        return true
    }

    /**
     * 初始化view
     */
    protected abstract fun initView(rootView: View)

    /**
     * 初始化逻辑
     */
    open fun initLogic() {

    }

    override fun onDestroyView() {
        if (openEventBus()) {
            EventBus.getDefault().unregister(this)
        }
        super.onDestroyView()
    }

    override fun onNoDoubleClick(v: View) {}

    fun showLog(msg: String, tag: String = "base_f_") {
        LogUtil.showLog(msg, tag)
    }

    fun showLog(data: Any, tag: String = "base_f_") {
        LogUtil.showLog(GsonUtil.toJson(data), tag)
    }

    fun showToast(msg: String) {
        activity?.let { ToastUtil.show(it, msg) }
    }

    fun <T : ViewModel> getModel(modelClass: Class<T>): T {
        return ViewModelProvider(this).get(modelClass)
    }

    fun showLoading(info: String = "", outSizeCancel: Boolean = false) {
//        EventBusUtils.sendEvent(EventBusEvent("loading_show", LoadSetting(info, outSizeCancel)))
        if (this::mActivity.isInitialized) {
            mActivity.getLoadingDialog(info, outSizeCancel)
        }
    }

    fun hideLoading() {
//        EventBusUtils.sendEvent(EventBusEvent("loading_hide"))
        if (this::mActivity.isInitialized) {
            mActivity.dismissDialog()
        }
    }
}
