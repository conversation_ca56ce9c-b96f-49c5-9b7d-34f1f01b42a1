package com.joinutech.ddbeslibrary.example

import android.app.Activity
import android.os.Bundle
import com.joinutech.ddbeslibrary.utils.BrightnessHelper
import com.joinutech.ddbeslibrary.utils.ScreenUtils
import com.joinutech.ddbeslibrary.utils.ScreenUtilsKt
import timber.log.Timber

/**
 * 亮度控制使用示例
 * 展示如何在Activity中使用亮度控制方法
 * 
 * 注意：这只是一个示例文件，展示如何使用亮度控制功能
 * 在实际项目中，请根据需要在相应的Activity中添加这些功能
 */
class BrightnessUsageExample {

    /**
     * 在Activity中使用亮度控制的示例
     */
    fun demonstrateInActivity(activity: Activity) {
        
        // ========== 基础用法 ==========
        
        // 1. 获取当前应用亮度
        val currentBrightness = ScreenUtilsKt.getAppBrightness(activity)
        Timber.d("当前应用亮度: $currentBrightness")
        
        // 2. 设置应用亮度为50%
        ScreenUtilsKt.setAppBrightness(activity, 0.5f)
        
        // 3. 使用Java版本的方法
        val brightness = ScreenUtils.getAppBrightness(activity)
        ScreenUtils.setAppBrightness(activity, 0.8f)
        
        
        // ========== 使用BrightnessHelper辅助类 ==========
        
        // 1. 获取系统亮度信息
        val systemBrightness = BrightnessHelper.getSystemBrightness(activity)
        val systemBrightnessFloat = BrightnessHelper.getSystemBrightnessFloat(activity)
        val isAutoBrightness = BrightnessHelper.isAutoBrightnessEnabled(activity)
        
        Timber.d("系统亮度: $systemBrightness (0-255)")
        Timber.d("系统亮度: $systemBrightnessFloat (0.0-1.0)")
        Timber.d("是否开启自动亮度: $isAutoBrightness")
        
        // 2. 设置不同的亮度级别
        BrightnessHelper.setMaxBrightness(activity)      // 最大亮度
        BrightnessHelper.setMinBrightness(activity)      // 最小亮度
        BrightnessHelper.setBrightnessByPercentage(activity, 75) // 75%亮度
        
        // 3. 获取当前亮度百分比
        val percentage = BrightnessHelper.getAppBrightnessPercentage(activity)
        Timber.d("当前亮度百分比: $percentage%")
        
        // 4. 恢复系统亮度
        BrightnessHelper.restoreSystemBrightness(activity)
        
        // 5. 平滑调整亮度（带动画效果）
        BrightnessHelper.smoothSetBrightness(
            activity = activity,
            targetBrightness = 0.3f,
            duration = 1000
        ) {
            Timber.d("亮度调整完成")
        }
    }
    
    /**
     * 在特定场景中使用亮度控制的示例
     */
    fun scenarioExamples(activity: Activity) {
        
        // ========== 场景1: 夜间模式 ==========
        fun enableNightMode() {
            // 设置较低的亮度，适合夜间使用
            BrightnessHelper.setBrightnessByPercentage(activity, 20)
            Timber.d("启用夜间模式")
        }
        
        // ========== 场景2: 阅读模式 ==========
        fun enableReadingMode() {
            // 设置适中的亮度，适合长时间阅读
            BrightnessHelper.setBrightnessByPercentage(activity, 60)
            Timber.d("启用阅读模式")
        }
        
        // ========== 场景3: 户外模式 ==========
        fun enableOutdoorMode() {
            // 设置最大亮度，适合户外强光环境
            BrightnessHelper.setMaxBrightness(activity)
            Timber.d("启用户外模式")
        }
        
        // ========== 场景4: 省电模式 ==========
        fun enablePowerSavingMode() {
            // 设置较低的亮度以节省电量
            BrightnessHelper.setBrightnessByPercentage(activity, 30)
            Timber.d("启用省电模式")
        }
        
        // ========== 场景5: 视频播放模式 ==========
        fun enableVideoMode() {
            // 根据环境光线自动调整亮度
            if (BrightnessHelper.isAutoBrightnessEnabled(activity)) {
                // 如果开启了自动亮度，使用系统亮度
                BrightnessHelper.restoreSystemBrightness(activity)
            } else {
                // 否则设置适中的亮度
                BrightnessHelper.setBrightnessByPercentage(activity, 70)
            }
            Timber.d("启用视频播放模式")
        }
    }
    
    /**
     * Activity生命周期中的亮度管理示例
     */
    fun lifecycleManagement(activity: Activity) {
        
        // 在onCreate中保存原始亮度
        fun onCreate(savedInstanceState: Bundle?) {
            val originalBrightness = ScreenUtilsKt.getAppBrightness(activity)
            // 保存原始亮度值，以便在onDestroy中恢复
            // 可以使用SharedPreferences或其他方式保存
        }
        
        // 在onResume中应用特定亮度
        fun onResume() {
            // 应用特定的亮度设置
            BrightnessHelper.setBrightnessByPercentage(activity, 80)
        }
        
        // 在onPause中可能需要调整亮度
        fun onPause() {
            // 根据需要调整亮度或保持当前设置
        }
        
        // 在onDestroy中恢复原始亮度
        fun onDestroy() {
            // 恢复系统亮度，避免影响其他应用
            BrightnessHelper.restoreSystemBrightness(activity)
        }
    }
    
    /**
     * 错误处理和边界情况示例
     */
    fun errorHandlingExamples(activity: Activity) {
        
        // 安全地设置亮度值
        fun safeSetBrightness(brightness: Float) {
            try {
                // 确保亮度值在有效范围内
                val safeBrightness = brightness.coerceIn(0f, 1f)
                ScreenUtilsKt.setAppBrightness(activity, safeBrightness)
                Timber.d("成功设置亮度: $safeBrightness")
            } catch (e: Exception) {
                Timber.e(e, "设置亮度失败")
                // 回退到默认亮度
                BrightnessHelper.restoreSystemBrightness(activity)
            }
        }
        
        // 检查亮度权限（如果需要修改系统亮度）
        fun checkBrightnessPermission(): Boolean {
            // 注意：修改应用亮度不需要特殊权限
            // 但如果要修改系统亮度，需要WRITE_SETTINGS权限
            return true
        }
    }
}

/**
 * 在实际Activity中使用的示例代码片段
 * 
 * class YourActivity : AppCompatActivity() {
 *     
 *     private var originalBrightness: Float = -1f
 *     
 *     override fun onCreate(savedInstanceState: Bundle?) {
 *         super.onCreate(savedInstanceState)
 *         setContentView(R.layout.activity_your)
 *         
 *         // 保存原始亮度
 *         originalBrightness = ScreenUtilsKt.getAppBrightness(this)
 *         
 *         // 设置特定亮度
 *         BrightnessHelper.setBrightnessByPercentage(this, 70)
 *     }
 *     
 *     override fun onDestroy() {
 *         super.onDestroy()
 *         // 恢复原始亮度
 *         if (originalBrightness >= 0) {
 *             ScreenUtilsKt.setAppBrightness(this, originalBrightness)
 *         } else {
 *             BrightnessHelper.restoreSystemBrightness(this)
 *         }
 *     }
 *     
 *     // 按钮点击事件示例
 *     private fun onBrightnessButtonClick() {
 *         when (selectedMode) {
 *             "night" -> BrightnessHelper.setBrightnessByPercentage(this, 20)
 *             "reading" -> BrightnessHelper.setBrightnessByPercentage(this, 60)
 *             "outdoor" -> BrightnessHelper.setMaxBrightness(this)
 *             "auto" -> BrightnessHelper.restoreSystemBrightness(this)
 *         }
 *     }
 * }
 */
