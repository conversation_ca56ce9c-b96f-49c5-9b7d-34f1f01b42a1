package com.joinutech.ddbeslibrary.down

import okhttp3.OkHttpClient
import retrofit2.Retrofit
import java.util.concurrent.TimeUnit

object RetrofitClient {
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .build()

    val instance: FileDownloadService by lazy {
        Retrofit.Builder()
            .baseUrl("https://ddbes.com") // 基础URL，实际下载使用完整URL
            .client(okHttpClient)
            .build()
            .create(FileDownloadService::class.java)
    }
}