package com.joinutech.ddbeslibrary.down

import android.content.Context
import com.joinutech.ddbeslibrary.utils.getApkDownloadDir
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.ResponseBody
import timber.log.Timber
import java.io.File
import java.io.InputStream

class DownloadManager(private val context: Context) {

    sealed class DownloadResult {
        data class Progress(val percentage: Int) : DownloadResult()
        data class Success(val file: File) : DownloadResult()
        data class Error(val exception: Throwable) : DownloadResult()
    }

    suspend fun downloadFile(
        context: Context,
        fileUrl: String,
        fileName: String,
        onProgress: (Int) -> Unit
    ): DownloadResult {
        return try {
            val responseBody = RetrofitClient.instance.downloadFile(fileUrl)
            saveFile(context ,responseBody, fileName, onProgress)
        } catch (e: Exception) {
            DownloadResult.Error(e)
        }
    }

    private suspend fun saveFile(
        context: Context,
        body: ResponseBody,
        fileName: String,
        onProgress: (Int) -> Unit
    ): DownloadResult = withContext(Dispatchers.IO) {
        var inputStream: InputStream? = null
        var outputStream: java.io.OutputStream? = null

        try {
            val dir = getApkDownloadDir(context)
            val file = File(dir +"/ddbes.apk")
            if(!file.parentFile.exists()) file.parentFile.mkdirs()
            inputStream = body.byteStream()
            outputStream = file.outputStream()

            val totalBytes = body.contentLength()
            val buffer = ByteArray(8 * 1024)
            var bytesDownloaded = 0L

            while (true) {
                val read = inputStream.read(buffer)
                if (read == -1) break

                outputStream.write(buffer, 0, read)
                bytesDownloaded += read

                // 更新进度（主线程）
                withContext(Dispatchers.Main) {
                    val progress = (bytesDownloaded * 100 / totalBytes).toInt()
                    onProgress(progress)
                }
            }

            outputStream.flush()
            DownloadResult.Success(file)
        } catch (e: Exception) {
            Timber.i(">>>> $e")
            DownloadResult.Error(e)
        } finally {
            inputStream?.close()
            outputStream?.close()
        }
    }
}