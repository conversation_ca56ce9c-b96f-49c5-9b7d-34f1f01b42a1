package com.joinutech.ddbeslibrary.utils

import android.Manifest
import android.animation.Animator
import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application.getProcessName
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.os.Process
import android.provider.MediaStore
import android.text.InputType
import android.text.TextUtils
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.util.Base64
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatSeekBar
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.children
import androidx.viewbinding.ViewBinding
import cat.ereza.customactivityoncrash.CustomActivityOnCrash
//import com.baidu.mapapi.SDKInitializer
import com.bumptech.glide.Glide
import com.bumptech.glide.Priority
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.joinutech.common.base.isDebug
import com.joinutech.common.storage.FileStorage
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.base.MyUseBindingActivity
import com.joinutech.ddbeslibrary.base.TailerBean
import com.joinutech.ddbeslibrary.bean.VerifyImageBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.LoginService
import com.joinutech.ddbeslibrary.utils.image.ImageLoaderUtils
import com.marktoo.lib.cachedweb.LogUtil
import com.tencent.bugly.crashreport.CrashReport
import com.trello.rxlifecycle3.LifecycleTransformer
import timber.log.Timber
import java.io.*
import java.math.BigDecimal
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*
import java.util.regex.Pattern
import kotlin.collections.HashMap


/**
 * @className: xUtil
 * @desc: 工具类集合
 * @author: zyy
 * @date: 2019/8/6 13:17
 * @company: joinUTech
 * @leader: ke
 */
object XUtil {

    fun getAprIcon(tag: String): String {
        val url = if (tag.isNotBlank()) {
//            "https://ddbes-repository-1257239906.cos.ap-beijing.myqcloud.com/icon/$tag-chk.png"
            FileStorage.TOS_DEFAULT_URL_PRE + TosFileType.APR_ICON.path + "$tag-chk.png"
        } else {
//            "https://ddbes-repository-1257239906.cos.ap-beijing.myqcloud.com/icon/11-chk.png"
            FileStorage.TOS_DEFAULT_URL_PRE + TosFileType.APR_ICON.path + "11-chk.png"
        }
        LogUtil.showLog("审批icon url is $url")
        return url
    }

    fun getTime(date: Date, pattern: String = DEFAULT_TIME_FORMAT_PATTERN): String {//可根据需要自行截取数据显示
        Log.d("getTime()", "choice date millis: " + date.time)
        @SuppressLint("SimpleDateFormat") val format = SimpleDateFormat(pattern)
        return format.format(date)
    }

    fun getTimeValue(
        dateInfo: String,
        pattern: String = DEFAULT_TIME_FORMAT_PATTERN
    ): String {//可根据需要自行截取数据显示
        Log.d("getTime()", "choice date millis: $dateInfo")
        @SuppressLint("SimpleDateFormat") val format = SimpleDateFormat(pattern)
        return format.parse(dateInfo)?.time.toString()
    }

    fun getMyDate(date: Date, pattern: String = "yyyy年MM月dd日"): String {
        @SuppressLint("SimpleDateFormat") val format = SimpleDateFormat(pattern)
        return format.format(date)
    }

    fun getMyTime(date: Date, pattern: String = "HH:mm"): String {
        @SuppressLint("SimpleDateFormat") val format = SimpleDateFormat(pattern)
        return format.format(date)
    }

    fun setEditType(editText: EditText, type: Int) {
        editText.inputType = InputType.TYPE_CLASS_TEXT or type
    }

    fun setEditNumberType(editText: EditText, type: Int) {
        editText.inputType = InputType.TYPE_CLASS_NUMBER or type
    }

    fun checkPass(pass: String): Boolean {
        val pattern = Pattern.compile("^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]{8,}\$")
        return pattern.matcher(pass).matches()
    }

    fun loadRoundImage(activity: Context, data: String, target: ImageView, cornerRadius: Int = 5) {
        ImageLoaderUtils.showRoundedImg(
            context = activity,
            path = data,
            imageView = target,
            radius = cornerRadius
        )
//        Glide.with(activity).load(data).apply(getRoundOptions(cornerRadius)).into(target)
    }

    private fun getRoundOptions(cornerRadius: Int = 10): RequestOptions {
        return RequestOptions.bitmapTransform(RoundedCorners(cornerRadius))
            .placeholder(R.drawable.default_heading)
            .error(R.drawable.bg_image_default)
            //下载的优先级
            .priority(Priority.NORMAL)
            //缓存策略
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .centerCrop()
            .dontAnimate()
    }

    fun loadImage(context: Context, imageView: ImageView, path: String?) {
        ImageLoaderUtils.loadImage(context, imageView, path ?: "")
//        Glide.with(context).load(path).apply(getDefaultOptions()).into(imageView)
    }

    private fun getDefaultOptions(): RequestOptions {
        return RequestOptions()
            .placeholder(R.drawable.bg_image_default)
            .error(R.drawable.bg_image_default)
            //下载的优先级
            .priority(Priority.NORMAL)
            //缓存策略
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .centerCrop()
            .dontAnimate()
    }

    fun setImage(view: View, resId: Int, url: String) {
        Glide.with(view).load(url).into(view.findViewById(resId))
    }

    fun setImage2(view: View, resId: Int, imgId: Int) {
        view.findViewById<ImageView>(resId).setImageResource(imgId)
    }

    fun setText(view: View, resId: Int, text: String) {
        view.findViewById<TextView>(resId).text = text
    }

    fun setText2(view: View, resId: Int, text: String) {
        view.findViewById<TextView>(resId).let { it.text = it.hint.toString().plus(text) }
    }

    fun setTextRes(view: View, resId: Int, textId: Int) {
        view.findViewById<TextView>(resId).setText(textId)
    }

    fun getShowTime(source: Long): String {

        val calendar = Calendar.getInstance()
        calendar.timeInMillis = source
        val d1 = calendar.get(Calendar.DAY_OF_MONTH)
        val m1 = calendar.get(Calendar.MONTH)
        val y1 = calendar.get(Calendar.MONTH)

        calendar.timeInMillis = System.currentTimeMillis()
        val d2 = calendar.get(Calendar.DAY_OF_MONTH)
        val m2 = calendar.get(Calendar.MONTH)
        val y2 = calendar.get(Calendar.MONTH)
        val pattern = when {
            y1 < y2 -> {
                TIME_FORMAT_PATTERN2
            }
            m1 < m2 || d1 < d2 -> {
                TIME_FORMAT_PATTERN6
            }
            else -> {
                TIME_FORMAT_PATTERN7
            }
        }

        return turnToTimeStr(source, pattern)
//        calendar.get(Calendar.)
    }

    fun turnToTimeStr(source: Long, pattern: String = DEFAULT_TIME_FORMAT_PATTERN): String {
        try {
            val date = Date(source)
            val sdf = SimpleDateFormat(pattern)
            return sdf.format(date)
        }catch (e: Exception){
            return ""
        }
    }

    @SuppressLint("SimpleDateFormat")
    fun turnToTimeStr(source: String?, pattern: String = DEFAULT_TIME_FORMAT_PATTERN): String {
        val result = turnToTime(source)
        if (result > 0) {
            val sdf = SimpleDateFormat(pattern)
            return sdf.format(Date(result))
        }
        return source ?: ""
    }

    fun turnToTime(source: String?): Long {
        if (source != null && StringUtils.isNumeric(source)) {
            var temp = source
            if (source.length < 13) {
                val size = 13 - source.length
                for (i in 0..size) {
                    temp = source.plus("0")
                }
            }
            return temp!!.toLong()
        }
        return 0L
    }

    /**
     * 字符串转double
     */
    fun turnToNumber(source: String?, bits: Int = 8): Double {
        if (source == null || source.isNullOrEmpty()) {
            return 0.0
        }
        return try {
            val bigDecimal = BigDecimal(source)
            bigDecimal.setScale(bits, BigDecimal.ROUND_HALF_UP).toDouble()
        } catch (e: NumberFormatException) {
            0.0
        }
    }

    /**
     * double 字符串格式化为指定位数后 返回 取消科学计数法显示
     */
    fun formatValue(source: Double, bits: Int): String {
        val format = NumberFormat.getInstance()
        format.maximumFractionDigits = bits
        format.isGroupingUsed = false
        return format.format(source)
    }

    fun showText(vararg views: TextView) {
        if (views.isNotEmpty()) {
            val method = HideReturnsTransformationMethod.getInstance()
            for (v in views) {
                v.transformationMethod = method
            }
        }
    }

    fun hideText(vararg views: TextView) {
        if (views.isNotEmpty()) {
            val method = PasswordTransformationMethod.getInstance()
            for (v in views) {
                v.transformationMethod = method
            }
        }
    }

    fun showView(vararg views: View) {
        if (views.isNotEmpty()) {
            for (v in views) {
                if (v.visibility != View.VISIBLE) v.visibility = View.VISIBLE
            }
        }
    }

    fun hasAnyChild(rootView: View?) : Boolean{
        if (rootView == null) return false
        val childCount  = (rootView as? ViewGroup)?.childCount ?:0
        return childCount> 0
    }

    fun hasAnyVisibleChild(rootView: View?) : Int{
        if (rootView == null) return 0
        val childCount  = (rootView as? ViewGroup)?.childCount ?:0
        if (childCount <= 0) return 0
        val vp = rootView as? ViewGroup
        if (vp == null) return 0
        val visibles = vp.children.filter {
            it.visibility == View.VISIBLE
        }
        return visibles.toList().size
    }


    fun hideView(vararg views: View) {
        if (views.isNotEmpty()) {
            for (v in views) {
                v.visibility = View.GONE
            }
        }
    }

    fun hideViews(containerView: View, vararg resIds: Int) {
        if (resIds.isNotEmpty()) {
            for (resId in resIds) {
                containerView.findViewById<View>(resId)?.visibility = View.GONE
            }
        }
    }

    /*设置TextView可以长按复制文本*/
    fun setSelectable(text: TextView, selectable: Boolean = true) {
        text.setTextIsSelectable(selectable)
    }

    fun toClipboard(context: Context, text: TextView, onChanged: () -> Unit) {
        toClipboard(context, text.text.toString().trim(), onChanged)
    }

    fun toClipboard(context: Context, text: String, onChanged: () -> Unit) {
        //获取剪贴板管理器：
        (context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager?)?.also {
            it.addPrimaryClipChangedListener {
                onChanged.invoke()
            }
            // 创建普通字符型ClipData
            val mClipData = ClipData.newPlainText("Label", text)
            // 将ClipData内容放到系统剪贴板里。
            it.setPrimaryClip(mClipData)
        }
    }

    /**年月日时间字符串拼接*/
    fun getDateString(year: Int, month: Int, day: Int = 0): String {
        var str = if (month < 10) "$year-0$month" else "$year-$month"
        if (day > 0) {
            str = str.plus(if (day < 10) "-0$day" else "-$day")
        }
        return str
    }

    fun getYMDStamp(year: Int, month: Int, day: Int): Long {
        val calendar = Calendar.getInstance()
        calendar.time = Date(System.currentTimeMillis())
        calendar.set(Calendar.YEAR, year)
        calendar.set(Calendar.MONTH, month - 1)
        calendar.set(Calendar.DAY_OF_MONTH, day)
        calendar.set(Calendar.AM_PM, Calendar.AM)
        calendar.set(Calendar.HOUR, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }

    fun getYMDStamp(date: Long? = null): Long {
        val calendar = Calendar.getInstance()
        val time = if (date != null && date > 0) date else System.currentTimeMillis()
        calendar.time = Date(time)
        calendar.set(Calendar.AM_PM, Calendar.AM)
        calendar.set(Calendar.HOUR, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }

    fun getYMStamp(date: Long? = null): Long {
        val calendar = Calendar.getInstance()
        val time = if (date != null && date > 0) date else System.currentTimeMillis()
        calendar.time = Date(time)
        calendar.set(Calendar.DAY_OF_MONTH, 1)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }

    fun getYMDValue(date: Long): Array<Int> {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = date
        return arrayOf(
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        )
    }

    fun getYMDValue(date: String): Array<Int> {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = turnToTime(date)
        return arrayOf(
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH) + 1,
            calendar.get(Calendar.DAY_OF_MONTH)
        )
    }

    fun errorLogToPrint(throwable: Throwable?) {
        if (throwable == null) {
            return
        }
        throwable.printStackTrace()
        val stackTrace = throwable.stackTrace
        for (element in stackTrace) {
            Log.e("error-toString", element?.toString() ?: "")
            Log.e("error-lineNumber", element?.lineNumber?.toString() ?: "")
            Log.e("error-className", element?.className ?: "")
            Log.e("error-methodName", element?.methodName ?: "")
            Log.e("error-fileName", element?.fileName ?: "")
            Log.e("error-isNativeM", element?.isNativeMethod?.toString() ?: "")
        }
        Log.e("error", throwable.toString())
        Log.e("error", throwable.message ?: "")
    }

    fun getFileCachePath(context: Context, dir: String): String {
        var directoryPath = ""
        //判断SD卡是否可用
        directoryPath = if (Environment.MEDIA_MOUNTED == Environment.getExternalStorageState()) {
            context.getExternalFilesDir(dir)?.absolutePath ?: context.cacheDir.absolutePath
            // directoryPath =context.getExternalCacheDir().getAbsolutePath() ;
        } else {
            //没内存卡就存机身内存
            context.filesDir.absolutePath + File.separator + dir
            // directoryPath=context.getCacheDir()+File.separator+dir;
        }
        val file = File(directoryPath)
        if (!file.exists()) { //判断文件目录是否存在
            file.mkdirs()
        }
        return directoryPath
    }

    private fun catchScreen(activity: Activity): File {
        //获取当前屏幕的大小
        val width = activity.window.decorView.rootView.width
        val height = activity.window.decorView.rootView.height
        //生成相同大小的图片
        var temBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        //找到当前页面的跟布局
        val view = activity.window.decorView.rootView
        //设置缓存
        view.isDrawingCacheEnabled = true
        view.buildDrawingCache()
        //从缓存中获取当前屏幕的图片
        temBitmap = view.drawingCache
        //输出到sd卡
//        FileIOUtil.GetInstance().onFolderAnalysis(FileIOUtil.GetInstance().getFilePathAndName());
        val folderName = getFileCachePath(activity, "jieping")
        val file = File(folderName + File.separator + "${System.currentTimeMillis()}.png")
        try {
            if (!file.exists()) {
                file.createNewFile()
            }
            val foStream = FileOutputStream(file)
            temBitmap.compress(Bitmap.CompressFormat.PNG, 100, foStream)
            foStream.flush()
            foStream.close()
        } catch (e: Exception) {
            Log.i("Show", e.toString())
        }
        return file
    }

    //截屏
    fun catchScreenWithPerssion(activity: Activity, onSuccess: (File) -> Unit) {
        val perms = arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )
        val tips = "截取图片需要您授权读写权限"

        PermissionUtils.requestPermissionActivity(activity, perms,
            hint = "照相机权限",
            onSuccess = {
                onSuccess.invoke(catchScreen(activity))
            },
            onError = {
                Toast.makeText(activity, tips, Toast.LENGTH_SHORT).show()
            })
    }

    //全局搜索“保存图片到相册”会找到其他人写的保存到相册
    //保存到相册,file为图片文件，如果图片文件想保存为指定的格式，可以打开注释；
    fun saveImageToAlbum(
        context: Activity,
        file: File,
        defaultType: String = "png",
        result: () -> Unit
    ) {
        /* val bitmap = BitmapFactory.decodeFile(file.absolutePath)//图片转换成bitmap，图片处理
         if (file.exists()) {
             file.delete()
         }
         try {
             val fos = FileOutputStream(file)
             if (StringUtils.isNotBlankAndEmpty(file.absolutePath)) {
                 when {
                     file.absolutePath.endsWith(".jpg") -> bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos)
                     file.absolutePath.endsWith(".JPEG") -> bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos)
                     else -> bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos)
                 }
             } else {
                 if (StringUtils.isNotBlankAndEmpty(defaultType)) {
                     bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos)
                 } else {
                     bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos)
                 }
             }
             fos.flush()
             fos.close()
         } catch (e: FileNotFoundException) {
             e.printStackTrace()
         } catch (e: IOException) {
             e.printStackTrace()
         }*/

        // 其次把文件插入到系统图库
        try {
            MediaStore.Images.Media.insertImage(
                context.contentResolver,
                file.absolutePath, file.name, null
            )
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
        }

        // 最后通知图库更新
        context.sendBroadcast(
            Intent(
                Intent.ACTION_MEDIA_SCANNER_SCAN_FILE,
                Uri.fromFile(file)
            )
        )//更新相册
        if (file.exists()) {
            file.delete()
            ToastUtil.show(BaseApplication.joinuTechContext, "已保存到系统相册")
        }

        result.invoke()
    }

    //将图片文件裁剪
    fun tailorImageFile(context: Context, file: File, tailerBean: TailerBean): File? {
        val screenHeight = ScreenUtils.heightPixels(context)
        val screenWidth = ScreenUtils.widthPixels(context)
        val oldBitmap = BitmapFactory.decodeFile(file.absolutePath)
        val tailorHeight = if (!StringUtils.isNotBlankAndEmpty(tailerBean.height)
            || tailerBean.height == "0"
        ) {
            screenHeight - tailerBean.top.toInt() - tailerBean.bottom.toInt()
        } else {
            tailerBean.height.toInt()
        }
        val newBitmap = Bitmap.createBitmap(
            oldBitmap,
            tailerBean.left.toInt(),
            tailerBean.top.toInt(),
            screenWidth - tailerBean.right.toInt() - tailerBean.left.toInt(),
            tailorHeight
        )
        val resultFile = saveBitmap(context, newBitmap, "${System.currentTimeMillis()}.png")
        return resultFile
    }

    //在使用该函数的时候，记得把文件的扩展名带上
    @Throws(IOException::class)
    fun saveBitmap(context: Context, bitmap: Bitmap?, bitName: String): File? {
        if (bitmap == null) {
            return null
        }
        val file = File(getFileCachePath(context, "tailfolder") + "${File.separator}$bitName")
        if (file.exists()) {
            file.delete()
        }
        val out: FileOutputStream
        try {
            out = FileOutputStream(file)
            if (bitmap.compress(Bitmap.CompressFormat.PNG, 90, out)) {
                out.flush()
                out.close()
            }
            return file
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
            return null
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
    }


    //图片处理tcp，把图片的base64转化成图片文件、bitmap
    @Throws(IOException::class)
    fun base64ToFile(
        context: Context?,
        base64String: String?
    ): File? {
        val resultString1 = base64String?.replace("data:image/png;base64", "")
        val resultString2 = resultString1?.replace("data:image/jpg;base64", "")
        val resultString3 = resultString2?.replace("data:image/jpeg;base64", "")
        val bytes =
            Base64.decode(resultString3, Base64.DEFAULT)
        val srcBitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
        return saveBitmap(context!!, srcBitmap, System.currentTimeMillis().toString() + ".png")
    }

    //初始化bugly----------------------------------------开始-------------------
    fun initBugly() {
//        if (!isDebug && !BaseApplication.hasInitBugly) {
        if (!BaseApplication.hasInitBugly) {
            initBuglySdk(BaseApplication.joinuTechContext)
        }
    }

    fun reportException(error: String) {
        try {
            CrashReport.postException(8 , "flutterException" , error, error, HashMap())
        }catch (e: Exception){
            Timber.i("reportException==> $e")
        }
    }

    private fun initBuglySdk(mContext: Context) {
        BaseApplication.hasInitBugly = true
        val context = mContext
        // 获取当前包名
        val packageName = context.packageName
        // 获取当前进程名
        val processName = getProcessName(Process.myPid())
        // 设置是否为上报进程
        val strategy = CrashReport.UserStrategy(context)
        strategy.appVersion = DeviceUtil.getPKgVersionName(context).plus("_")
            .plus(DeviceUtil.getVersionCode(context).toString())
        strategy.appReportDelay = 20000
        strategy.isUploadProcess = processName == null || processName == packageName
        strategy.setDeviceID(DeviceIdUtil.getDeviceId(context, false))
        strategy.setDeviceModel(ConsValue.deviceModel)//设置设备型号
        val uid = UserHolder.getUserId() ?:""
        Timber.i("bugly设置 时候 uid = $uid")
        CrashReport.setUserId(uid)
        // 初始化bugly 建议在测试阶段建议设置成true，发布时设置为false
        CrashReport.initCrashReport(context, "1673faa759", false, strategy)
        CrashReport.setUserId(uid)
    }

    private fun getProcessName(pid: Int): String? {
        var reader: BufferedReader? = null
        try {
            reader = BufferedReader(FileReader("/proc/$pid/cmdline"))
            var processName = reader.readLine()
            if (!TextUtils.isEmpty(processName)) {
                processName = processName.trim { it <= ' ' }
            }
            return processName
        } catch (throwable: Throwable) {
            throwable.printStackTrace()
        } finally {
            try {
                reader?.close()
            } catch (exception: IOException) {
                exception.printStackTrace()
            }

        }
        return null
    }
    //初始化bugly----------------------------------------结束-------------------

    //初始化地图
    fun initLocation() {
        //百度地图
        Loggerr.i("初始化", "===初始化百度地图===")
//        SDKInitializer.setAgreePrivacy(BaseApplication.joinuTechContext, true)
//        try {
//            SDKInitializer.initialize(BaseApplication.joinuTechContext)
//        } catch (e: Exception) {
//            XUtil.errorLogToPrint(e)
//        }
//        SDKInitializer.setCoordType(CoordType.BD09LL)
    }

    //图片验证码整理============开始============================================================================
    private var dialog: AlertDialog? = null

    //仅验证图片验证码，不发送短信
    private fun checkImageCode(
        life: LifecycleTransformer<Result<Any>>, dataMap: Any,
        onSuccess: (Any) -> Unit, onError: (String) -> Unit
    ) {
        //校验图片验证码
        LoginService.verifyImage(dataMap)
            .compose(life)
            .compose(ErrorTransformer.getInstance<Any>())
            .subscribe(object : BaseSubscriber<Any>() {
                override fun onError(ex: ApiException) {
                    if (ex.code == 1500) {
                        onError.invoke("匹配失败")
                    } else onError.invoke(ex.message)
                }

                override fun onComplete() {

                }

                override fun onNext(t: Any?) {
                    if (t != null) {
                        onSuccess.invoke(t)
                    }
                }

            })
    }

    //验证图片验证码是否正确,并发送短信
    private fun checkImageCodeAndGetMsg(
        life: LifecycleTransformer<Result<Any>>, dataMap: Any,
        onSuccess: (Any) -> Unit, onError: (String) -> Unit
    ) {
        //验证图片验证码带短信验证码....
        LoginService.verifyImageWithMsg(dataMap)//图片验证并发送验证码
            .compose(life)
            .compose(ErrorTransformer.getInstance<Any>())
            .subscribe(object : BaseSubscriber<Any>() {
                override fun onError(ex: ApiException) {
                    onError.invoke(ex.message)
                }

                override fun onComplete() {

                }

                override fun onNext(t: Any?) {
                    if (t != null) {
                        onSuccess.invoke(t)
                    }
                }
            })
    }

    private fun getImageCodeData(
        life: LifecycleTransformer<Result<VerifyImageBean>>, phone: String,
        onSuccess: (VerifyImageBean) -> Unit, onError: (String) -> Unit
    ) {
        //获取图片验证码
        LoginService.getVerifyImage(phone)
            .compose(life)
            .compose(ErrorTransformer.getInstance<VerifyImageBean>())
            .subscribe(object : BaseSubscriber<VerifyImageBean>() {
                override fun onError(ex: ApiException) {
                    //该手机号未注册
                    onError.invoke(
                        if (ex.code == 1104) {
                            "该手机号未注册"
                        } else ex.message
                    )
                }

                override fun onComplete() {

                }

                override fun onNext(t: VerifyImageBean?) {
                    if (t != null) {
                        onSuccess.invoke(t)
                    }
                }

            })
    }

    private fun showImageVerifyIv(
        activity: MyUseBindingActivity<out ViewBinding>,
        it: VerifyImageBean, backUrl: String, bigIv: ImageView,
        topUrl: String, smallIv: ImageView
    ): Pair<String, String> {
        var backUrl1 = backUrl
        var topUrl1 = topUrl
        if (StringUtils.isNotBlankAndEmpty(it.backUrl)) {
            backUrl1 = it.backUrl
            val options = RequestOptions
                .placeholderOf(R.drawable.image_placeholder_im)
                .error(R.drawable.image_placeholder_im)
                .centerCrop()
            ImageLoaderUtils.showImgWithOption(activity!!, backUrl1, bigIv, options)
        }
        if (StringUtils.isNotBlankAndEmpty(it.topUrl)) {
            topUrl1 = it.topUrl
            ImageLoaderUtils.loadImage(activity, smallIv, topUrl1)
        }
        return Pair(backUrl1, topUrl1)
    }

    private fun resetImageDistance(
        seekBar: AppCompatSeekBar, smallIv: ImageView,
        layoutParams: ConstraintLayout.LayoutParams?
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            seekBar.setProgress(0, true)
        } else seekBar.progress = 0
        var layoutParams1 = layoutParams
        if (layoutParams1 == null)
            layoutParams1 = smallIv.layoutParams as ConstraintLayout.LayoutParams
        layoutParams1.leftMargin = 0
        smallIv.layoutParams = layoutParams1
    }


    private fun showAndVerifyImage(
        activity: MyUseBindingActivity<out ViewBinding>,
        it: VerifyImageBean,
        phone: String,
        type: Int,
        isNeedMsg: Boolean,
        onSuccess: () -> Unit,
        onError: () -> Unit
    ) {
        var layoutParams: ConstraintLayout.LayoutParams? = null
        val view = View.inflate(activity, R.layout.dialog_image_verify, null)
        dialog = BottomDialogUtil.showBottomDialog(activity, view, Gravity.CENTER)
        dialog?.setCanceledOnTouchOutside(false)
        val bigIv = view.findViewById<ImageView>(R.id.bigIv)
        val smallIv = view.findViewById<ImageView>(R.id.smallIv)
        val seekBarView = view.findViewById<AppCompatSeekBar>(R.id.seekBar)
        val fileText = view.findViewById<TextView>(R.id.fileText)
        val maskIng = view.findViewById<View>(R.id.maskIng)
        val verifySuccessIv = view.findViewById<ImageView>(R.id.verifySuccessIv)
        val refresh = view.findViewById<ImageView>(R.id.refresh)
        var topUrl = ""
        var backUrl = ""
        refresh.setOnClickListener(object : OnNoDoubleClickListener {
            override fun onNoDoubleClick(v: View) {
                //点击刷新按钮，启动动画
                v.animate().rotationBy(360f).setDuration(500)
                    .setInterpolator(AccelerateDecelerateInterpolator())
                    .setListener(object : Animator.AnimatorListener {
                        override fun onAnimationStart(animation: Animator) {}
                        override fun onAnimationEnd(animation: Animator) {
                            getImageCodeData(activity.bindToLifecycle(), phone,
                                onSuccess = {
                                    val pair = showImageVerifyIv(
                                        activity,
                                        it,
                                        backUrl,
                                        bigIv,
                                        topUrl,
                                        smallIv
                                    )
                                    backUrl = pair.first
                                    topUrl = pair.second
                                    resetImageDistance(seekBarView, smallIv, layoutParams)
                                },
                                onError = {
                                    ToastUtil.show(activity!!, it)
                                })
                        }

                        override fun onAnimationCancel(animation: Animator) {}
                        override fun onAnimationRepeat(animation: Animator) {}
                    })
            }

        })
        val pair = showImageVerifyIv(activity, it, backUrl, bigIv, topUrl, smallIv)
        backUrl = pair.first
        topUrl = pair.second

        seekBarView.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                layoutParams = smallIv.layoutParams as ConstraintLayout.LayoutParams
                val width: Int = bigIv.width
                val smallWidth: Int = smallIv.width
                val differenceValue = progress * 0.01 * width
                val value: Int = if (differenceValue > width - smallWidth) {
                    width - smallWidth
                } else {
                    differenceValue.toInt()
                }
                layoutParams?.leftMargin = value
                smallIv.layoutParams = layoutParams
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {

            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                val progress = seekBar!!.progress
                Log.e("uploadProgressValue", progress.toString())
                activity.getLoadingDialog("获取图片验证码", false)
                val width: Int = bigIv.width
                val smallWidth: Int = smallIv.width
                val differenceValue = progress * 0.01 * width
                val value: Double = if (differenceValue > width - smallWidth) {
                    (width - smallWidth) * 1.00 / width
                } else {
                    differenceValue / width
                }
                val hashMap = hashMapOf<String, Any>()
                val newId = if (StringUtils.isNotBlankAndEmpty(backUrl)) {
                    val dotIndex = backUrl.lastIndexOf(".")
                    val lineIndex = backUrl.lastIndexOf("/")
                    backUrl.substring(lineIndex + 1, dotIndex)
                } else ""
                val oriId = if (StringUtils.isNotBlankAndEmpty(topUrl)) {
                    val dotIndex = topUrl.lastIndexOf(".")
                    val lineIndex = topUrl.lastIndexOf("/")
                    topUrl.substring(lineIndex + 1, dotIndex)
                } else ""
                hashMap["newId"] = newId
                hashMap["oriId"] = oriId
                hashMap["phone"] = phone
                hashMap["type"] = type
                /*  hashMap["type"] = if (needCode != "0") {
                      when (needCode) {
                          "1" -> 7 //imageVerType  7用户登录
  //                        "2" -> 14 //imageVerType ??
                          else -> 0
                      }
                  } else {
                      0
                  }*/
                hashMap["scale"] = value

                if (isNeedMsg) {
                    checkImageCodeAndGetMsg(activity.bindToLifecycle(),
                        hashMap,
                        onSuccess = {
                            activity.dismissDialog()
                            ToastUtil.show(activity, "图片匹配成功，短信已发送")
                            maskIng.visibility = View.VISIBLE
                            verifySuccessIv.visibility = View.VISIBLE
                            maskIng.postDelayed({
                                dialog?.dismiss()
                                onSuccess.invoke()
                            }, 1500)
                        },
                        onError = {
                            activity.dismissDialog()
                            if (it == "匹配失败") {
                                fileText.visibility = View.VISIBLE
                                fileText.postDelayed({
                                    fileText.visibility = View.GONE
                                }, 1000)
                            }
                            ToastUtil.show(activity!!, it)
                            resetImageDistance(seekBarView, smallIv, layoutParams)
                        }
                    )
                } else {
                    checkImageCode(activity.bindToLifecycle(),
                        hashMap,
                        onSuccess = {
                            activity.dismissDialog()
                            ToastUtil.show(activity, "图片匹配成功")
                            maskIng.visibility = View.VISIBLE
                            verifySuccessIv.visibility = View.VISIBLE
                            maskIng.postDelayed({
                                dialog?.dismiss()
                                onSuccess.invoke()
                            }, 1500)
                        },
                        onError = {
                            activity.dismissDialog()
                            if (it == "匹配失败") {
                                fileText.visibility = View.VISIBLE
                                fileText.postDelayed({
                                    fileText.visibility = View.GONE
                                }, 1000)
                            }
                            ToastUtil.show(activity!!, it)
                            resetImageDistance(seekBarView, smallIv, layoutParams)
                        })
                }
            }
        })
    }

    //type:是在验证图片验证码是否正确时使用到的；
    //isNeedMsg:代表图片验证码通过后是否发送短信
    fun getImageCode(
        activity: MyUseBindingActivity<out ViewBinding>,
        phone: String,
        type: Int,
        isNeedMsg: Boolean = true,
        onSuccess: () -> Unit,
        onError: () -> Unit
    ) {
        activity.getLoadingDialog("获取图片验证码", false)
        getImageCodeData(activity.bindToLifecycle(), phone, onSuccess = {
            activity.dismissDialog()
            showAndVerifyImage(activity, it, phone, type, isNeedMsg, onSuccess, onError)
        },
            onError = {
                activity.dismissDialog()
                ToastUtil.show(activity, it)
            })
    }

    //图片验证码整理============结束===========

    //读取assets中的文件，还有第二种方式是读取res/raw中的文件
    fun readPrivacy(context: Context): String {
        val assetManage = context.assets
        var inputStream: InputStream? = null
        var reader: InputStreamReader? = null
        var buffer: BufferedReader? = null

        val stringBuilder = StringBuilder()

        try {
            inputStream = assetManage.open("privacy.txt")
            reader = InputStreamReader(inputStream, "utf-8")
            buffer = BufferedReader(reader)

            var line = ""
            while (true) {
                line = buffer.readLine() ?: break
                line = line.trim()
                stringBuilder.append(line).append("\n")
            }

            buffer.close()
            reader.close()
            inputStream.close()
            buffer = null
            reader = null
            inputStream = null

            return stringBuilder.toString()
        } catch (e: Exception) {
            errorLogToPrint(e)
        } finally {
            if (buffer != null) {
                buffer.close()
            }
            if (reader != null) {
                reader.close()
            }
            if (inputStream != null) {
                inputStream.close()
            }
        }

        return ""
    }
}


