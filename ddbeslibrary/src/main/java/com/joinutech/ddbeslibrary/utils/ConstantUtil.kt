package com.joinutech.ddbeslibrary.utils

import android.content.Context
import android.os.Build
import com.joinutech.ddbeslibrary.BuildConfig

/**
 * Description 常量工具类
 * Author HJR36
 * Date 2018/4/12 16:57
 */
//两次点击重复时间，300ms为宜
const val MIN_CLICK_DELAY_TIME = 500L
var lastClickTime: Long = 0
var lastClickViewId: Int = -1

/**
 * 下面均为跳转路径保存处
 */
const val webLoginActivity = "/login/WebLoginActivity"
const val login: String = "/login/LoginActivity"
const val home: String = "/app/HomeActivity"

/**web 文件预览页面*/
const val filePreviewWebActivity = "/ddbesLib/file_preview_activity"

/**通用web页面*/
const val COMMON_WEB_ACTIVITY: String = "/ddbesLib/common_web_activity"

///**简单web页面 只接受url和简单返回事件处理*/
//const val SIMPLE_WEB_ACTIVITY: String = "/ddbesLib/simple_web_activity"

/**环境变量切换页面 非PRO可用*/
const val ENV_CHANGE = "/ddbesLib/env_change"

object RoutePersonal {
    /**关于App页面*/
    const val PERSON_ABOUT_APP: String = "/Personal/AboutAppActivity"

    /**用户二维码*/
    const val orgQrCode: String = "/Personal/OrgQrCodeActivity"

    /**个人详情页面*/
    const val personalInfo: String = "/Personal/PersonInfoActivity"

    /**邮件设置页面*/
    const val personNameOrEmailActivity = "/Personal/PersonNameActivity"

    /**用户通知配置页面*/
    const val personNotifyConfigActivity = "/Personal/PersonNotifyConfigActivity"
}

object RouteRtcMeeting {
    // 音视频会议
    const val rtcMeetingActivity: String = "/rtc_meeting/RtcMeetingActivity"
}

object RouteIm {
    /**聊天消息处理服务*/
    const val CHAT_SERVICE: String = "/message/service"

    /**用户定位消息详情页面*/
    const val locationActivity: String = "/message/IMLocationDetailActivity"

    /**
     * 系统消息免打扰
     * 团队免打扰设置页面
     * 审批独立功能的 全局 免打扰设置
     * */
    const val workMessageNoticeSetActivity: String = "/message/WorkNoticeSetActivity"

    /**单聊页面1*/
    const val singleChat: String = "/message/IMMessageActivity"

    //预览文件页面
    const val playFileActivity: String = "/libim/PlayFileActivity"
    const val fileDeatailActivity: String = "/libim/FileDeatailActivity"

    /**群聊页面1*/
    const val groupChat: String = "/message/GroupChatMessageActivity"

    //通知页面tcp
    const val noticeChat: String = "/message/TcpNoticeListActivity"

    /**系统消息详情页面*/
    const val systemMsg: String = "/message/SystemMessageDetailActivity"

    /**系统消息列表页面*/
    @Deprecated("no used")
    const val systemMsgList: String = "/message/SystemMessageActivity"

    /**公司消息通知列表页面*/
    @Deprecated("no used")
    const val companyMsg: String = "/message/WorkMessageActivity"

//    /**公司审批消息 通知列表页面*/
//    const val companyApprovalMsg: String = "/message/ApprovalMessageActivity"
}

//object RouteAte {
//    /**考勤规则页面*/
//    const val attendanceRuleActivity: String = "/work/AttendanceRuleActivity"
//
//    /**考勤打卡页面 打卡 和 考勤统计*/
//    const val attendanceHomeActivity: String = "/work/AttendanceHomeActivity"
//
//    /**考勤设置首页*/
//    const val attendanceSetActivity: String = "/work/AttendanceSetActivity"
//
//    /**新版考勤首页*/
//    const val attendanceOrderListActivity: String = "/work/attendanceOrderListActivity"
//    const val attendanceSpecialDateShowActivity: String = "/work/AttendanceSpecialDateShowActivity"
//}

object RouteTask {

//    /**项目中任务列表页面 分类显示任务*/
//    const val taskHomeActivity: String = "/task/TaskActivity"

    /**选择任务结束时间页面*/
    const val selectEndTimeActivity: String = "/task/SelectTaskEndTimeActivity"

    /**任务详情*/
    const val taskDetailActivity: String = "/task/TaskDetailActivity"

    /**项目移交*/
    const val projectHandOverActivity: String = "/task/ProjectHandOverActivity"

    /**任务所有参与人页面*/
    const val taskAllJoiner = "/task/TaskAllJoinerShowActivity"
}

//object RouteWork {
////
////    /**考勤本地记录查询*/
////    const val ATTEND_LOG = "/lib_im/attend_log"
////
////    /**群投票页面*/
////    const val voteFlutterPageActivity: String = "/work/VoteFlutterPageActivity"
////
////    /**群通知页面*/
////    const val groupNoticeFlutterPageActivity: String = "/work/GroupNoticePageActivity"
////
////    /**日报*/
////    const val companyDigitalReportActivity = "/work/CompanyDigitalReportActivity"
////
////    /**工作汇报web页面*/
////    const val reportWebActivity = "/work/ReportWebActivity"
////
////    /**工作汇报web 编辑页面*/
////    const val reportWebEditActivity = "/work/ReportWebEditActivity"
////
////    /**
////     * 外链web页
////     */
////    const val ExternalWebActivity = "/work/ExternalWebPageActivity"
////
////    /**汇报列表页面*/
////    const val reportListActivity = "/work/ReportListActivity"
//}

object RouteOrg {

    /**跳转选择部门可见级别*/
    const val CODE_DEPT_VISIBLE_SET = 1201
    const val CODE_CHECK_ORG_RANGE = 1202

    /*团队转移创建者*/
    const val ORG_CHANGE_CREATOR: String = "/addressbook/or_change_creator"

    /*团队可见范围*/
    const val ORG_SECRET_VISIBLE_RANGE: String = "/addressbook/visibleRangeActivity"

    /*团队选择部门 几个部门显示*/
    const val ORG_DEPT_SELECT: String = "/addressbook/deptSelectActivity"

    /**团队和好友 申请详情*/
    const val applicationDetailActivity = "/addressbook/ApplicationDetailsActivity"

    /**未处理申请页面*/
    const val undo: String = "/addressbook/UndoActivity"

    /**创建项目选择参与人员、邀请新成员、团队权限管理、邀请方式、聊天选择好友*/
    const val friendListWithSearchActivity = "/addressbook/PersonSearchSelectList1"

    const val searchOrgPersonActivity = "/addressbook/SearchOrgPersonActivity"

    /**选择会话分享等*/
    const val searchFriendToTranslateActivity = "/addressbook/PersonSearchSelectList2"

    //黑名单列表
    const val blackListActivity = "/addressbook/BlackListActivity"

    /**用户详情信息页面*/
    const val friendInfoActivity = "/addressbook/FriendInfoActivity"

    /**选择成员分享页面*/
    const val selectMemberShareActivity = "/addressbook/SelectMemberShareActivity"

    /**添加好友页面*/
    const val addFriendActivity = "/addressbook/AddFriendActivity"

    /**组织架构页面*/
    const val orgChartActivity: String = "/addressbook/OrganizationChartActivity"

    /**团队成员、加入团队、搜索好友、添加好友、邀请外部联系人*/
    const val searchResultActivity: String = "/addressbook/SearchResultActivity"

    /**创建团队页面*/
    const val createOrgActivity: String = "/addressbook/CreatOrganizationActivity"

    /**团队邀请详情*/
    const val orgIntroActivity: String = "/addressbook/OrganizationIntroActivity"

    /**权限详情页面*/
    const val permissionNoticeDetailActivity: String = "/addressbook/PermissionNoticeDetailActivity"

    const val industryActivity: String = "/addressbook/IndustryActivity"

    const val orgImportPersonActivity = "/addressbook/OrgImportPersonActivity"

    /**选择公司成员页面*/
    const val orgSelectMemberActivity = "/addressbook/OrgSelectMemberActivity"
    const val phoneContactActivity = "/addressbook/PhoneContactActivity"

    /**协作人申请详情页面*/
    const val cooperationCompanyApplicationDetailActivity =
            "/addressbook/CooperationCompanyApplicationDetailActivity"

    /**群组列表页面*/
    const val groupListActivity = "/addressbook/GroupListActivity"

    const val FriendSelectWithSearchList = "/addressbook/FriendSelectWithSearchList"
    const val searchFriendToShareActivity = "/addressbook/FriendSelectWithSearchList2"//tcp新添加
    const val WithDataListSelectActivity = "/addressbook/WithDataListSelectActivity"

    /**选择人员页面*/
    const val SelectSearchListWithBottomShowActivity =
            "/addressbook/SelectWithSearchListWithBottomShowActivity"

    const val SelectSearchListWithBottomShowActivity2 =
            "/addressbook/SelectWithSearchListWithBottomShowActivity2"

}

object RouteVc {

    const val CODE_DEPT_VISIBLE_SET = 1301
    const val CODE_CHECK_ORG_RANGE = 1302

    /**视频会议列表*/
    const val VC_LIST_PAGE = "/vc/vc_list"

    /**预约会议页面*/
    const val VC_PLAN_PAGE = "/vc/vc_plan"

    /**会议详情页面*/
    const val VC_DETAIL_PAGE = "/vc/vc_detail"

    /**邀请加入会议页面**/
    const val VC_INVITE_PAGE = "/vc/InviteJoinVideoConversationActivity"

    /**当前邀请中会议Id*/
    const val CURRENT_INVITE_MEETING_ID = "current_invite_meeting_id"

    /**当前进行中会议Id*/
    const val CURRENT_MEETING_ID = "current_meeting_id"

    /**参会人员页面*/
    const val VC_CONFEREE_PAGE = "/vc/ConfereeActivity"

    /**
     * 会议页面
     * todo 复用 实现多人聊天页面
     */
    const val VC_CONFEREE_ROOM = "/vc/VideoRoomActivity"

    /**跳转进行中已邀请会议列表*/
    const val VC_START_RUNNING_CONF = "/vc/start_conf"

    /**创建通话*/
    const val VC_CREATE_CALL = "/vc/create_call"

    /**单聊页面*/
    const val VC_SINGLE_CONFEREE_ROOM = "/vc/SingleVideoRoomActivity"

    /**群聊页面*/
    const val VC_GROUP_CONFEREE_ROOM = "/vc/GroupVideoRoomActivity"

    /**邀请加入语音或视频通话页面**/
    const val VC_INVITE_CHAT_PAGE = "/vc/InviteMediaChatActivity"

    /**音视频相关服务调用*/
    const val VC_SERVICE = "/vc/service"
}

object PushEvent {
    /**厂商推送token更新*/
    const val PUSH_TOKEN_UPDATE = "push_token_update"

    /**离线推送消息数据接收到*/
    const val OFF_PUSH_RECEIVE = "off_push_receive"

    //微信支付结果回调
    const val TCP_WX_PAY_RESULT_CODE = "tcp_wx_pay_result_code"
}

object CommonKeys {
    const val TITLE_NAME = "title"
    const val RIGHT_TITLE = "rightTitle"
    const val DATA = "data"
    const val DATA_ARRAY = "data_array"
    const val EXTRA_DATA = "extra_data"
    const val DATE = "extra_date"
    const val POSITION = "position"
    const val PAGE_TYPE = "pageType"
    const val SHOW_LEVEL = "showLevel"
    const val RESULT_DATA = "resultData"
    const val COMPANY_ID = "companyId"
    const val ORG_ID = "organizationId"

    //系统通知和审批通知开启免打扰后，存到缓存集合中的字符串id
    const val TCP_SYSTEM_PUSH_ID = "tcp_system_push_id"
    const val TCP_APPROVAL_PUSH_ID = "tcp_approval_push_id"
    const val TCP_TICKET_PUSH_ID = "tcp_ticket_push_id"
    const val TCP_KINGDEE_PUSH_ID = "tcp_kingdee_push_id"
    const val TCP_TRAIN_PUSH_ID = "tcp_train_push_id"
    const val TCP_MATTER_PUSH_ID = "tcp_matter_push_id"
    const val TCP_GENER_MANAGEMENT_PUSH_ID = "tcp_gener_management_push_id"
}

object ConsKeys {

    /**系统消息*/
    const val KEY_SYSTEM = "system"

    /**公司消息*/
    const val KEY_COMPANY = "company"

    /**公司审批消息集合*/
    const val KEY_COMPANY_APPROVAL = "approval"

    /**协作公司消息*/
    const val KEY_COMPANY_COOPER = "cooperationCompany"

    /**公司消息转为系统消息处理*/
    const val KEY_COMPANY_SYSTEM = "company_system"

    /**群组消息*/
    const val KEY_GROUP = "group"

    /**单聊消息*/
    const val KEY_SINGLE_CHAT = "single_chat"

    /**群组通知*/
    const val KEY_GROUP_NOTICE = "group_notice"

    /**intent 传数据对象时使用*/
    const val KEY_INTENT_DATA = "intentData"

    /**选择好友*/
    const val SELECT_FRIEND = 1201

    /**选择团队成员*/
    const val SELECT_TEAM_MEMBER = 1202

    /**结果数据*/
    const val RESULT_DATA = "result_data"

    /**结果类型*/
    const val RESULT_TYPE = "result_type"

    const val PHONE_CLIENT = 2
    const val PHONE = "userPhone"

    //个人信息页缓存相关
    const val USER_INFO = "userInfo"//用户id

    const val PUSHTOKEN = "pushToken"
    const val TOKEN_CACHE = "token_cache"
    const val FRIEND_CACHE_VERSION = "friend_cache_version"

    /**好友信息更新,data 为 userId*/
    const val FRIEND_INFO_UPDATE = "friend_info_update"

    /**人员信息更新,data 为 userId*/
    const val USER_INFO_UPDATE = "user_info_update"

    /** 群信息更新,data 为 groupId*/
    const val GROUP_INFO_UPDATE = "group_info_update"

    /** 团队信息更新,data 为 companyId*/
    const val ORG_INFO_UPDATE = "org_info_update"

    /** 主要团队更新 companyId*/
    const val ORG_MAIN_CHANGE = "org_main_change"

    const val OFFLINE_NEW_SESSION_LIST = "OFFLINE_NEW_SESSION_LIST"

//    /**刷新群组列表*/
//    const val GROUP_LIST_REFRESH = "Event_refresh_group_list"

    /**应用版本更新*/
    const val APP_VERSION_UPDATE = "app_version_update"

    /**应用版本更新进度回调*/
    const val APP_UPDATE_PROGRESS = "app_update_progress"

    const val TITLE_NAME = "title"
    const val RIGHT_TITLE = "rightTitle"

    /**公司id*/
    const val COMPANY_ID = "companyId"

    /**url参数*/
    const val PARAMS_URL = "paramsUrl"

    /**传参为某id时使用，审批 modelId 考勤组id*/
    const val MODEL_ID = "modelId"

    const val MODEL_NAME = "modelName"

    /**传参类型时公共key*/
    const val MODEL_TYPE = "modelType"

    /**审批 approveId 传参key*/
    const val TARGET_ID = "targetId"

    /**日期数据*/
    const val PARAMS_DATE = "paramsDate"

    /**用户id*/
    const val USER_ID = "userId"

    /**目标id*/
    const val TARGET_USER_ID = "targetUserId"

    /**JS交互接口名*/
    const val JS_TRANS_PORT = "DDBESOFFICE"

    /**选择图片 拍照 类型*/
    const val TAKE_PHOTO = "takePhoto"
    const val TAKE_VIDEO = "takeVideo"

    const val ATTEND_TAB_CHECK = "ATTEND_TAB_CHECK"  // 考勤的 审批 tab
}

object RouteProvider {

    /**审批功能提供外部调用*/
    const val APR_FUN_PROVIDER = "/approval/provider"

    /**工作模块对外提供外部调用*/
    const val WORK_PROVIDER = "/work/workProvider"

    /**IM 对外提供调用*/
    const val IM_PROVIDER = "/lib_im/service/im"
}

/**全局动态常量*/
object ConsValue {
    private var statusBarHeight = 0
    fun getStatusBarHeight(context: Context, reset: Boolean = false): Int {
        if (statusBarHeight == 0 || reset) {
            statusBarHeight = ScreenUtils.px2dip(context, ScreenUtils.getStatusBarHeight2(context))
        }
        return statusBarHeight
    }

    /**厂商/品牌/设备型号/设备本名/分辨率/数据库版本*/
    var deviceModel = ""

    // 语音消息录制格式
    const val RECORD_TYPE_AAC = 1
    const val RECORD_TYPE_AMR = 0

    fun getWebHeaders(): HashMap<String, String> {
        return hashMapOf(
                "platform" to "1",
                "deviceModel" to ConsValue.deviceModel,
                "appVersion" to BuildConfig.innerCode,
                "version" to Build.VERSION.SDK_INT.toString()
        )
    }

    /**根部门id*/
    const val ROOT_DEPT_ID = "0"
}

object USER {

//    const val USER_ID = "userId"//用户id
//    const val COMPANY_ID = "companyId"
//    const val GROUP_ID = "groupId"

//    const val USERNAME = "userName"
//    const val WXNICKNAME = "wxNickName"
//    const val HEADIMG = "headimg"
//    const val EMAIL = "email"

//    const val TOKEN = "token"
//    const val REFRESHTOKEN = "refresh_token"

//    const val COMPANYNAME = "companyName"
//    const val COMPANYLOGO = "companyLogo"
//    const val REJECTJOIN = "rejectJoin"
//    const val REJECTINVITATION = "rejectInvitation"
//    const val USERSEX = "userSex"
//    const val AGE = "userAge"
//    const val PROFESSION = "userProfession"
//    const val ADDRESS = "userAddress"
}

object MMKVKEY {
    //记录一些重要的MMKV的key值
    /*// TODO: 2020/4/26 8:47 验证码登录判断 */
    const val FIRST_VERIFY_CODE_PHONES = "isFirstUseVerifyCode" //是否是第一次使用验证码登录
    const val IMEI = "imei"//用户手机标识
    const val VERSION_NAME = "version_name"//当前版本号 发行版本号
    const val VERSION_CODE = "version_code"//当前打包版本号 内部代码
//    const val TEL_NUM = "tel_num"//客服号码

    /*TODO: 2020/4/26 8:44 只有首次安装成功后，第一次打开才弹出隐私设置*/
    const val IS_READ_PRIVACY = "is_read_privacy" //是否阅读隐私权议
    const val PRIVACY_READED = "privacy_readed" //是否阅读隐私权议
    const val APP_Statistics = "statistics"//app应用统计
    const val ENTRY_VIDEO_ROOM = "entry_Video_Room"//进入视频会议的房间

    /**网络请求日志log*/
    const val KEY_REQUEST_LOG_CONTENT = "key_request_log_content"

}

//IM消息类型
object MESSAGE_TYPE {
    /**系统消息 -2*/
    const val SYSTEM = -2

    /**公司消息  -3*/
    const val COMPANY = -3

    /**合作团队  -4*/
    const val COOPERATIONCOMPANY = -4

    //单聊离线消息  系统消息 公司消息 合作团队
    const val OFFLINE = -100

    /**只在登录时推送*/
    /**邀请且进行中会议信息 数组*/
    const val OFFLINE_MEETING = -110

    /**好有申请和团队申请未处理数量*/
    const val OFFLINE_FRIEND_ORG = -111

    /**只在登录时推送*/

    /**单向添加或删除好友 消息*/
    const val FRIEND_DELETE = -201

    /**群组消息 -301*/
    const val GROUP = -301

    //群组名称修改
    const val GROUP_NAME_CHANGE = -302

    //创建群组发给其他成员邀请信息和xx邀请你加入群组通知
    const val GROUP_INVITE = -303

    /**
     * 群邀请信息
     * {
    "groupId":"2487247263340954621",
    "msgId":"2487247263340956669",
    "groupLogo":"https://ddbes-repository-1257239906.cos.ap-beijing.myqcloud.com/IM/GROUP/2487247263340955645.png",
    "type":"-303",
    "title":"担当",
    "content":"{\"userNameAvatars\":[{\"id\":\"2481977188144907261\",\"name\":\"贾老师\",\"headimg\":\"http://ddbes.cdn.joinutech.com/7.jpg\"},
    {\"id\":\"2483544464660890621\",\"name\":\"黎叔\",\"headimg\":\"http://ddbes.cdn.joinutech.com/10.jpg\"}],
    \"username\":\"赵阳阳\"}",
    "icon":"",
    "createTime":1588987477061,
    "groupName":"贾老师、黎叔",
    "pushMsg":"赵阳阳邀请贾老师, 黎叔加入了群聊"
    }
     * */

    //群组创建者修改
    const val GROUP_CREATOR_CHANGE = -304

    //您被群组创建者移出此群组
    const val GROUP_REMOVE_MEMBER = -305

    //团队群组已解散
    const val DISBAND_GROUP = -306

    //团队群组的加入
    const val GROUP_ORG_ADD = -307

    //团队群组新加入的成员打招呼
    const val GROUP_ORG_ADD_NEW_HELLO = -308

//    fun getGroupNotifyTypes(): HashSet<Int> {
//        return hashSetOf(GROUP_NAME_CHANGE, GROUP_INVITE, GROUP_CREATOR_CHANGE,
//                GROUP_REMOVE_MEMBER, DISBAND_GROUP, GROUP_ORG_ADD, GROUP_ORG_ADD_NEW_HELLO)
//    }

    /*
    * 群组通知类型 去message，实际消息内容
    * {"groupId":"2484278513226482685",
    * 存储消息id："msgId":"2484278514300225533",
    * "groupLogo":"https://ddbes-repository-1257239906.cos.ap-beijing.myqcloud.com/IM/GROUP/2484278514300224509.png",
    * "type":"-303",
    * "title":"担当",
    * "content":"#黄洁如#邀请\u0026iOS王园, 张杰斌\u0026加入了群聊",// type -303 单聊 格式修改为 map {userNameAvatars邀请的人，username 邀请人}
    * "icon":"1 语音 2 视频",
    * "createTime":1586222614203,
    * "groupName":"iOS王园、张杰斌"}
    * */

    /*{"duration":0.0,
    接收人或者群id："targitId":"2485487838578082813",
    发送人："fromUserId":"2468510354695583741",
    类型："type":-301.0,
    内容："content":"{\"msgType\":1.0,\"msgId\":\"7F9E5062-2296-4988-AD81-9C58631CB6D1\",\"content\":\"669966\"}",
    时间："sendTime":1.587436654785E12}*/
    /* {
    msgType 对应以下类型：\"msgType\":1.0,
    实际消息id：\"msgId\":\"7F9E5062-2296-4988-AD81-9C58631CB6D1\",
    实际内容：\"content\":\"669966\"}*/

    //文本消息
    const val TEXT = 1

    //图片消息
    const val IMAGE = 2

    //语言消息
    const val VOICE = 3

    //位置消息
    const val LOCATION = 4

    //消息撤回
    const val MSGRETURN = 9

    //消息回执
    const val MSGREAD = 10

    //邀请加入团队
    const val ADDORG = 11

    //分享工作汇报
    const val SHARE_REPORT = 12

    fun getAllNormalTypes(): HashSet<Int> {
        return hashSetOf(TEXT, IMAGE, VOICE, LOCATION, ADDORG, SHARE_REPORT,
                CALL_START, CALL_FINISH, CALL_UN_RECEIVE,// 通话相关消息类型
                AT_GROUP, AT_GROUP_ALL)
    }

    /**通话发起，单聊时为发起人时，自身不收到，仅接听者收到；群组时所有人收到*/
    const val CALL_START = 13//SSChatMessageTypeInitiateVoiceVideo= 11  //发起音视频通话

    /**通话结束单聊和群组*/
    const val CALL_FINISH = 14//SSChatMessageTypeVoiceVideoEnd= 12   // 音视频通话结束

    /**通话未接听，仅单聊时发送*/
    const val CALL_UN_RECEIVE = 15//SSChatMessageTypeVoiceVideoCallFailure= 13, // 音视频通话失败 未接听

    /**通话拒绝接听 content 1 我在通话中 2 我拒接了通话*/
    const val CALL_NO_RECEIVE = 16// SSChatMessageTypeVoiceVideoRefused= 14, // callContent=1已在通话会议中拒绝，2接收者主动挂断--指令=不计离线

    /**二次邀请 会话中加人，pushMsg为二次邀请的人*/
    const val CALL_GROUP_INVITE_AGAIN = 17// SSChatMessageTypeVoiceVideoTwoInitiate= 15, // 群组2次邀请通话人员--指令=不计离线

    /**云文档分享内容*/
    const val CLOUD_DOC_SHARE = 18

    //群聊@消息  最多10个@符号
    const val AT_GROUP = 307

    //群聊@全部消息
    const val AT_GROUP_ALL = 308

    private fun getCallTypes(): HashSet<String> {
        val types = hashSetOf<String>()
//        types.add(MSGRETURN.toString())
        types.add(CALL_START.toString())
        types.add(CALL_GROUP_INVITE_AGAIN.toString())
        types.add(CALL_FINISH.toString())
        return types
    }

    fun isNoticeMsg(type: Int): Boolean {
        val groupGreyMsgSet = hashSetOf<String>()
        groupGreyMsgSet.addAll(GroupMessageType.getAllTypes())
        groupGreyMsgSet.addAll(getCallTypes())
        return type.toString() in groupGreyMsgSet
    }
}

//群组消息类型
object GroupMessageType {
    //（群组消息下的小type）
    //    -302
    //    -308
    //群组名称修改
    const val GROUP_NAME_CHANGE = "-302"

    //创建群组发给其他成员邀请信息和xx邀请你加入群组通知
    const val GROUP_INVITE = "-303"

    //群组创建者修改
    const val GROUP_CREATOR_CHANGE = "-304"

    //您被群组创建者移出此群组
    const val GROUP_REMOVE_MEMBER = "-305"

    //团队群组已解散
    const val DISBAND_GROUP = "-306"

    //团队群组的加入
    const val GROUP_ORG_ADD = "-307"

    //团队群组新加入的成员打招呼
    const val GROUP_ORG_ADD_NEW_HELLO = "-308"

    fun getAllTypes(): HashSet<String> {
        return hashSetOf(GROUP_NAME_CHANGE, GROUP_INVITE, GROUP_CREATOR_CHANGE,
                GROUP_REMOVE_MEMBER, DISBAND_GROUP, GROUP_ORG_ADD, GROUP_ORG_ADD_NEW_HELLO)
    }
}

//工作消息类型
object WORK_MESSAGE_TYPE {

    /**团队公告提醒 new */
    const val ORG_NOTICE = "公告提醒"

    //团队公告
    const val ORG_BULLE = "团队公告"

    //团队加入申请提醒
    const val ORG_JOIN_OLD = "组织加入申请提醒"

    /**团队加入申请提醒*/
    const val ORG_JOIN = "团队加入申请提醒"

    /**团队加入申请提醒 new*/
    const val ORG_JOIN_NOTICE = "团队加入申请"

    //权限变更
    const val PERMISSION_CHANGE = "权限变更"

    //考勤提醒
    const val ATTENDANCE_REMIND = "考勤提醒"

    //考勤规则变更
    const val ATTENDANCE_RULE_CHANGE = "考勤规则变更"

    //特殊工作日调整
    const val SPECIAL_DATE_CHANGE = "特殊工作日调整"

    //缺卡提醒
    const val MISS_ATTENDANCE_REMIND = "缺卡提醒"

    //项目通知
    const val PROJECT_NOTICE = "项目通知"

    //任务通知
    const val TASK_NOTICE = "任务通知"

    /**审批提醒*/
    const val APPROVAL_NOTICE = "审批提醒"

    /**审批 单独消息列表*/
    const val APPROVAL_MSG = "审批"

    /**评论提醒*/
    const val COMMENT_REMIND = "评论提醒"

    /**任务评论提醒*/
    const val COMMENT_REMIND_TASK = "M0"

    /**审批评论提醒*/
    const val COMMENT_REMIND_APR = "M1"

    /**企业数字报告*/
    const val COMPANY_DIGITAL_REPORT_NOTICE = "企业数字报告"

    /**音/视频会议*/
    const val VC_NOTICE = "音/视频会议"

    /**工作汇报*/
    const val WORK_REPORT = "工作汇报"

    /**协作消息*/
    const val WORK_COOPER = "协作消息"
}

object ORG_PERMISS_TYPE {
    //超级管理权限
    const val SUPER_PERMISSION = "-1"

    //团队管理权限
    const val ORG_PERMISSION = '1'

    //考勤管理权限
    const val ATTENDANCE_PERMISSION = '2'

    //任务管理权限
    const val TASK_PERMISSION = '3'

    //审批管理权限
    const val APPROVAL_PERMISSION = '4'

    //日报管理权限
    const val REPORT_PERMISSION = '5'

    //数字报告预览
    const val DIGITAL_REPORT = '6'

    //全部审批权限
    const val TOTAL_APPROVAL = '7'

    //健康上报统计权限
    const val HEALTH_STATISTICS = '8'

    //云文档管理权限
    const val CLOUD_MANAGE = '9'

    //访客管理权限
    const val VISITOR_MANAGE = 'a'//访客系统，代表字符，这个字符有两个位置，另一个位置在添加权限选项时了


//    fun getMyPower(deptId: String, power: String): String {
//        return if ("0" == deptId || SUPER_PERMISSION == power || power.contains(SUPER_PERMISSION)) {
//            "$ORG_PERMISSION" + "$ATTENDANCE_PERMISSION" + "$TASK_PERMISSION" +
//                    "$APPROVAL_PERMISSION" + "$REPORT_PERMISSION" + "$DIGITAL_REPORT"
//        } else {
//            power
//        }
//    }
//
//    fun checkPower(power: String, isCreate: Boolean = false): String {
//        return if (isCreate || SUPER_PERMISSION == power || power.contains(SUPER_PERMISSION)) {
//            "$ORG_PERMISSION" + "$ATTENDANCE_PERMISSION" + "$TASK_PERMISSION" +
//                    "$APPROVAL_PERMISSION" + "$REPORT_PERMISSION" + "$DIGITAL_REPORT"
//        } else {
//            power
//        }
//    }

    fun checkSuperPermission(power: String?): Boolean {
        return !power.isNullOrBlank() && power.contains(SUPER_PERMISSION)
    }

    fun checkInvitePermission(power: String?): Boolean {
        return !power.isNullOrBlank() && (power.contains(SUPER_PERMISSION) || power.contains(ORG_PERMISSION))
    }
}

//系统消息类型
object SystemMessageType {
    /**设为创始人标记*/
    const val GET_ORG = "的创始人"

    /**您已加入团队*/
    const val JOINED_ORG = "您已加入团队"
    const val JOINED_ORG_OLD = "您已加入组织"

    /**您已被请离团队*/
    const val LEAVE_ORG = "您已被请离团队"
    const val LEAVE_ORG_OLD = "您已被请离组织"

    /**您已退出团队*/
    const val YOUR_QUIT_ORG = "您已退出团队"
    const val YOUR_QUIT_ORG_OLD = "您已退出组织"

    /**已退出团队*/
    const val QUIT_ORG = "已退出团队"
    const val QUIT_ORG_OLD = "已退出组织"

    /**已解散*/
    const val DISSOLVED_ORG = "已解散"

    /**请求添加您为好友*/
    const val REQUEST_ADD = "请求添加您为好友"

    /**部门负责人*/
    const val DEPART_HEADER = "部门负责人"

    /**部门副职负责人*/
    const val DEPART_VICE_HEADER = "部门副职负责人"

    /**已被移动到*/
    const val HAVE_MOVED_TO = "已被移动到"

    /**您已被设为部门负责人等*/
    const val HAVE_SETTED = "您已被设为"

    /**您已被撤销部门负责人等*/
    const val HAVE_REVOKED = "您已被撤销"

    /**您已被移动到其他部门等*/
    const val YOUR_HAVE_MOVED_TO = "您已被移动到"

}

// 公司消息免打扰
const val ORG_NO_DISTURB = "company_msg_no_disturb"

// 群组消息免打扰
const val GROUP_NO_DISTURB = "group_msg_no_disturb"

/**
 * app_id是从微信官网申请到的合法APPid
 */
val APP_ID_WX = "wxf55e2c3e1dcd35fc"

/**
 * 微信AppSecret值
 */
val APP_SECRET_WX = "5e94ec8b5939cbad04fae4139b9bdf1d"

//裁剪照片路径
const val CROP_PATH_NEW = "/ddbes/crop/image/"
const val IMAGE_STORAGE_DIR = "/ddbes/image/"

//下载目录
var DOWNLOAD_DIR: String = ""

//融云位置发起
const val LOCATION_RONG = 0x000002

// 图片裁剪
const val IMAGE_CUT_CODE = 0x000003
const val VIDEO_CUT_CODE = 0x000033
const val IMAGE_CUT_ONE_CODE = 0x000004
const val SCAN_ADDFRIEND = 0x000009
const val IMMSGDETAIL = 0x000008
const val REMARKNAME = 0x000007
const val IMAGE_WEB_BASE_URL = "http://ddbes.cdn.joinutech.com/"

/**推送通知上次检查时间*/
const val MSG_PUSH_CHECK_LAST_TIME = "msg_push_check_last_time"

//通知声音震动设置
/**通知设置改变*/
const val MSG_NOTIFY_CHANGE = "msg_notify_change"

/**消息列表信息发生变化更新，需要刷新列表 notifyDataSetChanged*/
const val MSG_LIST_INFO_CHANGE = "msg_list_info_change"

const val MSG_NOTIFICATION_ACTION = "msg_notification_action"
const val MSG_NOTIFICATION_SOUND = "msg_notification_sound"
const val MSG_NOTIFICATION_VIBRATES = "msg_notification_vibrates"

const val CHANGE_MEMBER_DEP = 0x000010
const val SET_DEP_HEAD = 0x000011
const val SET_ABOVE_DEP_NAME = 0x000012
const val UPDATECOMPANYLOGO = 0x000013
const val ATTENDANCE_LOCATION = 0x000014
const val ATTENDANCE__DATE = 0x000015
const val ATTENDANCE_CLOCK = 0x000016
const val ATTENDANCE_SPECIAL_DATE = 0x000017
const val ATTENDANCE_WIFI = 0x000018
const val ATTENDANCE_PERSON = 0x000019

/**跳转选择考勤关联部门*/
const val ATTENDANCE_DEP = 0x000020

const val ATTENDANCE_COMPLETE = 0x000021
const val ORGPERMISSION_CHANGE = 0x000022
const val UPDATE_TASK = 0x000023
const val TASK_STATUS_SELECT = 0x000024
const val CREATE_TASK_WATCHMORE = 0x000025
const val SELECTED_TASK_ENDTIME = 0x000026
const val SELECTED_TASK_MANAGER = 0x000027
const val SELECTED_TASK_JOINER = 0x000028
const val DEL_RECYCLE_PROJECT = 0x000029
const val SELECT_TASK_PROJECT = 0x000030
const val PROJECT_HAND_OVER = 0x000031
const val SELECT_TASK_LABEL = 0x000032
const val BATCH_PROCESS_APPLICATION = 0x000033
const val COMPANY_INTRO_CHANGE = 0x000034
const val ADD_EXTERNAL_CONTACT = 0x000035
const val ADD_EXTERNAL_CONTACT_FROM_PHONE = 0x000036
const val ADD_EXTERNAL_CONTACT_FROM_FRIEND_LIST = 0x000037
const val SEARCH_APPROVAL_PERSON_LIST = 0x000038
const val TASK_CHILD_STATUS_CHANGE = 0x000039
const val PROJECT_NOTICE_EDIT = 0x000040
const val TASK_TYPE_CHANGE = 0x000041
const val SERVER_MSG_VERSION = "v2"
const val ADD_EXTERNAL_CONTACT_FRIEND = 0x000042
const val PLAN_JOIN_MEMBER = 0x000043
const val CREATE_GROUP = 0x000044
const val TRANSLATE_GROUP_CREATE_PERSON = 0x000045
const val WATCH_ALL_MEMBER = 0x000046
const val REMOVE_GROUP_MEMBER = 0x000047
const val ADD_REPORT_RULE = 0x000048
const val REPORT_SORT = 0x000049
const val SHARE_WORK_REPORT = 0x000050
const val COMMENT_WORK_REPORT = 0x000051
const val AT_SELECT_PERSON = 0x000052
const val IM_VC_SELECT_PERSON = 0x000053
const val UPDATE_GROUP_SETTING = 0x000054

/**请求通知设置页面*/
const val REQUEST_PERSON_NOTIFY_CONFIG = 0x000054


/**天地图相关*/
const val REQUEST_GET_LOCATION = 0x000060


//公司相关
const val CURRENTCOMPANY = "currentCompany"

/**所有团队信息*/
const val COMPANIES = "all_companies"

/**协作团队*/
const val COOPERATION_ORG_ALL = "all_cooperation"

//shortCutId
const val shortCutId = "shortCutId"

/**任务图片预览*/
const val IMAGE_PREVIEW = "/ddbesLib/image_preview"

/**时间格式化模型*/
const val DEFAULT_TIME_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss"
const val TIME_FORMAT_PATTERN1 = "yyyy-MM-dd HH:mm"
const val TIME_FORMAT_PATTERN2 = "yyyy/MM/dd HH:mm"
const val TIME_FORMAT_PATTERN3 = "HH:mm:ss"
const val TIME_FORMAT_PATTERN4 = "yyyy-MM-dd"
const val TIME_FORMAT_PATTERN5 = "yyyy-MM"
const val TIME_FORMAT_PATTERN6 = "MM/dd HH:mm"
const val TIME_FORMAT_PATTERN7 = "HH:mm"
const val TIME_FORMAT_PATTERN8 = "yyyy年MM月dd日 HH:mm"
const val TIME_FORMAT_PATTERN9 = "MM-dd HH:mm"
const val TIME_FORMAT_PATTERN10 = "yyyy年MM月dd日"
//const val TIME_FORMAT_PATTERN9 = "MM-dd HH:mm:ss"

/**属性标题宽度定义*/
const val PROPERTY_TITLE_MODEL = "担当办公设置()"
const val PROPERTY_DETAIL_TITLE_MODEL = "担当办公"
