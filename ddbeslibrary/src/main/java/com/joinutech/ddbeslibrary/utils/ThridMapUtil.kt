package com.joinutech.ddbeslibrary.utils

import Coordinate
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import android.widget.Toast
//import com.baidu.mapapi.model.LatLng
import java.net.URISyntaxException

/**
 * Description 第三方地图工具类
 * Author HJR36
 * Date 2018/7/19 16:25
 */
object ThridMapUtil {

    fun isAvilible(context: Context, packageName: String): Boolean {
        return CommonUtils.checkAppInstallState(context, packageName)
    }

    /**
     * BD-09 坐标转换成 GCJ-02 坐标
     */
    fun BD2GCJ(bd: Coordinate): Coordinate {
        val x = bd.lon - 0.0065
        val y = bd.lat - 0.006
        val z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * Math.PI)
        val theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * Math.PI)

        val lng = z * Math.cos(theta)//lng
        val lat = z * Math.sin(theta)//lat
        return Coordinate(lng,lat)
    }

    fun startBaiduMap(context: Context, mLatitude: Double, mLongitude: Double, mAreaName: String) {
        if (isAvilible(context, "com.baidu.BaiduMap")) {// 传入指定应用包名
            try {
                val intent = Intent.getIntent("intent://map/direction?destination=latlng:"
                        + mLatitude + ","
                        + mLongitude + "|name:" + mAreaName + // 终点

                        "&mode=driving&" + // 导航路线方式

                        "region=北京" + //

                        "&src=加优科技|担当#Intent;scheme=bdapp;package=com.baidu.BaiduMap;end")
                context.startActivity(intent) // 启动调用
            } catch (e: URISyntaxException) {
                Log.e("intent", e.message ?: "")
            }

        } else {// 未安装
            Toast.makeText(context, "您尚未安装百度地图", Toast.LENGTH_LONG)
                    .show()
            val uri = Uri
                    .parse("market://details?id=com.baidu.BaiduMap")
            val intent = Intent(Intent.ACTION_VIEW, uri)
            context.startActivity(intent)
        }
    }

    fun startAmap(context: Context, mLatitude: Double, mLongitude: Double, mAreaName: String) {
        if (isAvilible(context, "com.autonavi.minimap")) {
            // 执行转换操作
////            val desLatLng = BD2GCJ(Coordinate(mLongitude,mLatitude ))//坐标转换
//            val mLatitudeNew = desLatLng.lat
//            val mLongitudeNew = desLatLng.lon
            try {
                val intent = Intent().apply {
                    action = "android.intent.action.VIEW"
                    val url = "amapuri://route/plan/?dlat=${mLatitude}&dlon=${mLongitude}&dev=0&t=0"
                    data = Uri.parse(url)
                }
                context.startActivity(intent)
            } catch (e: URISyntaxException) {
                e.printStackTrace()
            }

        } else {
            Toast.makeText(context, "您尚未安装高德地图", Toast.LENGTH_LONG)
                    .show()
            val uri = Uri
                    .parse("market://details?id=com.autonavi.minimap")
            val intent = Intent(Intent.ACTION_VIEW, uri)
            context.startActivity(intent)

        }
    }

    /**
     * 跳转腾讯地图
     */
    fun goToTencentMap(context: Context, mLatitude: Double, mLongitude: Double, mAreaName: String) {
        if (!isAvilible(context, "com.tencent.map")) {
            ToastUtil.show(context, "请先安装腾讯地图客户端")
            val uri = Uri
                    .parse("market://details?id=com.tencent.map")
            val intent = Intent(Intent.ACTION_VIEW, uri)
            context.startActivity(intent)
            return
        }
//        val endPoint = BD2GCJ(Coordinate(mLongitude,mLatitude,))//坐标转换
        val stringBuffer = StringBuffer("qqmap://map/routeplan?type=drive")
                .append("&tocoord=").append(mLatitude).append(",")
                .append(mLongitude).append("&to=$mAreaName")
        val intent = Intent("android.intent.action.VIEW", Uri.parse(stringBuffer.toString()))
        context.startActivity(intent)
    }

}