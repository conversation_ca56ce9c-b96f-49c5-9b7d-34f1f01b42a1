package com.joinutech.ddbeslibrary.utils

import android.app.Activity
import android.app.Service
import android.content.Context
import android.graphics.BitmapFactory
import android.os.Build
import android.provider.Settings
import android.util.DisplayMetrics
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import timber.log.Timber

/**
 * 获取屏幕宽高等信息、全屏切换、保持屏幕常亮、截屏等
 */
object ScreenUtilsKt {
    var isFullScreen = false
        private set
    private val dm: DisplayMetrics? = null
    private fun displayMetrics(context: Context): DisplayMetrics? {
        if (null != dm) {
            return dm
        }
        val dm = DisplayMetrics()
        val windowManager = (context
            .getSystemService(Context.WINDOW_SERVICE) as WindowManager)
        windowManager.defaultDisplay.getMetrics(dm)

        return dm
    }

    private fun disRealplayMetrics(context: Context): DisplayMetrics {
        val dm = DisplayMetrics()
        val windowManager = (context
            .getSystemService(Context.WINDOW_SERVICE) as WindowManager)
        windowManager.defaultDisplay.getRealMetrics(dm)
        return dm
    }

    fun widthPixels(context: Context): Int {
        return displayMetrics(context)!!.widthPixels
    }

    fun widthRealPixels(context: Context): Int {
        return disRealplayMetrics(context).widthPixels
    }

    fun heightRealPixels(context: Context): Int {
        return disRealplayMetrics(context).heightPixels
    }

    // 虚拟导航栏是否存在
    fun isNavigationBarShow(context: Context): Boolean {
        val windowManager = context
            .getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val defaultDisplay = windowManager.defaultDisplay
        //获取屏幕高度
        val outMetrics = DisplayMetrics()
        defaultDisplay.getRealMetrics(outMetrics)
        val heightPixels = outMetrics.heightPixels
        //宽度
        val widthPixels = outMetrics.widthPixels

        //获取内容高度
        val outMetrics2 = DisplayMetrics()
        defaultDisplay.getMetrics(outMetrics2)
        val heightPixels2 = outMetrics2.heightPixels
        //宽度
        val widthPixels2 = outMetrics2.widthPixels
        val has = heightPixels - heightPixels2 > 0 || widthPixels - widthPixels2 > 0
        Timber.i("有虚拟导航栏么 = $has")
        return has
    }

    fun heightPixels(context: Context): Int {
        return displayMetrics(context)!!.heightPixels
    }

    fun density(context: Context): Float {
        return displayMetrics(context)!!.density
    }

    fun densityDpi(context: Context): Int {
        return displayMetrics(context)!!.densityDpi
    }

    fun toggleFullScreen(activity: Activity) {
        val window = activity.window
        val flagFullscreen = WindowManager.LayoutParams.FLAG_FULLSCREEN
        if (isFullScreen) {
            window.clearFlags(flagFullscreen)
            isFullScreen = false
        } else {
            window.setFlags(flagFullscreen, flagFullscreen)
            isFullScreen = true
        }
    }

    /**
     * 保持屏幕常亮
     */
    fun keepBright(activity: Activity) {
        //需在setContentView前调用
        val keepScreenOn = WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
        activity.window.setFlags(keepScreenOn, keepScreenOn)
    }

    /**
     * 获取当前应用的亮度值
     * @param activity Activity实例
     * @return 亮度值，范围0.0-1.0，-1表示使用系统亮度
     */
    fun getAppBrightness(activity: Activity): Float {
        val layoutParams = activity.window.attributes
        return layoutParams.screenBrightness
    }

    /**
     * 设置当前应用的亮度
     * @param activity Activity实例
     * @param brightness 亮度值，范围0.0-1.0，-1表示使用系统亮度
     */
    fun setAppBrightness(activity: Activity, brightness: Float) {
        val layoutParams = activity.window.attributes
        // 确保亮度值在有效范围内
        layoutParams.screenBrightness = when {
            brightness < 0f -> -1f // 使用系统亮度
            brightness > 1f -> 1f  // 最大亮度
            else -> brightness
        }
        activity.window.attributes = layoutParams
    }

    /**
     * @Title: dip2px
     * @Description: TODO(根据手机的分辨率从 dp 的单位 转成为 px ( 像素))
     * @param: @param context
     * @param: @param dpValue
     * @param: @return 设定文件
     * @return: int 返回类型
     * @date: 2013-8-19 下午2:38:08
     */
    fun dip2px(context: Context, dpValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (dpValue * scale + 0.5f).toInt()
    }


    /**
     * 华为手机是否隐藏了虚拟导航栏
     * @return true 表示隐藏了，false 表示未隐藏
     */
    fun isHuaWeiHideNav(context: Context) =
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            Settings.System.getInt(context.contentResolver, "navigationbar_is_min", 0)
        } else {
            Settings.Global.getInt(context.contentResolver, "navigationbar_is_min", 0)
        } != 0

    /**
     * 小米手机是否开启手势操作
     * @return false 表示使用的是虚拟导航键(NavigationBar)， true 表示使用的是手势， 默认是false
     */
    fun isMiuiFullScreen(context: Context) =
        Settings.Global.getInt(context.contentResolver, "force_fsg_nav_bar", 0) != 0

    /**
     * Vivo手机是否开启手势操作
     * @return false 表示使用的是虚拟导航键(NavigationBar)， true 表示使用的是手势， 默认是false
     */
    fun isVivoFullScreen(context: Context): Boolean =
        Settings.Secure.getInt(context.contentResolver, "navigation_gesture_on", 0) != 0

    /**
     * 其他手机根据屏幕真实高度与显示高度是否相同来判断
     */
    fun isHasNavigationBar(context: Context): Boolean {
        val windowManager: WindowManager =
            context.getSystemService(Service.WINDOW_SERVICE) as WindowManager
        val d = windowManager.defaultDisplay

        val realDisplayMetrics = DisplayMetrics()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            d.getRealMetrics(realDisplayMetrics)
        }
        val realHeight = realDisplayMetrics.heightPixels
        val realWidth = realDisplayMetrics.widthPixels

        val displayMetrics = DisplayMetrics()
        d.getMetrics(displayMetrics)
        val displayHeight = displayMetrics.heightPixels
        val displayWidth = displayMetrics.widthPixels

        // 部分无良厂商的手势操作，显示高度 + 导航栏高度，竟然大于物理高度，对于这种情况，直接默认未启用导航栏
        if (displayHeight + getNavigationBarHeight(context) > realHeight) return false

        return realWidth - displayWidth > 0 || realHeight - displayHeight > 0
    }



    /**
     * 根据手机的分辨率PX(像素)转成DP
     *
     * @param context
     * @param pxValue
     * @return
     */
    fun px2dip(context: Context, pxValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (pxValue / scale + 0.5f).toInt()
    }

    /**
     * 获取状态栏高度
     *
     * @param context
     * @return
     */
    fun getStatusBarHeight(context: Context): Int {
        var result = 0
        val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = context.resources.getDimensionPixelSize(resourceId)
        }
        return result
    }

    fun getStatusBarHeight2(context: Context): Float {
        var result = 0f
        val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = context.resources.getDimension(resourceId)
        }
        return result
    }
    //    public static int getStatusBarHeight2(Context context) {
    //        try {
    //            Class<?> c = Class.forName("com.android.internal.R$dimen");
    //            Object obj = c.newInstance();
    //            Field field = c.getField("status_bar_height");
    //            int x = Integer.parseInt(field.get(obj).toString());
    //            return context.getResources().getDimensionPixelSize(x);
    //        } catch (Exception e) {
    //            e.printStackTrace();
    //        }
    //        return 0;
    //    }
    /**
     * 获取导航栏高度
     *
     * @param context 当前上下文
     * @return 状态栏高度
     */
    fun getNavigationBarHeight(context: Context): Int {
        val resources = context.resources
        val resourceId =
            resources.getIdentifier("navigation_bar_height", "dimen", "android")
        //获取NavigationBar的高度
        return resources.getDimensionPixelSize(resourceId)
    }

    /**
     * 获取图片宽高
     *
     * @param path
     * @return
     */
    fun getImageWidthHeight(path: String?): IntArray {
        val options = BitmapFactory.Options()
        /**
         * 最关键在此，把options.inJustDecodeBounds = true;
         * 这里再decodeFile()，返回的bitmap为空，但此时调用options.outHeight时，已经包含了图片的高了
         */
        options.inJustDecodeBounds = true
        BitmapFactory.decodeFile(path, options) // 此时返回的bitmap为null
        /**
         * options.outHeight为原始图片的高
         */
        return intArrayOf(options.outWidth, options.outHeight)
    }

    /**
     * 隐藏软键盘(只适用于Activity，不适用于Fragment)
     */
    fun hideSoftKeyboard(activity: Activity) {
        val view = activity.currentFocus
        if (view != null && view.windowToken != null) {
            val inputMethodManager =
                activity.getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
            inputMethodManager.hideSoftInputFromWindow(
                view.windowToken,
                InputMethodManager.HIDE_NOT_ALWAYS
            )
        }
    }

    /**
     * 隐藏软键盘(可用于Activity，Fragment)
     * viewList 中需要放的是当前界面所有触发软键盘弹出的控件。
     * 比如一个登陆界面， 有一个账号输入框和一个密码输入框， 需要隐藏键盘的时候，
     * 就将两个输入框对象放在 viewList 中， 作为参数传到 hideSoftKeyboard 方法中即可
     */
    fun hideSoftKeyboard(context: Context, viewList: List<View>?) {
        if (viewList == null) return
        val inputMethodManager =
            context.getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
        for (v in viewList) {
            inputMethodManager.hideSoftInputFromWindow(
                v.windowToken,
                InputMethodManager.HIDE_NOT_ALWAYS
            )
        }
    }

    //显示软键盘
    fun showSoftKeyboard(context: Activity, editText: EditText) {
        editText.requestFocus()
        val inputMethodManager =
            context.getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
        inputMethodManager.showSoftInput(editText, 0)
    }
}