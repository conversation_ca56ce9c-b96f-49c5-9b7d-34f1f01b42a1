package com.joinutech.ddbeslibrary.utils

import android.app.Activity
import android.content.Context
import android.provider.Settings
import timber.log.Timber

/**
 * 亮度控制辅助类
 * 提供应用亮度和系统亮度的控制方法
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-29
 */
object BrightnessHelper {

    /**
     * 获取当前应用的亮度值
     * @param activity Activity实例
     * @return 亮度值，范围0.0-1.0，-1表示使用系统亮度
     */
    fun getAppBrightness(activity: Activity): Float {
        return ScreenUtilsKt.getAppBrightness(activity)
    }

    /**
     * 设置当前应用的亮度
     * @param activity Activity实例
     * @param brightness 亮度值，范围0.0-1.0，-1表示使用系统亮度
     */
    fun setAppBrightness(activity: Activity, brightness: Float) {
        ScreenUtilsKt.setAppBrightness(activity, brightness)
        Timber.d("设置应用亮度: $brightness")
    }

    /**
     * 获取系统亮度值
     * @param context Context实例
     * @return 系统亮度值，范围0-255
     */
    fun getSystemBrightness(context: Context): Int {
        return try {
            Settings.System.getInt(
                context.contentResolver,
                Settings.System.SCREEN_BRIGHTNESS
            )
        } catch (e: Settings.SettingNotFoundException) {
            Timber.e(e, "获取系统亮度失败")
            125 // 默认亮度值
        }
    }

    /**
     * 获取系统亮度值（0.0-1.0范围）
     * @param context Context实例
     * @return 系统亮度值，范围0.0-1.0
     */
    fun getSystemBrightnessFloat(context: Context): Float {
        val brightness = getSystemBrightness(context)
        return brightness / 255f
    }

    /**
     * 检查是否开启了自动亮度
     * @param context Context实例
     * @return true表示开启了自动亮度
     */
    fun isAutoBrightnessEnabled(context: Context): Boolean {
        return try {
            val mode = Settings.System.getInt(
                context.contentResolver,
                Settings.System.SCREEN_BRIGHTNESS_MODE
            )
            mode == Settings.System.SCREEN_BRIGHTNESS_MODE_AUTOMATIC
        } catch (e: Settings.SettingNotFoundException) {
            Timber.e(e, "检查自动亮度状态失败")
            false
        }
    }

    /**
     * 恢复应用亮度为系统亮度
     * @param activity Activity实例
     */
    fun restoreSystemBrightness(activity: Activity) {
        setAppBrightness(activity, -1f)
        Timber.d("恢复应用亮度为系统亮度")
    }

    /**
     * 设置应用为最大亮度
     * @param activity Activity实例
     */
    fun setMaxBrightness(activity: Activity) {
        setAppBrightness(activity, 1.0f)
        Timber.d("设置应用为最大亮度")
    }

    /**
     * 设置应用为最小亮度
     * @param activity Activity实例
     */
    fun setMinBrightness(activity: Activity) {
        setAppBrightness(activity, 0.01f) // 设置为接近0但不为0的值，避免屏幕完全黑暗
        Timber.d("设置应用为最小亮度")
    }

    /**
     * 根据百分比设置亮度
     * @param activity Activity实例
     * @param percentage 亮度百分比，范围0-100
     */
    fun setBrightnessByPercentage(activity: Activity, percentage: Int) {
        val brightness = (percentage.coerceIn(0, 100) / 100f)
        setAppBrightness(activity, brightness)
        Timber.d("设置应用亮度百分比: $percentage%")
    }

    /**
     * 获取当前应用亮度的百分比
     * @param activity Activity实例
     * @return 亮度百分比，范围0-100，-1表示使用系统亮度
     */
    fun getAppBrightnessPercentage(activity: Activity): Int {
        val brightness = getAppBrightness(activity)
        return if (brightness < 0) {
            -1 // 使用系统亮度
        } else {
            (brightness * 100).toInt()
        }
    }

    /**
     * 平滑调整亮度（带动画效果）
     * @param activity Activity实例
     * @param targetBrightness 目标亮度值，范围0.0-1.0
     * @param duration 动画持续时间（毫秒）
     * @param onComplete 完成回调
     */
    fun smoothSetBrightness(
        activity: Activity,
        targetBrightness: Float,
        duration: Long = 500,
        onComplete: (() -> Unit)? = null
    ) {
        val currentBrightness = getAppBrightness(activity)
        val startBrightness = if (currentBrightness < 0) {
            getSystemBrightnessFloat(activity)
        } else {
            currentBrightness
        }

        val steps = 20
        val stepDuration = duration / steps
        val stepSize = (targetBrightness - startBrightness) / steps

        var currentStep = 0
        val handler = android.os.Handler(android.os.Looper.getMainLooper())

        val runnable = object : Runnable {
            override fun run() {
                if (currentStep <= steps) {
                    val brightness = startBrightness + (stepSize * currentStep)
                    setAppBrightness(activity, brightness)
                    currentStep++
                    handler.postDelayed(this, stepDuration)
                } else {
                    onComplete?.invoke()
                }
            }
        }
        handler.post(runnable)
    }
}
