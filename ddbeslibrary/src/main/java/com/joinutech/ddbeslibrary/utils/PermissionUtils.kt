package com.joinutech.ddbeslibrary.utils

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager.PERMISSION_GRANTED
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.NonNull
import androidx.fragment.app.Fragment
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.permissions.RxPermissions
import timber.log.Timber

/**
 * Description 权限工具类PermissionUtils
 * Author HJR36
 * Date 2018/6/23 17:10
 */
object PermissionUtils {

    private const val SETTINGS_REQ_CODE = 16061

    /**
     * activity中请求权限
     * @param activity 当前activity
     * @param perms 权限数组
     * @param onSuccess 成功回调
     * @param hint 权限名称
     * @param onError 失败回调
     */
    @SuppressLint("CheckResult")
    fun requestPermissionActivity(activity: Activity, perms: Array<String>, hint: String, onSuccess: () -> Unit,
                                  onError: (Int) -> Unit, selfControl: Boolean = true , preTips : String = "" , cancelDialog: ((String) -> Unit)? = null) {

        if (checkPermissions(activity, perms)) {
            onSuccess.invoke()
        } else {
            preTipPermissionDialog1(activity , preTips , hint, onAgree = {

                val rxPermissions = RxPermissions(activity) // where this is an Activity
                rxPermissions.requestEachCombined(*perms)
                    .subscribe { permission ->
                        run {
                            when {
                                permission.granted ->{// 用户已经同意该权限 !
                                    onSuccess.invoke()
                                }
                                //弹框提示没有选中[不再询问]
                                permission.shouldShowRequestPermissionRationale -> {
                                    // At least one denied permission without ask never again
                                    // 用户拒绝了该权限，没有选中『不再询问』
                                    // （Never ask again）,那么下次再次启动时，还会提示请求权限的对话框
                                    onError.invoke(0)
                                }
                                else -> {
                                    //说明：selfControl如果为false,代表当用户拒绝过并且不在询问时，直接打开设置界面
                                    //selfControl为true时，不打开设置界面，由用户自己处理，这里只调用一下onError；
                                    if (selfControl) {
                                        onError.invoke(1)
                                    } else {
                                        // At least one denied permission with ask never again
                                        // 用户拒绝了该权限，并且选中『不再询问』
                                        // Need to go to the settings
                                        ToastUtil.show(BaseApplication.joinuTechContext,
                                            "您已经拒绝${hint}权限，要使用需要去设置中开启")
                                        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                                        val uri = Uri.fromParts("package",
                                            activity.packageName, null)
                                        intent.data = uri
                                        startAppSettingsScreen(activity, intent)
                                    }
                                }
                            }
                        }
                    }
            } , onCancel = {
                cancelDialog?.invoke("")
            })
        }
    }

    /**
     * fragment中请求权限
     * @param fragment fragment
     * @param perms 权限数组
     * @param onSuccess 成功回调
     * @param hint 权限名称
     * @param onError 失败回调
     */
    @SuppressLint("CheckResult")
    fun requestPermissionFragment(fragment: Fragment, perms: Array<String>, hint: String, onSuccess: () -> Unit,
                                  onError: (Int) -> Unit, selfControl: Boolean = false , preTips: String = "" , ) {
        try {
            if (checkPermissions(fragment.requireActivity(), perms)) {
                onSuccess.invoke()
            } else {

                preTipPermissionDialog1(fragment.requireContext() , preTips , hint ,onAgree = {
                    if(fragment.isAdded){
                        val rxPermissions = RxPermissions(fragment.requireActivity()) // where this is an Fragment instance
                        rxPermissions.requestEachCombined(*perms)
                            .subscribe { permission ->
                                run {
                                    when {
                                        permission.granted -> // 用户已经同意该权限 !
                                            onSuccess.invoke()
                                        permission.shouldShowRequestPermissionRationale -> {
                                            // At least one denied permission without ask never again
                                            // 用户拒绝了该权限，没有选中『不再询问』（Never ask again）,
                                            // 那么下次再次启动时，还会提示请求权限的对话框
                                            onError.invoke(0)
                                        }
                                        else -> {
                                            if (selfControl) {
                                                onError.invoke(1)
                                            } else {
                                                // At least one denied permission with ask never again
                                                // 用户拒绝了该权限，并且选中『不再询问』
                                                // Need to go to the settings
                                                ToastUtil.show(
                                                    BaseApplication.joinuTechContext,
                                                    "您已经拒绝${hint}权限，继续使用需要去设置中开启")
                                                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                                                val uri = Uri.fromParts("package",
                                                    fragment.requireActivity().packageName, null)
                                                intent.data = uri
                                                startAppSettingsScreen(fragment.requireActivity(), intent)
                                            }
                                        }
                                    }
                                }
                            }
                    }

                } , onCancel = {

                })
            }
        } catch (e: Exception) {
            onError.invoke(0)
        }
    }

    private fun startAppSettingsScreen(page: Any, intent: Intent) {
        when (page) {
            is Activity -> page.startActivityForResult(intent, SETTINGS_REQ_CODE)
            is Fragment -> page.startActivityForResult(intent, SETTINGS_REQ_CODE)
        }
    }

    fun checkPermissions(context: Context, @NonNull permissions: Array<String>): Boolean {
        when {
            Build.VERSION.SDK_INT < Build.VERSION_CODES.M -> {
                return true
            }
            permissions.isNotEmpty() -> {
                val var3 = permissions.size
                for (var4 in 0 until var3) {
                    val permission = permissions[var4]
                    if (context.checkCallingOrSelfPermission(permission) != PERMISSION_GRANTED) {
                        return false
                    }
                }
                return true
            }
            else -> {
                return true
            }
        }
    }

    // 申请权限之前，需要注明此权限申请的目的
    fun preTipPermissionDialog(context: Context , message : String , toastTip: String, onAgree: () -> Unit) {
        val tips = if (StringUtils.isEmpty(message)) "您需要申请权限$toastTip" else message
        AlertDialog.Builder(context)
            .setMessage(tips).setPositiveButton("同意" ,
                DialogInterface.OnClickListener { dialog, which ->
                    dialog?.dismiss()
                    onAgree()
                }).setNegativeButton("拒绝") { _, _ -> }
            .setCancelable(true)
            .show()

    }


    fun preTipPermissionDialog1(context: Context ,  message : String , toastTip: String, onAgree: () -> Unit , onCancel:() -> Unit) {
        val tips = if (StringUtils.isEmpty(message)) "您需要申请权限$toastTip" else message
        val view = View.inflate(context, R.layout.dialog_pre_dialog_permission, null)

        val dialog = BottomDialogUtil.showBottomDialog(context, view, Gravity.CENTER)
        view.findViewById<TextView>(R.id.confirm).setOnClickListener {
            onAgree()
            BottomDialogUtil.isShowing = false
            dialog.dismiss()
        }

        view.findViewById<TextView>(R.id.later).setOnClickListener {
            onCancel()
            BottomDialogUtil.isShowing = false
            dialog.dismiss()
        }

        view.findViewById<TextView>(R.id.tv_version_code).text = tips
        //设置点击返回键不消失
        dialog.setCancelable(false)
        //设置点击屏幕不消失
        dialog.setCanceledOnTouchOutside(false)
        dialog.show()
        BottomDialogUtil.isShowing = true
    }

    fun checkNetTypePermission(context: Activity , callBack: () -> Unit ,cancel: ((String) -> Unit)? = null) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            context.runOnUiThread {
                Timber.i("SDK_INT 版本 大于等于 Android R （target 30）")
                val perms = arrayOf(
//                    Manifest.permission.ACCESS_NETWORK_STATE ,
                    Manifest.permission.READ_PHONE_STATE
                )
                val preTips = "拨打电话需要访问网络权限"
                requestPermissionActivity(context, perms, "网络权限", {
                    callBack()
                }, {
                    toast(context, "您已拒绝拨打电话权限请求")
                } , preTips = preTips , cancelDialog = {
                    cancel?.invoke("")
                })
            }
        }else{
            callBack()
        }
    }

}

val permissionSet = mapOf(
    "android.webkit.resource.AUDIO_CAPTURE" to Manifest.permission.RECORD_AUDIO,
    "android.webkit.resource.VIDEO_CAPTURE" to Manifest.permission.CAMERA,
)

fun convertJsPermission(ps: List<String>): List<String> {
    val r = arrayListOf<String>()
    ps.forEach {
        if (permissionSet.containsKey(it)){
            r.add(permissionSet.get(it) ?:"")
        }
    }
    return r
}