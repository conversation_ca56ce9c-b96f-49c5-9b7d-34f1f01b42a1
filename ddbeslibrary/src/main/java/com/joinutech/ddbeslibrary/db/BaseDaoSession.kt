package com.joinutech.ddbeslibrary.db

import android.util.Log
import com.joinu.db.gen.DaoSession
import com.joinutech.ddbeslibrary.db.helper.BaseMigrationHelper
import org.greenrobot.greendao.AbstractDao
import org.greenrobot.greendao.database.Database
import org.greenrobot.greendao.identityscope.IdentityScopeType
import org.greenrobot.greendao.internal.DaoConfig

/**
 * @Description: TODO
 * @Author: zhaoyy
 * @Time: 2021/4/23 13:23
 * @packageName: com.joinutech.ddbeslibrary.db
 * @Company: Copyright (C),2019- 2021,JoinuTech
 */
class BaseDaoSession(db: Database, val type: IdentityScopeType,
                     val daoConfigMap: Map<Class<out AbstractDao<*, *>?>?, DaoConfig?>
//                         private val daoEntityMap: Map<Class<T>?, AbstractDao<T, *>?>

) : DaoSession(db, type, daoConfigMap) {
//    : AbstractDaoSession(db) {

    init {
//        Log.e("db_test", "DDbesDaoSession 初始化")
        BaseMigrationHelper.registerEntityDao(this, daoConfigMap, IdentityScopeType.Session)
//        val attendHistoryDataDaoConfig = daoConfigMap[AttendHistoryDataDao::class.java]!!.clone()
//        attendHistoryDataDaoConfig.initIdentityScope(type)
//
//        val requestLogDaoConfig = daoConfigMap[RequestLogDao::class.java]!!.clone()
//        requestLogDaoConfig.initIdentityScope(type)
//
//        val attendHistoryDataDao = AttendHistoryDataDao(attendHistoryDataDaoConfig, this)
//        val requestLogDao = RequestLogDao(requestLogDaoConfig, this)
//
//        registerDao(AttendHistoryData::class.java, attendHistoryDataDao)
//        registerDao(RequestLog::class.java, requestLogDao)

//        val daoEntityMap = BaseMigrationHelper.registerEntityDao(this, type)
//        if (!daoEntityMap.isNullOrEmpty()) {
//            val keys = daoEntityMap.keys
//            for (key in keys) {
//                registerDao(key, daoEntityMap[key])
//            }
//        }

    }

    public override fun <T : Any?> registerDao(entityClass: Class<T>?, dao: AbstractDao<T, *>) {
//        Log.e("db_test", "注册dao 进入entityDao 记录中 $entityClass -- ${dao.javaClass.name}")
        super.registerDao(entityClass, dao)
    }
}