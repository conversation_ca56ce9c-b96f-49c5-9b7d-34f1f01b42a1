package com.joinutech.ddbeslibrary.db.helper

/**
 * @Description: 数据库更新监听类
 * @Author: zhaoyy
 * @Time: 2021/4/23 11:14
 * @packageName: com.joinutech.ddbeslibrary.db.helper
 * @Company: Copyright (C),2019- 2021,JoinuTech
 */
//class BaseDbUpdateListener : BaseMigrationHelper.DbUpdateListener {
//
//    override fun getName(): String {
//        return "base"
//    }
//
//    private val configMap = hashMapOf<Class<out AbstractDao<*, *>>, DaoConfig>()
//
//    override fun registerDaoWithConfig(master: AbstractDaoMaster) {
//        configMap[RequestLogDao::class.java] = DaoConfig(master.database, RequestLogDao::class.java)
//        configMap[AttendHistoryDataDao::class.java] = DaoConfig(master.database, AttendHistoryDataDao::class.java)
//    }
//
//    override fun registerDaoWithEntity(daoSession: DaoSession, configMap: MutableMap<Class<out AbstractDao<*, *>>, DaoConfig?>, type: IdentityScopeType?) {
////        val logConfig = configMap[RequestLogDao::class.java]?.clone()
////        logConfig?.initIdentityScope(type)
////        daoSession.registerDao(RequestLog::class.java, RequestLogDao(logConfig, daoSession))
////
////        val attendHistoryConfig = configMap[AttendHistoryDataDao::class.java]?.clone()
////        attendHistoryConfig?.initIdentityScope(type)
////        daoSession.registerDao(AttendHistoryData::class.java, AttendHistoryDataDao(attendHistoryConfig, daoSession))
//    }
//
//    override fun onDropAllTables(db: Database, ifExists: Boolean) {
//        RequestLogDao.createTable(db, ifExists)
//        AttendHistoryDataDao.createTable(db, ifExists)
//    }
//
//    override fun onUpgradeTables(db: Database?, oldVersion: Int, newVersion: Int) {
//
//    }
//
////    override fun registerDaoWithEntity(session: DDbesDaoSession, type: IdentityScopeType) {
////        val logConfig = configMap[RequestLogDao::class.java]?.clone()
////        logConfig?.initIdentityScope(type)
////        session.registerDao(RequestLog::class.java, RequestLogDao(logConfig, session))
////
////        val attendHistoryConfig = configMap[AttendHistoryDataDao::class.java]?.clone()
////        attendHistoryConfig?.initIdentityScope(type)
////        session.registerDao(AttendHistoryData::class.java, AttendHistoryDataDao(attendHistoryConfig, session))
////    }
//
//    //    override fun getConfigDao(master: AbstractDaoMaster): MutableMap<Class<out AbstractDao<*, *>>, DaoConfig> {
////        configMap[RequestLogDao::class.java] = DaoConfig(master.database, RequestLogDao::class.java)
////        configMap[AttendHistoryDataDao::class.java] = DaoConfig(master.database, AttendHistoryDataDao::class.java)
////        return configMap
////    }
//    override fun getConfigDao(master: AbstractDaoMaster): ArrayList<Class<out AbstractDao<*, *>>> {
//        Log.e("db_test", "获取dao 实体类注入到管理器中，生成DaoConfig")
//        val list = arrayListOf<Class<out AbstractDao<*, *>>>()
//        list.add(RequestLogDao::class.java)
//        list.add(AttendHistoryDataDao::class.java)
//        return list
//    }
//
//    override fun registerDaoClass(master: AbstractDaoMaster, vararg daoClass: Class<out AbstractDao<*, *>>) {
//        if (!daoClass.isNullOrEmpty()) {
//            daoClass.forEach { clasz ->
//                configMap[clasz] = DaoConfig(master.database, clasz)
//            }
//        }
//    }
//
//    override fun onCreateAllTables(db: Database, ifNotExists: Boolean) {
//    }
//
//    override fun restoreData(db: Database) {
//    }
//
//    override fun generateTempTables(db: Database) {
//    }
//
//}