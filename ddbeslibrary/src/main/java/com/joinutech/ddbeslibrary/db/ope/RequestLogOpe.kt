package com.joinutech.ddbeslibrary.db.ope

import android.content.Context
import com.joinu.db.gen.RequestLogDao
import com.joinutech.ddbeslibrary.db.BaseDbManager
import com.joinutech.ddbeslibrary.db.data.RequestLog
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.marktoo.lib.cachedweb.LogUtil
import io.reactivex.Observable
import io.reactivex.ObservableOnSubscribe
import io.reactivex.Observer
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

/**
 * @PackageName: com.joinutech.ddbeslibrary.db.ope
 * @ClassName: AttendHistoryOpe
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/9/25 11:08
 * @Desc: //TODO 考勤打卡数据本地缓存操作类
 */
class RequestLogOpe private constructor() {

    private object DBHolder {
        val instance = RequestLogOpe()
    }

    companion object {
        fun getInstance(): RequestLogOpe {
            return DBHolder.instance
        }
    }

    private fun getDao(context: Context): RequestLogDao? {
        return BaseDbManager.getInstance(context)?.getDaoSession(context)?.requestLogDao
    }

    fun insert(context: Context, data: RequestLog) {
        Observable.create(ObservableOnSubscribe<Long> {
            val id = getDao(context)?.insert(data)
            if (id != null && id > 0) {
                it.onNext(id)
            } else {
                it.onError(Throwable("插入请求日志失败"))
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                .subscribe(object : Observer<Long> {
                    override fun onComplete() {
                    }

                    override fun onSubscribe(d: Disposable) {
                    }

                    override fun onNext(recordId: Long) {
                        LogUtil.showLog("插入请求日志成功 $recordId")
                    }

                    override fun onError(e: Throwable) {
                        LogUtil.showLog("插入请求日志失败 [${GsonUtil.toJson(data)}]")
                    }
                })
    }

    fun findBeforeTodayLog(context: Context, currentTime: Long, page: Int, pageSize: Int, result: (List<RequestLog>) -> Unit) {
        Observable.create(ObservableOnSubscribe<List<RequestLog>> {
            val data = getDao(context)?.queryBuilder()
                    ?.where(RequestLogDao.Properties.StartTime.lt(currentTime))// 早于今天时间的日志
                    ?.offset(page * pageSize)
                    ?.limit(pageSize)
                    ?.list() ?: arrayListOf()
            if (!data.isNullOrEmpty()) {
                it.onNext(data)
            } else {
                it.onError(Throwable("存储考勤记录失败"))
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                .subscribe(object : Observer<List<RequestLog>> {
                    override fun onComplete() {
                    }

                    override fun onSubscribe(d: Disposable) {
                    }

                    override fun onNext(data: List<RequestLog>) {
                        result.invoke(data)
                    }

                    override fun onError(e: Throwable) {
                        result.invoke(arrayListOf())
                    }
                })
    }

    fun findBeforeTodayLog(context: Context, currentTime: Long): List<RequestLog> {
        return getDao(context)?.queryBuilder()
                ?.where(RequestLogDao.Properties.StartTime.lt(currentTime))// 早于今天时间的日志
                ?.list() ?: arrayListOf()
    }

    fun findBeforeTodayLog(context: Context, currentTime: Long, page: Int, pageSize: Int): List<RequestLog> {
        return getDao(context)?.queryBuilder()
                ?.where(RequestLogDao.Properties.StartTime.lt(currentTime))// 早于今天时间的日志
                ?.offset(page * pageSize)
                ?.limit(pageSize)
                ?.list() ?: arrayListOf()
    }

    fun findBeforeTodayLogCount(context: Context, currentTime: Long): Long {
        return getDao(context)?.queryBuilder()
                ?.where(RequestLogDao.Properties.StartTime.lt(currentTime))// 早于今天时间的日志(<)
                ?.count() ?: 0L
    }

    fun findBeforeTodayLogCount(context: Context, currentTime: Long, result: (Long) -> Unit) {
        Observable.create(ObservableOnSubscribe<Long> {
            val count = getDao(context)?.queryBuilder()
                    ?.where(RequestLogDao.Properties.StartTime.lt(currentTime))// 早于今天时间的日志
                    ?.count() ?: 0L
            it.onNext(count)
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                .subscribe(object : Observer<Long> {
                    override fun onComplete() {
                    }

                    override fun onSubscribe(d: Disposable) {
                    }

                    override fun onNext(data: Long) {
                        result.invoke(data)
                    }

                    override fun onError(e: Throwable) {
                        result.invoke(0L)
                    }
                })
    }

    fun getAll(context: Context): List<RequestLog> {
        return getDao(context)?.loadAll() ?: arrayListOf()
    }

    fun removeAll(context: Context, currentDayTime: Long) {
        Observable.create(ObservableOnSubscribe<String> {
            try {
                getDao(context)?.queryBuilder()
                        ?.where(RequestLogDao.Properties.StartTime.lt(currentDayTime))// 早于今天时间的日志
                        ?.buildDelete()?.executeDeleteWithoutDetachingEntities()
                it.onNext("清除日志成功")
            } catch (e: Exception) {
                it.onNext("清除日志失败")
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                .subscribe(object : Observer<String> {
                    override fun onComplete() {
                    }

                    override fun onSubscribe(d: Disposable) {
                    }

                    override fun onNext(data: String) {
                        LogUtil.showLog("清除日志结果： $data")
                    }

                    override fun onError(e: Throwable) {
                    }
                })
    }

    fun removeAll(context: Context, currentDayTime: Long, ids: List<Long>) {
        Observable.create(ObservableOnSubscribe<String> {
            try {
                getDao(context)?.queryBuilder()
                        ?.where(RequestLogDao.Properties.StartTime.lt(currentDayTime))// 早于今天时间的日志
                        ?.where(RequestLogDao.Properties.Id.`in`(ids))// 早于今天时间的日志
                        ?.buildDelete()?.executeDeleteWithoutDetachingEntities()
                it.onNext("清除日志成功")
            } catch (e: Exception) {
                it.onNext("清除日志失败")
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                .subscribe(object : Observer<String> {
                    override fun onComplete() {
                    }

                    override fun onSubscribe(d: Disposable) {
                    }

                    override fun onNext(data: String) {
                        LogUtil.showLog("清除日志结果： $data")
                    }

                    override fun onError(e: Throwable) {
                    }
                })
    }
}