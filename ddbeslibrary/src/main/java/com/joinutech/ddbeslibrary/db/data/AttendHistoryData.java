package com.joinutech.ddbeslibrary.db.data;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;

import java.io.Serializable;

import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Keep;
import org.jetbrains.annotations.NotNull;

/**
 * @PackageName: com.joinutech.ddbeslibrary.db.data
 * @ClassName: 考勤历史数据记录
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/9/25 10:37
 * @Desc: 缓存数据工具类
 */
@Entity
public class AttendHistoryData implements Serializable {

    private static final long serialVersionUID = 2L;

    @Id(autoincrement = true)
    private Long id;
    /*当前打卡人id*/
    private String userId;
    /**
     * 公司id
     */
    private String companyId;
    /**
     * 考勤接口调用 打卡日期和时间 需要转成 yyyy-MM-dd HH:mm:ss 更新或者打卡时间可能不在同一天
     */
    private long createTime;
    /**
     * 考勤日期 yy-MM-dd
     */
    private String currentDate;
    /**
     * 打卡状态 1 正常 2 迟早 3 早退 4外勤 ,负数表示打卡失败记录
     */
    private int clockStatus;
    /**
     * 考勤打卡提交数据
     */
    private String parameterJson;
    /**
     * 考勤数据
     */
    private String clockDataJson;

    /**
     * 当前环境名称
     */
    private String envName;

    @Keep
    public AttendHistoryData(@NotNull String userId, @NotNull String companyId, long createTime, @NotNull String currentDate, int clockStatus, @NotNull String parameterJson, @NotNull String clockDataJson) {
        this.userId = userId;
        this.companyId = companyId;
        this.createTime = createTime;
        this.currentDate = currentDate;
        this.clockStatus = clockStatus;
        this.parameterJson = parameterJson;
        this.clockDataJson = clockDataJson;
    }

    @Keep
    public AttendHistoryData(Long id, String userId, String companyId, long createTime, String currentDate, int clockStatus, String parameterJson, String clockDataJson, String envName) {
        this.id = id;
        this.userId = userId;
        this.companyId = companyId;
        this.createTime = createTime;
        this.currentDate = currentDate;
        this.clockStatus = clockStatus;
        this.parameterJson = parameterJson;
        this.clockDataJson = clockDataJson;
        this.envName = envName;
    }

//@Generated(hash = *********)
@Keep
public AttendHistoryData() {
}

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserId() {
        return this.userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCompanyId() {
        return this.companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public String getCurrentDate() {
        return this.currentDate;
    }

    public void setCurrentDate(String currentDate) {
        this.currentDate = currentDate;
    }

    public int getClockStatus() {
        return this.clockStatus;
    }

    public void setClockStatus(int clockStatus) {
        this.clockStatus = clockStatus;
    }

    public String getParameterJson() {
        return this.parameterJson;
    }

    public void setParameterJson(String parameterJson) {
        this.parameterJson = parameterJson;
    }

    public String getClockDataJson() {
        return this.clockDataJson;
    }

    public void setClockDataJson(String clockDataJson) {
        this.clockDataJson = clockDataJson;
    }

    public String getEnvName() {
        return this.envName;
    }

    public void setEnvName(String envName) {
        this.envName = envName;
    }

}
