package com.joinutech.ddbeslibrary.db

import android.database.sqlite.SQLiteDatabase
import com.joinu.db.gen.DaoMaster
import org.greenrobot.greendao.database.Database
import org.greenrobot.greendao.database.StandardDatabase
import org.greenrobot.greendao.identityscope.IdentityScopeType

/**
 * @Description: TODO
 * @Author: zhaoyy
 * @Time: 2021/4/23 18:50
 * @packageName: com.joinutech.ddbeslibrary.db
 * @Company: Copyright (C),2019- 2021,JoinuTech
 */
class BaseDaoMaster : DaoMaster {
    constructor(db: SQLiteDatabase?) : this(StandardDatabase(db))
    constructor(db: Database) : super(db)

//    init {
//        Log.e("db_test", "初始化master过程中 注册数据表关联Config 到数据库中")
//        val result = BaseMigrationHelper.registerConfigDao(this)
//        if (!result.isNullOrEmpty()) {
//            result.forEach {
//                Log.e("db_test", "增加${it} DaoConfig")
//                registerDaoClass(it)
//            }
//        }
//        Log.e("db_test", "初始化master 完成")
//    }
//
    override fun newSession(): BaseDaoSession {
        return BaseDaoSession(db, type = IdentityScopeType.Session, daoConfigMap = daoConfigMap)
    }
}