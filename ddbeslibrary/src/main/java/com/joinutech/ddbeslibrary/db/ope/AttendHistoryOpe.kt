package com.joinutech.ddbeslibrary.db.ope

import android.content.Context
import com.joinu.db.gen.AttendHistoryDataDao
import com.joinutech.ddbeslibrary.db.BaseDbManager
import com.joinutech.ddbeslibrary.db.data.AttendHistoryData
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.marktoo.lib.cachedweb.LogUtil
import io.reactivex.Observable
import io.reactivex.ObservableOnSubscribe
import io.reactivex.Observer
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

/**
 * @PackageName: com.joinutech.ddbeslibrary.db.ope
 * @ClassName: AttendHistoryOpe
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/9/25 11:08
 * @Desc: //TODO 考勤打卡数据本地缓存操作类
 */
class AttendHistoryOpe private constructor() {

    private object DBHolder {
        val instance = AttendHistoryOpe()
    }

    companion object {
        fun getInstance(): AttendHistoryOpe {
            return DBHolder.instance
        }
    }

    private fun getDao(context: Context): AttendHistoryDataDao? {
        return BaseDbManager.getInstance(context)?.getDaoSession(context)?.attendHistoryDataDao
    }

    fun insert(context: Context, data: AttendHistoryData) {
        Observable.create(ObservableOnSubscribe<Long> {
            val id = getDao(context)?.insert(data)
            if (id != null && id > 0) {
                it.onNext(id)
            } else {
                it.onError(Throwable("存储考勤记录失败"))
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                .subscribe(object : Observer<Long> {
                    override fun onComplete() {
                    }

                    override fun onSubscribe(d: Disposable) {
                    }

                    override fun onNext(recordId: Long) {
                        LogUtil.showLog("存储考勤记录成功 $recordId")
                    }

                    override fun onError(e: Throwable) {
                        LogUtil.showLog("存储考勤记录失败[${GsonUtil.toJson(data)}]")
                    }
                })
    }

    fun getAll(context: Context): List<AttendHistoryData> {
        return getDao(context)?.loadAll() ?: arrayListOf()
    }
}