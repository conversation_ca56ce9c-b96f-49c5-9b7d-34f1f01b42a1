package com.joinutech.ddbeslibrary.db

import android.content.Context
import android.database.sqlite.SQLiteDatabase
import com.joinutech.ddbeslibrary.db.helper.BaseOpenHelper
import com.marktoo.lib.cachedweb.LogUtil

/**
 * Description 数据库管理类
 * Author HJR36
 * Date 2018/7/2 12:46
 */
class BaseDbManager private constructor(applicationContext: Context) {
    private val DB_NAME = "ddbes_base.db"

    //旧数据保留开关
    private var mDevOpenHelper: BaseOpenHelper? = null//可以保留旧数据的helper
    private var mDaoMaster: BaseDaoMaster? = null
    private var mDaoSession: BaseDaoSession? = null

    init {
        // 初始化数据库信息
        mDevOpenHelper = BaseOpenHelper(applicationContext, DB_NAME)//可以保留旧数据的helper
        getDaoMaster(applicationContext)?.let {
            getDaoSession(applicationContext)
        }
    }

    companion object {
        @Volatile
        var instance: BaseDbManager? = null

        fun getInstance(mContext: Context): BaseDbManager? {
            if (instance == null) {
                synchronized(BaseDbManager::class) {
                    if (instance == null) {
                        instance = BaseDbManager(mContext.applicationContext)
                    }
                }
            }
            return instance
        }
    }


    /**
     * 获取可读数据库
     *
     * @param context
     * @return
     */
    fun getReadableDatabase(context: Context): SQLiteDatabase? {
        if (null == mDevOpenHelper) {
            getInstance(context)
        }
        return mDevOpenHelper?.readableDatabase
    }

    /**
     * 获取可写数据库
     *
     * @param context
     * @return
     */
    private fun getWritableDatabase(context: Context): SQLiteDatabase? {
        if (null == mDevOpenHelper) {
            getInstance(context)
        }
        return mDevOpenHelper?.writableDatabase
    }

    /**
     * 获取DaoMaster
     *
     * @param context
     * @return
     */
    private fun getDaoMaster(context: Context): BaseDaoMaster? {
        if (null == mDaoMaster) {
            synchronized(BaseDaoMaster::class.java) {
                if (null == mDaoMaster) {
                    LogUtil.showLog("当前DaoMaster 未初始化")
                    mDaoMaster = BaseDaoMaster(getWritableDatabase(context))
                }
            }
        }
        return mDaoMaster
    }

    /**
     * 获取DaoSession
     *
     * @param context
     * @return
     */
    fun getDaoSession(context: Context): BaseDaoSession? {
        if (null == mDaoSession) {
            synchronized(BaseDaoMaster::class.java) {
                if (mDaoSession == null) {
                    LogUtil.showLog("当前DaoSession 未初始化")
                    getDaoMaster(context)?.let {
                        val session = it.newSession()
//                        val map =
//                                BaseMigrationHelper.registerConfigDao(it)
//                        val session = DDbesDaoSession(it.database, IdentityScopeType.Session, map)
//                        BaseMigrationHelper.registerEntityDao(session, IdentityScopeType.Session)
                        mDaoSession = session
                    }
                }
            }
        }
        return mDaoSession
    }
}