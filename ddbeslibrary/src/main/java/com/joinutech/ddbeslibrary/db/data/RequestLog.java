package com.joinutech.ddbeslibrary.db.data;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Index;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Keep;

/**
 * @Description: 网络请求日志
 * @Author: zhaoyy
 * @Time: 2021/4/23 9:02
 * @packageName: com.joinutech.ddbeslibrary.db.data
 * @Company: Copyright (C),2019- 2021,JoinuTech
 */
@Entity
public class RequestLog {
    @Id(autoincrement = true)
    private Long id;
    @Index
    private int method;
    private String url;
    private Long startTime;
    private Long endTime;
    @Index
    private int code;
    @Index
    private String network;
    @Index
    private int appVersion;

    public RequestLog(int method, String url, Long startTime, Long endTime,
            int code, String network, int appVersion) {
        this.method = method;
        this.url = url;
        this.startTime = startTime;
        this.endTime = endTime;
        this.code = code;
        this.network = network;
        this.appVersion = appVersion;
    }

    @Generated(hash = 1479553945)
    public RequestLog(Long id, int method, String url, Long startTime, Long endTime,
            int code, String network, int appVersion) {
        this.id = id;
        this.method = method;
        this.url = url;
        this.startTime = startTime;
        this.endTime = endTime;
        this.code = code;
        this.network = network;
        this.appVersion = appVersion;
    }

    //    @Generated(hash = 1754024340)
    @Keep
    public RequestLog() {
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public int getMethod() {
        return this.method;
    }

    public void setMethod(int method) {
        this.method = method;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Long getStartTime() {
        return this.startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return this.endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public int getCode() {
        return this.code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getNetwork() {
        return this.network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public int getAppVersion() {
        return this.appVersion;
    }

    public void setAppVersion(int appVersion) {
        this.appVersion = appVersion;
    }

}
