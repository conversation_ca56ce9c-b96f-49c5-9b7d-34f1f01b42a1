package com.joinutech.ddbeslibrary.db.helper;


import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import com.joinu.db.gen.DaoMaster;

import org.greenrobot.greendao.database.Database;

import static com.joinu.db.gen.DaoMaster.SCHEMA_VERSION;

public class BaseOpenHelper extends DaoMaster.OpenHelper {

    public BaseOpenHelper(Context context, String name) {
        super(context, name);
//        Log.e("db_test", "MyOpenHelper init");
    }

    public BaseOpenHelper(Context context, String name, SQLiteDatabase.CursorFactory factory) {
        super(context, name, factory);
    }

    @Override
    public void onCreate(Database db) {
//        Log.e("db_test", "MyOpenHelper create db");
//        Log.i("greenDAO", "Creating tables for schema version " + SCHEMA_VERSION);
        super.onCreate(db);
        BaseMigrationHelper.createTables(db);
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
//        Log.e("db_test", "MyOpenHelper upgrade db");

//        super.onUpgrade(db, oldVersion, newVersion);
//        // 迁移数据库(如果修改了多个实体类，则需要把对应的Dao都传进来)
//        // MigrationHelper.migrate(db, SessionDao.class);
        if (oldVersion >= newVersion) {
            return;
        }
        //升级时，需要改哪个表，就要把对应的dao类添加在这里；
        //升级的方法：在bean类中新增加某些字段后，将数据库版本号加1，
        // 不用清除bean类中自动生成的代码，直接Make Project即可
        // 尽量只增加字段，如果删除字段的话，需手动删除某些自动生成的代码，如果报错，就只能把对应bean类中自动生成的代码全部删除再make project；
        BaseMigrationHelper.onUpgradeTables(db, oldVersion, newVersion);
    }
}