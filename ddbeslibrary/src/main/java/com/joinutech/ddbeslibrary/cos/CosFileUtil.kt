package com.joinutech.ddbeslibrary.cos

import android.Manifest
import android.view.View
import android.widget.TextView
import com.joinutech.common.base.isDebug
import com.joinutech.common.util.UserHolder
import com.joinutech.common.storage.CosService
import com.joinutech.common.storage.FileStorage
import com.joinutech.ddbeslibrary.R
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.base.MyUseBaseActivity
import com.joinutech.ddbeslibrary.bean.*
import com.joinutech.ddbeslibrary.request.DdbesApiUtil
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.request.RxScheduleUtil
import com.joinutech.ddbeslibrary.request.exception.ApiException
import com.joinutech.ddbeslibrary.request.exception.BaseSubscriber
import com.joinutech.ddbeslibrary.request.exception.ErrorTransformer
import com.joinutech.ddbeslibrary.service.UploadFileService
import com.joinutech.ddbeslibrary.utils.*
import com.marktoo.lib.cachedweb.LogUtil
import com.tencent.cos.xml.CosXmlServiceConfig
import com.tencent.cos.xml.model.PresignedUrlRequest
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import io.reactivex.FlowableOnSubscribe
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import java.io.File

/**
 * @PackageName: com.joinutech.ddbeslibrary.cos
 * @ClassName: CosFileUtil
 * @Author: zhaoyy
 * @Leader: Ke
 * @CreateTime: 2020/12/8 10:58
 * @Desc: //TODO cos文件上传 工具类 带存储控件检测
 */
class CosFileUtil {

    fun getTencentCloudConfig(): CosXmlServiceConfig? {
        if (FileStorage.COS_REGION.isNullOrBlank()) return null
        return CosXmlServiceConfig.Builder()
                .isHttps(true)
                .setRegion(FileStorage.COS_REGION)
//                  .setAppidAndRegion(appid, region)
                .setDebuggable(isDebug)
                .builder()
    }

    fun buildService(t: TencentSessionBean): CosService? {
        val secretId = t.credentials.tmpSecretId
        val secretKey = t.credentials.tmpSecretKey
        val sessionToken = t.credentials.sessionToken
        val expiredTime: Long = t.expiredTime//临时密钥有效截止时间戳
        //初始化 config
        val serviceConfig = FileStorage.getTencentCloudConfig() ?: return null
        //使用临时密钥初始化QCloudCredentialProvider
        val qCloudCredentialProvider = MyCredentialProvider(secretId, secretKey, sessionToken, expiredTime)
//            not necessary
//            qCloudCredentialProvider.refresh()
        //初始化CosXmlService
        return CosService(BaseApplication.joinuTechContext, serviceConfig, qCloudCredentialProvider)
    }

    fun buildService(t: TencentSessionBean, serviceConfig: CosXmlServiceConfig): CosService {
        val secretId = t.credentials.tmpSecretId
        val secretKey = t.credentials.tmpSecretKey
        val sessionToken = t.credentials.sessionToken
        val expiredTime: Long = t.expiredTime//临时密钥有效截止时间戳
        //使用临时密钥初始化QCloudCredentialProvider
        val qCloudCredentialProvider = MyCredentialProvider(secretId, secretKey, sessionToken, expiredTime)
//            not necessary
//            qCloudCredentialProvider.refresh()
        //初始化CosXmlService
        return CosService(BaseApplication.joinuTechContext, serviceConfig, qCloudCredentialProvider)
    }

    fun buildTempUrl(cosService: CosService, bucket: String, cosPath: String): String {
//        val bucket = "examplebucket-**********" //存储桶名称
//        val cosPath = "exampleobject" //即对象在存储桶中的位置标识符。

        val method = "GET" //请求 HTTP 方法.
        val preSignedUrlRequest = PresignedUrlRequest(bucket, cosPath)
        preSignedUrlRequest.setRequestMethod(method)
        return cosService.getPresignedURL(preSignedUrlRequest)
    }

    /**
     * 获取本地临时访问临时存储中文件url
     * 要web显示的文件url获取
     * @param files 要web显示的文件集合,云端存在文件的集合
     */
    fun buildTempLinkUrl(files: ArrayList<UploadFileBean>, onResult: (List<PanLinkResult>) -> Unit, onError: (String) -> Unit) {
        val list = files.map {
            hashMapOf("key" to it.fileId, "name" to it.fileName)
        }
        FileStorage.getPanBucketConfig().flatMap { config ->
            FileStorage.updatePanBucketConfig(config)
            val bucket = if (files[0].bucket.isNullOrBlank()) config.bucket else files[0].bucket
            UploadFileService.getPanFileTempUrl(PanLinkRequest(list, bucket))
                    .compose(ErrorTransformer.getInstance<List<PanLinkResult>>())
        }.subscribe(object : BaseSubscriber<List<PanLinkResult>>() {
            override fun onError(ex: ApiException) {
                onError.invoke(ex.message)
            }

            override fun onComplete() {

            }

            override fun onNext(result: List<PanLinkResult>?) {
//                LogUtil.showLog("获取文件session ${GsonUtil.toJson(result)} ----", "file_up__")
                result?.map {
                    LogUtil.showLog("获取到文件链接 ${it.fileId} :: ${it.uri}")
                }
                onResult.invoke(result ?: arrayListOf())
            }
        })
    }

    /**显示无控件弹窗*/
    fun showNoCapacityDialog(activity: MyUseBaseActivity) {
        val dialog = BaseCenterDialogHelper(activity = activity,
                onBindView = { view ->
                    run {
                        val cancel = view.findViewById<TextView>(R.id.cancel)
                        val lineV = view.findViewById<View>(R.id.line_v)
                        val tvContent = view.findViewById<TextView>(R.id.tv_content)
                        tvContent.text = "团队云盘存储空间已满，无法上传更多图片/附件。" +
                                "请登录pan.ddbes.com了解更多存储详情"
                        val tvTitle = view.findViewById<TextView>(R.id.tv_title)
                        val tvHint = view.findViewById<TextView>(R.id.tv_hint)
                        tvTitle.visibility = View.GONE
                        tvHint.visibility = View.GONE
                        cancel.visibility = View.GONE
                        lineV.visibility = View.GONE
                    }
                },
                onConfirm = {}, onCancel = {})
        dialog.initView()
        dialog.show(true)
    }

    companion object {
        val placeHolderData = UploadFileBean(isUploadFlag = true)
    }

    /**
     * 检测云存储空间的文件上传流程
     * 1、过滤上传中和上传完成文件，初始化文件上传信息 findUploadNeedList
     * 2、获取文件上传信息 getFileHashInfo
     * 3、确认是否在云端存在 getCloudFileInfo
     * 4、有需要上传文件，获取云存储空间信息 getCosCapacity
     * 5、检查云存储空间是否可用 checkCapacity
     * 6、空间可用，文件不存在，执行上传 dealUploadEvent
     */
    fun findUploadNeedList(selectedFile: List<UploadFileBean>, progressHash: HashMap<String, Int>,
                           onResult: (successList: ArrayList<UploadFileBean>, needList: ArrayList<UploadFileBean>) -> Unit) {
        val temp = mutableListOf<UploadFileBean>()
        if (!selectedFile.isNullOrEmpty()) {
            temp.addAll(selectedFile)
            if (temp.last() == placeHolderData) {
                temp.removeAt(temp.size - 1)
            }
        }
        if (temp.isEmpty()) {
            onResult.invoke(arrayListOf(), arrayListOf())
        }
        val needUploadFiles = arrayListOf<UploadFileBean>()
        val uploadSuccessList = arrayListOf<UploadFileBean>()
        for (file in temp) {
            /**已完成或者正在上传的文件不再加入上传队列*/
            if (progressHash.isNotEmpty() && progressHash.containsKey(file.fileUrl)) {
                if (progressHash[file.fileUrl] == 100) {
                    file.isUploadFlag = true
                    uploadSuccessList.add(file)
                } else {
                    needUploadFiles.add(file)
                }
                continue
            } else {
                // 添加到上传记录中，进度为0
                needUploadFiles.add(file)
                progressHash[file.fileUrl] = 0
            }
        }
        onResult.invoke(uploadSuccessList, needUploadFiles)
    }

    /**获取要上传文件的hash信息 确认要上传的文件集合获取hash信息*/
    fun getFileHashInfo(activity: MyUseBaseActivity, currentUploadFiles: ArrayList<UploadFileBean>,
                        onResult: (HashMap<String, String>) -> Unit) {
        val perms = arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE)
        val tips = "选取图片需要您授权读写"
        PermissionUtils.requestPermissionActivity(activity, perms, tips,
                onSuccess = {
                    //先获取文件的md5，再通过后台获取上传文件的id值，再去上传文件
                    Flowable.create(FlowableOnSubscribe<HashMap<String, String>> { emitter ->
                        try {
                            // 去重需要上传的文件，hashMap中key为要上传文件的路径，value为文件对应的hash
                            val hashMap = hashMapOf<String, String>()
                            currentUploadFiles.forEach { bean ->
                                if (hashMap.isEmpty() || !hashMap.containsKey(bean.fileUrl)) {
                                    val hashValue = StringUtils.getFileMD5(File(bean.fileUrl))
                                    if (StringUtils.isNotBlankAndEmpty(hashValue)) {
                                        hashMap[bean.fileUrl] = hashValue
                                    }
                                }
                            }
                            // 上传文件转换为map记录即将上传的数据，文件路径:对应文件的hash
                            if (hashMap.isNotEmpty()) {
                                emitter.onNext(hashMap)
                            } else {
                                emitter.onNext(hashMapOf())
                            }
                        } catch (e: Exception) {
                            emitter.onNext(hashMapOf())
                        }
                    }, BackpressureStrategy.BUFFER)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe { hashValues ->
                                LogUtil.showLog("获取hash完成，开始请求文件id ${GsonUtil.toJson(hashValues)}", "file_up__")
                                onResult.invoke(hashValues)
                            }
                },
                onError = {
                    ToastUtil.show(BaseApplication.joinuTechContext, tips)
                })
    }

    /**根据文件fileHash信息中hash集合 获取文件云存储信息fileId，并判断云端是否存在*/
    fun getCloudFileInfo(fileList: List<UploadFileBean>,
                         /**文件路径和对应hash */
                         hashValues: HashMap<String, String>,
                         /**返回已在云端存在文件的进度信息和 云端不存在文件，确认需要上传文件的信息filePath:UploadFileBean*/
                         onResult: (HashMap<String, Int>, HashMap<String, UploadFileBean>) -> Unit,
                         /**请求云存储失败时返回*/
                         onError: () -> Unit) {
        // 取出需要上传文件的 hash 值集合，请求fileId信息，判断文件在云盘是否存在
        val hashList = hashValues.values.toList()
        val map = hashMapOf<String, Any>()
        map["hash"] = hashList
        UploadFileService.getPanTencentId(UserHolder.getAccessToken(), map)
                .compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<List<PanFileBean>>() {
                    override fun onError(ex: ApiException) {
                        onError.invoke()
                    }

                    override fun onComplete() {

                    }

                    override fun onNext(cloudFileInfoList: List<PanFileBean>?) {
                        LogUtil.showLog("获取文件id 结果回调 ${GsonUtil.toJson(cloudFileInfoList)} ----", "file_up__")
                        if (cloudFileInfoList.isNullOrEmpty()) {
                            onError.invoke()
                            return
                        }
                        val temp = hashMapOf<String, UploadFileBean>()
                        val progressHash = HashMap<String, Int>()
                        fileList.forEach { file ->
                            val hash = hashValues[file.fileUrl]
                            if (!hash.isNullOrBlank()) {
                                val data = cloudFileInfoList[hashList.indexOf(hash)]
                                file.fileId = data.key
                                file.isUploadFlag = data.exist
                                file.hash = hash
                                // 要上传的文件在云端已经存在时，设置上传记录中进度为100，已上传文件中设置文件路径对应云端id即fileId
                                if (data.exist) {
                                    progressHash[file.fileUrl] = 100
                                } else {
                                    // 云端不存在的文件加入需要上传文件的集合中
                                    temp[file.fileUrl] = file
                                }
                            }
                        }
                        onResult.invoke(progressHash, temp)
                    }
                })
    }

    /**
     * 获取云存储空间
     * param：公司id
     * result：公司容量信息
     */
    fun getCosCapacity(companyId: String, onResult: (CapacityBean) -> Unit, onError: () -> Unit) {
        val tag = "检查云存储空间"
        RxScheduleUtil.rxSchedulerHelper(
                DdbesApiUtil.getTaskService().searchCompanyCapacity(UserHolder.getAccessToken(), companyId)
        ).compose(ErrorTransformer.getInstance())
                .subscribe(object : BaseSubscriber<CapacityBean>() {
                    override fun onError(ex: ApiException) {
                        RequestHelper.showLogLine(msg = ">>>$tag 返回错误<<<")
                        onError.invoke()
                    }

                    override fun onComplete() {
                        RequestHelper.showLogLine(msg = ">>>$tag 获取结束<<<")
                    }

                    override fun onNext(result: CapacityBean?) {
                        RequestHelper.showLogLine(msg = ">>>$tag 返回数据<<<")
                        if (result != null) {
                            onResult.invoke(result)
                        } else {
                            onError.invoke()
                        }
                    }
                })
    }

    /**
     * 判断空间是否满足要上传文件，确认是否执行上传
     * param：要上传的文件集合和公司容量信息
     * result：是否可以容纳上传文件
     */
    fun checkCapacity(currentUploadFiles: List<UploadFileBean>,
                      /**容量数据*/
                      capacity: CapacityBean,
                      /**result true 空间可用 false 空间不可用*/
                      onResult: (Boolean) -> Unit) {
        LogUtil.showLog("查看容量结果回调 ----", "file_up__")
        val canUsed = capacity.capacity - capacity.used
        var computeFileTotalSize = 0L
        for (file in currentUploadFiles) {
            computeFileTotalSize += File(file.fileUrl).length()
            if (computeFileTotalSize > canUsed) {
                break
            }
        }
        //判断是否容量可用
        onResult.invoke(computeFileTotalSize <= canUsed)
    }

    /**
     * 真实处理文件上传
     * @param activity 关联页面，当用户关闭页面时可以结束调上传流程
     * @param files 要上传的文件信息
     * @param onProgress 上传进度信息回调
     * @param onResult 上传结果回调
     * @param onError 上传异常回调
     */
    // TODO: test 调试onError --ok
    fun dealUploadEvent(activity: MyUseBaseActivity,
                        /**上传文件*/
                        files: ArrayList<UploadFileBean>,
                        /**上传进度*/
                        onProgress: (filePath: String, percent: Int) -> Unit,
                        /**上传成功*/
                        onResult: (ArrayList<FileUploadUtil.UploadResultBean>) -> Unit,
                        /**上传失败*/
                        onError: (failedFile: List<FileUploadUtil.UploadResultBean>) -> Unit) {
        FileUploadUtil.uploadMultiFileWithProgress(
                hashList = files,
                onProgress = { filePath, _, _, percent ->
                    activity.runOnUiThread {
                        // 更新进度时是按文件路径更新，所有列表中根据元素文件路径获取进度即可，多个相同文件时也不需要多个上传任务
                        onProgress.invoke(filePath, percent)
                    }
                },
                onSuccess = { uploadResult ->
                    onResult.invoke(uploadResult)
                },
                onError = {
                    /*图片上传失败处理*/
                    LogUtil.showLog("图片上传失败")
                    LogUtil.showLog(it)
                    onError.invoke(it)
                },
                tosFileType = TosFileType.PAN
        )
    }

}