package com.joinutech.ddbeslibrary.service

import android.content.Context
//import com.baidu.location.BDAbstractLocationListener
//import com.baidu.location.BDLocation
//import com.baidu.location.LocationClient
//import com.baidu.location.LocationClientOption
//import com.baidu.mapapi.map.*
//import com.baidu.mapapi.model.LatLng
//import com.baidu.mapapi.search.core.PoiInfo
//import com.baidu.mapapi.search.core.RouteNode.location
//import com.baidu.mapapi.search.core.SearchResult
//import com.baidu.mapapi.search.geocode.*
//import com.baidu.mapapi.utils.DistanceUtil
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.Loggerr
import com.joinutech.ddbeslibrary.utils.XUtil
import com.marktoo.lib.cachedweb.LogUtil

/**
 * @className: LocationService
 * @desc: 定位服务处理，分离代码中定位sdk关联
 * @author: zyy
 * @date: 2019/6/5 15:46
 * @company: joinUTech
 * @leader: ke
 */
class LocationService(context: Context)  {
//    private var client: LocationClient? = null
//    private var customOption: LocationClientOption? = null
//    private var defaultOption: LocationClientOption? = null
//
//    private var mSearch: GeoCoder? = null
//
//    @Volatile
//    private var objLock = Object()
//
//    init {
//        Loggerr.i("初始化","===初始化百度定位===")
//        //初始化定位
//        if (client == null) {
//            synchronized(objLock) {
//                if (client == null) {
//                    LocationClient.setAgreePrivacy(true)
//                    try {
//                        client = LocationClient(context)
//                    } catch (e: Exception) {
//                        XUtil.errorLogToPrint(e)
//                    }
//                    client?.locOption = defaultOption
//                }
//                objLock.notifyAll()
//            }
//        }
//    }
//
//    private val listeners = arrayListOf<LocationCallback?>()
//
//    /**定位sdk监听器*/
//    private var myListener: MyListener? = null
//
//    //百度地图定位，，，使用流程总结如下，使用示例搜索“百度地图定位示例”
//    //1，首先调用initLocation()方法
//    //2，再调用registerListener(listener: LocationCallback?)，在需要的页面注册监听
//    //3，最后调用start(是否开启POI)方法开启定位，即可在第2步中的监听中获取到定位数据；
//    //4，最后对应页面关闭的时候移除监听，即，调用stop()方法；
//
//    fun initLocation() {
//        myListener = MyListener(this)
////        client?.registerLocationListener(myListener)
//    }
//
//    fun isRegister(listener: LocationCallback): Boolean {
//        var registered: Boolean
//        synchronized(objLock) {
//            registered = listeners.contains(listener)
//            objLock.notifyAll()
//        }
//        return registered
//    }
//
//    /***
//     *
//     * @param listener 定位监听器
//     * @return 注册定位监听成功
//     */
//    fun registerListener(listener: LocationCallback?): Boolean {
//        var isSuccess = false
//        if (listener != null) {
//            synchronized(objLock) {
//                if (listeners.isEmpty() || !listeners.contains(listener)) {
//                    listeners.add(listener)
//                }
//                objLock.notifyAll()
//            }
//            isSuccess = true
//        }
//        return isSuccess
//    }
//
//    fun unregisterListener(listener: LocationCallback?) {
//        synchronized(objLock) {
//            if (listeners.isNotEmpty() && listeners.contains(listener)) {
//                listeners.remove(listener)
//            }
//            objLock.notifyAll()
//        }
//    }
//
//    /***
//     *
//     * @param option
//     * @return isSuccessSetOption
//     */
//    fun setLocationOption(option: LocationClientOption?): Boolean {
//        val isSuccess = false
//        if (option != null) {
//            if (client!!.isStarted) {
//                client!!.stop()
//            }
//            client!!.locOption = option
//        }
//        return isSuccess
//    }
//
//    /***
//     *
//     * @return DefaultLocationClientOption  默认O设置
//     */
//    fun getDefaultLocationClientOption(): LocationClientOption {
//        if (defaultOption == null) {
//            defaultOption = LocationClientOption()
//            defaultOption!!.apply {
//                locationMode = LocationClientOption.LocationMode.Hight_Accuracy//可选，默认高精度，设置定位模式，高精度，低功耗，仅设备
//                setCoorType("bd09ll")//可选，默认gcj02，设置返回的定位结果坐标系，如果配合百度地图使用，建议设置为bd09ll;
//                setScanSpan(3000)//可选，默认0，即仅定位一次，设置发起连续定位请求的间隔需要大于等于1000ms才是有效的
//                setIsNeedAddress(true)//可选，设置是否需要地址信息，默认不需要
//                setIsNeedLocationDescribe(true)//可选，设置是否需要地址描述
//                setNeedDeviceDirect(false)//可选，设置是否需要设备方向结果
//                isLocationNotify = false//可选，默认false，设置是否当gps有效时按照1S1次频率输出GPS结果
//                setIgnoreKillProcess(true)//可选，默认true，定位SDK内部是一个SERVICE，并放到了独立进程，设置是否在stop的时候杀死这个进程，默认不杀死
//                setIsNeedLocationDescribe(true)//可选，默认false，设置是否需要位置语义化结果，可以在BDLocation.getLocationDescribe里得到，结果类似于“在北京天安门附近”
//                setIsNeedLocationPoiList(true)//可选，默认false，设置是否需要POI结果，可以在BDLocation.getPoiList里得到
//                SetIgnoreCacheException(false)//可选，默认false，设置是否收集CRASH信息，默认收集
//                isOpenGps = true//可选，默认false，设置是否开启Gps定位
//                setIsNeedAltitude(false)//可选，默认false，设置定位时是否需要海拔信息，默认不需要，除基础定位版本都可用
//            }
//
//        }
//        return defaultOption as LocationClientOption
//    }
//
//    /**
//     *
//     * @return option 自定义Option设置
//     */
//    fun getOption(): LocationClientOption {
//        if (customOption == null) {
//            customOption = LocationClientOption()
//            customOption?.apply {
//                isOpenGps = true // 打开gps
//                locationMode = LocationClientOption.LocationMode.Hight_Accuracy//高精度
//                setCoorType("bd09ll") // 设置坐标类型
//                setScanSpan(1000)
//                isLocationNotify = true
//                setIsNeedAddress(true)
//                isNeedAltitude = true
//            }
//        }
//        return customOption as LocationClientOption
//    }
//
//    private var needPoiInfo = false
//
//    fun start(needPoiInfo: Boolean = true) {
//        Loggerr.i("百度地图定位","===开启定位======")
//        synchronized(objLock) {
//            this.needPoiInfo = needPoiInfo
//            lastLocation = null
//            if (needPoiInfo) {
//                Loggerr.i("百度地图定位","===启动poi解析=needPoiInfo=${needPoiInfo}=")
//                mSearch = GeoCoder.newInstance()
//                mSearch?.setOnGetGeoCodeResultListener(this)
//            }
//            myListener?.let {
//                client?.registerLocationListener(it)
//            }
//            if (client != null && !client!!.isStarted) {
//                Loggerr.i("百度地图定位", "=定位服务===start==执行==")
//                client!!.start()
//            }
//            objLock.notifyAll()
//        }
//    }
//
//    fun stop() {
//        synchronized(objLock) {
//            if (client != null) {
//                LogUtil.showLog("定位服务-->停止定位")
//                if (!listeners.isNullOrEmpty()) {
//                    listeners.clear()
//                }
//                myListener?.let {
//                    client?.unRegisterLocationListener(it)
//                }
//                client!!.stop()
//            }
//            objLock.notifyAll()
//        }
//    }
//
//    fun onDestory() {
//        mSearch?.destroy()
//    }
//
//    fun isStart(): Boolean {
//        return client!!.isStarted
//    }
//
//    fun requestHotSpotState(): Boolean {
//        return client!!.requestHotSpotState()
//    }
//
//    class LocationInfo(private val location: BDLocation?) {
////        init {
////            LogUtil.showLog("建筑名:${location?.buildingName ?: "未获取到建筑名"}")
////            LogUtil.showLog("地址名:${location?.addrStr ?: "未获取到地址名"}")
////            LogUtil.showLog("街道名:${location?.street ?: "未获取到街道名"}")
////            LogUtil.showLog("街道名2:${location?.address?.address ?: "未获取到街道名"}")
////            LogUtil.showLog("街道名3:${location?.address?.adcode ?: "未获取到adcode名"}")
////        }
//
//        fun locationSuccess(): Boolean = location != null
//
//        fun getAddr(): String? = location?.addrStr
//
//        fun getPoiFirstPosition(): String? = location?.poiList?.get(0)?.name
//
//        fun getLat(): Double = location?.latitude ?: 0.0
//
//        fun getLng(): Double = location?.longitude ?: 0.0
//
//        fun getRadius(): Float = location?.radius ?: 0.0f
//
//        fun getCity(): String = location?.address?.city ?: ""
//
//        fun getName(): String = location?.street ?: "未知"
//
//    }
//
//    fun getDistance(lat: Double, lng: Double, currentLat: Double, currentLng: Double): Double {
//        return DistanceUtil.getDistance(LatLng(lat, lng), LatLng(currentLat, currentLng))
//    }
//
//    fun setLocation(mapObj: Any, lat: Double, lng: Double, accuracy: Float, direction: Float) {
//        // 设置定位数据
//        if (mapObj is BaiduMap) {
//            mapObj.setMyLocationData(MyLocationData.Builder()
////                    .accuracy(accuracy)
//                    // 此处设置开发者获取到的方向信息，顺时针0-360
//                    .direction(direction)
//                    .latitude(lat)
//                    .longitude(lng)
//                    .build())
//        }
//    }
//
//    fun addMarker(mapObj: Any, markerResourceId: Int) {
//        // 设置自定义图标
//        if (mapObj is BaiduMap) {
//            val mCurrentMarker = BitmapDescriptorFactory
//                    .fromResource(markerResourceId)
//            val config = MyLocationConfiguration(
//                    MyLocationConfiguration.LocationMode.NORMAL, true, mCurrentMarker)
//            mapObj.setMyLocationConfiguration(config)
//        }
//    }
//
//    fun zoomMap(mapObj: Any, lat: Double, lng: Double, zoomSize: Float, animate: Boolean = false) {
//        // 缩放地图
//        if (mapObj is BaiduMap) {
//            val latLng = LatLng(lat, lng)
//            val newMapStatus = MapStatus.Builder().target(latLng).zoom(zoomSize).build()
//            if (animate) {
//                mapObj.animateMapStatus(MapStatusUpdateFactory.newMapStatus(newMapStatus))
//            } else {
//                mapObj.setMapStatus(MapStatusUpdateFactory.newMapStatus(newMapStatus))
//            }
//        }
//    }
//
//    fun setCenter(mapObj: Any, lat: Double, lng: Double, resourceId: Int) {
//        //设定中心点坐标
//        if (mapObj is BaiduMap) {
//            val point = LatLng(lat, lng)
//            val bitmap = BitmapDescriptorFactory.fromResource(resourceId)
//            mapObj.addOverlay(MarkerOptions().position(point).icon(bitmap))
//        }
//    }
//
//    fun decodeLatLng(latLng: LatLng, onResult: (state: Int, name: String) -> Unit) {
//        val geocode = GeoCoder.newInstance()
//        val options = ReverseGeoCodeOption().location(latLng)
//        geocode.setOnGetGeoCodeResultListener(object : OnGetGeoCoderResultListener {
//            override fun onGetGeoCodeResult(result: GeoCodeResult?) {
//            }
//
//            override fun onGetReverseGeoCodeResult(result: ReverseGeoCodeResult?) {
//                if (result == null || result.error != SearchResult.ERRORNO.NO_ERROR) {
//                    onResult.invoke(-1, "")
//                    return
//                }
//                if (result.error == SearchResult.ERRORNO.NO_ERROR) {
//                    onResult.invoke(0, result.address)
//
//                }
//            }
//        })
//        geocode.reverseGeoCode(options)
//    }
//
//    inner class MyListener(private val locationListener: LocationCallback?) : BDAbstractLocationListener() {
//
//        override fun onReceiveLocation(location: BDLocation?) {
//            Loggerr.i("百度地图定位", "=接收定位信息==定位时间=${location?.time}==")
////            LogUtil.showLog("current position : lat= ${location?.latitude} , ${location?.longitude}")
//            /*
//            * 167 代表百度服务端无法定位
//            * 162 代表SO配置出现异常
//            * 62 无法获取有效定位依据，定位失败，请检查运营商网络或者WI-FI网络是否正常开启，尝试重新请求定位
//            * 505
//            * 定位SDK中可能出现第一次定位成功，后续均返回505错误：为了保证定位效率，定位和AK验证是同时进行的，
//            * 很有可能定位先处理完，所以在AK验证错误的消息还未收到时已经显示定位成功了
//            * 定位位SDK并只使用GPS模式（不联网），定位SDK需要联网进行AK验证，
//            * 如果使用定位SDK而不使用网络定位（或者不开启网络），仅使用GPS定位的话，
//            * 使用系统的定位接口即可。如果使用定位SDK，请确保网络可用。
//            * */
//            val locationResult = if (location == null ||
//                    location.locType in listOf(167, 162, 62, 505)) {
//                LocationResult()
//            } else {
//                LocationResult(location.latitude, location.longitude, radius = location.radius,
//                        city = location.city ?: "",
//                        address = location.addrStr ?: "",
//                        gpsAccuracyStatus = location.gpsAccuracyStatus,
//                        locationSuccess = location.latitude != 0.0 && location.longitude != 0.0,
//                        altitude = location.altitude)
//            }
//            locationListener?.onLocationResult(locationResult)// 获取到当前定位坐标结果时回调到定位服务中
//        }
//    }
//
//    private var lastLocation: LocationResult? = null
//
//    override fun getTag(): String {
//        return "base"
//    }
//
//    //百度地图定位，，，单纯定位经纬度成功后，会回调这个方法
//    override fun onLocationResult(locationResult: LocationResult) {
//        // 坐标发生变化时更新
//        if (lastLocation == null || lastLocation!!.lat != locationResult.lat || lastLocation!!.lng != locationResult.lng) {
//            Loggerr.i("百度地图定位", "==当前定位信息为：${GsonUtil.toJson(locationResult)}====")
//            // 记录最后一次定位信息
//            lastLocation = locationResult
//
//            fun onPoiError(result: LocationResult) {
//                if (needPoiInfo) {
//                    LogUtil.showLog("-->-->需要获取地理信息 未开启定位或定位异常时处理")
//                    onPoiLocationResult(result)
//                } else {
//                    LogUtil.showLog("->只需要定位信息时处理")
//                    Loggerr.i("百度地图定位", "===单纯经纬度定位，没有POI解析==定位成功后=开始回调=onLocationResult方法=")
//                    if (listeners.isNotEmpty()) {
//                        listeners.forEach {
//                            it?.onLocationResult(result)// 不能进行POI解析时，定位信息回调
//                        }
//                    }
//                }
//            }
//
//            if (needPoiInfo) {
//                // 需要POI解析时调用
//                LogUtil.showLog("-->需要获取地理信息")
//                if (locationResult.lat != 0.0 && locationResult.lng != 0.0) {
//                    val center = LatLng(locationResult.lat, locationResult.lng)
//                    //反Geo搜索
//                    try {
//                        mSearch?.reverseGeoCode(ReverseGeoCodeOption().location(center))
//                    } catch (e: Exception) {
//                        LogUtil.showLog("-->-->需要获取地理信息 未开启定位时处理")
//                        onPoiError(LocationResult())
//                    }
//                } else {
//                    onPoiError(LocationResult())
//                }
//            } else {
//                // 不需要POI解析时调用
//                onPoiError(locationResult)
//            }
//        }
//    }
//
//    /**定位服务中 POI结果回调 代理*/
//    override fun onPoiLocationResult(poiResult: LocationResult) {
//        if (listeners.isNotEmpty()) {
//            listeners.forEach {
//                it?.onPoiLocationResult(poiResult)// 反地理编码 结果回调
//            }
//        }
//    }
//
//    override fun onGetGeoCodeResult(p0: GeoCodeResult?) {
//    }
//
//    //百度地图定位，，，，开启POI解析后的定位成功后，，会回调这个方法
//    override fun onGetReverseGeoCodeResult(result: ReverseGeoCodeResult?) {
//        val poiResult = if (result == null || result.error != SearchResult.ERRORNO.NO_ERROR) {
//            lastLocation ?: LocationResult()
//        } else {
//            val info = if (result.poiList.isNullOrEmpty()) {
//                val data = PoiInfo()
//                data.name = ""
//                data.city = ""
//                data.address = ""
//                data
//            } else {
//                result.poiList[0]
//            }
//            LogUtil.showLog("-->-->地址解析内容为 ${GsonUtil.toJson(info)}")
//            LocationResult(result.location.latitude, result.location.longitude,
//                    info.name, info.city, info.address, lastLocation?.radius ?: 0.0f,
//                    true)
//        }
//        val data = poiResult.copy(
//                locationSuccess = poiResult.lat != 0.0 && poiResult.lng != 0.0
//        )
//        Loggerr.i("百度地图定位", "===开启了POI解析的定位成功后===开始回调==onPoiLocationResult方法==")
//        onPoiLocationResult(data)// 获取到当前定位坐标poi查询结果回调到定位服务中
//    }
}

interface LocationCallback {

    fun getTag(): String

    /**定位结果回调*/
    fun onLocationResult(result: LocationResult)

    /**定位结果POI查询结果回调*/
    fun onPoiLocationResult(result: LocationResult)
}

data class LocationResult(val lat: Double = 0.0, val lng: Double = 0.0,
                          val name: String = "",
                          val city: String = "",
                          val address: String = "",
                          /**角度*/
                          val radius: Float = 0f,
                          /**定位是否成功*/
                          val locationSuccess: Boolean = false,
                          /**gps精度*/
                          val gpsAccuracyStatus: Int = 0,
                          /**gps高度，需要使用只有是GPS定位结果时或者LocationClientOption.setIsNeedAltitude(true)时才有效，单位米*/
                          val altitude: Double = 0.0
)
