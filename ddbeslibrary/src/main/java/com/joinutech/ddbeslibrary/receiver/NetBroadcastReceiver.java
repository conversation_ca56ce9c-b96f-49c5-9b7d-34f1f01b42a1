package com.joinutech.ddbeslibrary.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

/**
 * description ： 网络连接状态
 * author: 黄洁如
 * date : 2019/10/23
 */
public class NetBroadcastReceiver extends BroadcastReceiver {

    private NetConnectedListener netConnectedListener;

    @Override
    public void onReceive(Context context, Intent intent) {

        ConnectivityManager connectivityManager = (ConnectivityManager) context.
                getSystemService(Context.CONNECTIVITY_SERVICE);

        NetworkInfo mobNetInfo = connectivityManager.getNetworkInfo(ConnectivityManager.TYPE_MOBILE);

        NetworkInfo wifiNetInfo = connectivityManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI);

        String netType = "NONE";

        try {
            NetworkInfo info = connectivityManager.getActiveNetworkInfo();
            if (info != null) {
                if (info.getType() == ConnectivityManager.TYPE_WIFI) {
                    netType = "WIFI";
                } else {
                    netType = info.getSubtypeName();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (wifiNetInfo != null && wifiNetInfo.isConnected()) {
            //WIFI连接
            netConnectedListener.netContent(2, netType);
        } else if (mobNetInfo != null && mobNetInfo.isConnected()) {
            //移动网络连接
            netConnectedListener.netContent(1, netType);
        } else {
            //WIFI和移动网络均未连接
            netConnectedListener.netContent(0, netType);
        }


    }

    public void setNetConnectedListener(NetConnectedListener netConnectedListener) {
        this.netConnectedListener = netConnectedListener;
    }

    public interface NetConnectedListener {
        void netContent(int netState, String netType);
    }
}
