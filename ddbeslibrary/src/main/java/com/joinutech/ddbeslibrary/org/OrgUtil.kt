package com.joinutech.ddbeslibrary.org

import androidx.lifecycle.MutableLiveData
import com.joinutech.common.util.CompanyHolder
import com.joinutech.ddbeslibrary.bean.WorkStationBean
import com.joinutech.ddbeslibrary.utils.COMPANIES
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.MMKVUtil
import com.joinutech.ddbeslibrary.utils.StringUtils
import com.marktoo.lib.cachedweb.LogUtil.showLog

/**
 * @Description: 团队管理工具类
 * @Author: zhaoyy
 * @Time:  2020-03-12 15:48
 * @Company: Copyright (C),2017- 2020,JoinuTech
 */
fun getAllCompanies(): HashMap<String, WorkStationBean> {
    val companyMap = hashMapOf<String, WorkStationBean>()
    val json = MMKVUtil.getString(COMPANIES, "")
    showLog("all companies is $json")

    if (StringUtils.isNotBlankAndEmpty(json)) {
        val companies = GsonUtil.fromJson(json, Array<WorkStationBean>::class.java)
        if (companies != null && companies.isNotEmpty()) {
            companyMap.putAll(companies.filter { StringUtils.isNotBlankAndEmpty(it.companyId) }.map { it.companyId to it })
        }
    }
    return companyMap
}

object GlobalCompanyHolder {
    val currentCompany = MutableLiveData<WorkStationBean?>()
    val companyUpdateResult = MutableLiveData<CompanyUpdateBean>()

    /**
     * 侧滑菜单切换当前团队
     * 获取团队列表后更新当前团队
     */
    fun setCurrentCompany(work: WorkStationBean?) {
        CompanyHolder.saveCurrentOrg(work)
        currentCompany.value = work
    }
}

class CompanyUpdateBean(val type: Int, val data: List<WorkStationBean>?)