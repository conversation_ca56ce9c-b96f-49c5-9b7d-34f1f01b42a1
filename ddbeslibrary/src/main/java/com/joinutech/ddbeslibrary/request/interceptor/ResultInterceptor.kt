package com.joinutech.ddbeslibrary.request.interceptor

import android.util.Log
import com.joinutech.common.base.isDebug
import com.joinutech.common.base.tokenKey
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.request.exception.ErrorType
import com.joinutech.ddbeslibrary.service.LoginService
import okhttp3.Interceptor
import okhttp3.MediaType
import okhttp3.Response
import okhttp3.ResponseBody
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.lang.Long
import java.nio.charset.StandardCharsets.UTF_8

/**
 * 请求结果拦截器
 * 拦截判断请求返回结果，
 * 请求失败时，移除data，避免返回数据进行解析造成请求异常
 * token失效时，重新获取token后，再重新尝试请求
 *
 * 结果token是否过期认证，主要是通过 code = -2来判断
 */
class ResultInterceptor : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        return resultIntercept(chain)
    }

    private fun resultIntercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val originalResponse = chain.proceed(request)
        val responseBody = originalResponse.body()
        val source = responseBody?.source()
        source?.request(Long.MAX_VALUE)
        val buffer = source?.buffer
        var charset = UTF_8
        val contentType = responseBody?.contentType()
        if (contentType != null) {
            charset = contentType.charset(UTF_8)
        }
        val bodyStr = buffer?.clone()?.readString(charset)
        val url = request.url().encodedPath()
//        RequestHelper.showLogLine("-->result interceptor->response > request_url=$url")
//        RequestHelper.showLogLine("-->result interceptor->response > response=${bodyStr ?: "null"}")
        if (!bodyStr.isNullOrBlank()) {
            if (bodyStr.startsWith("{") /*|| bodyStr.startsWith("[")*/) {
                val jsonObject = JSONObject(bodyStr)
//                printFormat(url, bodyStr)//已经在日志的输出中打印了，这里就不需要再次打印了
                if (jsonObject.has("code") && jsonObject.get("code") is Int) {
                    val code = jsonObject.get("code") as Int
                    when {
                        code == ErrorType.ERROR_AUTH_FAILED -> {
                            val time = System.currentTimeMillis()
                            /**token失效，发起同步请求*/
                            // RequestHelper.showLogLine("拦截请求结果===特殊处理=token失效，重新请求")
                            //token过期了(code为-2，且是没刷新过token)
//                            val newToken = getNewToken()
                            val newToken = UserHolder.getAccessToken()
                            return if (newToken.isNullOrBlank()) {
                                RequestHelper.showLogLine("拦截响应==${url}===\n=====code=(${code})==重新获取的token为空")
                                clearData(jsonObject, contentType, originalResponse)
                            } else {
                                //重新拼装请求头
                                //   RequestHelper.showLogLine("拦截请求结果===特殊处理=取到新token==" + "重试原请求：$url")
                                RequestHelper.showLogLine("拦截响应==${url}===\n====code=(${code})=重新获取token成功，再次发起请求===")
                                val newRequest = request.newBuilder().header(tokenKey, newToken).build()
                                chain.proceed(newRequest)//重试request
                            }
                        }
                        code != ErrorType.SUCCESS -> {// 1802 还未加入考勤组
                            RequestHelper.showLogLine("拦截响应=${url}===\n==异常处理==失败code=${code}=")
                            return clearData(jsonObject, contentType, originalResponse)
                        }
                    }
                }
            }
        }
        return originalResponse
    }

    private fun printFormat(header: String, json: String) {
        FormatLogger.printJson("http response", json, header)
    }

    private fun clearData(jsonObject: JSONObject, contentType: MediaType?, originalResponse: Response): Response {
        if (jsonObject.has("data")) {
            jsonObject.remove("data")
        }
      //  RequestHelper.showLogLine("拦截请求结果=异常处理=token为空或者请求失败==清理后的数据：${jsonObject}")
        return originalResponse.newBuilder()
                .body(ResponseBody.create(contentType, jsonObject.toString()))
                .build()
    }
}

object FormatLogger {
    val LINE_SEPARATOR = System.getProperty("line.separator") ?: "\n"
    var showLog = true
    private fun printLine(tag: String, isTop: Boolean) {
        if (isTop) {
            Log.d(tag, "╔═══════════════════════════════════════════════════════════════════════════════════════");
        } else {
            Log.d(tag, "╚═══════════════════════════════════════════════════════════════════════════════════════");
        }
    }

    fun printJson(tag: String, msg: String, headString: String) {
        if (!showLog) return
        var message = ""
        try {
            message = when {
                msg.startsWith("{") -> {
                    val jsonObject = JSONObject(msg)
                    jsonObject.toString(4)
                    //最重要的方法，就一行，返回格式化的json字符串，其中的数字4是缩进字符数
                }
                msg.startsWith("[") -> {
                    val jsonArray = JSONArray(msg)
                    jsonArray.toString(4)
                }
                else -> {
                    msg
                }
            }
        } catch (e: JSONException) {
            message = msg
        }

        printLine(tag, true);
        message = headString + LINE_SEPARATOR + message;
        val lines = message.split(LINE_SEPARATOR)
        for (line in lines) {
            Log.d(tag, "║ $line")
        }
        printLine(tag, false)
    }
}