package com.joinutech.ddbeslibrary.request.interceptor

import com.joinutech.ddbeslibrary.utils.MMKVUtil
import com.marktoo.lib.cachedweb.LogUtil
import okhttp3.Dns
import java.net.InetAddress
import java.net.UnknownHostException

/**
 * 结果token是否过期认证，主要是通过 code = -2来判断
 */
class DnsCharge private constructor() : Dns {
    override fun lookup(hostname: String): MutableList<InetAddress> {
        return try {
            val ipAddress = Dns.SYSTEM.lookup(hostname)
            if (!ipAddress.isNullOrEmpty()) {
                MMKVUtil.saveString(hostname, ipAddress[0].hostAddress)
                LogUtil.showLog("解析到$hostname 的IP地址为${MMKVUtil.getString(hostname)}")
            }
            ipAddress
        } catch (e: UnknownHostException) {
            LogUtil.showLog("解析到$hostname 的IP地址异常")
            val hostIp = MMKVUtil.getString(hostname, "")
            if (!hostIp.isNullOrBlank()) {
                LogUtil.showLog("找到缓存记录中IP地址为${MMKVUtil.getString(hostname)}")
                InetAddress.getAllByName(hostIp).toMutableList()
            } else if (hostname.contains("passport.inddbes.com")) {
                LogUtil.showLog("手动配置IP地址为${DEV_SERVER_HOST}")
                InetAddress.getAllByName(DEV_SERVER_HOST).toMutableList()
            } else if (hostname.contains("dev.inddbes.com")) {
                LogUtil.showLog("手动配置IP地址为${DEV_AUTH_HOST}")
                InetAddress.getAllByName(DEV_AUTH_HOST).toMutableList()
            } else {
                LogUtil.showLog("IP地址未找到，抛出异常")
                throw e
            }
        }
    }

    companion object {
//        private const val DEV_AUTH_HOST = "************:9098"
//        private const val DEV_SERVER_HOST = "************:8768"
        private const val DEV_AUTH_HOST = "************"
        private const val DEV_SERVER_HOST = "************"
        val INSTANCE: DnsCharge by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) { DnsCharge() }
    }
}