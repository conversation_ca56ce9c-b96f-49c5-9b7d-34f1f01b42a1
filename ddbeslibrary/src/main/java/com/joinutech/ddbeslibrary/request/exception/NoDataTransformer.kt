package com.joinutech.ddbeslibrary.request.exception

import com.joinutech.ddbeslibrary.bean.TokenBean
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.utils.GsonUtil
import io.reactivex.Flowable
import io.reactivex.FlowableTransformer
import io.reactivex.functions.Function
import org.reactivestreams.Publisher

/**
 * @className: NoDataTransformer
 * @desc: Result中data字段不需要返回数据情况的处理
 * @author: zyy
 * @date: 2019/6/14 11:45
 * @company: joinUTech
 * @leader: ke
 */
object NoDataTransformer : FlowableTransformer<Result<Any>, Any> {
    override fun apply(upstream: Flowable<Result<Any>>): Publisher<Any> {
        return upstream.map<Any> { httpResult ->
            // 通过对返回码进行业务判断决定是返回错误还是正常取数据
            //                if (httpResult.getCode() != 200) throw new RuntimeException(httpResult.getMessage());
            if (httpResult.code != ErrorType.SUCCESS) {
                throw ServerException(httpResult.msg, httpResult.code!!)
            }
            ""
        }.onErrorResumeNext(Function<Throwable, Publisher<Any>> { throwable ->
            throwable.printStackTrace()
            Flowable.error<Any>(ExceptionEngine.handleException(throwable))
        })
    }

}

class OnlyDataTransformer<T> : FlowableTransformer<T, T> {
    override fun apply(upstream: Flowable<T>): Publisher<T> {
        return upstream.map<T> { httpResult ->
            // 通过对返回码进行业务判断决定是返回错误还是正常取数据
            val result = getTokenResult(httpResult)
            if (result is TokenBean) {
                result
            } else if (result is Result<*>) {
                throw ServerException(result.msg, result.code!!)
            }
            null
        }.onErrorResumeNext(Function<Throwable, Publisher<T>> { throwable ->
            throwable.printStackTrace()
            Flowable.error<T>(ExceptionEngine.handleException(throwable))
        })
    }

    /**解析token bean 数据*/
    private fun getTokenResult(data: Any?): Any? {
        if (data != null && data is Map<*, *>) {
            val json = GsonUtil.toJson(data)
            return when {
                data.containsKey("access_token") -> {
                    GsonUtil.fromJson<TokenBean>(json)
                }
                data.containsKey("code") -> {
                    GsonUtil.fromJson<Result<String>>(json)
                }
                else -> {
                    val result: Result<String> = Result()
                    result.code = 0
                    result.msg = "请求失败"
                    result
                }
            }
        } else {
            val result: Result<String> = Result()
            result.code = 0
            result.msg = "请求失败"
            return result
        }
    }

}