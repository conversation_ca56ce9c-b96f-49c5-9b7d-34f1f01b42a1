package com.joinutech.ddbeslibrary.request.gson

import android.annotation.SuppressLint
import com.google.gson.TypeAdapter
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.request.Result
import okhttp3.ResponseBody
import org.json.JSONObject
import retrofit2.Converter

/**
 * Description TODO 请求体Converter
 * Author HJR36
 * Date 2018/4/12 19:16
 */
class DdbesGsonResponseBodyConverter<T>(private var adapter: TypeAdapter<T>)
    : Converter<ResponseBody, T> {

    @SuppressLint("WrongConstant")
    override fun convert(body: ResponseBody?): T? {
        return try {
            val response = body?.string()
//          Loggerr.i("-----完整数据---","----查看code值---"+response)
//            RequestHelper.showLogLine("resultJson: $response")
//            RequestHelper.showLogLine("response json 解析前： $response")
            if (response.isNullOrBlank()) {
                null
            } else {
//                val test = "{\"code\":1,\"msg\":\"请求成功\",\"data\":\"\"}"
                val json = JSONObject(response)
                if (json.has("access_token") && json.has("refresh_token")) {
                    val temp = adapter.fromJson(response)
//                    RequestHelper.showLogLine("resultObj: ${GsonUtil.toJson(temp)}")
                    temp
                } else {
                    val temp = try {
                        adapter.fromJson(response)
                    } catch (e: Exception) {
                        RequestHelper.showLogLine("接口请求数据解析失败，response json convert exception ${e.message}")
                        val data = Result<Any>()
                        data.code = 0
                        data.msg = "数据错误"
                        data.data = null
                        data as T
                    }
//                    RequestHelper.showLogLine("resultObj: ${GsonUtil.toJson(temp)}")
                    temp
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            RequestHelper.showLogLine("json 解析出错[${e.message}]，返回null")
            null
        } finally {
            body?.close()
        }
    }

}