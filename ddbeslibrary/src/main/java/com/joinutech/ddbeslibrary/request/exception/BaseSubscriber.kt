package com.joinutech.ddbeslibrary.request.exception

import io.reactivex.Observer
import io.reactivex.subscribers.DefaultSubscriber


/**
 * Description TODO
 * Author HJR36
 * Date 2018/6/25 17:30
 */
abstract class BaseSubscriber<T>: DefaultSubscriber<T>() {

    override fun onError(t: Throwable?) {
        if (t is ApiException){
            onError(t)
        }else{
            onError(ApiException(t!!, 1000,""))
        }
    }

    /**
     * 错误回调
     */
    protected abstract fun onError(ex: ApiException)
}

abstract class BaseObserver<T>: Observer<T> {
    override fun onError(t: Throwable) {
        if (t is ApiException){
            onError(t)
        }else{
            onError(ApiException(t!!, 1000,""))
        }
    }

    /**
     * 错误回调
     */
    protected abstract fun onError(ex: ApiException)
}