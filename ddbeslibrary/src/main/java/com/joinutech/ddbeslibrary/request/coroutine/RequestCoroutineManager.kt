package com.joinutech.ddbeslibrary.request.coroutine

import android.os.Build
import com.joinutech.common.base.BASE_URL
import com.joinutech.common.base.REQUEST_TIMEOUT_CONNECT
import com.joinutech.common.base.REQUEST_TIMEOUT_READ
import com.joinutech.common.base.REQUEST_TIMEOUT_WRITE
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.request.SSLSocketFactoryUtils
import com.joinutech.ddbeslibrary.request.TrustAllHostnameVerifier
import com.joinutech.ddbeslibrary.utils.toast
import okhttp3.ConnectionSpec
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Response
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * @Des：专为协程使用 api request
 * @author: moon
 * @date: 3/11/23
 */
object RequestCoroutineManager {

    val retrofit = Retrofit.Builder()
        .baseUrl(BASE_URL)
        .client(getRecipeOkHttpClient())
        .addConverterFactory(GsonConverterFactory.create())
        .build()

    private fun getRecipeOkHttpClient(): OkHttpClient {
        val level = HttpLoggingInterceptor.Level.BODY
        val loggingInterceptor = HttpLoggingInterceptor { message ->
            println(message)
        }
        loggingInterceptor.level = level

        val builder = OkHttpClient.Builder().apply {
            readTimeout(REQUEST_TIMEOUT_READ, TimeUnit.SECONDS)
            connectTimeout(REQUEST_TIMEOUT_CONNECT, TimeUnit.SECONDS)
            writeTimeout(REQUEST_TIMEOUT_WRITE , TimeUnit.SECONDS)
            retryOnConnectionFailure(true)
            addInterceptor(AuthInterceptor())
            addInterceptor(loggingInterceptor)
                .sslSocketFactory(SSLSocketFactoryUtils.createSSLSocketFactory(), SSLSocketFactoryUtils.createTrustAllManager())//忽略所有证书
                .hostnameVerifier(TrustAllHostnameVerifier())//忽略所有证书
        }
        if (Build.VERSION.SDK_INT < 21) {
            builder.connectionSpecs(arrayListOf(ConnectionSpec.CLEARTEXT))
        }
        return builder.build()
    }

}

class AuthInterceptor : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request().newBuilder()
            .addHeader("Content-Type", "application/json")
            .addHeader("Accept", "application/json")
            .addHeader("Authorization", "Bearer ${UserHolder.getUserToken()}")
//            .addHeader("ImToken", "${UserHolder.getImTokenBean()?.token ?:""} ")
            .addHeader("ImToken", "${UserHolder.getImToken()} ")
            .build()
        return chain.proceed(request)
    }
}

suspend fun<T : Any> safeApiCall(call: suspend () -> NetResponse<T>): NetResponse<T> {
    return try {
        val r = call()
        if (r.code != 1){
            if (r.code != -4){
                toast(r.msg)
            }
        }
        r
    } catch (e: Exception) {
        e.printStackTrace()
        if ((e is java.net.SocketTimeoutException)) {
//            toast("当前网络不畅，请检查您的网络设置!")
            return NetResponse(msg = "当前网络不畅，请检查您的网络设置!", code = 0 , data = null)
        } else {
//            toast("请求异常,请稍后重试")
            return NetResponse(msg = "请求异常,请稍后重试", code = 0, data = null)
        }
    }
}


data class NetResponse<T>(var code: Int, var data: T?, var msg: String) {
    fun success() = code == 1

    fun isExpired() = code == -3 || code == -2
}


