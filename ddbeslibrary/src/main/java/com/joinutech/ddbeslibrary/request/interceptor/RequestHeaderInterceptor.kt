package com.joinutech.ddbeslibrary.request.interceptor

import android.os.Build
import com.joinutech.common.base.ServerEnv
import com.joinutech.common.base.local
import com.joinutech.common.base.tokenKey
import com.joinutech.common.util.UserHolder
import com.joinutech.ddbeslibrary.BuildConfig
import com.joinutech.ddbeslibrary.request.RequestHelper
import com.joinutech.ddbeslibrary.request.exception.ErrorType
import com.joinutech.ddbeslibrary.service.LoginService
import com.joinutech.ddbeslibrary.utils.*
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import okhttp3.ResponseBody
import org.json.JSONObject

/**
 * 请求头拦截器
 * 添加公共的请求头
 * 判断token是否有效，token失效时，会先请求token再执行请求
 */
class RequestHeaderInterceptor : Interceptor {

    /**
     * 不需要token的请求，登录之前的一些请求处理
     * 登录后请求判断token是否过期
     *      如果token过期，则重新请求token, 如果请求失败，则重新登录
     *      如果token未过期，则继续执行请求
     *      */
    override fun intercept(chain: Interceptor.Chain): Response {
        val originRequest = chain.request()
        val url = originRequest.url().toString()
        val imei = MMKVUtil.getString(MMKVKEY.IMEI)

        val deviceModel = DeviceUtil.getValueEncoded(ConsValue.deviceModel)
        val builder = originRequest.newBuilder()
                .header("Authorization", "Bearer ${UserHolder.getUserToken()}")
                .header("appVersion", BuildConfig.VERSION_NAME)// 3.0.5 恢复版本名称
                .header("version", Build.VERSION.RELEASE)// 2.3.6版本后增加系统版本上传
                .header("device", imei)
                .header("platform", "android")
        if (!deviceModel.isNullOrBlank() && deviceModel != "null") {
            builder.header("deviceModel", deviceModel)
        }

        RequestHelper.showLogLine("拦截请求头=${url}=\n=所有的请求头参数= \n${originRequest.headers()}")

        builder.method(originRequest.method(), originRequest.body())

        return chain.proceed(builder.build())

        /*return if (CommonUtils.verifyRequestHeader(url)) {
            val afterRequest = builder.build()
//            RequestHelper.showLogLine("拦截请求头=${url}=\n=不需要token=所有的请求头参数= \n${afterRequest.headers()}")
//            val headerSize = afterRequest.headers().size()
//            RequestHelper.showLogLine("拦截请求头==url=[$url]")
            chain.proceed(afterRequest)
        } else {// 需要token
            val time = System.currentTimeMillis()
            val token = UserHolder.getAccessToken() ?:""
            if (token.isNullOrBlank()) { // 需要token，已登录，获取token失败
                RequestHelper.showLogLine("拦截请求头=${url}======\n====获取token失败 或者 token不存在，尝试重新登录")
                reTryLogin(chain, originRequest,url)
            } else {
                val afterRequest = builder.header(tokenKey, token).build()
                RequestHelper.showLogLine("拦截请求头=${url}=====\n=====获取token成功 或者 token存在===所有的请求头参数= \n${afterRequest.headers()}")
                chain.proceed(afterRequest)
            }
        }*/
    }

    private fun reTryLogin(chain: Interceptor.Chain, originRequest: Request,currentUrl:String?): Response {
        val originalResponse = chain.proceed(originRequest)
        val responseBody = originalResponse.body()
        RequestHelper.showLogLine("拦截请求头=${currentUrl}===\n=====token为空，正在重新登录")
        val result = JSONObject()
        result.put("code", ErrorType.ERROR_RELOGIN)
        result.put("msg", "请重新登录")
        return originalResponse.newBuilder()
                .body(ResponseBody.create(responseBody?.contentType(), result.toString()))
                .build()
    }

}