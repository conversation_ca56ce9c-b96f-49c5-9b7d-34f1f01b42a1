package com.joinutech.ddbeslibrary.request.downloadFile

import okhttp3.Interceptor
import okhttp3.Response

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/11/28 9:39
 * @packageName: com.joinutech.ddbeslibrary.request.downloadFile
 * @Company: JoinuTech
 */
class JsDownloadInterceptor(private val downloadListener: JsDownloadListener) : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val response = chain.proceed(chain.request())
        return response.newBuilder().body(
                JsResponseBody(response.body(), downloadListener)).build()
    }

}