package com.joinutech.ddbeslibrary.request.exception;

import com.joinutech.ddbeslibrary.request.RequestHelper;
import com.joinutech.ddbeslibrary.request.Result;

import org.reactivestreams.Publisher;

import io.reactivex.Flowable;
import io.reactivex.FlowableTransformer;
import io.reactivex.functions.Function;

import static com.joinutech.ddbeslibrary.request.exception.ErrorType.DATA_NULL;

/**
 * Created by York on 2016/7/23.
 * 加入了对错误处理，已经比较完整了
 */
public class ErrorTransformer<T> implements FlowableTransformer<Result<T>, T> {

    public static <T> ErrorTransformer<T> create() {
        return new ErrorTransformer<>();
    }

    private static ErrorTransformer instance = null;

    private ErrorTransformer() {
    }

    /**
     * 双重校验锁单例(线程安全)
     */
    public static <T> ErrorTransformer<T> getInstance() {
        if (instance == null) {
            synchronized (ErrorTransformer.class) {
                if (instance == null) {
                    instance = new ErrorTransformer<>();
                }
            }
        }
        return instance;
    }

    @Override
    public Publisher<T> apply(Flowable<Result<T>> upstream) {
        return upstream.map(new Function<Result<T>, T>() {
            @Override
            public T apply(Result<T> httpResult) throws Exception {
                // 通过对返回码进行业务判断决定是返回错误还是正常取数据
//                if (httpResult.getCode() != 200) throw new RuntimeException(httpResult.getMessage());
                RequestHelper.INSTANCE.showLogLine("请求结果返回code [" + httpResult.getCode() + "]");
                if (httpResult.getCode() != ErrorType.SUCCESS) {
                    throw new ServerException(httpResult.getMsg(), httpResult.getCode());
                }
                T result = httpResult.getData();
                if (result == null) {
                    throw new ServerException("请求失败", DATA_NULL);
                }
                return result;
            }
        }).onErrorResumeNext(new Function<Throwable, Publisher<? extends T>>() {
            @Override
            public Publisher<? extends T> apply(Throwable throwable) throws Exception {
                //ExceptionEngine为处理异常的驱动器
                throwable.printStackTrace();
                return Flowable.error(ExceptionEngine.handleException(throwable));
            }
        });
    }
}
