package com.joinutech.ddbeslibrary.request.downloadFile;

import android.util.Log;
import org.jetbrains.annotations.NotNull;
import java.io.IOException;
import okhttp3.MediaType;
import okhttp3.ResponseBody;
import okio.Buffer;
import okio.BufferedSource;
import okio.ForwardingSource;
import okio.Okio;
import okio.Source;

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/11/28 9:44
 * @packageName: com.joinutech.ddbeslibrary.request.downloadFile
 * @Company: JoinuTech
 */
public class JsResponseBody extends ResponseBody {
    private ResponseBody responseBody;
    private JsDownloadListener downloadListener;
    // BufferedSource 是okio库中的输入流，这里就当作inputStream来使用。
    private BufferedSource bufferedSource;
    public JsResponseBody(ResponseBody responseBody, JsDownloadListener downloadListener) {
        this.responseBody = responseBody;
        this.downloadListener = downloadListener;
        downloadListener.onStartDownload(responseBody.contentLength());
    }
    @Override
    public MediaType contentType() {
        return responseBody.contentType();
    }
    @Override
    public long contentLength() {
        return responseBody.contentLength();
    }
    @NotNull
    @Override
    public BufferedSource source() {
        if (bufferedSource == null) {
            bufferedSource = Okio.buffer(source(responseBody.source()));
        }
        return bufferedSource;
    }
    private Source source(Source source) {
        return new ForwardingSource(source) {
            long totalBytesRead = 0L;
            @Override
            public long read(@NotNull Buffer sink, long byteCount) throws IOException {
                long bytesRead = super.read(sink, byteCount);
                totalBytesRead += bytesRead != -1 ? bytesRead : 0;
                int progress = (int) (
                        totalBytesRead * 100 / responseBody.contentLength());
                Log.e("download", "read: "+ progress);
                if (null != downloadListener) {
                    if (bytesRead != -1) {
                        if (progress !=100){
                            downloadListener.onProgress(progress);
                        }else {
                            if (sink.size()!=0 && sink.size()!=499)downloadListener.onSuccess();
                        }
                    }
                }
                return bytesRead;
            }
        };
    }
}
