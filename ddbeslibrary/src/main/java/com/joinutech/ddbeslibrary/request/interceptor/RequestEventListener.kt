package com.joinutech.ddbeslibrary.request.interceptor

import com.joinutech.common.base.BASE_URL
import com.joinutech.common.base.isDebug
import com.joinutech.ddbeslibrary.BuildConfig
import com.joinutech.ddbeslibrary.base.BaseApplication
import com.joinutech.ddbeslibrary.db.data.RequestLog
import com.joinutech.ddbeslibrary.db.ope.RequestLogOpe
import com.joinutech.ddbeslibrary.request.Result
import com.joinutech.ddbeslibrary.utils.GsonUtil
import com.joinutech.ddbeslibrary.utils.Loggerr
import com.joinutech.ddbeslibrary.utils.XUtil
import com.marktoo.lib.cachedweb.LogUtil
import com.zhy.http.okhttp.OkHttpUtils
import io.reactivex.Flowable
import okhttp3.*
import okhttp3.EventListener
import retrofit2.http.Body
import retrofit2.http.POST
import java.io.IOException
import java.util.*
import java.util.concurrent.Executors
import java.util.concurrent.atomic.AtomicLong
import kotlin.collections.ArrayList

/**
 * @Description: TODO
 * @Author: zhaoyy
 * @Time: 2021/2/10 17:10
 * @packageName: com.joinutech.ddbeslibrary.request.interceptor
 * @Company: Copyright (C),2019- 2021,JoinuTech
 * OkHttpClient.Builder().eventListener()->eventListenerFactory
 * okHttpClient.eventListenerFactory
 */
class RequestEventListener(
    private val callId: Long,
    val url: HttpUrl,
    val method: String,
    private val startTime: Long
) : EventListener() {

    object RequestEventFactory : Factory {
        private val nexCallId = AtomicLong(1L)
        override fun create(call: Call): EventListener {
            return RequestEventListener(
                nexCallId.getAndIncrement(),
                call.request().url(),
                call.request().method(),
                System.currentTimeMillis()
            )
        }
    }

    val cache = RequestCache()

    //往数据库插入一条日志
    private fun recordLog(eventName: String) {
        if (eventName == "start") {
//            cache.onStart("${callId}_${url.url().path}")
        } else {
//            cache.onEnd("${callId}_${url.url().path}_${System.currentTimeMillis() - startNanos}")
            val resultCode = if (eventName == "end") {
                1
            } else {
                0
            }
            val url = url.toString()

            val methodCode = when {
                method.contains("GET", false) -> 1
                method.contains("POST", false) -> 2
                method.contains("PUT", false) -> 3
                method.contains("DELETE", false) -> 4
                method.contains("PATCH", false) -> 5
                else -> 6// web
            }
            cache.log(
                callId,
                url,
                methodCode,
                startTime,
                System.currentTimeMillis(),
                resultCode
            )
        }
    }

    override fun callStart(call: Call) {
        recordLog("start")
        super.callStart(call)
    }

    override fun callEnd(call: Call) {
        recordLog("end")
        super.callEnd(call)
    }

    override fun callFailed(call: Call, ioe: IOException) {
        recordLog("failed")
        RequestCache.showLog("请求失败，错误内容: ${ioe.message}")
        super.callFailed(call, ioe)
    }

}

class RequestCache {

    companion object {

        fun showLog(msg: String) {
            LogUtil.showLog(msg, "requestTime__")
        }

        val appVersion: Int by lazy {
            BuildConfig.innerCode.toInt()
        }

        var netType: String = "NONE"

        fun changeNetType(currentNetType: String) {
            netType = currentNetType
        }

        private var currentDayTime = 0L
        private var calendar: Calendar? = null

        /**加载请求日志缓存，发现今天之前日志后提交接口*/
        private fun initCache() {
            if (calendar == null) {
                calendar = Calendar.getInstance()
            }
            if (!isDebug) {
                calendar?.set(Calendar.HOUR_OF_DAY, 0)
                calendar?.set(Calendar.MINUTE, 0)
                calendar?.set(Calendar.SECOND, 0)
                calendar?.set(Calendar.MILLISECOND, 0)
            }

            currentDayTime = calendar?.timeInMillis ?: 0L
            Loggerr.i("上传日志", "====当日的时间戳=${currentDayTime}")
        }

        //在启动页开始上传日志
        fun checkUploadLog() {
            initCache()//更新当天的时间戳

            fun uploadLogSplit() {
                val pageSize = 10

                fun uploadLogsByPages(pages: ArrayList<Int>) {
                    val logDatas = arrayListOf<LogData>()
                    pages.forEach { page ->
                        val requestLogs = findBeforeTodayLog(page, pageSize)
                        Loggerr.i(
                            "上传日志",
                            "===page=${page}==最大时间=${requestLogs.maxByOrNull { it.startTime }?.startTime}=="
                        )
                        //因为上传到服务器的json，不能带有字段id，所以这里转化一下；
                        val logs = requestLogs.map {
                            RequestLogTemp(
                                it.method,
                                it.url, it.startTime, it.endTime, it.code, it.network, it.appVersion
                            )
                        }
                        logDatas.add(LogData(logs))
                    }
                    //太多数据时，删除前面的一部分
                    if (pages.isNotEmpty() && pages.get(0) != 0) {
                        val firstPage = pages.get(0)
                        val logsTemp = findBeforeTodayLog(firstPage, pageSize)
                        val minBeanStartTime = logsTemp.minByOrNull { it.startTime }?.startTime
                        clearBeforeUpdate(minBeanStartTime)
                    }

                    uploadLogsLastDay(logDatas, onSuccess = {
                        clearBeforeUpdate(currentDayTime)
                        Loggerr.i("上传日志", "===上传完成==")
                    }, onFailer = {})
                }

                findLogCount().let { count ->
                    Loggerr.i("上传日志", "====日志数量=${count}")
                    if (count > 10L) {
                        var page = (count / pageSize).toInt()
                        val last = (count % pageSize).toInt()
                        if (last > 0) {
                            page += 1
                        }
                        if (page > 0) {
                            Loggerr.i("上传日志", "====需要上传 ${page} 页的日志")
                            val pages = arrayListOf<Int>()
                            val resultPages = arrayListOf<Int>()
                            for (i in 0 until page) {
                                pages.add(i)
                            }
//                            uploadLogByRx(pages)
                            if (pages.size > 100) {
                                resultPages.addAll(pages.takeLast(100))
                            } else {
                                resultPages.addAll(pages)
                            }
                            uploadLogsByPages(resultPages)
                        }
                    }
                }
            }

            uploadLogSplit()
        }

        //导致了内存溢出
        /* private fun uploadLogOnce(log: LogData, onSuccess: (Boolean) -> Unit) {
             DdbesApiUtil.getService(StatisticApi::class.java)
                 .uploadRequestLog(log)
                 .compose(ErrorTransformer.getInstance())
                 .subscribeOn(Schedulers.io())
                 .observeOn(Schedulers.io())
                 .subscribe(
                     object : BaseSubscriber<Any>() {
                         override fun onError(ex: ApiException) {
                             showLog("--^^^^^^ ${log.requestRecordDTOS.size} 条失败")
                             onSuccess.invoke(false)
                         }

                         override fun onComplete() {
                         }

                         override fun onNext(result: Any?) {
                             if (result != null) {
                                 showLog("--^^^^^^ ${log.requestRecordDTOS.size} 条成功")
                                 onSuccess.invoke(true)
                             } else {
                                 onSuccess.invoke(false)
                             }
                         }
                     })
         }*/

        //使用线程池控制线程
        private fun uploadLogsLastDay(
            dataList: List<LogData>,
            onSuccess: () -> Unit,
            onFailer: () -> Unit
        ) {
            var finishCount = 0
            val totalCount = dataList.size
            if (dataList.isNotEmpty()) {
                var failCountTag = 0//当累计失败5次或者以上时，就自动释放线程池
                if (BaseApplication.singleThreadExecutor == null) {
                    BaseApplication.singleThreadExecutor = Executors.newSingleThreadExecutor()
                }
                dataList.forEach { logData ->
                    val runnable = object : Runnable {
                        override fun run() {
                            val json = GsonUtil.toJson(logData)
                            var response: Response? = null
                            //使用自己封装的okhttp------开始-----OkhttpUtil2就是复制的OkhttpUtil，既然不使用，就把OkhttpUtil2删除了----
                            /* val type = MediaType.parse("application/json; charset=utf-8")
                             val requestBody = RequestBody.create(type, json)//生成requestBody
                             val requestData = Request.Builder()       //创建一个请求
                                 .url(BASE_URL + "statistic/statistic/record")
                             val request = requestData.post(requestBody).build()
                              response = OkhttpUtil2.getOkInstance()?.execute(request)*/
                            //使用自己封装的okhttp------结束---------

                            //使用okhttpUtils=========开始============
                            try {
                                response = OkHttpUtils.postString()
                                    .url(BASE_URL + "statistic/statistic/record")
                                    .content(json)
                                    .mediaType(MediaType.parse("application/json; charset=utf-8"))
                                    .build()
                                    .execute()
//                                Thread.sleep(2000)
                                Loggerr.i("上传日志", "==线程=${Thread.currentThread().name}===")
                            } catch (e: Exception) {
                                XUtil.errorLogToPrint(e)
                            }
                            //使用okhttpUtils=========结束============

//                            Loggerr.i("上传日志", "==结果==${response?.isSuccessful}===")
                            Loggerr.i("上传日志", "==结果==${response?.body()?.string()}===")
//                            Loggerr.i("上传日志", "==结果==${response?.message()}===")
                            if (response?.isSuccessful ?: false) {
                                finishCount++
                                Loggerr.i("上传日志", "====完成一页===")
                                if (finishCount == totalCount) {
                                    onSuccess.invoke()
                                }
                            } else {
                                failCountTag++
                                Loggerr.i("上传日志", "=====失败次数==${failCountTag}")
                                if (failCountTag > 5) {
                                    //释放线程池
                                    if (BaseApplication.singleThreadExecutor != null) {
                                        BaseApplication.singleThreadExecutor?.shutdownNow()
                                        BaseApplication.singleThreadExecutor = null
                                    }
                                    //删除数据库中的日志，防止累计的太多，出现问题
                                    clearBeforeUpdate(currentDayTime)
                                }
                            }

                            val maxLog = logData.requestRecordDTOS.maxByOrNull { it.startTime }
                            clearBeforeUpdate(maxLog?.startTime)
                        }
                    }
                    BaseApplication.singleThreadExecutor?.execute(runnable)
                }

            }
        }


        private fun findLogCount(): Long {
            return RequestLogOpe.getInstance()
                .findBeforeTodayLogCount(BaseApplication.joinuTechContext, currentDayTime)
        }

        private fun removeTooMatchLogs() {
            val count = findLogCount()
            if (count > 1000) {

            }
        }


        private fun findBeforeTodayLog(page: Int, pageSize: Int): List<RequestLog> {
            return RequestLogOpe.getInstance().findBeforeTodayLog(
                BaseApplication.joinuTechContext,
                currentDayTime, page, pageSize
            )
        }


        fun saveLog(log: RequestLog) {
            RequestLogOpe.getInstance().insert(BaseApplication.joinuTechContext, log)
        }

        /**清理之前未上传的日志*/
        private fun clearBeforeUpdate(startTime: Long?) {
            showLog("10---->清理之前缓存")
            if (startTime == null) {
                return
            }
            RequestLogOpe.getInstance().removeAll(BaseApplication.joinuTechContext, startTime)
        }

    }

    fun log(callId: Long, url: String, method: Int, start: Long, end: Long, resultCode: Int) {
        if (url.contains("statistic/statistic/record")) {
            return
        }
        saveLog(RequestLog(method, url, start, end, resultCode, netType, appVersion))
    }

//    data class LogInfo(val url: String, val method: Int, val startTime: Long, val endTime: Long,
//                       val code: Int, val network: String, val appVersion: Int)

    data class LogData(val requestRecordDTOS: List<RequestLogTemp>)

    data class RequestLogTemp(
        val method: Int,
        val url: String,
        val startTime: Long,
        val endTime: Long,
        val code: Int,
        val network: String,
        val appVersion: Int
    )

    interface StatisticApi {
        // 上传请求日志
        @POST("statistic/statistic/record")
        fun uploadRequestLog(@Body requestBody: RequestBody): Flowable<Result<Any>>
    }
}
