package com.joinutech.ddbeslibrary.request.downloadFile

import com.joinutech.common.base.BASE_URL
import com.joinutech.ddbeslibrary.request.RetrofitClient
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/4/9.
 */
@Deprecated("no used")
class DownLoadRetrofitClientNew private constructor() {

    var mDownLoadRetrofit: Retrofit? = null

    companion object {
        val single_intance by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            DownLoadRetrofitClientNew()
        }
    }

    fun getRxRetrofit(listener: JsDownloadListener): Retrofit {
        val client: OkHttpClient = RetrofitClient.single_intance.client()
                .addInterceptor(JsDownloadInterceptor(listener))
                .build()

        if (mDownLoadRetrofit == null) {
            synchronized(DownLoadRetrofitClientNew::class.java) {
                if (mDownLoadRetrofit == null)
                    mDownLoadRetrofit = Retrofit.Builder()
                            .baseUrl(BASE_URL)
                            .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
                            .client(client)
                            .build()
            }
        }
        return mDownLoadRetrofit!!
    }

}