package com.joinutech.ddbeslibrary.request.coroutine

import android.os.Build
import com.joinutech.common.base.IM_OFFLINE_SERVER
import com.joinutech.common.base.REQUEST_TIMEOUT_CONNECT
import com.joinutech.common.base.REQUEST_TIMEOUT_READ
import com.joinutech.common.base.REQUEST_TIMEOUT_WRITE
import com.joinutech.ddbeslibrary.request.SSLSocketFactoryUtils
import com.joinutech.ddbeslibrary.request.TrustAllHostnameVerifier
import okhttp3.ConnectionSpec
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * @Des：专为协程使用 api request
 * @author: moon
 * @date: 3/11/23
 */
object RequestIMCoroutineManager {

    val retrofit : Retrofit? = null

    fun getBaseUrl(baseUrl: String? = IM_OFFLINE_SERVER) : Retrofit {
        return Retrofit.Builder()
            .baseUrl(baseUrl ?: IM_OFFLINE_SERVER)
            .client(getRecipeOkHttpClient())
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    private fun getRecipeOkHttpClient(): OkHttpClient {
        val level = HttpLoggingInterceptor.Level.BODY
        val loggingInterceptor = HttpLoggingInterceptor { message ->
            println(message)
        }
        loggingInterceptor.level = level

        val builder = OkHttpClient.Builder().apply {
            readTimeout(REQUEST_TIMEOUT_READ, TimeUnit.SECONDS)
            connectTimeout(REQUEST_TIMEOUT_CONNECT, TimeUnit.SECONDS)
            writeTimeout(REQUEST_TIMEOUT_WRITE , TimeUnit.SECONDS)
            addInterceptor(AuthInterceptor())
            addInterceptor(loggingInterceptor)
                .sslSocketFactory(SSLSocketFactoryUtils.createSSLSocketFactory(), SSLSocketFactoryUtils.createTrustAllManager())//忽略所有证书
                .hostnameVerifier(TrustAllHostnameVerifier())//忽略所有证书
        }
        if (Build.VERSION.SDK_INT < 21) {
            builder.connectionSpecs(arrayListOf(ConnectionSpec.CLEARTEXT))
        }
        return builder.build()
    }

}


