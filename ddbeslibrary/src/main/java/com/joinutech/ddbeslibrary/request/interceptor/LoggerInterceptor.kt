package com.joinutech.ddbeslibrary.request.interceptor

import android.os.Build
import android.os.Debug
import android.text.TextUtils
import android.util.Log
import com.joinutech.common.base.isDebug
import com.joinutech.ddbeslibrary.BuildConfig
import okhttp3.*
import okio.Buffer
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.io.IOException

class LoggerInterceptor @JvmOverloads constructor(tag: String, private val showResponse: Boolean = false) :
    Interceptor {
    private val tag: String

    private var last: Int = 0

    init {
        var tag = tag
        if (TextUtils.isEmpty(tag)) {
            tag = TAG
        }
        this.tag = tag
    }

    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        logForRequest(request)
        val response = chain.proceed(request)

        return logForResponse(response)
    }

    private fun logForRequest(request: Request) {
        try {
            if ((!isDebug) && (!BuildConfig.DEBUG)) return
            val url = request.url().toString()
            val headers = request.headers()

            Log.e(tag, "﹀ ﹀ ﹀ ﹀ ﹀ ﹀ ﹀ ﹀ ﹀ ﹀ ﹀请求 log start﹀ ﹀ ﹀ ﹀ ﹀ ﹀ ﹀ ﹀ ﹀ ﹀ ﹀")
            Log.e(tag, "method : " + request.method())
            Log.e(tag, "url : $url")
            if (headers != null && headers.size() > 0) {
//                Log.e(tag, "headers : \n")
//                Log.e(tag, headers.toString())
            }
            val requestBody = request.body()
            if (requestBody != null) {
                val mediaType = requestBody.contentType()
                if (mediaType != null) {
                    Log.e(tag, "contentType : " + mediaType.toString())
                    if (isText(mediaType)) {
                        Log.e(tag, "body : " + bodyToString(request))
                    } else {
                        val buffer = Buffer()
                        val copy = request.newBuilder().build()
                        copy.body()!!.writeTo(buffer)
                        val params =
                            buffer.readUtf8().split("&".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                        Log.e(tag, "params : \n")
                        val sb = StringBuilder()
                        for (param in params) {
                            sb.append(param.replace("=", ": ")).append("\n")
                        }
                        Log.e(tag, sb.toString())
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            Log.e(tag, "︿ ︿ ︿ ︿ ︿ ︿ ︿ ︿ ︿ ︿ ︿请求 log end︿ ︿ ︿ ︿ ︿ ︿ ︿ ︿ ︿ ︿ ︿")
        }
    }

    private fun logForResponse(response: Response): Response {
        try {
            Log.e(tag, "︾ ︾ ︾ ︾ ︾ ︾ ︾ ︾ ︾ ︾ ︾响应 log start︾ ︾ ︾ ︾ ︾ ︾ ︾ ︾ ︾ ︾ ︾")
            val builder = response.newBuilder()
            val clone = builder.build()
            Log.e(tag, "url : " + clone.request().url())
            Log.e(tag, "code : " + clone.code())
            Log.e(tag, "protocol : " + clone.protocol())
            if (!TextUtils.isEmpty(clone.message())) Log.e(tag, "message : " + clone.message())

            if (showResponse) {
                var body = clone.body()
                //                Buffer buffer = new Buffer();
                //                body.source().readAll(buffer);
                //                body = ResponseBody.create(MediaType.parse("application/json;charset=utf-8"),body.contentLength(),buffer);
                if (body != null) {
                    val mediaType = body.contentType()
                    //                    body.contentType() = MediaType.parse("application/json;charset=utf-8");
                    if (mediaType != null) {
                        Log.e(tag, "contentType : " + mediaType.toString())
                        if (isText(mediaType)) {
                            val resp = body.string()
//                            Log.e(tag, "content : $resp")
                            printJson(tag, resp, "")
                            body = ResponseBody.create(mediaType, resp)
                            return response.newBuilder().body(body).build()
                        } else {
                            Log.e(tag, "content : " + " maybe [file part] , too large too print , ignored!")
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            Log.e(tag, "︽ ︽ ︽ ︽ ︽ ︽ ︽ ︽ ︽ ︽ ︽响应 log end︽ ︽ ︽ ︽ ︽ ︽ ︽ ︽ ︽ ︽ ︽")
        }

        return response
    }

    private fun isText(mediaType: MediaType): Boolean {
        if (mediaType.type() != null && mediaType.type() == "text") {
            return true
        }
        if (mediaType.subtype() != null) {
            if (mediaType.subtype() == "json" ||
                mediaType.subtype() == "xml" ||
                mediaType.subtype() == "html" ||
                mediaType.subtype() == "webviewhtml"
            )
            //
                return true
        }
        return false
    }

    private fun bodyToString(request: Request): String {
        try {
            val copy = request.newBuilder().build()
            val buffer = Buffer()
            copy.body()!!.writeTo(buffer)
            return buffer.readUtf8()
        } catch (e: IOException) {
            return "something error when show requestBody."
        }

    }

    fun printJson(tag: String, msg: String, headString: String) {

        var message: String

        try {
            if (msg.startsWith("{")) {
                val jsonObject = JSONObject(msg)
                message = jsonObject.toString(4)
            } else if (msg.startsWith("[")) {
                val jsonArray = JSONArray(msg)
                message = jsonArray.toString(4)
            } else {
                message = msg
            }
        } catch (e: JSONException) {
            message = msg
        }

        //        final String ranTag =
        printLine(randomKey() + tag, true)
        message = headString + LINE_SEPARATOR + message
        val lines = message.split(LINE_SEPARATOR.toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        for (line in lines) {
            Log.e(randomKey() + tag, "║ $line")
        }

        printLine(randomKey() + tag, false)
    }

    fun printLine(tag: String, isTop: Boolean) {
        if (isTop) {
            Log.e(tag, "╔═══════════════════════════════════════════════════════════════════════════════════════")
        } else {
            Log.e(tag, "╚═══════════════════════════════════════════════════════════════════════════════════════")
        }
    }

    fun randomKey(): String {
        var random = (10 * Math.random()).toInt()
        if (random == last) {
            random = (random + 1) % 10
        }
        last = random
        return random.toString()
    }

    companion object {

        val LINE_SEPARATOR = System.getProperty("line.separator")
        val TAG = "Credit"
    }

}