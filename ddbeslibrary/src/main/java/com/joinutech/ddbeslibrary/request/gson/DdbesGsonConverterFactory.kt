package com.joinutech.ddbeslibrary.request.gson

import com.google.gson.Gson
import com.google.gson.TypeAdapter
import com.google.gson.TypeAdapterFactory
import com.google.gson.reflect.TypeToken
import com.joinutech.ddbeslibrary.utils.GsonUtil
import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.Converter
import retrofit2.Retrofit
import java.lang.reflect.Type
import java.util.concurrent.CopyOnWriteArrayList

/**
 * Description TODO Gson控制器
 * Author HJR36
 * Date 2018/4/12 17:38
 */
class DdbesGsonConverterFactory(private val gson: Gson) : Converter.Factory() {

    companion object {

        fun create(): DdbesGsonConverterFactory {
            return create(GsonUtil.getGson())
        }

        fun create(gson: Gson): DdbesGsonConverterFactory {
            return DdbesGsonConverterFactory(gson)
        }
    }


    override fun requestBodyConverter(type: Type?, parameterAnnotations: Array<out Annotation>?,
                                      methodAnnotations: Array<out Annotation>?, retrofit: Retrofit?): Converter<*, RequestBody>? {
        val adapter: TypeAdapter<*> = gson.getAdapter(TypeToken.get(type))
        return DdbesGsonRequestBobyConverter(gson, adapter)
    }

    override fun responseBodyConverter(type: Type?, annotations: Array<out Annotation>?,
                                       retrofit: Retrofit?): Converter<ResponseBody, *>? {
        val adapter: TypeAdapter<*> = gson.getAdapter(TypeToken.get(type))
        return DdbesGsonResponseBodyConverter(adapter)
    }
}