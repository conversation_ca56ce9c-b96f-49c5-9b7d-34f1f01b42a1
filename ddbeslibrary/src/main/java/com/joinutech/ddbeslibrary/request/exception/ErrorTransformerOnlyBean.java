package com.joinutech.ddbeslibrary.request.exception;

import com.joinutech.ddbeslibrary.bean.TokenBean;
import com.joinutech.ddbeslibrary.request.Result;
import com.joinutech.ddbeslibrary.utils.GsonUtil;

import org.reactivestreams.Publisher;

import java.util.Map;

import io.reactivex.Flowable;
import io.reactivex.FlowableTransformer;
import io.reactivex.functions.Function;

/**
 * Created by York on 2016/7/23.
 * 加入了对错误处理，已经比较完整了
 */
public class ErrorTransformerOnlyBean implements FlowableTransformer<Object, Object> {

    public static Object ErrorTransformerOnlyBeancreate() {
        return new ErrorTransformerOnlyBean();
    }

    private static ErrorTransformerOnlyBean instance = null;

    private ErrorTransformerOnlyBean() {
    }

    /**
     * 双重校验锁单例(线程安全)
     */
    public static ErrorTransformerOnlyBean getInstance() {
        if (instance == null) {
            synchronized (ErrorTransformerOnlyBean.class) {
                if (instance == null) {
                    instance = new ErrorTransformerOnlyBean();
                }
            }
        }
        return instance;
    }

    @Override
    public Publisher apply(Flowable<Object> upstream) {
        return upstream.map(new Function<Object, Object>() {
            @Override
            public Object apply(Object httpResult) throws Exception {
                // 通过对返回码进行业务判断决定是返回错误还是正常取数据
                Object result = getTokenResult(httpResult);
                if (result instanceof TokenBean) {
                    return result;
                } else if (result instanceof Result) {
                    Result data = (Result) result;
                    throw new ServerException(data.getMsg(), data.getCode());
                }
                return httpResult;
            }
        }).onErrorResumeNext(new Function<Throwable, Publisher<Object>>() {
            @Override
            public Publisher<Object> apply(Throwable throwable) throws Exception {
                //ExceptionEngine为处理异常的驱动器
                throwable.printStackTrace();
                return Flowable.error(ExceptionEngine.handleException(throwable));
            }
        });
    }

    /**
     * 解析token bean 数据
     */
    private Object getTokenResult(Object data) {
        if (data != null && data instanceof Map) {
            String json = GsonUtil.INSTANCE.toJson(data);
            Map<String, Object> map = (Map<String, Object>) data;
            if (map.containsKey("access_token")) {
                TokenBean tokenBean = GsonUtil.INSTANCE.fromJson(json, TokenBean.class);
                return tokenBean;
            } else if (map.containsKey("code")) {
                Result result = GsonUtil.INSTANCE.fromJson(json, Result.class);
                return result;
            } else {
                Result<String> result = new Result();
                result.setCode(0);
                return result;
            }
        } else {
            Result<String> result = new Result();
            result.setCode(0);
            return result;
        }
    }
}
