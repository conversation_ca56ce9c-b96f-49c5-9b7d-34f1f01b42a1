package com.joinutech.ddbeslibrary.request.interceptor

import com.joinutech.ddbeslibrary.request.RequestHelper
import okhttp3.logging.HttpLoggingInterceptor

val logging: HttpLoggingInterceptor = HttpLoggingInterceptor(HttpLoggingInterceptor.Logger { message ->
    try {
        RequestHelper.showLogLine("拦截日志信息=====以下=====")
        val tag = "HLI="
        val splitLength = 1024 * 4
        if (message.length > splitLength) {
            RequestHelper.showLogLine(">>>===============")
            val length = message.length
            for (i in 0 until length step splitLength) {
                if (i + splitLength < length) {
                    RequestHelper.showLogLine(message.substring(i, i + splitLength))
                } else {
                    RequestHelper.showLogLine(message.substring(i))
                }
            }
            RequestHelper.showLogLine("<<<=================")
        } else {
            RequestHelper.showLogLine(message)
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
}).setLevel(HttpLoggingInterceptor.Level.BODY)