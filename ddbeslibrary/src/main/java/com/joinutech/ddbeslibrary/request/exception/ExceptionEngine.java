package com.joinutech.ddbeslibrary.request.exception;

import android.net.ParseException;

import com.google.gson.JsonParseException;
import com.joinutech.ddbeslibrary.request.RequestHelper;
import com.joinutech.ddbeslibrary.utils.CommonUtils;
import com.joinutech.ddbeslibrary.utils.EventBusEvent;
import com.joinutech.ddbeslibrary.utils.EventBusUtils;
import com.joinutech.ddbeslibrary.utils.IMEvent;

import org.apache.http.conn.ConnectTimeoutException;
import org.json.JSONException;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;

import retrofit2.HttpException;


public class ExceptionEngine {
    //对应HTTP的状态码
    private static final int USERNOREGISTER = 301;
    private static final int PARAMERROR = 400;
    private static final int UNAUTHORIZED = 401;
    private static final int FORBIDDEN = 403;
    private static final int NOT_FOUND = 404;
    private static final int REQUEST_TIMEOUT = 500;
    private static final int INTERNAL_SERVER_ERROR = 408;
    private static final int BAD_GATEWAY = 502;
    private static final int SERVICE_UNAVAILABLE = 503;
    private static final int GATEWAY_TIMEOUT = 504;

    public static ApiException handleException(Throwable e) {
        ApiException ex;
        if (e instanceof HttpException) {
            //HTTP错误
            HttpException httpException = (HttpException) e;
            ex = new ApiException(e, httpException.code(), "");
            switch (httpException.code()) {
                case PARAMERROR:
                    ex.setMessage("请求参数错误");
                    break;
                case UNAUTHORIZED:
                    ex.setMessage(CommonUtils.INSTANCE.dealHttpException(httpException));
                    break;
                case FORBIDDEN:
                    ex.setMessage("服务器已经理解请求，但是拒绝执行它");
                    break;
                case NOT_FOUND:
                    ex.setMessage("服务器异常，请稍后再试");
                    break;
                case REQUEST_TIMEOUT:
                    ex.setMessage("请求超时");
                    break;
                case GATEWAY_TIMEOUT:
                    ex.setMessage("作为网关或者代理工作的服务器尝试执行请求时， +\n" +
                            "                            \"未能及时从上游服务器（URI标识出的服务器，" +
                            "例如HTTP、FTP、LDAP）或者辅助服务器（例如DNS）收到响应;");
                    break;
                case INTERNAL_SERVER_ERROR:
                    ex.setMessage("服务器遇到了一个未曾预料的状况，导致了它无法完成对请求的处理");
                    break;
                case BAD_GATEWAY:
                    ex.setMessage("作为网关或者代理工作的服务器尝试执行请求时，从上游服务器接收到无效的响应");
                    break;
                case SERVICE_UNAVAILABLE:
                    ex.setMessage("由于临时的服务器维护或者过载，服务器当前无法处理请求");
                    break;
                case USERNOREGISTER:
                    ex.setMessage("此手机号尚未注册担当办公，请注册后登录");
                    break;
                default:
                    //其它均视为网络错误
                    ex.setMessage("网络错误");
                    break;
            }
            return ex;
        } else if (e instanceof ServerException) {
            //服务器返回的错误
            final ServerException resultException = (ServerException) e;
            ex = new ApiException(resultException, resultException.code, resultException.message);
            if (resultException.code == ErrorType.NETWORD_ERROR) {
                ex.setMessage("网络异常");
                ex.setMessage(resultException.message);
            } else if (resultException.code == ErrorType.ERROR_TOKEN) {
                // IM TOKEN 失效
            } else if (resultException.code == ErrorType.ERROR_RELOGIN
                    || resultException.message.contains("认证失败")
                    || resultException.message.contains("长时间不登录")
                    || resultException.message.contains("在其他地方登录")) {
//                EventBusUtils.INSTANCE.sendEvent(
//                        new EventBusEvent(IMEvent.MULTIUSER_LOGIN, resultException.message));
            } else {
                //其它均视为请求错误
                ex.setMessage(resultException.message);
                RequestHelper.INSTANCE.showLogLine("ExceptionEngine: msg = [" + ex.getMessage() + "]");
            }
            return ex;
        } else if (e instanceof JsonParseException
                || e instanceof JSONException
                || e instanceof ParseException) {
            ex = new ApiException(e, ErrorType.PARSE_ERROR, "");
            //均视为解析错误
            ex.setMessage("解析错误");
            return ex;
        } else if (e instanceof UnknownHostException || e instanceof ConnectException) {
            ex = new ApiException(e, ErrorType.NETWORD_ERROR, "");
            //均视为网络错误
            ex.setMessage("请检查网络连接");
            return ex;
        } else if (e instanceof SocketTimeoutException || e instanceof ConnectTimeoutException) {
            ex = new ApiException(e, ErrorType.NETWORD_ERROR, "");
            //均视为网络错误
            ex.setMessage("连接超时");
            return ex;
        } else {
            ex = new ApiException(e, ErrorType.UNKNOWN, "");
            //未知错误
            ex.setMessage("未知错误");
            return ex;
        }
    }

}
