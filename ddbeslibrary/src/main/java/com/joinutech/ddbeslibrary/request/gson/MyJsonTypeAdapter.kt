package com.joinutech.ddbeslibrary.request.gson

import com.google.gson.Gson
import com.google.gson.TypeAdapter
import com.google.gson.TypeAdapterFactory
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type
import java.util.concurrent.CopyOnWriteArrayList

/**
 * @Description: json 解析，TypeAdapter获取
 * @Author: zhaoyy
 * @Time: 2021/3/16 13:34
 * @packageName: com.joinutech.ddbeslibrary.request.gson
 * @Company: Copyright (C),2019- 2021,JoinuTech
 */
@Deprecated("no used")
object MyJsonAdapter {

    interface MyAdapterListener {
        fun check(gson: Gson, type: Type): TypeAdapter<Any>?
    }

    val listeners: CopyOnWriteArrayList<MyAdapterListener> = CopyOnWriteArrayList()

    val FACTORY: TypeAdapterFactory = object : TypeAdapterFactory {
        override fun <T> create(gson: <PERSON><PERSON>, type: TypeToken<T>): TypeAdapter<T>? {
            var result: TypeAdapter<T>? = null
            if (listeners.isNotEmpty()) {
                for (listener in listeners) {
                    val temp = listener.check(gson, type.rawType)
                    if (temp != null) {
                        result = temp as TypeAdapter<T>
                        break
                    }
                }
            }
            return result
        }

    }
}