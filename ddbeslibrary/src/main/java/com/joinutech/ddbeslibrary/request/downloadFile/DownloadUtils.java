package com.joinutech.ddbeslibrary.request.downloadFile;

import android.util.Log;

import androidx.annotation.NonNull;

import com.joinutech.ddbeslibrary.api.DownLoadApi;
import com.joinutech.ddbeslibrary.request.DdbesApiUtil;

import org.reactivestreams.Subscriber;
import org.reactivestreams.Subscription;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;
import okhttp3.ResponseBody;

/**
 * @Description: 类作用描述
 * @Author: 黄洁如
 * @Time: 2019/11/28 9:47
 * @packageName: com.joinutech.ddbeslibrary.request.downloadFile
 * @Company: JoinuTech
 */
public class DownloadUtils {

    /**
     * 开始下载
     *
     * @param url
     * @param file
     */
    public void download(@NonNull String url, final File file,
                         final JsDownloadListener listener) {
//        DownLoadRetrofitClientNew.Companion.getSingle_intance()
//                .getRxRetrofit(listener)
//                .create(DownLoadApi.class)
        DdbesApiUtil.INSTANCE.getDownLoadService().downloadApk(url)
                .subscribeOn(Schedulers.io())
                .unsubscribeOn(Schedulers.io())
                .map(new Function<ResponseBody, InputStream>() {

                    @Override
                    public InputStream apply(ResponseBody responseBody) throws Exception {
                        return responseBody.byteStream();
                    }
                })
                .observeOn(Schedulers.computation()) // 用于计算任务
                .doOnNext(new Consumer<InputStream>() {
                    @Override
                    public void accept(InputStream inputStream) throws Exception {
                        writeFile(inputStream, file, listener);
                    }
                })
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<InputStream>() {
                    @Override
                    public void onSubscribe(Subscription s) {

                    }

                    @Override
                    public void onNext(InputStream inputStream) {
                        Log.e("inputStream", inputStream.toString());
                    }

                    @Override
                    public void onError(Throwable t) {
                        listener.onFail(t.getMessage());
                    }

                    @Override
                    public void onComplete() {
                        listener.onSuccess();
                    }
                });

    }

    /**
     * 将输入流写入文件
     *
     * @param inputString
     * @param file
     * @param listener
     */
    private void writeFile(InputStream inputString, File file, JsDownloadListener listener) {
        if (file.exists()) {
            file.delete();
        }

        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(file);

            byte[] b = new byte[1024];

            int len;
            while ((len = inputString.read(b)) != -1) {
                fos.write(b, 0, len);
            }
            inputString.close();
            fos.close();

        } catch (FileNotFoundException e) {
            listener.onFail("FileNotFoundException");
        } catch (IOException e) {
            listener.onFail("IOException");
        }

    }
}
