package com.joinutech.ddbeslibrary.request.exception;

/**
 * 约定异常
 */

public interface ErrorType {
    /**
     * 请求成功
     */
    int SUCCESS = 1;

    /**
     * 服务器请求成功
     */
    int SERIVE_SUCCESS = 200;
    /**
     * 未知错误
     */
    int UNKNOWN = 1000;
    /**
     * 解析错误
     */
    int PARSE_ERROR = 1001;
    /**
     * 网络异常
     */
    int NETWORD_ERROR = -1;
    /**
     * auth 认证失败
     */
    int ERROR_AUTH_FAILED = -2;
    /**
     * 需要重新登录
     */
    int ERROR_RELOGIN = -3;
    /**
     * token 无效，需要重新登录
     */
    int ERROR_TOKEN = -4;

    /**
     * 协议出错
     */
    int HTTP_ERROR = 1003;

    /***返回数据为空时返回错误码*/
    int DATA_NULL = -999;
}
