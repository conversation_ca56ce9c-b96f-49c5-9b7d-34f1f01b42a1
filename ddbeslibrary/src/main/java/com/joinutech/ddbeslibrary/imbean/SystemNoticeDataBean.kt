package com.joinutech.ddbeslibrary.imbean

import java.io.Serializable

data class SystemNoticeDataBean(
        val classifyType: Int = 0,
        val companyId: String = "",
        val companyLogo: String = "",
        val companyName: String = "",
        val cmdId: String = "",
        val content: String = "",
        val detail: String = "",
        val iconUrl: String = "",
        val msgType: Int = 0,
        val noticeName: String = "",
        val noticeTitle: String = "",
        val tempType: Int = 0,

        var buttons: String = "",
        val resultTitle: String = "",
        val resultColorType: Int = 0,
        val functionType: Int = 0

) : Serializable

data class DataBeanDetail(
        val routeId: String = "",
        val userName: String = "",
        val header: String = "",
        val userId: String = "",
        val handlerName: String = "",
        val handlerAvatar: String = "",
        val backgroundUrl: String = "",//模板2的背景图片，通知模板2 的图片统一为 2:1的比例  默认992 X 496 px分辨率，模板内的图片等比放大
        val promptTitle: String = "",//模板2背景图片上显示的文字
        val needClock: Long = 0L,
        val typeId: Int = 0,
        val stteId: Int = 0,//没有使用
//汇报时用到的字段
        val modelId: String = "",
        val modelName: String = "",
        val reportContent: String = "",
        val reportName: String = "",
//模板6,邀请提交汇报用到的字段
        val avatar: String = "",
        val end: String = "",
        val endDay: String = "",
        val holiday: Int = 0,
        val period: Int = 0,
        val pre: Int = 0,
        val start: String = "",
        val startDay: String = "",
        var week: List<String> = listOf(),
//去汇报统计
        var endTime: String = "",
        var prompt: String = "",
        var startTime: String = "",
//被授予权限去详情用到的字段
        var permissions: String = "",
        var link:String="",//直接跳转到指定的web地址

        var signatureButton:ArrayList<Int> = arrayListOf() ,//里边的int代表buttonId,,有则需要跳转手写签名

        var paramType: Int = 0

) : Serializable

data class DataBeanButton(
        val colorType: Int = 0,
        val name: String = "",
        val apiUrl: String = "",
        var body: String = "",
        val methodType: Int = 0,
        val resultTitle: String = "",
        val resultColorType: Int = 0,
        val buttonId: Int = 0,

        var needToSignature:Boolean=false//自己添加的字段，代表是否需要手写签名
) : Serializable

data class DetailReportContent(
        val prompt: String = "",//模板6显示内容的时候，每条内容由这两个字段拼接起来显示
        val title: String = ""
) : Serializable


// 系统消息的推送数据， （目前有审批，会议）
data class FlutterPushData(
        val argument:Object ,
        val routeName:String ): Serializable
