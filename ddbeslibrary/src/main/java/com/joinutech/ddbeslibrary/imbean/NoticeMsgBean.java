package com.joinutech.ddbeslibrary.imbean;

public class NoticeMsgBean {
    private Long id;
    private String uid;//当前用户userId
    private String sessionId;//类似"approvalNotification"样式的
    private String msgId;
    private String cmdId;
    private long timestamp;//消息发送时间时间戳
    private String title;//主标题
    private String subtitle;//副标题
    private String context;//内容
    private String ext;//"扩展"
    private int tempStatus;  //模板按钮的状态(审批按钮展示用) 无值-1，默认0展示按钮，1按钮buttonId，2按钮buttonId
    // todo tempstatus -1 无按钮 ； 0 展示按钮（根据buttons的个数） ； 1 只展示一个按钮（从buttons里找）
    private String data_p;// 数据JSON
    // ---------data p start---  //datap内数据解析存储
    private int sessionType;////会话类型 10000系统消息 ，20000工作通知 ，30000审批通知，40000合作通知
    private int classifyType; //工作通知类型，用于筛选工作通知
    private int msgType;//消息通知具体类型
    private int tempType;
    //通知模板类型的UI样式：
    // 1系统通知的样式，
    // 2考勤打卡的大图样式，
    // 3工作通知通用样式，
    // 4审批通知的样式
    // 5纯提示无详情(工作通知通用样式去掉底部查看详情)
    // 6 内容特殊（20301）
    // 7 自定义模版机器人样式
    // 8 新的审批类型样式
    private String buttons;// 4=tempType按钮数量数组的JSON,如上
    private String resultTitle;// 4=tempType && 0<tempStatus 结果名称
    private int resultColorType;// 4=tempType && 0<tempStatus 结果的文字颜色的类型
    private int functionType;// 功能跳转Type
    private String noticeName;//通知名称
    private String noticeTitle;//通知标题
    private String content;//通知内容 datap 里的content是个 数组 转的 JSON串
    private String iconUrl;//通知类型图片地址
    private String companyId;// 公司id
    private String companyLogo;//公司logo
    private String companyName;//公司名
    private String detail;//详情内容JSON
    private String routeId;
    public void setId(Long id) {
        this.id = id;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    public void setTempStatus(int tempStatus) {
        this.tempStatus = tempStatus;
    }

    public void setData_p(String data_p) {
        this.data_p = data_p;
    }

    public void setSessionType(int sessionType) {
        this.sessionType = sessionType;
    }

    public void setClassifyType(int classifyType) {
        this.classifyType = classifyType;
    }

    public void setMsgType(int msgType) {
        this.msgType = msgType;
    }

    public void setTempType(int tempType) {
        this.tempType = tempType;
    }

    public void setButtons(String buttons) {
        this.buttons = buttons;
    }

    public void setResultTitle(String resultTitle) {
        this.resultTitle = resultTitle;
    }

    public void setResultColorType(int resultColorType) {
        this.resultColorType = resultColorType;
    }

    public void setFunctionType(int functionType) {
        this.functionType = functionType;
    }

    public void setNoticeName(String noticeName) {
        this.noticeName = noticeName;
    }

    public void setNoticeTitle(String noticeTitle) {
        this.noticeTitle = noticeTitle;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public void setCompanyLogo(String companyLogo) {
        this.companyLogo = companyLogo;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public void setRouteId(String routeId) {
        this.routeId = routeId;
    }

    public Long getId() {
        return id;
    }

    public String getUid() {
        return uid;
    }

    public String getSessionId() {
        return sessionId;
    }

    public String getMsgId() {
        return msgId;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public String getTitle() {
        return title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public String getContext() {
        return context;
    }

    public String getExt() {
        return ext;
    }

    public int getTempStatus() {
        return tempStatus;
    }

    public String getData_p() {
        return data_p;
    }

    public int getSessionType() {
        return sessionType;
    }

    public int getClassifyType() {
        return classifyType;
    }

    public int getMsgType() {
        return msgType;
    }

    public int getTempType() {
        return tempType;
    }

    public String getButtons() {
        return buttons;
    }

    public String getResultTitle() {
        return resultTitle;
    }

    public int getResultColorType() {
        return resultColorType;
    }

    public int getFunctionType() {
        return functionType;
    }

    public String getNoticeName() {
        return noticeName;
    }

    public String getNoticeTitle() {
        return noticeTitle;
    }

    public String getContent() {
        return content;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public String getCompanyId() {
        return companyId;
    }

    public String getCompanyLogo() {
        return companyLogo;
    }

    public String getCompanyName() {
        return companyName;
    }

    public String getDetail() {
        return detail;
    }

    public String getRouteId() {
        return routeId;
    }

    public void setCmdId(String cmdId) {
        this.cmdId = cmdId;
    }

    public String getCmdId() {
        return cmdId;
    }

}
