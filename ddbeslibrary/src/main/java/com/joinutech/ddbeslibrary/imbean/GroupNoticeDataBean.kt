package com.joinutech.ddbeslibrary.imbean

import java.io.Serializable

data class GroupNoticeDataBean(
        val content:String="",
        val msgType:Int=0,
        val name:String="",
        val userId:String=""

) : Serializable
data class InviteGroupNoticeContentBean(
        val name:String="",
        val userId:String=""
)

data class ReNameReCreaterContentBean(
        val color:Int=0,
        val content:String="",
        val symbol:Int=0
)

data class GroupVoiceVideoReceiveMsgBean(
        val callType:Int=0,
        val content:String="",
        val meetingId:String="",
        val msgType:String="",
        val personList:String="",
        val sendId:String=""

)