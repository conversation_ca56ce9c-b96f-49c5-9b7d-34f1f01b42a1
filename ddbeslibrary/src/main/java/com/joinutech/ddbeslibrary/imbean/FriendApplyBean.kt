package com.joinutech.ddbeslibrary.imbean

import com.joinutech.ddbeslibrary.bean.FriendUndoListBean
import java.io.Serializable

data class IsInSameCompany(
    val type: Int
)

data class FriendApplyBean(
        val avatar: String,
        val createTime: Long,
        val id: String,
        val message: String,
        val name: String,
        val status: Int,
        val updateTime: Long,
        val userId: String

) : Serializable {
    fun toFriendUndoListBean(): FriendUndoListBean {
        val friendBean = FriendUndoListBean()
        friendBean.id=id
        friendBean.applyUserId=userId
        friendBean.name=name
        friendBean.avatar=avatar
        friendBean.message=message
        friendBean.createTime=createTime.toString()
        friendBean.status=status
        friendBean.birthday=""
        friendBean.gender=0
        return friendBean
    }
}