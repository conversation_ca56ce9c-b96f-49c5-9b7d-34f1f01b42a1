package com.joinu.db.gen;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.joinutech.ddbeslibrary.db.data.RequestLog;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "REQUEST_LOG".
*/
public class RequestLogDao extends AbstractDao<RequestLog, Long> {

    public static final String TABLENAME = "REQUEST_LOG";

    /**
     * Properties of entity RequestLog.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property Method = new Property(1, int.class, "method", false, "METHOD");
        public final static Property Url = new Property(2, String.class, "url", false, "URL");
        public final static Property StartTime = new Property(3, Long.class, "startTime", false, "START_TIME");
        public final static Property EndTime = new Property(4, Long.class, "endTime", false, "END_TIME");
        public final static Property Code = new Property(5, int.class, "code", false, "CODE");
        public final static Property Network = new Property(6, String.class, "network", false, "NETWORK");
        public final static Property AppVersion = new Property(7, int.class, "appVersion", false, "APP_VERSION");
    }


    public RequestLogDao(DaoConfig config) {
        super(config);
    }
    
    public RequestLogDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"REQUEST_LOG\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"METHOD\" INTEGER NOT NULL ," + // 1: method
                "\"URL\" TEXT," + // 2: url
                "\"START_TIME\" INTEGER," + // 3: startTime
                "\"END_TIME\" INTEGER," + // 4: endTime
                "\"CODE\" INTEGER NOT NULL ," + // 5: code
                "\"NETWORK\" TEXT," + // 6: network
                "\"APP_VERSION\" INTEGER NOT NULL );"); // 7: appVersion
        // Add Indexes
        db.execSQL("CREATE INDEX " + constraint + "IDX_REQUEST_LOG_METHOD ON \"REQUEST_LOG\"" +
                " (\"METHOD\" ASC);");
        db.execSQL("CREATE INDEX " + constraint + "IDX_REQUEST_LOG_CODE ON \"REQUEST_LOG\"" +
                " (\"CODE\" ASC);");
        db.execSQL("CREATE INDEX " + constraint + "IDX_REQUEST_LOG_NETWORK ON \"REQUEST_LOG\"" +
                " (\"NETWORK\" ASC);");
        db.execSQL("CREATE INDEX " + constraint + "IDX_REQUEST_LOG_APP_VERSION ON \"REQUEST_LOG\"" +
                " (\"APP_VERSION\" ASC);");
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"REQUEST_LOG\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, RequestLog entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
        stmt.bindLong(2, entity.getMethod());
 
        String url = entity.getUrl();
        if (url != null) {
            stmt.bindString(3, url);
        }
 
        Long startTime = entity.getStartTime();
        if (startTime != null) {
            stmt.bindLong(4, startTime);
        }
 
        Long endTime = entity.getEndTime();
        if (endTime != null) {
            stmt.bindLong(5, endTime);
        }
        stmt.bindLong(6, entity.getCode());
 
        String network = entity.getNetwork();
        if (network != null) {
            stmt.bindString(7, network);
        }
        stmt.bindLong(8, entity.getAppVersion());
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, RequestLog entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
        stmt.bindLong(2, entity.getMethod());
 
        String url = entity.getUrl();
        if (url != null) {
            stmt.bindString(3, url);
        }
 
        Long startTime = entity.getStartTime();
        if (startTime != null) {
            stmt.bindLong(4, startTime);
        }
 
        Long endTime = entity.getEndTime();
        if (endTime != null) {
            stmt.bindLong(5, endTime);
        }
        stmt.bindLong(6, entity.getCode());
 
        String network = entity.getNetwork();
        if (network != null) {
            stmt.bindString(7, network);
        }
        stmt.bindLong(8, entity.getAppVersion());
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public RequestLog readEntity(Cursor cursor, int offset) {
        RequestLog entity = new RequestLog( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.getInt(offset + 1), // method
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // url
            cursor.isNull(offset + 3) ? null : cursor.getLong(offset + 3), // startTime
            cursor.isNull(offset + 4) ? null : cursor.getLong(offset + 4), // endTime
            cursor.getInt(offset + 5), // code
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // network
            cursor.getInt(offset + 7) // appVersion
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, RequestLog entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setMethod(cursor.getInt(offset + 1));
        entity.setUrl(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setStartTime(cursor.isNull(offset + 3) ? null : cursor.getLong(offset + 3));
        entity.setEndTime(cursor.isNull(offset + 4) ? null : cursor.getLong(offset + 4));
        entity.setCode(cursor.getInt(offset + 5));
        entity.setNetwork(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setAppVersion(cursor.getInt(offset + 7));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(RequestLog entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(RequestLog entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(RequestLog entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
