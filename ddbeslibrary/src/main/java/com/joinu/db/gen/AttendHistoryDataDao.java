package com.joinu.db.gen;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.joinutech.ddbeslibrary.db.data.AttendHistoryData;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "ATTEND_HISTORY_DATA".
*/
public class AttendHistoryDataDao extends AbstractDao<AttendHistoryData, Long> {

    public static final String TABLENAME = "ATTEND_HISTORY_DATA";

    /**
     * Properties of entity AttendHistoryData.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property UserId = new Property(1, String.class, "userId", false, "USER_ID");
        public final static Property CompanyId = new Property(2, String.class, "companyId", false, "COMPANY_ID");
        public final static Property CreateTime = new Property(3, long.class, "createTime", false, "CREATE_TIME");
        public final static Property CurrentDate = new Property(4, String.class, "currentDate", false, "CURRENT_DATE");
        public final static Property ClockStatus = new Property(5, int.class, "clockStatus", false, "CLOCK_STATUS");
        public final static Property ParameterJson = new Property(6, String.class, "parameterJson", false, "PARAMETER_JSON");
        public final static Property ClockDataJson = new Property(7, String.class, "clockDataJson", false, "CLOCK_DATA_JSON");
        public final static Property EnvName = new Property(8, String.class, "envName", false, "ENV_NAME");
    }


    public AttendHistoryDataDao(DaoConfig config) {
        super(config);
    }
    
    public AttendHistoryDataDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"ATTEND_HISTORY_DATA\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"USER_ID\" TEXT," + // 1: userId
                "\"COMPANY_ID\" TEXT," + // 2: companyId
                "\"CREATE_TIME\" INTEGER NOT NULL ," + // 3: createTime
                "\"CURRENT_DATE\" TEXT," + // 4: currentDate
                "\"CLOCK_STATUS\" INTEGER NOT NULL ," + // 5: clockStatus
                "\"PARAMETER_JSON\" TEXT," + // 6: parameterJson
                "\"CLOCK_DATA_JSON\" TEXT," + // 7: clockDataJson
                "\"ENV_NAME\" TEXT);"); // 8: envName
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"ATTEND_HISTORY_DATA\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, AttendHistoryData entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String userId = entity.getUserId();
        if (userId != null) {
            stmt.bindString(2, userId);
        }
 
        String companyId = entity.getCompanyId();
        if (companyId != null) {
            stmt.bindString(3, companyId);
        }
        stmt.bindLong(4, entity.getCreateTime());
 
        String currentDate = entity.getCurrentDate();
        if (currentDate != null) {
            stmt.bindString(5, currentDate);
        }
        stmt.bindLong(6, entity.getClockStatus());
 
        String parameterJson = entity.getParameterJson();
        if (parameterJson != null) {
            stmt.bindString(7, parameterJson);
        }
 
        String clockDataJson = entity.getClockDataJson();
        if (clockDataJson != null) {
            stmt.bindString(8, clockDataJson);
        }
 
        String envName = entity.getEnvName();
        if (envName != null) {
            stmt.bindString(9, envName);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, AttendHistoryData entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String userId = entity.getUserId();
        if (userId != null) {
            stmt.bindString(2, userId);
        }
 
        String companyId = entity.getCompanyId();
        if (companyId != null) {
            stmt.bindString(3, companyId);
        }
        stmt.bindLong(4, entity.getCreateTime());
 
        String currentDate = entity.getCurrentDate();
        if (currentDate != null) {
            stmt.bindString(5, currentDate);
        }
        stmt.bindLong(6, entity.getClockStatus());
 
        String parameterJson = entity.getParameterJson();
        if (parameterJson != null) {
            stmt.bindString(7, parameterJson);
        }
 
        String clockDataJson = entity.getClockDataJson();
        if (clockDataJson != null) {
            stmt.bindString(8, clockDataJson);
        }
 
        String envName = entity.getEnvName();
        if (envName != null) {
            stmt.bindString(9, envName);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public AttendHistoryData readEntity(Cursor cursor, int offset) {
        AttendHistoryData entity = new AttendHistoryData( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // userId
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // companyId
            cursor.getLong(offset + 3), // createTime
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // currentDate
            cursor.getInt(offset + 5), // clockStatus
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // parameterJson
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // clockDataJson
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8) // envName
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, AttendHistoryData entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setUserId(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setCompanyId(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setCreateTime(cursor.getLong(offset + 3));
        entity.setCurrentDate(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setClockStatus(cursor.getInt(offset + 5));
        entity.setParameterJson(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setClockDataJson(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setEnvName(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(AttendHistoryData entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(AttendHistoryData entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(AttendHistoryData entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
