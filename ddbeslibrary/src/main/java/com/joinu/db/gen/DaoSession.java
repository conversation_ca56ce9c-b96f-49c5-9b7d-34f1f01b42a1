package com.joinu.db.gen;

import java.util.Map;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.AbstractDaoSession;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.identityscope.IdentityScopeType;
import org.greenrobot.greendao.internal.DaoConfig;

import com.joinutech.ddbeslibrary.db.data.AttendHistoryData;
import com.joinutech.ddbeslibrary.db.data.RequestLog;

import com.joinu.db.gen.AttendHistoryDataDao;
import com.joinu.db.gen.RequestLogDao;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.

/**
 * {@inheritDoc}
 * 
 * @see org.greenrobot.greendao.AbstractDaoSession
 */
public class DaoSession extends AbstractDaoSession {

    private final DaoConfig attendHistoryDataDaoConfig;
    private final DaoConfig requestLogDaoConfig;

    private final AttendHistoryDataDao attendHistoryDataDao;
    private final RequestLogDao requestLogDao;

    public DaoSession(Database db, IdentityScopeType type, Map<Class<? extends AbstractDao<?, ?>>, DaoConfig>
            daoConfigMap) {
        super(db);

        attendHistoryDataDaoConfig = daoConfigMap.get(AttendHistoryDataDao.class).clone();
        attendHistoryDataDaoConfig.initIdentityScope(type);

        requestLogDaoConfig = daoConfigMap.get(RequestLogDao.class).clone();
        requestLogDaoConfig.initIdentityScope(type);

        attendHistoryDataDao = new AttendHistoryDataDao(attendHistoryDataDaoConfig, this);
        requestLogDao = new RequestLogDao(requestLogDaoConfig, this);

        registerDao(AttendHistoryData.class, attendHistoryDataDao);
        registerDao(RequestLog.class, requestLogDao);
    }
    
    public void clear() {
        attendHistoryDataDaoConfig.clearIdentityScope();
        requestLogDaoConfig.clearIdentityScope();
    }

    public AttendHistoryDataDao getAttendHistoryDataDao() {
        return attendHistoryDataDao;
    }

    public RequestLogDao getRequestLogDao() {
        return requestLogDao;
    }

}
