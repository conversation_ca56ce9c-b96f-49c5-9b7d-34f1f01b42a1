apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply plugin: 'kotlin-kapt'

apply plugin: 'org.greenrobot.greendao'

apply plugin: 'com.kezong.fat-aar'


android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    namespace = "com.joinutech.ddbeslibrary" // 使用模块的实际包名
    def version_name = rootProject.ext.android.versionName
    // todo 此处固定code，对应后端记录版本内部code，即config.gradle中配置版本code
    // 检查版本更新时使用的是此code配置，如果上线应用市场后发现问题，需要重新打包，code必须+1，此时会触发后台版本更新 强制更新逻辑，
    def version_code = rootProject.ext.android.versionCode
    def array = version_name.split("\\.")
    def codeResult = String.format("%d%02d%02d", Integer.parseInt(array[0]), Integer.parseInt(array[1]), Integer.parseInt(array[2]))

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName

        //经过下面代码的转化，可以以类似BuildConfig.innerCode的方式调用这些值
        buildConfigField "String", "innerCode", "\"" + codeResult + "\""

        buildConfigField "String", "VERSION_NAME", "\"" + version_name + "\""
        buildConfigField "Integer", "VERSION_CODE", "$version_code"

        multiDexEnabled true
//        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        kapt {
            arguments {
                arg("AROUTER_MODULE_NAME", "ddbesLib")
            }
        }
    }

//    androidExtensions {
//        experimental = true
//    }

    buildFeatures {
        buildConfig = true
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }

    //使用aar包，添加dirs'libs',,位置3
    repositories {
        flatDir {
            dirs 'libs','../task/libs'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            consumerProguardFiles 'proguard-rules.pro'
        }
    }

    //数据库版本
    greendao {
        schemaVersion 1
        daoPackage 'com.joinu.db.gen'
//        targetGenDir 'src/main/java'
        targetGenDir "$buildDir/generated/source/greendao"
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
//        useIR = true
    }
    dexOptions {
        incremental true
        javaMaxHeapSize "4g"
    }

    viewBinding {
        enabled = true

    }

//    configurations{
//        all{
//            //exclude关键字可以避免目标库的重复编译
//            exclude group:"com.qcloud.cos", module:"cos-android-nobeacon"
//        }
//    }
}


dependencies {
    api fileTree(include: ['*.aar','*.jar'], dir: 'libs')//使用aar包，位置1

//    api 'androidx.constraintlayout:constraintlayout:1.1.3'

    // ViewModel and LiveData
//    api 'androidx.lifecycle:lifecycle-extensions:2.2.0'


    //v7支持包
    api supportLibs.lifecycle_extensions
    api supportLibs.appcompat
    api supportLibs.material
    api supportLibs.constraintLayout
    api 'androidx.legacy:legacy-support-v4:1.0.0'
    api project(path: ':fileselectlibrary')

    // Kotlin
    implementation supportLibs.stdlib_jdk7//代替
    implementation supportLibs.kotlin_reflect
    api supportLibs.act_ktx
    api supportLibs.fragment_ktx
//    api "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
//    api "org.jetbrains.kotlin:kotlin-reflect:$kotlin_version"

    //multidex
    api supportLibs.multidex

    //安卓兼容库
    api supportLibs.nineoldandroids

    //屏幕适配
    api supportLibs.autosize

    // Retrofit 网络请求库
    api supportLibs.retrofit2
    api supportLibs.adapter_rxjava2
    api supportLibs.logging_interceptor
    implementation supportLibs.converter_gson
    api supportLibs.gson

    //glide
    api supportLibs.glide
//    implementation files('libs\\tbs_sdk_thirdapp_v4.3.0.386_44286_sharewithdownloadwithfile_withoutGame_obfs_20230210_114429.jar')//X5内核sdk
    kapt supportLibs.glide_compiler
    api supportLibs.glide_transformations
    api supportLibs.glassfish_annotation

    //上下拉刷新
//    api supportLibs.SmartRefreshLayout
    api supportLibs.refresh_layout
    api supportLibs.refresh_header
    api supportLibs.refresh_footer

    //事件传递
    api supportLibs.eventbus

    //状态栏适配
    api supportLibs.immersionBar

    //Dagger
    api supportLibs.dagger

    //配置文件+内存存储
    api supportLibs.mmkv

    //微信登录分享支付
    api supportLibs.wechat

    //日期、数字等滚动选择库
    api supportLibs.pickers

//    //悬浮窗
//    api supportLibs.easyFloat
//    api 'com.marktoo.widget:cachedweb:1.0.0.12'
    api 'com.marktoo.library:cachedweb:1.0.0.15'
//    implementation('com.tencent.qcloud:cosxml-lite:5.4.25') {
//    implementation('com.tencent.qcloud:cosxml-lite:5.5.3') {
//    api('com.tencent.qcloud:cosxml-lite:5.6.0') {
//    api('com.qcloud.cos:cos-android-lite:5.6.0') {

//    api('com.qcloud.cos:cos-android:5.6.14') {
//    api('com.qcloud.cos:cos-android-nobeacon:5.9.16') {
//        //关闭云存储 mta log 上报功能
//        exclude group: 'com.tencent.qcloud', module: 'mtaUtils'
////         去除beacon上报
//        exclude group: 'com.tencent.qcloud', module: 'beacon-android-release'
//        // test Flutter里引用的，后续看是否加入
////        exclude group: 'com.tencent.qcloud', module: 'cos-android-nobeacon'
//    }

    api('com.qcloud.cos:cos-android:5.9.35')
//    api('com.tencent.qcloud:qcloud-java-sdk-core:5.0.10')

//    api('com.qcloud.cos:qcloud-foundation:1.5.5'){
//
//    }

    api supportLibs.rxlifecycle_components
//    api('com.trello.rxlifecycle3:rxlifecycle-components:3.1.0') {
//        exclude group: 'androidx.appcompat' , module: 'appcompat'
//    }


    implementation supportLibs.bugly//bugly使用
//    implementation supportLibs.nativecrashreport//lj添加的，bugly使用
    api supportLibs.arouter
    kapt supportLibs.arouter_compiler

    api supportLibs.viewPager2
    api supportLibs.recyclerview
    //switchButton
    api supportLibs.switch_button

    //    ---------------------- library code dependency ----------------------

    //三方库

    // 推送sdk集成封装，集成推送
//    api 'com.marktoo.common:pushlib:1.0.3'
//    api "com.marktoo.library:pushlib:1.0.5"//旧push，位置7
    // 图片选择
//    api project(':picture_library')
//    api('com.github.LuckSiege.PictureSelector:picture_library:v2.6.0') {
    api(project(':lib_picture')) {
        exclude group: "com.google.guava", module: 'listenablefuture'
    }

    // 二维码
//    api project(':library-zxing')
    api 'com.google.zxing:core:3.3.1'
    //    ---------------------- 已包含在：api fileTree(include: ['*.jar'], dir: 'libs')
//    if (!rootProject.ext.isDebug) {
//        api(name: 'flutter-release', ext: 'aar')
//        api(name: 'shared_preferences-release', ext: 'aar')
//    } else {
//        api project(':flutter')
//    }
    implementation 'cat.ereza:customactivityoncrash:2.3.0'
    api 'com.iyao.easyat:easyat:1.0.0'

    //buf修改4，protobuf依赖
    api 'com.google.protobuf:protobuf-java:3.7.0'
    api 'com.google.protobuf:protoc:3.7.0'
    //增加和json互转的依赖
    api 'com.google.protobuf:protobuf-java-util:3.6.0'//这个是官方提供的工具类

    //这是加载超长图片用的
    implementation 'com.davemorrissey.labs:subsampling-scale-image-view:3.10.0'

    api("androidx.camera:camera-camera2:${cfgs.camerax_version}") {
        exclude group: "com.google.guava", module: 'listenablefuture'
    }

    //greenDao 数据库存储
    implementation supportLibs.greendao

//    api 'com.baidu.lbsyun:BaiduMapSDK_Map:7.5.0'//百度地图
//    api 'com.baidu.lbsyun:BaiduMapSDK_Search:7.5.0'//百度地图
//    api 'com.baidu.lbsyun:BaiduMapSDK_Util:7.5.0'//百度地图
//    api 'com.baidu.lbsyun:BaiduMapSDK_Location_All:9.5.2'//百度地图
    api 'com.otaliastudios:cameraview:2.7.2'//窗口相机位置5

    //厂商推送开始集成===================开始====
    //小米推送在libs里边放有对应aar包
//    api (name: 'MiPush_SDK_Client_5_6_2-C_3rd', ext: 'aar')//小米推送
//    api files('libs/MiPush_SDK_Client_5_7_8-C_3rd.aar')//小米推送
//    api files('libs/vivo_pushSDK_v3.0.0.4_484.aar')//vivo推送
//    api files('libs/com.heytap.msp_3.1.0.aar')//OPPO推送
//    api files('libs/TbsFileSdk_base_armeabi_1.0.5.6000012.20230215103447.aar')//使用aar包，位置2，，，腾讯浏览服务文档sdk


//    api 'com.android.support:support-annotations:28.0.0'//OPPO推送

//    api 'com.huawei.hms:push:6.9.0.300'
//    api 'com.huawei.hms:hmscoreinstaller:6.9.0.300'

//    api 'com.meizu.flyme.internet:push-internal:4.2.3'

    //厂商推送开始集成===================结束====

    api 'com.jakewharton.timber:timber:4.7.1'
    api 'com.tencent.tbs:tbssdk:44226'
//    implementation 'TbsFileSdk.aar'//腾讯浏览服务，这里是文档SDK的集成，内核SDK的集成是通过jar包集成的；


//    api project(':LocalRepo:heytap_push')
//    api project(':LocalRepo:mi_push')
    api project(':LocalRepo:tbs')
//    api project(':LocalRepo:vivo_push')
//    api project(':LocalRepo:hwrtc')

    // 引入协程的一些
    def lifecycle_version = "2.3.0"
    // ViewModel
    api "androidx.lifecycle:lifecycle-viewmodel:$lifecycle_version"
    // LiveData
    api "androidx.lifecycle:lifecycle-livedata:$lifecycle_version"
    // alternately - if using Java8, use the following instead of lifecycle-compiler
    api "androidx.lifecycle:lifecycle-common-java8:$lifecycle_version"
    api "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.3.0"
    api "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version"
//    api 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.4.0-alpha01'


    api 'com.jakewharton:process-phoenix:2.1.2'
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.13.0")

    compileOnly('com.meizu.flyme.internet:push-internal:4.2.3')


}




